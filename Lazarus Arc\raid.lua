local Raid = {
    id = "default_raid",
    name = "Generic Raid",
    location = {x = 0, y = 0},
    currentWave = 0,
    maxWaves = 3,
    waveInterval = 30, -- seconds between waves
    timer = 0,
    isActive = false,
    waves = {
        -- These can be filled dynamically or defined ahead of time
        {
            delay = 0,
            enemies = {
                {type = "goblin", count = 5},
                {type = "wolf", count = 2}
            }
        },
        {
            delay = 30,
            enemies = {
                {type = "goblin_archer", count = 4},
                {type = "bear", count = 1}
            }
        },
        {
            delay = 60,
            enemies = {
                {type = "goblin_chief", count = 1},
                {type = "forest_golem", count = 2}
            }
        }
    }
}

function Raid.init(config)
    for k, v in pairs(config or {}) do
        Raid[k] = v
    end
    Raid.timer = 0
    Raid.currentWave = 0
    Raid.isActive = true
end

function Raid.update(world, dt)
    if not Raid.isActive then return end

    Raid.timer = Raid.timer + dt

    local nextWave = Raid.waves[Raid.currentWave + 1]
    if nextWave and Raid.timer >= nextWave.delay then
        Raid.spawnWave(world, nextWave)
        Raid.currentWave = Raid.currentWave + 1
    end

    if Raid.currentWave >= Raid.maxWaves then
        Raid.isActive = false
        print("Raid complete!")
    end
end

function Raid.spawnWave(world, wave)
    for _, enemyGroup in ipairs(wave.enemies) do
        for i = 1, enemyGroup.count do
            local entity = world.entityFactory:create(enemyGroup.type)
            if entity then
                entity.position = {
                    x = Raid.location.x + math.random(-5, 5),
                    y = Raid.location.y + math.random(-5, 5)
                }
                world.entitySystem:addEntity(entity)
            end
        end
    end
    print("Spawned wave " .. Raid.currentWave + 1)
end

return Raid
