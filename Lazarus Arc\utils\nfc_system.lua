-- nfc_system.lua
-- Handles NFC card detection and management for Lazarus Arc.
-- This upgraded version includes stubs for mobile (Android/iOS) NFC integration.

local NFCSystem = {
    initialized = false,
    onConnectCallback = nil,
    onDisconnectCallback = nil,
    activeCards = {},           -- Track currently connected cards
    pinVerificationQueue = {},  -- Queue for PIN verification
    hardwareAvailable = false,  -- Whether physical NFC hardware is available
    simulationMode = true,      -- Defaults to simulation mode if hardware is not available
    pollInterval = 0.5,         -- How often to check for cards (seconds)
    lastPollTime = 0,
    ledgerEnabled = false,      -- Enable ledger logging if desired
    nfcLedger = {}              -- Ledger for NFC events (if enabled)
}

-- Utility: Check if we're running on a mobile platform (Android or iOS)
local function isMobilePlatform()
    local osName = love.system.getOS()
    return osName == "Android" or osName == "iOS"
end

-- Stub: Initialize NFC for mobile platforms
local function initMobileNFC()
    -- Here you would add code to interface with native Android/iOS NFC APIs.
    -- For now, we simply log that mobile NFC is not yet implemented.
    print("Mobile NFC integration is not yet implemented. (Stub function)")
    return false
end

-- Optional: Record an NFC event in the ledger
local function recordNFCEvent(eventType, cardId, details)
    details = details or {}
    local record = {
        eventType = eventType,
        cardId = cardId,
        timestamp = os.time(),
        details = details
    }
    table.insert(NFCSystem.nfcLedger, record)
    print("NFC Event recorded: " .. eventType .. " for card " .. cardId)
    return record
end

-- Retrieve NFC event history
function NFCSystem.getNFCEventHistory(lookback)
    lookback = lookback or #NFCSystem.nfcLedger
    local history = {}
    for i = math.max(1, #NFCSystem.nfcLedger - lookback + 1), #NFCSystem.nfcLedger do
        table.insert(history, NFCSystem.nfcLedger[i])
    end
    return history
end

-- Clear NFC event ledger
function NFCSystem.clearNFCEventHistory()
    NFCSystem.nfcLedger = {}
    print("NFC ledger cleared.")
end

-- Initialize the NFC system
function NFCSystem.init(connectCallback, disconnectCallback, options)
    options = options or {}
    print("Initializing NFC System...")
    
    NFCSystem.onConnectCallback = connectCallback
    NFCSystem.onDisconnectCallback = disconnectCallback
    
    NFCSystem.ledgerEnabled = options.enableLedger or false
    if NFCSystem.ledgerEnabled then
        NFCSystem.nfcLedger = {}
    end
    
    -- Check for NFC hardware:
    if isMobilePlatform() then
        -- For mobile platforms, attempt to initialize native NFC (stubbed)
        NFCSystem.hardwareAvailable = initMobileNFC()
        if NFCSystem.hardwareAvailable then
            NFCSystem.simulationMode = false
            print("Mobile NFC hardware initialized.")
        else
            print("Mobile NFC not available. Using simulation mode.")
            NFCSystem.simulationMode = true
        end
    else
        -- For desktop, we default to simulation mode (or implement PC NFC if available)
        NFCSystem.hardwareAvailable = NFCSystem.checkHardware()
        if NFCSystem.hardwareAvailable then
            print("NFC hardware detected. Using physical NFC reader.")
            NFCSystem.simulationMode = false
            NFCSystem.initHardware()
        else
            print("No NFC hardware detected. Using simulation mode.")
            NFCSystem.simulationMode = true
        end
    end
    
    NFCSystem.initialized = true
    NFCSystem.lastPollTime = love.timer.getTime()
    
    return NFCSystem.initialized
end

-- Check if NFC hardware is available (desktop placeholder)
function NFCSystem.checkHardware()
    -- Replace this with actual hardware detection logic for desktop if needed.
    return false
end

-- Initialize NFC hardware connection (desktop placeholder)
function NFCSystem.initHardware()
    -- Initialize communication with physical NFC hardware here.
    print("NFC hardware initialization (desktop placeholder)")
end

-- Update NFC system state
function NFCSystem.update(dt)
    if not NFCSystem.initialized then return end
    
    NFCSystem.processPINQueue()
    
    if not NFCSystem.simulationMode then return end
    
    local currentTime = love.timer.getTime()
    if currentTime - NFCSystem.lastPollTime >= NFCSystem.pollInterval then
        NFCSystem.lastPollTime = currentTime
        -- Implement simulation logic here if desired.
    end
end

-- Process PIN verification queue
function NFCSystem.processPINQueue()
    for cardId, entry in pairs(NFCSystem.pinVerificationQueue) do
        if entry.isComplete then
            NFCSystem.pinVerificationQueue[cardId] = nil
            if entry.isVerified then
                NFCSystem.activeCards[cardId] = true
            end
        end
    end
end

-- Handle a card being presented to the reader
function NFCSystem.handleCardDetection(cardId)
    if NFCSystem.activeCards[cardId] then
        return false
    end
    
    print("NFC Card detected: " .. cardId)
    
    NFCSystem.pinVerificationQueue[cardId] = {
        cardId = cardId,
        timestamp = love.timer.getTime(),
        isComplete = false,
        isVerified = false
    }
    
    if NFCSystem.ledgerEnabled then
        recordNFCEvent("CardDetected", cardId)
    end
    
    if NFCSystem.onConnectCallback then
        NFCSystem.onConnectCallback(cardId, function(isPINVerified)
            local queueEntry = NFCSystem.pinVerificationQueue[cardId]
            if queueEntry then
                queueEntry.isComplete = true
                queueEntry.isVerified = isPINVerified
            end
        end)
    end
    
    return true
end

-- Handle a card being removed from the reader
function NFCSystem.handleCardRemoval(cardId)
    if not NFCSystem.activeCards[cardId] then
        return false
    end
    
    print("NFC Card removed: " .. cardId)
    NFCSystem.activeCards[cardId] = nil
    
    if NFCSystem.ledgerEnabled then
        recordNFCEvent("CardRemoved", cardId)
    end
    
    if NFCSystem.onDisconnectCallback then
        NFCSystem.onDisconnectCallback(cardId)
    end
    
    return true
end

-- Handle physical card detection (would be called by a hardware library)
function NFCSystem.handlePhysicalCardDetection(cardId, isPresent)
    if isPresent then
        NFCSystem.handleCardDetection(cardId)
    else
        NFCSystem.handleCardRemoval(cardId)
    end
end

-- Simulate NFC card connection (for testing)
function NFCSystem.simulateCardConnect(cardId, pinPromptCallback)
    print("SIMULATION: NFC Card connected: " .. cardId)
    NFCSystem.handleCardDetection(cardId)
    
    if pinPromptCallback then
        pinPromptCallback(function(isPINVerified)
            local queueEntry = NFCSystem.pinVerificationQueue[cardId]
            if queueEntry then
                queueEntry.isComplete = true
                queueEntry.isVerified = isPINVerified
                if isPINVerified then
                    NFCSystem.activeCards[cardId] = true
                end
            end
        end)
    end
end

-- Simulate NFC card disconnection (for testing)
function NFCSystem.simulateCardDisconnect(cardId)
    print("SIMULATION: NFC Card disconnected: " .. cardId)
    NFCSystem.handleCardRemoval(cardId)
end

-- Get a list of currently connected cards
function NFCSystem.getConnectedCards()
    local cards = {}
    for cardId, _ in pairs(NFCSystem.activeCards) do
        table.insert(cards, cardId)
    end
    return cards
end

-- Check if a specific card is connected
function NFCSystem.isCardConnected(cardId)
    return NFCSystem.activeCards[cardId] == true
end

-- Clean up and shut down the NFC system
function NFCSystem.shutdown()
    if not NFCSystem.initialized then return end
    
    if NFCSystem.hardwareAvailable then
        -- Close connection to NFC hardware here
    end
    
    NFCSystem.initialized = false
    NFCSystem.activeCards = {}
    NFCSystem.pinVerificationQueue = {}
    print("NFC System shutdown complete")
end

return NFCSystem
