-- entities/beetle.lua
-- Beetle insect with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local Beetle = {
    id = "beetle",
    name = "Beetle",
    type = "beetle",
    shape = {
        {0, -0.6}, {0.5, -0.4}, {0.6, 0}, {0.5, 0.4},
        {0, 0.6}, {-0.5, 0.4}, {-0.6, 0}, {-0.5, -0.4}
    },
    size = 3,

    -- Entity categories
    categories = {"insect", "small", "armored", "scavenger"},
    threatCategories = {"player", "bird", "spider"},
    
    -- Base stats (small but armored)
    maxHealth = 15,
    health = 15,
    maxStamina = 30,
    stamina = 30,
    speed = 1.8,
    attack = 6,
    defense = 12,  -- High defense due to shell
    chitin_armor = 8,
    burrowing = 5,
    
    -- Behaviors
    behaviors = {"scavenge", "burrow", "defensive_curl", "swarm"},
    behaviorConfigs = {
        scavenge = {
            moveSpeed = 1.5,
            searchRadius = 8,
            foodPreference = {"organic_matter", "dead_creatures"},
            efficiency = 1.2
        },
        burrow = {
            burrowSpeed = 0.8,
            underground_movement = true,
            hideChance = 0.6,
            emergeSurprise = true
        },
        defensive_curl = {
            triggerHealth = 0.5,
            defenseBonus = 2.0,
            duration = 5,
            immunity = ["crushing"]
        },
        swarm = {
            groupRadius = 10,
            swarmBonus = 1.3,
            maxSwarmSize = 12
        }
    },
    
    -- Enhanced variant system for beetles
    variantChances = {
        normal = 0.75,          -- 75% dung beetle
        shiny = 0.18,           -- 18% rhinoceros beetle (shiny)
        rare = 0.06,            -- 6% titan beetle (rare)
        legendary = 0.01        -- 1% scarab god (legendary)
    },
    
    variants = {
        normal = {
            name = "Dung Beetle",
            description = "A common beetle that rolls balls of organic matter",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.4, 0.3, 0.2, 1.0}  -- Brown
            }
        },
        
        shiny = {
            name = "Rhinoceros Beetle",
            description = "A powerful beetle with an impressive horn",
            statModifiers = {
                maxHealth = 1.6,    -- 24 health
                attack = 2.0,       -- 12 attack
                defense = 1.4,      -- 16.8 defense
                horn_power = 2.0,
                strength = 1.8
            },
            appearanceModifiers = {
                scale = 1.3,
                glow = true,
                colorTint = {0.2, 0.2, 0.1, 1.0},  -- Dark with metallic sheen
                prominent_horn = true,
                muscular_build = true
            },
            soundModifiers = {
                pitch = 0.8,
                volume = 1.3,
                powerful = true
            }
        },
        
        rare = {
            name = "Titan Beetle",
            description = "A massive beetle with incredible strength and armor",
            statModifiers = {
                maxHealth = 2.5,    -- 37.5 health
                attack = 2.5,       -- 15 attack
                defense = 2.0,      -- 24 defense
                size_advantage = 2.0,
                crushing_mandibles = 2.5
            },
            appearanceModifiers = {
                scale = 1.8,
                colorTint = {0.3, 0.2, 0.1, 1.0},  -- Dark titan coloring
                massive_mandibles = true,
                thick_armor = true,
                intimidating_presence = true
            },
            soundModifiers = {
                pitch = 0.6,
                volume = 1.5,
                reverb = true,
                crushing_sound = true
            }
        },
        
        legendary = {
            name = "Scarab God",
            description = "A divine beetle with mystical powers over life and death",
            statModifiers = {
                maxHealth = 4.0,    -- 60 health
                attack = 3.0,       -- 18 attack
                defense = 3.0,      -- 36 defense
                divine_power = 5.0,
                resurrection_magic = 3.0
            },
            appearanceModifiers = {
                scale = 1.5,
                glow = true,
                colorTint = {1.2, 1.0, 0.3, 1.0},  -- Golden divine glow
                divine_markings = true,
                sacred_aura = "legendary",
                hieroglyphic_shell = true,
                sun_disk = true
            },
            soundModifiers = {
                pitch = 0.7,
                volume = 1.6,
                reverb = true,
                echo = true,
                divine_resonance = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "chitin_fragment", chance = 0.8, quantity = {1, 2}},
        {id = "insect_leg", chance = 0.6, quantity = {2, 4}},
        {id = "beetle_shell", chance = 0.7, quantity = {1, 1}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "rhinoceros_horn", chance = 0.9, quantity = {1, 1}},
            {id = "strength_essence", chance = 0.7, quantity = {1, 1}},
            {id = "reinforced_chitin", chance = 0.8, quantity = {1, 2}}
        },
        rare = {
            {id = "titan_mandible", chance = 0.9, quantity = {1, 2}},
            {id = "massive_chitin_plate", chance = 0.8, quantity = {1, 1}},
            {id = "crushing_power_essence", chance = 0.7, quantity = {1, 1}},
            {id = "size_enhancement_gland", chance = 0.5, quantity = {1, 1}}
        },
        legendary = {
            {id = "scarab_god_shell", chance = 0.95, quantity = {1, 1}},
            {id = "divine_scarab_essence", chance = 0.9, quantity = {1, 2}},
            {id = "resurrection_crystal", chance = 0.8, quantity = {1, 1}},
            {id = "sun_disk_fragment", chance = 0.7, quantity = {1, 1}},
            {id = "hieroglyphic_tablet", chance = 0.6, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "beetle",
        scale = 0.6,
        animations = {
            "crawl", "burrow", "curl_up", "horn_charge", "divine_glow"
        },
        variants = {
            "dung_beetle", "rhinoceros_beetle", "titan_beetle", "scarab_god"
        }
    },
    
    -- Sound effects with insect characteristics
    sounds = {
        crawl = {
            file = "beetle_crawl",
            synth = {
                instrument = "xylophone",
                notes = {"C4", "D4"},
                durations = {0.1, 0.1},
                volume = 0.15,
                chittering = true
            }
        },
        shell_click = {
            file = "shell_click",
            synth = {
                instrument = "percussion",
                notes = {"F4"},
                duration = 0.05,
                volume = 0.2,
                clicking = true
            }
        },
        horn_charge = {
            file = "horn_charge",
            synth = {
                instrument = "brass",
                notes = {"G3", "C4"},
                durations = {0.2, 0.3},
                volume = 0.4,
                charging = true
            }
        },
        divine_hum = {
            file = "divine_hum",
            synth = {
                instrument = "organ",
                notes = {"A3", "C4", "E4"},
                durations = {0.8, 0.6, 1.0},
                volume = 0.5,
                sacred = true
            }
        }
    },
    
    -- Special insect abilities
    abilities = {
        chitin_armor = {
            type = "passive",
            description = "Natural armor provides damage reduction",
            effect = "damage_reduction"
        },
        defensive_curl = {
            type = "active",
            description = "Curl into shell for maximum protection",
            effect = "damage_immunity",
            duration = 3,
            cooldown = 15
        },
        burrow_escape = {
            type = "active",
            description = "Quickly burrow underground to escape danger",
            effect = "escape_ability",
            cooldown = 20
        },
        swarm_coordination = {
            type = "passive",
            description = "Gains bonuses when near other beetles",
            effect = "swarm_bonus"
        }
    }
}

-- Initialize the beetle entity using enhanced template
function Beetle.init(entity, world)
    -- Copy all fields from Beetle template to entity instance
    for k, v in pairs(Beetle) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return Beetle
