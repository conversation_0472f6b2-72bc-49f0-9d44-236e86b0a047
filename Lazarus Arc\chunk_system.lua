-- chunk_system.lua
-- Enhanced chunk management system for Lazarus Arc game
-- Fully dynamic content loader - no hardcoded values
--
-- IMPORTANT: Methods that use 'self' must be defined with ':' syntax
-- Example: function ChunkSystem:methodName() NOT function ChunkSystem.methodName()
-- This ensures 'self' is properly set to the ChunkSystem instance

local Logger = require("utils.logger")
local UUID = require("utils.uuid")
local TileDecorations = require("tiles.tile_decorations")

-- Isometric conversion: converts world coordinates to isometric screen coordinates with height support.
local function toIsometric(x, y, tileWidth, tileHeight, height)
    local isoX = (x - y) * (tileWidth / 2)
    local isoY = (x + y) * (tileHeight / 2)

    -- Apply height offset if provided
    if height and height > 0 then
        -- Each unit of height moves the tile up by half the tile height
        isoY = isoY - (height * tileHeight / 2)
    end

    return isoX, isoY
end

-- Helper function to count table elements
local function tableCount(t)
    local count = 0
    for _ in pairs(t) do
        count = count + 1
    end
    return count
end

-- Add count function to table if it doesn't exist
if not table.count then
    table.count = tableCount
end

-- Cache for loaded modules to prevent reloading
local moduleCache = {
    tiles = {},
    biomes = {},
    entities = {},
    behaviors = {}
}

local ChunkSystem = {
    initialized = false,
    chunks = {},            -- Stored chunks indexed by "x,y" string
    activeChunks = {},      -- Currently active chunks (referenced by coordinates)
    chunkSize = 8,          -- Reduced from 16 to 8 tiles per chunk
    viewDistance = 2,       -- Reduced from 3 to 2 chunks in each direction
    maxLoadedChunks = 25,   -- Reduced from 100 to 25 (5x5 grid)
    biomeModules = {},      -- Available biome modules (dynamically loaded)
    tileModules = {},       -- Available tile modules (dynamically loaded)
    entityModules = {},     -- Available entity modules (dynamically loaded)
    behaviorModules = {},   -- Available behavior modules (dynamically loaded)
    defaultBiome = nil,     -- Will be set from first loaded biome
    world = nil,            -- Reference to the world
    chunkPool = {},         -- Pool of reusable chunk objects
    spriteCache = {},       -- Cache for loaded sprites
    lastPlayerPos = {x = 0, y = 0}, -- Track last player position for chunk loading
    updateInterval = 1.0,   -- How often to check for chunk updates (in seconds)
    lastUpdate = 0,         -- Last update time
    moduleLoadErrors = {},  -- Track module loading errors
    loadingStats = {        -- Track loading statistics
        tiles = {success = 0, failed = 0},
        biomes = {success = 0, failed = 0},
        entities = {success = 0, failed = 0},
        behaviors = {success = 0, failed = 0}
    },
    entityPool = {},        -- Pool of reusable entity objects
    activeEntities = {},    -- Currently active entities
    lastModuleCheck = 0,    -- Last time we checked for new modules
    moduleCheckInterval = 300, -- Check for new modules every 5 minutes
    combatEnabled = false,  -- Whether combat is currently enabled
    lastChunkCheck = 0,     -- Last time we checked chunk positions
    chunkCheckInterval = 0.5, -- How often to check chunk positions (in seconds)
    visibleTiles = {},      -- Cache of currently visible tiles
    lastTileUpdate = 0,     -- Last time we updated visible tiles
    tileUpdateInterval = 0.25, -- How often to update visible tiles (in seconds)
    timeOfDay = {
        hour = 12,          -- 0-23
        minute = 0,         -- 0-59
        dayLength = 24,     -- hours in a day
        dawnHour = 6,       -- hour when dawn starts
        duskHour = 18,      -- hour when dusk starts
        timeScale = 0.2,    -- Time scale factor (1.0 = real time, 0.2 = 5x slower)
        dayPhase = "day",   -- Current phase of day (day, night, dawn, dusk)
        lastPhaseChange = 0 -- Time of last phase change
    }
}

---------------------------------------------------------------
-- Module-loading functions (defined before init)

-- Load all tile modules from the tiles folder
function ChunkSystem.loadAllTileModules()
    Logger.log("DEBUG: Starting loadAllTileModules...")
    local tilesFolder = "tiles"
    local tileCount = 0
    local files = love.filesystem.getDirectoryItems(tilesFolder)
    Logger.log("DEBUG: Files in tiles/:", table.concat(files, ", "))
    for _, file in ipairs(files) do
        if file:match("%.lua$") then
            local tileName = file:gsub("%.lua$", "")
            Logger.log("DEBUG: Attempting to load tile module:", tileName)
            local success = ChunkSystem.loadTileModule(tileName)
            if success then
                tileCount = tileCount + 1
            else
                Logger.log("DEBUG: Failed loading tile module:", tileName)
            end
        end
    end
    Logger.log("DEBUG: Finished loadAllTileModules. Loaded count:", tileCount)
    return tileCount
end

-- Load all biome modules from the biomes folder
function ChunkSystem.loadAllBiomeModules()
    Logger.log("DEBUG: Starting loadAllBiomeModules...")
    local biomesFolder = "biomes"
    local biomeCount = 0
    local files = love.filesystem.getDirectoryItems(biomesFolder)
    Logger.log("DEBUG: Files in biomes/:", table.concat(files, ", "))
    for _, file in ipairs(files) do
        if file:match("%.lua$") then
            -- Extract biome name directly from file name without trying to handle "-biome" format
            local biomeName = file:gsub("%.lua$", "")

            if biomeName and biomeName ~= file then
                Logger.log("DEBUG: Attempting to load biome module:", biomeName)
                local success = ChunkSystem.loadBiomeModule(biomeName)
                if success then
                    biomeCount = biomeCount + 1

                    -- If this is the hub biome, make special note of it
                    if biomeName == "hub" then
                        Logger.log("DEBUG: Hub biome loaded successfully")
                    end
                else
                    Logger.log("DEBUG: Failed loading biome module:", biomeName)
                end
            end
        end
    end
    Logger.log("DEBUG: Finished loadAllBiomeModules. Loaded count:", biomeCount)
    return biomeCount
end

-- Load all entity modules from the entities folder
function ChunkSystem.loadAllEntityModules()
    Logger.log("DEBUG: Starting loadAllEntityModules...")
    local entitiesFolder = "entities"
    local entityCount = 0
    local files = love.filesystem.getDirectoryItems(entitiesFolder)
    Logger.log("DEBUG: Files in entities/:", table.concat(files, ", "))
    for _, file in ipairs(files) do
        if file:match("%.lua$") then
            local entityName = file:gsub("%.lua$", "")
            if entityName ~= "entity" then -- Skip base entity file if applicable
                Logger.log("DEBUG: Attempting to load entity module:", entityName)
                local success = ChunkSystem.loadEntityModule(entityName)
                if success then
                    entityCount = entityCount + 1
                else
                    Logger.log("DEBUG: Failed loading entity module:", entityName)
                end
            end
        end
    end
    Logger.log("DEBUG: Finished loadAllEntityModules. Loaded count:", entityCount)
    return entityCount
end

-- Load all behavior modules from the behaviors folder
function ChunkSystem.loadAllBehaviorModules()
    Logger.log("DEBUG: Starting loadAllBehaviorModules...")
    local behaviorsFolder = "behaviors"
    local behaviorCount = 0
    local files = love.filesystem.getDirectoryItems(behaviorsFolder)
    Logger.log("DEBUG: Files in behaviors/:", table.concat(files, ", "))
    for _, file in ipairs(files) do
        if file:match("%.lua$") then
            local behaviorName = file:gsub("%.lua$", "")
            if behaviorName:match("behavior%-") then
                behaviorName = behaviorName:gsub("behavior%-", "")
            end
            Logger.log("DEBUG: Attempting to load behavior module:", behaviorName)
            local success = ChunkSystem.loadBehaviorModule(behaviorName)
            if success then
                behaviorCount = behaviorCount + 1
            else
                Logger.log("DEBUG: Failed loading behavior module:", behaviorName)
            end
        end
    end
    Logger.log("DEBUG: Finished loadAllBehaviorModules. Loaded count:", behaviorCount)
    return behaviorCount
end

-- Helper function to safely require a module
local function safeRequire(path)
    local success, result = pcall(require, path)
    if not success then
        Logger.log("ERROR: Failed to require module: " .. path .. "\nError: " .. tostring(result))
        return nil
    end
    return result
end

-- Helper function to initialize a module
local function initializeModule(module, moduleType, name, world)
    if not module or type(module) ~= "table" then
        Logger.log("ERROR: Invalid module type for " .. moduleType .. " module: " .. name)
        return false
    end

    if module.init then
        local success, err = pcall(function()
            if moduleType == "entity" then
                -- Create a temporary entity for initialization
                local tempEntity = {
                    position = {x = 0, y = 0},
                    velocity = {x = 0, y = 0},
                    behaviors = module.behaviors or {},
                    behaviorConfigs = module.behaviorConfigs or {}
                }
                module.init(tempEntity, world)
            else
                module.init(module, world)
            end
        end)

        if not success then
            Logger.log("ERROR: Failed to initialize " .. moduleType .. " module: " .. name .. "\nError: " .. tostring(err))
            return false
        end
    end

    return true
end

-- Load a specific tile module with caching
function ChunkSystem.loadTileModule(tileName)
    if moduleCache.tiles[tileName] then
        Logger.log("DEBUG: Using cached tile module:", tileName)
        return moduleCache.tiles[tileName]
    end

    if ChunkSystem.tileModules[tileName] then
        Logger.log("DEBUG: Tile module already loaded:", tileName)
        return true
    end

    local path = "tiles." .. tileName
    Logger.log("DEBUG: Loading tile module:", path)

    local tileModule = safeRequire(path)
    if not tileModule then
        ChunkSystem.loadingStats.tiles.failed = ChunkSystem.loadingStats.tiles.failed + 1
        ChunkSystem.moduleLoadErrors[tileName] = "Failed to load tile module"
        Logger.log("Failed to load tile module: " .. tileName)
        return false
    end

    if initializeModule(tileModule, "tile", tileName, ChunkSystem.world) then
        ChunkSystem.tileModules[tileName] = tileModule
        moduleCache.tiles[tileName] = tileModule
        ChunkSystem.loadingStats.tiles.success = ChunkSystem.loadingStats.tiles.success + 1
        Logger.log("Loaded tile module: " .. tileName)
        return true
    else
        ChunkSystem.loadingStats.tiles.failed = ChunkSystem.loadingStats.tiles.failed + 1
        ChunkSystem.moduleLoadErrors[tileName] = "Failed to initialize tile module"
        Logger.log("Failed to initialize tile module: " .. tileName)
        return false
    end
end

-- Load a specific biome module with caching
function ChunkSystem.loadBiomeModule(biomeName)
    if moduleCache.biomes[biomeName] then
        Logger.log("DEBUG: Using cached biome module:", biomeName)
        return moduleCache.biomes[biomeName]
    end

    if ChunkSystem.biomeModules[biomeName] then
        Logger.log("DEBUG: Biome module already loaded:", biomeName)
        return true
    end

    -- Try different possible paths:
    -- 1. First try the simple format: biomes.name (this is the actual file structure)
    -- 2. If that fails, try the standard format: biomes.name-biome (for backwards compatibility)
    local paths = {
        "biomes." .. biomeName,
        "biomes." .. biomeName .. "-biome"
    }

    local biomeModule = nil
    for _, path in ipairs(paths) do
        Logger.log("DEBUG: Attempting to load biome module from path:", path)
        biomeModule = safeRequire(path)
        if biomeModule then
            Logger.log("DEBUG: Successfully loaded biome from path:", path)
            break
        end
    end

    if not biomeModule then
        ChunkSystem.loadingStats.biomes.failed = ChunkSystem.loadingStats.biomes.failed + 1
        ChunkSystem.moduleLoadErrors[biomeName] = "Failed to load biome module"
        Logger.log("Failed to load biome module: " .. biomeName)
        return false
    end

    if initializeModule(biomeModule, "biome", biomeName, ChunkSystem.world) then
        -- Special handling for hub biome
        if biomeName == "hub" then
            Logger.log("DEBUG: Initializing hub biome")
        end

        ChunkSystem.biomeModules[biomeName] = biomeModule
        moduleCache.biomes[biomeName] = biomeModule
        ChunkSystem.loadingStats.biomes.success = ChunkSystem.loadingStats.biomes.success + 1
        Logger.log("Loaded biome module: " .. biomeName)
        return true
    else
        ChunkSystem.loadingStats.biomes.failed = ChunkSystem.loadingStats.biomes.failed + 1
        ChunkSystem.moduleLoadErrors[biomeName] = "Failed to initialize biome module"
        Logger.log("Failed to initialize biome module: " .. biomeName)
        return false
    end
end

-- Load a specific entity module with caching
function ChunkSystem.loadEntityModule(entityName)
    if moduleCache.entities[entityName] then
        Logger.log("DEBUG: Using cached entity module:", entityName)
        return moduleCache.entities[entityName]
    end

    if ChunkSystem.entityModules[entityName] then
        Logger.log("DEBUG: Entity module already loaded:", entityName)
        return true
    end

    local path = "entities." .. entityName
    Logger.log("DEBUG: Loading entity module:", path)

    local entityModule = safeRequire(path)
    if not entityModule then
        ChunkSystem.loadingStats.entities.failed = ChunkSystem.loadingStats.entities.failed + 1
        ChunkSystem.moduleLoadErrors[entityName] = "Failed to load entity module"
        Logger.log("Failed to load entity module: " .. entityName)
        return false
    end

    if initializeModule(entityModule, "entity", entityName, ChunkSystem.world) then
        ChunkSystem.entityModules[entityName] = entityModule
        moduleCache.entities[entityName] = entityModule
        ChunkSystem.loadingStats.entities.success = ChunkSystem.loadingStats.entities.success + 1
        Logger.log("Loaded entity module: " .. entityName)
        return true
    else
        ChunkSystem.loadingStats.entities.failed = ChunkSystem.loadingStats.entities.failed + 1
        ChunkSystem.moduleLoadErrors[entityName] = "Failed to initialize entity module"
        Logger.log("Failed to initialize entity module: " .. entityName)
        return false
    end
end

-- Load a specific behavior module with caching
function ChunkSystem.loadBehaviorModule(behaviorName)
    if moduleCache.behaviors[behaviorName] then
        Logger.log("DEBUG: Using cached behavior module:", behaviorName)
        return moduleCache.behaviors[behaviorName]
    end

    if ChunkSystem.behaviorModules[behaviorName] then
        Logger.log("DEBUG: Behavior module already loaded:", behaviorName)
        return true
    end

    -- Try multiple paths to find the module
    local behaviorModule = nil
    local paths = {
        "behaviors." .. behaviorName,                    -- Direct name (behaviors.hunt)
        "behaviors." .. behaviorName:gsub(" ", "_"),     -- Replace spaces with underscores (behaviors.Pack_Hunting)
        "behaviors." .. behaviorName:gsub("-", "_")      -- Replace hyphens with underscores (behaviors.trading_bartering)
    }

    -- For diagnostics
    local attemptedPaths = {}

    -- Try each path
    for _, path in ipairs(paths) do
        table.insert(attemptedPaths, path)
        local success, module = pcall(require, path)
        if success and module then
            behaviorModule = module
            Logger.log("DEBUG: Successfully loaded behavior module using path: " .. path)
            break
        end
    end

    if not behaviorModule then
        ChunkSystem.loadingStats.behaviors.failed = ChunkSystem.loadingStats.behaviors.failed + 1
        ChunkSystem.moduleLoadErrors[behaviorName] = "Failed to load behavior module"
        Logger.log("WARNING: Could not load behavior module: " .. behaviorName)
        Logger.log("DEBUG: Attempted paths: " .. table.concat(attemptedPaths, ", "))
        return false
    end

    if initializeModule(behaviorModule, "behavior", behaviorName, ChunkSystem.world) then
        ChunkSystem.behaviorModules[behaviorName] = behaviorModule
        moduleCache.behaviors[behaviorName] = behaviorModule
        ChunkSystem.loadingStats.behaviors.success = ChunkSystem.loadingStats.behaviors.success + 1
        Logger.log("Loaded behavior module: " .. behaviorName)
        return true
    else
        ChunkSystem.loadingStats.behaviors.failed = ChunkSystem.loadingStats.behaviors.failed + 1
        ChunkSystem.moduleLoadErrors[behaviorName] = "Failed to initialize behavior module"
        Logger.log("Failed to initialize behavior module: " .. behaviorName)
        return false
    end
end

---------------------------------------------------------------
-- Now define the init function (and the rest) after module loaders

-- Helper: Get a chunk from the pool or create a new one
function ChunkSystem:getChunkFromPool()
    if #self.chunkPool > 0 then
        return table.remove(self.chunkPool)
    end
    return {
        x = 0,
        y = 0,
        tiles = {},
        active = false
    }
end

-- Helper: Return a chunk to the pool
function ChunkSystem:returnChunkToPool(chunk)
    if not chunk then return end

    -- Clear chunk data
    chunk.tiles = {}
    chunk.active = false

    -- Return to pool
    table.insert(self.chunkPool, chunk)
end

-- Optimized chunk creation with pooling and pre-allocation
function ChunkSystem:createChunk(chunkX, chunkY)
    -- Make sure coordinates are properly normalized
    chunkX = math.floor(chunkX)
    chunkY = math.floor(chunkY)

    -- Check if this chunk already exists
    local key = self:getChunkKey(chunkX, chunkY)
    if self.chunks[key] then
        return self.chunks[key]
    end

    -- Create the chunk from pool
    local chunk = self:getChunkFromPool()
    chunk.x = chunkX
    chunk.y = chunkY
    chunk.active = true
    chunk.lastUpdate = love.timer.getTime()
    chunk.needsUpdate = false
    chunk.error = nil

    -- Special case for hub chunk
    if chunkX == 0 and chunkY == 0 and self.world and self.world.isHubWorld then
        chunk.biome = "hub"
        chunk.isHub = true
    else
        -- Improved concentric rings: core -> varied -> beach -> ocean
        local dist = math.max(math.abs(chunkX), math.abs(chunkY))
        local innerR, midR, outerR = 3, 6, 9
        if not self.defaultBiome then self.defaultBiome = "plains" end
        if dist < innerR then
            -- Central plains
            chunk.biome = self.defaultBiome
        elseif dist == outerR - 1 then
            -- Coastal beaches
            chunk.biome = "beach"
        elseif dist >= outerR then
            -- Open ocean
            chunk.biome = "ocean"
        elseif dist < midR then
            -- Mid-range varied
            local biomes = {"plains", "forest", "swamp"}
            local idx = (math.abs(chunkX * 3 + chunkY * 5) % #biomes) + 1
            chunk.biome = biomes[idx]
        else
            -- Outer landforms before beach
            local biomes = {"desert", "mountains", "river", "hills"}
            local idx = (math.abs(chunkX * 7 + chunkY * 11) % #biomes) + 1
            chunk.biome = biomes[idx]
        end
    end

    -- Determine the default tile type for this biome - simplified
    local tileType = "grass"
    if chunk.biome == "desert" then
        tileType = "sand"
    elseif chunk.biome == "ocean" or chunk.biome == "river" then
        tileType = "water"
    elseif chunk.biome == "mountains" then
        tileType = "stone"
    elseif chunk.biome == "swamp" then
        tileType = "mud"
    elseif chunk.biome == "hub" then
        tileType = "hub_floor"
    end

    chunk.defaultTileType = tileType

    -- Pre-create all tiles for the chunk immediately with variable heights
    chunk.tiles = {}

    -- Generate a noise map for height variation
    local heightNoise = {}
    local heightScale = 0.05  -- Controls the scale of height variation
    local heightMultiplier = 2.0  -- Controls the intensity of height variation

    -- Generate height noise values for the entire chunk
    for x = 0, self.chunkSize - 1 do
        heightNoise[x] = {}
        for y = 0, self.chunkSize - 1 do
            local worldX = chunkX * self.chunkSize + x
            local worldY = chunkY * self.chunkSize + y

            -- Use Perlin noise for smooth height transitions
            local noise = love.math.noise(worldX * heightScale, worldY * heightScale)

            -- Transform noise to height value (0.0 to heightMultiplier)
            heightNoise[x][y] = noise * heightMultiplier
        end
    end

    -- Create tiles with height values
    for x = 0, self.chunkSize - 1 do
        chunk.tiles[x] = {}
        for y = 0, self.chunkSize - 1 do
            -- Apply biome-specific height adjustments
            local height = heightNoise[x][y]

            -- Adjust height based on biome
            if chunk.biome == "mountains" then
                height = height * 2.0  -- Mountains are higher
            elseif chunk.biome == "hills" then
                height = height * 1.5  -- Hills are moderately high
            elseif chunk.biome == "plains" then
                height = height * 0.5  -- Plains are relatively flat
            elseif chunk.biome == "ocean" or chunk.biome == "river" then
                height = 0  -- Water is at base level
            end

            -- Create the tile with height property
            chunk.tiles[x][y] = {
                x = chunkX * self.chunkSize + x,
                y = chunkY * self.chunkSize + y,
                biome = chunk.biome,
                type = tileType,
                sprite = nil,
                needsUpdate = true,
                lastUpdate = 0,
                height = height  -- Add height property
            }
        end
    end

    -- Bake full tile layer (base, biome/season, decorations) into one Canvas
    do
        local tileW, tileH = 64, 32
        local csz = self.chunkSize
        local canvas = love.graphics.newCanvas(csz * tileW, csz * tileH)
        love.graphics.push()
        love.graphics.origin()
        love.graphics.setCanvas(canvas)
        love.graphics.clear(0,0,0,0)
        for tx = 0, csz - 1 do
            for ty = 0, csz - 1 do
                local tile = chunk.tiles[tx][ty]
                local wx = chunk.x * csz + tx
                local wy = chunk.y * csz + ty
                local tileHeight = tile.height or 0
                local isoX = (wx - wy) * (tileW/2)
                local isoY = (wx + wy) * (tileH/2) - (tileHeight * tileH / 2)
                -- Get base color
                local baseCol = TileDecorations.getBaseColor(tile.biome, wx, wy) or {1,1,1,1}

                -- Draw side faces for elevated tiles
                if tileHeight > 0 then
                    -- Left face (darker)
                    love.graphics.setColor(baseCol[1]*0.7, baseCol[2]*0.7, baseCol[3]*0.7, baseCol[4] or 1)
                    love.graphics.polygon("fill",
                        isoX - tileW/2, isoY,                  -- top-left
                        isoX, isoY + tileH/2,                  -- top-bottom
                        isoX, isoY + tileH/2 + tileHeight * tileH/2, -- ground-bottom
                        isoX - tileW/2, isoY + tileHeight * tileH/2  -- ground-left
                    )

                    -- Right face (slightly brighter)
                    love.graphics.setColor(baseCol[1]*0.85, baseCol[2]*0.85, baseCol[3]*0.85, baseCol[4] or 1)
                    love.graphics.polygon("fill",
                        isoX + tileW/2, isoY,                  -- top-right
                        isoX, isoY + tileH/2,                  -- top-bottom
                        isoX, isoY + tileH/2 + tileHeight * tileH/2, -- ground-bottom
                        isoX + tileW/2, isoY + tileHeight * tileH/2  -- ground-right
                    )
                end

                -- Base diamond (top face)
                love.graphics.setColor(baseCol)
                love.graphics.polygon("fill",
                    isoX,         isoY - tileH/2,
                    isoX + tileW/2, isoY,
                    isoX,         isoY + tileH/2,
                    isoX - tileW/2, isoY
                )
                love.graphics.setColor(0,0,0,0.2)
                love.graphics.polygon("line",
                    isoX,         isoY - tileH/2,
                    isoX + tileW/2, isoY,
                    isoX,         isoY + tileH/2,
                    isoX - tileW/2, isoY
                )
                -- Highlight overlay
                love.graphics.setColor(1,1,1,0.1)
                local hlw, hlh = tileW * 0.6, tileH * 0.6
                love.graphics.polygon("fill",
                    isoX,         isoY - hlh/2,
                    isoX + hlw/2, isoY,
                    isoX,         isoY + hlh/2,
                    isoX - hlw/2, isoY
                )
                -- Seasonal overlays (baked)
                local season = self.world.season or "summer"
                if season == "winter" then
                    local snow = tile.decorations and tile.decorations.snow or 0
                    local a = math.min(1, snow * 0.1)
                    if a > 0 then
                        love.graphics.setColor(1,1,1,a)
                        love.graphics.polygon("fill",
                            isoX, isoY - tileH/2,
                            isoX + tileW/2, isoY,
                            isoX, isoY + tileH/2,
                            isoX - tileW/2, isoY
                        )
                    end
                elseif season == "spring" then
                    local freq2 = 0.1
                    local h2 = love.math.noise(wx * freq2, wy * freq2)
                    if h2 < 0.3 then
                        local puddleA = math.min(1, (0.3 - h2) * 2)
                        love.graphics.setColor(0.2,0.4,0.8,puddleA * 0.5)
                        love.graphics.polygon("fill",
                            isoX, isoY - tileH/2,
                            isoX + tileW/2, isoY,
                            isoX, isoY + tileH/2,
                            isoX - tileW/2, isoY
                        )
                    end
                elseif (season == "autumn" or season == "fall") and tile.biome == "forest" then
                    local freq3 = 0.2
                    local lf = love.math.noise(wx * freq3, wy * freq3)
                    if lf > 0.95 then
                        love.graphics.setColor(1,0.5,0,0.7)
                        love.graphics.polygon("fill",
                            isoX, isoY - tileH/2,
                            isoX + tileW/2, isoY,
                            isoX, isoY + tileH/2,
                            isoX - tileW/2, isoY
                        )
                    end
                end
                -- Decorations
                local decs = TileDecorations.generateDecorations(tile, wx, wy, tile.biome, self.world.season)
                for _, dec in ipairs(decs) do
                    local lean = (dec.type=="grass" or dec.type=="flowers") and 1 or 0
                    local px = isoX - tileW/2 + (dec.offsetX or 0)
                    local py = isoY - tileH/2 + (dec.offsetY or 0)
                    if dec.type == "grass" then
                        TileDecorations.drawGrass(px, py, dec.color, dec.height, dec.density)
                    elseif dec.type == "flowers" then
                        TileDecorations.drawFlowers(px, py, dec.color, dec.density)
                    elseif dec.type == "rocks" then
                        TileDecorations.drawRocks(px, py, dec.color, dec.density)
                    end
                end
            end
        end
        love.graphics.setCanvas()
        love.graphics.pop()
        chunk.decorationCanvas = canvas
    end

    -- Store in the chunks table and active chunks list
    self.chunks[key] = chunk
    table.insert(self.activeChunks, chunk)

    return chunk
end

-- Optimized chunk update with dirty flag system
function ChunkSystem:updateChunk(chunk)
    if not chunk or not chunk.active then return end

    local currentTime = love.timer.getTime()
    if not chunk.needsUpdate and (currentTime - chunk.lastUpdate) < self.updateInterval then
        return
    end

    chunk.needsUpdate = false
    chunk.lastUpdate = currentTime

    for x = 0, self.chunkSize - 1 do
        for y = 0, self.chunkSize - 1 do
            local tile = chunk.tiles[x][y]
            if tile and tile.needsUpdate then
                self:updateTile(tile)
                tile.needsUpdate = false
                tile.lastUpdate = currentTime
            end
        end
    end
end

-- Optimized tile update
function ChunkSystem:updateTile(tile)
    if not tile then return end

    -- Update tile sprite if needed
    if not tile.sprite then
        tile.sprite = self:getTileSprite(tile.biome, tile.x, tile.y)
    end

    -- Apply any biome-specific updates
    local biomeModule = self.biomeModules[tile.biome]
    if biomeModule and biomeModule.updateTile then
        biomeModule.updateTile(tile)
    end
end

-- Update visible tiles based on camera position - with proper isometric transformation
function ChunkSystem:updateVisibleTiles()
    local camX, camY = 0, 0
    if _G.Engine and Engine.systems and Engine.systems.viewportManager and Engine.systems.viewportManager.getOffset then
        camX, camY = Engine.systems.viewportManager.getOffset()
    end

    -- Get screen dimensions
    local screenWidth = love.graphics.getWidth()
    local screenHeight = love.graphics.getHeight()

    -- Clear previous visible tiles
    self.visibleTiles = {}

    -- Define tile dimensions for proper isometric display
    local tileWidth, tileHeight = 64, 32  -- Half height for isometric tiles

    -- Calculate horizon line (where tiles should fade out)
    local horizonY = screenHeight * 0.6  -- Horizon at 60% of screen height
    local horizonFadeStart = screenHeight * 0.4  -- Start fading at 40% of screen height
    local horizonFadeEnd = screenHeight * 0.8  -- Complete fade at 80% of screen height

    -- Get all potentially visible chunks
    local visibleChunks = {}

    -- Calculate buffer zone to include chunks just outside the screen
    local bufferZone = 2  -- Number of additional chunks to include beyond visible area

    -- Calculate visible area in world coordinates with buffering
    local visibleStartX = math.floor((camX - screenWidth/2 - bufferZone * tileWidth) / tileWidth)
    local visibleEndX = math.ceil((camX + screenWidth/2 + bufferZone * tileWidth) / tileWidth)
    local visibleStartY = math.floor((camY - screenHeight/2 - bufferZone * tileHeight) / tileHeight)
    local visibleEndY = math.ceil((camY + screenHeight/2 + bufferZone * tileHeight) / tileHeight)

    -- Get chunks in visible area
    for x = visibleStartX, visibleEndX do
        for y = visibleStartY, visibleEndY do
            local chunk = self:getChunkAt(x, y)
            if chunk then
                table.insert(visibleChunks, chunk)
            end
        end
    end

    for _, chunk in ipairs(visibleChunks) do
        for y = 0, self.chunkSize - 1 do  -- Process tiles by row for proper sorting
            for x = 0, self.chunkSize - 1 do
                local tile = chunk.tiles[x][y]
                if tile then
                    -- Calculate world coordinates of the tile
                    local worldX = tile.x * tileWidth
                    local worldY = tile.y * tileHeight

                    -- Convert to isometric
                    local isoX, isoY = toIsometric(worldX, worldY, tileWidth, tileHeight)

                    -- Adjust for camera
                    local screenX = isoX - camX
                    local screenY = isoY - camY

                    -- Calculate distance from horizon for fade effect
                    local distanceFromHorizon = screenY - horizonY
                    local fadeFactor = 1.0

                    if distanceFromHorizon > 0 then
                        -- Tile is below horizon
                        fadeFactor = math.max(0, 1 - (distanceFromHorizon / (horizonFadeEnd - horizonY)))
                    else
                        -- Tile is above horizon
                        fadeFactor = math.min(1, 1 + (distanceFromHorizon / (horizonY - horizonFadeStart)))
                    end

                    -- Check if this tile is potentially visible (with margin and fade factor)
                    if fadeFactor > 0.01 and
                       (screenX + tileWidth > -tileWidth) and
                       (screenX < screenWidth + tileWidth) and
                       (screenY + tileHeight > -tileHeight) and
                       (screenY < screenHeight + tileHeight) then

                        -- Calculate depth for z-ordering (deeper tiles drawn first)
                        local depth = tile.x + tile.y

                        -- Add to visible tiles with screen coordinates, depth, and fade factor
                        table.insert(self.visibleTiles, {
                            tile = tile,
                            screenX = screenX,
                            screenY = screenY,
                            depth = depth,
                            fadeFactor = fadeFactor
                        })
                    end
                end
            end
        end
    end

    -- Sort tiles by depth to ensure proper rendering order
    table.sort(self.visibleTiles, function(a, b)
        return a.depth < b.depth  -- Lower depth (further away) tiles drawn first
    end)
end

-- Update active entities based on view distance
function ChunkSystem:updateActiveEntities()
    -- Skip if no world or entity system
    if not self.world or not self.world.entitySystem then
        return
    end

    -- Check if getAll method exists
    if not self.world.entitySystem.getAll or type(self.world.entitySystem.getAll) ~= "function" then
        -- Entity system doesn't have getAll method
        return
    end

    -- Get all entities
    local allEntities = self.world.entitySystem:getAll()

    -- Clear active entities
    self.activeEntities = {}

    -- Get player position
    local playerPos = {x = 0, y = 0}
    if self.world.player then
        playerPos = self.world.player.position
    end

    -- Calculate view distance in world coordinates
    local viewDistanceInTiles = self.viewDistance * self.chunkSize

    -- Add entities within view distance to active entities
    for _, entity in pairs(allEntities) do
        if entity.position then
            local dx = entity.position.x - playerPos.x
            local dy = entity.position.y - playerPos.y
            local distSquared = dx * dx + dy * dy

            -- Use squared distance for efficiency (avoiding square root)
            if distSquared <= viewDistanceInTiles * viewDistanceInTiles then
                table.insert(self.activeEntities, entity)
            end
        end
    end
end

-- Update time of day and handle day/night transitions
function ChunkSystem:updateTimeOfDay(dt)
    -- Skip if timeOfDay is not initialized
    if not self.timeOfDay then
        self.timeOfDay = {
            hour = 12,          -- 0-23
            minute = 0,         -- 0-59
            dayLength = 24,     -- hours in a day
            dawnHour = 6,       -- hour when dawn starts
            duskHour = 18,      -- hour when dusk starts
            timeScale = 0.2,    -- Time scale factor (1.0 = real time, 0.2 = 5x slower)
            dayPhase = "day",   -- Current phase of day (day, night, dawn, dusk)
            lastPhaseChange = 0 -- Time of last phase change
        }
        return
    end

    -- Update time using time scale
    local scaledDt = dt * self.timeOfDay.timeScale
    self.timeOfDay.minute = self.timeOfDay.minute + scaledDt * 60

    -- Handle minute rollover
    if self.timeOfDay.minute >= 60 then
        self.timeOfDay.minute = 0
        self.timeOfDay.hour = (self.timeOfDay.hour + 1) % 24

        -- Determine new day phase
        local newPhase = self:getDayPhase()
        if newPhase ~= self.timeOfDay.dayPhase then
            self.timeOfDay.dayPhase = newPhase
            self.timeOfDay.lastPhaseChange = love.timer.getTime()

            -- Notify world of phase change if it has a weather system
            if self.world and self.world.weatherSystem then
                self.world.weatherSystem:onDayPhaseChange(newPhase, self.timeOfDay)
            end
        end
    end
end

-- Get current phase of day
function ChunkSystem:getDayPhase()
    local hour = self.timeOfDay.hour
    local dawn = self.timeOfDay.dawnHour
    local dusk = self.timeOfDay.duskHour

    -- Dawn transition (1 hour before dawn)
    if hour >= (dawn - 1) and hour < dawn then
        return "dawn"
    -- Day time
    elseif hour >= dawn and hour < dusk then
        return "day"
    -- Dusk transition (1 hour before dusk)
    elseif hour >= (dusk - 1) and hour < dusk then
        return "dusk"
    -- Night time
    else
        return "night"
    end
end

-- Update function with improved time handling
function ChunkSystem:update(dt)
    -- Ensure we're being called as a method
    if type(self) ~= "table" then
        Logger.log("ERROR: update method called without proper self binding")
        return
    end

    local currentTime = love.timer.getTime()

    -- Check for new modules periodically (every 5 minutes)
    if currentTime - self.lastModuleCheck > self.moduleCheckInterval then
        self:checkForNewModules()
        self.lastModuleCheck = currentTime
    end

    -- Check chunk positions less frequently
    if currentTime - self.lastChunkCheck > self.chunkCheckInterval then
        self:updateChunkPositions()
        self.lastChunkCheck = currentTime
    end

    -- Update visible tiles periodically
    if currentTime - self.lastTileUpdate > self.tileUpdateInterval then
        self:updateVisibleTiles()
        self.lastTileUpdate = currentTime
    end

    -- Update active entities based on view distance
    self:updateActiveEntities()

    -- Update active chunks
    for _, chunk in ipairs(self.activeChunks) do
        self:updateChunk(chunk)
    end

    -- Update time of day with improved handling
    self:updateTimeOfDay(dt)
end

-- Update chunk positions based on player movement - optimized to reduce unnecessary cycling
function ChunkSystem:updateChunkPositions()
    local playerPos = {x = 0, y = 0}
    if self.world and self.world.player then
        playerPos = self.world.player.position
    end

    -- Calculate which chunks should be active based on player position
    local playerChunkX = math.floor(playerPos.x / self.chunkSize)
    local playerChunkY = math.floor(playerPos.y / self.chunkSize)

    -- Only update if player has moved to a new chunk or forced update
    if playerChunkX == self.lastPlayerPos.x and playerChunkY == self.lastPlayerPos.y then
        return  -- No change in position, skip update
    end

    self.lastPlayerPos = {x = playerChunkX, y = playerChunkY}

    -- Track chunks that should be active
    local chunksToKeepActive = {}

    -- Determine which chunks should be loaded within view distance
    for dx = -self.viewDistance, self.viewDistance do
        for dy = -self.viewDistance, self.viewDistance do
            local chunkX = playerChunkX + dx
            local chunkY = playerChunkY + dy
            local key = self:getChunkKey(chunkX, chunkY)

            chunksToKeepActive[key] = true

            -- Create chunk if it doesn't exist
            if not self.chunks[key] then
                self:createChunk(chunkX, chunkY)
            end
        end
    end

    -- Unload chunks outside view distance
    for i = #self.activeChunks, 1, -1 do
        local chunk = self.activeChunks[i]
        local key = self:getChunkKey(chunk.x, chunk.y)

        if not chunksToKeepActive[key] then
            -- Remove from active chunks
            table.remove(self.activeChunks, i)
            -- Return to pool
            self:returnChunkToPool(chunk)
            -- Remove from chunks table
            self.chunks[key] = nil
        end
    end

    -- Enforce maximum loaded chunks: drop furthest chunks if over limit
    if self.maxLoadedChunks and #self.activeChunks > self.maxLoadedChunks then
        -- Compute player chunk coordinates
        local px, py = playerChunkX, playerChunkY
        -- Sort activeChunks by distance descending
        table.sort(self.activeChunks, function(a, b)
            local da = math.abs(a.x - px) + math.abs(a.y - py)
            local db = math.abs(b.x - px) + math.abs(b.y - py)
            return da > db
        end)
        -- Remove extra chunks
        while #self.activeChunks > self.maxLoadedChunks do
            local c = table.remove(self.activeChunks, 1)
            self:returnChunkToPool(c)
            self.chunks[self:getChunkKey(c.x, c.y)] = nil
        end
    end
end

-- Get or generate a chunk at the given coordinates (in chunk space) - reduced logging
function ChunkSystem:getChunkAt(chunkX, chunkY)
    -- Ensure coordinates are numbers
    if type(chunkX) ~= "number" or type(chunkY) ~= "number" then
        return nil
    end

    local key = self:getChunkKey(chunkX, chunkY)
    if not key then
        return nil
    end

    if self.chunks[key] then
        return self.chunks[key]
    else
        return self:createChunk(chunkX, chunkY)
    end
end

-- Initialize the chunk system
function ChunkSystem.init(world, options)
    Logger.log("\n=== CHUNK SYSTEM INITIALIZATION ===")
    options = options or {}

    -- Set reference to world first
    if not world then
        Logger.log("ERROR: World reference is required for ChunkSystem initialization!")
        return nil
    end
    ChunkSystem.world = world

    -- Apply options
    if options.chunkSize then ChunkSystem.chunkSize = options.chunkSize end
    if options.viewDistance then ChunkSystem.viewDistance = options.viewDistance end
    if options.maxLoadedChunks then ChunkSystem.maxLoadedChunks = options.maxLoadedChunks end

    Logger.log("\nLoading modules...")
    -- Initialize other systems
    local tileCount = ChunkSystem.loadAllTileModules()
    local biomeCount = ChunkSystem.loadAllBiomeModules()
    local entityCount = ChunkSystem.loadAllEntityModules()
    local behaviorCount = ChunkSystem.loadAllBehaviorModules()

    Logger.log("\n=== MODULE LOADING SUMMARY ===")
    Logger.log(string.format("Tiles loaded: %d", tileCount))
    Logger.log(string.format("Biomes loaded: %d", biomeCount))
    Logger.log(string.format("Entities loaded: %d", entityCount))
    Logger.log(string.format("Behaviors loaded: %d", behaviorCount))

    -- Set default biome
    if options.defaultBiome and ChunkSystem.biomeModules[options.defaultBiome] then
        ChunkSystem.defaultBiome = options.defaultBiome
        Logger.log(string.format("\nUsing specified default biome: %s", options.defaultBiome))
    else
        local foundDefault = false
        for biomeName, biomeData in pairs(ChunkSystem.biomeModules) do
            if type(biomeData) == "table" then
                ChunkSystem.defaultBiome = biomeName
                foundDefault = true
                Logger.log(string.format("\nUsing first available biome as default: %s", biomeName))
                break
            end
        end
        if not foundDefault then
            ChunkSystem.defaultBiome = nil
            Logger.log("\nWARNING: No biomes loaded or available!")
        end
    end

    ChunkSystem.initialized = true
    Logger.log("\n=== CHUNK SYSTEM INITIALIZATION COMPLETE ===")

    -- Create a new instance of ChunkSystem
    local instance = setmetatable({}, { __index = ChunkSystem })
    for k, v in pairs(ChunkSystem) do
        if type(v) == "function" then
            instance[k] = v
        else
            instance[k] = v
        end
    end

    return instance
end

-- Create a new ChunkSystem instance with the given options
function ChunkSystem.new(options)
    Logger.log("Creating new ChunkSystem instance...")
    options = options or {}

    -- Check if modules have been loaded and load them if not
    if table.count(ChunkSystem.biomeModules) == 0 then
        Logger.log("No modules loaded yet, loading all modules first...")
        ChunkSystem.loadAllTileModules()
        ChunkSystem.loadAllBiomeModules()
        ChunkSystem.loadAllEntityModules()
        ChunkSystem.loadAllBehaviorModules()
    end

    -- Create a new instance
    local instance = setmetatable({}, { __index = ChunkSystem })

    -- Copy non-function properties
    instance.chunks = {}
    instance.activeChunks = {}
    instance.chunkSize = options.chunkSize or ChunkSystem.chunkSize
    instance.viewDistance = options.viewDistance or ChunkSystem.viewDistance
    instance.maxLoadedChunks = options.maxLoadedChunks or ChunkSystem.maxLoadedChunks

    -- Copy module references from global ChunkSystem
    instance.biomeModules = {}
    instance.tileModules = {}
    instance.entityModules = {}
    instance.behaviorModules = {}

    -- Copy loaded modules
    for name, module in pairs(ChunkSystem.biomeModules) do
        instance.biomeModules[name] = module
    end
    for name, module in pairs(ChunkSystem.tileModules) do
        instance.tileModules[name] = module
    end
    for name, module in pairs(ChunkSystem.entityModules) do
        instance.entityModules[name] = module
    end
    for name, module in pairs(ChunkSystem.behaviorModules) do
        instance.behaviorModules[name] = module
    end

    -- Copy default biome
    instance.defaultBiome = ChunkSystem.defaultBiome

    instance.chunkPool = {}
    instance.spriteCache = {}
    instance.lastPlayerPos = {x = 0, y = 0}
    instance.lastUpdate = 0
    instance.moduleLoadErrors = {}
    instance.loadingStats = {
        tiles = {success = 0, failed = 0},
        biomes = {success = 0, failed = 0},
        entities = {success = 0, failed = 0},
        behaviors = {success = 0, failed = 0}
    }
    instance.entityPool = {}
    instance.activeEntities = {}
    instance.visibleTiles = {}

    instance.initialized = true
    Logger.log("New ChunkSystem instance created with chunk size: " .. instance.chunkSize)
    Logger.log(string.format("Copied %d biome modules, %d tile modules, %d entity modules, %d behavior modules",
        table.count(instance.biomeModules),
        table.count(instance.tileModules),
        table.count(instance.entityModules),
        table.count(instance.behaviorModules)))

    return instance
end

-- Define create as an alias for init
ChunkSystem.create = ChunkSystem.init

-- Helper: get a unique key for a chunk based on its coordinates
function ChunkSystem:getChunkKey(x, y)
    -- Ensure x and y are numbers
    if type(x) ~= "number" or type(y) ~= "number" then
        Logger.log("ERROR: getChunkKey received invalid coordinates - x:", type(x), "y:", type(y))
        return nil
    end

    -- For negative coordinates, use a special format to ensure uniqueness
    local xStr = x < 0 and "n" .. math.abs(x) or tostring(x)
    local yStr = y < 0 and "n" .. math.abs(y) or tostring(y)

    return xStr .. "," .. yStr
end

-- Example function to choose a biome for a tile
function ChunkSystem:getBiomeForTile(tileX, tileY)
    -- Check if a biome has been explicitly set for this tile
    -- First, calculate which chunk this tile belongs to
    local chunkX = math.floor(tileX / self.chunkSize)
    local chunkY = math.floor(tileY / self.chunkSize)

    -- Check if the chunk exists
    local key = self:getChunkKey(chunkX, chunkY)
    if self.chunks[key] then
        local chunk = self.chunks[key]

        -- Handle hub chunk special case
        if chunk.isHub then
            return "hub"
        end

        -- Calculate local coordinates within the chunk
        local localX = tileX % self.chunkSize
        local localY = tileY % self.chunkSize

        -- Check if the tile already has a biome assigned
        if chunk.tiles[localX] and chunk.tiles[localX][localY] and chunk.tiles[localX][localY].biome then
            return chunk.tiles[localX][localY].biome
        end
    end

    -- If no specific biome has been set, use the default biome
    return self.defaultBiome or "plains"
end

-- Set biome at specific coordinates - simplified to set for entire chunk
function ChunkSystem:setBiomeAt(x, y, biomeName)
    -- Basic validation
    if not biomeName then
        biomeName = self.defaultBiome or "plains"
    end

    -- Normalize coordinates
    x = math.floor(x)
    y = math.floor(y)

    -- Convert to chunk coordinates
    local chunkX = math.floor(x / self.chunkSize)
    local chunkY = math.floor(y / self.chunkSize)

    -- Get or create the chunk
    local chunk = self:getChunkAt(chunkX, chunkY)
    if not chunk then
        return false
    end

    -- Only set biome if it's changed
    if chunk.biome == biomeName then
        return true
    end

    -- Set the biome for the entire chunk
    chunk.biome = biomeName

    -- Determine the default tile type for this biome
    local tileType = "grass"
    if biomeName == "desert" then
        tileType = "sand"
    elseif biomeName == "ocean" or biomeName == "river" then
        tileType = "water"
    elseif biomeName == "mountains" then
        tileType = "stone"
    elseif biomeName == "swamp" then
        tileType = "mud"
    elseif biomeName == "hub" then
        tileType = "hub_floor"
    end

    chunk.defaultTileType = tileType

    -- Update all tiles in the chunk with new biome and type, preserving height
    for x = 0, self.chunkSize - 1 do
        for y = 0, self.chunkSize - 1 do
            if chunk.tiles[x] and chunk.tiles[x][y] then
                local tile = chunk.tiles[x][y]
                local currentHeight = tile.height or 0

                -- Update tile properties
                tile.biome = biomeName
                tile.type = tileType
                tile.needsUpdate = true

                -- Adjust height based on new biome while preserving some of the original variation
                if biomeName == "mountains" then
                    tile.height = currentHeight * 0.3 + 2.0  -- Mountains are higher
                elseif biomeName == "hills" then
                    tile.height = currentHeight * 0.3 + 1.0  -- Hills are moderately high
                elseif biomeName == "plains" then
                    tile.height = currentHeight * 0.3 + 0.2  -- Plains are relatively flat
                elseif biomeName == "ocean" or biomeName == "river" then
                    tile.height = 0  -- Water is at base level
                else
                    -- For other biomes, preserve height but scale it appropriately
                    tile.height = currentHeight * 0.5
                end
            end
        end
    end

    chunk.needsUpdate = true
    return true
end

-- Optimized sprite loading with caching
function ChunkSystem:getTileSprite(biome, tileX, tileY)
    -- Don't always use grass - use the tile's actual type if available
    local tileType = nil

    -- Get the tile at these coordinates to determine its type
    if tileX ~= nil and tileY ~= nil then
        local tile = self:getTileAt(tileX, tileY)
        if tile then
            tileType = tile.type
        end
    end

    -- Default to grass if no type was determined
    if not tileType or tileType == "" then
        tileType = "grass"
    end

    -- Get the module for this tile type
    local tileModule = ChunkSystem.getTileModule(tileType)
    if not tileModule then
        Logger.log("Warning: Could not get tile module for '" .. tileType .. "', using placeholder")
        return nil
    end

    -- Use module ID if available, otherwise use the type
    local tileId = tileModule.id or tileType

    -- Check sprite cache first
    if self.spriteCache[tileId] then
        return self.spriteCache[tileId]
    end

    -- Try multiple paths to find the sprite
    local possiblePaths = {
        "assets/tiles/" .. tileId .. ".png",
        "assets/tiles/" .. tileType .. ".png",
        "assets/" .. tileId .. ".png",
        "assets/" .. tileType .. ".png"
    }

    -- Try each path until we find one that works
    for _, path in ipairs(possiblePaths) do
        Logger.log("DEBUG: Attempting to load sprite from: " .. path)

        if love.filesystem.getInfo(path) then
            local success, sprite = pcall(love.graphics.newImage, path)
            if success and sprite then
                -- Cache the sprite
                self.spriteCache[tileId] = sprite
                Logger.log("DEBUG: Successfully loaded and cached sprite for tile: " .. tileId .. " from " .. path)
                return sprite
            end
        end
    end

    -- Create a simple colored rectangle as a fallback
    local fallbackSize = 64
    local fallbackCanvas = love.graphics.newCanvas(fallbackSize, fallbackSize)
    love.graphics.setCanvas(fallbackCanvas)

    -- Use different colors for different tile types
    local r, g, b = 0.5, 0.5, 0.5 -- Default gray
    if tileType == "grass" then
        r, g, b = 0.2, 0.7, 0.2
    elseif tileType == "water" then
        r, g, b = 0.2, 0.2, 0.8
    elseif tileType == "sand" then
        r, g, b = 0.9, 0.8, 0.2
    elseif tileType == "stone" then
        r, g, b = 0.6, 0.6, 0.6
    elseif tileType == "mud" then
        r, g, b = 0.5, 0.3, 0.1
    end

    love.graphics.setColor(r, g, b)
    love.graphics.rectangle("fill", 0, 0, fallbackSize, fallbackSize)
    love.graphics.setColor(1, 1, 1)
    love.graphics.setCanvas()

    -- Cache the fallback sprite
    self.spriteCache[tileId] = fallbackCanvas
    Logger.log("Warning: Created fallback sprite for tile '" .. tileId .. "'")

    return fallbackCanvas
end

-- Get a tile module, loading it only once
function ChunkSystem.getTileModule(tileName)
    -- Ensure we have a valid tileName
    if not tileName or tileName == "" or tileName == "default" then
        tileName = "grass"
    end

    -- Check if module is already loaded - return immediately if so
    if ChunkSystem.tileModules[tileName] then
        return ChunkSystem.tileModules[tileName]
    end

    -- Try to load the module once
    if ChunkSystem.loadTileModule(tileName) then
        return ChunkSystem.tileModules[tileName]
    end

    -- If we couldn't load it, create a placeholder once
    local placeholderModule = {
        id = "placeholder_" .. tileName,
        name = "Placeholder " .. tileName,
        type = "placeholder",
        solid = false,
        init = function() end,
        update = function() end,
        render = function() end
    }

    -- Store it so we don't try loading it again
    ChunkSystem.tileModules[tileName] = placeholderModule
    moduleCache.tiles[tileName] = placeholderModule

    return placeholderModule
end

-- Get tile at world coordinates - simplified
function ChunkSystem:getTileAt(x, y)
    -- Ensure x and y are numbers and convert to integers
    if type(x) == "table" then
        x = x.x or 0
    end
    if type(y) == "table" then
        y = y.y or 0
    end

    x = math.floor(tonumber(x) or 0)
    y = math.floor(tonumber(y) or 0)

    -- Convert world coordinates to chunk coordinates
    local chunkX = math.floor(x / self.chunkSize)
    local chunkY = math.floor(y / self.chunkSize)

    -- Get or create chunk
    local chunk = self:getChunkAt(chunkX, chunkY)
    if not chunk then
        return nil
    end

    -- Convert world coordinates to local chunk coordinates
    local localX = x - (chunkX * self.chunkSize)
    local localY = y - (chunkY * self.chunkSize)

    -- Ensure localX and localY are within valid range
    if localX < 0 or localX >= self.chunkSize or localY < 0 or localY >= self.chunkSize then
        return nil
    end

    -- Return the tile (should always exist now since we pre-create them)
    return chunk.tiles[localX][localY]
end

-- Check for new modules only when needed
function ChunkSystem:checkForNewModules()
    Logger.log("Checking for new modules...")

    -- Get current files
    local currentFiles = {
        tiles = love.filesystem.getDirectoryItems("tiles"),
        biomes = love.filesystem.getDirectoryItems("biomes"),
        entities = love.filesystem.getDirectoryItems("entities"),
        behaviors = love.filesystem.getDirectoryItems("behaviors")
    }

    -- Check for new files and load them
    for _, file in ipairs(currentFiles.tiles) do
        if file:match("%.lua$") then
            local tileName = file:gsub("%.lua$", "")
            if not self.tileModules[tileName] then
                self:loadTileModule(tileName)
            end
        end
    end

    -- Check biomes
    for _, file in ipairs(currentFiles.biomes) do
        if file:match("%.lua$") then
            local biomeName = file:gsub("%-biome%.lua$", "")
            if biomeName and biomeName ~= file and not self.biomeModules[biomeName] then
                self:loadBiomeModule(biomeName)
            end
        end
    end

    -- Check entities
    for _, file in ipairs(currentFiles.entities) do
        if file:match("%.lua$") then
            local entityName = file:gsub("%.lua$", "")
            if entityName ~= "entity" and not self.entityModules[entityName] then
                self:loadEntityModule(entityName)
            end
        end
    end

    -- Check behaviors
    for _, file in ipairs(currentFiles.behaviors) do
        if file:match("%.lua$") then
            local behaviorName = file:gsub("%.lua$", "")
            if behaviorName:match("behavior%-") then
                behaviorName = behaviorName:gsub("behavior%-", "")
            end
            if not self.behaviorModules[behaviorName] then
                self:loadBehaviorModule(behaviorName)
            end
        end
    end
end

-- Toggle combat system
function ChunkSystem:toggleCombat(enabled)
    self.combatEnabled = enabled
    Logger.log("Combat system " .. (enabled and "enabled" or "disabled"))

    -- Update entity behaviors based on combat state
    for _, entity in ipairs(self.activeEntities) do
        if entity.behaviors then
            for _, behavior in ipairs(entity.behaviors) do
                if behavior.type == "combat" then
                    behavior.active = enabled
                end
            end
        end
    end
end

-- Save all chunks to persistent storage
function ChunkSystem:saveAllChunks()
    Logger.log("Saving all chunks...")

    -- Count how many chunks we're saving
    local chunkCount = table.count(self.chunks)
    Logger.log("Saving " .. chunkCount .. " chunks")

    -- For now, just log that we would save the chunks
    -- In a real implementation, this would serialize and save each chunk

    -- Example implementation (commented out):
    --[[
    for chunkId, chunk in pairs(self.chunks) do
        -- Skip empty or temporary chunks
        if chunk.tiles and not chunk.temporary then
            -- Serialize chunk data
            local chunkData = {
                x = chunk.x,
                y = chunk.y,
                biome = chunk.biome,
                -- We don't save the full tile objects, just their types
                tileTypes = {}
            }

            -- Save tile types
            for x = 0, self.chunkSize - 1 do
                chunkData.tileTypes[x] = {}
                for y = 0, self.chunkSize - 1 do
                    if chunk.tiles[x] and chunk.tiles[x][y] then
                        chunkData.tileTypes[x][y] = chunk.tiles[x][y].type
                    end
                end
            end

            -- Save to file
            local filename = "world/chunk_" .. chunk.x .. "_" .. chunk.y .. ".json"
            -- Would use a proper JSON serializer here
        end
    end
    --]]

    Logger.log("Chunk saving complete")
    return true
end

-- Modify the drawTile function
function ChunkSystem:drawTile(tile, x, y)
    if not tile then return end

    -- Get the current season from the world
    local season = "summer"  -- Default season
    if self.world and self.world.season then
        season = self.world.season
    end

    -- Get the biome for this tile
    local biome = tile.biome or self.defaultBiome or "plains"

    -- Use the tile decoration system to draw the tile
    TileDecorations.drawTile(tile, x, y, biome, season)
end

Logger.log("DEBUG: ChunkSystem: About to return ChunkSystem table. Type of create:", type(ChunkSystem.create))
return ChunkSystem
