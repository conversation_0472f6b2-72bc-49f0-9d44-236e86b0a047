-- behaviors/Pack Hunting.lua
-- Pack hunting behavior for predators coordinating in groups

local PackHuntingBehavior = {}  -- first create the table

PackHuntingBehavior.id = "pack_hunting"
PackHuntingBehavior.name = "Pack Hunting Behavior"

-- Configuration parameters
PackHuntingBehavior.defaultConfig = {
    huntingRadius = 20,            -- Radius to detect prey
    groupFormationRadius = 10,     -- Radius for group coordination
    chaseSpeed = 3.5,              -- Speed during chase
    surroundPrey = true,           -- Whether the pack tries to surround prey
    surroundRadius = 5,            -- Radius to form a hunting circle
    attackThreshold = 0.6,         -- Proportion of pack in position required to attack
    retreatIfOutnumbered = true,   -- Retreat if pack is smaller than prey group
    maxChaseDistance = 30,         -- Maximum distance to chase prey
    regroupAfterAttack = true,     -- Whether to regroup after attack
    regroupRadius = 10,            -- Radius to regroup after hunting
    staminaDrainPerSecond = 0.3,   -- Stamina drain rate while hunting
    moveSpeed = 2.0               -- Speed when positioning
}

-- Initialize the behavior
function PackHuntingBehavior.init(entity, customConfig)
    -- Ensure the entity has a packHuntingState table
    entity.packHuntingState = entity.packHuntingState or {
        targetPrey = nil,
        huntingPack = {},
        positionReady = false,
        attackReady = false,
        config = {}
    }

    local config = entity.packHuntingState.config
    -- Copy default configuration values
    for k, v in pairs(PackHuntingBehavior.defaultConfig) do
        config[k] = v
    end

    -- Override with custom configuration if provided
    if customConfig then
        for k, v in pairs(customConfig) do
            config[k] = v
        end
    end
end

-- Update pack hunting behavior
function PackHuntingBehavior.update(entity, world, dt)
    local state = entity.packHuntingState
    local config = state.config

    -- Identify prey if not already set
    if not state.targetPrey then
        state.targetPrey = PackHuntingBehavior.findPrey(entity, world)
        if not state.targetPrey then 
            return 
        end
    end

    -- Form hunting pack
    state.huntingPack = PackHuntingBehavior.formHuntingPack(entity, world)

    -- Move to hunting positions
    PackHuntingBehavior.positionPack(entity, state.huntingPack, dt)

    -- Determine if ready to attack or retreat
    if PackHuntingBehavior.checkAttackReadiness(entity, state.huntingPack) then
        PackHuntingBehavior.attack(entity, state.targetPrey)
    elseif config.retreatIfOutnumbered and PackHuntingBehavior.checkIfOutnumbered(entity, state.huntingPack) then
        PackHuntingBehavior.retreat(entity, dt)
    end
end

-- Find prey within hunting radius
function PackHuntingBehavior.findPrey(entity, world)
    -- Implementation details...
end

-- Form hunting pack
function PackHuntingBehavior.formHuntingPack(entity, world)
    -- Implementation details...
end

-- Move pack members into positions
function PackHuntingBehavior.positionPack(entity, huntingPack, dt)
    -- Implementation details...
end

-- Check if pack is ready to attack
function PackHuntingBehavior.checkAttackReadiness(entity, huntingPack)
    -- Implementation details...
end

-- Attack prey
function PackHuntingBehavior.attack(entity, prey)
    -- Implementation details...
end

-- Check if the entity is outnumbered
function PackHuntingBehavior.checkIfOutnumbered(entity, huntingPack)
    -- Implementation details...
end

-- Retreat from the hunt
function PackHuntingBehavior.retreat(entity, dt)
    -- Implementation details...
end

return PackHuntingBehavior
