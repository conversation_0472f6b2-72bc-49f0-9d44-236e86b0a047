-- tiles/snow.lua
local SnowTile = {
    id = "snow",
    name = "Snow",
    passable = true, -- Passable, but potentially very difficult depending on depth
    variants = 6, -- e.g., Light Powder, Deep Drifts, Packed Snow, Melting Slush, Icy Crust, Firn

    -- Entities found in snowy areas
    spawns = {"snow_hare", "arctic_fox", "ptarmigan", "ice_wraith_minor", "frozen_shrub", "tracks_in_snow"}, -- Cold-weather creatures and signs

    -- Movement properties
    movementSpeed = 0.7, -- Default speed reduction, heavily modified by depth

    -- Snow-specific properties
    depth = 0.5, -- Average depth in tiles/units (can range from 0.1 to 2.0+)
    snowType = "powder", -- powder, packed, wet, crusty
    temperature = -5, -- Base temperature (Celsius)
    preservesTracks = true, -- Tracks are very visible in fresh snow

    -- Interaction function
    interact = function(tile, entity)
        -- Check snow depth
        local depthDesc = "The snow seems moderately deep here."
        if tile.depth < 0.3 then depthDesc = "A light covering of snow dusts the ground."
        elseif tile.depth > 1.2 then depthDesc = "The snow is very deep here, making passage difficult." end

        -- Look for tracks
        if tile.preservesTracks and entity.skills and entity.skills.tracking > 0 and math.random() < 0.4 then
             -- Placeholder: Check for actual tracks
             local tracks = {"fox_tracks_fresh", "deer_tracks_old", "unknown_large_paw_prints"}
             local foundTrack = tracks[math.random(#tracks)]
             return { success = true, message = depthDesc .. " You clearly see " .. foundTrack .. " in the snow.", effects={{type="gain_tracking_clue", clue=foundTrack}} }
        end

        -- Search/Dig in the snow
        if entity.canDig and math.random() < 0.15 then
             local buriedItems = {
                 {id="frozen_berry_bush", chance=0.4},
                 {id="hibernating_animal_den_small", chance=0.2}, -- Interaction point, not item
                 {id="lost_mitten", chance=0.25},
                 {id="ice_shard", chance=0.15}
             }
             for _, item in ipairs(buriedItems) do
                 if math.random() < item.chance then
                     if item.id == "hibernating_animal_den_small" then
                         return { success=true, message=depthDesc.." Digging reveals a small den entrance, likely containing a hibernating creature.", effects={{type="discover_feature", feature="hibernating_den"}} }
                     else
                         return { success = true, message = depthDesc .. " Digging through the snow, you find a " .. item.id .. ".", effects={{type="add_item", item=item.id, quantity=1}} }
                     end
                 end
             end
        end

        -- Gather snow (for water source?)
        if entity.hasItem and entity.hasItem("container_empty") then
             return { success=true, message=depthDesc.." You gather some clean snow, which could be melted for water.", effects={{type="add_item", item="packed_snow", quantity=1}} }
        end

        -- General examination
        return {
            success = false,
            message = depthDesc
        }
    end,

    -- Weather effects
    weatherEffects = {
        snow = function(tile) -- More snow!
            tile.depth = tile.depth + 0.2 -- Accumulates
            tile.snowType = "powder" -- Fresh snow is powder
            tile.preservesTracks = true -- Fresh snow shows tracks well
             -- Deeper snow further reduces speed
            tile.movementSpeed = math.max(0.2, 0.7 - (tile.depth * 0.3)) 
            return "visual_effect", "fresh_snowfall"
        end,
        
        heat = function(tile) -- Melting
            if world.temperature > 0 then
                 local meltRate = 0.3 * (world.temperature / 10) -- Melts faster at higher temps
                 tile.depth = math.max(0, tile.depth - meltRate)
                 tile.snowType = "wet" -- Becomes wet/slushy
                 tile.preservesTracks = false -- Melting obscures tracks
                 tile.movementSpeed = 0.5 -- Wet slush is hard to move through
                 if tile.depth == 0 then
                      -- Reveals underlying tile
                      return "transform", tile.underlyingTile or "dirt" 
                 end
                 return "visual_effect", "melting_snow_slush"
            end
            return nil
        end,

        sun = function(tile) -- Slower melting, potential for crust
            if world.temperature > 0 then
                 local meltRate = 0.15
                 tile.depth = math.max(0, tile.depth - meltRate)
                 tile.snowType = "wet"
                 tile.preservesTracks = false
                 tile.movementSpeed = 0.5
                 if tile.depth == 0 then
                      return "transform", tile.underlyingTile or "grass" 
                 end
                 return "visual_effect", "melting_snow_surface"
            -- Sun after freeze might create crust? Requires tracking prev state.
            -- elseif tile.wasFrozen then tile.snowType = "crusty" end 
            end
            return nil
        end,

        freeze = function(tile) -- Refreezing, potential for crust
             if tile.snowType == "wet" and world.temperature < -2 then
                 tile.snowType = "crusty"
                 tile.movementSpeed = 0.6 -- Crust might be slightly easier? Or breaks through?
                 tile.footstepSound = "snow_crunch_crusty"
                 return "visual_effect", "frozen_snow_crust"
             elseif world.temperature < -10 then -- Deep freeze makes powder squeak?
                 if tile.snowType == "powder" then tile.footstepSound = "snow_squeak_cold" end
             end
             return nil
        end,

        wind = function(tile) -- Wind creates drifts, reduces visibility
            if world.windStrength > 0.6 then
                 -- Redistribute snow depth? Create drift variants?
                 if math.random() < 0.2 then tile.depth = tile.depth + 0.5; tile.variant_name="Deep Drifts" end -- Crude drift simulation
                 if world.windStrength > 0.8 then
                      -- Blizzard conditions
                      return "trigger_event", "blizzard"
                 end
                 return "visual_effect", "blowing_snow"
            end
            return nil
        end
    },

     -- Time effects
    timeEffects = {
        night = function(tile)
            -- Colder, refreezing more likely
            tile.temperature = (world.temperature or -5) - 3 
            tile.ambientSound = "wind_howl_snowy"
            return nil
        end,
        
        day = function(tile)
             tile.temperature = world.temperature or -5
             tile.ambientSound = "wind_soft_snowy"
             -- Melting more likely if temp > 0
            return nil
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {0.95, 0.95, 1.0}, -- White
        variants = {
            {name = "Light Powder", depth=0.2, texture="snow_powder_light"},
            {name = "Deep Drifts", depth=1.5, shape="drift", texture="snow_deep"},
            {name = "Packed Snow", depth=0.6, texture="snow_packed", snowType="packed"},
            {name = "Melting Slush", depth=0.4, texture="snow_slush", snowType="wet", movementSpeed=0.5},
            {name = "Icy Crust", depth=0.5, texture="snow_crusty", snowType="crusty", shininess=0.4},
            {name = "Firn", depth=1.0, texture="snow_firn", snowType="packed", color_shift={-0.1, -0.1, 0}} -- Old, compacted snow
        },
        decoration_objects = {
            {name = "snow_drift_small", chance = 0.3},
            {name = "animal_tracks_snow_decal", chance = 0.4, condition = function(tile) return tile.preservesTracks end},
            {name = "icicle_on_branch", chance = 0.1, condition = function(tile) return tile.temperature < 0 end},
            {name = "buried_object_shape", chance = 0.1} -- Hint of something under snow
        },
         special_effects = {
             sparkle = {intensity=0.5, when="sunny"},
             blowing_snow_particles = {type="particle", density=0.4, direction="wind", when="windy"}
         },
         hides_underlying_tile = true, -- Snow covers the tile below visually
         depth_visual_modifier = true -- Appearance changes with depth property
    },
    
    -- Audio properties
    ambientSound = "wind_soft_snowy",
    footstepSound = "snow_crunch_soft", -- Default, changes with type/temp
     specialSounds = {
        {name = "snow_slide_soft", trigger = "movement_on_slope", chance = 0.05},
        {name = "wind_howl_strong", trigger = "windy", chance = 0.4},
        {name = "distant_wolf_howl", trigger = "night", chance = 0.03}
    },
    -- Audio Modifiers
     audioModifiers = {
         muffling = function(tile) return 0.4 + tile.depth * 0.2 end -- Snow muffles sounds, more so when deep
     },

    -- Connections to other tiles
    connects_to = {
        "snow", -- Itself
        "ice", -- Frozen water bodies
        "dirt", -- Edge of snow cover
        "grass", -- Edge of snow cover
        "stone", -- Edge of snow cover
        "rocky_ground", -- Edge of snow cover
        "mountain_base", -- Common location for snow
        "mountain_path", -- Often covered in snow
        "forest_snowy" -- Leads into snowy forests
    },

    -- Pathfinding properties
     pathfinding = {
        travel_cost = function(tile) return 1.4 + tile.depth * 0.8 end, -- Cost increases significantly with depth
        reduces_stealth = false, -- Snow muffles sound, might aid stealth
        hides_paths = true -- Can obscure trails beneath
    },

    -- Effects on entities that walk on this tile
    onWalk = function(tile, entity)
        local sound = tile.footstepSound or "snow_crunch_soft"
        if tile.snowType == "crusty" then sound = "snow_crunch_crusty"
        elseif tile.temperature < -10 and tile.snowType == "powder" then sound = "snow_squeak_cold"
        elseif tile.snowType == "wet" then sound = "slush_step" end

        local fatigue = 0.15 * (1 + tile.depth * 1.5) -- High fatigue cost in deep snow
        local effects = {{type="increase_fatigue", amount = fatigue}}
        
        -- Apply cold effect
        if tile.temperature < -5 then
             table.insert(effects, {type="apply_status", status="cold", duration=30, magnitude=math.abs(tile.temperature/10)})
        end
        
        -- Leave tracks
        if tile.preservesTracks then
             table.insert(effects, {type="leave_tracks", track_type=entity.trackType or "humanoid", duration=100}) -- Tracks last a while
        end
        
        -- Movement speed calculation should happen here or be influenced by pathfinding cost
        -- entity.currentSpeed = entity.baseSpeed * (tile.movementSpeed or 0.7) * (1 - (tile.depth * 0.3)) -- Example calculation

        return {
            sound = sound,
            volume = 0.8 * (1 - (tile.depth * 0.2)), -- Sound volume might decrease slightly in deep snow due to muffling? Or use modifier.
            effects = effects
        }
    end,
    
    -- Effect when discovering snow
    onDiscover = function(tile, entity)
        if entity.journal and entity.journal.addDiscovery then
             entity.journal.addDiscovery({
                type = "terrain",
                name = "Snow Cover",
                location = {x = tile.x, y = tile.y},
                notes = "Area covered in snow, depth approx " .. string.format("%.1f", tile.depth) .. "."
            })
        end
        return {
            message = "You enter an area covered in snow.",
            effects = {
                {type = "reveal_map", radius = 2}
            }
        }
    end
}

function SnowTile.init(world)
    print("Snow tile module initialized")
    -- Register with weather/environment systems
     if world.systems and world.systems.weather then
        world.systems.weather:registerSnowCover("snow", {baseDepth=0.5})
    end
     if world.systems and world.systems.environment then
        world.systems.environment:registerTrackableSurface("snow")
    end
end

return SnowTile