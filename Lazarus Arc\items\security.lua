-- items/security.lua
-- Security-related items and data storage devices

local SecurityItems = {
    -- Access Cards & Keys
    security_keycard_low = {
        id = "security_keycard_low",
        name = "Low Level Security Keycard",
        description = "A basic security keycard with limited access privileges.",
        type = "key",
        rarity = 2,
        value = 25,
        weight = 0.5,
        stackable = false,
        tags = {"security", "keycard", "low_level"}
    },
    
    access_keycard_high = {
        id = "access_keycard_high",
        name = "High Level Access Keycard",
        description = "A high-security keycard with extensive access privileges.",
        type = "key",
        rarity = 3,
        value = 50,
        weight = 0.5,
        stackable = false,
        tags = {"security", "keycard", "high_level"}
    },
    
    security_access_key_tower = {
        id = "security_access_key_tower",
        name = "Tower Security Access Key",
        description = "A specialized key for accessing control tower systems.",
        type = "key",
        rarity = 3,
        value = 45,
        weight = 0.5,
        stackable = false,
        tags = {"security", "key", "tower"}
    },

    -- Security Override Devices
    security_override_code = {
        id = "security_override_code",
        name = "Security Override Code",
        description = "A temporary code for overriding security systems.",
        type = "code",
        rarity = 3,
        value = 40,
        weight = 0.1,
        stackable = false,
        temporary = true,
        single_use = true,
        tags = {"security", "override", "temporary"}
    },
    
    area_control_override_code = {
        id = "area_control_override_code",
        name = "Area Control Override Code",
        description = "A powerful code for overriding area-wide control systems.",
        type = "code",
        rarity = 4,
        value = 60,
        weight = 0.1,
        stackable = false,
        temporary = true,
        single_use = true,
        tags = {"security", "override", "area_control"}
    },

    -- Data Storage & Logs
    facility_data_log = {
        id = "facility_data_log",
        name = "Facility Data Log",
        description = "A data storage device containing facility information.",
        type = "data",
        rarity = 2,
        value = 30,
        weight = 1,
        stackable = true,
        maxStack = 10,
        tags = {"data", "facility", "log"}
    },
    
    surveillance_data_chip = {
        id = "surveillance_data_chip",
        name = "Surveillance Data Chip",
        description = "A chip containing surveillance footage and sensor data.",
        type = "data",
        rarity = 2,
        value = 25,
        weight = 0.5,
        stackable = true,
        maxStack = 15,
        tags = {"data", "surveillance"}
    },
    
    communication_log = {
        id = "communication_log",
        name = "Communication Log",
        description = "A record of communications and transmissions.",
        type = "data",
        rarity = 2,
        value = 20,
        weight = 0.5,
        stackable = true,
        maxStack = 15,
        tags = {"data", "communication"}
    },
    
    surveillance_log_fragment = {
        id = "surveillance_log_fragment",
        name = "Surveillance Log Fragment",
        description = "A partial record of surveillance data.",
        type = "data",
        rarity = 2,
        value = 15,
        weight = 0.5,
        stackable = true,
        maxStack = 20,
        tags = {"data", "surveillance", "fragment"}
    },
    
    data_log_fragment = {
        id = "data_log_fragment",
        name = "Data Log Fragment",
        description = "A fragment of corrupted or partial data.",
        type = "data",
        rarity = 1,
        value = 10,
        weight = 0.5,
        stackable = true,
        maxStack = 20,
        tags = {"data", "fragment"}
    },

    -- Security Documentation
    security_protocol_manual = {
        id = "security_protocol_manual",
        name = "Security Protocol Manual",
        description = "A manual containing security procedures and protocols.",
        type = "document",
        rarity = 2,
        value = 35,
        weight = 2,
        stackable = false,
        tags = {"security", "document", "manual"}
    },
    
    facility_schematic = {
        id = "facility_schematic",
        name = "Facility Schematic",
        description = "Detailed schematics of a facility's layout and systems.",
        type = "document",
        rarity = 3,
        value = 45,
        weight = 2,
        stackable = false,
        tags = {"security", "document", "schematic"}
    },

    -- Contraband
    contraband_confiscated = {
        id = "contraband_confiscated",
        name = "Confiscated Contraband",
        description = "An item confiscated by security personnel.",
        type = "contraband",
        rarity = 2,
        value = 30,
        weight = 1,
        stackable = true,
        maxStack = 10,
        tags = {"contraband", "confiscated"}
    }
}

return SecurityItems 