-- ability_system.lua
-- Manages active skill and overdrive instances
-- Handles ability activation, cooldowns, and effects

local UUID = require("utils.uuid")
local AbilitySystem = {}
AbilitySystem.__index = AbilitySystem

-- Create a new AbilitySystem instance
function AbilitySystem.new()
    local self = setmetatable({}, AbilitySystem)
    self.activeAbilities = {}   -- Stores active abilities by their UUID
    self.updateQueue = {}       -- Queue for parallel ability updates
    self.batchSize = 10         -- Number of abilities to update in parallel
    return self
end

-- Activate a skill or overdrive
-- Returns the ability instance's UUID
function AbilitySystem:activateAbility(abilityType, caster, target, options)
    if not abilityType then
        error("Cannot activate ability without type")
    end
    
    -- Load ability template
    local template = require("abilities." .. abilityType)
    if not template then
        error("Invalid ability type: " .. abilityType)
    end
    
    -- Create ability instance
    local ability = {
        uuid = UUID.generate(),
        type = abilityType,
        caster = caster,
        target = target,
        options = options or {},
        created = os.time(),
        lastUpdate = os.time(),
        duration = template.duration or 0,
        cooldown = template.cooldown or 0,
        isActive = true
    }
    
    -- Initialize ability using template
    if template.onActivate then
        template.onActivate(ability, caster, target, options)
    end
    
    -- Store ability
    self.activeAbilities[ability.uuid] = ability
    
    return ability.uuid
end

-- Get an ability instance by its UUID
function AbilitySystem:getAbility(uuid)
    return self.activeAbilities[uuid]
end

-- Remove an ability
function AbilitySystem:removeAbility(uuid)
    if self.activeAbilities[uuid] then
        local ability = self.activeAbilities[uuid]
        -- Call cleanup if available
        local template = require("abilities." .. ability.type)
        if template and template.onDeactivate then
            template.onDeactivate(ability)
        end
        self.activeAbilities[uuid] = nil
        return true
    end
    return false
end

-- Update a single ability
function AbilitySystem:updateAbility(ability, dt)
    local template = require("abilities." .. ability.type)
    if template and template.update then
        template.update(ability, dt)
    end
    
    -- Check duration
    if ability.duration > 0 then
        ability.duration = ability.duration - dt
        if ability.duration <= 0 then
            self:removeAbility(ability.uuid)
        end
    end
    
    ability.lastUpdate = os.time()
end

-- Update abilities in parallel batches
function AbilitySystem:update(dt)
    -- Queue all abilities for update
    for _, ability in pairs(self.activeAbilities) do
        table.insert(self.updateQueue, ability)
    end
    
    -- Process abilities in batches
    while #self.updateQueue > 0 do
        local batch = {}
        for i = 1, math.min(self.batchSize, #self.updateQueue) do
            table.insert(batch, table.remove(self.updateQueue))
        end
        
        -- Process batch in parallel
        local threads = {}
        for _, ability in ipairs(batch) do
            local thread = coroutine.create(function()
                self:updateAbility(ability, dt)
            end)
            table.insert(threads, thread)
            coroutine.resume(thread)
        end
        
        -- Wait for all threads in batch to complete
        for _, thread in ipairs(threads) do
            while coroutine.status(thread) ~= "dead" do
                coroutine.resume(thread)
            end
        end
    end
end

-- Serialize ability data for saving (async)
function AbilitySystem:serialize()
    local data = {}
    local threads = {}
    
    -- Create threads for parallel serialization
    for uuid, ability in pairs(self.activeAbilities) do
        local thread = coroutine.create(function()
            data[uuid] = {
                type = ability.type,
                caster = ability.caster.uuid,
                target = ability.target and ability.target.uuid,
                options = ability.options,
                created = ability.created,
                lastUpdate = ability.lastUpdate,
                duration = ability.duration,
                cooldown = ability.cooldown,
                isActive = ability.isActive
            }
        end)
        table.insert(threads, thread)
        coroutine.resume(thread)
    end
    
    -- Wait for all serialization threads to complete
    for _, thread in ipairs(threads) do
        while coroutine.status(thread) ~= "dead" do
            coroutine.resume(thread)
        end
    end
    
    return data
end

-- Deserialize ability data when loading (async)
function AbilitySystem:deserialize(data, entitySystem)
    local threads = {}
    
    -- Create threads for parallel deserialization
    for uuid, abilityData in pairs(data) do
        local thread = coroutine.create(function()
            -- Get caster and target entities
            local caster = entitySystem:getEntityByUUID(abilityData.caster)
            local target = abilityData.target and entitySystem:getEntityByUUID(abilityData.target)
            
            if caster then
                local ability = {
                    uuid = uuid,
                    type = abilityData.type,
                    caster = caster,
                    target = target,
                    options = abilityData.options,
                    created = abilityData.created,
                    lastUpdate = abilityData.lastUpdate,
                    duration = abilityData.duration,
                    cooldown = abilityData.cooldown,
                    isActive = abilityData.isActive
                }
                
                -- Reinitialize ability using template
                local template = require("abilities." .. ability.type)
                if template and template.onActivate then
                    template.onActivate(ability, caster, target, ability.options)
                end
                
                self.activeAbilities[uuid] = ability
            end
        end)
        table.insert(threads, thread)
        coroutine.resume(thread)
    end
    
    -- Wait for all deserialization threads to complete
    for _, thread in ipairs(threads) do
        while coroutine.status(thread) ~= "dead" do
            coroutine.resume(thread)
        end
    end
end

-- Query abilities using a filter function (async)
function AbilitySystem:query(filterFn)
    local result = {}
    local threads = {}
    
    -- Create threads for parallel filtering
    for _, ability in pairs(self.activeAbilities) do
        local thread = coroutine.create(function()
            if filterFn(ability) then
                table.insert(result, ability)
            end
        end)
        table.insert(threads, thread)
        coroutine.resume(thread)
    end
    
    -- Wait for all filter threads to complete
    for _, thread in ipairs(threads) do
        while coroutine.status(thread) ~= "dead" do
            coroutine.resume(thread)
        end
    end
    
    return result
end

-- Get abilities by type (async)
function AbilitySystem:getAbilitiesByType(typeName)
    return self:query(function(ability)
        return ability.type == typeName
    end)
end

-- Get abilities for a specific caster (async)
function AbilitySystem:getAbilitiesForCaster(caster)
    return self:query(function(ability)
        return ability.caster == caster
    end)
end

-- Get abilities affecting a specific target (async)
function AbilitySystem:getAbilitiesAffectingTarget(target)
    return self:query(function(ability)
        return ability.target == target
    end)
end

return AbilitySystem 