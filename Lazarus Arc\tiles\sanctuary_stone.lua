-- tiles/sanctuary_stone.lua
local SanctuaryStone = {
    id = "sanctuary_stone",
    name = "Sanctuary Stone",
    passable = true,
    variants = 1, -- Only one special variant of the sanctuary stone
    
    -- Entities that can manifest near sanctuary stone
    spawns = {"memory_guardian", "essence_light", "dimensional_anchor"},
    
    -- Hub-specific properties
    sanctuaryLevel = 1, -- Affected by hub level
    memoryPower = 1.0, -- Core power of sanctuary memories
    dimensionalStability = 0.8, -- How stable the sanctuary is
    coreResonance = 1.0, -- Always at maximum resonance
    
    -- Movement properties
    movementSpeed = 1.0, -- Standard movement on the sacred stone
    
    -- Interaction function
    interact = function(tile, entity)
        -- The sanctuary stone is the heart of the sanctuary
        -- and responds strongly to the player
        
        -- Always provide a core sanctuary effect
        local healAmount = 10 * tile.sanctuaryLevel
        local staminaAmount = 20 * tile.sanctuaryLevel
        
        -- Apply healing and restoration
        if entity.stats then
            if entity.stats.health then
                entity.stats.health = math.min(entity.stats.health + healAmount, entity.stats.maxHealth)
            end
            
            if entity.stats.stamina then
                entity.stats.stamina = math.min(entity.stats.stamina + staminaAmount, entity.stats.maxStamina)
            end
            
            -- Mana restoration at higher levels
            if tile.sanctuaryLevel >= 3 and entity.stats.mana then
                local manaAmount = 15 * (tile.sanctuaryLevel - 2)
                entity.stats.mana = math.min(entity.stats.mana + manaAmount, entity.stats.maxMana)
            end
        end
        
        -- Memory restoration and hub level interactions
        if tile.sanctuaryLevel >= 2 then
            local memoryMessage
            if tile.sanctuaryLevel == 2 then
                memoryMessage = "The stone pulses, helping you recall fragments of your past."
            elseif tile.sanctuaryLevel == 3 then
                memoryMessage = "Memories flow through the stone, becoming clearer with each visit."
            elseif tile.sanctuaryLevel == 4 then
                memoryMessage = "The sanctuary stone reveals connections between your memories and other realms."
            else -- Level 5
                memoryMessage = "The fully awakened sanctuary stone illuminates the tapestry of your existence."
            end
            
            return {
                success = true,
                message = memoryMessage,
                effects = {
                    {type = "restore_health", amount = healAmount},
                    {type = "restore_stamina", amount = staminaAmount},
                    {type = "restore_mana", amount = tile.sanctuaryLevel >= 3 and (15 * (tile.sanctuaryLevel - 2)) or 0},
                    {type = "memory_restoration", intensity = tile.sanctuaryLevel * 0.2}
                }
            }
        end
        
        -- Basic level 1 response
        return {
            success = true,
            message = "The sanctuary stone responds to your touch, restoring your vitality.",
            effects = {
                {type = "restore_health", amount = healAmount},
                {type = "restore_stamina", amount = staminaAmount}
            }
        }
    end,
    
    -- Time effects always active
    timeEffects = {
        night = function(tile)
            -- Stone glows brightest at night
            tile.glowIntensity = (tile.baseGlowIntensity or 1.0) * 1.5
            tile.pulsePower = (tile.basePulsePower or 0.5) * 2
            return "visual_effect", "night_resonance"
        end,
        
        dawn = function(tile)
            -- Reset glow to normal in day but still bright
            tile.glowIntensity = tile.baseGlowIntensity or 1.0
            tile.pulsePower = tile.basePulsePower or 0.5
            return nil
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {1.0, 1.0, 1.0}, -- Pure white/light
        variants = {
            {name = "sanctuary_core", unique = true}
        },
        glowing = true,
        glow_color = {1.0, 1.0, 1.0},
        glow_intensity = 1.0,
        animation = {
            type = "pulse",
            intensity = 0.5,
            speed = 1.0,
            synchronizeWithBeat = true -- Pulses with a heartbeat-like rhythm
        },
        light_source = {
            radius = 10,
            intensity = 1.0,
            color = {1.0, 1.0, 1.0}
        },
        special_effects = {
            floating_particles = {
                count = 20,
                color = {1.0, 1.0, 1.0},
                rise_rate = 0.5
            },
            energy_tendrils = {
                count = 8,
                length = 3,
                color = {0.9, 0.9, 1.0}
            },
            memory_echoes = {
                chance = 0.2,
                duration = 3
            }
        }
    },
    
    -- Audio properties
    ambientSound = "sanctuary_core_hum",
    specialSounds = {
        {name = "stone_pulse", trigger = "interaction", chance = 1.0},
        {name = "memory_whisper", trigger = "night", chance = 0.2},
        {name = "dimensional_shift", trigger = "level_change", chance = 1.0}
    },
    
    -- Sanctuary properties
    sanctuary = {
        coreNode = true, -- This is the central node of the sanctuary
        protectionLevel = 1.0, -- Maximum protection
        healingAura = true, -- Constantly emits healing
        staminaRegen = true, -- Constantly regenerates stamina
        memoryNexus = true, -- Central point for memory connections
        dimensionalAnchor = true -- Stabilizes the sanctuary's existence
    },
    
    -- Hub level effects - central to hub upgrading
    applyHubLevel = function(tile, level)
        tile.sanctuaryLevel = level
        tile.memoryPower = 0.5 + (level * 0.5)
        
        -- Visual upgrades with levels
        tile.visual.glow_intensity = 1.0 + (0.2 * level)
        tile.baseGlowIntensity = tile.visual.glow_intensity
        
        tile.visual.animation.intensity = 0.5 + (0.1 * level)
        tile.basePulsePower = tile.visual.animation.intensity
        
        -- Particle effects increase with level
        tile.visual.special_effects.floating_particles.count = 10 + (10 * level)
        
        -- Sanctuary core functionality improves with level
        tile.sanctuary.healingRadius = 3 + level
        tile.sanctuary.protectionRadius = 5 + (2 * level)
        
        -- Special abilities at higher levels
        if level >= 3 then
            tile.sanctuary.manaFount = true -- Can restore mana
        end
        
        if level >= 4 then
            tile.sanctuary.dimensionalGateway = true -- Can connect to other dimensions
        end
        
        if level >= 5 then
            tile.sanctuary.timeResonance = true -- Allows limited temporal effects
            tile.sanctuary.memoryArchive = true -- Permanently stores important memories
        end
    end,
    
    -- Connections to other tiles
    connects_to = {
        "hub_path",
        "hub_floor",
        "hub_grass"
    },
    
    -- Effect when standing on the sanctuary stone
    onStand = function(tile, entity)
        -- Deep connection with the sanctuary core
        if entity.applyStatus then
            -- Core sanctuary protection
            entity.applyStatus("sanctuary_blessing", 60, tile.sanctuaryLevel)
            
            -- Continuous healing
            if entity.stats and entity.stats.health and entity.stats.health < entity.stats.maxHealth then
                entity.stats.health = math.min(entity.stats.health + (0.5 * tile.sanctuaryLevel), entity.stats.maxHealth)
            end
            
            -- Continuous stamina regeneration
            if entity.stats and entity.stats.stamina and entity.stats.stamina < entity.stats.maxStamina then
                entity.stats.stamina = math.min(entity.stats.stamina + (1.0 * tile.sanctuaryLevel), entity.stats.maxStamina)
            end
            
            -- Mana fountain at higher levels
            if tile.sanctuary.manaFount and entity.stats and entity.stats.mana and entity.stats.mana < entity.stats.maxMana then
                entity.stats.mana = math.min(entity.stats.mana + (0.8 * (tile.sanctuaryLevel - 2)), entity.stats.maxMana)
            end
        end
        
        -- Memory connection
        if entity.connectToMemories and math.random() < 0.2 * tile.sanctuaryLevel then
            entity.connectToMemories(tile.memoryPower)
        end
        
        -- Visual effect
        return {
            visual = "sanctuary_embrace",
            intensity = tile.sanctuaryLevel,
            duration = 3
        }
    end,
    
    -- Special function for hub upgrading
    upgradeSanctuary = function(tile, entity, newLevel)
        if not entity or not entity.isPlayer then
            return {
                success = false,
                message = "Only the sanctuary's owner can upgrade it."
            }
        end
        
        -- Check if requirements are met for upgrading
        local requiredMemories = (newLevel - 1) * 5
        if entity.memories and #entity.memories < requiredMemories then
            return {
                success = false,
                message = "You need to recover more memories before upgrading the sanctuary."
            }
        end
        
        -- Upgrade successful
        local oldLevel = tile.sanctuaryLevel
        tile.sanctuaryLevel = newLevel
        tile.applyHubLevel(tile, newLevel)
        
        -- Trigger sanctuary-wide effect
        return {
            success = true,
            message = "The sanctuary stone pulses with power, and the entire sanctuary begins to transform.",
            effects = {
                {type = "sanctuary_upgrade", from = oldLevel, to = newLevel},
                {type = "visual_effect", effect = "sanctuary_evolution", intensity = newLevel},
                {type = "play_sound", sound = "dimensional_shift", volume = 1.0}
            }
        }
    end
}

function SanctuaryStone.init(world)
    print("Sanctuary stone tile module initialized")
    
    -- Register with sanctuary system
    if world.systems and world.systems.sanctuary then
        world.systems.sanctuary:registerSanctuaryElement("sanctuary_stone", "core")
    end
    
    -- Register as dimensional anchor point
    if world.systems and world.systems.dimensions then
        world.systems.dimensions:registerAnchorPoint("sanctuary_stone")
    end
    
    -- Register with memory system
    if world.systems and world.systems.memory then
        world.systems.memory:registerMemoryNexus("sanctuary_stone")
    end
end

return SanctuaryStone