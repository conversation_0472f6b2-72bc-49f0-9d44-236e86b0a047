local Minnow = {
    id = "minnow",
    name = "Minnow",
    type = "minnow",

    -- Entity categories
    categories = { "animal", "fish", "small", "prey", "bait" }, -- Can be used as bait

    -- Threat categories
    threatCategories = { "predator", "medium", "large" }, -- Many fish prey on minnows
    foodCategories = { "insect", "algae" },

    -- Stats
    maxHealth = 1, -- Very fragile
    health = 1,
    speed = 3.0, -- Quick to escape

    -- Behaviors
    behaviors = { "schooling", "flee", "Foraging" },

    -- Behavior configurations
    behaviorConfigs = {
        schooling = {
            neighborRadius = 2, -- Tight schools
            separationDistance = 0.3,
        },
        flee = {
            useCategories = true,
            moveSpeed = 5.0, -- Very fast escape
            detectionRadius = 3
        },
        觅食 = {
            foodTypes = { "insect", "algae" },
            foodRadius = 1
        }
    },

    -- Appearance
    appearance = {
        sprite = "minnow", -- Replace with your minnow sprite
        scale = 0.5, -- Small size
        animations = {
            "swim",
            "flee"
        }
    },

    -- Sound effects
    sounds = {
        -- Might not have distinct sounds
    }
}

-- Initialize the minnow entity
function Minnow.init(entity, world)
    entity.position = entity.position or { x = 0, y = 0 }
    entity.velocity = entity.velocity or { x = 0, y = 0 }

    -- Initialize behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.init then
            behavior.init(entity, entity.behaviorConfigs[behaviorName])
        end
    end

    return entity
end

return Minnow