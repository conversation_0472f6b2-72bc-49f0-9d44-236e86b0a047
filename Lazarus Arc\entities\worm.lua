local Worm = {
    id = "worm",
    name = "Worm",
    type = "worm",

    -- Entity categories
    categories = {"invertebrate", "prey"},

    -- No threat categories, only food
    foodCategories = {}, -- Worms don't eat in this simulation

    -- Stats
    maxHealth = 5,
    health = 5,
    speed = 0.2,

    -- Behaviors
    behaviors = {"wander", "burrow"},

    -- Behavior configurations
    behaviorConfigs = {
        wander = {
            moveSpeed = 0.1,
            changeDirectionChance = 0.1,
            idleChance = 0.5,
            idleDuration = {2, 5},
            wanderRadius = 3
        },
        burrow = {
            canBurrow = true,
            burrowSpeed = 0.4,
            burrowTime = {3, 8}
        }
    },

    -- Appearance
    appearance = {
        sprite = "worm",
        scale = 0.5,
        animations = {
            "idle", "wiggle", "burrow"
        }
    },

    -- No sounds
    sounds = {},

    -- No drops
    drops = {}
}

-- Initialize the worm entity
function Worm.init(entity, world)
    entity.x = entity.x or 0
    entity.y = entity.y or 0
    entity.rotation = entity.rotation or 0
    entity.width = 1
    entity.height = 1
    entity.type = "worm"
    
    -- Apply behavior
    local behaviorName = "worm_behavior"
    -- Check if world is not nil before trying to access it
    if world and world.modules and world.modules.behaviors then
        local behavior = world.modules.behaviors[behaviorName]
        if behavior then
            behavior.apply(entity)
        end
    end
    
    return entity
end

-- Update the worm entity
function Worm.update(entity, world, dt)
    -- Handle burrowing behavior
    if entity.abilities.burrow.canBurrow then
        if entity.isBurrowed then
            entity.burrowTimer = entity.burrowTimer - dt
            if entity.burrowTimer <= 0 then
                entity.isBurrowed = false
            end
        else
            -- Randomly burrow
            if math.random() < 0.05 then
                entity.isBurrowed = true
                entity.burrowTimer = math.random(entity.abilities.burrow.burrowTime[1], entity.abilities.burrow.burrowTime[2])
            end
        end
    end
end

return Worm