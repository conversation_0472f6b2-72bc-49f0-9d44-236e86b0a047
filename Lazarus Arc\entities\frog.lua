local Frog = {
    id = "frog",
    name = "Frog",
    type = "frog",
    shape = {
        {0, -1}, {1, 0}, {0, 1}, {-1, 0}
    },
    size = 4,

    -- Entity categories
    categories = { "animal", "amphibian", "prey" },

    -- Threat and food categories
    threatCategories = { "predator", "large" },
    foodCategories = { "insect", "small" },

    -- Stats
    maxHealth = 10,
    health = 10,
    speed = 2.0,  -- Moderate speed
    jumpPower = 5.0, -- Can jump a good distance

    -- Behaviors
    behaviors = { "wander", "hunt", "flee" },

    -- Behavior configurations
    behaviorConfigs = {
        wander = {
            moveSpeed = 1.5,
            changeDirectionChance = 0.1,
            idleChance = 0.4, -- Frogs tend to sit still a lot
            idleDuration = { 3, 10 },
            wanderRadius = 10
        },
        hunt = {
            huntRadius = 3, -- Short range, ambush predator
            chaseRadius = 5, -- Short chase
            attackRadius = 1, -- Tongue attack range
            preferredTargets = { "fly", "worm", "snail" } -- Example prey
        },
        flee = {
            useCategories = true,
            moveSpeed = 4.0, -- Quick escape
            detectionRadius = 6,
            useHiding = true,
            hideLocations = { "water", "tall_grass" } -- Prefers to hide in water or tall grass
        }
    },

    -- Appearance
    appearance = {
        sprite = "frog", -- Replace with your frog sprite
        scale = 0.8,
        animations = {
            "idle",
            "hop", -- Use hop instead of walk
            "jump", -- For longer jumps
            "eat",
            "hide"
        },
        -- Color variations
        variants = {
            "green",
            "brown",
            "red"
        }
    },

    -- Sound effects with synth configuration
    sounds = {
        croak = {
            file = "frog_croak",
            synth = {
                instrument = "bass_guitar",
                notes = {"C3", "G2"},
                durations = {0.3, 0.4},
                volume = 0.4,
                vibrato = true,
                vibratoRate = 4.0
            }
        },
        ribbit = {
            file = "frog_ribbit",
            synth = {
                instrument = "bass_guitar",
                notes = {"G2", "C3", "G2"},
                durations = {0.2, 0.15, 0.25},
                volume = 0.35,
                vibrato = true,
                vibratoRate = 5.0
            }
        },
        splash = {
            synth = {
                instrument = "vibraphone",
                notes = {"F4", "D4"},
                durations = {0.1, 0.15},
                volume = 0.3
            }
        },
        hop = {
            synth = {
                instrument = "kalimba",
                notes = {"G3"},
                duration = 0.1,
                volume = 0.2
            }
        },
        swim = {
            synth = {
                instrument = "vibraphone",
                notes = {"C4", "E4"},
                durations = {0.2, 0.2},
                volume = 0.25
            }
        },
        hurt = {
            synth = {
                instrument = "electric_guitar",
                notes = {"D#3"},
                duration = 0.25,
                volume = 0.4,
                vibrato = true,
                vibratoRate = 8.0
            }
        },
        death = {
            synth = {
                instrument = "cello",
                notes = {"G2", "E2", "C2"},
                durations = {0.4, 0.4, 0.8},
                volume = 0.4,
                vibrato = true,
                vibratoRate = 2.0
            }
        }
    }
}

-- Initialize the frog entity
function Frog.init(entity, world)
    entity.x = entity.x or 0
    entity.y = entity.y or 0
    entity.rotation = entity.rotation or 0
    entity.width = 1
    entity.height = 1
    entity.type = "frog"
    -- Apply behavior
    local behaviorName = "hopping_behavior"
    -- Check if world is not nil before trying to access it
    if world and world.modules and world.modules.behaviors then
        local behavior = world.modules.behaviors[behaviorName]
        if behavior then
            behavior.apply(entity)
        end
    end
    return entity
end

return Frog