-- biomes/plains.lua
local PlainsBiome = {
    id = "plains",
    name = "Plains",
    
    -- Core tile types used in this biome
    primaryTiles = {"grass", "dirt", "flower"},
    secondaryTiles = {"bush", "tall_grass", "rock"},
    rareTiles = {"ancient_ruins", "magic_spring", "crystal_formation"},
    
    -- Default proportions (adjustable through variants)
    tileDistribution = {
        grass = 0.45,
        dirt = 0.25,
        flower = 0.15,
        bush = 0.08,
        tall_grass = 0.05,
        rock = 0.01,
        ancient_ruins = 0.005,
        magic_spring = 0.003,
        crystal_formation = 0.002
    },
    
    -- Entities common to this biome
    commonEntities = {
        "rabbit", "bird", "deer", "fox", "sheep"
    },
    
    uncommonEntities = {
        "wolf", "horse", "cow", "plains_spirit", "wind_elemental"
    },
    
    rareEntities = {
        "unicorn", "griffin", "plains_dragon", "celestial_being"
    },
    
    -- Biome variants for diverse generation
    variants = {
        temperate = {
            name = "Temperate Plains",
            elevationMultiplier = 1.0,
            temperatureOffset = 0.0,
            tileAdjustments = {
                grass = 0.5,
                flower = 0.2,
                bush = 0.1
            },
            entityAdjustments = {
                deer = 1.5,
                bird = 1.3,
                sheep = 1.2
            },
            specialEffects = {
                "gentle_breeze",
                "swaying_grass",
                "floating_clouds"
            }
        },
        wildflower = {
            name = "Wildflower Plains",
            elevationMultiplier = 1.0,
            temperatureOffset = 2.0,
            tileAdjustments = {
                grass = 0.4,
                flower = 0.3,
                tall_grass = 0.1
            },
            entityAdjustments = {
                butterfly = 2.0,
                bee = 1.5,
                bird = 1.3
            },
            specialEffects = {
                "flower_petals",
                "buzzing_insects",
                "colorful_aurora"
            }
        },
        magical = {
            name = "Magical Plains",
            elevationMultiplier = 1.0,
            temperatureOffset = 0.0,
            tileAdjustments = {
                grass = 0.35,
                magic_spring = 0.1,
                crystal_formation = 0.1,
                flower = 0.2
            },
            entityAdjustments = {
                unicorn = 1.5,
                plains_spirit = 1.3,
                celestial_being = 1.0
            },
            specialEffects = {
                "floating_lights",
                "magical_mist",
                "sparkling_air"
            }
        },
        windswept = {
            name = "Windswept Plains",
            elevationMultiplier = 1.0,
            temperatureOffset = -1.0,
            tileAdjustments = {
                grass = 0.4,
                tall_grass = 0.2,
                rock = 0.1
            },
            entityAdjustments = {
                wind_elemental = 2.0,
                griffin = 1.5,
                horse = 1.3
            },
            specialEffects = {
                "strong_wind",
                "swaying_grass",
                "distant_thunder"
            }
        }
    },
    
    -- Structures that can generate in this biome
    structures = {
        {
            name = "farmstead",
            chance = 0.15,
            entities = {"farmer", "crop_field", "barn"}
        },
        {
            name = "windmill",
            chance = 0.1,
            entities = {"miller", "grain_silo", "windmill"}
        },
        {
            name = "ancient_circle",
            chance = 0.05,
            requiresVariant = "magical",
            entities = {"stone_circle", "magic_spring", "ancient_artifact"}
        },
        {
            name = "nomad_camp",
            chance = 0.12,
            entities = {"nomad", "tent", "campfire"}
        },
        {
            name = "griffin_nest",
            chance = 0.07,
            requiresVariant = "windswept",
            entities = {"griffin", "nest", "prey_remains"}
        },
        {
            name = "unicorn_meadow",
            chance = 0.08,
            requiresVariant = "magical",
            entities = {"unicorn", "magic_spring", "enchanted_flowers"}
        }
    },
    
    -- Weather patterns
    weather = {
        clear = 0.4,
        cloudy = 0.3,
        rain = 0.2,
        storm = 0.08,
        windy = 0.02
    }
}

return PlainsBiome 