-- biomes/swamp.lua
local SwampBiome = {
    id = "swamp",
    name = "Swamp",
    description = "A humid and murky wetland with a variety of unique plant and animal life.", -- [cite: 697]

    -- Environmental factors (Humid, warm, stagnant air)
    environment = {
        humidity = 0.9,
        temperature = 0.7, -- Represents warm
        sunlight = 0.5,    -- Can be reduced by trees/fog
        windStrength = 0.1,
        airQuality = 0.4   -- Decaying matter, stagnant water
    },

    -- Core tile types used in this biome
    -- NOTE: 'swamp_water', 'poisonous_bog' need defining. Using 'mud', 'shallow_water', 'water', 'ruins'.
    primaryTiles = {"mud", "shallow_water"}, -- Mix of mud and water
    secondaryTiles = {"dirt", "swamp_water", "grass"}, -- Hummocks of dirt/grass
    rareTiles = {"poisonous_bog", "ruins"}, -- Sunken ruins or dangerous bog patches

    -- Default proportions (adjustable through variants)
    tileDistribution = {
        mud = 0.4,
        shallow_water = 0.35,
        dirt = 0.1,        -- Small islands/banks
        grass = 0.05,      -- On the dirt patches
        swamp_water = 0.05, -- Deeper/murkier water variant?
        poisonous_bog = 0.03,
        ruins = 0.02
    },

    -- Entities common to this biome
    commonEntities = {
        "frog", "snake", "dragonfly", "giant_mosquito", "swamp_reeds" -- Added reeds
    },
    uncommonEntities = {
        "alligator", "swamp_monster_minor", "will_o_wisp", "giant_leech" -- Added leech/wisp
    },
    rareEntities = {
        "swamp_hydra", "ancient_bog_spirit", "witch_swamp" -- Added hydra/spirit/witch
    },

    -- Biome variants for diverse generation
    variants = {
        murky_bog = {
            name = "Murky Bog",
            humidity = 0.95,
            airQuality = 0.2,
            tileAdjustments = {
                mud = 0.5,
                shallow_water = 0.2,
                poisonous_bog = 0.15, -- More dangerous bog
                dirt = 0.05
            },
            entityAdjustments = {
                 swamp_monster_minor = 1.5,
                 giant_leech = 1.4,
                 alligator = 0.8
            },
            specialEffects = {"thick_fog", "bubbling_mud_sound", "disease_aura"}
        },
        mangrove_thicket = {
            name = "Mangrove Thicket", -- Likely coastal
            humidity = 0.9,
            tileAdjustments = {
                mud = 0.3,
                shallow_water = 0.4, -- More shallow brackish water?
                mangrove_roots_tile = 0.2, -- Hypothetical tile for dense roots
                dirt = 0.1
            },
            entityAdjustments = {
                 snake = 1.3,
                 mangrove_crab = 1.5, -- Specific entity
                 alligator = 1.2
            },
            specialEffects = {"tangled_roots_visual", "brackish_water"}
        },
        cypress_bayou = {
            name = "Cypress Bayou",
            humidity = 0.8,
            tileAdjustments = {
                mud = 0.3,
                shallow_water = 0.4,
                water = 0.1, -- Deeper channels
                cypress_knee_tile = 0.1, -- Hypothetical tile
                dirt = 0.1
            },
            entityAdjustments = {
                 alligator = 1.4,
                 swamp_bird = 1.5, -- e.g., Heron
                 frog = 1.2
            },
            specialEffects = {"spanish_moss_visual", "slow_water_current"}
        },
        sunken_ruins = {
            name = "Sunken Ruins Swamp",
            description = "Ancient structures slowly sinking into the murky swamp.",
            tileAdjustments = {
                mud = 0.3,
                shallow_water = 0.3,
                ruins = 0.25, -- More ruins
                water = 0.1,
                poisonous_bog = 0.05
            },
            entityAdjustments = {
                 swamp_monster_minor = 1.3,
                 ancient_bog_spirit = 1.5,
                 snake = 1.2
            },
            specialEffects = {"submerged_structures_visual", "ancient_whispers"}
        }
    },

    -- Structures that can generate in this biome
    structures = {
        {
            name = "fishing_village", -- Likely on stilts or edge
            chance = 0.06,
            requiresNearby = "water", -- Need access to deeper/clearer water?
            entities = {"swamp_fisher", "villager_stilt", "drying_racks"}
        },
        {
            name = "stilt_house", -- Isolated dwelling
            chance = 0.1,
            entities = {"swamp_hermit", "strange_pet"}
        },
        {
            name = "abandoned_shack",
            chance = 0.12,
            entities = {"giant_leech_hidden", "rotted_supplies", "ghost_lost"}
        },
        {
            name = "ancient_altar", -- Often part of ruins
            chance = 0.04,
            requiresNearby = "ruins",
            entities = {"bog_spirit_guardian", "ritual_remains"}
        },
        { -- Added Witch Hut from features list
             name = "witch_hut",
             chance = 0.03,
             unique = true,
             entities = {"witch_swamp", "black_cat", "bubbling_cauldron"}
        }
    },

    -- Weather patterns
    weather = {
        transitions = {
             fog = { fog = 50, rain_light = 30, clear_humid = 20 },
             rain_light = { fog = 30, rain_light = 40, rain_heavy = 25, clear_humid = 5 },
             rain_heavy = { rain_light = 60, rain_heavy = 30, storm = 10 },
             storm = { rain_heavy = 70, rain_light = 30 },
             clear_humid = { fog = 40, rain_light = 30, clear_humid = 30 }
        },
        default = "fog" -- Swamps often start foggy/humid
    },

    -- Unique features generation
    features = {
        {
            name = "sunken_ruins", -- Areas of ruin tiles
            chance = 0.1,
            size = {min=5, max=15}
        },
        -- Witch hut handled as structure
        {
            name = "hidden_grove", -- Small area of different biome? e.g., magical glade
            chance = 0.05,
            -- Generation logic places a small patch of different tiles/entities
        },
        {
            name = "poisonous_bog", -- Places hazardous bog tiles
            chance = 0.08,
            size = {min=4, max=10},
            danger = true
        },
        {
            name = "giant_lily_pad", -- Large entity or special platform tile
            chance = 0.06,
            requiresTile = "water" -- Needs to be on water
        },
         -- Added cypress/mangrove features assuming trees are entities/features mostly
         {
             name = "cypress_stand",
             chance = 0.15,
             -- Places cypress tree entities
         },
         {
             name = "mangrove_cluster",
             chance = 0.12,
             requiresVariant = "mangrove_thicket" -- Or coastal proximity
             -- Places mangrove tree entities / root tiles
         }
    },

    -- Generation algorithm (Structure based on forest/plains)
    generate = function(chunk, world, variant)
        print("Generating " .. (variant and SwampBiome.variants[variant].name or "Swamp") .. " biome")
        local variantData = variant and SwampBiome.variants[variant] or nil
        local humidity = variantData and variantData.humidity or SwampBiome.environment.humidity
        local airQuality = variantData and variantData.airQuality or SwampBiome.environment.airQuality

        -- Adjust tile distribution
        local tileDistribution = {}
        for tileType, chance in pairs(SwampBiome.tileDistribution) do
            tileDistribution[tileType] = chance
            if variantData and variantData.tileAdjustments and variantData.tileAdjustments[tileType] then
                tileDistribution[tileType] = variantData.tileAdjustments[tileType]
            end
        end

        -- Noise functions
        local seed = world.seed + (chunk.x * 293 + chunk.y * 701)
        math.randomseed(seed)
        local function pnoise2D(x, y, frequency, seed_offset) return love.math.noise(x * frequency, y * frequency, (world.seed + (seed_offset or 0)) * 0.01) end

        -- Generate tiles
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                local worldX = chunk.x * world.CHUNK_SIZE + x
                local worldY = chunk.y * world.CHUNK_SIZE + y

                local elevationNoise = pnoise2D(worldX, worldY, 0.07, 1) -- Controls water level vs land patches
                local mudNoise = pnoise2D(worldX, worldY, 0.15, 2) -- Controls mud distribution
                local featureNoise = pnoise2D(worldX, worldY, 0.03, 3) -- Larger features like ruin areas/bogs

                -- Determine tile type
                local tileType = "mud" -- Default to mud
                if elevationNoise < -0.3 then
                    tileType = "shallow_water"
                    if elevationNoise < -0.6 then tileType = "water" end -- Deeper pools/channels
                elseif elevationNoise > 0.4 then
                    tileType = "dirt" -- Higher ground patches
                    if math.random() < 0.4 then tileType = "grass" end -- Grass on higher ground
                elseif mudNoise > 0.5 then
                     tileType = "mud" -- Ensure mud is common
                end

                 -- Apply distribution for rarer types
                 local tileRoll = math.random()
                 local cumulativeChance = 0
                 for tType, chance in pairs(tileDistribution) do
                      cumulativeChance = cumulativeChance + chance
                      -- Override floor types only
                      if (tileType == "mud" or tileType == "dirt" or tileType == "grass") and tileRoll <= cumulativeChance then
                           if tType == "poisonous_bog" or tType == "ruins" then
                                tileType = tType
                                break
                           end
                      end
                 end

                -- Variant overrides (simplified)
                if variant == "murky_bog" and mudNoise > 0.3 then
                     if math.random() < 0.2 then tileType = "poisonous_bog" end
                elseif variant == "mangrove_thicket" and elevationNoise < -0.1 then
                     -- More water, potentially place root tiles/entities later
                     if math.random() < 0.3 then tileType = "shallow_water" end
                end

                -- Create the tile
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = math.random(1, 3),
                    humidity = humidity + (math.abs(elevationNoise) * 0.1),
                    airQuality = airQuality - (1 - humidity) * 0.1, -- Worse air in less humid spots? or just base?
                    isUnderground = false
                }

                 -- Apply properties based on type
                 if tileType == "mud" then tile.movementSpeed = 0.4; tile.stickiness = 0.7; end
                 if tileType == "shallow_water" or tileType == "water" then tile.passable = true; tile.movementSpeed = 0.6; end -- Swamp water is walkable but slow
                 if tileType == "poisonous_bog" then tile.hazard = "poison"; tile.movementSpeed = 0.2; end

                local tileIndex = x + y * world.CHUNK_SIZE
                chunk.tiles[tileIndex] = tile
            end
        end

        -- Call helper functions
        SwampBiome.generateFeatures(chunk, world, variant)
        SwampBiome.populateEntities(chunk, world, variant)
        SwampBiome.generateStructures(chunk, world, variant)
        SwampBiome.postProcess(chunk, world, variant)
    end,

    -- Helper function implementations (Placeholders - need detailed logic)
    generateFeatures = function(chunk, world, variant)
        print("Generating Swamp features...")
        -- Logic for placing features like sunken ruins areas, hidden groves, bog patches, giant lily pads, tree clusters (cypress/mangrove)
        for _, feature in ipairs(SwampBiome.features) do
             if math.random() < feature.chance then
                  if not feature.requiresVariant or feature.requiresVariant == variant then
                      if not feature.unique or not (chunk.uniqueFeatures and chunk.uniqueFeatures[feature.name]) then
                         local fx = math.random(1, world.CHUNK_SIZE - 2)
                         local fy = math.random(1, world.CHUNK_SIZE - 2)
                         local fwx = chunk.x * world.CHUNK_SIZE + fx
                         local fwy = chunk.y * world.CHUNK_SIZE + fy
                         print("Placing feature: " .. feature.name .. " at " .. fwx .. "," .. fwy)
                         -- Add specific generation logic per feature
                         if feature.name == "poisonous_bog" then
                            local radius = math.random(feature.size.min, feature.size.max) / 2
                            for dx = -radius, radius do for dy = -radius, radius do
                                if math.sqrt(dx*dx + dy*dy) <= radius then
                                    local tx, ty = fx+dx, fy+dy
                                    if tx >= 0 and tx < world.CHUNK_SIZE and ty >= 0 and ty < world.CHUNK_SIZE then
                                        chunk.tiles[tx + ty * world.CHUNK_SIZE].type = "poisonous_bog"
                                    end
                                end
                            end end
                         elseif feature.name == "cypress_stand" then
                              local count = math.random(3, 8)
                              for i=1, count do
                                   world.entitySystem:addEntity("cypress_tree", fwx+math.random(-3,3), fwy+math.random(-3,3), {size=math.random() + 0.5})
                              end
                         end
                         -- Mark unique
                         if feature.unique then chunk.uniqueFeatures = chunk.uniqueFeatures or {}; chunk.uniqueFeatures[feature.name] = true end
                     end
                 end
             end
        end
    end,

    populateEntities = function(chunk, world, variant)
        print("Populating Swamp entities...")
        local variantData = variant and SwampBiome.variants[variant] or nil
        local function spawnEntity(entityType, count)
            local multiplier = 1.0
            if variantData and variantData.entityAdjustments and variantData.entityAdjustments[entityType] then multiplier = variantData.entityAdjustments[entityType] end
            local adjustedCount = math.floor(count * multiplier + 0.5)
            for i = 1, adjustedCount do
                 local attempts = 0; local placed = false
                 while attempts < 5 and not placed do
                     local x = math.random(0, world.CHUNK_SIZE - 1)
                     local y = math.random(0, world.CHUNK_SIZE - 1)
                     local tile = chunk.tiles[x + y * world.CHUNK_SIZE]
                     -- Check if tile is suitable (allow water/mud for many swamp creatures)
                     if tile and tile.type ~= "poisonous_bog" then -- Avoid spawning directly in poison
                          world.entitySystem:addEntity(entityType, chunk.x * world.CHUNK_SIZE + x, chunk.y * world.CHUNK_SIZE + y)
                          placed = true
                     end
                     attempts = attempts + 1
                 end
            end
        end
        -- Spawn based on lists
        for _, et in ipairs(SwampBiome.commonEntities) do spawnEntity(et, math.random(2,6)) end -- Swamps are usually teeming
        for _, et in ipairs(SwampBiome.uncommonEntities) do spawnEntity(et, math.random(0,3)) end
        for _, et in ipairs(SwampBiome.rareEntities) do if math.random() < 0.1 then spawnEntity(et, 1) end end
        -- Add variant specifics
    end,

    generateStructures = function(chunk, world, variant)
        print("Generating Swamp structures...")
         -- Similar logic to previous biomes: Iterate list, check chance/variant, find location, place structure & entities
          for _, structure in ipairs(SwampBiome.structures) do
             if math.random() < structure.chance then
                  if not structure.requiresVariant or structure.requiresVariant == variant then
                      local attempts = 0; local placed = false; local sx, sy
                      while attempts < 10 and not placed do
                           sx = math.random(2, world.CHUNK_SIZE - 3)
                           sy = math.random(2, world.CHUNK_SIZE - 3)
                           local tile = chunk.tiles[sx + sy * world.CHUNK_SIZE]
                           -- Ensure placement on slightly more solid ground? Or allow stilt houses over water?
                           if tile and (tile.type=="dirt" or tile.type=="grass" or (structure.name=="stilt_house" and tile.type=="shallow_water")) then
                                placed = true
                           end
                           attempts = attempts + 1
                      end
                      if placed then
                           local worldX = chunk.x * world.CHUNK_SIZE + sx
                           local worldY = chunk.y * world.CHUNK_SIZE + sy
                           print("Placing structure: "..structure.name.." at "..worldX..","..worldY)
                           if world.chunkSystem and world.chunkSystem.placeStructure then
                                world.chunkSystem:placeStructure(chunk, structure.name, worldX, worldY)
                           else
                               world.entitySystem:addEntity("structure_marker", worldX, worldY, { structureType = structure.name })
                           end
                           if structure.entities then
                                for _, et in ipairs(structure.entities) do
                                     world.entitySystem:addEntity(et, worldX + math.random(-1,1), worldY + math.random(-1,1))
                                end
                           end
                      end
                  end
             end
         end
    end,

    postProcess = function(chunk, world, variant)
        print("Post-processing Swamp chunk...")
        -- Apply fog, disease risk, etc.
        local variantData = variant and SwampBiome.variants[variant] or nil
        if variantData and variantData.specialEffects then
             chunk.environmentalEffects = chunk.environmentalEffects or {}
             for _, effect in ipairs(variantData.specialEffects) do table.insert(chunk.environmentalEffects, effect) end
        end
         -- Apply base disease risk/fog to chunk?
         chunk.diseaseRisk = SwampBiome.diseaseRisk + (variantData and variantData.diseaseRiskMod or 0)
         chunk.fogLevel = SwampBiome.fogLevel + (variantData and variantData.fogMod or 0)
    end,

    init = function(worldCore)
        print("Swamp biome module initialized")
        SwampBiome.worldCore = worldCore
        print("Swamp biome registered successfully for later use")
    end,

    registerWithWorld = function(world)
        print("Registering Swamp biome generators with world")
        if not world or not world.chunkSystem then print("Warning: Cannot register Swamp biome - world or chunkSystem is nil") return false end
        world.chunkSystem:registerGenerator(SwampBiome.id, SwampBiome.generate)
        for variantId, variantData in pairs(SwampBiome.variants) do
            local fullVariantId = SwampBiome.id .. "_" .. variantId
            world.chunkSystem:registerGenerator(fullVariantId, function(chunk, worldInstance)
                SwampBiome.generate(chunk, worldInstance, variantId)
            end)
        end
        return true
    end,
}

return SwampBiome