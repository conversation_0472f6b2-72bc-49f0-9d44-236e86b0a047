-- items/weapons/mace.lua

local Weapon = {
    id = "mace",
    name = "Mace",
    description = "A heavy blunt weapon that can crush armor and break bones.",
    type = "mace",
    category = "weapon",
    stackable = false,

    stats = {
        attack_power = 12.0,
        attack_speed = 0.7,
        durability = 110.0,
        repair_cost = 30,
        weight = 6.0,
    },

    damage_type = "physical",
    material = "iron",

    sprite = "res://Sprite/items/weapons/mace.png",
    size = { width = 64, height = 64 },

    effects = {
        armor_penetration = 0.15,
        stun_chance = 0.08
    },

    meta = {
        rarity = "common"
    },

    take_damage = function(self, amount)
        self.stats.durability = math.max(0, self.stats.durability - amount)
        if self.stats.durability == 0 then
            print(self.name .. " is broken! Penalties applied.")
            -- Potential penalties could be applied here
        end
    end,

    repair = function(self)
        local repair_amount = 110 - self.stats.durability
        self.stats.durability = 110
        print(self.name .. " repaired by " .. repair_amount .. " points.")
    end,
    
    onUse = function(self, user, target)
        if target then
            -- Check for stun effect
            local roll = math.random()
            if roll <= self.effects.stun_chance then
                print(target.name .. " was stunned by the impact!")
                -- In a real implementation, this would apply a stun effect to the target
                if target.applyEffect then
                    target:applyEffect("stunned", 2.0) -- Stun for 2 seconds
                end
            end
        end
        return true
    end
}

return Weapon
