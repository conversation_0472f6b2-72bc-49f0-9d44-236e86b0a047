local Bird = {
    id = "bird",
    name = "Bird",
    type = "bird",
    shape = {
        {0, -1}, {0.7, -0.7}, {1, 0}, {0.7, 0.7},
        {0, 1}, {-0.7, 0.7}, {-1, 0}, {-0.7, -0.7}
    },
    size = 6,

    -- Entity categories
    categories = {"animal", "bird", "flying"},

    -- Threat and food categories
    threatCategories = {"predator", "player"},
    foodCategories = {"insect", "seed", "berry"},

    -- Stats
    maxHealth = 15,
    health = 15,
    maxStamina = 40,
    stamina = 40,
    speed = 2.0,

    -- Flight properties
    flight = {
        maxHeight = 8,
        minHeight = 1,
        ascentSpeed = 0.8,
        descentSpeed = 1.2,
        hoverHeight = 3,
        currentHeight = 3,
        wingFlapRate = 0.2
    },

    -- Behaviors
    behaviors = {"flock", "forage", "roost", "migrate"},

    -- Behavior configurations
    behaviorConfigs = {
        flock = {
            moveSpeed = 2.5,
            followDistance = 3,
            separationDistance = 2,
            alignmentStrength = 0.2,
            cohesionStrength = 0.3,
            maxFlockSize = 20
        },
        forage = {
            moveSpeed = 1.5,
            searchRadius = 10,
            forageTime = {5, 10},
            foodTypes = {"seed", "berry", "insect"}
        },
        roost = {
            startTime = "dusk",
            endTime = "dawn",
            roostHeight = {2, 6},
            healthRegen = 0.02,
            staminaRegen = 0.05
        },
        migrate = {
            moveSpeed = 3.0,
            migrationSeason = "winter",
            returnSeason = "spring",
            migrationHeight = {4, 8},
            restInterval = 300
        }
    },

    -- Special abilities
    abilities = {
        quickFlight = {
            speedBoost = 1.5,
            duration = 2,
            cooldown = 5
        },
        song = {
            range = 8,
            duration = 1,
            cooldown = 3,
            effect = "attract"
        }
    },

    -- Appearance
    appearance = {
        sprite = "bird",
        scale = 0.8,
        animations = {
            "idle", "fly", "land", "forage", "sing"
        },
        variants = {
            "sparrow", "finch", "robin", "bluebird"
        }
    },

    -- Sound effects
    sounds = {
        chirp = "bird_chirp",
        song = "bird_song",
        wingFlap = "bird_wing_flap"
    },

    -- Loot drops
    drops = {
        {id = "meat", chance = 0.6, quantity = {1, 1}},
        {id = "feather", chance = 0.8, quantity = {1, 3}},
        {id = "egg", chance = 0.3, quantity = {1, 1}},
        {id = "beak", chance = 0.4, quantity = {1, 1}},
        {id = "bone", chance = 0.5, quantity = {1, 2}}
    }
}

function Bird.init(entity, world)
    -- Copy all fields from Bird template to entity instance
    for k, v in pairs(Bird) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random bird variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

return Bird