-- behaviors/flee.lua
-- Fleeing behavior for prey or scared entities

-- Create a factory function to ensure proper closure
local function createFleeBehavior()
    local FleeBehavior = {
        id = "flee",
        name = "Flee Behavior",
        
        -- Configuration parameters
        defaultConfig = {
            moveSpeed = 2.0,         -- Base movement speed (faster than normal)
            detectionRadius = 8,     -- How far to detect threats
            panicRadius = 5,         -- When to start full panic mode
            calmDownTime = 5,        -- Seconds before calming down after losing threat
            threatTypes = {},        -- Types of entities to flee from
            useHiding = true,        -- Whether to seek hiding spots
            groupFleeing = true,     -- Whether to alert others of same type
            alertRadius = 6,         -- How far to alert others
            staminaDepletion = 0.1,  -- Stamina cost per second of fleeing
            exhaustionThreshold = 0.2 -- When to slow down due to exhaustion
        }
    }
    
    -- Initialize the behavior with an entity
    function FleeBehavior.init(entity, customConfig)
        -- Handle the case where this is being called during module registration
        if entity == FleeBehavior then
            -- Being called as part of module registration
            -- Store worldCore if needed: FleeBehavior.worldCore = customConfig
            return
        end
        
        -- Create behavior state for this entity
        entity.fleeState = entity.fleeState or {
            currentThreat = nil,
            threatPosition = {x = 0, y = 0},
            isPanicking = false,
            calmDownTimer = 0,
            hidingSpot = nil,
            isExhausted = false,
            config = {}
        }
        
        -- Apply configuration (custom settings override defaults)
        local config = entity.fleeState.config
        for k, v in pairs(FleeBehavior.defaultConfig) do
            config[k] = v
        end
        
        if customConfig then
            for k, v in pairs(customConfig) do
                config[k] = v
            end
        end
    end
    
    -- Update function called on every game tick
    function FleeBehavior.update(entity, world, dt)
        local state = entity.fleeState
        if not state then
            FleeBehavior.init(entity)
            state = entity.fleeState
        end
        
        local config = state.config
        
        -- Update timers
        if state.calmDownTimer > 0 then
            state.calmDownTimer = state.calmDownTimer - dt
            if state.calmDownTimer <= 0 then
                state.isPanicking = false
                state.currentThreat = nil
            end
        end
        
        -- Handle stamina depletion if applicable
        if entity.stats and entity.stats.stamina and state.isPanicking then
            entity.stats.stamina = math.max(0, entity.stats.stamina - config.staminaDepletion * dt)
            
            -- Check for exhaustion
            if entity.stats.stamina < entity.stats.maxStamina * config.exhaustionThreshold then
                state.isExhausted = true
            else
                state.isExhausted = false
            end
        end
        
        -- Check for threats if not already panicking
        if not state.isPanicking then
            local threat = FleeBehavior.detectThreats(entity, world)
            
            if threat then
                state.currentThreat = threat.id
                state.threatPosition.x = threat.position.x
                state.threatPosition.y = threat.position.y
                state.isPanicking = true
                state.calmDownTimer = config.calmDownTime
                
                -- Alert others of the same type if configured
                if config.groupFleeing then
                    FleeBehavior.alertOthers(entity, threat, world)
                end
            else
                -- No threats, move normally or recover
                entity.velocity.x = 0
                entity.velocity.y = 0
                return
            end
        else
            -- We're already panicking, update threat position if it still exists
            if state.currentThreat then
                local threat = world.entitySystem.entities[state.currentThreat]
                if threat then
                    state.threatPosition.x = threat.position.x
                    state.threatPosition.y = threat.position.y
                    
                    -- Calculate distance to threat
                    local dx = threat.position.x - entity.position.x
                    local dy = threat.position.y - entity.position.y
                    local distSq = dx*dx + dy*dy
                    
                    -- Reset calm down timer if threat is still close
                    if distSq < config.detectionRadius * config.detectionRadius then
                        state.calmDownTimer = config.calmDownTime
                    end
                end
            end
        end
        
        -- Flee from the threat
        if state.isPanicking then
            if config.useHiding and not state.hidingSpot then
                -- Try to find a hiding spot
                state.hidingSpot = FleeBehavior.findHidingSpot(entity, world)
            end
            
            if state.hidingSpot then
                FleeBehavior.moveToHidingSpot(entity, state.hidingSpot)
            else
                FleeBehavior.fleeFromThreat(entity)
            end
        end
    end
    
    -- Detect nearby threats
    function FleeBehavior.detectThreats(entity, world)
        local state = entity.fleeState
        local config = state.config
        
        -- Find all entities within detection radius
        local nearbyEntities = world.entitySystem:findNearbyEntities(
            entity.position.x, entity.position.y, config.detectionRadius
        )
        
        -- Filter for threats
        local threats = {}
        
        for _, potentialThreat in ipairs(nearbyEntities) do
            -- Skip self
            if potentialThreat.id == entity.id then
                goto continue
            end
            
            -- Check if this is a threat type
            local isThreat = #config.threatTypes == 0 -- If no specific threats, all are threats
            
            for _, threatType in ipairs(config.threatTypes) do
                if potentialThreat.type == threatType then
                    isThreat = true
                    break
                end
            end
            
            if isThreat then
                table.insert(threats, potentialThreat)
            end
            
            ::continue::
        end
        
        -- Return closest threat, if any
        if #threats > 0 then
            table.sort(threats, function(a, b)
                local dxA = a.position.x - entity.position.x
                local dyA = a.position.y - entity.position.y
                local distA = dxA*dxA + dyA*dyA
                
                local dxB = b.position.x - entity.position.x
                local dyB = b.position.y - entity.position.y
                local distB = dxB*dxB + dyB*dyB
                
                return distA < distB
            end)
            
            return threats[1]
        end
        
        return nil
    end
    
    -- Alert other entities of the same type about a threat
    function FleeBehavior.alertOthers(entity, threat, world)
        local state = entity.fleeState
        local config = state.config
        
        -- Find all entities of same type within alert radius
        local nearbyEntities = world.entitySystem:findNearbyEntities(
            entity.position.x, entity.position.y, config.alertRadius
        )
        
        for _, otherEntity in ipairs(nearbyEntities) do
            -- Only alert entities of same type
            if otherEntity.type == entity.type and otherEntity.id ~= entity.id then
                -- Check if they have flee behavior
                if otherEntity.fleeState then
                    otherEntity.fleeState.currentThreat = threat.id
                    otherEntity.fleeState.threatPosition.x = threat.position.x
                    otherEntity.fleeState.threatPosition.y = threat.position.y
                    otherEntity.fleeState.isPanicking = true
                    otherEntity.fleeState.calmDownTimer = config.calmDownTime
                end
            end
        end
    end
    
    -- Find a suitable hiding spot to flee to
    function FleeBehavior.findHidingSpot(entity, world)
        local state = entity.fleeState
        local config = state.config
        local pos = entity.position
        local threatPos = state.threatPosition
        
        -- Calculate direction away from threat
        local dx = pos.x - threatPos.x
        local dy = pos.y - threatPos.y
        local dist = math.sqrt(dx*dx + dy*dy)
        
        if dist < 0.1 then
            -- If somehow on top of threat, pick random direction
            dx = math.random() * 2 - 1
            dy = math.random() * 2 - 1
            dist = math.sqrt(dx*dx + dy*dy)
        end
        
        -- Normalize
        dx = dx / dist
        dy = dy / dist
        
        -- Look for hiding spots in flee direction
        for distance = 3, 10, 1 do
            local testX = pos.x + dx * distance
            local testY = pos.y + dy * distance
            
            -- Check if this location is a good hiding spot
            local tile = world.chunkSystem:getTileAt(testX, testY)
            
            if tile then
                local tileModule = world.modules.tiles[tile.type]
                
                -- Ideal hiding spots: passable but providing cover
                if tileModule and tileModule.passable and 
                   (tileModule.providesCover or tileModule.tall or 
                   tile.type == "tall_grass" or tile.type == "bush" or
                   tile.type == "dense_foliage") then
                    
                    return {x = testX, y = testY, tile = tile}
                end
            end
        end
        
        -- No good hiding spot found
        return nil
    end
    
    -- Move toward hiding spot
    function FleeBehavior.moveToHidingSpot(entity, hidingSpot)
        local state = entity.fleeState
        local config = state.config
        local pos = entity.position
        
        -- Calculate direction to hiding spot
        local dx = hidingSpot.x - pos.x
        local dy = hidingSpot.y - pos.y
        local distSq = dx*dx + dy*dy
        
        -- If reached hiding spot, stop
        if distSq < 1 then
            entity.velocity.x = 0
            entity.velocity.y = 0
            
            -- Trigger hiding animation if available
            if entity.triggerAnimation then
                entity.triggerAnimation("hide")
            end
            
            return
        end
        
        -- Move toward hiding spot
        local dist = math.sqrt(distSq)
        local speed = state.isExhausted and config.moveSpeed * 0.5 or config.moveSpeed
        entity.velocity.x = (dx / dist) * speed
        entity.velocity.y = (dy / dist) * speed
    end
    
    -- Flee directly away from threat
    function FleeBehavior.fleeFromThreat(entity)
        local state = entity.fleeState
        local config = state.config
        local pos = entity.position
        local threatPos = state.threatPosition
        
        -- Calculate direction away from threat
        local dx = pos.x - threatPos.x
        local dy = pos.y - threatPos.y
        local dist = math.sqrt(dx*dx + dy*dy)
        
        if dist < 0.1 then
            -- If somehow on top of threat, pick random direction
            dx = math.random() * 2 - 1
            dy = math.random() * 2 - 1
            dist = math.sqrt(dx*dx + dy*dy)
        end
        
        -- Normalize and apply speed
        local speed = state.isExhausted and config.moveSpeed * 0.5 or config.moveSpeed
        entity.velocity.x = (dx / dist) * speed
        entity.velocity.y = (dy / dist) * speed
        
        -- Add some randomness to avoid predictable movement
        if math.random() < 0.1 then
            entity.velocity.x = entity.velocity.x + (math.random() * 0.6 - 0.3)
            entity.velocity.y = entity.velocity.y + (math.random() * 0.6 - 0.3)
        end
    end
    
    return FleeBehavior
end

-- Create and return the behavior module
return createFleeBehavior()
