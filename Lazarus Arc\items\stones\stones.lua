Stones = {
    -- 🌑 Night Stones (Dark Magic, Rare Enchantments)
    night_stone_1 = {name = "Night Stone (Raw)", category = "stone", value = "random(5,15)", weight = "random(0.5,1.0)", effect = "dark_magic_boost"},
    night_stone_2 = {name = "Night Stone (Polished)", category = "stone", value = "random(15,30)", weight = 0.75, effect = "dark_magic_boost"},
    night_stone_50 = {name = "Night Stone (Refined)", category = "stone", value = "random(50,100)", weight = 1.25, effect = "dark_enchant_upgrade"},

    -- 🔥 Fire Stones (Fire Magic & Weapon Enhancements)
    fire_stone_1 = {name = "Fire Stone (Raw)", category = "stone", value = "random(5,20)", weight = "random(0.5,1.0)", effect = "fire_magic_boost"},
    fire_stone_50 = {name = "Fire Stone (Refined)", category = "stone", value = "random(50,100)", weight = 1.25, effect = "fire_weapon_upgrade"},

    -- 🔵 Blue Stones (Water/Air Magic)
    blue_stone_1 = {name = "Blue Stone (Raw)", category = "stone", value = "random(5,15)", weight = "random(0.5,1.0)", effect = "water_magic_boost"},
    blue_stone_50 = {name = "Blue Stone (Refined)", category = "stone", value = "random(50,100)", weight = 1.25, effect = "air_weapon_upgrade"},

    -- 🔆 Sun Stones (Solar Magic, Healing)
    sun_stone_1 = {name = "Sun Stone (Raw)", category = "stone", value = "random(5,20)", weight = "random(0.5,1.0)", effect = "light_magic_boost"},
    sun_stone_50 = {name = "Sun Stone (Refined)", category = "stone", value = "random(50,100)", weight = 1.25, effect = "holy_healing_upgrade"},

    -- ⏳ Time Stones (Rare, Time-based Magic)
    time_stone_1 = {name = "Time Stone (Raw)", category = "stone", value = "random(20,50)", weight = 1.00, effect = "time_slow"},
    time_stone_50 = {name = "Time Stone (Refined)", category = "stone", value = "random(100,300)", weight = 1.50, effect = "time_acceleration"},

    -- 🔴 Blood Stones (Health, Lifesteal)
    blood_stone_1 = {name = "Blood Stone (Raw)", category = "stone", value = "random(10,25)", weight = "random(0.5,1.0)", effect = "lifesteal"},
    blood_stone_50 = {name = "Blood Stone (Refined)", category = "stone", value = "random(75,200)", weight = 1.25, effect = "blood_weapon_upgrade"},

    -- 🔋 Mana Stones (MP Recovery)
    mana_stone_1 = {name = "Mana Stone (Raw)", category = "stone", value = "random(10,30)", weight = 0.75, effect = "restore_mana", restore_amount = "random(10,50)"},
    mana_stone_50 = {name = "Mana Stone (Refined)", category = "stone", value = "random(50,150)", weight = 1.25, effect = "full_mana_recharge", restore_amount = "random(100,300)"}
}

-- Function to get a random value
function get_stone_value(item_id)
    local item = Stones[item_id]
    if not item then
        return "Item not found."
    end

    if type(item.value) == "string" then
        local min_val, max_val = string.match(item.value, "(%d+),(%d+)")
        return math.random(tonumber(min_val), tonumber(max_val))
    else
        return item.value
    end
end

return Stones
