-- entities/crystal_dragon.lua
-- Crystal dragon with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local CrystalDragon = {
    id = "crystal_dragon",
    name = "Crystal Dragon",
    type = "crystal_dragon",
    shape = {
        {0, -20}, {15, -15}, {25, 0}, {15, 15},
        {0, 20}, {-15, 15}, {-25, 0}, {-15, -15}
    },
    size = 25,

    -- Entity categories
    categories = {"dragon", "flying", "crystal", "magical", "rare"},
    threatCategories = {"player", "knight", "treasure_hunter", "dragon_slayer"},
    
    -- Base stats (magical crystal dragon)
    maxHealth = 650,
    health = 650,
    maxStamina = 280,
    stamina = 280,
    maxMana = 550,
    mana = 550,
    speed = 3.3,
    attack = 45,
    defense = 55,  -- High defense due to crystal armor
    magicAttack = 75,  -- Very high magic attack
    magicDefense = 65,
    crystalResonance = 100,
    magicVulnerability = 0.8,  -- Slightly vulnerable to magic
    flight_altitude = 50,
    crystal_mastery = 40,
    prismatic_power = 25,
    
    -- Behaviors
    behaviors = {"crystal_flight", "prismatic_breath", "crystal_magic", "resonance_field"},
    behaviorConfigs = {
        crystal_flight = {
            crystallineMovement = true,
            lightRefraction = 2.0,
            prismTrail = true,
            harmonicFlight = 1.8
        },
        prismatic_breath = {
            breathRange = 30,
            prismDamage = 2.2,
            lightSpectrum = true,
            crystalFormation = 10,
            blindingFlash = 0.6
        },
        crystal_magic = {
            crystalGrowth = 2.5,
            magicAmplification = 2.0,
            prismSpells = true,
            crystallineShields = 2.2
        },
        resonance_field = {
            resonanceRadius = 20,
            magicDisruption = 1.8,
            crystalHarmonics = true,
            frequencyControl = 2.0
        }
    },
    
    -- Enhanced variant system for crystal dragons
    variantChances = {
        normal = 0.50,          -- 50% young crystal dragon
        shiny = 0.35,           -- 35% adult crystal dragon (shiny)
        rare = 0.13,            -- 13% ancient crystal dragon (rare)
        legendary = 0.02        -- 2% prismatic sovereign (legendary)
    },
    
    variants = {
        normal = {
            name = "Young Crystal Dragon",
            description = "A young dragon with crystalline scales that refract light",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {1.1, 1.0, 1.3, 1.0}  -- Clear crystal with blue tint
            }
        },
        
        shiny = {
            name = "Adult Crystal Dragon",
            description = "A mature dragon with mastery over crystal and light magic",
            statModifiers = {
                maxHealth = 1.3,    -- 845 health
                defense = 1.4,      -- 77 defense
                magicAttack = 1.5,  -- 112.5 magic attack
                crystal_mastery = 1.8,
                prismatic_power = 2.0,
                light_control = 2.5
            },
            appearanceModifiers = {
                scale = 1.3,
                glow = true,
                colorTint = {1.2, 1.1, 1.4, 1.0},  -- Brilliant crystal with rainbow aura
                rainbow_refraction = true,
                crystal_armor = true,
                prismatic_aura = true
            },
            soundModifiers = {
                pitch = 1.1,
                volume = 1.2,
                crystalline = true
            }
        },
        
        rare = {
            name = "Ancient Crystal Dragon",
            description = "An ancient wyrm that has become one with the crystal realm",
            statModifiers = {
                maxHealth = 1.8,    -- 1170 health
                defense = 1.8,      -- 99 defense
                magicAttack = 2.0,  -- 150 magic attack
                crystal_mastery = 2.5,
                prismatic_power = 3.0,
                dimensional_crystal = 4.0,
                ancient_resonance = 5.0
            },
            appearanceModifiers = {
                scale = 1.6,
                colorTint = {1.3, 1.2, 1.5, 1.0},  -- Ancient crystal with dimensional glow
                dimensional_facets = true,
                reality_crystal = true,
                harmonic_resonance = true,
                ancient_prism = true
            },
            soundModifiers = {
                pitch = 0.9,
                volume = 1.4,
                reverb = true,
                harmonic = true
            }
        },
        
        legendary = {
            name = "Prismatic Sovereign",
            description = "A legendary dragon that embodies all light and crystal magic",
            statModifiers = {
                maxHealth = 2.5,    -- 1625 health
                defense = 2.2,      -- 121 defense
                magicAttack = 2.8,  -- 210 magic attack
                crystal_mastery = 3.5,
                prismatic_sovereignty = 10.0,
                light_embodiment = 5.0,
                reality_prism = 3.0
            },
            appearanceModifiers = {
                scale = 2.0,
                glow = true,
                colorTint = {1.5, 1.4, 1.8, 1.0},  -- Sovereign crystal with reality-bending light
                prismatic_sovereignty = true,
                light_embodiment_aura = "legendary",
                reality_prism_form = true,
                cosmic_crystal = true
            },
            soundModifiers = {
                pitch = 0.8,
                volume = 1.6,
                reverb = true,
                echo = true,
                sovereign_resonance = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "crystal_dragon_scale", chance = 1.0, quantity = {5, 10}},
        {id = "dragon_blood", chance = 0.9, quantity = {2, 4}},
        {id = "crystal_fang", chance = 0.8, quantity = {1, 2}},
        {id = "prismatic_essence", chance = 1.0, quantity = {3, 6}},
        {id = "resonant_heart", chance = 0.7, quantity = {1, 1}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "prismatic_dragon_scale", chance = 1.0, quantity = {6, 12}},
            {id = "light_control_crystal", chance = 0.9, quantity = {1, 1}},
            {id = "rainbow_prism", chance = 0.8, quantity = {1, 1}},
            {id = "crystal_mastery_core", chance = 0.7, quantity = {1, 2}}
        },
        rare = {
            {id = "ancient_crystal_scale", chance = 1.0, quantity = {8, 15}},
            {id = "dimensional_crystal", chance = 0.9, quantity = {1, 2}},
            {id = "reality_prism_fragment", chance = 0.8, quantity = {1, 1}},
            {id = "harmonic_resonance_core", chance = 0.7, quantity = {2, 4}},
            {id = "ancient_light_essence", chance = 0.8, quantity = {1, 1}}
        },
        legendary = {
            {id = "prismatic_sovereign_scale", chance = 1.0, quantity = {10, 20}},
            {id = "light_embodiment_crown", chance = 0.95, quantity = {1, 1}},
            {id = "prismatic_sovereignty_orb", chance = 0.9, quantity = {1, 1}},
            {id = "reality_prism_essence", chance = 0.8, quantity = {1, 2}},
            {id = "light_mastery_codex", chance = 0.7, quantity = {1, 1}},
            {id = "cosmic_crystal_scepter", chance = 0.6, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "crystal_dragon",
        scale = 1.8,
        animations = {
            "crystal_soar", "prismatic_breath", "harmonic_roar", "crystal_formation", "light_mastery"
        },
        variants = {
            "young_crystal", "adult_crystal", "ancient_crystal", "prismatic_sovereign"
        }
    },
    
    -- Sound effects with crystalline characteristics
    sounds = {
        harmonic_roar = {
            file = "crystal_dragon_roar",
            synth = {
                instrument = "celesta",
                notes = {"C2", "E2", "G2", "C3", "E3", "G3"},
                durations = {1.0, 0.8, 1.0, 0.8, 1.2, 1.5},
                volume = 1.1,
                crystalline = true
            }
        },
        prismatic_breath = {
            file = "prismatic_breath",
            synth = {
                instrument = "kalimba",
                notes = {"G3", "B3", "D4", "G4", "B4"},
                durations = {0.4, 0.3, 0.5, 0.4, 0.6},
                volume = 0.9,
                prismatic = true
            }
        },
        crystal_resonance = {
            file = "crystal_resonance",
            synth = {
                instrument = "xylophone",
                notes = {"C4", "E4", "G4", "C5", "E5"},
                durations = {0.6, 0.5, 0.6, 0.5, 0.8},
                volume = 0.8,
                resonant = true
            }
        },
        light_harmony = {
            file = "light_harmony",
            synth = {
                instrument = "harp",
                notes = {"F3", "A3", "C4", "F4", "A4", "C5"},
                durations = {0.8, 0.6, 0.8, 0.6, 0.8, 1.0},
                volume = 0.7,
                luminous = true
            }
        }
    },
    
    -- Special crystal dragon abilities
    abilities = {
        prismatic_breath = {
            type = "active",
            description = "Rainbow breath that creates crystal formations",
            effect = "prismatic_breath_attack",
            cooldown = 14
        },
        crystal_armor = {
            type = "passive",
            description = "Natural crystal armor provides high magic defense",
            effect = "crystal_defense"
        },
        light_refraction = {
            type = "active",
            description = "Bends light to become invisible or blind enemies",
            effect = "light_manipulation",
            manaCost = 50,
            cooldown = 16
        },
        resonance_field = {
            type = "passive",
            description = "Disrupts enemy magic with harmonic frequencies",
            effect = "magic_disruption"
        },
        crystal_growth = {
            type = "active",
            description = "Creates crystal barriers and formations",
            effect = "crystal_creation",
            manaCost = 60,
            cooldown = 18
        },
        prismatic_immunity = {
            type = "passive",
            description = "Immune to light-based attacks and resistant to magic",
            effect = "light_immunity"
        }
    }
}

-- Initialize the crystal dragon entity using enhanced template
function CrystalDragon.init(entity, world)
    -- Copy all fields from CrystalDragon template to entity instance
    for k, v in pairs(CrystalDragon) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return CrystalDragon
