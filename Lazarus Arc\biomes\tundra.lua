-- biomes/tundra.lua
-- Declare TundraBiome at the top level so it can be referenced from within functions
local TundraBiome = {}

-- Define the biome properties
TundraBiome = {
    id = "tundra",
    name = "Tundra",
    description = "A cold and treeless biome with a layer of permafrost beneath the surface.", -- [cite: 694]

    -- Environmental factors (cold, low humidity, variable light, moderate wind)
    environment = {
        humidity = 0.2,
        temperature = 0.1, -- Represents very cold, closer to 0.0 = freezing
        sunlight = 0.6,    -- Can vary greatly depending on latitude/season in reality
        windStrength = 0.5
    },

    -- Core tile types used in this biome
    -- NOTE: 'frozen_ground' needs definition. Using 'rocky_ground' for 'rock', 'grass' for 'sparse_grass'.
    primaryTiles = {"snow", "frozen_ground"},
    secondaryTiles = {"ice", "rocky_ground", "grass"}, -- Assuming rocky_ground for rock, grass for sparse_grass
    rareTiles = {"ice_cave"}, -- Added from features

    -- Default proportions (adjustable through variants)
    tileDistribution = {
        snow = 0.5,
        frozen_ground = 0.3,
        ice = 0.1,
        rocky_ground = 0.07,
        grass = 0.02, -- Sparse grass
        ice_cave = 0.01
    },

    -- Entities common to this biome [cite: 695]
    commonEntities = {
        "arctic_fox", "snowy_owl", "ptarmigan" -- Assuming ptarmigan is common
    },
    uncommonEntities = {
        "musk_ox", "penguin" -- Penguins are specific to southern hemisphere, but included per list
    },
    rareEntities = {
        "polar_bear", "ice_elemental" -- Added elemental for variety
    },

    -- Biome variants for diverse generation
    variants = {
        frozen_wastes = {
            name = "Frozen Wastes",
            temperature = 0.05, -- Even colder
            tileAdjustments = {
                snow = 0.6,
                ice = 0.25,
                frozen_ground = 0.1,
                rocky_ground = 0.05,
                grass = 0.0
            },
            entityAdjustments = {
                polar_bear = 1.5,
                ice_elemental = 1.3,
                arctic_fox = 0.5
            },
            specialEffects = {"howling_wind", "extreme_cold"}
        },
        coastal_tundra = {
            name = "Coastal Tundra",
            humidity = 0.4,
            temperature = 0.15, -- Slightly milder near coast
            tileAdjustments = {
                snow = 0.4,
                frozen_ground = 0.25,
                ice = 0.15, -- More coastal ice / icebergs?
                rocky_ground = 0.15,
                grass = 0.05
            },
            entityAdjustments = {
                penguin = 2.0,
                seal = 1.5, -- Added seal for coastal
                snowy_owl = 1.2
            },
            specialEffects = {"sea_mist_cold", "iceberg_nearby"}
        },
        alpine_tundra = {
            name = "Alpine Tundra",
            temperature = 0.1,
            windStrength = 0.7,
            tileAdjustments = {
                snow = 0.3,
                frozen_ground = 0.3,
                rocky_ground = 0.35, -- More rock
                grass = 0.05
            },
            entityAdjustments = {
                 musk_ox = 1.5,
                 mountain_goat_snow = 1.0, -- Hypothetical variant
                 arctic_fox = 1.2
            },
            specialEffects = {"strong_wind", "thin_air"}
        }
    },

    -- Structures that can generate in this biome [cite: 695]
    structures = {
        {
            name = "inuit_village", -- Renamed for clarity, could be generic 'tundra_village'
            chance = 0.05,
            entities = {"tundra_villager", "hunter", "sled_dog"}
        },
        {
            name = "research_station",
            chance = 0.03,
            entities = {"scientist", "research_drone", "security_bot"}
        },
        {
            name = "abandoned_outpost",
            chance = 0.08,
            entities = {"frozen_remains", "ice_wraith"}
        }
        -- Maybe add ice fishing hut?
    },

    -- Weather patterns common in this biome (Using transitions like template)
    weather = {
        transitions = {
             clear = { clear = 60, snow_light = 25, blizzard = 5, freezing_fog = 10 },
             snow_light = { clear = 30, snow_light = 50, blizzard = 15, freezing_fog = 5 },
             blizzard = { snow_light = 60, blizzard = 35, clear = 5 }, -- Less likely to clear immediately
             freezing_fog = { clear = 40, snow_light = 30, freezing_fog = 30 }
        },
        default = "clear" -- Starting weather state
    },

    -- Unique features generation [cite: 695]
    features = {
        {
            name = "ice_cave",
            chance = 0.1,
             -- Generation logic would place a cave entrance structure/tile
        },
        {
            name = "frozen_lake",
            chance = 0.15,
            size = {min = 10, max = 40} -- Places 'ice' tiles
        },
        {
            name = "aurora_borealis", -- This is more of a sky effect than a ground feature
            chance = 0.05, -- Chance per night? Needs special handling.
            specialEffect = true
        },
        {
            name = "glacier", -- Large feature, might span chunks
            chance = 0.03,
            unique = true -- Unique per region probably
            -- Generation logic would place 'ice' tiles, possibly special 'glacier_ice'
        },
        {
            name = "iceberg", -- Only makes sense in coastal variant
            chance = 0.08,
            requiresVariant = "coastal_tundra"
            -- Generation logic would place a large floating ice entity/structure
        }
    },

    -- Generation algorithm (Structure based on forest/plains)
    generate = function(chunk, world, variant)
        print("Generating " .. (variant and TundraBiome.variants[variant].name or "Tundra") .. " biome")
        local variantData = variant and TundraBiome.variants[variant] or nil
        local tempMod = variantData and variantData.temperature or TundraBiome.environment.temperature

        -- Adjust tile distribution based on variant
        local tileDistribution = {}
        for tileType, chance in pairs(TundraBiome.tileDistribution) do
            tileDistribution[tileType] = chance
            if variantData and variantData.tileAdjustments and variantData.tileAdjustments[tileType] then
                tileDistribution[tileType] = variantData.tileAdjustments[tileType]
            end
        end

        -- Initialize 2D tiles array if not exists
        chunk.tiles = chunk.tiles or {}
        for x = 0, world.CHUNK_SIZE - 1 do
            chunk.tiles[x] = chunk.tiles[x] or {}
        end

        -- Generate base terrain using noise functions
        local seed = world.seed + (chunk.x * 811 + chunk.y * 53)
        math.randomseed(seed)
        local function noise2D(x, y, frequency) return math.abs(math.sin(x * frequency) * math.cos(y * frequency)) end

        -- Generate tiles
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                local worldX = chunk.x * world.CHUNK_SIZE + x
                local worldY = chunk.y * world.CHUNK_SIZE + y

                local baseNoise = noise2D(worldX, worldY, 0.08) -- General terrain variation
                local detailNoise = noise2D(worldX, worldY, 0.25) -- Finer detail, ice patches etc
                local featureNoise = noise2D(worldX, worldY, 0.02) -- Large features like glaciers/lakes

                -- Determine tile type based on noise and distribution
                local tileType = "snow" -- Default for Tundra
                local tileRoll = math.random()
                local cumulativeChance = 0
                for tType, chance in pairs(tileDistribution) do
                    cumulativeChance = cumulativeChance + chance
                    if tileRoll <= cumulativeChance then
                        tileType = tType
                        break
                    end
                end

                -- Modify based on noise - e.g., lower areas more likely ice/frozen ground
                if baseNoise < 0.2 and featureNoise < 0.3 then -- Low, large flat areas
                     tileType = math.random() < 0.6 and "ice" or "frozen_ground"
                elseif baseNoise > 0.7 and detailNoise > 0.6 then -- High, detailed areas
                     tileType = math.random() < 0.7 and "rocky_ground" or "stone" -- Assume stone exists or use rocky_ground
                elseif detailNoise < 0.2 then -- Smooth areas might keep snow/frozen ground
                     tileType = math.random() < 0.6 and "snow" or "frozen_ground"
                end

                -- Sparse grass mainly on rocky/less frozen ground
                if (tileType == "rocky_ground" or tileType == "frozen_ground") and math.random() < 0.15 then
                    tileType = "grass" -- Represents sparse grass
                end

                -- Create the tile
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = math.random(1, 3),
                    passable = (tileType ~= "ice" and tileType ~= "snowdrift"),
                    isUnderground = false,
                    temperature = tempMod + (detailNoise * 0.1 - 0.05),
                    snowDepth = 0.3 + baseNoise * 0.7
                }

                -- Apply specific tile properties based on type
                if tileType == "snow" then tile.depth = 0.3 + baseNoise * 0.7 end
                if tileType == "ice" then tile.thickness = 0.5 + detailNoise * 1.0 end
                if tileType == "grass" then tile.variant = 1 end -- Sparse variant of grass

                -- Add to chunk using 2D indexing
                chunk.tiles[x][y] = tile
            end
        end

        -- Call helper functions defined below
        TundraBiome.generateFeatures(chunk, world, variant)
        TundraBiome.populateEntities(chunk, world, variant)
        TundraBiome.generateStructures(chunk, world, variant)
        TundraBiome.postProcess(chunk, world, variant) -- Call postProcess
    end,

    -- Helper function to generate features (Basic Implementation)
    generateFeatures = function(chunk, world, variant)
        print("Generating Tundra features...")
        -- Iterate through features list [cite: 695]
        for _, feature in ipairs(TundraBiome.features) do
             if math.random() < feature.chance then
                 if not feature.requiresVariant or feature.requiresVariant == variant then
                      if not feature.unique or not (chunk.uniqueFeatures and chunk.uniqueFeatures[feature.name]) then
                         -- Find spot and place feature (Simplified logic)
                         local fx = math.random(2, world.CHUNK_SIZE - 3)
                         local fy = math.random(2, world.CHUNK_SIZE - 3)
                         local fwx = chunk.x * world.CHUNK_SIZE + fx
                         local fwy = chunk.y * world.CHUNK_SIZE + fy
                         print("Placing feature: " .. feature.name .. " at " .. fwx .. "," .. fwy)

                         if feature.name == "frozen_lake" then
                             local radius = math.random(feature.size.min, feature.size.max) / 2
                             for dx = -radius, radius do
                                 for dy = -radius, radius do
                                     local dist = math.sqrt(dx*dx + dy*dy)
                                     if dist <= radius then
                                         local tx = fx + dx
                                         local ty = fy + dy
                                         if tx >= 0 and tx < world.CHUNK_SIZE and ty >= 0 and ty < world.CHUNK_SIZE then
                                              chunk.tiles[tx][ty].type = "ice"
                                              chunk.tiles[tx][ty].thickness = 1.5 + math.random()*0.5
                                         end
                                     end
                                 end
                             end
                         elseif feature.name == "ice_cave" then
                              -- Place cave entrance entity/tile modification
                              world.entitySystem:addEntity("ice_cave_entrance", fwx, fwy, {})
                         elseif feature.name == "glacier" then
                              -- More complex logic needed - maybe place large entity or modify many tiles to glacier_ice
                              world.entitySystem:addEntity("glacier_marker", fwx, fwy, {}) -- Placeholder
                         elseif feature.name == "iceberg" then
                              -- Place iceberg entity (requires water nearby ideally)
                              world.entitySystem:addEntity("iceberg", fwx, fwy, {size=math.random(5,15)})
                         end
                         -- Mark unique
                         if feature.unique then chunk.uniqueFeatures = chunk.uniqueFeatures or {}; chunk.uniqueFeatures[feature.name] = true end
                     end
                 end
             end
        end
    end,

    -- Helper function to add entities (Basic Implementation)
    populateEntities = function(chunk, world, variant)
        print("Populating Tundra entities...")
        local variantData = variant and TundraBiome.variants[variant] or nil
        local function spawnEntity(entityType, count)
            local multiplier = 1.0
            if variantData and variantData.entityAdjustments and variantData.entityAdjustments[entityType] then
                 multiplier = variantData.entityAdjustments[entityType]
            end
            local adjustedCount = math.floor(count * multiplier + 0.5)
            for i = 1, adjustedCount do
                 local attempts = 0; local placed = false
                 while attempts < 5 and not placed do
                     local x = math.random(0, world.CHUNK_SIZE - 1)
                     local y = math.random(0, world.CHUNK_SIZE - 1)
                     local tile = chunk.tiles[x][y]
                     -- Check if tile is suitable (passable, not deep ice/water unless aquatic)
                     if tile and (tile.type=="snow" or tile.type=="frozen_ground" or tile.type=="rocky_ground" or tile.type=="grass") then
                          world.entitySystem:addEntity(entityType, chunk.x * world.CHUNK_SIZE + x, chunk.y * world.CHUNK_SIZE + y)
                          placed = true
                     end
                     attempts = attempts + 1
                 end
            end
        end

        -- Spawn based on lists [cite: 695]
        for _, et in ipairs(TundraBiome.commonEntities) do spawnEntity(et, math.random(1,4)) end
        for _, et in ipairs(TundraBiome.uncommonEntities) do spawnEntity(et, math.random(0,2)) end
        for _, et in ipairs(TundraBiome.rareEntities) do if math.random() < 0.1 then spawnEntity(et, 1) end end
        -- Add variant specifics e.g., if variant == "coastal_tundra" then spawnEntity("seal", math.random(1,3)) end
    end,

    -- Helper function to place structures (Basic Implementation)
    generateStructures = function(chunk, world, variant)
         print("Generating Tundra structures...")
         -- Iterate through structures list [cite: 695]
         for _, structure in ipairs(TundraBiome.structures) do
             if math.random() < structure.chance then
                  if not structure.requiresVariant or structure.requiresVariant == variant then
                      -- Find suitable location (passable ground)
                      local attempts = 0; local placed = false; local sx, sy
                      while attempts < 10 and not placed do
                           sx = math.random(2, world.CHUNK_SIZE - 3)
                           sy = math.random(2, world.CHUNK_SIZE - 3)
                           local tile = chunk.tiles[sx][sy]
                           if tile and (tile.type=="snow" or tile.type=="frozen_ground" or tile.type=="grass") then
                                placed = true
                           end
                           attempts = attempts + 1
                      end
                      if placed then
                           local worldX = chunk.x * world.CHUNK_SIZE + sx
                           local worldY = chunk.y * world.CHUNK_SIZE + sy
                           print("Placing structure: "..structure.name.." at "..worldX..","..worldY)
                           -- Place structure (assuming function exists)
                           if world.chunkSystem and world.chunkSystem.placeStructure then
                                world.chunkSystem:placeStructure(chunk, structure.name, worldX, worldY)
                           else
                               world.entitySystem:addEntity("structure_marker", worldX, worldY, { structureType = structure.name })
                           end
                           -- Spawn related entities
                           if structure.entities then
                                for _, et in ipairs(structure.entities) do
                                     world.entitySystem:addEntity(et, worldX + math.random(-1,1), worldY + math.random(-1,1))
                                end
                           end
                      end
                  end
             end
         end
    end,

    -- Post-processing for variant-specific effects (Basic Implementation)
    postProcess = function(chunk, world, variant)
        print("Post-processing Tundra chunk...")
        local variantData = variant and TundraBiome.variants[variant] or nil
        if variantData and variantData.specialEffects then
            chunk.environmentalEffects = chunk.environmentalEffects or {}
            for _, effect in ipairs(variantData.specialEffects) do
                 table.insert(chunk.environmentalEffects, effect)
            end
        end
        -- Add other effects like persistent cold aura etc.
    end,

    -- Initialize the biome module (Standard Implementation)
    init = function(worldCore)
        print("Tundra biome module initialized")
        TundraBiome.worldCore = worldCore
        print("Tundra biome registered successfully for later use")
    end,

    -- Register generators with a specific world instance (Standard Implementation)
    registerWithWorld = function(world)
        print("Registering Tundra biome generators with world")
        if not world or not world.chunkSystem then print("Warning: Cannot register Tundra biome - world or chunkSystem is nil") return false end
        world.chunkSystem:registerGenerator(TundraBiome.id, TundraBiome.generate)
        for variantId, variantData in pairs(TundraBiome.variants) do
            local fullVariantId = TundraBiome.id .. "_" .. variantId
            world.chunkSystem:registerGenerator(fullVariantId, function(chunk, worldInstance)
                TundraBiome.generate(chunk, worldInstance, variantId)
                -- Post process is now called within generate for consistency with others
                -- TundraBiome.postProcess(chunk, worldInstance, variantId)
            end)
        end
        return true
    end,
}

return TundraBiome