-- game-loader.lua
-- Main entry point for the game

-- Load core systems
local WorldCore = require("world_core")
local Character = require("character")
local Classes = require("classes")
local ViewportManager = require("viewport_management")
local Player = require("entities/player")
local Combat = require("combat")

-- Game state
local Game = {
    isRunning = false,
    currentWorld = nil,
    players = {},
    viewportManager = nil,
    lastUpdateTime = 0,
    settings = {
        screenWidth = 1280,
        screenHeight = 720,
        soundEnabled = true,
        musicVolume = 0.7,
        sfxVolume = 1.0,
        fullscreen = false
    }
}

-- Function to assign a class to a character
local function assign_class(character, className)
    if not character then
        print("Error: Cannot assign class to nil character")
        return false
    end
    
    if not className then
        print("Warning: No class specified, defaulting to 'adventurer'")
        className = "adventurer"
    end
    
    local classData = Classes.loadedClasses[className]
    if not classData then
        print("Warning: Unknown class '" .. className .. "', defaulting to 'adventurer'")
        classData = Classes.loadedClasses["adventurer"] or {
            base_stats = {
                health = 100,
                mana = 50,
                strength = 10,
                dexterity = 10,
                intelligence = 10
            },
            skills = {"survival"},
            startingItems = {"basic_pack", "torch"}
        }
    end
    
    character.class = className
    if not character.stats then character.stats = {} end
    for stat, value in pairs(classData.base_stats or {}) do
        character.stats[stat] = value
    end
    
    if not character.skills then character.skills = {} end
    for _, skill in ipairs(classData.starting_skills or {}) do
        table.insert(character.skills, skill)
    end
    
    if not character.inventory then character.inventory = {} end
    for _, item in ipairs(classData.startingItems or {}) do
        if not character.addItem then
            character.addItem = function(self, itemId, count)
                count = count or 1
                self.inventory[itemId] = (self.inventory[itemId] or 0) + count
                print(self.name .. " received " .. count .. " " .. itemId)
            end
        end
        
        character:addItem(item, 1)
    end
    
    print("Assigned class '" .. className .. "' to character: " .. character.name)
    return true
end

-- Initialize the game
function Game.init()
    print("Initializing game...")
    
    math.randomseed(os.time())
    
    -- Initialize WorldCore first
    WorldCore.init()
    
    Game.initGraphics()
    Game.loadModules()
    
    Game.viewportManager = ViewportManager.new(Game.settings.screenWidth, Game.settings.screenHeight)
    
    Game.initInput()
    
    print("Game initialized successfully!")
    return true
end

-- Initialize graphics system
function Game.initGraphics()
    print("Initializing graphics...")
    -- For example: love.window.setMode(Game.settings.screenWidth, Game.settings.screenHeight)
end

-- Load all game modules
function Game.loadModules()
    print("Loading game modules...")
    
    -- Dynamically load all tiles from the tiles directory
    local tileFiles = love.filesystem.getDirectoryItems("tiles")
    if tileFiles then
        for _, file in ipairs(tileFiles) do
            if file:match("%.lua$") then
                local tileType = file:gsub("%.lua$", "")
                local success = WorldCore.loadModule("tiles", tileType)
                if not success then
                    print("Warning: Failed to load tile type: " .. tileType)
                end
            end
        end
    end
    
    local biomes = {
        "forest", "desert", "plains", "crystal_cavern", "hub", "ocean"
    }
    
    for _, biome in ipairs(biomes) do
        local success = WorldCore.loadModule("biomes", biome)
        if not success then
            print("Warning: Failed to load biome: " .. biome)
        end
    end
    
    local entityTypes = {
        "player", "npc", "creature", "item", "structure", "effect"
    }
    
    for _, entityType in ipairs(entityTypes) do
        local success = WorldCore.loadModule("entities", entityType)
        if not success then
            print("Warning: Failed to load entity type: " .. entityType)
        end
    end
    
    local behaviors = {
        "wander", "follow", "hunt", "flee", "idle", "guard", "collect"
    }
    
    for _, behavior in ipairs(behaviors) do
        local success = WorldCore.loadModule("behaviors", behavior)
        if not success then
            print("Warning: Failed to load behavior: " .. behavior)
        end
    end
    
    -- Load weather system
    local WeatherSystem = require("utils.weather_system")
    if WeatherSystem then
        Game.weatherSystem = WeatherSystem
        print("Weather system loaded successfully")
    else
        print("Warning: Failed to load weather system")
    end
    
    -- Dynamically load all weather patterns from the weather directory
    local weatherFiles = love.filesystem.getDirectoryItems("weather")
    if weatherFiles then
        for _, file in ipairs(weatherFiles) do
            if file:match("^weather_.+%.lua$") then
                local pattern = file:gsub("^weather_(.+)%.lua$", "%1")
                local success = WorldCore.loadModule("weather", "weather_" .. pattern)
                if not success then
                    print("Warning: Failed to load weather pattern: " .. pattern)
                end
            end
        end
    end
    
    local structures = {
        "cabin", "village", "ruins", "temple", "dungeon", "camp"
    }
    
    for _, structure in ipairs(structures) do
        local success = WorldCore.loadModule("structures", structure)
        if not success then
            print("Warning: Failed to load structure: " .. structure)
        end
    end
    
    print("Modules loaded successfully!")
end

-- Initialize input handlers
function Game.initInput()
    print("Initializing input handlers...")
    Game.keyBindings = {
        player1 = { up = "w", down = "s", left = "a", right = "d", action = "space" },
        player2 = { up = "up", down = "down", left = "left", right = "right", action = "return" },
        player3 = { up = "i", down = "k", left = "j", right = "l", action = "u" },
        player4 = { up = "8", down = "5", left = "4", right = "6", action = "0" },
        general = { quit = "escape", pause = "p", menu = "tab" }
    }
end

-- Start a new game
function Game.newGame(playerNames, playerClasses)
    print("Starting new game...")
    
    local worldSeed = math.random(1, 1000000)
    local worldSize = { width = 512, height = 512 }
    Game.currentWorld = WorldCore.createWorld(worldSeed, {
        width = worldSize.width,
        height = worldSize.height,
        chunkSize = 32, -- Default chunk size
        worldType = "overworld" -- Default world type
    })
    
    for i = 1, #playerNames do
        local character = Character.new(playerNames[i])
        assign_class(character, playerClasses[i])
        
        -- Register the player entity with the world's entity system
        local playerEntity = {
            id = i, -- Using loop index as ID for now, might need UUID later
            type = "player", -- Assuming a 'player' entity type exists
            name = playerNames[i],
            character = character,
            position = { x = 0, y = 0 } -- Default spawn position
        }
        Game.currentWorld.entitySystem:register(playerEntity)
        
        -- TODO: WorldCore.createPlayerHub seems missing, might need implementation or removal
        -- WorldCore.createPlayerHub(Game.currentWorld, i) 
        
        Game.viewportManager:addPlayer({
            id = i,
            x = 0,
            y = 0,
            character = character -- Pass the character data to viewport
        })
        
        -- Store the registered entity reference in Game.players
        table.insert(Game.players, playerEntity) 
    end
    
    Game.isRunning = true
    Game.lastUpdateTime = os.clock()
    
    print("New game started!")
    return true
end

-- Load a saved game
function Game.loadGame(saveName)
    print("Loading saved game: " .. saveName)
    
    Game.currentWorld = WorldCore.loadWorld(saveName)
    
    for i, player in pairs(Game.currentWorld.players) do
        Game.viewportManager:addPlayer({
            id = i,
            x = player.position.x,
            y = player.position.y,
            character = player.character
        })
        table.insert(Game.players, player)
    end
    
    Game.isRunning = true
    Game.lastUpdateTime = os.clock()
    
    print("Game loaded successfully!")
    return true
end

-- Save the current game
function Game.saveGame(saveName)
    print("Saving game as: " .. saveName)
    
    WorldCore.saveWorld(Game.currentWorld, saveName)
    
    print("Game saved successfully!")
    return true
end

-- Main game loop update
function Game.update(dt)
    if not Game.isRunning then return end
    
    local currentTime = os.clock()
    local deltaTime = currentTime - Game.lastUpdateTime
    Game.lastUpdateTime = currentTime
    
    WorldCore.updateWorld(Game.currentWorld, deltaTime)
    Game.processInput()
    Game.viewportManager:update(deltaTime)
    Game.render()
end

-- Process player input
function Game.processInput()
    for i, player in ipairs(Game.players) do
        local playerInput = Game.getPlayerInput(i)
        
        if playerInput.up then player.position.y = player.position.y - 2 end
        if playerInput.down then player.position.y = player.position.y + 2 end
        if playerInput.left then player.position.x = player.position.x - 2 end
        if playerInput.right then player.position.x = player.position.x + 2 end
        
        Game.viewportManager.players[i].x = player.position.x
        Game.viewportManager.players[i].y = player.position.y
        
        if playerInput.action then
            Game.playerAction(player)
        end
    end
end

-- Get input state for a player (simulated here)
function Game.getPlayerInput(playerIndex)
    return { up = false, down = false, left = false, right = false, action = false }
end

-- Process player action
function Game.playerAction(player)
    local tile = Game.currentWorld.chunkSystem:getTileAt(player.position.x, player.position.y)
    if tile then
        local tileType = WorldCore.modules.tiles[tile.type]
        if tileType and tileType.interact then
            local result = tileType.interact(tile, player)
            if result then
                print(player.name .. ": " .. result.message)
                if result.effects then
                    for _, effect in ipairs(result.effects) do
                        Game.applyEffect(player, effect)
                    end
                end
                if result.item then
                    player.character.addItem(result.item, result.count or 1)
                    print(player.name .. " received " .. (result.count or 1) .. " " .. result.item)
                end
            end
        end
    end
    
    local nearbyEntities = Game.currentWorld.entityManager:query(function(entity)
        local dx = entity.position.x - player.position.x
        local dy = entity.position.y - player.position.y
        return math.sqrt(dx * dx + dy * dy) <= 2
    end)
    
    if #nearbyEntities > 0 then
        local entity = nearbyEntities[1]
        print(player.name .. " interacts with " .. entity.type)
        if entity.interact then
            entity.interact(player)
        end
    end
end

-- Apply an effect to a player
function Game.applyEffect(player, effect)
    if effect.type == "restore_health" then
        player.character.stats.health = math.min(100, player.character.stats.health + effect.amount)
        print(player.name .. " restored health!")
    end
end

-- Render the game
function Game.render()
    if Game.viewportManager and Game.viewportManager.render then
        Game.viewportManager:render()
    end
end

return Game
