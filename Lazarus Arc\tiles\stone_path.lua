-- tiles/stone_path.lua
local StonePathTile = {
    id = "stone_path",
    name = "Stone Path",
    passable = true,
    variants = 5, -- Different stone path appearances
    
    -- Entities that can spawn on stone paths
    spawns = {"traveler", "small_creature", "lost_item", "pilgrim"},
    
    -- Movement properties
    movementSpeed = 1.3, -- Faster than natural terrain but slower than proper roads
    
    -- Path-specific properties
    pathType = nil, -- Set during init (cobblestone, flagstone, etc.)
    pathQuality = 0.7, -- How well-laid the path is (0-1)
    isAncient = false, -- Whether this is an ancient path
    isMagical = false, -- Some paths have magical properties
    
    -- Interaction function
    interact = function(tile, entity)
        -- Check for items on the path
        if math.random() < 0.1 then
            local items = {
                {id = "pebble", chance = 0.3, quantity = {1, 3}},
                {id = "coin", chance = 0.2, quantity = 1},
                {id = "pilgrim_token", chance = 0.1, quantity = 1},
                {id = "quartz", chance = 0.05, quantity = 1}
            }
            
            for _, item in ipairs(items) do
                if math.random() < item.chance then
                    local quantity = 1
                    if item.quantity and type(item.quantity) == "table" then
                        quantity = math.random(item.quantity[1], item.quantity[2])
                    elseif type(item.quantity) == "number" then
                        quantity = item.quantity
                    end
                    
                    return {
                        success = true,
                        message = "You find " .. (quantity > 1 and quantity .. " " or "a ") .. item.id .. " on the stone path.",
                        effects = {
                            {type = "add_item", item = item.id, quantity = quantity}
                        }
                    }
                end
            end
        end
        
        -- Special interactions for magical paths
        if tile.isMagical and math.random() < 0.2 then
            local effects = {
                {message = "As you touch the stones, you feel a subtle energy flowing through them.", effect = {type = "restore_energy", amount = 10}},
                {message = "The path stones briefly glow under your touch, revealing hidden runes.", effect = {type = "gain_knowledge", amount = 1}},
                {message = "A sense of direction fills you, and you suddenly understand where this path leads.", effect = {type = "reveal_map_line", direction = tile.direction, distance = 15}}
            }
            
            local chosenEffect = effects[math.random(#effects)]
            return {
                success = true,
                message = chosenEffect.message,
                effects = {chosenEffect.effect}
            }
        end
        
        -- Check for path markers
        if tile.hasMarker and math.random() < 0.4 then
            local markerDescription = "You notice a small marker stone beside the path."
            
            if tile.markerType == "directional" then
                markerDescription = markerDescription .. " It points toward " .. (tile.markerDestination or "an unknown destination") .. "."
            elseif tile.markerType == "warning" then
                markerDescription = markerDescription .. " It bears a warning symbol about dangers ahead."
            elseif tile.markerType == "shrine" then
                markerDescription = markerDescription .. " It appears to be a small shrine for travelers to pay respects."
            end
            
            return {
                success = true,
                message = markerDescription,
                effects = {
                    {type = "journal_note", note = "path_marker", location = {x = tile.x, y = tile.y}}
                }
            }
        end
        
        -- Examine the path itself
        local pathDescriptions = {
            cobblestone = "A path made of rounded cobblestones, worn smooth by years of foot traffic.",
            flagstone = "A path of flat, fitted flagstones that provide a smooth walking surface.",
            mosaic = "A decorative stone path with intricate mosaic patterns between the larger stones.",
            steppingstone = "A series of large flat stones set into the ground as a simple path.",
            ancient = "An ancient stone path with worn symbols visible on some of the stones."
        }
        
        local qualityDesc = ""
        if tile.pathQuality > 0.8 then
            qualityDesc = " The stonework is expertly crafted."
        elseif tile.pathQuality > 0.5 then
            qualityDesc = " The stones are in decent condition, though some have shifted."
        else
            qualityDesc = " Many stones are cracked or missing, making the path uneven."
        end
        
        local ageDesc = ""
        if tile.isAncient then
            ageDesc = " The design suggests it was laid in ancient times."
        end
        
        local description = pathDescriptions[tile.pathType] or "A path made of carefully laid stones."
        description = description .. qualityDesc .. ageDesc
        
        if tile.isMagical and entity.skills and entity.skills.magic and entity.skills.magic > 2 then
            description = description .. " You sense magical properties in these stones."
        end
        
        return {
            success = false,
            message = description
        }
    end,
    
    -- Weather effects
    weatherEffects = {
        rain = function(tile)
            -- Stones get slippery in rain
            tile.isSlippery = true
            if tile.pathQuality < 0.5 then
                tile.movementSpeed = tile.movementSpeed * 0.9 -- Slightly slower when wet
            end
            return nil
        end,
        
        sun = function(tile)
            -- Stones dry in sun
            tile.isSlippery = false
            if tile.pathQuality < 0.5 then
                tile.movementSpeed = 1.3 -- Return to normal speed
            end
            return nil
        end,
        
        snow = function(tile)
            -- Snow covers the path
            tile.isSnowCovered = true
            tile.movementSpeed = tile.movementSpeed * 0.8 -- Slower in snow
            return nil
        end,
        
        lightning = function(tile)
            -- Magical paths might react to lightning
            if tile.isMagical and math.random() < 0.3 then
                return "visual_effect", "path_glow"
            end
            return nil
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {0.65, 0.65, 0.65}, -- Grey stone
        variants = {
            {name = "cobblestone", texture = "cobblestone_pattern"},
            {name = "flagstone", texture = "flagstone_pattern"},
            {name = "mosaic", texture = "mosaic_pattern"},
            {name = "steppingstone", texture = "steppingstone_pattern"},
            {name = "ancient", texture = "ancient_stone_pattern", worn = true}
        },
        decoration_objects = {
            {name = "path_marker", chance = 0.08},
            {name = "small_shrine", chance = 0.03},
            {name = "moss_patch", chance = 0.2},
            {name = "wildflower", chance = 0.1}
        },
        condition_effects = {
            cracks = {threshold = 0.5, intensity = 0.6},
            moss = {threshold = 0.6, chance = 0.4},
            displacement = {threshold = 0.4, intensity = 0.5}
        },
        weather_overlays = {
            wet = {shininess = 0.5, when = "is_slippery"},
            snow = {color = {0.9, 0.9, 0.95}, when = "is_snow_covered"},
            ice = {shininess = 0.7, when = "frozen"}
        },
        magical_effects = {
            runes = {visible = "night", glow = 0.3},
            energy_flow = {visible = "magic_vision", particles = true}
        },
        connection_rules = "path" -- Special connection type for paths
    },
    
    -- Audio properties
    footstepSound = "stone_step_small",
    
    -- Path segments and connections
    segments = {
        straight = {rotation = {0, 90, 180, 270}},
        corner = {rotation = {0, 90, 180, 270}},
        t_junction = {rotation = {0, 90, 180, 270}},
        crossroads = {rotation = {0}},
        end_cap = {rotation = {0, 90, 180, 270}}
    },
    
    -- Connections to other tiles
    connects_to = {
        "stone_path",
        "dirt_path",
        "road",
        "ruins",
        "shrine"
    },
    
    -- Pathfinding properties
    pathfinding = {
        preferred_path = true, -- NPCs prefer to follow paths
        travel_cost = 0.7, -- Lower cost for pathfinding
    },
    
    -- Effects on entities that walk on this tile
    onWalk = function(tile, entity)
        -- Stone paths reduce stamina cost slightly
        if entity.stamina and entity.staminaCostMultiplier then
            entity.staminaCostMultiplier = entity.staminaCostMultiplier * 0.9 -- Temporary reduction
        end
        
        -- Slippery paths might cause problems
        if tile.isSlippery and math.random() < 0.05 then
            return {
                message = "The wet stones are slippery, causing you to stumble.",
                effects = {
                    {type = "slow", duration = 2}
                }
            }
        end
        
        -- Magical paths might have effects
        if tile.isMagical and math.random() < 0.1 then
            return {
                sound = "subtle_magic",
                volume = 0.3,
                effects = {
                    {type = "apply_status", status = "path_blessing", duration = 60}
                }
            }
        end
        
        -- Normal sound
        return {
            sound = "stone_step_small",
            volume = 0.8
        }
    end,
    
    -- Effect when discovering a new stone path
    onDiscover = function(tile, entity)
        -- Add to player's map
        if entity.map and entity.map.addPath then
            entity.map.addPath(tile.x, tile.y, tile.pathType)
        end
        
        -- Ancient paths are noteworthy
        if tile.isAncient and entity.journal and entity.journal.addNote then
            entity.journal.addNote({
                type = "ancient_path",
                pathType = tile.pathType,
                location = {x = tile.x, y = tile.y}
            })
        end
        
        -- Magical paths might be significant
        if tile.isMagical and entity.skills and entity.skills.magic and entity.skills.magic > 1 then
            return {
                message = "You discover a stone path with subtle magical properties.",
                effects = {
                    {type = "reveal_map_line", direction = tile.direction, distance = 8}
                }
            }
        else
            return {
                message = "You discover a " .. tile.pathType .. " path.",
                effects = {
                    {type = "reveal_map_line", direction = tile.direction, distance = 5}
                }
            }
        end
    end
}

function StonePathTile.init(world)
    print("Stone path tile module initialized")
    
    -- Path types and their chances
    local pathTypes = {
        {id = "cobblestone", chance = 0.3},
        {id = "flagstone", chance = 0.3},
        {id = "mosaic", chance = 0.15},
        {id = "steppingstone", chance = 0.15},
        {id = "ancient", chance = 0.1}
    }
    
    -- Register with pathfinding system
    if world.systems and world.systems.pathfinding then
        world.systems.pathfinding:registerPathType("stone_path", 0.7)
    end
    
    -- Register with map system
    if world.systems and world.systems.mapping then
        world.systems.mapping:registerPathType("stone_path")
    end
    
    -- Register magical paths with magic system
    if world.systems and world.systems.magic then
        world.systems.magic:registerMagicPathType("stone_path")
    end
    
    -- Register path naming scheme
    if world.systems and world.systems.naming then
        world.systems.naming:registerPathNaming("stone_path", {
            prefixes = {"Pilgrim's", "Ancient", "Sacred", "Old", "Hidden", "Garden", "Temple"},
            suffixes = {"Path", "Way", "Walk", "Stones", "Trail"},
            themes = {"pilgrim", "garden", "temple", "ancient"}
        })
    end
end

return StonePathTile