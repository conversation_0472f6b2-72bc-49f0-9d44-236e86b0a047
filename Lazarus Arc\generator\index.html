<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lazarus Arc Sprite Generator</title>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #e74c3c;
            --text-color: #ecf0f1;
            --border-color: #7f8c8d;
            --canvas-bg: #1a1a1a;
        }

        body {
            margin: 0;
            padding: 20px;
            font-family: 'Arial', sans-serif;
            background-color: var(--primary-color);
            color: var(--text-color);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .tab {
            padding: 10px 20px;
            background-color: var(--secondary-color);
            border: none;
            color: var(--text-color);
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .tab:hover {
            background-color: var(--accent-color);
        }

        .tab.active {
            background-color: var(--accent-color);
        }

        .content {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
        }

        .sidebar {
            background-color: var(--secondary-color);
            padding: 20px;
            border-radius: 4px;
        }

        .main-content {
            background-color: var(--secondary-color);
            padding: 20px;
            border-radius: 4px;
        }

        .canvas-container {
            width: 100%;
            height: 400px;
            background-color: var(--canvas-bg);
            border: 2px solid var(--border-color);
            border-radius: 4px;
            margin-bottom: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .canvas {
            image-rendering: pixelated;
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .controls {
            display: grid;
            gap: 15px;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .control-group label {
            font-weight: bold;
        }

        .palette-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 5px;
        }

        .palette-color {
            width: 30px;
            height: 30px;
            border: 2px solid var(--border-color);
            border-radius: 4px;
            cursor: pointer;
        }

        .animation-templates {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 10px;
            margin-top: 20px;
        }

        .template {
            padding: 10px;
            background-color: var(--primary-color);
            border-radius: 4px;
            cursor: pointer;
            text-align: center;
        }

        .template:hover {
            background-color: var(--accent-color);
        }

        .shiny-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .shiny-toggle {
            width: 40px;
            height: 20px;
            background-color: var(--border-color);
            border-radius: 10px;
            position: relative;
            cursor: pointer;
        }

        .shiny-toggle.active {
            background-color: var(--accent-color);
        }

        .shiny-toggle::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            background-color: var(--text-color);
            border-radius: 50%;
            top: 2px;
            left: 2px;
            transition: transform 0.3s;
        }

        .shiny-toggle.active::after {
            transform: translateX(20px);
        }

        /* Add new styles for weapon controls */
        .weapon-controls {
            display: none;
        }

        .weapon-controls.active {
            display: block;
        }

        .weapon-type-selector {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }

        .weapon-type {
            padding: 8px;
            background-color: var(--primary-color);
            border-radius: 4px;
            cursor: pointer;
            text-align: center;
        }

        .weapon-type:hover {
            background-color: var(--accent-color);
        }

        .weapon-type.active {
            background-color: var(--accent-color);
        }

        .weapon-style-selector {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }

        .weapon-style {
            padding: 8px;
            background-color: var(--primary-color);
            border-radius: 4px;
            cursor: pointer;
            text-align: center;
        }

        .weapon-style:hover {
            background-color: var(--accent-color);
        }

        .weapon-style.active {
            background-color: var(--accent-color);
        }

        /* Add preview grid for tiles */
        .tile-preview-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 5px;
            margin-bottom: 15px;
        }

        .tile-preview {
            width: 32px;
            height: 32px;
            border: 1px solid var(--border-color);
            cursor: pointer;
        }

        /* Add character customization options */
        .character-controls {
            display: none;
        }

        .character-controls.active {
            display: block;
        }

        .body-part-selector {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }

        .body-part {
            padding: 8px;
            background-color: var(--primary-color);
            border-radius: 4px;
            cursor: pointer;
            text-align: center;
        }

        .body-part:hover {
            background-color: var(--accent-color);
        }

        .body-part.active {
            background-color: var(--accent-color);
        }

        /* Add styles for tile controls */
        .tile-controls {
            display: none;
        }

        .tile-controls.active {
            display: block;
        }

        .tile-type-selector,
        .edge-style-selector,
        .variation-selector {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }

        .tile-type,
        .edge-style,
        .variation {
            padding: 8px;
            background-color: var(--primary-color);
            border-radius: 4px;
            cursor: pointer;
            text-align: center;
            transition: background-color 0.3s;
        }

        .tile-type:hover,
        .edge-style:hover,
        .variation:hover {
            background-color: var(--accent-color);
        }

        .tile-type.active,
        .edge-style.active,
        .variation.active {
            background-color: var(--accent-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Sprite Generator</h1>

        <!-- Tabs -->
        <div class="tabs">
            <div class="tab active" data-tab="weapons">Weapons</div>
            <div class="tab" data-tab="characters">Characters</div>
            <div class="tab" data-tab="tiles">Tiles</div>
        </div>

        <div class="generator-content">
            <!-- Canvas -->
            <div class="canvas-container">
                <canvas id="spriteCanvas" class="canvas"></canvas>
            </div>

            <!-- Controls -->
            <div class="controls">
                <!-- Common Controls -->
                <div class="control-group">
                    <label for="spriteSize">Size:</label>
                    <select id="spriteSize">
                        <option value="16">16x16</option>
                        <option value="32" selected>32x32</option>
                        <option value="64">64x64</option>
                        <option value="128">128x128</option>
                    </select>
                </div>
                <div class="control-group">
                    <label for="paletteSelect">Palette:</label>
                    <select id="paletteSelect">
                        <!-- Palette options will be added here -->
                    </select>
                </div>
                <button id="saveSprite">Save Sprite</button>
                <button id="exportAnimation">Export Animation</button>
                <button id="clearCanvas">Clear</button>

                <!-- Weapon Controls -->
                <div class="weapon-controls active">
                    <div class="control-group">
                        <label>Weapon Type</label>
                        <div class="weapon-type-selector">
                            <div class="weapon-type active" data-type="sword">Sword</div>
                            <div class="weapon-type" data-type="axe">Axe</div>
                            <div class="weapon-type" data-type="mace">Mace</div>
                            <div class="weapon-type" data-type="spear">Spear</div>
                        </div>
                    </div>
                    <div class="control-group">
                        <label>Style</label>
                        <div class="weapon-style-selector">
                            <div class="weapon-style active" data-style="basic">Basic</div>
                            <!-- Add more styles here -->
                        </div>
                    </div>
                </div>

                <!-- Character Controls -->
                <div class="character-controls">
                    <div class="control-group">
                        <label>Part</label>
                        <div class="body-part-selector">
                            <div class="body-part active" data-part="head">Head</div>
                            <div class="body-part" data-part="body">Body</div>
                            <div class="body-part" data-part="arms">Arms</div>
                            <div class="body-part" data-part="legs">Legs</div>
                        </div>
                    </div>
                    <!-- Add more character controls here -->
                </div>

                <!-- Tile Controls -->
                <div class="tile-controls">
                    <div class="control-group">
                        <label>Tile Type</label>
                        <div class="tile-type-selector">
                            <div class="tile-type active" data-type="grass">Grass</div>
                            <div class="tile-type" data-type="stone">Stone</div>
                            <div class="tile-type" data-type="water">Water</div>
                            <div class="tile-type" data-type="sand">Sand</div>
                            <div class="tile-type" data-type="dirt">Dirt</div>
                            <div class="tile-type" data-type="wood">Wood</div>
                        </div>
                    </div>
                    <div class="control-group">
                        <label>Edge Style</label>
                        <div class="edge-style-selector">
                            <div class="edge-style active" data-style="none">None</div>
                            <div class="edge-style" data-style="raised">Raised</div>
                            <div class="edge-style" data-style="sunken">Sunken</div>
                            <div class="edge-style" data-style="gradient">Gradient</div>
                        </div>
                    </div>
                    <div class="control-group">
                        <label>Variation</label>
                        <div class="variation-selector">
                            <div class="variation active" data-variation="0">None</div>
                            <div class="variation" data-variation="1">Spots</div>
                            <div class="variation" data-variation="2">Pattern</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import SpriteGenerator from './spriteGenerator.js';
        import WeaponGenerator from './weaponGenerator.js';
        import CharacterGenerator from './characterGenerator.js';
        import TileGenerator from './tileGenerator.js';

        document.addEventListener('DOMContentLoaded', () => {
            // Initialize canvas and generators
            const canvas = document.getElementById('spriteCanvas');
            let currentGenerator;
            let currentSize = 32;

            // Function to initialize or switch generator
            function initializeGenerator(type) {
                switch (type) {
                    case 'weapons':
                        currentGenerator = new WeaponGenerator(canvas);
                        break;
                    case 'characters':
                        currentGenerator = new CharacterGenerator(canvas);
                        break;
                    case 'tiles':
                        currentGenerator = new TileGenerator(canvas);
                        break;
                    default:
                        currentGenerator = new WeaponGenerator(canvas); // Default to weapons
                }
                updateCanvasSize(); // Initialize size and draw
            }

            // Set initial canvas size and draw
            function updateCanvasSize() {
                if (!currentGenerator) return; // Don't run if generator not ready
                canvas.width = currentSize;
                canvas.height = currentSize;
                currentGenerator.setSize(currentSize);
                currentGenerator.drawBaseShape();
            }

            // Initial setup
            initializeGenerator('weapons'); // Start with weapon generator

            // --- Event Listeners --- 

            // Handle sprite size changes
            document.getElementById('spriteSize').addEventListener('change', (e) => {
                currentSize = parseInt(e.target.value);
                updateCanvasSize();
            });

            // Tab switching
            document.querySelectorAll('.tab').forEach(tab => {
                tab.addEventListener('click', () => {
                    document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');

                    // Hide all control panels
                    document.querySelectorAll('.weapon-controls, .character-controls, .tile-controls')
                        .forEach(panel => panel.classList.remove('active'));

                    // Show appropriate control panel and initialize generator
                    const tabName = tab.dataset.tab;
                    const controlPanel = document.querySelector(`.${tabName}-controls`);
                    if (controlPanel) {
                        controlPanel.classList.add('active');
                    }
                    initializeGenerator(tabName);
                });
            });

            // Weapon type selection
            document.querySelectorAll('.weapon-type').forEach(type => {
                type.addEventListener('click', () => {
                    if (!(currentGenerator instanceof WeaponGenerator)) return;
                    document.querySelectorAll('.weapon-type').forEach(t => t.classList.remove('active'));
                    type.classList.add('active');
                    currentGenerator.setWeaponType(type.dataset.type);
                });
            });
            
            // Weapon style selection
            document.querySelectorAll('.weapon-style').forEach(style => {
                style.addEventListener('click', () => {
                     if (!(currentGenerator instanceof WeaponGenerator)) return;
                    document.querySelectorAll('.weapon-style').forEach(s => s.classList.remove('active'));
                    style.classList.add('active');
                    currentGenerator.setWeaponStyle(style.dataset.style);
                });
            });

            // Character part selection
            document.querySelectorAll('.body-part').forEach(part => {
                part.addEventListener('click', () => {
                    if (!(currentGenerator instanceof CharacterGenerator)) return;
                    document.querySelectorAll('.body-part').forEach(p => p.classList.remove('active'));
                    part.classList.add('active');
                    currentGenerator.setCurrentPart(part.dataset.part);
                });
            });

            // Tile type selection
            document.querySelectorAll('.tile-type').forEach(type => {
                type.addEventListener('click', () => {
                    if (!(currentGenerator instanceof TileGenerator)) return;
                    document.querySelectorAll('.tile-type').forEach(t => t.classList.remove('active'));
                    type.classList.add('active');
                    currentGenerator.setTileType(type.dataset.type);
                });
            });

            // Edge style selection
            document.querySelectorAll('.edge-style').forEach(style => {
                style.addEventListener('click', () => {
                    if (!(currentGenerator instanceof TileGenerator)) return;
                    document.querySelectorAll('.edge-style').forEach(s => s.classList.remove('active'));
                    style.classList.add('active');
                    currentGenerator.setEdgeStyle(style.dataset.style);
                });
            });

            // Variation selection
            document.querySelectorAll('.variation').forEach(variation => {
                variation.addEventListener('click', () => {
                    if (!(currentGenerator instanceof TileGenerator)) return;
                    document.querySelectorAll('.variation').forEach(v => v.classList.remove('active'));
                    variation.classList.add('active');
                    currentGenerator.setVariation(parseInt(variation.dataset.variation));
                });
            });
            
            // --- Placeholder for other controls like Palette, Save, etc. ---
            document.getElementById('clearCanvas').addEventListener('click', () => {
                 if (currentGenerator) {
                    currentGenerator.clear();
                 }
            });

            document.getElementById('saveSprite').addEventListener('click', () => {
                if (currentGenerator) {
                    const dataURL = currentGenerator.saveSprite();
                    const link = document.createElement('a');
                    link.href = dataURL;
                    link.download = `${currentGenerator.constructor.name.replace('Generator','')}_sprite.png`;
                    link.click();
                }
            });
            
        });
    </script>
</body>
</html> 