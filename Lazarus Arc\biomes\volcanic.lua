-- biomes/volcanic.lua
-- Declare VolcanicBiome at the top level so it can be referenced from within functions
local VolcanicBiome = {}

-- Define the biome properties
VolcanicBiome = {
    id = "volcanic",
    name = "Volcanic",
    description = "A harsh and barren landscape with active volcanoes and lava flows.", -- [cite: 696]

    -- Environmental factors (Hot, dry, poor air quality)
    environment = {
        humidity = 0.1,    -- Very dry
        temperature = 0.9, -- Represents very hot
        sunlight = 0.7,    -- Can be obscured by ash
        windStrength = 0.4,
        airQuality = 0.3   -- Poor due to sulfur/ash (0=bad, 1=good)
    },

    -- Core tile types used in this biome
    -- NOTE: 'volcanic_rock', 'lava', 'ash', 'obsidian', 'volcanic_vent' need defining.
    primaryTiles = {"volcanic_rock", "ash"},
    secondaryTiles = {"obsidian", "lava"}, -- Lava often flows or pools, not just static ground cover
    rareTiles = {"volcanic_vent", "metal_deposit_hot"}, -- Added metal deposit possibility

    -- Default proportions (adjustable through variants)
    tileDistribution = {
        volcanic_rock = 0.6,
        ash = 0.25,
        obsidian = 0.05,
        lava = 0.07, -- Represents active flows/pools
        volcanic_vent = 0.02,
        metal_deposit_hot = 0.01
    },

    -- Entities common to this biome
    commonEntities = {
        "fire_elemental_minor", "volcanic_bat", "ash_sprite", "lava_lizard" -- Added lava lizard
    },
    uncommonEntities = {
        "lava_creature", "fire_elemental", "obsidian_golem" -- Added golem
    },
    rareEntities = {
        "phoenix", "magma_dragon", "ancient_fire_guardian" -- Added dragon/guardian
    },

    -- Biome variants for diverse generation
    variants = {
        active_caldera = {
            name = "Active Caldera",
            temperature = 1.0, -- Hottest
            airQuality = 0.1,
            tileAdjustments = {
                volcanic_rock = 0.4,
                lava = 0.35, -- Much more lava
                volcanic_vent = 0.1,
                ash = 0.1
            },
            entityAdjustments = {
                 fire_elemental = 1.8,
                 lava_creature = 1.5,
                 phoenix = 1.2
            },
            specialEffects = {"constant_tremors", "toxic_fumes", "lava_eruptions"}
        },
        ash_wastes = {
            name = "Ash Wastes",
            temperature = 0.7,
            airQuality = 0.2,
            windStrength = 0.6,
            tileAdjustments = {
                ash = 0.8, -- Primarily ash
                volcanic_rock = 0.15,
                lava = 0.01 -- Very little lava
            },
            entityAdjustments = {
                 ash_sprite = 2.0,
                 volcanic_bat = 1.3,
                 fire_elemental = 0.5
            },
            specialEffects = {"ash_clouds", "low_visibility", "brittle_ground"}
        },
        obsidian_fields = {
            name = "Obsidian Fields",
            temperature = 0.8,
            tileAdjustments = {
                obsidian = 0.6, -- Primarily obsidian
                volcanic_rock = 0.3,
                ash = 0.05,
                lava = 0.05
            },
            entityAdjustments = {
                 obsidian_golem = 1.5,
                 fire_elemental = 1.0,
                 lava_lizard = 0.8
            },
            specialEffects = {"sharp_edges_hazard", "reflective_surfaces", "heat_shimmer"}
        },
        geothermal_vents = {
             name = "Geothermal Vents",
             temperature = 0.85,
             humidity = 0.4, -- More steam/humidity near vents
             tileAdjustments = {
                 volcanic_rock = 0.5,
                 volcanic_vent = 0.2, -- More vents
                 ash = 0.1,
                 hot_spring = 0.1, -- Added hot spring possibility
                 sulfur_deposit = 0.1 -- Added sulfur possibility
             },
             entityAdjustments = {
                 steam_elemental = 1.5, -- Added steam elemental
                 lava_creature = 0.7,
                 fire_elemental = 0.8
             },
             specialEffects = {"steam_vents", "sulfur_smell", "geyser_eruptions"}
        }
    },

    -- Structures that can generate in this biome
    structures = {
        {
            name = "fire_temple",
            chance = 0.04,
            requiresNearby = "lava", -- Near lava flows/lakes
            entities = {"fire_priest", "magma_guardian", "elemental_altar"}
        },
        {
            name = "abandoned_mine", -- Mining rare volcanic metals/gems
            chance = 0.07,
            entities = {"lava_crawler_nest", "burnt_remains", "rich_ore_vein"}
        },
        {
            name = "research_outpost", -- Studying volcanism/geothermal energy
            chance = 0.03,
            requiresVariant = "geothermal_vents", -- More likely near vents
            entities = {"geologist", "heat_resistant_drone", "sensor_equipment"}
        },
        {
            name = "ancient_forge",
            chance = 0.02,
            unique = true, -- Probably unique
            requiresNearby = "lava", -- Needs heat source
            entities = {"forge_master_spirit", "fire_golem_inactive", "masterwork_weapon_fragment"}
        }
    },

    -- Weather patterns (adapted for volcanic environment)
    weather = {
        transitions = {
             clear_hazy = { clear_hazy = 60, ashfall_light = 30, acid_rain = 5, wind_ash = 5 },
             ashfall_light = { clear_hazy = 40, ashfall_light = 40, ashfall_heavy = 20 },
             ashfall_heavy = { ashfall_light = 70, ashfall_heavy = 30 },
             acid_rain = { clear_hazy = 60, ashfall_light = 40 }, -- Corrosive but usually short-lived
             wind_ash = { clear_hazy = 50, ashfall_light = 30, wind_ash = 20 }
        },
        default = "clear_hazy" -- Base state is often hazy from heat/gases
    },

    -- Unique features generation
    features = {
        {
            name = "volcano", -- The central volcano, likely a large multi-chunk feature/structure
            chance = 0.01, -- Chance per region, not per chunk
            unique = true
        },
        {
            name = "lava_lake",
            chance = 0.08,
            size = {min=8, max=25} -- Places 'lava' tiles
        },
        {
            name = "lava_flow", -- Path of lava tiles
            chance = 0.1,
            length = {min=10, max=30}
        },
        {
            name = "volcanic_geyser", -- Places 'volcanic_vent' tile or entity
            chance = 0.12,
            requiresVariant = "geothermal_vents"
        },
        {
            name = "obsidian_pillar", -- Large obsidian structure/entity
            chance = 0.06,
            requiresVariant = "obsidian_fields"
        }
    },

    -- Generation algorithm (Structure based on forest/plains)
    generate = function(chunk, world, variant)
        print("Generating " .. (variant and VolcanicBiome.variants[variant].name or "Volcanic") .. " biome")
        local variantData = variant and VolcanicBiome.variants[variant] or nil
        local temp = variantData and variantData.temperature or VolcanicBiome.environment.temperature
        local airQuality = variantData and variantData.airQuality or VolcanicBiome.environment.airQuality

        -- Adjust tile distribution
        local tileDistribution = {}
        for tileType, chance in pairs(VolcanicBiome.tileDistribution) do
            tileDistribution[tileType] = chance
            if variantData and variantData.tileAdjustments and variantData.tileAdjustments[tileType] then
                tileDistribution[tileType] = variantData.tileAdjustments[tileType]
            end
        end

        -- Noise functions
        local seed = world.seed + (chunk.x * 617 + chunk.y * 311)
        math.randomseed(seed)
        local function pnoise2D(x, y, frequency, seed_offset) return love.math.noise(x * frequency, y * frequency, (world.seed + (seed_offset or 0)) * 0.01) end

        -- Initialize 2D tiles array if not exists
        chunk.tiles = chunk.tiles or {}
        for x = 0, world.CHUNK_SIZE - 1 do
            chunk.tiles[x] = chunk.tiles[x] or {}
        end

        -- Generate tiles
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                local worldX = chunk.x * world.CHUNK_SIZE + x
                local worldY = chunk.y * world.CHUNK_SIZE + y

                local elevationNoise = pnoise2D(worldX, worldY, 0.06, 1) -- Controls general elevation, lava flows
                local heatNoise = pnoise2D(worldX, worldY, 0.12, 2) -- Controls proximity to heat/lava
                local materialNoise = pnoise2D(worldX, worldY, 0.2, 3) -- Controls rock vs ash vs obsidian

                -- Determine tile type
                local tileType = "volcanic_rock" -- Default
                if materialNoise < -0.3 then
                    tileType = "ash"
                elseif materialNoise > 0.6 then
                    tileType = "obsidian"
                end

                -- Lava in low elevation or high heat areas
                if elevationNoise < -0.5 or heatNoise > 0.7 then
                    tileType = "lava"
                end

                 -- Vents in high heat, moderate elevation
                 if heatNoise > 0.6 and elevationNoise > 0.0 and elevationNoise < 0.5 and math.random() < 0.1 then
                      tileType = "volcanic_vent"
                 end

                -- Apply variant specific logic (e.g., more lava in caldera, more ash in wastes)
                if variant == "active_caldera" and heatNoise > 0.4 then
                    if math.random() < 0.4 then tileType = "lava" end
                elseif variant == "ash_wastes" and math.random() < 0.6 then
                     tileType = "ash"
                elseif variant == "obsidian_fields" and materialNoise > 0.3 then
                     tileType = "obsidian"
                end

                -- Use distribution table for rarer tiles based on roll (simplified)
                --[[ local tileRoll = math.random()
                local cumulativeChance = 0
                for tType, chance in pairs(tileDistribution) do ... end ]]-- (More complex integration needed)


                -- Create the tile
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = math.random(1, 3),
                    temperature = temp * 200 + heatNoise * 150, -- Example temp calculation
                    airQuality = airQuality - heatNoise * 0.2, -- Worse air near heat
                    isUnderground = false
                }

                 -- Apply specific properties
                 if tileType == "lava" then tile.passable = false; tile.damage = 10; end
                 if tileType == "ash" then tile.movementSpeed = 0.8; end
                 if tileType == "obsidian" then tile.sharp = true; end -- Maybe causes damage on walk?
                 if tileType == "volcanic_vent" then tile.passable = false; tile.emits = "steam/gas"; end

                chunk.tiles[x][y] = tile
            end
        end

        -- Call helper functions
        VolcanicBiome.generateFeatures(chunk, world, variant)
        VolcanicBiome.populateEntities(chunk, world, variant)
        VolcanicBiome.generateStructures(chunk, world, variant)
        VolcanicBiome.postProcess(chunk, world, variant)
    end,

    -- Helper function implementations (Placeholders - need detailed logic similar to previous examples)
    generateFeatures = function(chunk, world, variant)
        print("Generating Volcanic features...")
        -- Logic to place volcano structure (multi-chunk?), lava lakes/flows, geysers, obsidian pillars
        -- Example: Placing Lava Lake
        for _, feature in ipairs(VolcanicBiome.features) do
             if feature.name == "lava_lake" and math.random() < feature.chance then
                 local radius = math.random(feature.size.min, feature.size.max) / 2
                 local fx = math.random(radius, world.CHUNK_SIZE - 1 - radius)
                 local fy = math.random(radius, world.CHUNK_SIZE - 1 - radius)
                 print("Placing lava lake at " .. fx .. "," .. fy)
                 for dx = -radius, radius do for dy = -radius, radius do
                      if math.sqrt(dx*dx + dy*dy) <= radius then
                           local tx, ty = fx+dx, fy+dy
                           if tx >= 0 and tx < world.CHUNK_SIZE and ty >= 0 and ty < world.CHUNK_SIZE then
                                chunk.tiles[tx][ty].type = "lava"
                           end
                      end
                 end end
                 -- Mark unique if needed
                 if feature.unique then chunk.uniqueFeatures = chunk.uniqueFeatures or {}; chunk.uniqueFeatures[feature.name] = true end
                 break -- Place only one per chunk maybe? Or handle uniqueness differently.
             end
             -- Add logic for other features
        end
    end,

    populateEntities = function(chunk, world, variant)
        print("Populating Volcanic entities...")
        local variantData = variant and VolcanicBiome.variants[variant] or nil
        local function spawnEntity(entityType, count)
            local multiplier = 1.0
            if variantData and variantData.entityAdjustments and variantData.entityAdjustments[entityType] then multiplier = variantData.entityAdjustments[entityType] end
            local adjustedCount = math.floor(count * multiplier + 0.5)
            for i = 1, adjustedCount do
                 local attempts = 0; local placed = false
                 while attempts < 5 and not placed do
                      local x = math.random(0, world.CHUNK_SIZE - 1)
                      local y = math.random(0, world.CHUNK_SIZE - 1)
                      local tile = chunk.tiles[x][y]
                      -- Check if tile is suitable (avoid deep lava unless entity can handle it)
                      if tile and tile.type ~= "lava" then -- Simplified check
                           world.entitySystem:addEntity(entityType, chunk.x * world.CHUNK_SIZE + x, chunk.y * world.CHUNK_SIZE + y)
                           placed = true
                      end
                      attempts = attempts + 1
                 end
            end
        end
        -- Spawn based on lists
        for _, et in ipairs(VolcanicBiome.commonEntities) do spawnEntity(et, math.random(1,4)) end
        for _, et in ipairs(VolcanicBiome.uncommonEntities) do spawnEntity(et, math.random(0,2)) end
        for _, et in ipairs(VolcanicBiome.rareEntities) do if math.random() < 0.1 then spawnEntity(et, 1) end end
        -- Add variant specifics
    end,

    generateStructures = function(chunk, world, variant)
        print("Generating Volcanic structures...")
        -- Similar logic to previous examples: Iterate structures list, check chance/variant/nearby reqs, find location, place structure & entities
         for _, structure in ipairs(VolcanicBiome.structures) do
             if math.random() < structure.chance then
                 if not structure.requiresVariant or structure.requiresVariant == variant then
                      -- Check requiresNearby (e.g., lava) - requires more complex neighbor check
                      local placeStructure = true -- Assume true for now
                      if structure.requiresNearby == "lava" then
                          -- Simple check: is there any lava in the chunk? Needs better proximity check.
                          local foundLava = false
                          for x = 0, world.CHUNK_SIZE - 1 do
                              for y = 0, world.CHUNK_SIZE - 1 do
                                  if chunk.tiles[x][y].type == "lava" then foundLava = true; break end
                              end
                              if foundLava then break end
                          end
                          if not foundLava then placeStructure = false end
                      end

                      if placeStructure then
                           -- Find suitable location (not on lava)
                            local attempts = 0; local placed = false; local sx, sy
                            while attempts < 10 and not placed do
                                 sx = math.random(2, world.CHUNK_SIZE - 3)
                                 sy = math.random(2, world.CHUNK_SIZE - 3)
                                 local tile = chunk.tiles[sx][sy]
                                 if tile and tile.type ~= "lava" and tile.type ~= "volcanic_vent" then placed = true end
                                 attempts = attempts + 1
                            end
                           if placed then
                               local worldX = chunk.x * world.CHUNK_SIZE + sx
                               local worldY = chunk.y * world.CHUNK_SIZE + sy
                               print("Placing structure: "..structure.name.." at "..worldX..","..worldY)
                               if world.chunkSystem and world.chunkSystem.placeStructure then
                                    world.chunkSystem:placeStructure(chunk, structure.name, worldX, worldY)
                               else
                                   world.entitySystem:addEntity("structure_marker", worldX, worldY, { structureType = structure.name })
                               end
                               if structure.entities then
                                    for _, et in ipairs(structure.entities) do
                                         world.entitySystem:addEntity(et, worldX + math.random(-1,1), worldY + math.random(-1,1))
                                    end
                               end
                           end
                      end
                 end
             end
         end
    end,

    postProcess = function(chunk, world, variant)
        print("Post-processing Volcanic chunk...")
        -- Apply heat, ash effects, etc.
        local variantData = variant and VolcanicBiome.variants[variant] or nil
        if variantData and variantData.specialEffects then
             chunk.environmentalEffects = chunk.environmentalEffects or {}
             for _, effect in ipairs(variantData.specialEffects) do table.insert(chunk.environmentalEffects, effect) end
        end
         -- Apply persistent heat damage or air quality effects?
    end,

    init = function(worldCore)
        print("Volcanic biome module initialized")
        VolcanicBiome.worldCore = worldCore
        print("Volcanic biome registered successfully for later use")
    end,

    registerWithWorld = function(world)
        print("Registering Volcanic biome generators with world")
        if not world or not world.chunkSystem then print("Warning: Cannot register Volcanic biome - world or chunkSystem is nil") return false end
        world.chunkSystem:registerGenerator(VolcanicBiome.id, VolcanicBiome.generate)
        for variantId, variantData in pairs(VolcanicBiome.variants) do
            local fullVariantId = VolcanicBiome.id .. "_" .. variantId
            world.chunkSystem:registerGenerator(fullVariantId, function(chunk, worldInstance)
                VolcanicBiome.generate(chunk, worldInstance, variantId)
                -- VolcanicBiome.postProcess(chunk, worldInstance, variantId) -- Called within generate now
            end)
        end
        return true
    end,
}

return VolcanicBiome