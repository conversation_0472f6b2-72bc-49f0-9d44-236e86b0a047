-- weather/foggy.lua
-- Foggy weather pattern - reduced visibility with atmospheric effects

local FoggyWeather = {
    id = "foggy",
    name = "Fog",

    -- Visual properties
    visual = {
        skyColor = {r = 150, g = 150, b = 170}, -- Gray-white sky
        sunIntensity = 0.4,
        cloudCoverage = 0.8,
        cloudColor = {r = 180, g = 180, b = 200}, -- Light gray clouds
        ambientLightLevel = 0.5,
        fogColor = {r = 200, g = 200, b = 220} -- Light blue-white fog
    },

    -- Environment modifiers
    environment = {
        temperature = 0.95, -- Slightly cooler
        visibility = 0.3, -- Poor visibility
        humidity = 0.8, -- High humidity
        windStrength = 0.2, -- Very light wind
        fogDensity = 0.7, -- Dense fog
        moistureLevel = 0.7, -- High moisture
        condensationRate = 0.6 -- High condensation
    },

    -- Particle systems
    particles = {
        fog = {
            enabled = true,
            intensity = 0.7,
            color = {r = 200, g = 200, b = 220},
            size = {min = 2, max = 4},
            speed = {min = 10, max = 20},
            image = "assets/particles/Complex/smoke/smoke_1.png",
            blendMode = "normal",
            lifetime = {min = 2.0, max = 3.0},
            rotation = {min = 0, max = 360},
            alpha = {min = 0.3, max = 0.6}
        },
        mist = {
            enabled = true,
            intensity = 0.4,
            color = {r = 180, g = 180, b = 200},
            size = {min = 1, max = 3},
            speed = {min = 5, max = 15},
            image = "assets/particles/Complex/smoke/smoke_2.png",
            blendMode = "normal",
            lifetime = {min = 1.5, max = 2.5},
            rotation = {min = 0, max = 360},
            alpha = {min = 0.2, max = 0.4}
        },
        haze = {
            enabled = true,
            intensity = 0.3,
            color = {r = 160, g = 160, b = 180},
            size = {min = 3, max = 6},
            speed = {min = 0, max = 0},
            image = "assets/particles/Complex/circle/circle_1.png",
            blendMode = "normal",
            lifetime = {min = 1.0, max = 2.0},
            rotation = {min = 0, max = 360},
            alpha = {min = 0.1, max = 0.3}
        }
    },

    -- Sound effects
    sounds = {
        ambient = "fog_ambient",
        wind = "light_wind",
        moisture = "fog_moisture",
        volume = 0.3
    },

    -- Synth orchestra sound configurations
    synthSounds = {
        fog_ambient = {
            notes = {146.8, 174.6, 220}, -- D3, F3, A3 - mysterious, muffled tones
            durations = {3.5, 3.5, 3.5},
            instrument = "soft_pad",
            volume = 0.18,
            vibrato = true,
            vibratoRate = 0.15,
            vibratoDepth = 0.08
        },
        light_wind = {
            notes = {293.7, 220, 164.8}, -- D4, A3, E3 - gentle, muffled wind
            durations = {4.0, 4.0, 4.0},
            instrument = "flute",
            volume = 0.15,
            vibrato = true,
            vibratoRate = 0.2,
            vibratoDepth = 0.12
        },
        fog_moisture = {
            notes = {523, 659, 784}, -- C5, E5, G5 - subtle water droplet sounds
            durations = {0.3, 0.3, 0.3},
            instrument = "kalimba",
            volume = 0.1,
            vibrato = false
        }
    },

    -- Effects on game entities and tiles
    effects = {
        -- Movement effects
        movementSpeedMultiplier = 0.8, -- Reduced speed

        -- Vision effects
        visionRange = 0.3, -- Severely reduced vision

        -- Temperature effects
        temperatureModifier = 0.95, -- Slightly cooler temperature

        -- Energy effects
        energyDrainRate = 1.2, -- Increased energy drain

        -- Shield effects
        shieldEffectiveness = 0.9, -- Reduced shield effectiveness

        -- Sensor effects
        sensorRange = 0.4, -- Reduced sensor range

        -- Special effects
        slipChance = 0.1, -- Increased chance to slip on wet surfaces
        moistureDamage = 0.05 -- Damage from moisture exposure
    },

    -- Transition probabilities to other weather (per game hour)
    transitions = {
        clear = 0.2,
        cloudy = 0.3,
        light_rain = 0.1,
        -- Implied: 0.4 chance to stay foggy
    },

    -- Day/night cycle modifiers
    timeModifiers = {
        dawn = {
            skyColor = {r = 160, g = 160, b = 180},
            ambientLightLevel = 0.3,
            fogDensity = 0.8 -- Denser fog at dawn
        },
        day = {
            -- Uses default properties
        },
        dusk = {
            skyColor = {r = 140, g = 140, b = 160},
            ambientLightLevel = 0.2,
            fogDensity = 0.8 -- Denser fog at dusk
        },
        night = {
            skyColor = {r = 120, g = 120, b = 140},
            ambientLightLevel = 0.1,
            fogDensity = 0.9 -- Very dense fog at night
        }
    }
}

-- Helper function to safely set particle system properties
local function setupParticleSystem(ps, config)
    if not ps or type(ps) ~= "table" then
        print("Warning: Invalid particle system")
        return false
    end

    local methods = {
        setParticleLifetime = function() return ps:setParticleLifetime(config.lifetime.min, config.lifetime.max) end,
        setEmissionRate = function() return ps:setEmissionRate(config.emissionRate or 0) end,
        setSizeVariation = function() return ps:setSizeVariation(config.sizeVariation or 0.3) end,
        setLinearAcceleration = function() return ps:setLinearAcceleration(config.acceleration.x1 or 0, config.acceleration.y1 or 0, config.acceleration.x2 or 0, config.acceleration.y2 or 0) end,
        setColors = function() return ps:setColors(config.colors.r1 or 255, config.colors.g1 or 255, config.colors.b1 or 255, config.colors.a1 or 255, config.colors.r2 or 255, config.colors.g2 or 255, config.colors.b2 or 255, config.colors.a2 or 0) end,
        setBlendMode = function() return ps:setBlendMode(config.blendMode or "normal") end
    }

    for methodName, method in pairs(methods) do
        if type(ps[methodName]) ~= "function" then
            print("Warning: Particle system missing method: " .. methodName)
            return false
        end

        local success, err = pcall(method)
        if not success then
            print("Warning: Failed to call " .. methodName .. ": " .. tostring(err))
            return false
        end
    end

    return true
end

-- Initialize the weather pattern
function FoggyWeather.init(world)
    if not world then
        print("Warning: world parameter is nil in FoggyWeather.init")
        return
    end

    print("Initializing foggy weather")

    -- Set global light level
    world.lightLevel = FoggyWeather.visual.ambientLightLevel

    -- Start ambient sounds if weather system exists
    if world.weatherSystem then
        if FoggyWeather.sounds.ambient then
            world.weatherSystem:playSound(FoggyWeather.sounds.ambient, {
                verbose = false,  -- Don't print warnings if sound is missing
                loop = true,      -- Ambient sounds should loop
                volume = 0.5      -- Lower volume for ambient weather
            })
        end
    end

    -- Create particle systems
    if FoggyWeather.particles.fog.enabled then
        -- Create fog particles
        print("Creating dense fog particles with intensity: " .. FoggyWeather.particles.fog.intensity)
        local success, image = pcall(love.graphics.newImage, FoggyWeather.particles.fog.image)
        if success and image then
            local success2, ps = pcall(love.graphics.newParticleSystem, image)
            if success2 and ps then
                local config = {
                    lifetime = FoggyWeather.particles.fog.lifetime,
                    emissionRate = 40,
                    sizeVariation = 0.4,
                    acceleration = {x1 = -2, y1 = -2, x2 = 2, y2 = 2},
                    colors = {r1 = 255, g1 = 255, b1 = 255, a1 = 255, r2 = 200, g2 = 200, b2 = 220, a2 = 0},
                    blendMode = "normal"
                }

                if setupParticleSystem(ps, config) then
                    FoggyWeather.fogSystem = ps
                else
                    print("Warning: Failed to setup fog particle system")
                end
            else
                print("Warning: Failed to create fog particle system")
            end
        else
            print("Warning: Could not load fog particle image: " .. FoggyWeather.particles.fog.image)
        end
    end

    if FoggyWeather.particles.mist.enabled then
        -- Create mist particles
        print("Creating mist particles with intensity: " .. FoggyWeather.particles.mist.intensity)
        local success, image = pcall(love.graphics.newImage, FoggyWeather.particles.mist.image)
        if success and image then
            local success2, ps = pcall(love.graphics.newParticleSystem, image)
            if success2 and ps then
                local config = {
                    lifetime = FoggyWeather.particles.mist.lifetime,
                    emissionRate = 20,
                    sizeVariation = 0.3,
                    acceleration = {x1 = -1, y1 = -1, x2 = 1, y2 = 1},
                    colors = {r1 = 255, g1 = 255, b1 = 255, a1 = 255, r2 = 180, g2 = 180, b2 = 200, a2 = 0},
                    blendMode = "normal"
                }

                if setupParticleSystem(ps, config) then
                    FoggyWeather.mistSystem = ps
                else
                    print("Warning: Failed to setup mist particle system")
                end
            else
                print("Warning: Failed to create mist particle system")
            end
        else
            print("Warning: Could not load mist particle image: " .. FoggyWeather.particles.mist.image)
        end
    end

    if FoggyWeather.particles.haze.enabled then
        -- Create haze particles
        print("Creating haze particles with intensity: " .. FoggyWeather.particles.haze.intensity)
        local success, image = pcall(love.graphics.newImage, FoggyWeather.particles.haze.image)
        if success and image then
            local success2, ps = pcall(love.graphics.newParticleSystem, image)
            if success2 and ps then
                local config = {
                    lifetime = FoggyWeather.particles.haze.lifetime,
                    emissionRate = 10,
                    sizeVariation = 0.5,
                    acceleration = {x1 = 0, y1 = 0, x2 = 0, y2 = 0},
                    colors = {r1 = 255, g1 = 255, b1 = 255, a1 = 255, r2 = 160, g2 = 160, b2 = 180, a2 = 0},
                    blendMode = "normal"
                }

                if setupParticleSystem(ps, config) then
                    FoggyWeather.hazeSystem = ps
                else
                    print("Warning: Failed to setup haze particle system")
                end
            else
                print("Warning: Failed to create haze particle system")
            end
        else
            print("Warning: Could not load haze particle image: " .. FoggyWeather.particles.haze.image)
        end
    end

    -- Display message to players if message system exists
    if world.messageSystem then
        world.messageSystem:broadcast("WARNING: Dense fog detected. Reduced visibility and hazardous conditions expected.")
    else
        print("WARNING: Dense fog detected. Reduced visibility and hazardous conditions expected.")
    end
end

-- Update function called every frame
function FoggyWeather.update(world, dt)
    if not world then
        print("Warning: world parameter is nil in FoggyWeather.update")
        return
    end

    -- Update day/night cycle based on world time
    local timeOfDay = world.timeOfDay or "day"
    local modifiers = FoggyWeather.timeModifiers[timeOfDay]

    if modifiers then
        -- Apply time-specific modifiers
        if modifiers.skyColor then
            -- Change sky color
        end

        if modifiers.ambientLightLevel then
            world.lightLevel = modifiers.ambientLightLevel
        end

        if modifiers.fogDensity then
            FoggyWeather.particles.fog.intensity = modifiers.fogDensity
            if FoggyWeather.fogSystem then
                FoggyWeather.fogSystem:setEmissionRate(40 * modifiers.fogDensity)
            end
        end
    end

    -- Update particle systems
    if FoggyWeather.fogSystem then
        FoggyWeather.fogSystem:update(dt)
    end
    if FoggyWeather.mistSystem then
        FoggyWeather.mistSystem:update(dt)
    end
    if FoggyWeather.hazeSystem then
        FoggyWeather.hazeSystem:update(dt)
    end

    -- Apply effects to game world if entity system exists
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            -- Apply movement speed reduction
            if entity.speed then
                entity.speed = entity.speed * FoggyWeather.effects.movementSpeedMultiplier
            end

            -- Apply energy drain
            if entity.energy then
                entity.energy = entity.energy -
                    (FoggyWeather.effects.energyDrainRate * dt)
            end

            -- Apply moisture damage
            if entity.health then
                entity.health = entity.health -
                    (FoggyWeather.effects.moistureDamage * dt)
            end
        end
    end

    -- Apply weather effects if weather system exists and has applyEffects method
    if world.weatherSystem then
        if type(world.weatherSystem.applyEffects) == "function" then
            world.weatherSystem:applyEffects(FoggyWeather.environment)
        elseif type(Engine) == "table" and Engine.systems and Engine.systems.weatherSystem and
               type(Engine.systems.weatherSystem.applyEffects) == "function" then
            -- Use global weather system as fallback
            Engine.systems.weatherSystem.applyEffects(FoggyWeather.environment)
        end
    end

    -- Random moisture effects
    if math.random() < 0.05 then
        -- Play moisture sound
        print("Playing sound: " .. FoggyWeather.sounds.moisture)
    end
end

-- Clean up when weather changes
function FoggyWeather.cleanUp(world)
    if not world then
        print("Warning: world parameter is nil in FoggyWeather.cleanUp")
        return
    end

    -- Stop sounds if weather system exists
    if world.weatherSystem then
        if FoggyWeather.sounds.ambient then
            world.weatherSystem:stopSound(FoggyWeather.sounds.ambient)
        end
        if FoggyWeather.sounds.wind then
            world.weatherSystem:stopSound(FoggyWeather.sounds.wind)
        end
    end

    -- Clean up particle systems
    if FoggyWeather.fogSystem then
        FoggyWeather.fogSystem:stop()
    end
    if FoggyWeather.mistSystem then
        FoggyWeather.mistSystem:stop()
    end
    if FoggyWeather.hazeSystem then
        FoggyWeather.hazeSystem:stop()
    end

    -- Notify players if message system exists
    if world.messageSystem then
        world.messageSystem:broadcast("Fog clearing. Visibility improving.")
    else
        print("Fog clearing. Visibility improving.")
    end

    print("Foggy weather ending")
end

-- Table contains helper function
function table.contains(table, element)
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

return FoggyWeather
