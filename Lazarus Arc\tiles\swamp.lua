-- tiles/swamp.lua
local SwampTile = {
    id = "swamp",
    name = "Swamp",
    passable = true, -- Passable, but extremely difficult and hazardous
    variants = 6, -- e.g., Murky Bog, Cypress Swamp, Mangrove Thicket, Reed Marsh, Sunken Forest, Fungal Mire

    -- Entities found in swamps
    spawns = {"giant_mosquito", "swamp_snake", "alligator", "leech_swarm", "will_o_wisp", "strange_glowing_fungus", "submerged_log", "bog_mummy"}, -- Dangerous creatures and hazards

    -- Movement properties
    movementSpeed = 0.35, -- Extremely slow due to water, mud, and vegetation

    -- Swamp-specific properties
    waterDepth = 0.6, -- Average depth of standing water (0-1, variable within tile)
    mudDepth = 0.7, -- Average depth of underlying mud (0-1+)
    vegetationDensity = 0.8, -- Very dense vegetation
    diseaseRisk = 0.5, -- Chance per hour/turn of contracting swamp fever/disease (0-1)
    fogLevel = 0.4, -- Base fog level, often reducing visibility (0-1)
    isStagnant = true, -- Water is generally not flowing

    -- Interaction function
    interact = function(tile, entity)
        -- Search the murky water/mud (risky)
        if math.random() < 0.15 then
             -- High risk of negative outcomes
             if math.random() < 0.3 then -- Negative outcome chance
                 local hazards = {
                     {msg="You disturb a nest of swamp snakes!", effect={type="spawn_hostile", id="swamp_snake_swarm", count=3}},
                     {msg="Your hand brushes against something sharp and hidden!", effect={type="damage", amount=math.random(2,5), damageType="piercing"}},
                     {msg="A swarm of biting insects rises from the disturbed water!", effect={type="apply_status", status="swarmed", duration=15}}
                 }
                 local hazard = hazards[math.random(#hazards)]
                 return { success=false, message=hazard.msg, effects={hazard.effect} }
             else -- Positive outcome chance (lower)
                 local swampFinds = {
                     {id="preserved_bog_iron", chance=0.4},
                     {id="glowing_swamp_plant", chance=0.3}, -- Alchemy ingredient?
                     {id="waterlogged_chest_small", chance=0.1}, -- Rare find
                     {id="ancient_ritual_dagger", chance=0.2}
                 }
                 for _, item in ipairs(swampFinds) do
                     if math.random() < item.chance then
                         return { success=true, message="Risking the murky depths, you retrieve a " .. item.id .. ".", effects={{type="add_item", item=item.id, quantity=1}} }
                     end
                 end
             end
        end

        -- Gather plants (specific swamp types)
        if entity.skills and entity.skills.herbalism > 1 and math.random() < 0.25 then
             local plants = { {id="marsh_mallow_root", chance=0.6}, {id="pitcher_plant", chance=0.4} }
             for _, plant in ipairs(plants) do
                 if math.random() < plant.chance then
                     return { success=true, message="You carefully gather some " .. plant.id .. ".", effects={{type="add_item", item=plant.id, quantity=math.random(1,2)}} }
                 end
             end
        end

        -- General examination
        local visibilityDesc = ""
        if tile.fogLevel > 0.5 then visibilityDesc = " A thick fog hangs low over the water, limiting visibility." end
        return {
            success = false,
            message = "A dense, humid swamp filled with stagnant water, mud, and tangled vegetation." .. visibilityDesc .. " The air is thick with the smell of decay and buzzing insects."
        }
    end,

    -- Weather effects
    weatherEffects = {
        rain = function(tile)
            tile.waterDepth = math.min(1.2, tile.waterDepth + 0.2) -- Water level rises
            tile.mudDepth = math.min(1.5, tile.mudDepth + 0.1)
            tile.diseaseRisk = tile.diseaseRisk + 0.1 -- Rain stirs things up
            tile.fogLevel = math.min(0.8, tile.fogLevel + 0.2)
            return "visual_effect", "heavy_rain_swamp"
        end,
        
        heat = function(tile) -- Can lower water slightly, but increases disease/insects
            tile.waterDepth = math.max(0.3, tile.waterDepth - 0.05)
            tile.diseaseRisk = tile.diseaseRisk + 0.2 -- Heat breeds disease
             -- Increase insect swarm spawn chance?
            tile.spawnModifier = { type="giant_mosquito", chanceMultiplier=1.5 }
            return "visual_effect", "heat_haze_swamp"
        end,

        sun = function(tile) -- Slow drying, less impact than direct heat
            tile.waterDepth = math.max(0.3, tile.waterDepth - 0.02)
            tile.fogLevel = math.max(0, tile.fogLevel - 0.1) -- Sun burns off fog
            tile.spawnModifier = nil
            return "remove_visual_effect", "heat_haze_swamp"
        end,

        freeze = function(tile) -- Creates dangerous, uneven ice over water/mud
             tile.isFrozenPatchy = true
             tile.movementSpeed = 0.4 -- Still slow, ice might break
             tile.hazardRating = 0.7 -- High hazard from breaking through ice into mud/water
             return "visual_effect", "swamp_ice_patchy"
        end,
        
        wind = function(tile) -- Less effect due to dense vegetation/low profile
            local effectiveWind = world.windStrength * 0.3 -- Significantly reduced
             if effectiveWind > 0.6 then
                 -- Disperses fog slightly?
                 tile.fogLevel = math.max(0, tile.fogLevel - 0.2)
                 return "visual_effect", "wind_ripples_swamp"
             end
             return nil
        end
    },

     -- Time effects
    timeEffects = {
        dusk = function(tile)
             tile.fogLevel = math.min(0.7, tile.fogLevel + 0.3) -- Fog rolls in
             tile.diseaseRisk = tile.diseaseRisk + 0.1 -- Insects most active?
             tile.ambientSound = "swamp_dusk_chorus" -- Frogs, insects intensify
             tile.spawnModifier = { type="giant_mosquito", chanceMultiplier=2.0 }
            return nil
        end,
        night = function(tile)
            tile.fogLevel = math.min(0.9, tile.fogLevel + 0.1) -- Fog often thickest at night
            tile.temperatureOffset = 2 -- Swamps can feel warmer/more humid at night? Or just stable.
            tile.ambientSound = "swamp_night_ominous" -- Strange splashes, unknown calls
             -- Increase wisp/ghostly spawns?
             tile.spawnModifier = { type="will_o_wisp", chanceMultiplier=1.5 }
            return nil
        end,
        dawn = function(tile)
            tile.fogLevel = math.min(0.7, tile.fogLevel - 0.1) -- Fog starts lifting
            tile.ambientSound = "swamp_dawn_birds"
            tile.spawnModifier = nil
            -- Reset freeze effect?
             if tile.isFrozenPatchy and world.temperature > 0 then tile.isFrozenPatchy = false; tile.hazardRating=0.4 end
            return nil
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {0.3, 0.35, 0.25}, -- Murky green-brown
        variants = {
            {name = "Murky Bog", water_color={0.2,0.2,0.1}, vegetationDensity=0.5, mudDepth=1.0},
            {name = "Cypress Swamp", overlay_objects={{name="cypress_tree_snowy", chance=0.7}, {name="spanish_moss", chance=0.5}}}, -- Assuming cypress trees are defined
            {name = "Mangrove Thicket", overlay_objects={{name="mangrove_roots", chance=0.8}}, waterDepth=0.8}, -- Complex root systems
            {name = "Reed Marsh", overlay_objects={{name="dense_reeds", chance=0.9}}, vegetationDensity=0.9, treeDensity=0.1},
            {name = "Sunken Forest", overlay_objects={{name="rotting_log_submerged", chance=0.6}, {name="dead_tree_snag", chance=0.4}}},
            {name = "Fungal Mire", overlay_objects={{name="giant_glowing_mushroom", chance=0.5}, {name="spore_cloud_effect", chance=0.2}}}
        },
        decoration_objects = {
            {name = "swamp_gas_bubble_effect", chance = 0.3}, -- Animated bubbles
            {name = "lily_pad_murky", chance = 0.4},
            {name = "hanging_vines", chance = function(tile) return 0.3 * tile.treeDensity end},
            {name = "submerged_roots", chance = 0.5}
        },
         special_effects = {
             fog_volume = {intensity_scale = function(tile) return tile.fogLevel end, color={0.6, 0.7, 0.6}}, -- Volumetric fog
             insect_swarm_particles = {type="particle", density = function(tile) return 0.3 * tile.diseaseRisk end, texture="tiny_fly"}
         },
         water_visuals = { -- Specific look for swamp water
              base_color = {0.25, 0.3, 0.2, 0.7}, -- Murky, low transparency
              animation = "ripple_slow_stagnant",
              reflective = false -- Not very reflective
         }
    },
    
    -- Audio properties
    ambientSound = "swamp_buzzing_croaking",
    footstepSound = "mud_water_squelch", -- Mix of mud and water sounds
     specialSounds = {
        {name = "insect_swarm_loud", trigger = "entity_nearby", chance = function(tile) return 0.15 * tile.diseaseRisk end},
        {name = "splash_nearby_unknown", trigger = "random", chance = 0.05},
        {name = "gurgling_mud_pit", trigger = "random", chance = 0.04},
        {name = "distant_bellow_monster", trigger = "night", chance = 0.02}
    },
    -- Audio Modifiers
     audioModifiers = {
         muffling = 0.3, -- Vegetation muffles slightly
         low_pass_filter = 0.1 -- Slight filter due to humidity/fog?
     },

    -- Connections to other tiles
    connects_to = {
        "swamp", -- Itself
        "mud", -- Often borders or contains mud
        "shallow_water", -- Transition to clearer/deeper water
        "dirt", -- Edge of the swamp
        "grass", -- Edge of the swamp
        "forest", -- Swamps can be within or border forests
        "riverbank" -- Can form along slow rivers
    },

    -- Pathfinding properties
     pathfinding = {
        travel_cost = 3.0, -- Extremely high cost
        hazard_chance = function(tile) return 0.2 + tile.diseaseRisk * 0.2 end, -- Chance for disease, getting stuck, creature attack
        reduces_visibility = true, -- Fog and vegetation
        difficult_terrain = true -- Flag for AI avoidance?
    },

    -- Effects on entities that walk on this tile
    onWalk = function(tile, entity)
        local sound = tile.footstepSound or "mud_water_squelch"
        local fatigue = 0.4 * (1 + tile.mudDepth * 0.5 + tile.waterDepth * 0.3) -- Extremely high fatigue
        local effects = {{type="increase_fatigue", amount = fatigue}}

        -- Chance to get stuck (like mud, but maybe higher?)
        if math.random() < 0.2 * tile.mudDepth then
             table.insert(effects, {type="apply_status", status="stuck_in_mire", duration=4})
             return { sound="mud_suck_deep", volume=1.0, message="You sink deeply into the swampy mire!", effects=effects }
        end
        
        -- Apply wet/muddy status
         table.insert(effects, {type="apply_status", status="wet", duration=120})
         table.insert(effects, {type="apply_status", status="muddy", duration=120})

        -- Chance to contract disease
        if math.random() < SwampTile.diseaseRisk * 0.05 then -- Low chance per step, higher over time
             table.insert(effects, {type="apply_status", status="swamp_fever_incubation", duration=3600}) -- Long incubation period
             -- Maybe no immediate message? Or subtle one?
             -- message = "You inhale the fetid air, feeling slightly unwell."
        end

        return {
            sound = sound,
            volume = 0.9,
            effects = effects
        }
    end,
    
    -- Effect when discovering swamp
    onDiscover = function(tile, entity)
        if entity.journal and entity.journal.addDiscovery then
             entity.journal.addDiscovery({
                type = "biome",
                name = "Swamp",
                location = {x = tile.x, y = tile.y},
                notes = "A treacherous swamp. Humid, difficult terrain, likely dangerous."
            })
        end
        return {
            message = "You enter a murky, oppressive swamp.",
            effects = {
                {type = "apply_environment_effect", effect="humidity", intensity=tile.humidity},
                {type = "apply_environment_effect", effect="low_visibility", intensity=tile.fogLevel},
                {type = "reveal_map", radius = 1} -- Low visibility limits discovery radius
            }
        }
    end
}

function SwampTile.init(world)
    print("Swamp tile module initialized")
    -- Register with environment, pathfinding, ecosystem systems
     if world.systems and world.systems.environment then
        world.systems.environment:registerHazardousBiome("swamp", {disease_risk=0.5, visibility=0.6})
    end
     if world.systems and world.systems.pathfinding then
        world.systems.pathfinding:registerDifficultTerrain("swamp", {cost=3.0})
    end
end

return SwampTile