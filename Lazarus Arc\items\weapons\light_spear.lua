local LightSpear = {
    id = "light_spear",
    name = "Light Spear",
    type = "weapon",
    category = "spear",
    
    -- Properties
    properties = {
        damage = 35,
        attackSpeed = 1.2,
        range = 3.0,
        durability = 180,
        maxDurability = 180,
        holyDamage = 20,
        holyRadius = 2.5,
        holyDuration = 4,
        healAmount = 15,
        healRadius = 3.0,
        healChance = 0.25,
        lightBurstChance = 0.2,
        lightBurstDamage = 25,
        lightBurstRadius = 4.0,
        lightBurstCount = 4,
        lightSpeed = 1.0,
        lightCharge = 0,
        maxLightCharge = 100,
        chargeRate = 4,
        dischargeRate = 8,
        lightResistance = 0.8,
        lightLevel = 1.0,
        undeadDamage = 1.5,
        demonDamage = 1.3
    },
    
    -- Appearance
    appearance = {
        sprite = "light_spear",
        scale = 1.0,
        animations = {
            "idle",
            "thrust",
            "heal",
            "burst"
        },
        variants = {
            "golden",
            "crystal",
            "divine",
            "celestial"
        },
        blendMode = "add",
        tint = {1.0, 1.0, 0.8},
        alpha = 0.9
    },
    
    -- Sound effects
    sounds = {
        thrust = "light_spear_thrust",
        heal = "light_spear_heal",
        burst = "light_spear_burst",
        holy = "light_spear_holy"
    },
    
    -- Effects
    effects = {
        holy = {
            type = "status",
            duration = 4,
            effects = {
                damage = 20,
                radius = 2.5,
                undeadDamage = 1.5,
                demonDamage = 1.3
            }
        },
        heal = {
            type = "status",
            duration = 0.5,
            effects = {
                heal = 15,
                radius = 3.0
            }
        },
        lightBurst = {
            type = "status",
            duration = 0.5,
            effects = {
                damage = 25,
                radius = 4.0
            }
        }
    }
}

-- Initialize the weapon
function LightSpear.init(weapon, world)
    -- Copy all fields from LightSpear template to weapon instance
    for k, v in pairs(LightSpear) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        lightCharge = 0,
        lastChargeTime = 0,
        lastHealTime = 0
    }

    return weapon
end

-- Update the weapon
function LightSpear.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Update light charge
        if world.lightLevel and world.lightLevel > 0.5 then
            local chargeFactor = (world.lightLevel * 0.7 + 0.3) * dt
            weapon.properties.weaponState.lightCharge = math.min(weapon.properties.maxLightCharge,
                weapon.properties.weaponState.lightCharge + chargeFactor * weapon.properties.chargeRate)
        else
            weapon.properties.weaponState.lightCharge = math.max(0,
                weapon.properties.weaponState.lightCharge - weapon.properties.dischargeRate * dt)
        end
    end
end

-- Handle attack
function LightSpear.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Calculate damage
    local damage = weapon.properties.damage
    
    -- Apply holy damage to undead and demons
    if target.type then
        if target.type == "undead" then
            damage = damage * weapon.properties.undeadDamage
        elseif target.type == "demon" then
            damage = damage * weapon.properties.demonDamage
        end
    end
    
    -- Apply damage
    if target.takeDamage then
        target.takeDamage(damage)
    end
    
    -- Check for holy effect
    if math.random() < 0.3 then
        -- Create holy effect
        if world.createEffect then
            world.createEffect({
                type = "holy",
                position = target.position,
                radius = weapon.properties.holyRadius,
                duration = weapon.properties.holyDuration
            })
        end
        
        -- Apply holy effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.holy)
        end
        
        -- Play holy sound
        if world.playSound then
            world.playSound(weapon.sounds.holy)
        end
    end
    
    -- Check for heal effect
    if math.random() < weapon.properties.healChance then
        -- Create heal effect
        if world.createEffect then
            world.createEffect({
                type = "heal",
                position = attacker.position,
                radius = weapon.properties.healRadius,
                duration = 0.5
            })
        end
        
        -- Heal nearby allies
        if world.entities then
            for _, entity in ipairs(world.entities) do
                if entity.position and entity.heal then
                    local distance = math.sqrt(
                        (entity.position.x - attacker.position.x)^2 + 
                        (entity.position.y - attacker.position.y)^2
                    )
                    
                    if distance <= weapon.properties.healRadius then
                        entity.heal(weapon.properties.healAmount)
                    end
                end
            end
        end
        
        -- Play heal sound
        if world.playSound then
            world.playSound(weapon.sounds.heal)
        end
    end
    
    -- Check for light burst
    if math.random() < weapon.properties.lightBurstChance then
        -- Create light burst effect
        if world.createEffect then
            world.createEffect({
                type = "light_burst",
                position = target.position,
                radius = weapon.properties.lightBurstRadius,
                duration = 0.5
            })
        end
        
        -- Create light projectiles
        for i = 1, weapon.properties.lightBurstCount do
            local angle = (i - 1) * (2 * math.pi / weapon.properties.lightBurstCount)
            
            if world.createProjectile then
                world.createProjectile({
                    type = "light_ray",
                    position = target.position,
                    direction = {
                        x = math.cos(angle) * weapon.properties.lightSpeed,
                        y = math.sin(angle) * weapon.properties.lightSpeed
                    },
                    damage = weapon.properties.lightBurstDamage,
                    range = weapon.properties.lightBurstRadius
                })
            end
        end
        
        -- Play burst sound
        if world.playSound then
            world.playSound(weapon.sounds.burst)
        end
    end
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function LightSpear.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough light charge
    if weapon.properties.weaponState.lightCharge < 50 then
        return false
    end
    
    -- Create divine light effect
    if world.createEffect then
        world.createEffect({
            type = "divine_light",
            position = attacker.position,
            radius = weapon.properties.holyRadius * 3,
            duration = 5,
            damage = weapon.properties.damage * 2
        })
    end
    
    -- Apply holy effects to nearby entities
    if world.entities then
        for _, entity in ipairs(world.entities) do
            if entity.position then
                local distance = math.sqrt(
                    (entity.position.x - attacker.position.x)^2 + 
                    (entity.position.y - attacker.position.y)^2
                )
                
                if distance <= weapon.properties.holyRadius * 3 then
                    -- Apply random holy effect
                    local effect = math.random()
                    if effect < 0.4 then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.holy)
                        end
                    elseif effect < 0.7 then
                        if entity.heal then
                            entity.heal(weapon.properties.healAmount * 2)
                        end
                    else
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.lightBurst)
                        end
                    end
                end
            end
        end
    end
    
    -- Reset light charge
    weapon.properties.weaponState.lightCharge = 0
    weapon.properties.weaponState.lastChargeTime = world.time
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 5)
    
    return true
end

return LightSpear 