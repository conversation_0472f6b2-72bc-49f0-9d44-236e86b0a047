local CelestialBow = {
    id = "celestial_bow",
    name = "Celestial Bow",
    type = "weapon",
    category = "bow",
    
    -- Properties
    properties = {
        damage = 35,
        attackSpeed = 1.5,
        range = 8.0,
        durability = 180,
        maxDurability = 180,
        arrowSpeed = 16,
        arrowSpread = 0.1,
        holyDamage = 25,
        holyRadius = 2.0,
        holyDuration = 3,
        starArrowChance = 0.3,
        starArrowDamage = 30,
        starArrowCount = 3,
        starArrowSpread = 0.2,
        lightBurstChance = 0.25,
        lightBurstDamage = 40,
        lightBurstRadius = 3.0,
        lightBurstForce = 1.5,
        celestialCharge = 0,
        maxCelestialCharge = 100,
        chargeRate = 3,
        dischargeRate = 10,
        holyResistance = 0.8,
        starCount = 0,
        maxStars = 5,
        starPower = 1.3
    },
    
    -- Appearance
    appearance = {
        sprite = "celestial_bow",
        scale = 1.0,
        animations = {
            "idle",
            "draw",
            "shoot",
            "charge"
        },
        variants = {
            "gold",
            "silver",
            "crystal",
            "divine"
        },
        blendMode = "add",
        tint = {1.0, 1.0, 0.8},
        alpha = 0.9
    },
    
    -- Sound effects
    sounds = {
        draw = "celestial_bow_draw",
        shoot = "celestial_bow_shoot",
        star = "celestial_bow_star",
        light = "celestial_bow_light"
    },
    
    -- Effects
    effects = {
        holy = {
            type = "status",
            duration = 3,
            effects = {
                damage = 25,
                heal = 5,
                radius = 2.0
            }
        },
        starArrow = {
            type = "status",
            duration = 0.5,
            effects = {
                damage = 30,
                count = 3
            }
        },
        lightBurst = {
            type = "status",
            duration = 0.5,
            effects = {
                damage = 40,
                knockback = 1.5,
                radius = 3.0
            }
        }
    }
}

-- Initialize the weapon
function CelestialBow.init(weapon, world)
    -- Copy all fields from CelestialBow template to weapon instance
    for k, v in pairs(CelestialBow) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        celestialCharge = 0,
        lastChargeTime = 0,
        starCount = 0
    }

    return weapon
end

-- Update the weapon
function CelestialBow.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Update celestial charge
        if world.light and world.light > 0.7 then
            local chargeFactor = (world.light * 0.7 + 0.3) * dt
            weapon.properties.weaponState.celestialCharge = math.min(weapon.properties.maxCelestialCharge,
                weapon.properties.weaponState.celestialCharge + chargeFactor * weapon.properties.chargeRate)
        else
            weapon.properties.weaponState.celestialCharge = math.max(0,
                weapon.properties.weaponState.celestialCharge - weapon.properties.dischargeRate * dt)
        end
        
        -- Create light effect
        if world.createEffect then
            world.createEffect({
                type = "holy",
                position = weapon.position,
                radius = weapon.properties.holyRadius,
                duration = dt
            })
        end
    end
end

-- Handle attack
function CelestialBow.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Calculate arrow direction
    local angle = math.atan2(
        target.position.y - attacker.position.y,
        target.position.x - attacker.position.x
    )
    
    -- Add spread to arrow direction
    local spread = (math.random() - 0.5) * weapon.properties.arrowSpread
    angle = angle + spread
    
    -- Create holy arrow projectile
    if world.createProjectile then
        world.createProjectile({
            type = "holy_arrow",
            position = attacker.position,
            direction = {
                x = math.cos(angle) * weapon.properties.arrowSpeed,
                y = math.sin(angle) * weapon.properties.arrowSpeed
            },
            damage = weapon.properties.damage,
            range = weapon.properties.range
        })
    end
    
    -- Check for holy effect
    if math.random() < weapon.properties.holyDamage / weapon.properties.damage then
        -- Create holy effect
        if world.createEffect then
            world.createEffect({
                type = "holy",
                position = target.position,
                radius = weapon.properties.holyRadius,
                duration = weapon.properties.holyDuration
            })
        end
        
        -- Apply holy effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.holy)
        end
        
        -- Play light sound
        if world.playSound then
            world.playSound(weapon.sounds.light)
        end
    end
    
    -- Check for star arrow effect
    if math.random() < weapon.properties.starArrowChance then
        -- Create star arrow effect
        if world.createEffect then
            world.createEffect({
                type = "star_arrow",
                position = target.position,
                count = weapon.properties.starArrowCount,
                spread = weapon.properties.starArrowSpread,
                duration = 0.5
            })
        end
        
        -- Create star arrow projectiles
        for i = 1, weapon.properties.starArrowCount do
            local starAngle = angle + (i - 1) * (weapon.properties.starArrowSpread / (weapon.properties.starArrowCount - 1))
            
            if world.createProjectile then
                world.createProjectile({
                    type = "star_arrow",
                    position = target.position,
                    direction = {
                        x = math.cos(starAngle) * weapon.properties.arrowSpeed * 0.8,
                        y = math.sin(starAngle) * weapon.properties.arrowSpeed * 0.8
                    },
                    damage = weapon.properties.starArrowDamage,
                    range = weapon.properties.range * 0.7
                })
            end
        end
        
        -- Increase star count
        weapon.properties.weaponState.starCount = math.min(weapon.properties.maxStars,
            weapon.properties.weaponState.starCount + 1)
        
        -- Play star sound
        if world.playSound then
            world.playSound(weapon.sounds.star)
        end
    end
    
    -- Check for light burst effect
    if math.random() < weapon.properties.lightBurstChance then
        -- Create light burst effect
        if world.createEffect then
            world.createEffect({
                type = "light_burst",
                position = target.position,
                radius = weapon.properties.lightBurstRadius,
                duration = 0.5
            })
        end
        
        -- Apply light burst effect to nearby entities
        if world.entities then
            for _, entity in ipairs(world.entities) do
                if entity.position then
                    local distance = math.sqrt(
                        (entity.position.x - target.position.x)^2 + 
                        (entity.position.y - target.position.y)^2
                    )
                    
                    if distance <= weapon.properties.lightBurstRadius then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.lightBurst)
                        end
                        
                        -- Apply knockback
                        if entity.move then
                            local force = (1 - distance / weapon.properties.lightBurstRadius) * 
                                weapon.properties.lightBurstForce
                            local angle = math.atan2(
                                entity.position.y - target.position.y,
                                entity.position.x - target.position.x
                            )
                            entity.move({
                                x = math.cos(angle) * force,
                                y = math.sin(angle) * force
                            })
                        end
                    end
                end
            end
        end
    end
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function CelestialBow.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough celestial charge and stars
    if weapon.properties.weaponState.celestialCharge < 50 or weapon.properties.weaponState.starCount < 2 then
        return false
    end
    
    -- Create celestial bow effect
    if world.createEffect then
        world.createEffect({
            type = "celestial_bow",
            position = attacker.position,
            radius = weapon.properties.holyRadius * 3,
            duration = 6,
            damage = weapon.properties.damage * 2
        })
    end
    
    -- Apply celestial effects to nearby entities
    if world.entities then
        for _, entity in ipairs(world.entities) do
            if entity.position then
                local distance = math.sqrt(
                    (entity.position.x - attacker.position.x)^2 + 
                    (entity.position.y - attacker.position.y)^2
                )
                
                if distance <= weapon.properties.holyRadius * 3 then
                    -- Apply random celestial effect
                    local effect = math.random()
                    if effect < 0.4 then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.holy)
                        end
                    elseif effect < 0.7 then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.starArrow)
                        end
                    else
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.lightBurst)
                        end
                    end
                end
            end
        end
    end
    
    -- Reset celestial charge and consume stars
    weapon.properties.weaponState.celestialCharge = 0
    weapon.properties.weaponState.lastChargeTime = world.time
    weapon.properties.weaponState.starCount = math.max(0,
        weapon.properties.weaponState.starCount - 2)
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 5)
    
    return true
end

return CelestialBow 