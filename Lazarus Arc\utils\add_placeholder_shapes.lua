-- utils/add_placeholder_shapes.lua
-- <PERSON><PERSON>t to add placeholder shapes to entities that don't have them
-- PLACEHOLDER: This adds basic geometric shapes as placeholders for mob visuals

local lfs = love.filesystem

-- Define placeholder shapes for different entity types
local PLACEHOLDER_SHAPES = {
    -- Small creatures (size 4-6)
    small_mammal = {
        {0, -1}, {0.8, -0.6}, {1, 0}, {0.8, 0.6}, {0, 1}, {-0.8, 0.6}, {-1, 0}, {-0.8, -0.6}
    },
    small_bird = {
        {0, -1}, {0.6, -0.4}, {1, 0}, {0.6, 0.4}, {0, 1}, {-0.6, 0.4}, {-1, 0}, {-0.6, -0.4}
    },
    small_aquatic = {
        {0, -0.8}, {0.9, -0.3}, {1, 0}, {0.9, 0.3}, {0, 0.8}, {-0.9, 0.3}, {-1, 0}, {-0.9, -0.3}
    },
    
    -- Medium creatures (size 7-10)
    medium_mammal = {
        {0, -1}, {0.8, -0.8}, {1, 0}, {0.8, 0.8}, {0, 1}, {-0.8, 0.8}, {-1, 0}, {-0.8, -0.8}
    },
    medium_bird = {
        {0, -1}, {0.7, -0.5}, {1, 0}, {0.7, 0.5}, {0, 1}, {-0.7, 0.5}, {-1, 0}, {-0.7, -0.5}
    },
    medium_aquatic = {
        {0, -0.9}, {0.9, -0.4}, {1, 0}, {0.9, 0.4}, {0, 0.9}, {-0.9, 0.4}, {-1, 0}, {-0.9, -0.4}
    },
    
    -- Large creatures (size 11+)
    large_mammal = {
        {0, -1}, {0.9, -0.9}, {1, 0}, {0.9, 0.9}, {0, 1}, {-0.9, 0.9}, {-1, 0}, {-0.9, -0.9}
    },
    large_bird = {
        {0, -1}, {0.8, -0.6}, {1, 0}, {0.8, 0.6}, {0, 1}, {-0.8, 0.6}, {-1, 0}, {-0.8, -0.6}
    },
    large_aquatic = {
        {0, -1}, {0.95, -0.5}, {1, 0}, {0.95, 0.5}, {0, 1}, {-0.95, 0.5}, {-1, 0}, {-0.95, -0.5}
    }
}

-- Entity categorization for shape selection
local ENTITY_CATEGORIES = {
    -- Small mammals
    rabbit = {type = "small_mammal", size = 6},
    mouse = {type = "small_mammal", size = 4},
    field_mouse = {type = "small_mammal", size = 4},
    squirrel = {type = "small_mammal", size = 5},
    
    -- Medium mammals  
    sheep = {type = "medium_mammal", size = 8},
    goat = {type = "medium_mammal", size = 8},
    raccoon = {type = "medium_mammal", size = 7},
    
    -- Large mammals
    horse = {type = "large_mammal", size = 14},
    untamed_horse = {type = "large_mammal", size = 14},
    
    -- Small birds
    butterfly = {type = "small_bird", size = 3},
    fly = {type = "small_bird", size = 2},
    songbird = {type = "small_bird", size = 4},
    
    -- Medium birds
    bird = {type = "medium_bird", size = 6},
    pigeon = {type = "medium_bird", size = 6},
    duck = {type = "medium_bird", size = 7},
    owl = {type = "medium_bird", size = 7},
    snowy_owl = {type = "medium_bird", size = 7},
    great_horned_owl = {type = "medium_bird", size = 8},
    hawk = {type = "medium_bird", size = 8},
    
    -- Large birds
    goose = {type = "large_bird", size = 10},
    swan = {type = "large_bird", size = 12},
    crane = {type = "large_bird", size = 11},
    flamingo = {type = "large_bird", size = 11},
    pelican = {type = "large_bird", size = 12},
    eagle = {type = "large_bird", size = 10},
    vulture = {type = "large_bird", size = 10},
    
    -- Aquatic creatures
    frog = {type = "small_aquatic", size = 4},
    turtle = {type = "medium_aquatic", size = 8},
    
    -- Fish (small)
    minnow = {type = "small_aquatic", size = 3},
    goldfish = {type = "small_aquatic", size = 4},
    guppy = {type = "small_aquatic", size = 3},
    betta = {type = "small_aquatic", size = 4},
    
    -- Fish (medium)
    bass = {type = "medium_aquatic", size = 7},
    trout = {type = "medium_aquatic", size = 7},
    perch = {type = "medium_aquatic", size = 6},
    bluegill = {type = "medium_aquatic", size = 6},
    sunfish = {type = "medium_aquatic", size = 6},
    catfish = {type = "medium_aquatic", size = 8},
    carp = {type = "medium_aquatic", size = 8},
    crappie = {type = "medium_aquatic", size = 6},
    walleye = {type = "medium_aquatic", size = 7},
    pike = {type = "medium_aquatic", size = 8},
    northern_pike = {type = "medium_aquatic", size = 9},
    angelfish = {type = "medium_aquatic", size = 6},
    plecostomus = {type = "medium_aquatic", size = 7},
    
    -- Fish (large)
    muskellunge = {type = "large_aquatic", size = 12},
    tuna = {type = "large_aquatic", size = 14},
    swordfish = {type = "large_aquatic", size = 15},
    shark = {type = "large_aquatic", size = 18},
    whale = {type = "large_aquatic", size = 25},
    dolphin = {type = "large_aquatic", size = 16},
    ray = {type = "large_aquatic", size = 12},
    
    -- Special creatures (keep existing shapes if they have them)
    wolf = {type = "medium_mammal", size = 9}, -- Has shape already
    bear = {type = "large_mammal", size = 15}, -- Has shape already
    fox = {type = "medium_mammal", size = 7}, -- Has shape already
    deer = {type = "large_mammal", size = 12}, -- Has shape already
    crow = {type = "medium_bird", size = 5}, -- Has shape already
    crab = {type = "small_aquatic", size = 4}, -- Has shape already
}

-- Function to get appropriate shape and size for an entity
function getPlaceholderShapeAndSize(entityName)
    local category = ENTITY_CATEGORIES[entityName]
    if not category then
        -- Default fallback
        return PLACEHOLDER_SHAPES.medium_mammal, 6
    end
    
    local shape = PLACEHOLDER_SHAPES[category.type] or PLACEHOLDER_SHAPES.medium_mammal
    return shape, category.size
end

-- Function to check if an entity file has a shape defined
function hasShape(filePath)
    local content = lfs.read(filePath)
    if not content then return false end
    
    -- Check for shape definition
    return content:find("shape%s*=%s*{") ~= nil
end

-- Function to add shape and size to an entity file
function addShapeToEntity(filePath, entityName)
    local content = lfs.read(filePath)
    if not content then
        print("Could not read file: " .. filePath)
        return false
    end
    
    -- Get appropriate shape and size
    local shape, size = getPlaceholderShapeAndSize(entityName)
    
    -- Convert shape to string format
    local shapeStr = "    shape = {\n"
    for i = 1, #shape do
        local point = shape[i]
        shapeStr = shapeStr .. "        {" .. point[1] .. ", " .. point[2] .. "}"
        if i < #shape then
            shapeStr = shapeStr .. ","
        end
        shapeStr = shapeStr .. "\n"
    end
    shapeStr = shapeStr .. "    },"
    
    -- Add size
    local sizeStr = "    size = " .. size .. ","
    
    -- Find insertion point (after type = "...")
    local typePattern = '(type%s*=%s*"[^"]*",%s*\n)'
    local insertPos = content:find(typePattern)
    
    if insertPos then
        local beforeType, afterType = content:match('(.*)' .. typePattern .. '(.*)')
        if beforeType and afterType then
            -- Add PLACEHOLDER comment
            local placeholderComment = "    -- PLACEHOLDER: Basic shape for visual representation\n"
            local newContent = beforeType .. content:match(typePattern) .. 
                             placeholderComment .. shapeStr .. "\n    " .. sizeStr .. "\n" .. afterType
            
            -- Write back to file
            local success = lfs.write(filePath, newContent)
            if success then
                print("Added placeholder shape to: " .. entityName)
                return true
            else
                print("Failed to write file: " .. filePath)
                return false
            end
        end
    else
        print("Could not find insertion point in: " .. filePath)
        return false
    end
    
    return false
end

-- Main function to process all entities
function addPlaceholderShapes()
    print("=== ADDING PLACEHOLDER SHAPES TO ENTITIES ===")
    print("This adds basic geometric shapes as placeholders for mob visuals")
    print("")
    
    local entitiesDir = "entities"
    local files = lfs.getDirectoryItems(entitiesDir)
    local processed = 0
    local added = 0
    
    for _, file in ipairs(files) do
        if file:match("%.lua$") and file ~= "entity.lua" then
            local entityName = file:gsub("%.lua$", "")
            local filePath = entitiesDir .. "/" .. file
            
            -- Skip if it's a directory or special file
            if not lfs.getInfo(filePath) or lfs.getInfo(filePath).type ~= "file" then
                goto continue
            end
            
            processed = processed + 1
            
            -- Check if entity already has a shape
            if hasShape(filePath) then
                print("Skipping " .. entityName .. " (already has shape)")
            else
                -- Add shape to entity
                if addShapeToEntity(filePath, entityName) then
                    added = added + 1
                end
            end
            
            ::continue::
        end
    end
    
    print("")
    print("=== SUMMARY ===")
    print("Processed: " .. processed .. " entity files")
    print("Added shapes to: " .. added .. " entities")
    print("All placeholder shapes use basic geometric forms appropriate for entity size")
end

return {
    addPlaceholderShapes = addPlaceholderShapes,
    getPlaceholderShapeAndSize = getPlaceholderShapeAndSize,
    ENTITY_CATEGORIES = ENTITY_CATEGORIES,
    PLACEHOLDER_SHAPES = PLACEHOLDER_SHAPES
}
