local PoisonTrap = {
    id = "poison_trap",
    name = "Poison Trap",
    type = "trap",
    
    -- Entity categories
    categories = {"trap", "dungeon", "poison"},
    
    -- Target categories
    targetCategories = {"player", "monster"},
    
    -- Stats
    maxHealth = 20,
    health = 20,
    duration = 15,
    cooldown = 45,
    
    -- Trap properties
    properties = {
        radius = 2.0,
        setupTime = 3,
        isArmed = false,
        isTriggered = false,
        destroyOnActivate = false,
        poisonStrength = 0.8,
        cloudDuration = 8
    },
    
    -- Appearance
    appearance = {
        sprite = "poison_trap",
        scale = 1.0,
        animations = {
            "idle", "trigger", "cloud"
        },
        variants = {
            "green", "purple", "black", "toxic"
        }
    },
    
    -- Sound effects
    sounds = {
        trigger = "poison_trap_trigger",
        cloud = "poison_trap_cloud",
        hiss = "poison_trap_hiss"
    },
    
    -- Effects
    effects = {
        poison = {
            type = "status",
            duration = 15,
            effects = {
                damage = 4,
                interval = 1,
                slow = 0.3,
                weakness = 0.2
            }
        },
        poisonCloud = {
            type = "area",
            duration = 8,
            radius = 2.0,
            effects = {
                poison = {
                    type = "status",
                    duration = 5,
                    effects = {
                        damage = 2,
                        interval = 1,
                        slow = 0.2
                    }
                }
            }
        }
    }
}

-- Initialize the trap
function PoisonTrap.init(entity, world)
    -- Copy all fields from PoisonTrap template to entity instance
    for k, v in pairs(PoisonTrap) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position
    entity.position = entity.position or {x = 0, y = 0}

    -- Set random variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize trap state
    entity.properties.isArmed = false
    entity.properties.isTriggered = false
    entity.properties.triggerTime = 0
    entity.properties.cloudActive = false

    return entity
end

-- Update the trap
function PoisonTrap.update(entity, world, dt)
    -- Check if trap is armed and not triggered
    if entity.properties.isArmed and not entity.properties.isTriggered then
        -- Check for targets in radius
        if world and world.entities then
            for _, target in ipairs(world.entities) do
                if target.position and target.categories then
                    -- Check if target is in trap categories
                    local isTarget = false
                    for _, category in ipairs(entity.targetCategories) do
                        if table.contains(target.categories, category) then
                            isTarget = true
                            break
                        end
                    end
                    
                    if isTarget then
                        local distance = math.sqrt(
                            (target.position.x - entity.position.x)^2 + 
                            (target.position.y - entity.position.y)^2
                        )
                        
                        if distance <= entity.properties.radius then
                            -- Trigger trap
                            entity.properties.isTriggered = true
                            entity.properties.triggerTime = world.time
                            entity.properties.cloudActive = true
                            
                            -- Apply poison effect to target
                            if target.applyEffect then
                                target.applyEffect(entity.effects.poison)
                            end
                            
                            -- Create poison cloud
                            if world.createEffect then
                                world.createEffect({
                                    type = "poison_cloud",
                                    position = entity.position,
                                    radius = entity.properties.radius,
                                    duration = entity.properties.cloudDuration,
                                    effects = entity.effects.poisonCloud.effects
                                })
                            end
                            
                            -- Play trigger sound
                            if world.playSound then
                                world.playSound(entity.sounds.trigger)
                            end
                            
                            break
                        end
                    end
                end
            end
        end
    end
    
    -- Handle poison cloud
    if entity.properties.cloudActive then
        if world and world.entities then
            for _, target in ipairs(world.entities) do
                if target.position and target.categories then
                    local distance = math.sqrt(
                        (target.position.x - entity.position.x)^2 + 
                        (target.position.y - entity.position.y)^2
                    )
                    
                    if distance <= entity.properties.radius then
                        -- Apply weaker poison effect to targets in cloud
                        if target.applyEffect then
                            target.applyEffect(entity.effects.poisonCloud.effects.poison)
                        end
                    end
                end
            end
        end
        
        -- Check if cloud should dissipate
        if world.time - entity.properties.triggerTime >= entity.properties.cloudDuration then
            entity.properties.cloudActive = false
        end
    end
    
    -- Check if trap should be destroyed
    if entity.properties.isTriggered and world.time - entity.properties.triggerTime >= entity.duration then
        if world.removeEntity then
            world.removeEntity(entity)
        end
    end
    
    -- Check if trap can be rearmed
    if not entity.properties.isArmed and world.time - entity.properties.triggerTime >= entity.cooldown then
        entity.properties.isArmed = true
        entity.properties.isTriggered = false
        entity.properties.cloudActive = false
    end
end

-- Arm the trap
function PoisonTrap.arm(entity, world)
    if not entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = true
        entity.properties.triggerTime = world.time
        entity.properties.cloudActive = false
        return true
    end
    return false
end

-- Disarm the trap
function PoisonTrap.disarm(entity, world)
    if entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = false
        entity.properties.triggerTime = world.time
        entity.properties.cloudActive = false
        return true
    end
    return false
end

return PoisonTrap 