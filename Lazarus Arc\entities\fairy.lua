-- entities/fairy.lua
-- Fairy with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local Fairy = {
    id = "fairy",
    name = "Fairy",
    type = "fairy",
    shape = {
        {0, -0.4}, {0.3, -0.3}, {0.4, 0}, {0.3, 0.3},
        {0, 0.4}, {-0.3, 0.3}, {-0.4, 0}, {-0.3, -0.3}
    },
    size = 2,  -- Very small

    -- Entity categories
    categories = {"fae", "magical", "flying", "nature", "tiny"},
    threatCategories = {"spider", "bird", "dark_magic"},
    
    -- Base stats (small but magical)
    maxHealth = 15,
    health = 15,
    maxStamina = 40,
    stamina = 40,
    maxMana = 60,
    mana = 60,
    speed = 4.0,  -- Very fast
    attack = 5,   -- Low physical attack
    defense = 2,  -- Very low defense
    magicAttack = 25,  -- High magic attack
    magicDefense = 20,
    flight_altitude = 15,
    fairy_magic = 20,
    nature_affinity = 15,
    
    -- Behaviors
    behaviors = {"fairy_flight", "nature_magic", "mischief", "flower_dance"},
    behaviorConfigs = {
        fairy_flight = {
            flightSpeed = 4.5,
            hovering = true,
            erraticMovement = true,
            sparkleTrail = true
        },
        nature_magic = {
            healingMagic = 2.0,
            plantGrowth = 1.5,
            flowerBloom = true,
            natureBless = 1.8
        },
        mischief = {
            prankChance = 0.3,
            confusionSpells = true,
            illusionMagic = 1.5,
            playfulTricks = true
        },
        flower_dance = {
            danceRadius = 8,
            magicAmplification = 1.5,
            groupDancing = true,
            seasonalRituals = true
        }
    },
    
    -- Enhanced variant system for fairies
    variantChances = {
        normal = 0.70,          -- 70% garden fairy
        shiny = 0.22,           -- 22% moon fairy (shiny)
        rare = 0.07,            -- 7% nature spirit (rare)
        legendary = 0.01        -- 1% fairy queen (legendary)
    },
    
    variants = {
        normal = {
            name = "Garden Fairy",
            description = "A tiny fairy that tends to flowers and gardens",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {1.0, 0.8, 1.2, 1.0}  -- Soft pink-white
            }
        },
        
        shiny = {
            name = "Moon Fairy",
            description = "A luminous fairy blessed by moonlight",
            statModifiers = {
                maxHealth = 1.4,    -- 21 health
                maxMana = 1.6,      -- 96 mana
                magicAttack = 1.5,  -- 37.5 magic attack
                speed = 1.3,        -- 5.2 speed
                lunar_magic = 2.0,
                night_vision = 3.0
            },
            appearanceModifiers = {
                scale = 1.1,
                glow = true,
                colorTint = {0.8, 0.9, 1.4, 1.0},  -- Silvery moonlight
                moon_glow = true,
                starlight_wings = true,
                lunar_aura = true
            },
            soundModifiers = {
                pitch = 1.3,
                volume = 1.1,
                ethereal = true
            }
        },
        
        rare = {
            name = "Nature Spirit",
            description = "A powerful fairy spirit connected to the essence of nature",
            statModifiers = {
                maxHealth = 1.8,    -- 27 health
                maxMana = 2.0,      -- 120 mana
                magicAttack = 2.0,  -- 50 magic attack
                speed = 1.4,        -- 5.6 speed
                nature_mastery = 4.0,
                elemental_control = 2.5
            },
            appearanceModifiers = {
                scale = 1.2,
                colorTint = {0.6, 1.3, 0.7, 1.0},  -- Vibrant nature green
                nature_integration = true,
                seasonal_changes = true,
                elemental_wings = true,
                forest_aura = true
            },
            soundModifiers = {
                pitch = 1.1,
                volume = 1.2,
                reverb = true,
                natural_harmony = true
            }
        },
        
        legendary = {
            name = "Fairy Queen",
            description = "The sovereign ruler of all fairy folk with immense magical power",
            statModifiers = {
                maxHealth = 2.5,    -- 37.5 health
                maxMana = 3.0,      -- 180 mana
                magicAttack = 3.0,  -- 75 magic attack
                speed = 1.6,        -- 6.4 speed
                royal_authority = 10.0,
                fairy_dominion = 5.0,
                reality_weaving = 3.0
            },
            appearanceModifiers = {
                scale = 1.4,
                glow = true,
                colorTint = {1.4, 1.2, 1.6, 1.0},  -- Royal rainbow shimmer
                fairy_crown = true,
                royal_aura = "legendary",
                reality_shimmer = true,
                court_presence = true
            },
            soundModifiers = {
                pitch = 1.0,
                volume = 1.4,
                reverb = true,
                echo = true,
                royal_chimes = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "fairy_dust", chance = 0.9, quantity = {1, 3}},
        {id = "tiny_wing", chance = 0.7, quantity = {1, 2}},
        {id = "flower_petal", chance = 0.8, quantity = {2, 4}},
        {id = "nature_magic_essence", chance = 0.6, quantity = {1, 1}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "moon_fairy_dust", chance = 0.9, quantity = {1, 2}},
            {id = "starlight_wing", chance = 0.8, quantity = {1, 1}},
            {id = "lunar_essence", chance = 0.7, quantity = {1, 1}},
            {id = "moonbeam_crystal", chance = 0.6, quantity = {1, 1}}
        },
        rare = {
            {id = "nature_spirit_essence", chance = 0.9, quantity = {1, 2}},
            {id = "elemental_wing_fragment", chance = 0.8, quantity = {1, 1}},
            {id = "seasonal_magic_core", chance = 0.7, quantity = {1, 1}},
            {id = "forest_spirit_blessing", chance = 0.6, quantity = {1, 1}}
        },
        legendary = {
            {id = "fairy_queen_crown_fragment", chance = 0.95, quantity = {1, 1}},
            {id = "royal_fairy_essence", chance = 0.9, quantity = {1, 2}},
            {id = "reality_weaving_dust", chance = 0.8, quantity = {1, 1}},
            {id = "fairy_dominion_crystal", chance = 0.7, quantity = {1, 1}},
            {id = "court_authority_gem", chance = 0.6, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "fairy",
        scale = 0.4,
        animations = {
            "flutter", "flower_dance", "magic_sparkle", "mischief", "royal_flight"
        },
        variants = {
            "garden_fairy", "moon_fairy", "nature_spirit", "fairy_queen"
        }
    },
    
    -- Sound effects with magical fairy characteristics
    sounds = {
        fairy_chime = {
            file = "fairy_chime",
            synth = {
                instrument = "celesta",
                notes = {"C5", "E5", "G5", "C6"},
                durations = {0.2, 0.2, 0.3, 0.4},
                volume = 0.3,
                magical = true
            }
        },
        wing_flutter = {
            file = "wing_flutter",
            synth = {
                instrument = "flute",
                notes = {"A4", "C5", "E5"},
                durations = {0.1, 0.1, 0.2},
                volume = 0.2,
                delicate = true
            }
        },
        magic_sparkle = {
            file = "magic_sparkle",
            synth = {
                instrument = "kalimba",
                notes = {"G4", "B4", "D5", "G5"},
                durations = {0.15, 0.15, 0.2, 0.3},
                volume = 0.25,
                sparkling = true
            }
        },
        fairy_giggle = {
            file = "fairy_giggle",
            synth = {
                instrument = "xylophone",
                notes = {"E5", "G5", "B5", "E6"},
                durations = {0.1, 0.1, 0.1, 0.2},
                volume = 0.3,
                playful = true
            }
        }
    },
    
    -- Special fairy abilities
    abilities = {
        fairy_flight = {
            type = "passive",
            description = "Natural flight with perfect maneuverability",
            effect = "perfect_flight"
        },
        healing_magic = {
            type = "active",
            description = "Heals allies with fairy magic",
            effect = "magical_heal",
            manaCost = 20,
            cooldown = 8
        },
        nature_blessing = {
            type = "active",
            description = "Blesses plants and natural areas",
            effect = "nature_enhancement",
            manaCost = 15,
            cooldown = 12
        },
        fairy_dust = {
            type = "active",
            description = "Sprinkles magical dust with various effects",
            effect = "random_magic",
            manaCost = 10,
            cooldown = 6
        },
        mischief_magic = {
            type = "active",
            description = "Casts harmless but confusing spells",
            effect = "confusion_magic",
            manaCost = 12,
            cooldown = 10
        },
        size_advantage = {
            type = "passive",
            description = "Too small to be hit by many attacks",
            effect = "evasion_bonus"
        }
    }
}

-- Initialize the fairy entity using enhanced template
function Fairy.init(entity, world)
    -- Copy all fields from Fairy template to entity instance
    for k, v in pairs(Fairy) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return Fairy
