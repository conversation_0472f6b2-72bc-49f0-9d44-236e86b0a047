-- classes/summoner.lua
-- Summoner class definition for Lazarus Arc

local Summoner = {
    name = "Summoner",
    description = "A conjurer of mystical creatures, using summoned allies to control the battlefield.",
    icon = "summoner_icon.png",

    -- 🎭 **Playstyle & Trade-offs**
    pros = {
        "Can summon creatures to fight in their place",
        "Extremely strong battlefield control",
        "Can customize summons to match different strategies"
    },
    cons = {
        "Weaker direct combat abilities",
        "Requires constant management of summoned entities",
        "If summons are defeated, the Summoner is vulnerable"
    },

    -- 🏋 **Base Stats & Growth**
    base_stats = { 
        health = 85, 
        maxHealth = 85,
        stamina = 50, 
        maxStamina = 50,
        mana = 110, 
        maxMana = 110,
        pattack = 4, 
        pdefense = 4, 
        mattack = 14, 
        mdefense = 9, 
        speed = 5, 
        luck = 6 
    },

    stat_growth = {
        health = 7,
        stamina = 5,
        mana = 11,
        pattack = 0.3,
        pdefense = 0.5,
        mattack = 2.0,
        mdefense = 1.0,
        speed = 0.5,
        luck = 0.5
    },

    -- ⚔️ **Weapon Proficiency**
    proficiencies = { 
        staff = 2.0,  
        wand = 2.0,  
        dagger = 1.0,  
        sword = 0.7,  
        greatsword = 0.3,  
        bow = 0.5,  
        gun = 0.4,  
        magic = 2.5  -- Strong summoning magic proficiency
    },

    -- 🌿 **Elemental Affinities**
    element_affinities = {
        earth = 10,    -- Strong connection to nature-based summoning
        fire = 10,     -- Capable of summoning powerful fire spirits
        ice = 8,       -- Some control over frost-based creatures
        air = 7,       -- Can summon fast-moving wind spirits
        dark = 6,      -- Some summoners tap into forbidden entities
    },

    -- 💥 **Overdrive Abilities**
    overdrive = { 
        "grand_summon", -- Calls forth a legendary creature with immense power
        "elemental_legion", -- Summons multiple elemental spirits at once
        "soul_merge", -- Temporarily fuses the Summoner with a summon, boosting stats
    },

    -- 🎯 **Starting Skills**
    starting_skills = {
        "summon_wolf",
        "elemental_contract",
        "summoner's_mark"
    },

    -- 📈 **Skill Progression**
    skill_tree = {
        tier1 = {
            "summon_fire_sprite",
            "enhanced_binding",
            "mystic_barrier"
        },
        tier2 = {
            "summon_frost_beast",
            "summon_storm_drake", 
            "soul_transfer",
            "eldritch_pact"
        },
        tier3 = {
            "summon_phoenix",
            "summon_ancient_titan",
            "divine_guardian"
        }
    },

    -- 🎒 **Inventory & Carrying Capacity**
    inventory_limit = 90, -- Mostly carries magical artifacts rather than weapons

    -- ✨ **Special Abilities (Passive Perks)**
    special_abilities = {
        "Summoner's Pact", -- Summons last longer and require less mana upkeep
        "Tactical Control", -- Can issue advanced commands to summons
        "Elemental Synergy", -- Summons gain a boost if aligned with the Summoner’s elemental affinity
        "Spirit Link", -- Shares a portion of damage taken with active summons
        "Resonant Echo", -- When a summon is defeated, the Summoner gains a temporary stat boost
    },

    -- 🎭 **Prestige Paths**
    prestige_paths = {
        primal_conjurer = {
            description = "A master of summoning raw elemental creatures, specializing in nature-based beings.",
            bonuses = {
                "Elemental summons are significantly stronger",
                "Increased affinity to fire, ice, and earth magic",
                "Summons regenerate health over time"
            },
            weaknesses = {
                "Limited non-elemental summon choices",
                "Weaker against summoners who control demons or celestial beings"
            }
        },

        voidcaller = {
            description = "A summoner who binds otherworldly entities to their will, calling forth eldritch horrors.",
            bonuses = {
                "Dark-based summons deal immense damage",
                "Can summon multiple minions at once",
                "Summons inflict fear or debuffs on enemies"
            },
            weaknesses = {
                "Void summons have a chance to disobey commands",
                "Weaker against holy magic users"
            }
        },

        celestial_invoker = {
            description = "A summoner who communes with celestial beings, bringing divine warriors to the battlefield.",
            bonuses = {
                "Can summon angelic or spirit guardians",
                "Summons can heal allies in addition to attacking",
                "Higher resistance to dark magic"
            },
            weaknesses = {
                "More defensive than offensive",
                "Celestial summons require high mana upkeep"
            }
        }
    },

    -- ⚔️ **Hybrid & Multiclass Options**
    hybrid_options = {
        summoner_warlock = {
            description = "A Summoner who fuses eldritch magic with direct spellcasting, giving them more offensive power.",
            pros = {
                "Can directly cast offensive spells alongside summons",
                "Summons drain enemy health to restore mana"
            },
            cons = {
                "Weaker durability",
                "More reliant on debuffs rather than pure summon strength"
            }
        },

        summoner_knight = {
            description = "A Summoner who fights alongside their summoned creatures, wielding melee combat skills in battle.",
            pros = {
                "Can use heavy armor without penalty",
                "Summons take hits meant for the Summoner"
            },
            cons = {
                "Requires stamina and mana management",
                "Less powerful summons compared to pure Summoners"
            }
        }
    }
}

return Summoner
