-- timer_system.lua
-- Timer utility for scheduling delayed and repeating actions

local TimerSystem = {
    callbacks = {},
    nextId = 1
}

-- Add a new timer that executes once after the specified delay
function TimerSystem:after(delay, callback)
    local id = self.nextId
    self.nextId = self.nextId + 1
    
    self.callbacks[id] = {
        delay = delay,
        remaining = delay,
        callback = callback,
        repeat_interval = nil
    }
    
    return id
end

-- Add a new timer that executes repeatedly with the specified interval
function TimerSystem:every(interval, callback)
    local id = self.nextId
    self.nextId = self.nextId + 1
    
    self.callbacks[id] = {
        delay = interval,
        remaining = interval,
        callback = callback,
        repeat_interval = interval
    }
    
    return id
end

-- Cancel a timer
function TimerSystem:cancel(id)
    self.callbacks[id] = nil
end

-- Update all timers (call in love.update)
function TimerSystem:update(dt)
    for id, timer in pairs(self.callbacks) do
        timer.remaining = timer.remaining - dt
        
        if timer.remaining <= 0 then
            timer.callback(dt)
            
            if timer.repeat_interval then
                -- Reset for repeating timer
                timer.remaining = timer.repeat_interval
            else
                -- Remove one-time timer
                self.callbacks[id] = nil
            end
        end
    end
end

-- Initialize with any configuration
function TimerSystem:init(config)
    -- Any initialization parameters can be set here
    return self
end

return TimerSystem 