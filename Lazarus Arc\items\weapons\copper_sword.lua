-- items/weapons/copper_sword.lua

local Materials = require("items.materials")

-- Create the base item
local baseSword = {
    id = "copper_sword",
    name = "Sword",
    description = "A basic sword for combat.",
    type = "sword",
    category = "weapon",
    stackable = false,

    stats = {
        attack_power = 7.0,
        attack_speed = 1.0,
        durability = 80.0,
        repair_cost = 20,
        weight = 4.0,
    },

    damage_type = "physical",
    
    sprite = "res://Sprite/items/weapons/copper_sword.png",
    size = { width = 64, height = 64 },

    effects = {
        critical_chance = 0.05
    },

    meta = {
        rarity = "common"
    },

    take_damage = function(self, amount)
        self.stats.durability = math.max(0, self.stats.durability - amount)
        if self.stats.durability == 0 then
            print(self.name .. " is broken! Penalties applied.")
            -- Potential penalties could be applied here
        end
    end,

    repair = function(self)
        local repair_amount = self.stats.max_durability - self.stats.durability
        self.stats.durability = self.stats.max_durability
        print(self.name .. " repaired by " .. repair_amount .. " points.")
    end
}

-- Apply the copper material properties to the sword
local Weapon = Materials:applyMaterialToItem(baseSword, "copper")

-- Store the max durability for repair function
Weapon.stats.max_durability = Weapon.stats.durability

return Weapon
