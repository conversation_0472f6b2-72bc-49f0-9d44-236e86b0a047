-- tiles/farmstead.lua
local FarmsteadTile = {
    id = "farmstead",
    name = "Farmstead",
    passable = true,
    variants = 5, -- Different farm layouts and states
    
    -- Properties
    properties = {
        cropType = nil, -- Set during init
        growthStage = "empty", -- empty, planted, growing, mature, harvested
        waterLevel = 0.7,
        soilQuality = 0.8,
        pestLevel = 0.0,
        lastHarvest = 0,
        hasIrrigation = true,
        hasStorage = true,
        hasTools = true
    },
    
    -- Visual properties
    visual = {
        base_color = {0.6, 0.4, 0.2}, -- Brown
        variants = {
            {name = "empty_plot", type = "empty"},
            {name = "planted_plot", type = "planted"},
            {name = "growing_plot", type = "growing"},
            {name = "mature_plot", type = "mature"},
            {name = "irrigated_plot", type = "irrigated"}
        },
        crop_colors = {
            {0.2, 0.8, 0.2}, -- Green
            {0.8, 0.2, 0.2}, -- Red
            {0.2, 0.2, 0.8}  -- Blue
        }
    },
    
    -- Sound properties
    sounds = {
        ambient = {
            "wind_rustling",
            "crop_waving",
            "farm_animals"
        },
        footsteps = {
            "dirt_step",
            "grass_step"
        }
    },
    
    -- Resources
    resources = {
        crops = {
            amount = 5,
            regenRate = 0.1,
            harvestAmount = 3
        },
        water = {
            amount = 10,
            regenRate = 0.2,
            harvestAmount = 1
        }
    },
    
    -- Effects
    effects = {
        irrigation = {
            active = true,
            efficiency = 0.8
        },
        fertilization = {
            active = false,
            strength = 0.0
        }
    },
    
    -- Spawn rules
    spawnRules = {
        crops = {
            chance = 0.8,
            minDistance = 1,
            maxDensity = 0.9
        },
        pests = {
            chance = 0.2,
            minDistance = 3,
            maxDensity = 0.3
        }
    },
    
    -- Weather effects
    weatherEffects = {
        rain = function(tile)
            -- Rain waters the crops
            tile.properties.waterLevel = math.min(1.0, tile.properties.waterLevel + 0.2)
            if tile.properties.growthStage == "growing" then
                tile.properties.growthStage = "mature"
                return "crop_growth"
            end
            return "water_increase"
        end,
        drought = function(tile)
            -- Drought reduces water and crop health
            tile.properties.waterLevel = math.max(0.0, tile.properties.waterLevel - 0.3)
            if tile.properties.waterLevel < 0.3 then
                tile.properties.growthStage = "wilted"
                return "crop_wilt"
            end
            return "water_decrease"
        end,
        storm = function(tile)
            -- Storms can damage crops
            if tile.properties.growthStage == "mature" then
                tile.properties.growthStage = "damaged"
                return "crop_damage"
            end
            return nil
        end
    },
    
    -- Time effects
    timeEffects = {
        day = function(tile)
            -- Daytime increases growth
            if tile.properties.growthStage == "growing" then
                tile.properties.growthStage = "mature"
                return "crop_growth"
            end
            return nil
        end,
        night = function(tile)
            -- Nighttime reduces water consumption
            tile.properties.waterLevel = math.min(1.0, tile.properties.waterLevel + 0.1)
            return "water_conserve"
        end
    }
}

-- Initialize the tile
function FarmsteadTile.init(tile, world)
    -- Initialize properties first
    tile.properties = tile.properties or {}
    
    -- Set random crop type if not set
    if not tile.properties.cropType then
        local cropTypes = {"wheat", "corn", "potatoes", "carrots", "tomatoes"}
        tile.properties.cropType = cropTypes[math.random(#cropTypes)]
    end
    
    -- Copy all fields from FarmsteadTile template to tile instance
    for k, v in pairs(FarmsteadTile) do
        if type(v) ~= "function" and tile[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                tile[k] = {}
                for subk, subv in pairs(v) do
                    tile[k][subk] = subv
                end
            else
                tile[k] = v
            end
        end
    end
    
    -- Set random variant
    if tile.visual and tile.visual.variants then
        local variants = tile.visual.variants
        tile.currentVariant = variants[math.random(#variants)]
    end
    
    -- Initialize resources
    if tile.resources then
        for resourceType, resource in pairs(tile.resources) do
            resource.currentAmount = resource.amount
            resource.lastHarvest = 0
            resource.growthStage = "mature"
        end
    end
    
    return tile
end

return FarmsteadTile 