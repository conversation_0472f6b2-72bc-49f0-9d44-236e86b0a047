-- items/weapons/fire_staff.lua

local FireStaff = {
    id = "fire_staff",
    name = "Fire Staff",
    type = "weapon",
    category = "staff",
    
    -- Properties
    properties = {
        damage = 30,
        attackSpeed = 1.0,
        range = 6.0,
        durability = 160,
        maxDurability = 160,
        fireballSpeed = 10,
        fireballSize = 1.2,
        fireballSpread = 0.2,
        burnChance = 0.3,
        burnDamage = 8,
        burnDuration = 4,
        burnRadius = 1.5,
        explosionChance = 0.2,
        explosionDamage = 25,
        explosionRadius = 2.5,
        explosionForce = 3.0,
        heatWaveChance = 0.15,
        heatWaveDamage = 15,
        heatWaveRadius = 3.0,
        heatWaveDuration = 2,
        fireCharge = 0,
        maxFireCharge = 100,
        chargeRate = 4,
        dischargeRate = 12,
        fireResistance = 0.6,
        temperature = 1.5
    },
    
    -- Appearance
    appearance = {
        sprite = "fire_staff",
        scale = 1.0,
        animations = {
            "idle",
            "cast",
            "charge",
            "discharge"
        },
        variants = {
            "ruby",
            "phoenix",
            "inferno",
            "dragon"
        },
        blendMode = "add",
        tint = {1.0, 0.4, 0.0},
        alpha = 0.9
    },
    
    -- Sound effects
    sounds = {
        cast = "fire_staff_cast",
        burn = "fire_staff_burn",
        explosion = "fire_staff_explosion",
        charge = "fire_staff_charge"
    },
    
    -- Effects
    effects = {
        burn = {
            type = "status",
            duration = 4,
            effects = {
                damage = 8,
                radius = 1.5
            }
        },
        heatWave = {
            type = "status",
            duration = 2,
            effects = {
                damage = 15,
                radius = 3.0,
                temperature = 1.0
            }
        },
        explosion = {
            type = "status",
            duration = 0.5,
            effects = {
                damage = 25,
                knockback = 3.0
            }
        }
    }
}

-- Initialize the weapon
function FireStaff.init(weapon, world)
    -- Copy all fields from FireStaff template to weapon instance
    for k, v in pairs(FireStaff) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        fireCharge = 0,
        lastChargeTime = 0,
        isCharging = false
    }

    return weapon
end

-- Update the weapon
function FireStaff.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Update fire charge
        if world.temperature and world.temperature > 0.5 then
            local chargeFactor = (world.temperature * 0.7 + 0.3) * dt
            weapon.properties.weaponState.fireCharge = math.min(weapon.properties.maxFireCharge,
                weapon.properties.weaponState.fireCharge + chargeFactor * weapon.properties.chargeRate)
        else
            weapon.properties.weaponState.fireCharge = math.max(0,
                weapon.properties.weaponState.fireCharge - weapon.properties.dischargeRate * dt)
        end
    end
end

-- Handle attack
function FireStaff.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Calculate fireball direction
    local angle = math.atan2(
        target.position.y - attacker.position.y,
        target.position.x - attacker.position.x
    )
    
    -- Add spread to fireball direction
    local spread = (math.random() - 0.5) * weapon.properties.fireballSpread
    angle = angle + spread
    
    -- Create fireball projectile
    if world.createProjectile then
        world.createProjectile({
            type = "fireball",
            position = attacker.position,
            direction = {
                x = math.cos(angle) * weapon.properties.fireballSpeed,
                y = math.sin(angle) * weapon.properties.fireballSpeed
            },
            damage = weapon.properties.damage,
            range = weapon.properties.range,
            size = weapon.properties.fireballSize
        })
    end
    
    -- Check for burn effect
    if math.random() < weapon.properties.burnChance then
        -- Create burn effect
        if world.createEffect then
            world.createEffect({
                type = "burn",
                position = target.position,
                radius = weapon.properties.burnRadius,
                duration = weapon.properties.burnDuration
            })
        end
        
        -- Apply burn effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.burn)
        end
        
        -- Play burn sound
        if world.playSound then
            world.playSound(weapon.sounds.burn)
        end
    end
    
    -- Check for explosion
    if math.random() < weapon.properties.explosionChance then
        -- Create explosion effect
        if world.createEffect then
            world.createEffect({
                type = "explosion",
                position = target.position,
                radius = weapon.properties.explosionRadius,
                damage = weapon.properties.explosionDamage,
                force = weapon.properties.explosionForce
            })
        end
        
        -- Apply explosion effect to nearby entities
        if world.entities then
            for _, entity in ipairs(world.entities) do
                if entity.position then
                    local distance = math.sqrt(
                        (entity.position.x - target.position.x)^2 + 
                        (entity.position.y - target.position.y)^2
                    )
                    
                    if distance <= weapon.properties.explosionRadius then
                        if entity.takeDamage then
                            entity.takeDamage(weapon.properties.explosionDamage)
                        end
                        
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.explosion)
                        end
                        
                        -- Apply knockback
                        if entity.move then
                            local force = (1 - distance / weapon.properties.explosionRadius) * 
                                weapon.properties.explosionForce
                            local angle = math.atan2(
                                entity.position.y - target.position.y,
                                entity.position.x - target.position.x
                            )
                            entity.move({
                                x = math.cos(angle) * force,
                                y = math.sin(angle) * force
                            })
                        end
                    end
                end
            end
        end
        
        -- Play explosion sound
        if world.playSound then
            world.playSound(weapon.sounds.explosion)
        end
    end
    
    -- Check for heat wave
    if math.random() < weapon.properties.heatWaveChance then
        -- Create heat wave effect
        if world.createEffect then
            world.createEffect({
                type = "heat_wave",
                position = target.position,
                radius = weapon.properties.heatWaveRadius,
                duration = weapon.properties.heatWaveDuration
            })
        end
        
        -- Apply heat wave effect to nearby entities
        if world.entities then
            for _, entity in ipairs(world.entities) do
                if entity.position then
                    local distance = math.sqrt(
                        (entity.position.x - target.position.x)^2 + 
                        (entity.position.y - target.position.y)^2
                    )
                    
                    if distance <= weapon.properties.heatWaveRadius then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.heatWave)
                        end
                    end
                end
            end
        end
    end
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function FireStaff.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough fire charge
    if weapon.properties.weaponState.fireCharge < 50 then
        return false
    end
    
    -- Create inferno effect
    if world.createEffect then
        world.createEffect({
            type = "inferno",
            position = attacker.position,
            radius = weapon.properties.heatWaveRadius * 2,
            duration = 4,
            damage = weapon.properties.damage * 2
        })
    end
    
    -- Apply fire effects to nearby entities
    if world.entities then
        for _, entity in ipairs(world.entities) do
            if entity.position then
                local distance = math.sqrt(
                    (entity.position.x - attacker.position.x)^2 + 
                    (entity.position.y - attacker.position.y)^2
                )
                
                if distance <= weapon.properties.heatWaveRadius * 2 then
                    -- Apply random fire effect
                    local effect = math.random()
                    if effect < 0.4 then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.burn)
                        end
                    elseif effect < 0.7 then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.heatWave)
                        end
                    else
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.explosion)
                        end
                    end
                end
            end
        end
    end
    
    -- Reset fire charge
    weapon.properties.weaponState.fireCharge = 0
    weapon.properties.weaponState.lastChargeTime = world.time
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 5)
    
    return true
end

return FireStaff
