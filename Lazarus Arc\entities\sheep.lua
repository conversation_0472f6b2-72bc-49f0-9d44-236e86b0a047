-- entities/sheep.lua
local Sheep = {
    id = "sheep",
    name = "Sheep",
    type = "sheep",
    -- PLACEHOLDER: Basic shape for visual representation
    shape = {
        {0, -1}, {0.8, -0.8}, {1, 0}, {0.8, 0.8},
        {0, 1}, {-0.8, 0.8}, {-1, 0}, {-0.8, -0.8}
    },
    size = 8,

    -- Entity categories
    categories = {"animal", "livestock", "mammal", "medium"},
    
    -- Threat categories
    threatCategories = {"predator", "monster"},
    
    -- Stats
    maxHealth = 25,
    health = 25,
    maxStamina = 35,
    stamina = 35,
    speed = 1.8,
    
    -- Behaviors
    behaviors = {"graze", "wander", "flee"},
    
    -- Behavior configurations
    behaviorConfigs = {
        graze = {
            moveSpeed = 0.8,
            grazeRadius = 6,
            grazeTime = 8
        },
        wander = {
            moveSpeed = 1.0,
            changeDirectionChance = 0.03,
            idleChance = 0.5
        },
        flee = {
            moveSpeed = 2.5,
            detectionRadius = 10,
            panicDuration = 5
        }
    },
    
    -- Appearance configuration
    appearance = {
        sprite = "sheep",
        scale = 1.1,
        animations = {
            "idle", "walk", "run", "graze"
        },
        variants = {
            "white", "brown", "black"
        }
    },
    
    -- Drops
    drops = {
        {id = "wool", chance = 1.0, quantity = {1, 3}},
        {id = "meat", chance = 0.8, quantity = {1, 2}},
        {id = "hide", chance = 0.7, quantity = {1, 1}}
    },

    -- Sound effects with synth configuration (gentle, pastoral sounds)
    sounds = {
        bleat = {
            file = "sheep_bleat",
            synth = {
                instrument = "cello",
                notes = {"G3", "E3"},
                durations = {0.3, 0.4},
                volume = 0.4,
                vibrato = true,
                vibratoRate = 4.0
            }
        },
        footstep = {
            file = "sheep_footstep",
            synth = {
                instrument = "marimba",
                notes = {"D3"},
                duration = 0.12,
                volume = 0.2
            }
        },
        hurt = {
            file = "sheep_hurt",
            synth = {
                instrument = "cello",
                notes = {"D#3"},
                duration = 0.4,
                volume = 0.45,
                vibrato = true,
                vibratoRate = 7.0
            }
        },
        death = {
            file = "sheep_death",
            synth = {
                instrument = "bass_guitar",
                notes = {"G3", "E3", "C3"},
                durations = {0.5, 0.5, 1.0},
                volume = 0.45,
                vibrato = true,
                vibratoRate = 2.0
            }
        },
        idle = {
            file = "sheep_idle",
            synth = {
                instrument = "classical_guitar",
                notes = {"G3"},
                duration = 0.6,
                volume = 0.25,
                vibrato = true,
                vibratoRate = 3.0
            }
        },
        graze = {
            file = "sheep_graze",
            synth = {
                instrument = "kalimba",
                notes = {"C4", "E4"},
                durations = {0.15, 0.15},
                volume = 0.2
            }
        },
        alert = {
            file = "sheep_alert",
            synth = {
                instrument = "cello",
                notes = {"A3", "G3"},
                durations = {0.2, 0.3},
                volume = 0.4
            }
        }
    }
}

function Sheep.init(entity, world)
    -- Copy all fields from Sheep template to entity instance
    for k, v in pairs(Sheep) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random sheep variant (with safety checks)
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

return Sheep
