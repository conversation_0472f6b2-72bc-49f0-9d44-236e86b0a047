-- entities/rabbit.lua
-- Enhanced rabbit with variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local Rabbit = {
    id = "rabbit",
    name = "Rabbit",
    type = "rabbit",
    -- PLACEHOLDER: Basic shape for visual representation
    shape = {
        {0, -1}, {0.8, -0.6}, {1, 0}, {0.8, 0.6},
        {0, 1}, {-0.8, 0.6}, {-1, 0}, {-0.8, -0.6}
    },
    size = 6,

    -- Entity categories
    categories = {"animal", "prey", "mammal", "small"},

    -- Threat categories
    threatCategories = {"predator", "player"},

    -- Stats
    maxHealth = 20,
    health = 20,
    maxStamina = 40,
    stamina = 40,
    speed = 3.0,
    
    -- Behaviors
    behaviors = {"flee", "wander", "graze"},
    
    -- Behavior configurations
    behaviorConfigs = {
        flee = {
            moveSpeed = 4.0,
            detectionRadius = 10,
            panicDuration = 3,
            zigzagChance = 0.3
        },
        wander = {
            moveSpeed = 1.5,
            changeDirectionChance = 0.05,
            wanderRadius = 5
        },
        graze = {
            moveSpeed = 0.5,
            grazeRadius = 3,
            grazeTime = {5, 10}
        }
    },
    
    -- Special abilities
    abilities = {
        hop = {
            speedBoost = 1.5,
            duration = 0.5,
            cooldown = 2.0
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "rabbit",
        scale = 0.8,
        animations = {
            "idle", "hop", "run", "graze"
        },
        variants = {
            "brown", "white", "gray", "black"
        }
    },
    
    -- Sound effects with synth configuration
    sounds = {
        hop = {
            file = "rabbit_hop", -- fallback to audio file
            synth = {
                instrument = "kalimba",
                notes = {"C4"},
                duration = 0.15,
                volume = 0.2
            }
        },
        squeak = {
            file = "rabbit_squeak",
            synth = {
                instrument = "kalimba",
                notes = {"A4", "C5"},
                durations = {0.2, 0.3},
                volume = 0.3,
                vibrato = true,
                vibratoRate = 6.0
            }
        },
        footstep = {
            file = "rabbit_footstep",
            synth = {
                instrument = "marimba",
                notes = {"C3"},
                duration = 0.1,
                volume = 0.15
            }
        },
        hurt = {
            file = "rabbit_hurt",
            synth = {
                instrument = "kalimba",
                notes = {"D#4"},
                duration = 0.25,
                volume = 0.4,
                vibrato = true,
                vibratoRate = 8.0
            }
        },
        death = {
            file = "rabbit_death",
            synth = {
                instrument = "bass_guitar",
                notes = {"F3"},
                duration = 1.0,
                volume = 0.4,
                vibrato = true,
                vibratoRate = 3.0
            }
        },
        idle = {
            file = "rabbit_idle",
            synth = {
                instrument = "kalimba",
                notes = {"A4"},
                duration = 0.5,
                volume = 0.2
            }
        },
        alert = {
            file = "rabbit_alert",
            synth = {
                instrument = "kalimba",
                notes = {"E5", "G5"},
                durations = {0.15, 0.2},
                volume = 0.35
            }
        }
    },
    
    -- === ENHANCED VARIANT SYSTEM ===
    variantChances = {
        normal = 0.85,          -- 85% normal rabbit
        shiny = 0.12,           -- 12% golden rabbit (shiny)
        rare = 0.025,           -- 2.5% moon rabbit (rare)
        legendary = 0.005       -- 0.5% mythical rabbit (legendary)
    },

    variants = {
        normal = {
            name = "Common Rabbit",
            description = "A typical woodland rabbit",
            statModifiers = {},
            appearanceModifiers = {}
        },

        shiny = {
            name = "Golden Rabbit",
            description = "A rare rabbit with shimmering golden fur",
            statModifiers = {
                maxHealth = 1.5,    -- 30 health instead of 20
                speed = 1.3,        -- 3.9 speed instead of 3.0
                maxStamina = 1.4    -- 56 stamina instead of 40
            },
            appearanceModifiers = {
                scale = 1.1,
                glow = true,
                colorTint = {1.4, 1.2, 0.6, 1.0},  -- Golden fur
                sparkles = true
            },
            soundModifiers = {
                pitch = 1.3,
                volume = 1.1,
                magical_chime = true
            }
        },

        rare = {
            name = "Moon Rabbit",
            description = "A mystical rabbit blessed by lunar magic",
            statModifiers = {
                maxHealth = 2.0,    -- 40 health
                speed = 1.5,        -- 4.5 speed
                maxStamina = 1.8,   -- 72 stamina
                lunar_magic = 1.0   -- New stat
            },
            appearanceModifiers = {
                scale = 1.15,
                colorTint = {1.1, 1.1, 1.4, 1.0},  -- Silvery-blue
                lunar_markings = true,
                moon_glow = true
            },
            soundModifiers = {
                pitch = 1.1,
                volume = 1.2,
                reverb = true,
                ethereal = true
            }
        },

        legendary = {
            name = "Mythical Rabbit",
            description = "A legendary rabbit of ancient folklore",
            statModifiers = {
                maxHealth = 3.0,    -- 60 health
                speed = 2.0,        -- 6.0 speed
                maxStamina = 2.5,   -- 100 stamina
                mythical_power = 2.0
            },
            appearanceModifiers = {
                scale = 1.3,
                glow = true,
                colorTint = {1.5, 1.3, 1.5, 1.0},  -- Iridescent
                rainbow_aura = true,
                mythical_presence = "legendary"
            },
            soundModifiers = {
                pitch = 0.9,
                volume = 1.5,
                reverb = true,
                echo = true,
                mythical_harmony = true
            }
        }
    },

    -- === ENHANCED DROP SYSTEM ===

    -- Base drops (available from all variants)
    baseDrops = {
        {id = "meat", chance = 0.8, quantity = {1, 1}},
        {id = "hide", chance = 0.6, quantity = {1, 1}},
        {id = "fur", chance = 0.7, quantity = {1, 2}},
        {id = "bone", chance = 0.4, quantity = {1, 1}}
    },

    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "golden_fur", chance = 0.9, quantity = {1, 2}},
            {id = "lucky_rabbit_foot", chance = 0.7, quantity = {1, 1}},
            {id = "golden_rabbit_essence", chance = 0.5, quantity = {1, 1}}
        },
        rare = {
            {id = "moon_blessed_fur", chance = 0.8, quantity = {1, 2}},
            {id = "lunar_essence", chance = 0.7, quantity = {1, 1}},
            {id = "moon_rabbit_charm", chance = 0.6, quantity = {1, 1}},
            {id = "celestial_whisker", chance = 0.4, quantity = {1, 1}}
        },
        legendary = {
            {id = "mythical_rabbit_pelt", chance = 0.9, quantity = {1, 1}},
            {id = "legend_essence", chance = 0.8, quantity = {1, 2}},
            {id = "mythical_rabbit_horn", chance = 0.6, quantity = {1, 1}},
            {id = "folklore_fragment", chance = 0.5, quantity = {1, 1}},
            {id = "rainbow_rabbit_tail", chance = 0.3, quantity = {1, 1}}
        }
    }
}

-- Initialize the entity using enhanced template
function Rabbit.init(entity, world)
    -- Copy all fields from Rabbit template to entity instance
    for k, v in pairs(Rabbit) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return Rabbit
