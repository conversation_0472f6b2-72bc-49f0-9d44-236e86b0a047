-- items/armor/iron_shield.lua

local Shield = {
    id = "iron_shield",
    name = "Iron Shield",
    description = "A sturdy shield made of iron that provides good protection.",
    type = "shield",
    category = "shield",
    stackable = false,

    stats = {
        defense = 10.0,
        block_chance = 0.25,
        durability = 120.0,
        repair_cost = 30,
        weight = 8.0,
    },

    material = "iron",

    sprite = "res://Sprite/items/armor/iron_shield.png",
    size = { width = 64, height = 64 },

    effects = {
        projectile_resistance = 0.15,
        bash_damage = 5.0
    },

    meta = {
        rarity = "uncommon"
    },

    take_damage = function(self, amount)
        self.stats.durability = math.max(0, self.stats.durability - amount)
        if self.stats.durability == 0 then
            print(self.name .. " is broken! Penalties applied.")
            -- Potential penalties could be applied here
        end
    end,

    repair = function(self)
        local repair_amount = 120 - self.stats.durability
        self.stats.durability = 120
        print(self.name .. " repaired by " .. repair_amount .. " points.")
    end,
    
    onUse = function(self, user, target)
        -- Shield bash attack
        if user and target then
            print(user.name .. " bashes " .. target.name .. " with the shield!")
            -- In a real implementation, this would apply damage and possibly a stun effect
            if target.takeDamage then
                target:takeDamage(self.effects.bash_damage, "physical")
            end
            
            -- Chance to stun
            local roll = math.random()
            if roll <= 0.2 then -- 20% chance to stun
                print(target.name .. " is stunned by the shield bash!")
                if target.applyEffect then
                    target:applyEffect("stunned", 1.5) -- Stun for 1.5 seconds
                end
            end
            
            return true
        end
        return false
    end,
    
    onBlock = function(self, user, attacker, damage)
        -- Successful block
        print(user.name .. " blocks " .. attacker.name .. "'s attack with the shield!")
        
        -- Reduce shield durability
        self:take_damage(math.max(1, damage * 0.2))
        
        -- Return the amount of damage blocked
        return damage * 0.8 -- Block 80% of the damage
    end
}

return Shield
