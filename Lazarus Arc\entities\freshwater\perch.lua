local Perch = {
    id = "perch",
    name = "Yellow Perch",
    type = "fish",
    
    -- Entity categories
    categories = {"animal", "fish", "freshwater", "predator", "schooling"},
    
    -- Threat and food categories
    threatCategories = {"player", "predator", "fish"},
    foodCategories = {"insect", "fish", "small_prey", "plant"},
    
    -- Stats
    maxHealth = 25,
    health = 25,
    maxStamina = 45,
    stamina = 45,
    speed = 2.0,
    
    -- Water properties
    water = {
        swimSpeed = 2.0,
        maxDepth = 6,
        preferredDepth = 2,
        oxygenLevel = 1.0,
        temperature = "cool",
        currentSpeed = 1.0
    },
    
    -- Behaviors
    behaviors = {"hunt", "patrol", "school", "flee"},
    
    -- Behavior configurations
    behaviorConfigs = {
        hunt = {
            moveSpeed = 2.2,
            searchRadius = 10,
            attackRange = 1.2,
            damage = 12,
            preferredPrey = {"insect", "fish", "small_prey"},
            successChance = 0.7,
            schoolBonus = 0.15
        },
        patrol = {
            moveSpeed = 1.8,
            patrolRadius = 15,
            preferredDepth = 2,
            restInterval = {6, 10}
        },
        school = {
            moveSpeed = 1.9,
            followDistance = 1.5,
            separationDistance = 1.0,
            alignmentStrength = 0.5,
            cohesionStrength = 0.6,
            maxGroupSize = 15
        },
        flee = {
            moveSpeed = 2.5,
            detectionRange = 8,
            escapeDistance = 12,
            successChance = 0.8
        }
    },
    
    -- Special abilities
    abilities = {
        dart = {
            speed = 2.8,
            duration = 0.3,
            cooldown = 1.5,
            staminaCost = 5
        },
        flash = {
            range = 2,
            duration = 0.2,
            cooldown = 2,
            effect = "confuse"
        },
        schoolBoost = {
            speed = 1.2,
            duration = 3,
            cooldown = 4,
            effect = "group"
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "perch",
        scale = 0.9,
        animations = {
            "idle", "swim", "dart", "school"
        },
        variants = {
            "yellow", "green", "striped", "dark"
        }
    },
    
    -- Sound effects
    sounds = {
        splash = "fish_splash",
        swim = "fish_swim",
        dart = "fish_dart"
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.5, quantity = {1, 1}},
        {id = "scale", chance = 0.4, quantity = {1, 2}},
        {id = "fin", chance = 0.2, quantity = {1, 1}},
        {id = "bone", chance = 0.3, quantity = {1, 1}}
    }
}

-- Initialize the entity
function Perch.init(entity, world)
    -- Copy all fields from Perch template to entity instance
    for k, v in pairs(Perch) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random perch variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Perch.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update water properties
    if entity.water then
        -- Adjust hunting success based on time of day
        if world and world.timeOfDay == "dawn" or world.timeOfDay == "dusk" then
            entity.behaviorConfigs.hunt.successChance = 0.8
        else
            entity.behaviorConfigs.hunt.successChance = 0.7
        end

        -- Adjust preferred depth based on time of day
        if world and world.timeOfDay == "night" then
            entity.water.preferredDepth = 1
        else
            entity.water.preferredDepth = 2
        end

        -- Apply school bonus to hunting
        if world and world.entities then
            local nearbySchool = 0
            for _, other in ipairs(world.entities) do
                if other.type == "fish" and other.id == "perch" then
                    local distance = math.sqrt(
                        (other.position.x - entity.position.x)^2 + 
                        (other.position.y - entity.position.y)^2
                    )
                    if distance < 5 then
                        nearbySchool = nearbySchool + 1
                    end
                end
            end
            if nearbySchool > 0 then
                entity.behaviorConfigs.hunt.successChance = 
                    entity.behaviorConfigs.hunt.successChance + 
                    entity.behaviorConfigs.hunt.schoolBonus
            end
        end
    end
end

return Perch