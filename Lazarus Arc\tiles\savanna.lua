-- tiles/savanna.lua
local SavannaTile = {
    id = "savanna",
    name = "Savanna",
    passable = true,
    variants = 4, -- Different grass patterns and densities
    
    -- Properties
    properties = {
        grassDensity = 0.7,
        grassHeight = 1.0,
        temperature = 25,
        humidity = 0.4,
        windStrength = 0.6,
        fireRisk = 0.3,
        hasTermiteMounds = true,
        hasAcaciaTrees = true,
        hasWildlife = true
    },
    
    -- Visual properties
    visual = {
        base_color = {0.8, 0.7, 0.3}, -- Golden yellow
        variants = {
            {name = "tall_grass", type = "grass"},
            {name = "sparse_grass", type = "grass"},
            {name = "acacia_shade", type = "shade"},
            {name = "termite_mound", type = "feature"}
        },
        grass_colors = {
            {0.8, 0.7, 0.3}, -- Golden
            {0.7, 0.6, 0.2}, -- Drier
            {0.9, 0.8, 0.4}  -- Lighter
        }
    },
    
    -- Sound properties
    sounds = {
        ambient = {
            "wind_rustling",
            "grass_waving",
            "distant_animals"
        },
        footsteps = {
            "grass_step",
            "dirt_step"
        }
    },
    
    -- Resources
    resources = {
        grass = {
            amount = 5,
            regenRate = 0.2,
            harvestAmount = 1
        },
        termite_mound = {
            amount = 1,
            regenRate = 0,
            harvestAmount = 1
        }
    },
    
    -- Effects
    effects = {
        wind = {
            strength = 0.6,
            direction = "random",
            grassWave = true
        },
        heat = {
            intensity = 0.7,
            dehydration = 0.1
        }
    },
    
    -- Spawn rules
    spawnRules = {
        grass = {
            chance = 0.7,
            minDistance = 1,
            maxDensity = 0.8
        },
        termite_mound = {
            chance = 0.2,
            minDistance = 5,
            maxDensity = 0.3
        },
        acacia = {
            chance = 0.3,
            minDistance = 3,
            maxDensity = 0.4
        }
    },
    
    -- Weather effects
    weatherEffects = {
        rain = function(tile)
            -- Rain temporarily increases grass density
            tile.properties.grassDensity = math.min(1.0, tile.properties.grassDensity + 0.1)
            return "grass_growth"
        end,
        drought = function(tile)
            -- Drought reduces grass density and increases fire risk
            tile.properties.grassDensity = math.max(0.3, tile.properties.grassDensity - 0.2)
            tile.properties.fireRisk = math.min(1.0, tile.properties.fireRisk + 0.2)
            return "grass_wither"
        end,
        wind = function(tile)
            -- Strong winds can spread fire
            if tile.properties.fireRisk > 0.5 then
                tile.properties.fireRisk = math.min(1.0, tile.properties.fireRisk + 0.1)
                return "fire_risk_increase"
            end
            return nil
        end
    },
    
    -- Time effects
    timeEffects = {
        day = function(tile)
            -- Daytime increases temperature and fire risk
            tile.properties.temperature = math.min(35, tile.properties.temperature + 2)
            tile.properties.fireRisk = math.min(1.0, tile.properties.fireRisk + 0.1)
            return "heat_increase"
        end,
        night = function(tile)
            -- Nighttime cools down and reduces fire risk
            tile.properties.temperature = math.max(15, tile.properties.temperature - 5)
            tile.properties.fireRisk = math.max(0, tile.properties.fireRisk - 0.2)
            return "cool_down"
        end
    }
}

-- Initialize the tile
function SavannaTile.init(tile, world)
    -- Initialize properties first
    tile.properties = tile.properties or {}
    
    -- Copy all fields from SavannaTile template to tile instance
    for k, v in pairs(SavannaTile) do
        if type(v) ~= "function" and tile[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                tile[k] = {}
                for subk, subv in pairs(v) do
                    tile[k][subk] = subv
                end
            else
                tile[k] = v
            end
        end
    end
    
    -- Set random variant
    if tile.visual and tile.visual.variants then
        local variants = tile.visual.variants
        tile.currentVariant = variants[math.random(#variants)]
    end
    
    -- Initialize resources
    if tile.resources then
        for resourceType, resource in pairs(tile.resources) do
            resource.currentAmount = resource.amount
            resource.lastHarvest = 0
            resource.growthStage = "mature"
        end
    end
    
    return tile
end

return SavannaTile 