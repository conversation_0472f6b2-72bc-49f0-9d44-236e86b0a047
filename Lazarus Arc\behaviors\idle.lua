-- behaviors/idle.lua
-- A behavior for entities to perform idle animations and actions

local IdleBehavior = {}
IdleBehavior.id = "idle"
IdleBehavior.name = "Idle Behavior"
IdleBehavior.description = "Entity will perform idle animations and small actions when not busy"

-- Configuration options
IdleBehavior.defaults = {
    idleAnimations = {"idle", "look_around", "scratch", "yawn"},
    idleTime = {min = 3, max = 8},
    lookChance = 0.3,
    lookRadius = 6,
    wanderChance = 0.2,
    wanderDistance = 2,
    sitChance = 0.15,
    sitDuration = {min = 5, max = 15},
    reactToEvents = true,
    eventReactRadius = 10,
    ambientSounds = true,
    timeBasedBehavior = true,
    weatherReactions = true
}

-- Initialization function called when behavior is applied to an entity
function IdleBehavior.init(entity, options)
    options = options or {}
    
    -- Ensure entity has a valid position
    if not entity.position then
        entity.position = { x = 0, y = 0 }
    end

    -- Create the behavior state in the entity
    entity.idleBehavior = {
        idleAnimations = options.idleAnimations or IdleBehavior.defaults.idleAnimations,
        idleTime = options.idleTime or IdleBehavior.defaults.idleTime,
        lookChance = options.lookChance or IdleBehavior.defaults.lookChance,
        lookRadius = options.lookRadius or IdleBehavior.defaults.lookRadius,
        wanderChance = options.wanderChance or IdleBehavior.defaults.wanderChance,
        wanderDistance = options.wanderDistance or IdleBehavior.defaults.wanderDistance,
        sitChance = options.sitChance or IdleBehavior.defaults.sitChance,
        sitDuration = options.sitDuration or IdleBehavior.defaults.sitDuration,
        reactToEvents = options.reactToEvents or IdleBehavior.defaults.reactToEvents,
        eventReactRadius = options.eventReactRadius or IdleBehavior.defaults.eventReactRadius,
        ambientSounds = options.ambientSounds or IdleBehavior.defaults.ambientSounds,
        timeBasedBehavior = options.timeBasedBehavior or IdleBehavior.defaults.timeBasedBehavior,
        weatherReactions = options.weatherReactions or IdleBehavior.defaults.weatherReactions,
        
        idleTimer = 0,                -- Time until next idle action
        currentAnimation = "idle",    -- Current animation playing
        isIdle = true,                -- Whether entity is currently idle
        isSitting = false,            -- Whether entity is sitting
        sitTimer = 0,                 -- How long entity has been sitting
        homePosition = {              -- Base position for idle wandering
            x = entity.position.x,
            y = entity.position.y
        },
        lookTarget = nil,             -- What the entity is looking at
        lookTimer = 0,                -- How long to look at current target
        wanderTarget = nil,           -- Where entity is wandering to
        recentEvents = {},            -- Recent events the entity has reacted to
        lastSoundTime = 0,            -- When entity last made a sound
        active = true,                -- Whether this behavior is active
        priority = 1                  -- Low priority, other behaviors override this
    }
    
    -- Set initial idle timer
    entity.idleBehavior.idleTimer = math.random(entity.idleBehavior.idleTime.min, entity.idleBehavior.idleTime.max)
    
    -- Store entity type for type-specific behaviors
    entity.idleBehavior.entityType = entity.type or "generic"
    
    -- Add type-specific idle animations if available
    if entity.type then
        if entity.type == "rabbit" then
            table.insert(entity.idleBehavior.idleAnimations, "clean_ears")
            table.insert(entity.idleBehavior.idleAnimations, "hop_in_place")
        elseif entity.type == "cat" or entity.type == "fox" then
            table.insert(entity.idleBehavior.idleAnimations, "clean_paws")
            table.insert(entity.idleBehavior.idleAnimations, "tail_flick")
        elseif entity.type == "dog" or entity.type == "wolf" then
            table.insert(entity.idleBehavior.idleAnimations, "paw_scratch")
            table.insert(entity.idleBehavior.idleAnimations, "tail_wag")
        elseif entity.type == "bird" then
            table.insert(entity.idleBehavior.idleAnimations, "preen_feathers")
            table.insert(entity.idleBehavior.idleAnimations, "flutter_wings")
        elseif entity.type == "npc" or entity.type == "human" then
            table.insert(entity.idleBehavior.idleAnimations, "stretch")
            table.insert(entity.idleBehavior.idleAnimations, "adjust_clothes")
            table.insert(entity.idleBehavior.idleAnimations, "check_inventory")
        end
    end
    
    print("Idle behavior initialized for entity " .. entity.id)
end

-- (The rest of the file's update functions and other methods remain unchanged)

return IdleBehavior
