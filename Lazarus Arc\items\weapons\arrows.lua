-- items/weapons/arrows.lua

local Ammo = {
    id = "arrows",
    name = "Arrows",
    description = "Standard arrows for use with bows.",
    type = "ammo",
    category = "weapon",
    stackable = true,
    maxStack = 99,

    stats = {
        damage_bonus = 0.0,
        weight = 0.1,
    },

    damage_type = "physical",
    material = "wood",

    sprite = "res://Sprite/items/weapons/arrows.png",
    size = { width = 32, height = 32 },

    effects = {
        piercing = 0.02
    },

    meta = {
        rarity = "common"
    },
    
    onUse = function(self, user)
        print("You need a bow to use arrows.")
        return false
    end
}

return Ammo
