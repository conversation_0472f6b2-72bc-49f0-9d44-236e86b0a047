local Plecostomus = {
    id = "plecostomus",
    name = "Plecostomus",
    type = "plecostomus",

    -- Entity categories
    categories = { "animal", "fish", "medium", "bottom_feeder", "cleaner" },

    -- Threat categories
    threatCategories = { "predator", "large" },
    foodCategories = { "algae", "detritus" },

    -- Stats
    maxHealth = 20,
    health = 20,
    speed = 1.8, -- Slow swimmer
    armor = 0.2, -- Has bony plates for protection

    -- Behaviors
    behaviors = { "foraging", "flee" },

    -- Behavior configurations
    behaviorConfigs = {
        觅食 = {
            foodTypes = { "algae", "detritus" },
            foodRadius = 7,
            bottomFeeding = true
        },
        flee = {
            useCategories = true,
            moveSpeed = 3.5,
            detectionRadius = 6
        }
    },

    -- Appearance
    appearance = {
        sprite = "plecostomus", -- Replace with your plecostomus sprite
        scale = 1.1,
        animations = {
            "swim",
            "flee",
            "eat"
        }
    },

    -- Sound effects
    sounds = {
        -- May not have distinct sounds
    }
}

-- Initialize the plecostomus entity
function Plecostomus.init(entity, world)
    entity.position = entity.position or { x = 0, y = 0 }
    entity.velocity = entity.velocity or { x = 0, y = 0 }

    -- Initialize behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.init then
            behavior.init(entity, entity.behaviorConfigs[behaviorName])
        end
    end

    return entity
end

return Plecostomus