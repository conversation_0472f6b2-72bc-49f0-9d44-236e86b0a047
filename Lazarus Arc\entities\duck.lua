local Duck = {
    id = "duck",
    name = "<PERSON>",
    type = "bird",
    shape = {
        {0, -1}, {1, 0}, {0, 1}, {-1, 0}
    },
    size = 6,
    
    -- Entity categories
    categories = {"animal", "bird", "flying", "water", "social"},
    
    -- Threat and food categories
    threatCategories = {"player", "predator"},
    foodCategories = {"plant", "insect", "fish", "food"},
    
    -- Stats
    maxHealth = 25,
    health = 25,
    maxStamina = 50,
    stamina = 50,
    speed = 1.5,
    
    -- Flight properties
    flight = {
        maxHeight = 8,
        minHeight = 1,
        ascentSpeed = 0.6,
        descentSpeed = 1.0,
        hoverHeight = 2,
        currentHeight = 2,
        wingFlapRate = 0.15,
        soarChance = 0.2
    },
    
    -- Water properties
    water = {
        swimSpeed = 1.2,
        diveDepth = 3,
        diveDuration = 5,
        forageRange = 4
    },
    
    -- Behaviors
    behaviors = {"forage", "swim", "flock", "roost"},
    
    -- Behavior configurations
    behaviorConfigs = {
        forage = {
            moveSpeed = 1.0,
            searchRadius = 8,
            preferredFood = {"plant", "insect", "fish"},
            diveChance = 0.3,
            successChance = 0.6
        },
        swim = {
            moveSpeed = 1.2,
            preferredTerrain = "water",
            groupSpacing = 2,
            directionChange = 0.1
        },
        flock = {
            moveSpeed = 1.5,
            followDistance = 3,
            separationDistance = 1.5,
            alignmentStrength = 0.3,
            cohesionStrength = 0.4,
            maxGroupSize = 12,
            preferredTime = "day"
        },
        roost = {
            startTime = "dusk",
            endTime = "dawn",
            roostHeight = {1, 2},
            healthRegen = 0.02,
            staminaRegen = 0.1
        }
    },
    
    -- Special abilities
    abilities = {
        dive = {
            speed = 1.5,
            duration = 5,
            cooldown = 8,
            staminaCost = 10
        },
        quack = {
            range = 6,
            duration = 0.5,
            cooldown = 2,
            effect = "alert"
        },
        preen = {
            duration = 3,
            cooldown = 5,
            healthRegen = 0.05
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "duck",
        scale = 0.8,
        animations = {
            "idle", "fly", "swim", "dive", "forage", "preen"
        },
        variants = {
            "mallard", "wood", "teal", "pintail"
        }
    },
    
    -- Sound effects with synth configuration
    sounds = {
        quack = {
            file = "duck_quack",
            synth = {
                instrument = "dulcimer",
                notes = {"F3", "D3"},
                durations = {0.2, 0.3},
                volume = 0.4,
                vibrato = true,
                vibratoRate = 4.0
            }
        },
        wingFlap = {
            file = "duck_wing_flap",
            synth = {
                instrument = "marimba",
                notes = {"D3", "F3", "D3"},
                durations = {0.1, 0.08, 0.1},
                volume = 0.3
            }
        },
        splash = {
            file = "duck_splash",
            synth = {
                instrument = "xylophone",
                notes = {"G4", "E4", "C4"},
                durations = {0.1, 0.1, 0.15},
                volume = 0.35
            }
        },
        footstep = {
            synth = {
                instrument = "kalimba",
                notes = {"D3"},
                duration = 0.08,
                volume = 0.15
            }
        },
        fly = {
            synth = {
                instrument = "harmonica",
                notes = {"F3", "A3"},
                durations = {0.2, 0.2},
                volume = 0.3
            }
        },
        swim = {
            synth = {
                instrument = "vibraphone",
                notes = {"A3", "C4"},
                durations = {0.3, 0.3},
                volume = 0.25
            }
        },
        hurt = {
            synth = {
                instrument = "dulcimer",
                notes = {"D#3"},
                duration = 0.3,
                volume = 0.45,
                vibrato = true,
                vibratoRate = 8.0
            }
        },
        death = {
            synth = {
                instrument = "cello",
                notes = {"F3", "D3", "A2"},
                durations = {0.4, 0.4, 0.8},
                volume = 0.45,
                vibrato = true,
                vibratoRate = 2.0
            }
        },
        dive = {
            synth = {
                instrument = "vibraphone",
                notes = {"C4", "A3", "F3"},
                durations = {0.2, 0.2, 0.3},
                volume = 0.3
            }
        }
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.5, quantity = {1, 2}},
        {id = "feather", chance = 0.6, quantity = {1, 3}},
        {id = "egg", chance = 0.3, quantity = {1, 1}},
        {id = "down", chance = 0.4, quantity = {1, 2}}
    }
}

-- Initialize the entity
function Duck.init(entity, world)
    -- Copy all fields from Duck template to entity instance
    for k, v in pairs(Duck) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random duck variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Duck.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update flight properties
    if entity.flight then
        -- Update wing flapping based on height and activity
        if entity.flight.currentHeight > entity.flight.minHeight then
            if entity.flight.soarChance > math.random() then
                entity.flight.wingFlapRate = 0.08
            else
                entity.flight.wingFlapRate = 0.15
            end
        else
            entity.flight.wingFlapRate = 0.25
        end
    end

    -- Update water properties
    if entity.water then
        -- Adjust dive duration based on time of day
        if world and world.timeOfDay == "dawn" or world.timeOfDay == "dusk" then
            entity.water.diveDuration = 7
        else
            entity.water.diveDuration = 5
        end
    end
end

return Duck 