# Complete Entity System Guide

## 🌟 **Enhanced Entity System - Full Implementation**

This guide covers the complete enhanced entity system with **shiny variants**, **rare drops**, **biome-based spawning**, and **comprehensive testing tools**.

## 📋 **Complete Entity List**

### **🐾 Animals (Updated with Enhanced System)**
- **🐰 Rabbit** - Golden → Moon → Mythical variants
- **🦊 Fox** - Silver → Arctic → Spirit variants  
- **🐺 Wolf** - Dire → Alpha → Fenrir variants
- **🐻 Bear** - Grizzly → Cave → Great Bear Spirit variants
- **🦌 Deer** - Golden → Stag Lord → Forest Guardian variants
- **🐦 Crow** - Raven → Wise → Odin's Raven variants

### **🧝 Fantasy Races (New)**
- **🧝 Elf** - High Elf → Ancient Elf → Archelf
- **🏔️ Dwarf** - Forge Master → Runebeard → Dwarf King
- **⚔️ Orc** - War Chief → Berserker → Orc Warlord
- **😈 Tiefling** - Noble Tiefling → Cambion → Archfiend
- **🍀 Halfling** - <PERSON> Halfling → Hero Halfling → Halfling Legend
- **🤖 Robot** - Advanced Model → Prototype → AI Overlord

### **💀 Undead Entities (New)**
- **🐺 Undead Wolf** - Skeletal → Wraith → Lich Wolf
- **🐻 Undead Bear** - Bone Bear → Plague Bear → Death Lord Bear
- **⚔️ Skeleton Warrior** - Knight → Captain → Lich King

### **🌊 Underwater Entities (New)**
- **🦈 Shark** - Great White → Tiger Shark → Megalodon
- **🐙 Octopus** - Giant Pacific → Mimic → Kraken Spawn
- **🐢 Sea Turtle** - Leatherback → Ancient → World Turtle

### **🦅 Flying Entities (New)**
- **🔥 Phoenix** - Ice Phoenix → Storm Phoenix → Cosmic Phoenix
- **🐉 Wyvern** - Frost → Shadow → Ancient Wyvern

### **🕳️ Underground Entities (New)**
- **🦔 Mole** - Star-nosed → Giant → Tunnel King
- **👹 Cave Troll** - Crystal → Magma → Mountain Heart Troll

### **🐛 Insects (New)**
- **🪲 Beetle** - Rhinoceros → Titan → Scarab God
- **🕷️ Spider** - Black Widow → Giant Tarantula → Arachne

### **👑 Boss Entities (New)**
- **🌳 Forest Guardian Boss** - World Tree → Corrupted → Primordial Guardian
- **👤 Shadow Lord Boss** - Void Lord → Nightmare Lord → Darkness Incarnate

## 🎲 **Variant System Features**

### **Rarity Tiers & Probabilities**
- **Normal** (65-85%) - Standard entity with base stats
- **Shiny** (12-25%) - Enhanced stats, visual glow, better drops
- **Rare** (4-8%) - Significantly stronger, unique abilities, rare materials
- **Legendary** (1-3%) - Mythical power, reality-altering drops, epic encounters

### **Enhanced Features Per Variant**
- **Stat Scaling** - Health, attack, speed, defense multipliers
- **Visual Effects** - Scale changes, glow, color tints, special auras
- **Audio Changes** - Pitch, volume, reverb, unique sound effects
- **Unique Drops** - Variant-exclusive materials and artifacts

## 🗺️ **Biome-Based Spawning System**

### **Forest Biome**
- **Common**: Rabbit, Deer, Squirrel, Crow, Butterfly
- **Uncommon**: Fox, Wolf, Bear, Eagle, Hawk, Owl
- **Rare**: Elf, Forest Guardian Boss

### **Mountains Biome**
- **Common**: Eagle, Hawk, Crow, Mole
- **Uncommon**: Bear, Wolf, Dwarf, Cave Troll
- **Rare**: Wyvern, Phoenix

### **Underground Biome**
- **Common**: Mole, Beetle, Spider
- **Uncommon**: Cave Troll, Dwarf, Skeleton Warrior
- **Rare**: Undead Bear, Shadow Lord Boss

### **Ocean Biome**
- **Common**: Crab, Sea Turtle
- **Uncommon**: Shark, Octopus
- **Rare**: Ice Phoenix

### **Swamp Biome**
- **Common**: Frog, Fly, Crow
- **Uncommon**: Undead Wolf, Skeleton Warrior
- **Rare**: Undead Bear, Shadow Lord Boss

## 🎮 **Using the Entity System**

### **Basic Spawning**
```lua
local EntitySpawner = require("entities.entity_spawner")

-- Spawn random entity for biome
local entity = EntitySpawner.spawnEntity(world, x, y, {
    biome = "forest",
    difficulty = "normal"
})

-- Spawn specific entity and variant
local entity = EntitySpawner.spawnEntity(world, x, y, {
    forceEntity = "wolf",
    forceVariant = "legendary"
})
```

### **Event-Based Spawning**
```lua
-- Blood moon event (more undead, higher rare chances)
local entity = EntitySpawner.spawnEntity(world, x, y, {
    event = "blood_moon",
    biome = "swamp"
})

-- Shiny event (much higher shiny variant chances)
local entity = EntitySpawner.spawnEntity(world, x, y, {
    event = "shiny_event"
})
```

### **Group Spawning**
```lua
-- Spawn pack of wolves
local pack = EntitySpawner.spawnGroup(world, x, y, {
    entityType = "wolf",
    count = 5,
    radius = 15
})
```

## 🔧 **Debug and Testing System**

### **Testing All Variants**
```lua
local EntityDebugSystem = require("entities.entity_debug_system")

-- Test all variants of all entities
EntityDebugSystem.testAllVariants(world, 0, 0)

-- Test specific entity variants
EntityDebugSystem.spawnTestEntity(world, "wolf", "legendary", 0, 0)
```

### **Testing Spawn Rates**
```lua
-- Test biome spawn distribution
EntityDebugSystem.testBiomeSpawns(world, "forest", 100)

-- Test drop rates
EntityDebugSystem.testDropRates("wolf", "legendary", 100)

-- Generate comprehensive report
EntityDebugSystem.generateSpawnReport(world)
```

### **Quick Debug Commands**
```lua
-- Spawn for testing
EntityDebugSystem.quickTest(world, "phoenix", "cosmic")

-- Get detailed entity info
EntityDebugSystem.printEntityInfo(entity)
```

## 💎 **Drop System Examples**

### **Legendary Wolf (Fenrir) Drops**
- `fenrir_pelt` (95% chance) - Legendary crafting material
- `chain_of_fate_link` (90% chance) - Mythical binding material
- `world_ending_howl` (80% chance) - Reality-altering essence
- `apocalypse_fragment` (40% chance) - Ultimate rare material

### **Cosmic Phoenix Drops**
- `cosmic_phoenix_plume` (95% chance) - Stellar feather
- `star_essence` (90% chance) - Cosmic energy
- `reality_transcendence_gem` (60% chance) - Dimension-bending crystal

## 🎯 **Integration with Debug Menu**

Add these functions to your debug menu for easy testing:

```lua
-- In your debug menu system
function debugMenu.spawnEntity(entityType, variant)
    local EntityDebugSystem = require("entities.entity_debug_system")
    return EntityDebugSystem.quickTest(world, entityType, variant)
end

function debugMenu.testSpawnRates(biome)
    local EntityDebugSystem = require("entities.entity_debug_system")
    return EntityDebugSystem.testBiomeSpawns(world, biome, 50)
end
```

## 📊 **Performance Considerations**

- **Efficient Spawning**: Biome tables pre-calculated for fast lookups
- **Memory Management**: Deep copying only when necessary
- **Scalable Design**: Easy to add new entities and variants
- **Debug Mode**: Can be disabled for production builds

## 🔮 **Future Expansion Ideas**

### **Additional Biomes**
- **Volcanic** - Fire-based entities, magma creatures
- **Arctic** - Ice variants, frozen entities
- **Celestial** - Star-based creatures, cosmic entities

### **New Mechanics**
- **Seasonal Spawning** - Different entities per season
- **Weather Effects** - Rain boosts water creatures
- **Player Influence** - Actions affect local spawn rates

### **Advanced Features**
- **Entity Evolution** - Entities can grow stronger over time
- **Territorial Systems** - Entities claim and defend areas
- **Ecosystem Balance** - Predator-prey relationships

## 🎉 **Summary**

The enhanced entity system provides:

✅ **60+ Entity Variants** across all categories
✅ **Biome-Based Spawning** for realistic distribution  
✅ **Event System** for special encounters
✅ **Comprehensive Testing** tools for debugging
✅ **Scalable Architecture** for easy expansion
✅ **Rich Drop Tables** with variant-specific loot
✅ **Visual & Audio Enhancements** for each variant

Your game now has a robust, engaging entity system that provides meaningful variety, progression, and discovery for players!
