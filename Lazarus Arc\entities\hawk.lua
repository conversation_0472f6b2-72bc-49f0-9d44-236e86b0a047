local Hawk = {
    id = "hawk",
    name = "<PERSON>",
    type = "bird",
    shape = {
        {0, -1}, {0.7, -0.7}, {1, 0}, {0.7, 0.7},
        {0, 1}, {-0.7, 0.7}, {-1, 0}, {-0.7, -0.7}
    },
    size = 8,
    
    -- Entity categories
    categories = {"animal", "bird", "flying", "predator"},
    
    -- Threat and food categories
    threatCategories = {"player", "eagle"},
    foodCategories = {"small_prey", "insect", "bird"},
    
    -- Stats
    maxHealth = 35,
    health = 35,
    maxStamina = 60,
    stamina = 60,
    speed = 2.8,
    
    -- Flight properties
    flight = {
        maxHeight = 12,
        minHeight = 1,
        ascentSpeed = 1.5,
        descentSpeed = 2.5,
        hoverHeight = 3,
        currentHeight = 3,
        wingFlapRate = 0.15,
        soarChance = 0.6
    },
    
    -- Behaviors
    behaviors = {"hunt", "patrol", "roost", "evade"},
    
    -- Behavior configurations
    behaviorConfigs = {
        hunt = {
            moveSpeed = 3.2,
            searchRadius = 25,
            diveSpeed = 4.5,
            attackRange = 1.5,
            damage = 20,
            preferredPrey = {"mouse", "bird", "insect"},
            agility = 0.9
        },
        patrol = {
            moveSpeed = 2.8,
            patrolRadius = 30,
            soarChance = 0.6,
            restInterval = {8, 15}
        },
        roost = {
            startTime = "dusk",
            endTime = "dawn",
            roostHeight = {3, 6},
            healthRegen = 0.04,
            staminaRegen = 0.08
        },
        evade = {
            moveSpeed = 3.5,
            detectionRadius = 20,
            dodgeChance = 0.8,
            escapeHeight = 8
        }
    },
    
    -- Special abilities
    abilities = {
        dive = {
            speedBoost = 4.0,
            duration = 1.2,
            cooldown = 4,
            staminaCost = 15
        },
        talonStrike = {
            damage = 12,
            duration = 0.8,
            cooldown = 2,
            staminaCost = 8
        },
        screech = {
            range = 12,
            duration = 0.8,
            cooldown = 3,
            effect = "fear"
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "hawk",
        scale = 0.8,
        animations = {
            "idle", "fly", "dive", "attack", "screech"
        },
        variants = {
            "red-tailed", "coopers", "sharp-shinned", "broad-winged"
        }
    },
    
    -- Sound effects with synth configuration
    sounds = {
        screech = {
            file = "hawk_screech",
            synth = {
                instrument = "trumpet",
                notes = {"B4", "G4", "D4"},
                durations = {0.25, 0.3, 0.4},
                volume = 0.55,
                vibrato = true,
                vibratoRate = 7.0
            }
        },
        wingFlap = {
            file = "hawk_wing_flap",
            synth = {
                instrument = "marimba",
                notes = {"A3", "B3", "A3"},
                durations = {0.12, 0.08, 0.12},
                volume = 0.3
            }
        },
        call = {
            file = "hawk_call",
            synth = {
                instrument = "trumpet",
                notes = {"A4", "F4"},
                durations = {0.3, 0.5},
                volume = 0.5,
                vibrato = true,
                vibratoRate = 5.0
            }
        },
        footstep = {
            synth = {
                instrument = "xylophone",
                notes = {"G3"},
                duration = 0.08,
                volume = 0.18
            }
        },
        fly = {
            synth = {
                instrument = "harmonica",
                notes = {"B3", "D4"},
                durations = {0.2, 0.2},
                volume = 0.35
            }
        },
        hurt = {
            synth = {
                instrument = "trumpet",
                notes = {"D#4"},
                duration = 0.35,
                volume = 0.55,
                vibrato = true,
                vibratoRate = 9.0
            }
        },
        death = {
            synth = {
                instrument = "cello",
                notes = {"G4", "E4", "B3", "G3"},
                durations = {0.4, 0.4, 0.4, 0.8},
                volume = 0.55,
                vibrato = true,
                vibratoRate = 2.0
            }
        },
        dive = {
            synth = {
                instrument = "saxophone",
                notes = {"D5", "B4", "G4", "D4"},
                durations = {0.15, 0.15, 0.15, 0.25},
                volume = 0.45
            }
        }
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.6, quantity = {1, 2}},
        {id = "feather", chance = 0.7, quantity = {1, 3}},
        {id = "talon", chance = 0.3, quantity = {1, 1}},
        {id = "beak", chance = 0.2, quantity = {1, 1}}
    }
}

-- Initialize the entity
function Hawk.init(entity, world)
    -- Copy all fields from Hawk template to entity instance
    for k, v in pairs(Hawk) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random hawk variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Hawk.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update flight properties
    if entity.flight then
        -- Update wing flapping based on height and activity
        if entity.flight.currentHeight > entity.flight.minHeight then
            if entity.flight.soarChance > math.random() then
                entity.flight.wingFlapRate = 0.08
            else
                entity.flight.wingFlapRate = 0.15
            end
        else
            entity.flight.wingFlapRate = 0.25
        end
    end
end

return Hawk 