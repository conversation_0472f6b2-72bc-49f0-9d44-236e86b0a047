local CopperBow = {
    id = "copper_bow",
    name = "Copper Bow",
    type = "weapon",
    category = "bow",
    
    -- Properties
    properties = {
        damage = 19,
        attackSpeed = 1.05,
        range = 5.5,
        durability = 170,
        maxDurability = 170,
        arrowSpeed = 12,
        arrowSpread = 0.25,
        drawTime = 0.55,
        criticalChance = 0.12,
        criticalMultiplier = 1.3,
        armorPenetration = 0.2,
        weight = 1.3,
        momentum = 0,
        maxMomentum = 3,
        momentumPower = 1.0
    },
    
    -- Appearance
    appearance = {
        sprite = "copper_bow",
        scale = 1.0,
        animations = {
            "idle",
            "draw",
            "shoot",
            "momentum"
        },
        variants = {
            "copper",
            "bronze",
            "iron",
            "steel"
        },
        blendMode = "normal",
        tint = {0.9, 0.5, 0.3},
        alpha = 1.0
    },
    
    -- Sound effects
    sounds = {
        draw = "bow_draw",
        shoot = "bow_shoot",
        release = "bow_release",
        momentum = "bow_momentum"
    },
    
    -- Effects
    effects = {
        critical = {
            type = "status",
            duration = 0.3,
            effects = {
                damage = 1.3
            }
        },
        momentum = {
            type = "status",
            duration = 0.4,
            effects = {
                damage = 1.0
            }
        }
    }
}

-- Initialize the weapon
function CopperBow.init(weapon, world)
    -- Copy all fields from CopperBow template to weapon instance
    for k, v in pairs(CopperBow) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        momentum = 0,
        lastMomentumTime = 0,
        drawStartTime = 0,
        isDrawing = false
    }

    return weapon
end

-- Update the weapon
function CopperBow.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Decay momentum
        if world.time - weapon.properties.weaponState.lastMomentumTime > 1.5 then
            weapon.properties.weaponState.momentum = 0
        end
        
        -- Check if draw is complete
        if weapon.properties.weaponState.isDrawing then
            if world.time - weapon.properties.weaponState.drawStartTime >= weapon.properties.drawTime then
                -- Auto-release if fully drawn
                CopperBow.release(weapon, world, attacker, target)
            end
        end
    end
end

-- Handle attack (start drawing)
function CopperBow.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Start drawing
    weapon.properties.weaponState.isDrawing = true
    weapon.properties.weaponState.drawStartTime = world.time
    
    -- Play draw sound
    if world.playSound then
        world.playSound(weapon.sounds.draw)
    end
    
    return true
end

-- Handle release
function CopperBow.release(weapon, world, attacker, target)
    if not weapon.properties.weaponState.isDrawing then
        return false
    end
    
    -- Calculate shot direction
    local angle = math.atan2(
        target.position.y - attacker.position.y,
        target.position.x - attacker.position.x
    )
    
    -- Add spread to angle
    angle = angle + (math.random() - 0.5) * weapon.properties.arrowSpread
    
    -- Create shoot effect
    if world.createEffect then
        world.createEffect({
            type = "shoot",
            position = attacker.position,
            angle = angle,
            range = weapon.properties.range,
            duration = 0.4
        })
    end
    
    -- Check for critical hit
    if math.random() < weapon.properties.criticalChance then
        -- Create critical effect
        if world.createEffect then
            world.createEffect({
                type = "critical",
                position = target.position,
                duration = 0.3
            })
        end
        
        -- Apply critical effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.critical)
        end
    end
    
    -- Update momentum
    if world.time - weapon.properties.weaponState.lastMomentumTime <= 1.5 then
        weapon.properties.weaponState.momentum = math.min(weapon.properties.maxMomentum,
            weapon.properties.weaponState.momentum + 1)
    else
        weapon.properties.weaponState.momentum = 1
    end
    weapon.properties.weaponState.lastMomentumTime = world.time
    
    -- Apply momentum effect if active
    if weapon.properties.weaponState.momentum > 1 then
        if target.applyEffect then
            target.applyEffect(weapon.effects.momentum)
        end
        
        -- Play momentum sound
        if world.playSound then
            world.playSound(weapon.sounds.momentum)
        end
    end
    
    -- Play release sound
    if world.playSound then
        world.playSound(weapon.sounds.release)
    end
    
    -- Reset drawing state
    weapon.properties.weaponState.isDrawing = false
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function CopperBow.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough momentum
    if weapon.properties.weaponState.momentum < 2 then
        return false
    end
    
    -- Create rapid shot effect
    if world.createEffect then
        world.createEffect({
            type = "rapid_shot",
            position = attacker.position,
            angle = math.atan2(
                target.position.y - attacker.position.y,
                target.position.x - attacker.position.x
            ),
            range = weapon.properties.range * 1.2,
            duration = 0.6
        })
    end
    
    -- Apply critical effect with increased power
    if target.applyEffect then
        target.applyEffect({
            type = "critical",
            duration = 0.4,
            effects = {
                damage = weapon.properties.criticalMultiplier * 1.2
            }
        })
    end
    
    -- Reset momentum
    weapon.properties.weaponState.momentum = 0
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 2)
    
    return true
end

return CopperBow 