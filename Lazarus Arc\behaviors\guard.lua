-- behaviors/guard.lua
-- Guarding behavior for sentinel or protector entities

local GuardBehavior = {
    id = "guard",
    name = "Guard Behavior",

    defaultConfig = {
        guardRadius = 8,
        alertRadius = 12,
        pursuitRadius = 15,
        returnDelay = 5,
        moveSpeed = 1.0,
        attackRadius = 2.0,
        attackCooldown = 2.0,
        threatTypes = {},
        protectedTypes = {},
        patrolPattern = "circular",
        alertedByDamage = true,
        callsForHelp = true
    }
}

function GuardBehavior.init(entity, customConfig)
    -- Ensure entity has a position
    local pos = entity.position or { x = 0, y = 0 }
    entity.position = pos

    entity.guardState = entity.guardState or {
        guardPointX = pos.x,
        guardPointY = pos.y,
        currentThreat = nil,
        attackTimer = 0,
        isAlerted = false,
        returnTimer = 0,
        patrolTarget = {x = 0, y = 0},
        patrolProgress = 0,
        patrolDirection = 1,
        config = {},
        patrolInitialized = false
    }

    local config = entity.guardState.config
    for k, v in pairs(GuardBehavior.defaultConfig) do
        config[k] = v
    end

    if customConfig then
        for k, v in pairs(customConfig) do
            config[k] = v
        end
    end
end

function GuardBehavior.update(entity, world, dt)
    local state = entity.guardState
    if not state then
        GuardBehavior.init(entity)
        state = entity.guardState
    end

    local config = state.config

    if not state.patrolInitialized then
        GuardBehavior.setPatrolTarget(entity)
        state.patrolInitialized = true
    end

    if state.attackTimer > 0 then
        state.attackTimer = state.attackTimer - dt
    end

    if state.returnTimer > 0 then
        state.returnTimer = state.returnTimer - dt
        if state.returnTimer <= 0 then
            state.isAlerted = false
            state.currentThreat = nil
        end
    end

    if state.currentThreat then
        local threat = world.entitySystem.entities[state.currentThreat]
        if not threat then
            state.currentThreat = nil
            state.returnTimer = config.returnDelay
            return
        end

        local dx = threat.position.x - entity.position.x
        local dy = threat.position.y - entity.position.y
        local distSq = dx * dx + dy * dy

        if distSq > config.pursuitRadius * config.pursuitRadius then
            state.currentThreat = nil
            state.returnTimer = config.returnDelay
            return
        end

        if distSq <= config.attackRadius * config.attackRadius and state.attackTimer <= 0 then
            GuardBehavior.attack(entity, threat, world)
            return
        end

        local dist = math.sqrt(distSq)
        entity.velocity.x = (dx / dist) * config.moveSpeed
        entity.velocity.y = (dy / dist) * config.moveSpeed

    elseif state.isAlerted then
        local threat = GuardBehavior.findThreats(entity, world)

        if threat then
            state.currentThreat = threat.id
        else
            if state.returnTimer <= 0 then
                state.returnTimer = config.returnDelay
            end
        end
    else
        GuardBehavior.patrol(entity, world, dt)

        if math.random() < 0.1 * dt then
            local threat = GuardBehavior.findThreats(entity, world)

            if threat then
                state.currentThreat = threat.id
                state.isAlerted = true

                if config.callsForHelp then
                    GuardBehavior.callForHelp(entity, threat, world)
                end
            end
        end

        if config.alertedByDamage and #config.protectedTypes > 0 and math.random() < 0.05 * dt then
            GuardBehavior.checkProtectedEntities(entity, world)
        end
    end
end

-- All other helper functions (setPatrolTarget, patrol, findThreats, etc.)
-- stay exactly as you had them before

return GuardBehavior
