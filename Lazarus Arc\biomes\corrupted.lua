-- biomes/corrupted.lua
local CorruptedBiome = {
    id = "corrupted",
    name = "Corrupted Lands",
    description = "A twisted and dangerous biome with a high concentration of corrupted creatures and features.", --

    -- Environmental factors (Unnatural, dangerous)
    environment = {
        humidity = 0.5,    -- Can vary, might be unnaturally damp or dry
        temperature = 0.4, -- Often feels unnaturally cold or feverishly hot, average is coolish
        sunlight = 0.3,    -- Light seems dimmer or distorted
        windStrength = 0.3,
        airQuality = 0.2,  -- Tainted air
        corruptionLevel = 0.7 -- Base level of corruption influence (0-1)
    },

    -- Core tile types used in this biome
    -- NOTE: Needs 'corrupted_earth', 'void_pool', 'decaying_vegetation', 'cursed_crystals'. 'twisted_trees' likely entity/feature.
    primaryTiles = {"corrupted_earth"},
    secondaryTiles = {"decaying_vegetation", "twisted_trees_tile", "cursed_crystals"}, -- Assuming trees/veg are sometimes tiles
    rareTiles = {"void_pool", "bone_field"}, -- Added bone field

    -- Default proportions (adjustable through variants)
    tileDistribution = {
        corrupted_earth = 0.6,
        decaying_vegetation = 0.15,
        twisted_trees_tile = 0.1,
        cursed_crystals = 0.05,
        void_pool = 0.05,
        bone_field = 0.05
    },

    -- Entities common to this biome
    commonEntities = {
        "corrupted_creature_small", "ghost_minor", "shadow_crawler", "blighted_plant" -- Added crawler/plant
    },
    uncommonEntities = {
        "cursed_spirit", "void_monster_lesser", "demon_imp", "corrupted_animal" -- Corrupted version of normal animal
    },
    rareEntities = {
        "demon_major", "void_terror", "lich_remnant", "cursed_guardian" -- Added lich/guardian
    },

    -- Biome variants for diverse generation
    variants = {
        blighted_forest = {
            name = "Blighted Forest",
            inherits = "forest", -- Hypothetical inheritance from base forest
            corruptionLevel = 0.6,
            tileAdjustments = { corrupted_earth=0.4, decaying_vegetation=0.3, twisted_trees_tile=0.2 },
            entityAdjustments = { corrupted_animal=1.5, ghost_minor=1.3, demon_imp=0.8 },
            specialEffects = {"whispering_woods_effect", "canopy_shadows"}
        },
        void_scar = {
            name = "Void Scar",
            corruptionLevel = 0.9, -- Highest corruption
            environment = { sunlight=0.1, airQuality=0.0 },
            tileAdjustments = { corrupted_earth=0.3, void_pool=0.4, cursed_crystals=0.2 },
            entityAdjustments = { void_monster_lesser=2.0, void_terror=1.5, cursed_spirit=1.2 },
            specialEffects = {"unstable_reality", "void_energy_pulses", "silence"}
        },
        haunted_battlefield = {
            name = "Haunted Battlefield",
            corruptionLevel = 0.5,
            tileAdjustments = { corrupted_earth=0.5, bone_field=0.3, decaying_vegetation=0.1 },
            entityAdjustments = { ghost_minor=1.8, cursed_spirit=1.5, demon_imp=1.0, skeleton_warrior=1.2 }, -- Added skeleton
            specialEffects = {"lingering_souls_visual", "battle_sounds_echo", "despair_aura"}
        },
        cursed_swamp = {
             name = "Cursed Swamp",
             inherits = "swamp",
             corruptionLevel = 0.7,
             environment = { humidity = 0.95, airQuality = 0.1 },
             tileAdjustments = { corrupted_earth=0.2, decaying_vegetation=0.2, void_pool=0.1, cursed_water=0.4 }, -- Need cursed water tile
             entityAdjustments = { corrupted_swamp_monster = 1.5, ghost_minor = 1.2, demon_leech = 1.8 }, -- Specific corrupted entities
             specialEffects = {"toxic_fumes", " grasping_vines", "illusory_lights"}
        }
    },

    -- Structures that can generate in this biome
    structures = {
        {
            name = "abandoned_village", -- Village consumed by corruption
            chance = 0.08,
            entities = {"ghost_villager", "cursed_spirit", "haunted_house_marker"} -- Haunted house is a feature maybe?
        },
        {
            name = "corrupted_shrine",
            chance = 0.1,
            entities = {"dark_priest", "bound_demon_minor", "desecrated_altar"}
        },
        {
            name = "forgotten_graveyard",
            chance = 0.07,
            entities = {"skeleton_warrior", "ghoul", "restless_spirit", "open_grave"}
        },
        {
            name = "demon_lair", -- Major structure
            chance = 0.03,
            unique = true,
            requiresVariant = "void_scar", -- Or high corruption level
            entities = {"demon_major", "demon_imp_horde", "hellhound"} -- Added hellhound
        }
    },

    -- Weather/Environmental Effects (Unnatural weather)
    weather = {
        transitions = {
             shadow_haze = { shadow_haze = 50, whispering_wind = 30, void_rain = 10, gloom = 10 },
             whispering_wind = { shadow_haze = 60, whispering_wind = 30, gloom = 10 },
             void_rain = { shadow_haze = 50, whispering_wind = 30, void_rain = 20 }, -- Corrosive/damaging rain?
             gloom = { shadow_haze = 70, gloom = 30 }
        },
        default = "shadow_haze"
    },
    environmentalEffects = { -- Added alongside weather
        corruption_aura = 0.7, -- Passive negative effect?
        reality_distortion = 0.1, -- Chance of minor visual/audio glitches?
        life_drain = 0.05 -- Small passive damage/fatigue?
    },

    -- Unique features generation
    features = {
        {
            name = "corrupted_temple", -- Similar to structure? Maybe larger ruin area
            chance = 0.06,
            -- Place corrupted ruins tiles
        },
        {
            name = "demon_gate", -- Portal feature
            chance = 0.03,
            unique = true,
            danger = true
            -- Places portal entity/structure
        },
        {
            name = "cursed_treasure", -- Feature placing a trapped/guarded treasure entity
            chance = 0.05,
            -- Places entity
        },
        {
            name = "haunted_house", -- Specific structure/feature placing building and ghosts
            chance = 0.07,
            -- Places structure/entities
        },
        {
            name = "whispering_woods", -- Area feature applying audio/sanity effects?
            chance = 0.1,
            size = {min=10, max=25},
            specialEffect = true
        },
         { -- Added void pool from tiles
             name = "void_pool_feature",
             chance = 0.1,
             size = {min=4, max=10},
             danger = true
             -- Places void_pool tiles
         },
         { -- Added cursed crystals from tiles
             name = "cursed_crystal_field",
             chance = 0.12,
             size = {min=6, max=15}
             -- Places cursed_crystal tiles/entities
         }
    },

    -- Generation algorithm (Structure based on forest/plains)
    generate = function(chunk, world, variant)
        print("Generating " .. (variant and CorruptedBiome.variants[variant].name or "Corrupted Lands") .. " biome")
        local variantData = variant and CorruptedBiome.variants[variant] or nil
        local corruption = variantData and variantData.corruptionLevel or CorruptedBiome.environment.corruptionLevel

        -- Adjust tile distribution
        local tileDistribution = {}
        for tileType, chance in pairs(CorruptedBiome.tileDistribution) do
            tileDistribution[tileType] = chance
            if variantData and variantData.tileAdjustments and variantData.tileAdjustments[tileType] then
                tileDistribution[tileType] = variantData.tileAdjustments[tileType]
            end
        end

        -- Noise functions
        local seed = world.seed + (chunk.x * 953 + chunk.y * 229)
        math.randomseed(seed)
        local function pnoise2D(x, y, frequency, seed_offset) return love.math.noise(x * frequency, y * frequency, (world.seed + (seed_offset or 0)) * 0.01) end

        -- Generate tiles
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                local worldX = chunk.x * world.CHUNK_SIZE + x
                local worldY = chunk.y * world.CHUNK_SIZE + y

                local baseNoise = pnoise2D(worldX, worldY, 0.07, 1) -- Controls base terrain structure
                local corruptionNoise = pnoise2D(worldX, worldY, 0.15, 2) -- Controls intensity/type of corruption

                -- Determine base tile type (assuming it corrupts something like plains/forest)
                local baseTileType = "corrupted_earth" -- Default corrupted ground
                if baseNoise < -0.3 then baseTileType = "decaying_vegetation"
                elseif baseNoise > 0.5 then baseTileType = "twisted_trees_tile" end -- Assume these are tiles for now

                local tileType = baseTileType

                -- Apply rarer tiles based on corruption noise and distribution
                if corruptionNoise > 0.6 then
                     local tileRoll = math.random()
                     local cumulativeChance = 0
                     for tType, chance in pairs(tileDistribution) do
                          cumulativeChance = cumulativeChance + chance
                          if tileRoll <= cumulativeChance and (tType == "void_pool" or tType == "cursed_crystals" or tType == "bone_field") then
                               tileType = tType
                               break
                          end
                     end
                end

                -- Variant overrides (e.g., more void in scar, more decay in forest)
                if variant == "void_scar" and corruptionNoise > 0.3 then
                    if math.random() < 0.4 then tileType = "void_pool" end
                elseif variant == "blighted_forest" and baseNoise > -0.3 then
                     if math.random() < 0.3 then tileType = "decaying_vegetation" end
                     if math.random() < 0.2 then tileType = "twisted_trees_tile" end
                end

                -- Create the tile
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = math.random(1, 3),
                    corruption = corruption + (corruptionNoise * 0.2), -- Tile specific corruption level
                    isUnderground = false,
                    passable = (tileType ~= "void_pool") -- Basic passability check
                }

                 -- Apply properties based on type
                 if tileType == "corrupted_earth" then tile.movementSpeed = 0.9; end
                 if tileType == "void_pool" then tile.passable = false; tile.damage = 5; tile.damageType="void"; end
                 if tileType == "decaying_vegetation" then tile.movementSpeed = 0.7; end
                 if tileType == "cursed_crystals" then tile.aura="negative_energy"; end

                -- Add to chunk
                chunk.tiles[x][y] = tile
            end
        end

        return chunk
    end,

    -- Initialize the biome
    init = function(worldCore)
        print("Corrupted Lands biome module initialized")
        CorruptedBiome.worldCore = worldCore
        print("Corrupted Lands biome registered successfully for later use")
        return true
    end,
    
    registerWithWorld = function(world)
        print("Registering Corrupted Lands biome generators with world")
        if not world or not world.chunkSystem then 
            print("Warning: Cannot register Corrupted Lands biome - world or chunkSystem is nil") 
            return false 
        end
        world.chunkSystem:registerGenerator(CorruptedBiome.id, CorruptedBiome.generate)
        for variantId, variantData in pairs(CorruptedBiome.variants) do
            local fullVariantId = CorruptedBiome.id .. "_" .. variantId
            world.chunkSystem:registerGenerator(fullVariantId, function(chunk, worldInstance)
                CorruptedBiome.generate(chunk, worldInstance, variantId)
            end)
        end
        return true
    end
}

return CorruptedBiome