local ConfusionTrap = {
    id = "confusion_trap",
    name = "Confusion Trap",
    type = "trap",
    
    -- Entity categories
    categories = {"trap", "dungeon", "magic"},
    
    -- Target categories
    targetCategories = {"player", "monster"},
    
    -- Stats
    maxHealth = 15,
    health = 15,
    duration = 8,
    cooldown = 30,
    
    -- Trap properties
    properties = {
        radius = 2.0,
        setupTime = 2,
        isArmed = false,
        isTriggered = false,
        destroyOnActivate = false,
        confusionDuration = 8,
        invertChance = 0.7,
        slowAmount = 0.3
    },
    
    -- Appearance
    appearance = {
        sprite = "confusion_trap",
        scale = 1.0,
        animations = {
            "idle", "trigger", "confuse"
        },
        variants = {
            "purple", "pink", "rainbow", "void"
        }
    },
    
    -- Sound effects
    sounds = {
        trigger = "confusion_trap_trigger",
        confuse = "confusion_trap_confuse",
        clear = "confusion_trap_clear"
    },
    
    -- Effects
    effects = {
        confusion = {
            type = "status",
            duration = 8,
            effects = {
                invertControls = true,
                slow = 0.3,
                dizzy = true
            }
        }
    }
}

-- Initialize the trap
function ConfusionTrap.init(entity, world)
    -- Copy all fields from ConfusionTrap template to entity instance
    for k, v in pairs(ConfusionTrap) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position
    entity.position = entity.position or {x = 0, y = 0}

    -- Set random variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize trap state
    entity.properties.isArmed = false
    entity.properties.isTriggered = false
    entity.properties.triggerTime = 0
    entity.properties.confusedTargets = {}

    return entity
end

-- Update the trap
function ConfusionTrap.update(entity, world, dt)
    -- Check if trap is armed and not triggered
    if entity.properties.isArmed and not entity.properties.isTriggered then
        -- Check for targets in radius
        if world and world.entities then
            for _, target in ipairs(world.entities) do
                if target.position and target.categories then
                    -- Check if target is in trap categories
                    local isTarget = false
                    for _, category in ipairs(entity.targetCategories) do
                        if table.contains(target.categories, category) then
                            isTarget = true
                            break
                        end
                    end
                    
                    if isTarget then
                        local distance = math.sqrt(
                            (target.position.x - entity.position.x)^2 + 
                            (target.position.y - entity.position.y)^2
                        )
                        
                        if distance <= entity.properties.radius then
                            -- Trigger trap
                            entity.properties.isTriggered = true
                            entity.properties.triggerTime = world.time
                            
                            -- Add target to confused list
                            table.insert(entity.properties.confusedTargets, target)
                            
                            -- Apply confusion effect to target
                            if target.applyEffect then
                                target.applyEffect(entity.effects.confusion)
                            end
                            
                            -- Play trigger sound
                            if world.playSound then
                                world.playSound(entity.sounds.trigger)
                            end
                            
                            break
                        end
                    end
                end
            end
        end
    end
    
    -- Handle confused targets
    if entity.properties.isTriggered then
        for i = #entity.properties.confusedTargets, 1, -1 do
            local target = entity.properties.confusedTargets[i]
            if target.position then
                -- Check if confusion should end
                if world.time - entity.properties.triggerTime >= entity.properties.confusionDuration then
                    -- Remove confusion effect
                    if target.removeEffect then
                        target.removeEffect("confusion")
                    end
                    
                    -- Play clear sound
                    if world.playSound then
                        world.playSound(entity.sounds.clear)
                    end
                    
                    -- Remove from confused list
                    table.remove(entity.properties.confusedTargets, i)
                end
            end
        end
    end
    
    -- Check if trap can be rearmed
    if not entity.properties.isArmed and world.time - entity.properties.triggerTime >= entity.cooldown then
        entity.properties.isArmed = true
        entity.properties.isTriggered = false
        entity.properties.confusedTargets = {}
    end
end

-- Arm the trap
function ConfusionTrap.arm(entity, world)
    if not entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = true
        entity.properties.triggerTime = world.time
        entity.properties.confusedTargets = {}
        return true
    end
    return false
end

-- Disarm the trap
function ConfusionTrap.disarm(entity, world)
    if entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = false
        entity.properties.triggerTime = world.time
        entity.properties.confusedTargets = {}
        return true
    end
    return false
end

return ConfusionTrap 