-- biomes/automated_agriculture.lua
local AutomatedAgricultureBiome = {
    id = "automated_agriculture",
    name = "Automated Agriculture",
    description = "A highly efficient farming area with automated systems and advanced technology.",

    -- Environmental factors
    environment = {
        humidity = 0.6,    -- Controlled humidity
        temperature = 0.7, -- Optimal growing temperature
        sunlight = 0.8,    -- Good sunlight
        windStrength = 0.2 -- Light winds
    },

    -- Core tile types used in this biome
    primaryTiles = {"farmland", "irrigation"},
    secondaryTiles = {"greenhouse", "storage"},
    rareTiles = {"research_lab", "control_center"},

    -- Default proportions
    tileDistribution = {
        farmland = 0.4,
        irrigation = 0.3,
        greenhouse = 0.15,
        storage = 0.1,
        research_lab = 0.03,
        control_center = 0.02
    },

    -- Entities common to this biome
    commonEntities = {
        "robot_farmer", "crop", "irrigation_system", "sensor"
    },

    uncommonEntities = {
        "research_robot", "harvest_robot", "weather_control", "nutrient_dispenser"
    },

    rareEntities = {
        "ai_controller", "genetic_engineer", "climate_control", "advanced_robot"
    },

    -- Environmental properties specific to automated agriculture biomes
    hasAutomation = true,
    hasGreenhouses = true,
    hasIrrigation = true,
    hasResearch = true,

    -- Biome variants for diverse generation
    variants = {
        hydroponic = {
            name = "Hydroponic Farm",
            temperature = 0.8,
            tileAdjustments = {
                farmland = 0.3,
                greenhouse = 0.4,
                irrigation = 0.2
            },
            entityAdjustments = {
                nutrient_dispenser = 2.0,
                research_robot = 1.5
            },
            specialEffects = {
                "nutrient_mist",
                "growth_lights"
            }
        },
        vertical = {
            name = "Vertical Farm",
            temperature = 0.7,
            tileAdjustments = {
                farmland = 0.5,
                storage = 0.2,
                control_center = 0.1
            },
            entityAdjustments = {
                harvest_robot = 2.0,
                weather_control = 1.5
            },
            specialEffects = {
                "elevator_sounds",
                "stacked_gardens"
            }
        },
        research = {
            name = "Research Farm",
            temperature = 0.6,
            tileAdjustments = {
                farmland = 0.3,
                research_lab = 0.3,
                greenhouse = 0.2
            },
            entityAdjustments = {
                genetic_engineer = 2.0,
                ai_controller = 1.5
            },
            specialEffects = {
                "lab_equipment",
                "research_aura"
            }
        }
    },

    -- Structures that can generate in this biome
    structures = {
        {
            name = "control_center",
            chance = 0.1,
            entities = {"ai_controller", "monitor"}
        },
        {
            name = "research_lab",
            chance = 0.08,
            requiresVariant = "research",
            entities = {"genetic_engineer", "lab_equipment"}
        },
        {
            name = "greenhouse",
            chance = 0.15,
            requiresVariant = "hydroponic",
            entities = {"crop", "irrigation_system"}
        },
        {
            name = "storage_facility",
            chance = 0.12,
            requiresVariant = "vertical",
            entities = {"harvest_robot", "storage_unit"}
        }
    },

    -- Environmental effects
    environmentalEffects = {
        automation_hum = 0.8,
        growth_lights = 0.6,
        irrigation_sounds = 0.4,
        robot_movement = 0.3,
        research_activity = 0.2
    },

    -- Unique features
    features = {
        {
            name = "control_center",
            chance = 0.1,
            unique = true
        },
        {
            name = "research_lab",
            chance = 0.08,
            requiresVariant = "research"
        },
        {
            name = "greenhouse",
            chance = 0.15,
            requiresVariant = "hydroponic"
        },
        {
            name = "storage_facility",
            chance = 0.12,
            requiresVariant = "vertical"
        },
        {
            name = "irrigation_system",
            chance = 0.2
        }
    }
}

return AutomatedAgricultureBiome