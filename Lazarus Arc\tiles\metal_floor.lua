local MetalFloor = {
    id = "metal_floor",
    name = "Metal Floor",
    type = "tile",
    
    -- Categories
    categories = {"floor", "metal", "dungeon", "structure"},
    
    -- Properties
    properties = {
        walkable = true,
        swimable = false,
        climbable = false,
        flyable = true,
        diggable = true,
        buildable = true,
        flammable = false,
        hardness = 0.8,
        conductivity = 0.9,
        corrosion = 0.1,
        corrosionRate = 0.05,
        rustChance = 0.2,
        rustSpread = 0.3,
        echoRadius = 2,
        echoStrength = 0.7,
        trapChance = 0.1,
        trapTypes = {"spike", "poison", "alarm"},
        lightReflection = 0.6,
        lightColor = {0.7, 0.7, 0.8}
    },
    
    -- Appearance
    appearance = {
        sprite = "metal_floor",
        scale = 1.0,
        animations = {
            "idle",
            "corrode",
            "rust",
            "reflect"
        },
        variants = {
            "steel",
            "iron",
            "copper",
            "bronze"
        },
        blendMode = "normal",
        tint = {0.7, 0.7, 0.7},
        alpha = 1.0
    },
    
    -- Sound effects
    sounds = {
        walk = "metal_walk",
        dig = "metal_dig",
        corrode = "metal_corrode",
        rust = "metal_rust",
        echo = "metal_echo"
    },
    
    -- Resources
    resources = {
        metal = {
            type = "mineral",
            amount = 3,
            harvestable = true,
            harvestAmount = 1,
            harvestTool = "pickaxe"
        }
    },
    
    -- Effects
    effects = {
        echo = {
            type = "environment",
            duration = 0,
            effects = {
                radius = 2,
                strength = 0.7
            }
        },
        reflect = {
            type = "light",
            duration = 0,
            effects = {
                reflection = 0.6,
                color = {0.7, 0.7, 0.8}
            }
        }
    },
    
    -- Spawn rules
    spawnRules = {
        structures = {
            "metal_pillar",
            "metal_door",
            "metal_trap",
            "metal_chest"
        }
    }
}

-- Initialize the tile
function MetalFloor.init(tile, world)
    -- Initialize properties first
    tile.properties = tile.properties or {}
    
    -- Initialize metal state
    tile.properties.metalState = {
        corrosion = 0,
        rusted = false,
        hasTrap = math.random() < (MetalFloor.properties.trapChance or 0.1),
        trapType = MetalFloor.properties.trapTypes[math.random(#MetalFloor.properties.trapTypes)],
        lastEchoTime = 0
    }

    -- Copy all fields from MetalFloor template to tile instance
    for k, v in pairs(MetalFloor) do
        if type(v) ~= "function" and tile[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                tile[k] = {}
                for subk, subv in pairs(v) do
                    tile[k][subk] = subv
                end
            else
                tile[k] = v
            end
        end
    end

    -- Set random variant
    if tile.appearance and tile.appearance.variants then
        local variants = tile.appearance.variants
        tile.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize resources
    if tile.resources then
        for resourceType, resource in pairs(tile.resources) do
            resource.currentAmount = resource.amount
            resource.lastHarvest = 0
            resource.growthStage = "mature"
        end
    end

    return tile
end

-- Update the tile
function MetalFloor.update(tile, world, dt)
    -- Update metal state
    if tile.properties.metalState then
        -- Update corrosion
        if world.moisture and world.moisture > 0.5 then
            tile.properties.metalState.corrosion = math.min(1.0,
                tile.properties.metalState.corrosion + tile.properties.corrosionRate * dt)
            
            -- Check for rust
            if tile.properties.metalState.corrosion > 0.5 and 
               not tile.properties.metalState.rusted and 
               math.random() < tile.properties.rustChance then
                tile.properties.metalState.rusted = true
                
                -- Create rust effect
                if world.createEffect then
                    world.createEffect({
                        type = "rust",
                        position = tile.position,
                        radius = tile.properties.rustSpread
                    })
                end
                
                -- Play rust sound
                if world.playSound then
                    world.playSound(tile.sounds.rust)
                end
            end
        end
        
        -- Update trap state
        if tile.properties.metalState.hasTrap then
            -- Check for trap activation
            if world.entities then
                for _, entity in ipairs(world.entities) do
                    if entity.position then
                        local distance = math.sqrt(
                            (entity.position.x - tile.position.x)^2 + 
                            (entity.position.y - tile.position.y)^2
                        )
                        
                        if distance <= 1.0 then
                            -- Activate trap
                            if world.createEntity then
                                world.createEntity({
                                    type = tile.properties.metalState.trapType,
                                    position = tile.position
                                })
                            end
                            
                            -- Remove trap after activation
                            tile.properties.metalState.hasTrap = false
                        end
                    end
                end
            end
        end
        
        -- Update echo effect
        if world.time - tile.properties.metalState.lastEchoTime >= 1.0 then
            -- Create echo effect
            if world.createEffect then
                world.createEffect({
                    type = "echo",
                    position = tile.position,
                    radius = tile.properties.echoRadius,
                    strength = tile.properties.echoStrength
                })
            end
            
            -- Play echo sound
            if world.playSound then
                world.playSound(tile.sounds.echo)
            end
            
            tile.properties.metalState.lastEchoTime = world.time
        end
    end
    
    -- Update resources
    if tile.resources then
        for resourceType, resource in pairs(tile.resources) do
            if resource.currentAmount < resource.amount then
                -- Regrow resources
                local regrowthAmount = resource.regrowthRate * dt
                resource.currentAmount = math.min(resource.amount, resource.currentAmount + regrowthAmount)
                
                -- Update growth stage
                if resource.currentAmount >= resource.amount then
                    resource.growthStage = "mature"
                elseif resource.currentAmount >= resource.amount * 0.5 then
                    resource.growthStage = "growing"
                else
                    resource.growthStage = "young"
                end
            end
        end
    end
end

-- Handle interaction
function MetalFloor.interact(tile, world, entity, action)
    if action == "walk" then
        -- Create echo effect
        if world.createEffect then
            world.createEffect({
                type = "echo",
                position = tile.position,
                radius = tile.properties.echoRadius,
                strength = tile.properties.echoStrength
            })
        end
        
        -- Play walk sound
        if world.playSound then
            world.playSound(tile.sounds.walk)
        end
    elseif action == "dig" then
        -- Handle digging
        if tile.properties.diggable then
            -- Create metal item
            if world.createItem then
                world.createItem({
                    type = "metal",
                    amount = 1,
                    position = tile.position
                })
            end
            
            -- Play dig sound
            if world.playSound then
                world.playSound(tile.sounds.dig)
            end
            
            -- Convert to dirt tile
            if world.setTile then
                world.setTile(tile.position, "dirt")
            end
        end
    elseif action == "harvest" then
        -- Handle resource harvesting
        if tile.resources then
            for resourceType, resource in pairs(tile.resources) do
                if resource.harvestable and resource.currentAmount > 0 then
                    -- Create harvested item
                    if world.createItem then
                        world.createItem({
                            type = resourceType,
                            amount = resource.harvestAmount,
                            position = tile.position
                        })
                    end
                    
                    -- Update resource amount
                    resource.currentAmount = math.max(0, resource.currentAmount - resource.harvestAmount)
                    resource.lastHarvest = world.time
                    resource.growthStage = "young"
                end
            end
        end
    end
end

return MetalFloor 