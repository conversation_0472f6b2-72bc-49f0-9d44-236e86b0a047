-- behaviors/scavenging.lua
-- Scavenging behavior for entities that seek out and consume leftovers

local ScavengingBehavior = {}  -- create an empty table

ScavengingBehavior.id = "scavenging"
ScavengingBehavior.name = "Scavenging Behavior"

-- Configuration parameters
ScavengingBehavior.defaultConfig = {
    searchRadius = 15,              -- Radius to search for carrion or leftover resources
    moveSpeed = 1.5,                -- Speed when moving toward items
    consumeDuration = {3, 6},       -- Duration spent consuming items
    prioritizeFreshness = true,     -- Whether to prioritize fresher leftovers
    avoidThreats = true,            -- Avoid threats while scavenging
    threatAvoidanceRadius = 10,     -- Radius to detect and avoid threats
    maxCarryWeight = 20,            -- Max weight entity can carry away
    returnToHomeWhenFull = true,    -- Return to home location when inventory is full
    homePosition = nil              -- Home location to return when full
}

-- Initialize the behavior
function ScavengingBehavior.init(entity, customConfig)
    entity.scavengingState = entity.scavengingState or {
        targetItem = nil,
        scavenging = false,
        config = {}
    }

    local config = entity.scavengingState.config
    for k, v in pairs(ScavengingBehavior.defaultConfig) do
        config[k] = v
    end

    if customConfig then
        for k, v in pairs(customConfig) do
            config[k] = v
        end
    end
end

-- Update scavenging behavior
function ScavengingBehavior.update(entity, world, dt)
    local state = entity.scavengingState
    local config = state.config

    -- Search for scavenging opportunities if none targeted yet
    if not state.targetItem then
        state.targetItem = ScavengingBehavior.findScavengeable(entity, world)
    end

    -- Move towards target item
    if state.targetItem then
        local dx = state.targetItem.position.x - entity.position.x
        local dy = state.targetItem.position.y - entity.position.y
        local distance = math.sqrt(dx * dx + dy * dy)

        if distance < 1 then
            ScavengingBehavior.collectItem(entity, state.targetItem)
            state.targetItem = nil
        else
            entity.velocity.x = (dx / distance) * config.moveSpeed
            entity.velocity.y = (dy / distance) * config.moveSpeed
        end
    end

    -- Check threats and flee if necessary
    if config.avoidThreats then
        local threat = ScavengingBehavior.detectThreat(entity, world)
        if threat then
            ScavengingBehavior.flee(entity, threat)
        end
    end
end

-- Identify scavengeable items (placeholder implementation)
function ScavengingBehavior.findScavengeable(entity, world)
    -- Implementation details...
end

-- Detect threats (placeholder implementation)
function ScavengingBehavior.detectThreat(entity, world)
    -- Implementation details...
end

-- Collect an item (placeholder implementation)
function ScavengingBehavior.collectItem(entity, item)
    -- Implementation details...
end

-- Flee from a threat (placeholder implementation)
function ScavengingBehavior.flee(entity, threat)
    -- Implementation details...
end

return ScavengingBehavior
