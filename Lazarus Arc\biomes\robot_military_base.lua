-- biomes/robot_military_base.lua
-- Declare RobotMilitaryBaseBiome at the top level so it can be referenced from within functions
local RobotMilitaryBaseBiome = {}

-- Define the biome properties
RobotMilitaryBaseBiome = {
    id = "robot_military_base",
    name = "Automated Fortress",
    description = "A heavily fortified, often dormant, military installation operated by advanced combat automatons. Intruders face overwhelming force, especially if regional threat levels are high or external activation occurs.",

    -- Environmental factors (Shielded, Controlled, High Energy)
    environment = {
        humidity = 0.4,    -- Controlled, dry air
        temperature = 0.5, -- Stable, possibly cool for machinery
        sunlight = 0.0,    -- Usually subterranean or windowless
        windStrength = 0.0, -- Internal circulation only
        airQuality = 1.0,  -- Filtered, sterile
        energySignature = 0.8 -- High energy readings, even if dormant
    },

    -- Core tile types used in this biome
    -- NOTE: Needs 'reinforced_metal_floor', 'bunker_wall', 'force_field_emitter', 'weapon_emplacement_tile', 'command_bunker_tile', 'vehicle_bay_tile', 'power_core_military'. Uses 'metal_floor', 'control_panel'.
    primaryTiles = {"reinforced_metal_floor", "bunker_wall"}, -- Heavy flooring and impassable walls
    secondaryTiles = {"weapon_emplacement_tile", "force_field_emitter", "maintenance_bay_military"},
    rareTiles = {"command_bunker_tile", "vehicle_bay_tile", "power_core_military", "prototype_lab_tile"},

    -- Default proportions (Highly structured, fortified)
    tileDistribution = {
        reinforced_metal_floor = 0.6,
        bunker_wall = 0.25, -- Significant wall structures
        weapon_emplacement_tile = 0.05,
        force_field_emitter = 0.03,
        maintenance_bay_military = 0.03,
        command_bunker_tile = 0.01,
        vehicle_bay_tile = 0.01,
        power_core_military = 0.01,
        prototype_lab_tile = 0.005
    },

    -- Entities common to this biome (Often initially dormant/patrolling)
    commonEntities = {
        "sentry_drone_dormant", "security_camera_active", "maintenance_bot_military"
    },
    uncommonEntities = {
        "military_robot_soldier", "stealth_recon_bot", "automated_turret" -- military_robot_soldier can be patrolling or dormant
    },
    rareEntities = {
        "heavy_assault_robot", "command_robot", "shield_drone_heavy" -- heavy_assault_robot may be dormant
    },
    -- Special GM-Activated Entity Pool
    gmActivatedEntities = {
        "gm_war_automaton_colossus", "orbital_strike_beacon_entity", "phase_shift_assassin_bot"
    },

    -- Specific biome properties
    isMilitary = true,
    isFortified = true,
    isAutomated = true,
    controllingFaction = "automaton_defense_network", -- Or specific military AI
    alertStatus = "dormant", -- dormant, low_alert, medium_alert, high_alert, lockdown, gm_override_active
    gmControlEnabled = true, -- Flag indicating GM can interact with this biome's systems

    -- Biome variants for diverse generation
    variants = {
        dormant_bunker = {
            name = "Dormant Bunker",
            alertStatus = "dormant",
            description = "An inactive military bunker, defenses offline unless provoked.",
            entityAdjustments = { sentry_drone_dormant=1.5, military_robot_soldier=0.2 },
            specialEffects = {"low_power_mode", "silent_atmosphere", "dust_layers"}
        },
        active_defense_post = {
            name = "Active Defense Post",
            alertStatus = "low_alert",
            description = "A functioning military base actively monitoring its perimeter.",
            entityAdjustments = { military_robot_soldier=1.2, security_camera_active=1.5, automated_turret=1.1 },
            specialEffects = {"active_patrols", "humming_energy_fields", "regular_status_pings"}
        },
        prototype_weapons_lab = {
            name = "Prototype Weapons Lab",
            alertStatus = "medium_alert",
            description = "A high-security wing focused on developing and testing advanced robot weaponry.",
            tileAdjustments = { prototype_lab_tile=0.1, containment_field_tile=0.05 },
            entityAdjustments = { heavy_assault_robot=1.1, tech_drone_secure=1.5, experimental_weapon_entity=1.0 },
            hazards = { {type="prototype_misfire", chance=0.1, effect="random_high_damage_effect"} }
        },
         planetary_cannon_control = {
            name = "Planetary Cannon Control",
            alertStatus = "high_alert",
            description = "A heavily defended facility housing the control system for a massive orbital defense weapon.",
            tileAdjustments = { command_bunker_tile = 0.15, power_core_military = 0.05, weapon_targeting_console=0.02 },
            entityAdjustments = { command_robot=1.2, heavy_security_robot=1.5, elite_guard_robot=1.0},
            specialEffects = {"massive_energy_signature", "fortified_command_center", "weapon_charge_indicator"}
         }
    },

    -- Structures (Key areas within the base)
    structures = {
        { name = "Main Command Bunker", chance = 0.1, unique=true },
        { name = "Vehicle/Heavy Robot Bay", chance = 0.15 },
        { name = "Troop Deployment Zone", chance = 0.2 }, -- Where standard soldiers are stored/activated
        { name = "Primary Power Core", chance = 0.1 },
        { name = "Perimeter Defense Control", chance = 0.25 },
        { name = "Armory / Weapons Storage", chance = 0.15 },
        { name = "Detention Block", chance = 0.05 }
    },

    -- Environmental effects (Focus on security state)
    environmentalEffects = {
        security_grid_status = 1.0, -- Integrity of local security network
        power_level = 0.2, -- Base power level (dormant), increases on alert
        intrusion_detection_level = 0.8, -- Sensitivity of sensors
        gm_override_signal = false -- Flag set by GM command
    },

    -- Unique features generation
    features = {
        { name = "Heavy Blast Doors", chance = 0.3 }, -- Controllable barrier
        { name = "Force Field Corridors", chance = 0.2 }, -- Energy barriers
        { name = "Automated Turret Network", chance = 0.4 }, -- Places turrets
        { name = "Vehicle Construction/Repair Bay", chance = 0.1 }, -- Places vehicle related entities/tiles
        { name = "Tactical Holo-Table", chance = 0.08, requiresStructure="Main Command Bunker" }, -- Interactive display
        { name = "GM Activation Console", chance = 1.0, unique=true }, -- Special console for GM use
        { name = "Prototype Stasis Pod", chance = 0.05, requiresVariant="prototype_weapons_lab"} -- Holds rare entity/item
    },

    -- Generation algorithm (Placeholder - Needs highly structured layout)
    generate = function(chunk, world, variant)
        print("Generating " .. (variant and RobotMilitaryBaseBiome.variants[variant].name or "Automated Fortress") .. " biome")
        local variantData = variant and RobotMilitaryBaseBiome.variants[variant] or nil
        local alertStatus = variantData and variantData.alertStatus or RobotMilitaryBaseBiome.alertStatus

        -- Adjust tile distribution
        -- ... (similar logic) ...

        -- Initialize 2D tiles array if not exists
        chunk.tiles = chunk.tiles or {}
        for x = 0, world.CHUNK_SIZE - 1 do
            chunk.tiles[x] = chunk.tiles[x] or {}
        end

        -- Generate tiles
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                local worldX = chunk.x * world.CHUNK_SIZE + x
                local worldY = chunk.y * world.CHUNK_SIZE + y

                local layoutNoise = pnoise2D(worldX, worldY, 0.1, 1) -- Defines rooms/corridors
                local defenseNoise = pnoise2D(worldX, worldY, 0.2, 2) -- Placement of defenses

                local tileType = "reinforced_metal_floor" -- Default floor
                local passable = true

                -- Crude wall placement for structure
                if layoutNoise < -0.6 or layoutNoise > 0.6 or (math.fmod(worldX,10)<1 or math.fmod(worldY,10)<1) then -- Outer walls and some internal structure
                    tileType = "bunker_wall"
                    passable = false
                -- Place defenses based on noise
                elseif defenseNoise > 0.7 then
                    tileType = "weapon_emplacement_tile"
                    passable = false -- Assume emplacement blocks direct path
                elseif defenseNoise < -0.7 then
                    tileType = "force_field_emitter" -- Emitter itself might be small obstacle
                    passable = true -- Tile itself passable, field is the barrier entity/effect
                end

                -- Apply rare tiles based on distribution (simplified)
                -- ...

                -- Create the tile
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = math.random(1, 2),
                    isMilitary = true,
                    passable = passable,
                    isUnderground = true -- Assume usually underground/shielded
                }

                -- Add to chunk using 2D indexing
                chunk.tiles[x][y] = tile
            end
        end

        -- Call helper functions
        RobotMilitaryBaseBiome.generateFeatures(chunk, world, variant)
        RobotMilitaryBaseBiome.populateEntities(chunk, world, variant)
        RobotMilitaryBaseBiome.generateStructures(chunk, world, variant)
        RobotMilitaryBaseBiome.postProcess(chunk, world, variant)
    end,

    -- Helper function implementations (Placeholders)
    generateFeatures = function(chunk, world, variant)
        print("Generating Military Base features...")
        -- Place blast doors, force fields, turrets, holo-tables, GM console
        -- Ensure GM console is placed in a secure location (Command Bunker?)
    end,

    populateEntities = function(chunk, world, variant)
        print("Populating Military Base entities...")
        local variantData = variant and RobotMilitaryBaseBiome.variants[variant] or nil
        local alertStatus = variantData and variantData.alertStatus or RobotMilitaryBaseBiome.alertStatus

        local function spawnEntity(entityType, count, stateOverride)
            local multiplier = 1.0
            if variantData and variantData.entityAdjustments and variantData.entityAdjustments[entityType] then 
                multiplier = variantData.entityAdjustments[entityType] 
            end
            local adjustedCount = math.floor(count * multiplier + 0.5)
            for i = 1, adjustedCount do
                local attempts = 0; local placed = false
                while attempts < 5 and not placed do
                    local x = math.random(0, world.CHUNK_SIZE - 1)
                    local y = math.random(0, world.CHUNK_SIZE - 1)
                    local tile = chunk.tiles[x][y]
                    if tile and tile.passable then
                        local props = { 
                            factionID = RobotMilitaryBaseBiome.factionID,
                            alertStatus = stateOverride or alertStatus
                        }
                        world.entitySystem:addEntity(entityType, chunk.x * world.CHUNK_SIZE + x, chunk.y * world.CHUNK_SIZE + y, props)
                        placed = true
                    end
                    attempts = attempts + 1
                end
            end
        end
        -- Spawn based on lists
        for _, et in ipairs(RobotMilitaryBaseBiome.commonEntities) do spawnEntity(et, math.random(3,8)) end
        for _, et in ipairs(RobotMilitaryBaseBiome.uncommonEntities) do spawnEntity(et, math.random(1,4)) end
        for _, et in ipairs(RobotMilitaryBaseBiome.rareEntities) do if math.random() < 0.1 then spawnEntity(et, 1) end end
    end,

    generateStructures = function(chunk, world, variant)
        print("Generating Military Base structures...")
        for _, structure in ipairs(RobotMilitaryBaseBiome.structures) do
            if math.random() < structure.chance then
                if not structure.requiresVariant or structure.requiresVariant == variant then
                    local attempts = 0; local placed = false; local sx, sy
                    while attempts < 10 and not placed do
                        sx = math.random(2, world.CHUNK_SIZE - 3)
                        sy = math.random(2, world.CHUNK_SIZE - 3)
                        local tile = chunk.tiles[sx][sy]
                        if tile and tile.passable and tile.type == "reinforced_metal_floor" then placed = true end
                        attempts = attempts + 1
                    end
                    if placed then
                        local worldX = chunk.x * world.CHUNK_SIZE + sx
                        local worldY = chunk.y * world.CHUNK_SIZE + sy
                        print("Marking structure area: "..structure.name.." at "..worldX..","..worldY)
                        world.entitySystem:addEntity("structure_poi", worldX, worldY, { 
                            structureType = structure.name, 
                            description="The "..structure.name.." is centered here." 
                        })
                    end
                end
            end
        end
    end,

    postProcess = function(chunk, world, variant)
        print("Post-processing Military Base chunk...")
        -- Set initial alert status, power level, link defenses to control areas.
        local variantData = variant and RobotMilitaryBaseBiome.variants[variant] or nil
        local alertStatus = variantData and variantData.alertStatus or RobotMilitaryBaseBiome.alertStatus
        chunk.environmentalEffects = chunk.environmentalEffects or {}
        table.insert(chunk.environmentalEffects, {type="security_alert_level", level=alertStatus})
        table.insert(chunk.environmentalEffects, {type="gm_control_active", status=RobotMilitaryBaseBiome.gmControlEnabled})
        -- Add effects based on alert status (e.g., active force fields)
    end,

    -- Function for GM to activate defenses / spawn OP units
    gmActivateDefense = function(chunkOrWorldPos, intensity)
         print("GM ACTIVATION RECEIVED: Intensity " .. intensity)
         -- Find relevant chunk(s)
         -- Change chunk.environmentalEffects alert status to high/gm_override
         -- Spawn entities from gmActivatedEntities list based on intensity
         -- Activate dormant defenses (turrets, force fields)
         -- Example: world.entitySystem:addEntity(RobotMilitaryBaseBiome.gmActivatedEntities[1], worldX, worldY)
    end,


    init = function(worldCore)
        print("Robot Military Base biome module initialized")
        RobotMilitaryBaseBiome.worldCore = worldCore
        print("Robot Military Base biome registered successfully for later use")
    end,

    registerWithWorld = function(world)
        print("Registering Robot Military Base biome generators with world")
        if not world or not world.chunkSystem then print("Warning: Cannot register Military Base biome - world or chunkSystem is nil") return false end
        world.chunkSystem:registerGenerator(RobotMilitaryBaseBiome.id, RobotMilitaryBaseBiome.generate)
        for variantId, variantData in pairs(RobotMilitaryBaseBiome.variants) do
            local fullVariantId = RobotMilitaryBaseBiome.id .. "_" .. variantId
            world.chunkSystem:registerGenerator(fullVariantId, function(chunk, worldInstance)
                RobotMilitaryBaseBiome.generate(chunk, worldInstance, variantId)
            end)
        end
        -- Register GM function globally? Or associate with biome instance?
        -- world:registerGMCommand("activate_robot_base", RobotMilitaryBaseBiome.gmActivateDefense)
        return true
    end,
}

return RobotMilitaryBaseBiome