-- weather_system.lua
-- Weather system for Lazarus Arc game
-- Manages weather patterns, transitions, and effects on the game world
--
-- [2023-07-15] FIXED: Added type checking for dt parameter in update function
-- The weather system was attempting to perform arithmetic on dt when it was sometimes
-- passed as a table instead of a number. Added checks to handle this case by using
-- a default value of 0.016 (equivalent to 60 FPS) when dt is not a number.
-- Dynamically loads all weather types with no hardcoded names

local Engine = require("engine") -- Add reference to the Engine

local WeatherSystem = {
    initialized = false,
    currentWeather = nil,
    nextWeather = nil,
    transitionProgress = 0,
    transitionDuration = 10, -- seconds for weather transition
    weatherTypes = {}, -- Loaded weather type modules
    loadedWeatherTypes = {}, -- Track which weather types have been loaded
    defaultWeather = nil, -- Set during initialization from first loaded weather
    updateInterval = 1, -- seconds between weather update checks
    lastUpdateTime = 0,
    season = nil, -- Current season if seasonality is enabled
    weatherHistory = {}, -- Track recent weather for patterns
    historySize = 5,
    weatherQueue = {}, -- Queue planned weather changes
    debugMode = false,

    -- Silence management for natural ambient sound breaks
    silenceManager = {
        activeSounds = {}, -- Track active sounds with their timers
        silencePeriods = {}, -- Track when sounds should be silent
        ambientSoundInterval = {min = 15, max = 45}, -- Seconds between ambient sound plays
        ambientSoundDuration = {min = 8, max = 20}, -- How long ambient sounds play
        lastSilenceCheck = 0,
        silenceCheckInterval = 1 -- Check every second
    }
}

-- Initialize the weather system
function WeatherSystem.init(world, options)
    print("Initializing weather system...")
    options = options or {}

    -- Apply options
    if options.updateInterval then WeatherSystem.updateInterval = options.updateInterval end
    if options.transitionDuration then WeatherSystem.transitionDuration = options.transitionDuration end
    if options.debugMode ~= nil then WeatherSystem.debugMode = options.debugMode end
    if options.season ~= nil then WeatherSystem.season = options.season end

    -- Set reference to world
    WeatherSystem.world = world

    -- Load all weather types from the weather folder
    local weatherTypeCount = WeatherSystem.loadAllWeatherTypes()

    -- Set initial weather
    if options.defaultWeather and WeatherSystem.weatherTypes[options.defaultWeather] then
        WeatherSystem.defaultWeather = options.defaultWeather
    else
        -- Use first loaded weather as default if not specified
        for weatherName, _ in pairs(WeatherSystem.loadedWeatherTypes) do
            WeatherSystem.defaultWeather = weatherName
            break
        end
    end

    -- Ensure we have at least one weather type
    if not WeatherSystem.defaultWeather then
        print("Error: No weather types loaded!")
        return false
    end

    -- Set initial weather
    WeatherSystem.setWeather(WeatherSystem.defaultWeather, true)

    WeatherSystem.initialized = true
    WeatherSystem.lastUpdateTime = love.timer.getTime()

    print("Weather system initialized with " .. weatherTypeCount .. " weather types")
    print("Default weather: " .. WeatherSystem.defaultWeather)

    return WeatherSystem
end

-- Load all weather types from the weather folder
function WeatherSystem.loadAllWeatherTypes()
    local weatherFolder = "weather"
    local weatherCount = 0

    -- Get list of files in the weather folder
    local files = love.filesystem.getDirectoryItems(weatherFolder)

    for _, file in ipairs(files) do
        -- Check if it's a weather pattern file
        if file:match("^weather_.+%.lua$") then
            local weatherName = file:gsub("^weather_(.+)%.lua$", "%1")
            local success = WeatherSystem.loadWeatherType(weatherName)
            if success then
                weatherCount = weatherCount + 1
            end
        end
    end

    return weatherCount
end

-- Load a specific weather type
function WeatherSystem.loadWeatherType(weatherName)
    -- Skip if already loaded
    if WeatherSystem.loadedWeatherTypes[weatherName] then
        return true
    end

    -- Try to load the weather module with the correct path
    local path = "weather.weather_" .. weatherName
    local success, weatherModule = pcall(require, path)

    if success and type(weatherModule) == "table" then
        -- Ensure module has essential functions
        if not weatherModule.init or not weatherModule.update or not weatherModule.cleanUp then
            print("Warning: Weather type '" .. weatherName .. "' is missing required functions")
        end

        -- Initialize ID if not present
        weatherModule.id = weatherModule.id or "weather_" .. weatherName

        -- Store the weather module
        WeatherSystem.weatherTypes[weatherName] = weatherModule
        WeatherSystem.loadedWeatherTypes[weatherName] = true

        if WeatherSystem.debugMode then
            print("Loaded weather type: " .. weatherName)
        end

        return true
    else
        print("Failed to load weather type: " .. weatherName)
        print(weatherModule) -- Show error
        return false
    end
end

-- Get weather type by name, loading it if necessary
function WeatherSystem.getWeatherType(weatherName)
    -- Try to get already loaded weather
    if WeatherSystem.weatherTypes[weatherName] then
        return WeatherSystem.weatherTypes[weatherName]
    end

    -- Try to load the weather type
    if WeatherSystem.loadWeatherType(weatherName) then
        return WeatherSystem.weatherTypes[weatherName]
    end

    -- Fall back to default if requested weather can't be loaded
    if weatherName ~= WeatherSystem.defaultWeather then
        print("Warning: Weather type '" .. weatherName .. "' not found, using default instead")
        return WeatherSystem.getWeatherType(WeatherSystem.defaultWeather)
    end

    -- If even default weather can't be loaded, create minimal weather
    print("Warning: Could not load any weather types, using minimal default")
    return {
        id = "minimal",
        name = "Minimal Default",
        visual = { ambientLightLevel = 1.0 },
        environment = {},
        init = function() end,
        update = function() end,
        cleanUp = function() end
    }
end

-- Set the current weather
function WeatherSystem.setWeather(weatherName, instant)
    if not WeatherSystem.initialized then return false end

    -- Get the weather module
    local weatherModule = WeatherSystem.getWeatherType(weatherName)

    if not weatherModule then
        print("Error: Could not set weather to " .. weatherName)
        return false
    end

    -- If already this weather, do nothing
    if WeatherSystem.currentWeather and WeatherSystem.currentWeather.id == weatherModule.id then
        return true
    end

    if instant or not WeatherSystem.currentWeather then
        -- Instant change

        -- Clean up previous weather if any
        if WeatherSystem.currentWeather and WeatherSystem.currentWeather.cleanUp then
            WeatherSystem.currentWeather.cleanUp(WeatherSystem.world)
        end

        -- Set new weather
        WeatherSystem.currentWeather = weatherModule

        -- Initialize new weather
        if WeatherSystem.currentWeather.init then
            WeatherSystem.currentWeather.init(WeatherSystem.world)
        end

        -- Add to history
        WeatherSystem.addToHistory(weatherName)

        -- Reset transition
        WeatherSystem.nextWeather = nil
        WeatherSystem.transitionProgress = 0

        print("Weather changed to: " .. weatherModule.name)
    else
        -- Gradual transition
        WeatherSystem.nextWeather = weatherModule
        WeatherSystem.transitionProgress = 0

        print("Weather transitioning to: " .. weatherModule.name)
    end

    return true
end

-- Queue a weather change for later
function WeatherSystem.queueWeather(weatherName, delay)
    if not WeatherSystem.weatherTypes[weatherName] then
        print("Warning: Tried to queue unknown weather type: " .. weatherName)
        return false
    end

    table.insert(WeatherSystem.weatherQueue, {
        weatherName = weatherName,
        delay = delay or 0,
        remaining = delay or 0
    })

    return true
end

-- Add weather to history
function WeatherSystem.addToHistory(weatherName)
    -- Add to start of history
    table.insert(WeatherSystem.weatherHistory, 1, weatherName)

    -- Trim history if needed
    if #WeatherSystem.weatherHistory > WeatherSystem.historySize then
        table.remove(WeatherSystem.weatherHistory)
    end
end

-- Check if a weather type is in recent history
function WeatherSystem.isRecentWeather(weatherName, lookback)
    lookback = lookback or WeatherSystem.historySize

    for i = 1, math.min(lookback, #WeatherSystem.weatherHistory) do
        if WeatherSystem.weatherHistory[i] == weatherName then
            return true
        end
    end

    return false
end

-- Get the weather that would naturally follow based on transition probabilities
function WeatherSystem.getNextNaturalWeather(currentWeatherName)
    -- Get current weather module
    local currentWeather = WeatherSystem.getWeatherType(currentWeatherName)
    if not currentWeather or not currentWeather.transitions then
        -- If no transitions defined, just pick a random weather
        return WeatherSystem.getRandomWeather()
    end

    -- Build weighted list of possible next weather types
    local possibilities = {}
    local totalWeight = 0

    for weatherName, probability in pairs(currentWeather.transitions) do
        -- Skip weather types that don't exist
        if not WeatherSystem.weatherTypes[weatherName] then
            if WeatherSystem.debugMode then
                print("Warning: Transition to non-existent weather type: " .. weatherName)
            end
            goto continue
        end

        -- Apply seasonal modifier if season is set
        local seasonalModifier = 1.0
        if WeatherSystem.season and currentWeather.seasonalModifiers and
           currentWeather.seasonalModifiers[WeatherSystem.season] and
           currentWeather.seasonalModifiers[WeatherSystem.season][weatherName] then
            seasonalModifier = currentWeather.seasonalModifiers[WeatherSystem.season][weatherName]
        end

        -- Apply recency modifier (less likely to get the same weather repeatedly)
        local recencyModifier = 1.0
        if WeatherSystem.isRecentWeather(weatherName, 2) then
            recencyModifier = 0.5
        end

        -- Calculate final weight
        local weight = probability * seasonalModifier * recencyModifier

        -- Add to possibilities if weight is positive
        if weight > 0 then
            table.insert(possibilities, {
                weather = weatherName,
                weight = weight
            })
            totalWeight = totalWeight + weight
        end

        ::continue::
    end

    -- Add small chance to stay the same weather
    if not WeatherSystem.isRecentWeather(currentWeatherName, 2) then
        local stayWeight = 0.1  -- Small chance to keep current weather
        table.insert(possibilities, {
            weather = currentWeatherName,
            weight = stayWeight
        })
        totalWeight = totalWeight + stayWeight
    end

    -- If no valid transitions, pick a random weather
    if #possibilities == 0 or totalWeight == 0 then
        return WeatherSystem.getRandomWeather()
    end

    -- Pick a weather based on weights
    local randomValue = math.random() * totalWeight
    local cumulativeWeight = 0

    for _, possibility in ipairs(possibilities) do
        cumulativeWeight = cumulativeWeight + possibility.weight
        if randomValue <= cumulativeWeight then
            return possibility.weather
        end
    end

    -- Fallback to default if something went wrong
    return WeatherSystem.defaultWeather
end

-- Get a random weather type
function WeatherSystem.getRandomWeather()
    local weatherTypes = {}
    for name, _ in pairs(WeatherSystem.loadedWeatherTypes) do
        table.insert(weatherTypes, name)
    end

    if #weatherTypes == 0 then
        return WeatherSystem.defaultWeather
    end

    return weatherTypes[math.random(1, #weatherTypes)]
end

-- Play a weather sound using the synth orchestra
function WeatherSystem.playSound(soundName, options)
    -- Handle legacy calls where options might be a string (old format)
    if type(options) ~= "table" then
        local oldOptions = options
        options = {}
        if type(oldOptions) == "string" then
            options.legacy = oldOptions -- Store the original value just in case
        end
    end

    -- Check if sound name is valid
    if not soundName then
        print("Warning: Attempted to play a nil weather sound")
        return false
    end

    options = options or {}
    options.verbose = (options.verbose ~= false) -- Default to verbose unless explicitly set to false

    -- Check if this sound should be in a silence period
    if WeatherSystem.shouldBeSilent(soundName, options) then
        if options.verbose then
            print("Sound " .. tostring(soundName) .. " is in silence period")
        end
        return false
    end

    -- Try to use synth orchestra first with current weather's sound config
    local SynthOrchestra = require("utils.synth_orchestra")
    if SynthOrchestra and SynthOrchestra.initialized and WeatherSystem.currentWeather then
        local soundConfig = nil

        -- Look for sound configuration in current weather
        if WeatherSystem.currentWeather.synthSounds and WeatherSystem.currentWeather.synthSounds[soundName] then
            soundConfig = WeatherSystem.currentWeather.synthSounds[soundName]
        end

        if soundConfig then
            -- Add variation to the sound config
            local variedConfig = WeatherSystem.addSoundVariation(soundConfig)

            local source = SynthOrchestra.generateFromConfig(variedConfig)
            if source then
                -- Set volume and loop options
                if options.volume then
                    source:setVolume(options.volume)
                end
                if options.loop then
                    source:setLooping(options.loop)
                end

                -- Play the sound
                source:play()

                -- Store reference for stopping later
                WeatherSystem.activeSounds = WeatherSystem.activeSounds or {}
                WeatherSystem.activeSounds[soundName] = source

                if options.verbose then
                    print("Playing weather sound via synth orchestra: " .. tostring(soundName))
                end
                return source
            end
        end
    end

    -- Fallback to engine sound system if available
    if type(Engine) == "table" and Engine.systems and Engine.systems.soundSystem then
        local prefixedName
        if type(soundName) == "string" then
            prefixedName = soundName:match("^weather_") and soundName or ("weather_" .. soundName)
        else
            prefixedName = "weather_unknown"
            print("Warning: Non-string sound name passed to WeatherSystem.playSound: " .. tostring(soundName))
        end

        local success, result = pcall(function()
            return Engine.systems.soundSystem.playSound(prefixedName, options)
        end)

        if not success or not result then
            -- Try without prefix as fallback
            if type(soundName) == "string" and not soundName:match("^weather_") then
                success, result = pcall(function()
                    return Engine.systems.soundSystem.playSound(soundName, options)
                end)
            end
        end

        if success and result then
            return result
        end
    end

    -- If all else fails, print warning
    if options.verbose then
        print("Warning: Failed to play weather sound: " .. tostring(soundName))
    end
    return false
end

-- Stop a weather sound
function WeatherSystem.stopSound(soundName)
    if not soundName then
        print("Warning: Attempted to stop a nil weather sound")
        return false
    end

    -- Ensure soundName is a string
    local soundNameStr = soundName
    if type(soundName) ~= "string" then
        soundNameStr = tostring(soundName)
        print("Warning: Non-string sound name passed to WeatherSystem.stopSound: " .. soundNameStr)
    end

    -- Try to stop synth orchestra sound first
    WeatherSystem.activeSounds = WeatherSystem.activeSounds or {}
    if WeatherSystem.activeSounds[soundNameStr] then
        local source = WeatherSystem.activeSounds[soundNameStr]
        if source and source.stop then
            source:stop()
        end
        WeatherSystem.activeSounds[soundNameStr] = nil
        return true
    end

    -- Fallback to engine sound system if available
    if type(Engine) == "table" and Engine.systems and Engine.systems.soundSystem then
        -- Try with weather_ prefix first
        local prefixedName = soundNameStr
        if type(soundNameStr) == "string" and not soundNameStr:match("^weather_") then
            prefixedName = "weather_" .. soundNameStr
        end

        local success, result = pcall(function()
            return Engine.systems.soundSystem.stopSound(prefixedName)
        end)

        -- Try original name as fallback
        if not success or not result then
            if type(soundNameStr) == "string" and not soundNameStr:match("^weather_") then
                success, result = pcall(function()
                    return Engine.systems.soundSystem.stopSound(soundNameStr)
                end)
            end
        end
    end

    return true -- Return true regardless as we don't want to cause issues if stopping fails
end

-- Add variation to weather sound configurations for more dynamic audio
function WeatherSystem.addSoundVariation(baseConfig)
    if not baseConfig then return nil end

    -- Create a copy of the base configuration
    local variedConfig = {}
    for k, v in pairs(baseConfig) do
        if type(v) == "table" then
            variedConfig[k] = {}
            for i, item in ipairs(v) do
                variedConfig[k][i] = item
            end
        else
            variedConfig[k] = v
        end
    end

    -- Add pitch variation (±10% randomly)
    if variedConfig.notes then
        for i, note in ipairs(variedConfig.notes) do
            local variation = 0.9 + (math.random() * 0.2) -- 0.9 to 1.1 multiplier
            variedConfig.notes[i] = note * variation
        end
    end

    -- Add slight volume variation (±15%)
    if variedConfig.volume then
        local volumeVariation = 0.85 + (math.random() * 0.3) -- 0.85 to 1.15 multiplier
        variedConfig.volume = math.max(0.1, math.min(1.0, variedConfig.volume * volumeVariation))
    end

    -- Add duration variation (±20%)
    if variedConfig.durations then
        for i, duration in ipairs(variedConfig.durations) do
            local durationVariation = 0.8 + (math.random() * 0.4) -- 0.8 to 1.2 multiplier
            variedConfig.durations[i] = duration * durationVariation
        end
    elseif variedConfig.duration then
        local durationVariation = 0.8 + (math.random() * 0.4)
        variedConfig.duration = variedConfig.duration * durationVariation
    end

    -- Occasionally vary vibrato settings
    if variedConfig.vibrato and math.random() < 0.3 then
        if variedConfig.vibratoRate then
            local vibratoVariation = 0.7 + (math.random() * 0.6) -- 0.7 to 1.3 multiplier
            variedConfig.vibratoRate = variedConfig.vibratoRate * vibratoVariation
        end
        if variedConfig.vibratoDepth then
            local depthVariation = 0.5 + (math.random() * 1.0) -- 0.5 to 1.5 multiplier
            variedConfig.vibratoDepth = math.min(0.5, variedConfig.vibratoDepth * depthVariation)
        end
    end

    return variedConfig
end

-- Check if a sound should be silent based on natural ambient patterns
function WeatherSystem.shouldBeSilent(soundName, options)
    options = options or {}

    -- Don't apply silence to non-looping sounds or important sounds
    if not options.loop or options.important then
        return false
    end

    -- Don't apply silence to non-ambient sounds (thunder, etc.)
    if soundName and (soundName:match("thunder") or soundName:match("gust") or soundName:match("eruption")) then
        return false
    end

    local currentTime = love.timer.getTime()
    local silenceManager = WeatherSystem.silenceManager

    -- Initialize silence period for this sound if not exists
    if not silenceManager.silencePeriods[soundName] then
        silenceManager.silencePeriods[soundName] = {
            nextSilenceStart = currentTime + math.random(silenceManager.ambientSoundInterval.min, silenceManager.ambientSoundInterval.max),
            silenceEnd = 0,
            isInSilence = false
        }
    end

    local soundSilence = silenceManager.silencePeriods[soundName]

    -- Check if we should start a silence period
    if not soundSilence.isInSilence and currentTime >= soundSilence.nextSilenceStart then
        soundSilence.isInSilence = true
        local silenceDuration = math.random(5, 15) -- 5-15 seconds of silence
        soundSilence.silenceEnd = currentTime + silenceDuration

        -- Stop the currently playing sound if it exists
        WeatherSystem.stopSound(soundName)

        if WeatherSystem.debugMode then
            print("Starting silence period for " .. tostring(soundName) .. " for " .. silenceDuration .. " seconds")
        end
    end

    -- Check if silence period should end
    if soundSilence.isInSilence and currentTime >= soundSilence.silenceEnd then
        soundSilence.isInSilence = false
        -- Schedule next silence period
        soundSilence.nextSilenceStart = currentTime + math.random(silenceManager.ambientSoundInterval.min, silenceManager.ambientSoundInterval.max)

        if WeatherSystem.debugMode then
            print("Ending silence period for " .. tostring(soundName))
        end
    end

    return soundSilence.isInSilence
end

-- Update silence manager (called from weather system update)
function WeatherSystem.updateSilenceManager(dt)
    local currentTime = love.timer.getTime()
    local silenceManager = WeatherSystem.silenceManager

    -- Only check every second to avoid excessive processing
    if currentTime - silenceManager.lastSilenceCheck < silenceManager.silenceCheckInterval then
        return
    end

    silenceManager.lastSilenceCheck = currentTime

    -- Clean up old silence periods for sounds that are no longer active
    for soundName, silencePeriod in pairs(silenceManager.silencePeriods) do
        -- If sound hasn't been checked in a while and isn't currently playing, remove it
        if not WeatherSystem.activeSounds[soundName] and
           currentTime - (silencePeriod.lastCheck or 0) > 60 then -- 1 minute cleanup
            silenceManager.silencePeriods[soundName] = nil
        else
            silencePeriod.lastCheck = currentTime
        end
    end
end

-- Get weather sound configuration for synth orchestra
function WeatherSystem.getWeatherSoundConfig(soundName)
    -- Weather sound definitions for synth orchestra
    local weatherSounds = {
        -- Rain sounds
        rain_ambient = {
            notes = {220, 165, 110}, -- A3, E3, A2
            durations = {2.0, 2.0, 2.0},
            instrument = "soft_pad",
            volume = 0.3,
            vibrato = true,
            vibratoRate = 0.5,
            vibratoDepth = 0.1
        },
        heavy_rain = {
            notes = {220, 165, 110, 82.5}, -- A3, E3, A2, E2
            durations = {1.5, 1.5, 1.5, 1.5},
            instrument = "soft_pad",
            volume = 0.5,
            vibrato = true,
            vibratoRate = 0.8,
            vibratoDepth = 0.15
        },

        -- Thunder sounds
        thunder = {
            notes = {55, 73.5, 98}, -- A1, D2, G2
            durations = {0.3, 0.5, 0.8},
            instrument = "bass_guitar",
            volume = 0.8,
            vibrato = false
        },

        -- Wind sounds
        wind_ambient = {
            notes = {330, 247, 185}, -- E4, B3, F#3
            durations = {3.0, 3.0, 3.0},
            instrument = "flute",
            volume = 0.25,
            vibrato = true,
            vibratoRate = 0.3,
            vibratoDepth = 0.2
        },

        -- Volcanic sounds
        volcanic_ambient = {
            notes = {65.5, 87.5, 110}, -- C2, F2, A2
            durations = {2.5, 2.5, 2.5},
            instrument = "bass_guitar",
            volume = 0.4,
            vibrato = true,
            vibratoRate = 0.2,
            vibratoDepth = 0.1
        },

        -- Snow sounds
        snow_ambient = {
            notes = {523, 659, 784], -- C5, E5, G5
            durations = {1.0, 1.0, 1.0},
            instrument = "xylophone",
            volume = 0.2,
            vibrato = false
        }
    }

    return weatherSounds[soundName]
end

-- Update weather system state
function WeatherSystem.update(dt)
    if not WeatherSystem.initialized then return end

    -- Update silence manager for natural ambient sound breaks
    WeatherSystem.updateSilenceManager(dt)

    -- Process weather transitions
    if WeatherSystem.nextWeather then
        -- FIXED: Check if dt is a number before performing arithmetic
        local deltaTime = dt
        if type(dt) == "table" then
            -- If dt is a table, use a default value
            print("WARNING: dt is a table in WeatherSystem.update. Using default value of 0.016")
            deltaTime = 0.016  -- Default to 60 FPS (1/60)
        end
        WeatherSystem.transitionProgress = WeatherSystem.transitionProgress + (deltaTime / WeatherSystem.transitionDuration)

        if WeatherSystem.transitionProgress >= 1.0 then
            -- Transition complete, switch to next weather
            if WeatherSystem.currentWeather and WeatherSystem.currentWeather.cleanUp then
                WeatherSystem.currentWeather.cleanUp(WeatherSystem.world)
            end

            -- Add to history
            WeatherSystem.addToHistory(WeatherSystem.nextWeather.id)

            -- Set as current
            WeatherSystem.currentWeather = WeatherSystem.nextWeather
            WeatherSystem.nextWeather = nil
            WeatherSystem.transitionProgress = 0

            -- Initialize new weather
            if WeatherSystem.currentWeather.init then
                WeatherSystem.currentWeather.init(WeatherSystem.world)
            end

            print("Weather transition complete: " .. WeatherSystem.currentWeather.name)
        end
    end

    -- Update current weather
    if WeatherSystem.currentWeather and WeatherSystem.currentWeather.update then
        -- FIXED: Check if dt is a number before passing it to the update function
        local deltaTime = dt
        if type(dt) == "table" then
            -- If dt is a table, use a default value
            print("WARNING: dt is a table in WeatherSystem.currentWeather.update. Using default value of 0.016")
            deltaTime = 0.016  -- Default to 60 FPS (1/60)
        end
        WeatherSystem.currentWeather.update(WeatherSystem.world, deltaTime)
    end

    -- Process queued weather changes
    for i = #WeatherSystem.weatherQueue, 1, -1 do
        local queuedWeather = WeatherSystem.weatherQueue[i]
        -- FIXED: Check if dt is a number before performing arithmetic
        local deltaTime = dt
        if type(dt) == "table" then
            -- If dt is a table, use a default value
            print("WARNING: dt is a table in WeatherSystem.weatherQueue processing. Using default value of 0.016")
            deltaTime = 0.016  -- Default to 60 FPS (1/60)
        end
        queuedWeather.remaining = queuedWeather.remaining - deltaTime

        if queuedWeather.remaining <= 0 then
            -- Time to change weather
            WeatherSystem.setWeather(queuedWeather.weatherName)
            table.remove(WeatherSystem.weatherQueue, i)
        end
    end

    -- Check for random weather changes
    local currentTime = love.timer.getTime()
    if currentTime - WeatherSystem.lastUpdateTime >= WeatherSystem.updateInterval then
        WeatherSystem.lastUpdateTime = currentTime

        -- Consider a natural weather change (small chance per update)
        if math.random() < 0.01 and not WeatherSystem.nextWeather and #WeatherSystem.weatherQueue == 0 then
            local nextWeather = WeatherSystem.getNextNaturalWeather(WeatherSystem.currentWeather.id)

            if nextWeather ~= WeatherSystem.currentWeather.id then
                WeatherSystem.setWeather(nextWeather)
            end
        end
    end
end

-- Force a specific weather in an area
function WeatherSystem.forceWeatherInArea(weatherName, centerX, centerY, radius, duration)
    -- In a full implementation, this would create a localized weather zone
    -- For now, we'll just set the weather globally with the specified duration
    if not WeatherSystem.weatherTypes[weatherName] then
        print("Warning: Tried to force unknown weather type: " .. weatherName)
        return false
    end

    WeatherSystem.setWeather(weatherName)

    if duration then
        -- Schedule return to natural weather
        WeatherSystem.queueWeather(WeatherSystem.getNextNaturalWeather(weatherName), duration)
    end

    return true
end

-- Set the current season
function WeatherSystem.setSeason(season)
    -- Season can be any string, defined by the game
    -- Season modifiers are defined in individual weather types
    WeatherSystem.season = season
    print("Weather season set to: " .. season)
    return true
end

-- Get current weather information (for UI)
function WeatherSystem.getCurrentWeatherInfo()
    if not WeatherSystem.currentWeather then
        return {
            id = "none",
            name = "None",
            description = "No weather active"
        }
    end

    return {
        id = WeatherSystem.currentWeather.id,
        name = WeatherSystem.currentWeather.name,
        description = WeatherSystem.currentWeather.description or "Weather is " .. WeatherSystem.currentWeather.name,
        visual = WeatherSystem.currentWeather.visual,
        environment = WeatherSystem.currentWeather.environment,
        transitioning = WeatherSystem.nextWeather ~= nil,
        transitionProgress = WeatherSystem.transitionProgress,
        nextWeather = WeatherSystem.nextWeather and WeatherSystem.nextWeather.name or nil,
        season = WeatherSystem.season
    }
end

-- Helper function to create particle images programmatically
function WeatherSystem.createParticleImage(type, options)
    options = options or {}

    local width = options.width or 4
    local height = options.height or 4
    local color = options.color or {r = 1, g = 1, b = 1, a = 1}

    -- Create canvas
    local canvas = love.graphics.newCanvas(width, height)
    love.graphics.setCanvas(canvas)
    love.graphics.clear(0, 0, 0, 0)

    -- Set color (handle both 0-1 and 0-255 ranges)
    local r = color.r > 1 and color.r/255 or color.r
    local g = color.g > 1 and color.g/255 or color.g
    local b = color.b > 1 and color.b/255 or color.b
    local a = color.a or 1
    love.graphics.setColor(r, g, b, a)

    -- Draw different shapes based on type
    if type == "rain" then
        -- Vertical line for rain
        love.graphics.rectangle("fill", 0, 0, width, height)
    elseif type == "snow" then
        -- Circle for snow
        love.graphics.circle("fill", width/2, height/2, width/2)
    elseif type == "splash" then
        -- Small circles for splash
        love.graphics.circle("fill", width/2, height/2, width/2)
    elseif type == "wind" then
        -- Horizontal line for wind
        love.graphics.rectangle("fill", 0, 0, width, height)
    elseif type == "dust" or type == "debris" then
        -- Small square for dust/debris
        love.graphics.rectangle("fill", width/4, height/4, width/2, height/2)
    elseif type == "lightning" then
        -- Zigzag for lightning
        love.graphics.line(0, height/2, width/3, 0, 2*width/3, height, width, height/2)
    elseif type == "fog" or type == "mist" or type == "cloud" then
        -- Soft circle for fog/mist/cloud
        love.graphics.circle("fill", width/2, height/2, width/2)
    elseif type == "fire" or type == "energy" or type == "plasma" then
        -- Triangle for fire/energy/plasma
        love.graphics.polygon("fill", 0, height, width/2, 0, width, height)
    else
        -- Default: simple square
        love.graphics.rectangle("fill", 0, 0, width, height)
    end

    -- Reset canvas
    love.graphics.setCanvas()

    return canvas
end

-- Apply environmental effects from weather
function WeatherSystem.applyEffects(environmentSettings)
    if not WeatherSystem.initialized or not environmentSettings then
        return
    end

    -- Apply environmental effects to the world
    if WeatherSystem.world then
        -- Apply visibility changes
        if environmentSettings.visibility then
            WeatherSystem.world.visibility = environmentSettings.visibility
        end

        -- Apply ambient light level
        if environmentSettings.ambientLight then
            WeatherSystem.world.lightLevel = environmentSettings.ambientLight
        end

        -- Apply temperature effects
        if environmentSettings.temperature then
            WeatherSystem.world.temperature = environmentSettings.temperature
        end

        -- Apply humidity effects
        if environmentSettings.humidity then
            WeatherSystem.world.humidity = environmentSettings.humidity
        end

        -- Apply wind effects
        if environmentSettings.wind then
            WeatherSystem.world.wind = environmentSettings.wind
        end
    end

    -- Log application of effects if in debug mode
    if WeatherSystem.debugMode then
        print("Applied weather environmental effects")
    end
end

-- Get all loaded weather types
function WeatherSystem.getWeatherTypes()
    local types = {}
    for name, _ in pairs(WeatherSystem.loadedWeatherTypes) do
        table.insert(types, name)
    end
    return types
end

-- Count loaded weather types
function WeatherSystem.countLoadedWeatherTypes()
    local count = 0
    for _ in pairs(WeatherSystem.loadedWeatherTypes) do
        count = count + 1
    end
    return count
end

-- Clean up and shut down weather system
function WeatherSystem.shutdown()
    if not WeatherSystem.initialized then return end

    -- Clean up current weather
    if WeatherSystem.currentWeather and WeatherSystem.currentWeather.cleanUp then
        WeatherSystem.currentWeather.cleanUp(WeatherSystem.world)
    end

    -- Reset state
    WeatherSystem.currentWeather = nil
    WeatherSystem.nextWeather = nil
    WeatherSystem.weatherQueue = {}
    WeatherSystem.initialized = false

    print("Weather system shutdown complete")
end

-- Handler for when the day phase changes
function WeatherSystem.onDayPhaseChange(newPhase, timeOfDay)
    if not WeatherSystem.initialized then return end

    if WeatherSystem.debugMode then
        print("Day phase changed to: " .. newPhase)
    end

    -- Current weather might have specific day/night behavior
    if WeatherSystem.currentWeather and WeatherSystem.currentWeather.onDayPhaseChange then
        WeatherSystem.currentWeather.onDayPhaseChange(newPhase, timeOfDay)
    end

    -- Day phase changes can potentially trigger weather changes
    local changeWeather = false
    local targetWeather = nil

    -- More likely to have precipitation at dawn/dusk (atmospheric changes)
    if newPhase == "dawn" then
        -- Higher chance for morning fog or dew
        if math.random() < 0.3 and WeatherSystem.weatherTypes["fog"] then
            targetWeather = "fog"
            changeWeather = true
        end
    elseif newPhase == "dusk" then
        -- Higher chance for evening storms or atmospheric changes
        if math.random() < 0.2 and WeatherSystem.weatherTypes["cloudy"] then
            targetWeather = "cloudy"
            changeWeather = true
        end
    elseif newPhase == "night" then
        -- Night-specific weathers
        if WeatherSystem.season == "winter" and WeatherSystem.weatherTypes["aurora"] then
            -- Higher chance for auroras in winter
            if math.random() < 0.4 then
                targetWeather = "aurora"
                changeWeather = true
            end
        end

        -- Lower ambient light level during night regardless of weather
        if WeatherSystem.currentWeather and WeatherSystem.currentWeather.visual then
            WeatherSystem.currentWeather.visual.ambientLightLevel = WeatherSystem.currentWeather.visual.ambientLightLevel or 1.0
            WeatherSystem.currentWeather.visual.ambientLightLevel = WeatherSystem.currentWeather.visual.ambientLightLevel * 0.5
        end
    elseif newPhase == "day" then
        -- Restore normal ambient light during day
        if WeatherSystem.currentWeather and WeatherSystem.currentWeather.visual then
            WeatherSystem.currentWeather.visual.ambientLightLevel = WeatherSystem.currentWeather.visual.ambientLightLevel or 0.5
            WeatherSystem.currentWeather.visual.ambientLightLevel = WeatherSystem.currentWeather.visual.ambientLightLevel * 2.0
        end
    end

    -- Apply weather change if needed
    if changeWeather and targetWeather then
        -- Use a gradual transition
        WeatherSystem.setWeather(targetWeather, false)
    end
end

return WeatherSystem
