local IronCrossbow = {
    id = "iron_crossbow",
    name = "Iron Crossbow",
    type = "weapon",
    category = "crossbow",
    
    -- Properties
    properties = {
        damage = 38,
        attackSpeed = 0.65,
        range = 8.5,
        durability = 210,
        maxDurability = 210,
        boltSpeed = 22,
        boltSpread = 0.1,
        reloadTime = 2.0,
        criticalChance = 0.22,
        criticalMultiplier = 1.7,
        armorPenetration = 0.55,
        weight = 1.7,
        momentum = 0,
        maxMomentum = 3,
        momentumPower = 1.2,
        ammoType = "iron_bolt",
        maxAmmo = 1,
        drawTime = 0.95
    },
    
    -- Appearance
    appearance = {
        sprite = "iron_crossbow",
        scale = 1.25,
        animations = {
            "idle",
            "draw",
            "shoot",
            "reload",
            "momentum"
        },
        variants = {
            "iron",
            "steel",
            "bronze",
            "copper"
        },
        blendMode = "normal",
        tint = {0.7, 0.7, 0.7},
        alpha = 1.0
    },
    
    -- Sound effects
    sounds = {
        draw = "crossbow_draw",
        shoot = "crossbow_shoot",
        reload = "crossbow_reload",
        empty = "crossbow_empty",
        momentum = "crossbow_momentum"
    },
    
    -- Effects
    effects = {
        bolt = {
            type = "projectile",
            speed = 22,
            lifetime = 0.75,
            effects = {
                damage = 38,
                pierce = true,
                bleed = 0.32,
                armorPenetration = 0.55
            }
        },
        critical = {
            type = "status",
            duration = 0.35,
            effects = {
                damage = 1.7
            }
        },
        momentum = {
            type = "status",
            duration = 0.45,
            effects = {
                damage = 1.2
            }
        }
    }
}

-- Initialize the weapon
function IronCrossbow.init(weapon, world)
    -- Copy all fields from IronCrossbow template to weapon instance
    for k, v in pairs(IronCrossbow) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        momentum = 0,
        lastMomentumTime = 0,
        currentAmmo = weapon.properties.maxAmmo,
        isReloading = false,
        reloadStartTime = 0,
        isDrawing = false,
        drawStartTime = 0
    }

    return weapon
end

-- Update the weapon
function IronCrossbow.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Decay momentum
        if world.time - weapon.properties.weaponState.lastMomentumTime > 1.5 then
            weapon.properties.weaponState.momentum = 0
        end
        
        -- Check if reload is complete
        if weapon.properties.weaponState.isReloading then
            if world.time - weapon.properties.weaponState.reloadStartTime >= weapon.properties.reloadTime then
                -- Finish reload
                weapon.properties.weaponState.currentAmmo = weapon.properties.maxAmmo
                weapon.properties.weaponState.isReloading = false
                
                -- Play reload sound
                if world.playSound then
                    world.playSound(weapon.sounds.reload)
                end
            end
        end
        
        -- Check if draw is complete
        if weapon.properties.weaponState.isDrawing then
            if world.time - weapon.properties.weaponState.drawStartTime >= weapon.properties.drawTime then
                -- Finish drawing
                weapon.properties.weaponState.isDrawing = false
                
                -- Play draw sound
                if world.playSound then
                    world.playSound(weapon.sounds.draw)
                end
            end
        end
    end
end

-- Handle attack (shoot)
function IronCrossbow.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Check if weapon is drawn
    if not weapon.properties.weaponState.isDrawing then
        -- Start drawing
        weapon.properties.weaponState.isDrawing = true
        weapon.properties.weaponState.drawStartTime = world.time
        return false
    end
    
    -- Check ammo
    if weapon.properties.weaponState.currentAmmo <= 0 then
        -- Play empty sound
        if world.playSound then
            world.playSound(weapon.sounds.empty)
        end
        return false
    end
    
    -- Calculate shot direction
    local angle = math.atan2(
        target.position.y - attacker.position.y,
        target.position.x - attacker.position.x
    )
    
    -- Add spread to angle
    angle = angle + (math.random() - 0.5) * weapon.properties.boltSpread
    
    -- Create shoot effect
    if world.createEffect then
        world.createEffect({
            type = "shoot",
            position = attacker.position,
            angle = angle,
            range = weapon.properties.range,
            duration = 0.35
        })
    end
    
    -- Create bolt projectile
    if world.createProjectile then
        world.createProjectile({
            type = "bolt",
            position = attacker.position,
            angle = angle,
            speed = weapon.properties.boltSpeed,
            lifetime = weapon.effects.bolt.lifetime,
            effects = weapon.effects.bolt.effects
        })
    end
    
    -- Check for critical hit
    if math.random() < weapon.properties.criticalChance then
        -- Create critical effect
        if world.createEffect then
            world.createEffect({
                type = "critical",
                position = target.position,
                duration = 0.35
            })
        end
        
        -- Apply critical effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.critical)
        end
    end
    
    -- Update momentum
    if world.time - weapon.properties.weaponState.lastMomentumTime <= 1.5 then
        weapon.properties.weaponState.momentum = math.min(weapon.properties.maxMomentum,
            weapon.properties.weaponState.momentum + 1)
    else
        weapon.properties.weaponState.momentum = 1
    end
    weapon.properties.weaponState.lastMomentumTime = world.time
    
    -- Apply momentum effect if active
    if weapon.properties.weaponState.momentum > 1 then
        if target.applyEffect then
            target.applyEffect(weapon.effects.momentum)
        end
        
        -- Play momentum sound
        if world.playSound then
            world.playSound(weapon.sounds.momentum)
        end
    end
    
    -- Play shoot sound
    if world.playSound then
        world.playSound(weapon.sounds.shoot)
    end
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce ammo
    weapon.properties.weaponState.currentAmmo = weapon.properties.weaponState.currentAmmo - 1
    
    -- Reset drawing state
    weapon.properties.weaponState.isDrawing = false
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack (piercing shot)
function IronCrossbow.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough momentum
    if weapon.properties.weaponState.momentum < 2 then
        return false
    end
    
    -- Check if weapon is drawn
    if not weapon.properties.weaponState.isDrawing then
        -- Start drawing
        weapon.properties.weaponState.isDrawing = true
        weapon.properties.weaponState.drawStartTime = world.time
        return false
    end
    
    -- Check ammo
    if weapon.properties.weaponState.currentAmmo <= 0 then
        return false
    end
    
    -- Create piercing shot effect
    if world.createEffect then
        world.createEffect({
            type = "piercing_shot",
            position = attacker.position,
            angle = math.atan2(
                target.position.y - attacker.position.y,
                target.position.x - attacker.position.x
            ),
            range = weapon.properties.range * 1.25,
            duration = 0.7
        })
    end
    
    -- Create powerful bolt projectile
    if world.createProjectile then
        world.createProjectile({
            type = "bolt",
            position = attacker.position,
            angle = math.atan2(
                target.position.y - attacker.position.y,
                target.position.x - attacker.position.x
            ),
            speed = weapon.properties.boltSpeed * 1.15,
            lifetime = weapon.effects.bolt.lifetime * 1.15,
            effects = {
                damage = weapon.effects.bolt.effects.damage * 1.4,
                pierce = true,
                bleed = weapon.effects.bolt.effects.bleed * 1.4,
                armorPenetration = weapon.effects.bolt.effects.armorPenetration * 1.4
            }
        })
    end
    
    -- Reset momentum
    weapon.properties.weaponState.momentum = 0
    
    -- Reduce ammo
    weapon.properties.weaponState.currentAmmo = weapon.properties.weaponState.currentAmmo - 1
    
    -- Reset drawing state
    weapon.properties.weaponState.isDrawing = false
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 2)
    
    return true
end

-- Handle reload
function IronCrossbow.reload(weapon, world)
    if weapon.properties.weaponState.isReloading then
        return false
    end
    
    -- Start reload
    weapon.properties.weaponState.isReloading = true
    weapon.properties.weaponState.reloadStartTime = world.time
    
    return true
end

return IronCrossbow 