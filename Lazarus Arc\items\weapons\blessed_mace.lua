-- items/weapons/blessed_mace.lua

local Weapon = {
    id = "blessed_mace",
    name = "Blessed Mace",
    description = "A mace imbued with holy energy, effective against undead and evil creatures.",
    type = "mace",
    category = "weapon",
    stackable = false,

    stats = {
        attack_power = 12.0,
        attack_speed = 0.7,
        holy_power = 8.0,
        durability = 120.0,
        repair_cost = 40,
        weight = 6.0,
    },

    damage_type = "physical",
    material = "silver",

    sprite = "res://Sprite/items/weapons/blessed_mace.png",
    size = { width = 64, height = 64 },

    effects = {
        armor_penetration = 0.1,
        stun_chance = 0.08,
        holy_damage = 5.0,
        undead_multiplier = 1.5
    },

    meta = {
        rarity = "uncommon"
    },

    take_damage = function(self, amount)
        self.stats.durability = math.max(0, self.stats.durability - amount)
        if self.stats.durability == 0 then
            print(self.name .. " is broken! Penalties applied.")
            -- Potential penalties could be applied here
        end
    end,

    repair = function(self)
        local repair_amount = 120 - self.stats.durability
        self.stats.durability = 120
        print(self.name .. " repaired by " .. repair_amount .. " points.")
    end,
    
    onUse = function(self, user, target)
        if target then
            -- Check for stun effect
            local roll = math.random()
            if roll <= self.effects.stun_chance then
                print(target.name .. " was stunned by the impact!")
                -- In a real implementation, this would apply a stun effect to the target
                if target.applyEffect then
                    target:applyEffect("stunned", 2.0) -- Stun for 2 seconds
                end
            end
            
            -- Apply holy damage
            if target.type == "undead" or target.type == "demon" then
                print("The blessed mace glows with holy light against " .. target.name .. "!")
                -- In a real implementation, this would apply extra damage
                local extraDamage = self.effects.holy_damage * self.effects.undead_multiplier
                if target.takeDamage then
                    target:takeDamage(extraDamage, "holy")
                end
            end
        end
        return true
    end,
    
    onEquip = function(self, user)
        if user and user.stats then
            -- Apply holy aura if applicable
            if user.applyEffect then
                user:applyEffect("holy_aura", -1) -- -1 for permanent until unequipped
                print(user.name .. " is surrounded by a faint holy aura.")
            end
        end
    end,
    
    onUnequip = function(self, user)
        if user and user.stats then
            -- Remove holy aura if applicable
            if user.removeEffect then
                user:removeEffect("holy_aura")
                print("The holy aura around " .. user.name .. " fades.")
            end
        end
    end
}

return Weapon
