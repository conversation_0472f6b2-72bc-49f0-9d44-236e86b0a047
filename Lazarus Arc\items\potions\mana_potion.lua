-- items/potions/mana_potion.lua

local Potion = {
    id = "mana_potion",
    name = "Mana Potion",
    description = "A blue potion that restores magical energy when consumed.",
    type = "potion",
    category = "consumable",
    stackable = true,
    maxStack = 10,

    stats = {
        mana_restore = 25.0,
        weight = 0.5,
    },

    sprite = "res://Sprite/items/potions/mana_potion.png",
    size = { width = 32, height = 32 },

    effects = {
        instant_restore = true,
        duration = 0
    },

    meta = {
        rarity = "common"
    },

    onUse = function(self, user)
        if user and user.stats and user.stats.mana then
            local maxMana = user.stats.maxMana or 100
            local currentMana = user.stats.mana
            local restoreAmount = self.stats.mana_restore
            
            -- Apply mana restoration
            user.stats.mana = math.min(maxMana, currentMana + restoreAmount)
            
            -- Notify the user
            print(user.name .. " used a Mana Potion and recovered " .. restoreAmount .. " mana!")
            return true
        end
        return false
    end
}

return Potion
