-- items/weapons/wooden_sword.lua

local Weapon = {
    id = "wooden_sword",
    name = "Wooden Sword",
    description = "A basic training sword made of wood. Not very effective in combat.",
    type = "sword",
    category = "weapon",
    stackable = false,

    stats = {
        attack_power = 5.0,
        attack_speed = 1.2,
        durability = 50.0,
        repair_cost = 10,
        weight = 3.0,
    },

    damage_type = "physical",
    material = "wood",

    sprite = "res://Sprite/items/weapons/wooden_sword.png",
    size = { width = 64, height = 64 },

    effects = {
        critical_chance = 0.02
    },

    meta = {
        rarity = "common"
    },

    take_damage = function(self, amount)
        self.stats.durability = math.max(0, self.stats.durability - amount)
        if self.stats.durability == 0 then
            print(self.name .. " is broken! Penalties applied.")
            -- Potential penalties could be applied here
        end
    end,

    repair = function(self)
        local repair_amount = 50 - self.stats.durability
        self.stats.durability = 50
        print(self.name .. " repaired by " .. repair_amount .. " points.")
    end
}

return Weapon
