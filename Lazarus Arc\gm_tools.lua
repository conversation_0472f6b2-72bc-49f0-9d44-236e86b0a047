-- modules/gm_tools.lua
-- A collection of functions intended for Game Master use via console or GM UI.
-- The game engine is responsible for verifying GM privileges before calling these.

local GMTools = {}

-- World reference would be passed by the engine when calling functions
-- local world = nil -- Assume world access is provided somehow

-- Helper function (conceptual) to get target entity/entities
local function findTarget(targetIdentifier, world)
    -- Logic to find entity/entities based on ID, name, player selection, area, etc.
    -- Returns a table of entity objects or nil
    print("GM Helper: Finding target(s) for identifier: ", targetIdentifier)
    if targetIdentifier == "@player" and world.player then return { world.player } end -- Example
    if world.entitySystem and world.entitySystem.entities[targetIdentifier] then
        return { world.entitySystem.entities[targetIdentifier] }
    end
    -- Add more complex targeting logic (area, type, name search)
    return nil
end

-- Helper function (conceptual) to get target chunk
local function findChunk(chunkX, chunkY, world)
    -- Logic to get the chunk object
    print("GM Helper: Finding chunk at: ", chunkX, chunkY)
    if world.chunkSystem and world.chunkSystem.getChunk then
        return world.chunkSystem:getChunk(chunkX, chunkY)
    end
    return nil
end


-- === Entity Manipulation ===

--- Spawns one or more entities at a location.
-- @param world The main world object provided by the engine.
-- @param entityId The string ID of the entity type to spawn.
-- @param x World X coordinate.
-- @param y World Y coordinate.
-- @param count (Optional) Number of entities to spawn (default 1).
-- @param state (Optional) Initial state or properties table.
function GMTools.SpawnEntity(world, entityId, x, y, count, state)
    count = count or 1
    state = state or {}
    state.gmSpawned = true -- Mark as GM spawned
    print("GM Command: Spawning " .. count .. "x " .. entityId .. " at " .. x .. "," .. y)
    if world.entitySystem and world.entitySystem.addEntity then
        for i = 1, count do
            world.entitySystem:addEntity(entityId, x, y, state)
        end
        return true, "Spawned " .. count .. "x " .. entityId
    else
        return false, "Entity system not available."
    end
end

--- Modifies a property of one or more targeted entities.
-- @param world The main world object.
-- @param targetIdentifier String ID, player name, "@player", "@selected", or area identifier.
-- @param propertyPath String representing nested property (e.g., "stats.health", "ai.state").
-- @param value The new value to set.
function GMTools.SetEntityProperty(world, targetIdentifier, propertyPath, value)
    local targets = findTarget(targetIdentifier, world)
    if not targets then return false, "Target not found." end

    print("GM Command: Setting property '" .. propertyPath .. "' to '" .. tostring(value) .. "' for target: " .. targetIdentifier)
    local successCount = 0
    for _, entity in ipairs(targets) do
        -- Need safe nested table access/modification logic here
        local parts = {}
        for part in string.gmatch(propertyPath, "[^.]+") do table.insert(parts, part) end

        local current = entity
        for i = 1, #parts - 1 do
            if current and current[parts[i]] then
                current = current[parts[i]]
            else
                current = nil; break
            end
        end

        if current and parts[#parts] then
             -- Convert value if needed (e.g., string "true" to boolean, string number to number)
             local actualValue = value
             if tonumber(value) then actualValue = tonumber(value)
             elseif value == "true" then actualValue = true
             elseif value == "false" then actualValue = false
             elseif value == "nil" then actualValue = nil end

             current[parts[#parts]] = actualValue
             successCount = successCount + 1
        end
    end
    return true, "Set property for " .. successCount .. " entities."
end

--- Instantly kills targeted entity/entities.
-- @param world The main world object.
-- @param targetIdentifier String ID, player name, "@player", "@selected", or area identifier.
function GMTools.KillEntity(world, targetIdentifier)
    local targets = findTarget(targetIdentifier, world)
    if not targets then return false, "Target not found." end
    print("GM Command: Killing target: " .. targetIdentifier)
    -- Call entity's die() method or set health to 0, handle removal
    for _, entity in ipairs(targets) do
        if entity.setHealth then entity.setHealth(0) -- Example
        elseif entity.stats then entity.stats.health = 0 end
        -- world.entitySystem:removeEntity(entity.id) -- Or flag for removal
    end
    return true, "Kill command sent to " .. #targets .. " entities."
end

--- Teleports targeted entity/entities.
-- @param world The main world object.
-- @param targetIdentifier String ID, player name, "@player", "@selected", or area identifier.
-- @param x Target world X coordinate.
-- @param y Target world Y coordinate.
function GMTools.TeleportEntity(world, targetIdentifier, x, y)
     local targets = findTarget(targetIdentifier, world)
    if not targets then return false, "Target not found." end
    print("GM Command: Teleporting target " .. targetIdentifier .. " to " .. x .. "," .. y)
    for _, entity in ipairs(targets) do
         entity.position.x = x
         entity.position.y = y
         -- May need velocity reset or further updates
    end
    return true, "Teleported " .. #targets .. " entities."
end

-- === Biome / Chunk Manipulation ===

--- Triggers a specific event within a biome chunk (e.g., a hazard, weather effect).
-- @param world The main world object.
-- @param chunkX X coordinate of the chunk.
-- @param chunkY Y coordinate of the chunk.
-- @param eventName String name of the event to trigger (defined within biome logic).
function GMTools.TriggerBiomeEvent(world, chunkX, chunkY, eventName)
    local chunk = findChunk(chunkX, chunkY, world)
    if not chunk then return false, "Chunk not found." end
    print("GM Command: Triggering event '" .. eventName .. "' in chunk " .. chunkX .. "," .. chunkY)
    -- Need logic for chunk/biome to handle events
    if chunk.triggerEvent then
        chunk:triggerEvent(eventName)
        return true, "Event triggered."
    else
        return false, "Chunk cannot handle events."
    end
end

--- Calls a specific, potentially GM-only, function defined within a biome's script.
-- @param world The main world object.
-- @param chunkX X coordinate of the chunk.
-- @param chunkY Y coordinate of the chunk.
-- @param functionName String name of the function to call (e.g., "gmActivateDefense").
-- @param ... Variable arguments to pass to the biome function.
function GMTools.ActivateBiomeFunction(world, chunkX, chunkY, functionName, ...)
    local chunk = findChunk(chunkX, chunkY, world)
    if not chunk then return false, "Chunk not found." end
    local biomeId = chunk.biomeId
    if not biomeId then return false, "Chunk has no biome ID." end

    -- Get the biome definition table (assuming engine provides access)
    local biomeDef = world.biomeRegistry:getBiome(biomeId) -- Conceptual access
    if not biomeDef then return false, "Biome definition '"..biomeId.."' not found." end

    if biomeDef[functionName] and type(biomeDef[functionName]) == "function" then
        print("GM Command: Calling function '" .. functionName .. "' in biome '" .. biomeId .. "' at chunk " .. chunkX .. "," .. chunkY)
        -- Call the function, passing necessary context (chunk, world, args)
        local success, msg = pcall(biomeDef[functionName], chunk, ..., world) -- Use pcall for safety
        if success then
            return true, "Biome function called: " .. (msg or "Success")
        else
            return false, "Error calling biome function: " .. tostring(msg)
        end
    else
        return false, "Function '" .. functionName .. "' not found or not callable in biome '" .. biomeId .. "'."
    end
end

-- === World State Manipulation ===

--- Sets the time of day.
-- @param world The main world object.
-- @param hour Hour value (e.g., 0-23).
function GMTools.SetTimeOfDay(world, hour)
    print("GM Command: Setting time of day to " .. hour)
    if world.timeSystem and world.timeSystem.setTime then
        world.timeSystem:setTime(hour)
        return true, "Time set."
    else
        return false, "Time system not available."
    end
end

--- Forces a specific weather state.
-- @param world The main world object.
-- @param weatherState String ID of the weather state.
-- @param duration (Optional) Duration in seconds.
function GMTools.SetWeather(world, weatherState, duration)
    print("GM Command: Setting weather to " .. weatherState .. (duration and (" for " .. duration .. "s") or ""))
    if world.weatherSystem and world.weatherSystem.forceWeather then
        world.weatherSystem:forceWeather(weatherState, duration)
        return true, "Weather set."
    else
        return false, "Weather system not available."
    end
end

-- === Player Manipulation (Example) ===

--- Gives an item to a player.
-- @param world The main world object.
-- @param playerName Name of the target player.
-- @param itemId String ID of the item.
-- @param count (Optional) Number of items (default 1).
function GMTools.GivePlayerItem(world, playerName, itemId, count)
    count = count or 1
    local player = world.playerManager:findPlayer(playerName) -- Conceptual player manager
    if not player then return false, "Player '" .. playerName .. "' not found." end
    print("GM Command: Giving " .. count .. "x " .. itemId .. " to player " .. playerName)
    if player.inventory and player.inventory.addItem then
        player.inventory:addItem(itemId, count)
        return true, "Item(s) given."
    else
        return false, "Player inventory system not available."
    end
end

-- ... Add many more functions as needed: Set stats, apply effects, manage quests, inspect objects, etc. ...

return GMTools