-- utils/renderer.lua
-- <PERSON>les drawing the game world, entities, and applying camera transformations.

local Renderer = {
    initialized = false
}

local DrawingSystem       = require("utils.drawing_system")
local StructureGraphics   = require("structures.structure_graphics")
local TileVisuals         = require("utils.tile_visuals")
local ColorUtils          = require("utils.color_utils")
local EntityVisuals       = require("utils.entity_visuals")
local LODSystem           = require("utils.lod_system")
local TileRenderer        = require("utils.tile_renderer")

-- Load a simple sky gradient shader
local skyShader = love.graphics.newShader([[
    extern vec2 resolution;
    vec4 effect(vec4 vcolor, Image tex, vec2 tc, vec2 sc){
        float t = sc.y / resolution.y;
        vec3 top = vec3(0.1,0.1,0.3);
        vec3 bottom = vec3(0.4,0.4,0.6);
        vec3 col = mix(top, bottom, t);
        return vec4(col,1);
    }
]])

-- Insert water shader for animated waves
local waterShader = love.graphics.newShader([[
extern float time;
extern float waveSpeed;
extern float waveStrength;
vec4 effect(vec4 vcolor, Image texture, vec2 uv, vec2 sc) {
    float t = time * waveSpeed;
    float freq = 10.0;
    float amp = waveStrength;
    float wave = sin((uv.x + t) * freq) * amp + cos((uv.y + t * 1.3) * freq) * amp;
    vec2 tcoord = uv + vec2(wave, wave);
    vec4 tex = Texel(texture, tcoord);
    // Tint water slightly blue
    tex.rgb = mix(tex.rgb, vec3(0.2,0.4,0.8), 0.5);
    return tex * vcolor;
}
]])

-- Simple full-screen blur shader
local blurShader = love.graphics.newShader([[
    extern vec2 resolution;
    extern float radius;
    vec4 effect(vec4 vcolor, Image texture, vec2 tc, vec2 sc) {
        vec2 px = 1.0 / resolution;
        vec4 sum = vec4(0.0);
        // 3x3 box blur
        for(int x=-1; x<=1; x++) {
            for(int y=-1; y<=1; y++) {
                sum += Texel(texture, tc + vec2(x,y) * px * radius);
            }
        }
        return sum / 9.0;
    }
]])

-- Draw a simple full-screen fog overlay
function Renderer:drawFog()
    love.graphics.push()
    love.graphics.origin()
    love.graphics.setColor(1,1,1,0.1)
    love.graphics.rectangle("fill", 0, 0, self.settings.screenWidth, self.settings.screenHeight)
    love.graphics.pop()
    love.graphics.setColor(1,1,1,1)
end

-- Initialize the renderer (if needed for setup)
function Renderer:init(config)
    print("Initializing Renderer...")
    self.settings    = config.settings
    self.colorUtils  = config.colorUtils
    self.tileWidth   = config.tileWidth  or 64
    self.tileHeight  = config.tileHeight or 32
    self.effectSystem = config.effectSystem  -- optional particle/effect system

    -- Check if performance mode is enabled
    local performanceMode = self.settings and self.settings.performanceMode

    -- Set performance-related flags based on performance mode
    self.enableShaders = not performanceMode
    self.enableBlur = not performanceMode and (config.enableBlur or false)
    self.simplifiedTiles = performanceMode
    self.simplifiedEntities = performanceMode
    self.disableShadows = performanceMode

    -- Only initialize shaders if enabled
    if self.enableShaders then
        self.skyShader = skyShader
        self.skyShader:send("resolution", {self.settings.screenWidth, self.settings.screenHeight})

        -- Configure water shader
        self.waterShader = waterShader
        self.waterShader:send("waveSpeed", config.waterWaveSpeed or 1.0)
        self.waterShader:send("waveStrength", config.waterWaveStrength or 0.02)

        -- Store parallax assets and blur setup if blur is enabled
        if self.enableBlur then
            self.blurShader = blurShader
            self.blurCanvas = love.graphics.newCanvas(self.settings.screenWidth, self.settings.screenHeight)
            self.blurEnabled = true
            self.blurShader:send("resolution", {self.settings.screenWidth, self.settings.screenHeight})
            self.blurShader:send("radius", config.blurRadius or 1.0)
        else
            self.blurEnabled = false
        end
    else
        self.blurEnabled = false
    end

    -- Initialize unified tile renderer
    TileRenderer:init({
        system = self.settings and self.settings.tileSystem or "complex",
        debug = {
            enabled = self.settings and self.settings.debugTiles or false,
            showSystemInfo = self.settings and self.settings.showTileSystemInfo or false,
            showPerformanceMetrics = self.settings and self.settings.showTilePerformance or false,
            allowRuntimeSwitching = true
        },
        lod = {
            worldSeed = self.settings and self.settings.worldSeed or os.time(),
            chunkSize = self.settings and self.settings.chunkSize or 16,
            tileWidth = self.tileWidth,
            tileHeight = self.tileHeight
        }
    })

    self.initialized = true

    -- profiling fields for drawWorldTiles
    self._profile = {tileTime = 0, count = 0, lastPrint = love.timer.getTime()}
    print("Renderer initialized with performance mode: " .. (performanceMode and "ON" or "OFF"))
    return self
end

-- Main function to draw the game world state
function Renderer:drawWorld(world, player, viewportManager)
    if not self.initialized or not world then return end

    -- Begin blur render if enabled
    if self.blurEnabled then
        love.graphics.setCanvas(self.blurCanvas)
    end

    -- Draw background based on time of day and weather
    love.graphics.clear()

    -- Get time of day and weather info from the world if available
    local timePhase = "day"
    local weatherType = "clear"

    if world and world.chunkSystem and world.chunkSystem.timeOfDay then
        timePhase = world.chunkSystem.timeOfDay.dayPhase or "day"
    end

    if world and world.weatherSystem and world.weatherSystem.getCurrentWeatherInfo then
        local weatherInfo = world.weatherSystem.getCurrentWeatherInfo()
        weatherType = weatherInfo.id or "clear"
    end

    -- Draw sky based on time of day and weather
    self:drawSkyForTimeAndWeather(timePhase, weatherType)

    -- Always draw parallax layers (mountains, moons, and clouds) - enhanced for visual appeal
    love.graphics.push()
    love.graphics.origin()
    local camX, camY = 0,0
    if viewportManager and viewportManager.getOffset then
        camX, camY = viewportManager.getOffset()
    end

    -- Horizon line is now drawn at the end of drawWorld for consistency

    -- Draw stars in the background - connected to time of day
    love.graphics.setColor(1, 1, 1, 0.7)

    -- Get time of day from the world for star positioning
    local gameHour = 12
    local gameMinute = 0
    local dayPhase = "day"

    if world and world.chunkSystem and world.chunkSystem.timeOfDay then
        gameHour = world.chunkSystem.timeOfDay.hour or 12
        gameMinute = world.chunkSystem.timeOfDay.minute or 0
        dayPhase = world.chunkSystem.timeOfDay.dayPhase or "day"
    end

    -- Calculate star visibility based on time of day
    local starVisibility = 0.7 -- Default
    if dayPhase == "day" then
        starVisibility = 0.1 -- Barely visible during day
    elseif dayPhase == "dawn" or dayPhase == "dusk" then
        starVisibility = 0.4 -- Partially visible during dawn/dusk
    elseif dayPhase == "night" then
        starVisibility = 0.9 -- Highly visible at night
    end

    -- Calculate star movement based on game time instead of real time
    -- This makes stars move with the game clock
    local gameTimeInMinutes = (gameHour * 60 + gameMinute) % (24 * 60)
    local starCyclePosition = gameTimeInMinutes / (24 * 60) -- 0 to 1 over a full day

    -- Draw stars with position based on game time
    for i = 1, 50 do
        -- Calculate star position based on game time
        local starAngle = (starCyclePosition * math.pi * 2) + (i * 0.2)
        local starDistance = self.settings.screenHeight * 0.3 * (0.5 + math.sin(i * 3.7) * 0.5)

        -- Calculate x position with some parallax effect
        local x = (self.settings.screenWidth * 0.5) +
                 math.cos(starAngle) * starDistance +
                 (camX * 0.1 * (1 + i % 3) * 0.1)

        -- Calculate y position - stars should be in upper part of screen
        local y = (self.settings.screenHeight * 0.3) +
                 math.sin(starAngle) * starDistance * 0.5

        -- Make sure stars wrap around screen edges
        x = x % self.settings.screenWidth
        if y < 0 then y = y + self.settings.screenHeight * 0.3 end

        -- Twinkle effect - varies with game time
        local twinkle = math.sin(gameTimeInMinutes * 0.01 + i * 0.3) * 0.5 + 0.5
        local size = (1 + twinkle) * (i % 3 + 1)

        -- Set star color with visibility based on time of day
        love.graphics.setColor(1, 1, 1, starVisibility * (0.5 + twinkle * 0.5))
        love.graphics.circle("fill", x, y, size)
    end

    -- Draw three moons with different colors and positions based on time of day
    -- Calculate moon positions based on game time
    local moonCyclePosition = gameTimeInMinutes / (24 * 60) -- 0 to 1 over a full day

    -- First moon (large, blue) - completes a full cycle in 24 game hours
    local moon1Angle = moonCyclePosition * math.pi * 2
    local moon1X = self.settings.screenWidth * (0.5 + 0.4 * math.cos(moon1Angle)) - camX * 0.05
    local moon1Y = self.settings.screenHeight * (0.25 - 0.2 * math.sin(moon1Angle))

    -- Second moon (medium, red) - completes a cycle in 18 game hours
    local moon2Angle = (moonCyclePosition * 1.33) * math.pi * 2
    local moon2X = self.settings.screenWidth * (0.5 + 0.3 * math.cos(moon2Angle)) - camX * 0.03
    local moon2Y = self.settings.screenHeight * (0.2 - 0.15 * math.sin(moon2Angle))

    -- Third moon (small, green) - completes a cycle in 12 game hours
    local moon3Angle = (moonCyclePosition * 2) * math.pi * 2
    local moon3X = self.settings.screenWidth * (0.5 + 0.25 * math.cos(moon3Angle)) - camX * 0.07
    local moon3Y = self.settings.screenHeight * (0.15 - 0.1 * math.sin(moon3Angle))

    -- Calculate moon visibility based on time of day
    local moonVisibility = {
        day = {0.3, 0.2, 0.1},    -- Barely visible during day
        dawn = {0.5, 0.4, 0.3},   -- Partially visible during dawn
        dusk = {0.6, 0.5, 0.4},   -- More visible during dusk
        night = {0.9, 0.8, 0.7}   -- Highly visible at night
    }

    local visibility = moonVisibility[dayPhase] or moonVisibility.day

    -- Draw first moon (large, blue) with stronger blue color
    love.graphics.setColor(0.5, 0.7, 1.0, visibility[1])
    love.graphics.circle("fill", moon1X, moon1Y, 40)
    -- Add a subtle glow effect
    love.graphics.setColor(0.5, 0.7, 1.0, visibility[1] * 0.3)
    love.graphics.circle("fill", moon1X, moon1Y, 45)

    -- Draw second moon (medium, red) with stronger red color
    love.graphics.setColor(1.0, 0.5, 0.5, visibility[2])
    love.graphics.circle("fill", moon2X, moon2Y, 25)
    -- Add a subtle glow effect
    love.graphics.setColor(1.0, 0.5, 0.5, visibility[2] * 0.3)
    love.graphics.circle("fill", moon2X, moon2Y, 30)

    -- Third moon (small, green) with stronger green color
    love.graphics.setColor(0.5, 1.0, 0.6, visibility[3])
    love.graphics.circle("fill", moon3X, moon3Y, 15)
    -- Add a subtle glow effect
    love.graphics.setColor(0.5, 1.0, 0.6, visibility[3] * 0.3)
    love.graphics.circle("fill", moon3X, moon3Y, 18)

    -- Draw procedural mountains as a parallax layer (multiple ranges for depth)
    -- Far mountains
    local baseY1 = self.settings.screenHeight * 0.4
    love.graphics.setColor(0.2, 0.2, 0.3)
    love.graphics.polygon("fill", {
        -100, baseY1,
        self.settings.screenWidth * 0.2, baseY1 - 40,
        self.settings.screenWidth * 0.4, baseY1 - 20,
        self.settings.screenWidth * 0.6, baseY1 - 50,
        self.settings.screenWidth * 0.8, baseY1 - 30,
        self.settings.screenWidth + 100, baseY1
    })

    -- Mid mountains
    local baseY2 = self.settings.screenHeight * 0.35
    love.graphics.setColor(0.3, 0.3, 0.4)
    love.graphics.polygon("fill", {
        -100, baseY2,
        self.settings.screenWidth * 0.25, baseY2 - 50,
        self.settings.screenWidth * 0.5, baseY2 - 20,
        self.settings.screenWidth * 0.75, baseY2 - 70,
        self.settings.screenWidth + 100, baseY2
    })

    -- Near mountains
    local baseY3 = self.settings.screenHeight * 0.3
    love.graphics.setColor(0.4, 0.4, 0.5)
    love.graphics.polygon("fill", {
        -100, baseY3,
        self.settings.screenWidth * 0.15, baseY3 - 30,
        self.settings.screenWidth * 0.3, baseY3 - 60,
        self.settings.screenWidth * 0.5, baseY3 - 40,
        self.settings.screenWidth * 0.7, baseY3 - 70,
        self.settings.screenWidth * 0.9, baseY3 - 50,
        self.settings.screenWidth + 100, baseY3
    })

    -- Draw procedural clouds as simple shapes with movement based on game time
    -- Get time of day from the world for cloud movement
    local gameHour = 12
    local gameMinute = 0
    local dayPhase = "day"

    if world and world.chunkSystem and world.chunkSystem.timeOfDay then
        gameHour = world.chunkSystem.timeOfDay.hour or 12
        gameMinute = world.chunkSystem.timeOfDay.minute or 0
        dayPhase = world.chunkSystem.timeOfDay.dayPhase or "day"
    end

    -- Calculate cloud visibility based on time of day and weather
    local cloudOpacity = 0.5 -- Default
    if weatherType == "clear" then
        if dayPhase == "day" then
            cloudOpacity = 0.3 -- Fewer clouds during clear day
        elseif dayPhase == "night" then
            cloudOpacity = 0.2 -- Even fewer clouds during clear night
        end
    elseif weatherType == "cloudy" or weatherType == "overcast" then
        cloudOpacity = 0.7 -- More clouds during cloudy weather
    elseif weatherType == "foggy" then
        cloudOpacity = 0.4 -- Medium clouds during foggy weather
    elseif weatherType == "light_rain" or weatherType == "heavy_rain" then
        cloudOpacity = 0.8 -- Heavy clouds during rainy weather
    end

    -- Set cloud color based on time of day
    local cloudColor = {1, 1, 1, cloudOpacity}
    if dayPhase == "dawn" then
        cloudColor = {1, 0.9, 0.8, cloudOpacity} -- Warm tint at dawn
    elseif dayPhase == "dusk" then
        cloudColor = {0.9, 0.8, 0.9, cloudOpacity} -- Purple tint at dusk
    elseif dayPhase == "night" then
        cloudColor = {0.5, 0.5, 0.7, cloudOpacity} -- Blue tint at night
    end

    love.graphics.setColor(cloudColor)

    -- Calculate cloud movement based on game time
    local gameTimeInMinutes = (gameHour * 60 + gameMinute) % (24 * 60)
    local cloudTime = gameTimeInMinutes / 5 -- Clouds make full screen cycle every 5 game hours
    local cloudY = self.settings.screenHeight * 0.1

    -- Cloud movement speed varies with weather
    local windSpeed = 1.0 -- Default
    if weatherType == "windy" then
        windSpeed = 2.0 -- Faster in windy weather
    elseif weatherType == "storm" or weatherType == "heavy_rain" then
        windSpeed = 1.5 -- Faster in stormy weather
    elseif weatherType == "calm" or weatherType == "foggy" then
        windSpeed = 0.5 -- Slower in calm weather
    end

    -- Multiple cloud layers with different speeds and improved visuals
    -- Draw more realistic clouds using multiple circles for each cloud

    -- First cloud layer (slow, highest)
    local cloudY1 = self.settings.screenHeight * 0.08
    local cloud1X1 = (-camX * 0.1 + cloudTime * 15 * windSpeed) % (self.settings.screenWidth + 200) - 100
    local cloud1X2 = (-camX * 0.1 + cloudTime * 15 * windSpeed) % (self.settings.screenWidth + 200) - 60

    -- Draw first cloud as a group of circles
    love.graphics.setColor(cloudColor[1], cloudColor[2], cloudColor[3], cloudColor[4] * 0.9)
    love.graphics.circle("fill", cloud1X1, cloudY1, 30)
    love.graphics.circle("fill", cloud1X1 + 20, cloudY1 - 5, 25)
    love.graphics.circle("fill", cloud1X1 + 40, cloudY1, 28)
    love.graphics.circle("fill", cloud1X1 + 25, cloudY1 + 8, 22)

    -- Draw second cloud in first layer
    love.graphics.circle("fill", cloud1X2, cloudY1 + 5, 25)
    love.graphics.circle("fill", cloud1X2 + 15, cloudY1 - 8, 20)
    love.graphics.circle("fill", cloud1X2 + 35, cloudY1, 28)
    love.graphics.circle("fill", cloud1X2 + 20, cloudY1 + 10, 22)

    -- Second cloud layer (medium, middle)
    local cloudY2 = self.settings.screenHeight * 0.12
    local cloud2X1 = (-camX * 0.15 + cloudTime * 25 * windSpeed) % (self.settings.screenWidth + 300) - 150
    local cloud2X2 = (-camX * 0.15 + cloudTime * 25 * windSpeed) % (self.settings.screenWidth + 300) - 50

    -- Draw first cloud in second layer
    love.graphics.setColor(cloudColor[1], cloudColor[2], cloudColor[3], cloudColor[4] * 0.8)
    love.graphics.circle("fill", cloud2X1, cloudY2, 35)
    love.graphics.circle("fill", cloud2X1 + 25, cloudY2 - 10, 30)
    love.graphics.circle("fill", cloud2X1 + 50, cloudY2, 32)
    love.graphics.circle("fill", cloud2X1 + 30, cloudY2 + 12, 28)

    -- Draw second cloud in second layer
    love.graphics.circle("fill", cloud2X2, cloudY2 + 8, 32)
    love.graphics.circle("fill", cloud2X2 + 20, cloudY2 - 5, 28)
    love.graphics.circle("fill", cloud2X2 + 45, cloudY2 + 3, 30)
    love.graphics.circle("fill", cloud2X2 + 25, cloudY2 + 15, 25)

    -- Third cloud layer (fast, lowest)
    local cloudY3 = self.settings.screenHeight * 0.16
    local cloud3X1 = (-camX * 0.2 + cloudTime * 35 * windSpeed) % (self.settings.screenWidth + 400) - 200
    local cloud3X2 = (-camX * 0.2 + cloudTime * 35 * windSpeed) % (self.settings.screenWidth + 400) - 20

    -- Draw first cloud in third layer
    love.graphics.setColor(cloudColor[1], cloudColor[2], cloudColor[3], cloudColor[4] * 0.7)
    love.graphics.circle("fill", cloud3X1, cloudY3, 40)
    love.graphics.circle("fill", cloud3X1 + 30, cloudY3 - 12, 35)
    love.graphics.circle("fill", cloud3X1 + 60, cloudY3, 38)
    love.graphics.circle("fill", cloud3X1 + 35, cloudY3 + 15, 32)

    -- Draw second cloud in third layer
    love.graphics.circle("fill", cloud3X2, cloudY3 + 10, 38)
    love.graphics.circle("fill", cloud3X2 + 25, cloudY3 - 8, 32)
    love.graphics.circle("fill", cloud3X2 + 55, cloudY3 + 5, 36)
    love.graphics.circle("fill", cloud3X2 + 30, cloudY3 + 18, 30)

    -- Add extra clouds during cloudy or rainy weather
    if weatherType == "cloudy" or weatherType == "overcast" or
       weatherType == "light_rain" or weatherType == "heavy_rain" then

        -- Fourth cloud layer (extra clouds for cloudy/rainy weather)
        local cloudY4 = self.settings.screenHeight * 0.2
        local cloud4X1 = (-camX * 0.25 + cloudTime * 20 * windSpeed) % (self.settings.screenWidth + 500) - 250
        local cloud4X2 = (-camX * 0.25 + cloudTime * 20 * windSpeed) % (self.settings.screenWidth + 500) + 50

        -- Draw first cloud in fourth layer
        love.graphics.setColor(cloudColor[1], cloudColor[2], cloudColor[3], cloudColor[4] * 0.9)
        love.graphics.circle("fill", cloud4X1, cloudY4, 50)
        love.graphics.circle("fill", cloud4X1 + 35, cloudY4 - 15, 45)
        love.graphics.circle("fill", cloud4X1 + 70, cloudY4, 48)
        love.graphics.circle("fill", cloud4X1 + 40, cloudY4 + 20, 42)
        love.graphics.circle("fill", cloud4X1 + 20, cloudY4 + 10, 40)

        -- Draw second cloud in fourth layer
        love.graphics.circle("fill", cloud4X2, cloudY4 + 15, 48)
        love.graphics.circle("fill", cloud4X2 + 30, cloudY4 - 10, 42)
        love.graphics.circle("fill", cloud4X2 + 65, cloudY4 + 5, 46)
        love.graphics.circle("fill", cloud4X2 + 35, cloudY4 + 25, 40)
        love.graphics.circle("fill", cloud4X2 + 15, cloudY4 + 5, 38)
    end

    love.graphics.setColor(1,1,1,1)
    love.graphics.pop()

    -- Apply camera transform
    love.graphics.push()
    self:applyCameraTransform(player, viewportManager)

    -- Draw world in layers
    self:drawWorldTiles(world, player)

    -- Overlay fog at tile layer - only if shaders enabled
    if self.enableShaders then
        self:drawFog()
    end

    -- Draw structures with simplified rendering if needed
    self:drawStructures(world)

    -- Draw weather/particle effects only if enabled
    if not self.simplifiedTiles and self.effectSystem and type(self.effectSystem.draw) == "function" then
        self.effectSystem:draw()
    end

    -- Draw entities
    self:drawEntities(world, player)

    love.graphics.pop()

    -- End blur render pass
    if self.blurEnabled then
        love.graphics.setCanvas()
        love.graphics.setShader(self.blurShader)
        love.graphics.draw(self.blurCanvas, 0, 0)
        love.graphics.setShader()
    end

    -- Apply ambient tint (dawn/dusk/night) - only if shaders enabled
    if self.enableShaders then
        self:applyAmbientTint(world)
    end

    -- Draw horizon line on top of everything for both menu and gameplay states
    self:drawHorizonLine()
end

-- Switch to the next tile rendering system (for debugging)
function Renderer:switchTileSystem()
    if TileRenderer and TileRenderer.switchSystem then
        return TileRenderer:switchSystem()
    end
    return false
end

-- Get current tile system information
function Renderer:getTileSystemInfo()
    if TileRenderer and TileRenderer.getSystemInfo then
        return TileRenderer:getSystemInfo()
    end
    return nil
end

-- Set tile system debug mode
function Renderer:setTileDebugMode(enabled)
    if TileRenderer and TileRenderer.setDebugMode then
        TileRenderer:setDebugMode(enabled)
    end
end

-- Apply camera transformations (including isometric projection)
function Renderer:applyCameraTransform(player, viewportManager)
    -- Default camera position is center of screen
    local cameraX = self.settings.screenWidth / 2
    local cameraY = self.settings.screenHeight / 2

    -- Check if we're in menu view mode
    local isMenuView = viewportManager and viewportManager.menuView and viewportManager.menuView.active

    -- Get current state to determine camera behavior
    local currentState = "gameplay"
    if Engine and Engine.stateManager and Engine.stateManager.getCurrentState then
        currentState = Engine.stateManager:getCurrentState()
    end

    -- Force menu-like camera behavior in gameplay for consistency
    local useMenuLikeBehavior = (currentState == "gameplay")

    -- Handle menu view camera
    if isMenuView then
        -- Use the first player as camera (which should be the menu camera)
        if viewportManager.players and #viewportManager.players > 0 then
            local vp = viewportManager.players[1].viewport

            -- Apply zoom for menu view
            local menuZoom = viewportManager.menuView.zoom or 1.2
            love.graphics.scale(menuZoom, menuZoom)

            -- Apply camera transform based on projection mode
            if self.colorUtils and self.colorUtils.isometricDebug then
                -- Convert world coordinates to isometric for camera positioning
                local tileWidth = 32 -- TODO: Get from config/tile system
                local tileHeight = 16 -- TODO: Get from config/tile system
                local isoX = (vp.x - vp.y) * (tileWidth / 2)
                local isoY = (vp.x + vp.y) * (tileHeight / 4)
                love.graphics.translate(cameraX / menuZoom - isoX, cameraY / menuZoom - isoY)
            else
                -- Standard top-down camera transform
                love.graphics.translate(cameraX / menuZoom - vp.x, cameraY / menuZoom - vp.y)
            end

            -- Apply slight rotation for visual interest in menu view
            if viewportManager.menuView.rotation then
                love.graphics.rotate(viewportManager.menuView.rotation)
            end
        else
            -- Fallback if no menu camera is set up
            love.graphics.translate(cameraX, cameraY)
        end
    -- Handle gameplay camera with player
    elseif player and player.position then
        -- Use the viewport manager for camera positioning if available
        if viewportManager and viewportManager.players and #viewportManager.players > 0 then
            local vp = viewportManager.players[1].viewport -- Assuming single player viewport for now

            -- Apply zoom level - use the same zoom as menu for consistency
            local zoomLevel = viewportManager.gameplayZoom or viewportManager.menuView.zoom or 1.2
            if zoomLevel ~= 1.0 then
                love.graphics.scale(zoomLevel, zoomLevel)
            end

            -- EMERGENCY DEBUG: Print viewport position
            print("RENDERER: Viewport position: x=" .. tostring(vp.x) .. ", y=" .. tostring(vp.y))
            print("RENDERER: Player actual position: x=" .. tostring(player.position.x) .. ", y=" .. tostring(player.position.y))

            -- FIXED: Use player's actual position instead of viewport position
            -- This ensures the camera follows the player immediately without lag

            if self.colorUtils and self.colorUtils.isometricDebug then
                -- Convert world coordinates to isometric for camera positioning
                local tileWidth = 32 -- TODO: Get from config/tile system
                local tileHeight = 16 -- TODO: Get from config/tile system

                -- Use player's actual position instead of viewport position
                local isoX = (player.position.x - player.position.y) * (tileWidth / 2)
                local isoY = (player.position.x + player.position.y) * (tileHeight / 4)

                -- EMERGENCY DEBUG: Print isometric coordinates
                print("RENDERER: Isometric coordinates: x=" .. tostring(isoX) .. ", y=" .. tostring(isoY))

                love.graphics.translate(cameraX / zoomLevel - isoX, cameraY / zoomLevel - isoY)

                -- Apply slight rotation for visual interest in gameplay view (same as menu)
                if viewportManager.menuView and viewportManager.menuView.rotation then
                    love.graphics.rotate(viewportManager.menuView.rotation)
                end
            else
                -- Standard top-down camera transform
                -- Use player's actual position instead of viewport position
                love.graphics.translate(cameraX / zoomLevel - player.position.x, cameraY / zoomLevel - player.position.y)

                -- EMERGENCY DEBUG: Print camera translation
                print("RENDERER: Camera translation: x=" .. tostring(cameraX / zoomLevel - player.position.x) ..
                      ", y=" .. tostring(cameraY / zoomLevel - player.position.y))
            end
        else
            -- Fallback to direct player position if viewport manager not available
            if self.colorUtils and self.colorUtils.isometricDebug then
                local tileWidth = 32; local tileHeight = 16; -- TODO: Refactor
                local isoX = (player.position.x - player.position.y) * (tileWidth / 2)
                local isoY = (player.position.x + player.position.y) * (tileHeight / 4)
                love.graphics.translate(cameraX - isoX, cameraY - isoY)
            else
                love.graphics.translate(cameraX - player.position.x, cameraY - player.position.y)
            end
        end
    else
        -- Default translation if no player (e.g., for menu background)
        love.graphics.translate(cameraX, cameraY)
    end
end

-- Draw world tiles using unified tile renderer
function Renderer:drawWorldTiles(world, player)
    local profStart = love.timer.getTime()

    -- Use the unified tile renderer system
    TileRenderer:drawWorldTiles(world, player, self)

    -- profiling end
    local profDelta = love.timer.getTime() - profStart
    self._profile.tileTime = self._profile.tileTime + profDelta
    self._profile.count = self._profile.count + 1
    local now = love.timer.getTime()
    if now - self._profile.lastPrint >= 1 then
        print(string.format("Avg drawWorldTiles time: %.4f ms", (self._profile.tileTime / self._profile.count) * 1000))
        self._profile.tileTime = 0
        self._profile.count = 0
        self._profile.lastPrint = now
    end
end

-- Draw all structures procedurally using primitives
function Renderer:drawStructures(world)
    local cs = world.chunkSystem
    if not cs or not cs.activeChunks then return end
    -- Collect and depth-sort structures
    local queue = {}
    for _, chunk in ipairs(cs.activeChunks) do
        for _, s in ipairs(cs.structureSystem and cs.structureSystem:getStructuresForChunk(chunk.x, chunk.y) or {}) do
            local wx, wy = chunk.x * cs.chunkSize + s.x, chunk.y * cs.chunkSize + s.y
            table.insert(queue, {
                depth = wx + wy,
                isoX  = (wx - wy) * (self.tileWidth/2),
                isoY  = (wx + wy) * (self.tileHeight/2),
                struct = s
            })
        end
    end
    table.sort(queue, function(a, b) return a.depth < b.depth end)

    -- Get screen dimensions
    local screenHeight = love.graphics.getHeight()

    -- Calculate horizon line position (40% of screen height)
    local horizonY = screenHeight * 0.4

    for _, e in ipairs(queue) do
        -- Check if this structure is above the horizon line
        -- Get screen coordinates for the structure
        local screenX, screenY = love.graphics.transformPoint(e.isoX, e.isoY)

        -- Skip drawing if the structure is above the horizon line
        if screenY < horizonY then
            goto continue
        end

        -- Draw drop-shadow under structure based on its base size
        local def = StructureGraphics.structures[e.struct.type] and StructureGraphics.structures[e.struct.type].base
        if def then
            local bw = (def.width or 64) * (e.struct.scale or 1)
            local radX = bw / 2
            local radY = bw / 4
            -- Shadow offset towards top-right (light source from top-left)
            local offX = radX * 0.3
            local offY = radY * 0.5
            DrawingSystem.drawEllipse(e.isoX + offX, e.isoY + offY, radX, radY, { color = {0,0,0,0.3}, stroke = false })
        end
        -- Apply base tint based on time-of-day
        local phase = world.chunkSystem and world.chunkSystem.timeOfDay and world.chunkSystem.timeOfDay.dayPhase or "day"
        local tint = {1,1,1,1}
        if phase == "dawn" then
            tint = {1, 0.9, 0.8, 1}
        elseif phase == "dusk" then
            tint = {0.9, 0.9, 1, 1}
        elseif phase == "night" then
            tint = {0.6, 0.6, 0.8, 1}
        end
        love.graphics.setColor(tint)
        -- TODO: add side shading in StructureGraphics.drawStructure for faces opposite sun
        -- Draw the structure
        StructureGraphics.drawStructure(e.struct, e.isoX, e.isoY, e.struct.rotation or 0, e.struct.scale or 1)
        -- Reset tint
        love.graphics.setColor(1,1,1,1)

        ::continue::
    end
end

-- Calculate ambient tint RGBA based on time-of-day
function Renderer:calculateAmbientTint(world)
    local phase = world.chunkSystem and world.chunkSystem.timeOfDay and world.chunkSystem.timeOfDay.dayPhase or "day"
    if phase == "dawn" then
        return {1, 0.8, 0.6, 0.2}    -- warm morning glow
    elseif phase == "dusk" then
        return {0.8, 0.6, 1, 0.2}    -- cool evening glow
    elseif phase == "night" then
        return {0.2, 0.2, 0.5, 0.4}  -- dark blue overlay
    else
        return {0, 0, 0, 0}          -- no tint during day
    end
end

-- Draw a full-screen ambient tint overlay
function Renderer:applyAmbientTint(world)
    local tint = self:calculateAmbientTint(world)
    if tint[4] and tint[4] > 0 then
        love.graphics.push()
        love.graphics.origin()
        love.graphics.setColor(tint)
        love.graphics.rectangle("fill", 0, 0, self.settings.screenWidth, self.settings.screenHeight)
        love.graphics.pop()
        love.graphics.setColor(1,1,1,1)
    end
end

--[[
    Character Display System
    -----------------------

    This function is responsible for drawing all entities in the world, including
    the player character. It's a critical part of the character movement system because:

    1. It determines when and how the player character is displayed
    2. It handles the noCharacter flag to prevent the player from appearing in the main menu
    3. It applies culling to only draw entities within the visible range
    4. It handles depth sorting to ensure entities are drawn in the correct order

    The noCharacter flag is particularly important for the main menu. When this flag
    is set on the player entity, the player character is not drawn, which prevents
    the character from appearing in the main menu before being selected.

    This function works in conjunction with the character movement system to ensure
    that the player character is displayed at the correct position and with the
    correct orientation based on the player's movement.
]]

-- Draw all entities in the world with optimizations
function Renderer:drawEntities(world, player)
    if not world.entitySystem then return end

    -- Check if player has the noCharacter flag (used in main menu)
    -- This is a critical check that prevents the player character from appearing
    -- in the main menu before being selected from the character selection screen
    if player and player.noCharacter then
        -- Skip drawing the player character in the main menu
        player = nil
    end

    -- Get player position for culling
    local playerX, playerY = 0, 0
    if player and player.position then
        playerX = player.position.x
        playerY = player.position.y
    end

    -- Calculate visible area for culling (in world coordinates)
    local visibleRange = self.simplifiedEntities and 15 or 20 -- Reduce range in simplified mode
    local minX = playerX - visibleRange
    local maxX = playerX + visibleRange
    local minY = playerY - visibleRange
    local maxY = playerY + visibleRange

    -- Get maximum number of entities to render based on performance settings
    local maxEntities = self.simplifiedEntities and 30 or 100

    local entities = world.entitySystem:getAll()
    local entitiesToDraw = {}
    local entitiesCount = 0

    -- Separate player from other entities for draw order and apply culling
    for id, entity in pairs(entities) do
        if entity and entity.position and entity ~= player then
            -- Skip entities outside visible range
            if entity.position.x < minX or entity.position.x > maxX or
               entity.position.y < minY or entity.position.y > maxY then
                goto continue
            end

            -- Limit number of entities drawn
            entitiesCount = entitiesCount + 1
            if entitiesCount > maxEntities then
                break
            end

            table.insert(entitiesToDraw, entity)

            ::continue::
        end
    end

    -- Depth-sort entities by world-coordinates sum
    table.sort(entitiesToDraw, function(a, b)
        return (a.position.x + a.position.y) < (b.position.x + b.position.y)
    end)

    -- Get screen dimensions
    local screenHeight = love.graphics.getHeight()

    -- Calculate horizon line position (40% of screen height)
    local horizonY = screenHeight * 0.4

    -- Draw non-player entities
    for _, entity in ipairs(entitiesToDraw) do
        -- Compute isometric coordinates
        local isoX, isoY
        if self.colorUtils and self.colorUtils.isometricDebug then
            isoX, isoY = self.colorUtils.toIsometric(entity.position.x, entity.position.y)
        else
            isoX, isoY = entity.position.x, entity.position.y
        end

        -- Check if this entity is above the horizon line
        local screenX, screenY = love.graphics.transformPoint(isoX, isoY)

        -- Skip drawing if the entity is above the horizon line
        if screenY >= horizonY then
            self:drawSingleEntity(entity)
        end
    end

    -- Draw player last (on top) if player exists and doesn't have noCharacter flag
    if player and player.position then
        -- Compute isometric coordinates for player
        local isoX, isoY
        if self.colorUtils and self.colorUtils.isometricDebug then
            isoX, isoY = self.colorUtils.toIsometric(player.position.x, player.position.y)
        else
            isoX, isoY = player.position.x, player.position.y
        end

        -- Check if player is above the horizon line
        local screenX, screenY = love.graphics.transformPoint(isoX, isoY)

        -- Only draw player if below the horizon line
        if screenY >= horizonY then
            self:drawSingleEntity(player)
            -- TODO: Move player-specific overlays (name, debug info, direction indicator) here
        end
    end
end

-- Draw a single entity with optimizations
function Renderer:drawSingleEntity(entity)
    -- Compute isometric or top-down screen coordinates
    local x, y
    if self.colorUtils and self.colorUtils.isometricDebug then
        x, y = self.colorUtils.toIsometric(entity.position.x, entity.position.y)
    else
        x, y = entity.position.x, entity.position.y
    end

    -- Use simplified rendering in performance mode
    if self.simplifiedEntities then
        -- Get entity color
        local color = {1, 1, 1, 1} -- Default white
        if self.colorUtils and self.colorUtils.getEntityColor then
            color = self.colorUtils.getEntityColor(entity.type or "default")
        end

        -- Draw simple shape instead of detailed entity
        love.graphics.setColor(color)
        local size = entity.size or (entity.entityType == "player" and 8 or 6)

        if entity.entityType == "player" then
            -- Draw player as a triangle instead of a circle
            local triangleSize = size * 1.5 -- Make triangle slightly larger for visibility

            -- Calculate triangle vertices based on player direction
            local direction = entity.direction or "down"
            local vx1, vy1, vx2, vy2, vx3, vy3

            -- Default direction (down) if not specified
            if direction == "down" then
                -- Triangle pointing down
                vx1, vy1 = x, y + triangleSize
                vx2, vy2 = x - triangleSize, y - triangleSize
                vx3, vy3 = x + triangleSize, y - triangleSize
            elseif direction == "up" then
                -- Triangle pointing up
                vx1, vy1 = x, y - triangleSize
                vx2, vy2 = x - triangleSize, y + triangleSize
                vx3, vy3 = x + triangleSize, y + triangleSize
            elseif direction == "left" then
                -- Triangle pointing left
                vx1, vy1 = x - triangleSize, y
                vx2, vy2 = x + triangleSize, y - triangleSize
                vx3, vy3 = x + triangleSize, y + triangleSize
            elseif direction == "right" then
                -- Triangle pointing right
                vx1, vy1 = x + triangleSize, y
                vx2, vy2 = x - triangleSize, y - triangleSize
                vx3, vy3 = x - triangleSize, y + triangleSize
            else
                -- Default to down if direction is unknown
                vx1, vy1 = x, y + triangleSize
                vx2, vy2 = x - triangleSize, y - triangleSize
                vx3, vy3 = x + triangleSize, y - triangleSize
            end

            -- Draw the triangle
            love.graphics.polygon("fill", vx1, vy1, vx2, vy2, vx3, vy3)

            -- Draw health and mana bars above the player's head
            local barWidth = 20
            local barHeight = 3
            local barSpacing = 2
            local barY = y - triangleSize - barHeight - barSpacing

            -- Get player stats
            local healthPercent = 1.0
            local manaPercent = 1.0

            -- If player has character with stats, use them
            if entity.character then
                if entity.character.stats then
                    -- Calculate health percentage
                    if entity.character.stats.health and entity.character.stats.maxHealth then
                        healthPercent = math.max(0, math.min(1, entity.character.stats.health / entity.character.stats.maxHealth))
                    end

                    -- Calculate mana percentage
                    if entity.character.stats.mana and entity.character.stats.maxMana then
                        manaPercent = math.max(0, math.min(1, entity.character.stats.mana / entity.character.stats.maxMana))
                    end
                end
            end

            -- Draw health bar background
            love.graphics.setColor(0.2, 0.2, 0.2, 0.8)
            love.graphics.rectangle("fill", x - barWidth/2, barY, barWidth, barHeight)

            -- Draw health bar fill (red)
            love.graphics.setColor(0.8, 0.2, 0.2, 1.0)
            love.graphics.rectangle("fill", x - barWidth/2, barY, barWidth * healthPercent, barHeight)

            -- Draw mana bar background
            love.graphics.setColor(0.2, 0.2, 0.2, 0.8)
            love.graphics.rectangle("fill", x - barWidth/2, barY + barHeight + barSpacing, barWidth, barHeight)

            -- Draw mana bar fill (blue)
            love.graphics.setColor(0.2, 0.2, 0.8, 1.0)
            love.graphics.rectangle("fill", x - barWidth/2, barY + barHeight + barSpacing, barWidth * manaPercent, barHeight)

            -- Reset color after drawing bars
            love.graphics.setColor(color)
        else
            -- Draw other entities as squares
            love.graphics.rectangle("fill", x - size/2, y - size/2, size, size)
        end
    else
        -- Use full procedural engine visuals for entity
        EntityVisuals.draw(entity, x, y)
    end

    -- Reset color
    love.graphics.setColor(1, 1, 1, 1)
end

-- Draw sky based on time of day and weather
function Renderer:drawSkyForTimeAndWeather(timePhase, weatherType)
    -- Get more precise time information if available
    local gameHour = 12
    local gameMinute = 0

    if _G.Engine and Engine.currentWorld and Engine.currentWorld.chunkSystem and Engine.currentWorld.chunkSystem.timeOfDay then
        gameHour = Engine.currentWorld.chunkSystem.timeOfDay.hour or 12
        gameMinute = Engine.currentWorld.chunkSystem.timeOfDay.minute or 0
    end

    -- Calculate time progress for smooth transitions between phases
    local gameTimeInMinutes = (gameHour * 60 + gameMinute) % (24 * 60)
    local dayProgress = gameTimeInMinutes / (24 * 60) -- 0 to 1 over a full day

    -- Sky colors for different times of day and weather conditions
    local skyColors = {
        day = {
            clear = {0.4, 0.7, 1.0},
            cloudy = {0.6, 0.7, 0.8},
            light_rain = {0.5, 0.6, 0.7},
            heavy_rain = {0.3, 0.4, 0.5},
            foggy = {0.7, 0.7, 0.7},
            sunny = {0.5, 0.8, 1.0},
            overcast = {0.4, 0.4, 0.5},
            default = {0.4, 0.7, 1.0}
        },
        dawn = {
            clear = {0.9, 0.6, 0.4},
            cloudy = {0.8, 0.6, 0.5},
            light_rain = {0.7, 0.5, 0.4},
            heavy_rain = {0.5, 0.4, 0.3},
            foggy = {0.8, 0.7, 0.6},
            sunny = {1.0, 0.7, 0.4},
            overcast = {0.6, 0.5, 0.4},
            default = {0.9, 0.6, 0.4}
        },
        dusk = {
            clear = {0.8, 0.5, 0.6},
            cloudy = {0.7, 0.5, 0.6},
            light_rain = {0.6, 0.4, 0.5},
            heavy_rain = {0.4, 0.3, 0.4},
            foggy = {0.7, 0.6, 0.6},
            sunny = {0.9, 0.6, 0.5},
            overcast = {0.5, 0.4, 0.5},
            default = {0.8, 0.5, 0.6}
        },
        night = {
            clear = {0.1, 0.1, 0.3},
            cloudy = {0.1, 0.1, 0.2},
            light_rain = {0.1, 0.1, 0.15},
            heavy_rain = {0.05, 0.05, 0.1},
            foggy = {0.15, 0.15, 0.2},
            sunny = {0.15, 0.15, 0.3},
            overcast = {0.05, 0.05, 0.1},
            default = {0.1, 0.1, 0.3}
        },
        default = {
            default = {0.4, 0.7, 1.0}
        }
    }

    -- Get the appropriate sky color
    local skyColor = skyColors[timePhase] and
                    (skyColors[timePhase][weatherType] or skyColors[timePhase].default) or
                    skyColors.default.default

    -- Calculate transition between time phases for smoother day/night cycle
    local nextPhase = "day"
    local transitionProgress = 0

    -- Determine next phase and transition progress based on hour
    if timePhase == "day" and gameHour >= 16 and gameHour < 18 then
        -- Transitioning from day to dusk
        nextPhase = "dusk"
        transitionProgress = (gameHour - 16) / 2 + (gameMinute / 120)
    elseif timePhase == "dusk" and gameHour >= 18 and gameHour < 20 then
        -- Transitioning from dusk to night
        nextPhase = "night"
        transitionProgress = (gameHour - 18) / 2 + (gameMinute / 120)
    elseif timePhase == "night" and gameHour >= 4 and gameHour < 6 then
        -- Transitioning from night to dawn
        nextPhase = "dawn"
        transitionProgress = (gameHour - 4) / 2 + (gameMinute / 120)
    elseif timePhase == "dawn" and gameHour >= 6 and gameHour < 8 then
        -- Transitioning from dawn to day
        nextPhase = "day"
        transitionProgress = (gameHour - 6) / 2 + (gameMinute / 120)
    end

    -- Clamp transition progress between 0 and 1
    transitionProgress = math.max(0, math.min(1, transitionProgress))

    -- If we're in a transition, blend between current and next phase colors
    if transitionProgress > 0 then
        local nextColor = skyColors[nextPhase] and
                         (skyColors[nextPhase][weatherType] or skyColors[nextPhase].default) or
                         skyColors.default.default

        -- Blend colors for smooth transition
        skyColor = {
            skyColor[1] * (1 - transitionProgress) + nextColor[1] * transitionProgress,
            skyColor[2] * (1 - transitionProgress) + nextColor[2] * transitionProgress,
            skyColor[3] * (1 - transitionProgress) + nextColor[3] * transitionProgress
        }
    end

    -- Draw sky gradient
    if self.enableShaders and self.skyShader then
        -- Update shader with time of day
        self.skyShader:send("resolution", {self.settings.screenWidth, self.settings.screenHeight})

        -- Use shader for better gradient
        love.graphics.setShader(self.skyShader)
        love.graphics.setColor(skyColor)
        love.graphics.rectangle("fill", 0, 0, self.settings.screenWidth, self.settings.screenHeight)
        love.graphics.setShader()
    else
        -- Simple gradient without shader
        love.graphics.setColor(skyColor[1], skyColor[2], skyColor[3])
        love.graphics.rectangle("fill", 0, 0, self.settings.screenWidth, self.settings.screenHeight/2)

        -- Slightly darker for the bottom half
        love.graphics.setColor(skyColor[1] * 0.8, skyColor[2] * 0.8, skyColor[3] * 0.8)
        love.graphics.rectangle("fill", 0, self.settings.screenHeight/2, self.settings.screenWidth, self.settings.screenHeight/2)
    end

    -- Add special sky effects based on time of day
    if timePhase == "dawn" or (timePhase == "night" and gameHour >= 4 and gameHour < 6) then
        -- Dawn glow on the horizon
        local sunriseGlow = love.graphics.newCanvas(self.settings.screenWidth, self.settings.screenHeight/4)
        love.graphics.setCanvas(sunriseGlow)

        -- Create a radial gradient for sunrise
        local centerX = self.settings.screenWidth * 0.2
        local centerY = self.settings.screenHeight * 0.4
        local radius = self.settings.screenWidth * 0.4

        for r = radius, 0, -1 do
            local alpha = 0.1 * (1 - r/radius)
            love.graphics.setColor(1, 0.7, 0.4, alpha)
            love.graphics.circle("fill", centerX, centerY, r)
        end

        love.graphics.setCanvas()
        love.graphics.setColor(1, 1, 1, transitionProgress)
        love.graphics.draw(sunriseGlow, 0, 0)
    elseif timePhase == "dusk" or (timePhase == "day" and gameHour >= 16 and gameHour < 18) then
        -- Sunset glow on the horizon
        local sunsetGlow = love.graphics.newCanvas(self.settings.screenWidth, self.settings.screenHeight/4)
        love.graphics.setCanvas(sunsetGlow)

        -- Create a radial gradient for sunset
        local centerX = self.settings.screenWidth * 0.8
        local centerY = self.settings.screenHeight * 0.4
        local radius = self.settings.screenWidth * 0.4

        for r = radius, 0, -1 do
            local alpha = 0.1 * (1 - r/radius)
            love.graphics.setColor(0.9, 0.5, 0.3, alpha)
            love.graphics.circle("fill", centerX, centerY, r)
        end

        love.graphics.setCanvas()
        love.graphics.setColor(1, 1, 1, transitionProgress)
        love.graphics.draw(sunsetGlow, 0, 0)
    end

    -- Draw weather effects
    self:drawWeatherEffects(weatherType, timePhase)
end

-- Draw a dedicated horizon line for isometric view
function Renderer:drawHorizonLine()
    -- Always draw the horizon line, regardless of isometric mode
    -- This ensures it's visible in both menu and gameplay states

    -- Get screen dimensions
    local screenWidth = self.settings.screenWidth
    local screenHeight = self.settings.screenHeight

    -- Draw horizon line at 40% of screen height (higher on the screen)
    local horizonY = screenHeight * 0.4

    -- Save current graphics state
    love.graphics.push()
    love.graphics.origin() -- Reset transformation to draw in screen space

    -- Get time of day from the world for horizon coloring
    local dayPhase = "day"
    local gameHour = 12
    local gameMinute = 0

    -- Try to get the actual time from the world if available
    if _G.Engine and Engine.currentWorld and Engine.currentWorld.chunkSystem and Engine.currentWorld.chunkSystem.timeOfDay then
        gameHour = Engine.currentWorld.chunkSystem.timeOfDay.hour or 12
        gameMinute = Engine.currentWorld.chunkSystem.timeOfDay.minute or 0
        dayPhase = Engine.currentWorld.chunkSystem.timeOfDay.dayPhase or "day"
    end

    -- Calculate time-based colors for the horizon
    local horizonColors = {
        day = {
            mountains = {0.4, 0.5, 0.6, 0.8},
            skyGradient = {0.6, 0.7, 0.9},
            horizonLine = {0.5, 0.6, 0.9, 0.8},
            horizonAccent = {0.7, 0.8, 1.0, 0.6},
            groundFog = {0.7, 0.8, 0.9}
        },
        dawn = {
            mountains = {0.5, 0.4, 0.5, 0.8},
            skyGradient = {0.9, 0.7, 0.6},
            horizonLine = {0.9, 0.7, 0.5, 0.8},
            horizonAccent = {1.0, 0.8, 0.6, 0.6},
            groundFog = {0.8, 0.7, 0.6}
        },
        dusk = {
            mountains = {0.4, 0.3, 0.5, 0.8},
            skyGradient = {0.8, 0.5, 0.7},
            horizonLine = {0.8, 0.5, 0.7, 0.8},
            horizonAccent = {0.9, 0.6, 0.8, 0.6},
            groundFog = {0.7, 0.5, 0.7}
        },
        night = {
            mountains = {0.2, 0.2, 0.3, 0.8},
            skyGradient = {0.3, 0.3, 0.5},
            horizonLine = {0.3, 0.3, 0.5, 0.8},
            horizonAccent = {0.4, 0.4, 0.6, 0.6},
            groundFog = {0.3, 0.3, 0.5}
        }
    }

    -- Get the appropriate color set for the current time of day
    local colors = horizonColors[dayPhase] or horizonColors.day

    -- Calculate time-based animation offset
    local gameTimeInMinutes = (gameHour * 60 + gameMinute) % (24 * 60)
    local timeOffset = gameTimeInMinutes / (24 * 60) * 1000 -- 0-1000 over a day

    -- Draw distant mountains silhouette with time-based colors
    love.graphics.setColor(colors.mountains)

    -- Draw several mountain ranges with different heights and time-based movement
    local mountainRanges = {
        {baseHeight = 20, peakHeight = 60, frequency = 0.01, speed = 0.05},
        {baseHeight = 15, peakHeight = 40, frequency = 0.02, speed = 0.1},
        {baseHeight = 10, peakHeight = 30, frequency = 0.03, speed = 0.15}
    }

    for i, range in ipairs(mountainRanges) do
        local points = {}
        -- Start with left edge below horizon
        table.insert(points, 0)
        table.insert(points, horizonY + 5)

        -- Calculate time-based offset for this mountain range
        local rangeOffset = timeOffset * range.speed

        -- Generate mountain points with subtle movement
        for x = 0, screenWidth, 10 do
            local noise = math.sin((x + rangeOffset) * range.frequency) * 0.5 +
                          math.sin((x + rangeOffset * 0.7) * range.frequency * 2) * 0.25 +
                          math.sin((x + rangeOffset * 0.3) * range.frequency * 4) * 0.125

            local height = range.baseHeight + noise * range.peakHeight
            table.insert(points, x)
            table.insert(points, horizonY - height)
        end

        -- End with right edge below horizon
        table.insert(points, screenWidth)
        table.insert(points, horizonY + 5)

        -- Draw the mountain range as a filled polygon
        love.graphics.polygon("fill", points)
    end

    -- Draw a stronger gradient above the horizon (sky) with time-based colors
    local gradientHeight = 60
    for i = 0, gradientHeight do
        local alpha = 0.25 * (1 - i/gradientHeight)
        love.graphics.setColor(colors.skyGradient[1], colors.skyGradient[2], colors.skyGradient[3], alpha)
        love.graphics.line(0, horizonY - i, screenWidth, horizonY - i)
    end

    -- Draw the main horizon line with time-based colors
    love.graphics.setColor(colors.horizonLine)
    love.graphics.setLineWidth(3)
    love.graphics.line(0, horizonY, screenWidth, horizonY)

    -- Draw a second line for emphasis with time-based colors
    love.graphics.setColor(colors.horizonAccent)
    love.graphics.setLineWidth(1)
    love.graphics.line(0, horizonY+1, screenWidth, horizonY+1)

    -- Draw a stronger gradient below the horizon (ground fog) with time-based colors
    for i = 0, gradientHeight do
        local alpha = 0.2 * (1 - i/gradientHeight)
        love.graphics.setColor(colors.groundFog[1], colors.groundFog[2], colors.groundFog[3], alpha)
        love.graphics.line(0, horizonY + i, screenWidth, horizonY + i)
    end

    -- Add subtle animated elements based on time of day
    if dayPhase == "dawn" then
        -- Add sun rays at dawn
        local sunX = screenWidth * 0.2
        local sunY = horizonY - 10
        love.graphics.setColor(1, 0.8, 0.5, 0.3)
        for i = 1, 8 do
            local angle = i * math.pi / 4 + (timeOffset * 0.001)
            local length = 40
            love.graphics.line(
                sunX, sunY,
                sunX + math.cos(angle) * length,
                sunY + math.sin(angle) * length
            )
        end
    elseif dayPhase == "dusk" then
        -- Add sunset glow at dusk
        local sunX = screenWidth * 0.8
        local sunY = horizonY - 5
        love.graphics.setColor(0.9, 0.5, 0.3, 0.3)
        love.graphics.circle("fill", sunX, sunY, 30)
    elseif dayPhase == "night" then
        -- Add enhanced aurora effect at night
        -- First aurora layer (green-blue)
        local auroraPoints1 = {}
        local auroraHeight1 = 60
        local waveFreq1 = 0.01
        local waveSpeed1 = timeOffset * 0.1

        love.graphics.setColor(0.2, 0.6, 0.8, 0.2)

        table.insert(auroraPoints1, 0)
        table.insert(auroraPoints1, horizonY)

        for x = 0, screenWidth, 15 do
            local wave = math.sin((x + waveSpeed1) * waveFreq1) * auroraHeight1
            table.insert(auroraPoints1, x)
            table.insert(auroraPoints1, horizonY - wave - 10)
        end

        table.insert(auroraPoints1, screenWidth)
        table.insert(auroraPoints1, horizonY)

        love.graphics.polygon("fill", auroraPoints1)

        -- Second aurora layer (purple-pink)
        local auroraPoints2 = {}
        local auroraHeight2 = 40
        local waveFreq2 = 0.015
        local waveSpeed2 = timeOffset * 0.15

        love.graphics.setColor(0.6, 0.3, 0.8, 0.15)

        table.insert(auroraPoints2, 0)
        table.insert(auroraPoints2, horizonY)

        for x = 0, screenWidth, 15 do
            local wave = math.sin((x + waveSpeed2) * waveFreq2) * auroraHeight2
            table.insert(auroraPoints2, x)
            table.insert(auroraPoints2, horizonY - wave - 20)
        end

        table.insert(auroraPoints2, screenWidth)
        table.insert(auroraPoints2, horizonY)

        love.graphics.polygon("fill", auroraPoints2)

        -- Third aurora layer (green)
        local auroraPoints3 = {}
        local auroraHeight3 = 30
        local waveFreq3 = 0.02
        local waveSpeed3 = timeOffset * 0.2

        love.graphics.setColor(0.3, 0.8, 0.5, 0.1)

        table.insert(auroraPoints3, 0)
        table.insert(auroraPoints3, horizonY)

        for x = 0, screenWidth, 15 do
            local wave = math.sin((x + waveSpeed3) * waveFreq3) * auroraHeight3
            table.insert(auroraPoints3, x)
            table.insert(auroraPoints3, horizonY - wave - 30)
        end

        table.insert(auroraPoints3, screenWidth)
        table.insert(auroraPoints3, horizonY)

        love.graphics.polygon("fill", auroraPoints3)
    end

    -- Reset color and restore graphics state
    love.graphics.setColor(1, 1, 1, 1)
    love.graphics.pop()
end

-- Draw weather effects
function Renderer:drawWeatherEffects(weatherType, timePhase)
    -- Only draw weather effects if not in simplified mode
    if self.simplifiedTiles then return end

    local width = self.settings.screenWidth
    local height = self.settings.screenHeight

    -- Get time of day from the world for weather effects
    local gameHour = 12
    local gameMinute = 0

    if _G.Engine and Engine.currentWorld and Engine.currentWorld.chunkSystem and Engine.currentWorld.chunkSystem.timeOfDay then
        gameHour = Engine.currentWorld.chunkSystem.timeOfDay.hour or 12
        gameMinute = Engine.currentWorld.chunkSystem.timeOfDay.minute or 0
    end

    -- Calculate time-based animation
    local gameTimeInMinutes = (gameHour * 60 + gameMinute) % (24 * 60)
    local timeProgress = gameTimeInMinutes / (24 * 60) -- 0 to 1 over a full day
    local animTime = love.timer.getTime() * 0.5 -- For continuous animations

    -- Draw based on weather type
    if weatherType == "light_rain" or weatherType == "heavy_rain" then
        -- Draw rain with improved visuals and blue tint
        local intensity = weatherType == "heavy_rain" and 300 or 100

        -- Make rain blue-tinted as requested
        local rainColor = {0.7, 0.8, 1.0, 0.6} -- More blue tint

        -- Adjust rain color based on time of day while keeping blue tint
        if timePhase == "night" then
            rainColor = {0.3, 0.4, 0.8, 0.5} -- Darker blue at night
        elseif timePhase == "dawn" then
            rainColor = {0.7, 0.7, 1.0, 0.5} -- Lighter blue at dawn
        elseif timePhase == "dusk" then
            rainColor = {0.5, 0.6, 0.9, 0.5} -- Medium blue at dusk
        end

        love.graphics.setColor(rainColor)

        -- Rain drops with varying angles and sizes
        for i = 1, intensity do
            local x = math.random(0, width)
            local y = math.random(0, height)
            local length = math.random(5, weatherType == "heavy_rain" and 20 or 15)
            local angle = math.rad(70 + math.random(-10, 10)) -- Slight variation in angle
            local dx = math.cos(angle) * length
            local dy = math.sin(angle) * length

            love.graphics.setLineWidth(weatherType == "heavy_rain" and 2 or 1)
            love.graphics.line(x, y, x + dx, y + dy)
        end

        -- Add puddles on the ground for heavy rain with blue tint
        if weatherType == "heavy_rain" then
            love.graphics.setColor(0.5, 0.6, 1.0, 0.2) -- Bluer puddles
            for i = 1, 15 do
                local x = math.random(0, width)
                local y = math.random(height * 0.6, height * 0.9)
                local size = math.random(10, 30)
                love.graphics.ellipse("fill", x, y, size, size * 0.5)
            end

            -- Add ripples in puddles
            love.graphics.setColor(0.6, 0.7, 1.0, 0.15)
            for i = 1, 10 do
                local x = math.random(0, width)
                local y = math.random(height * 0.7, height * 0.95)
                local size = math.random(3, 8)
                love.graphics.circle("line", x, y, size)
                love.graphics.circle("line", x, y, size * 0.7)
            end

            -- Add occasional lightning flash
            if math.random() < 0.005 then
                love.graphics.setColor(1, 1, 1, 0.3)
                love.graphics.rectangle("fill", 0, 0, width, height)
            end
        end

    elseif weatherType == "foggy" then
        -- Draw fog with improved visuals
        love.graphics.setColor(1, 1, 1, 0.3)

        -- Create denser fog effect
        for i = 1, 30 do
            local x = math.random(0, width)
            local y = math.random(height * 0.3, height)
            local size = math.random(70, 200)
            -- Animate fog patches slowly
            local xOffset = math.sin(animTime + i * 0.1) * 20
            local yOffset = math.cos(animTime + i * 0.2) * 10
            love.graphics.circle("fill", x + xOffset, y + yOffset, size)
        end

        -- Add fog overlay
        love.graphics.setColor(0.9, 0.9, 0.9, 0.1)
        love.graphics.rectangle("fill", 0, height * 0.4, width, height * 0.6)

    elseif weatherType == "overcast" then
        -- Draw overcast sky with improved visuals
        love.graphics.setColor(0.7, 0.7, 0.7, 0.2)

        -- Create cloud cover
        for i = 1, 15 do
            local x = math.random(0, width)
            local y = math.random(height * 0.1, height * 0.4)
            local sizeX = math.random(100, 300)
            local sizeY = math.random(50, 100)
            -- Animate clouds very slowly
            local xOffset = math.sin(animTime * 0.2 + i * 0.3) * 10
            love.graphics.ellipse("fill", x + xOffset, y, sizeX, sizeY)
        end

        -- Darken the overall scene slightly
        love.graphics.setColor(0.5, 0.5, 0.5, 0.05)
        love.graphics.rectangle("fill", 0, 0, width, height)

    elseif weatherType == "sunny" then
        -- Draw sunny weather with improved visuals
        local sunX = width * (0.5 + 0.3 * math.cos(timeProgress * math.pi * 2))
        local sunY = height * (0.3 - 0.2 * math.sin(timeProgress * math.pi * 2))

        -- Only show sun during day, dawn, or dusk
        if timePhase ~= "night" then
            -- Sun glow with time-based color
            local sunColor = {1, 1, 0.8, 0.3} -- Default day
            if timePhase == "dawn" then
                sunColor = {1, 0.7, 0.4, 0.3} -- Orange at dawn
            elseif timePhase == "dusk" then
                sunColor = {1, 0.5, 0.3, 0.3} -- Red-orange at dusk
            end

            love.graphics.setColor(sunColor)

            -- Draw sun with rays
            for r = 40, 10, -5 do
                love.graphics.setColor(sunColor[1], sunColor[2], sunColor[3], sunColor[4] * (r / 40))
                love.graphics.circle("fill", sunX, sunY, r)
            end

            -- Sun rays with animation
            for i = 1, 12 do
                local angle = i * math.pi / 6 + animTime * 0.1
                local length = 60 + math.sin(animTime + i) * 10
                love.graphics.setColor(sunColor[1], sunColor[2], sunColor[3], sunColor[4] * 0.7)
                love.graphics.setLineWidth(2)
                love.graphics.line(
                    sunX, sunY,
                    sunX + math.cos(angle) * length,
                    sunY + math.sin(angle) * length
                )
            end

            -- Heat haze effect at the horizon during hot day
            if timePhase == "day" then
                love.graphics.setColor(1, 1, 1, 0.05)
                local horizonY = height * 0.4
                for i = 1, 10 do
                    local waveHeight = 3
                    local points = {}
                    for x = 0, width, 20 do
                        local wave = math.sin((x + animTime * 30) * 0.02) * waveHeight
                        table.insert(points, x)
                        table.insert(points, horizonY + wave + i * 5)
                    end
                    love.graphics.line(points)
                end
            end
        end

    elseif weatherType == "clear" then
        -- Draw clear weather effects
        if timePhase == "night" then
            -- Add extra stars for clear night
            love.graphics.setColor(1, 1, 1, 0.8)
            for i = 1, 30 do
                local x = math.random(0, width)
                local y = math.random(0, height * 0.4)
                local size = math.random(1, 3) * 0.5
                -- Twinkle effect
                local twinkle = (math.sin(animTime * 2 + i * 0.3) + 1) * 0.5
                love.graphics.circle("fill", x, y, size * (0.7 + twinkle * 0.3))
            end
        elseif timePhase == "day" then
            -- Subtle light rays through clouds on clear day
            love.graphics.setColor(1, 1, 0.9, 0.1)
            for i = 1, 5 do
                local x = width * (0.2 + i * 0.15)
                local topY = height * 0.1
                local bottomY = height * 0.4
                local rayWidth = 50 + math.sin(animTime + i) * 20

                love.graphics.polygon("fill",
                    x - rayWidth/2, topY,
                    x + rayWidth/2, topY,
                    x + rayWidth, bottomY,
                    x - rayWidth, bottomY
                )
            end
        end

    elseif weatherType == "windy" then
        -- Draw wind effects
        love.graphics.setColor(0.9, 0.9, 0.9, 0.2)

        -- Animated wind lines
        for i = 1, 20 do
            local y = height * (0.3 + i * 0.03)
            local speed = 100 + i * 10
            local offset = (animTime * speed) % (width * 2) - width
            local length = 50 + math.random(0, 50)

            love.graphics.line(offset, y, offset + length, y)
        end

        -- Blowing particles
        love.graphics.setColor(0.8, 0.8, 0.8, 0.3)
        for i = 1, 30 do
            local speed = 150 + i * 5
            local x = (width + (animTime * speed)) % (width * 1.2) - width * 0.1
            local y = height * (0.4 + math.random() * 0.5)
            local size = math.random(2, 5)

            love.graphics.circle("fill", x, y, size)
        end

    elseif weatherType == "sandstorm" then
        -- Draw sandstorm effects
        love.graphics.setColor(0.8, 0.7, 0.5, 0.4)

        -- Sand particles
        for i = 1, 200 do
            local speed = 200 + i * 3
            local x = (width + (animTime * speed)) % (width * 1.2) - width * 0.1
            local y = height * (0.3 + math.random() * 0.6)
            local size = math.random(1, 3)

            love.graphics.circle("fill", x, y, size)
        end

        -- Sand overlay
        love.graphics.setColor(0.7, 0.6, 0.4, 0.2)
        love.graphics.rectangle("fill", 0, 0, width, height)

    elseif weatherType == "snow" then
        -- Draw snow effects with pure white color
        love.graphics.setColor(1, 1, 1, 0.9) -- Pure white with high opacity

        -- Snowflakes - more varied and realistic
        for i = 1, 150 do
            local x = (width + i * 17 + animTime * (10 + i % 10)) % width
            local fallSpeed = 15 + i % 20 -- Varied fall speeds
            local y = (height + i * 13 + animTime * fallSpeed) % height
            local size = math.random(2, 5) -- Slightly larger flakes

            -- Snowflake with slight wobble
            local wobble = math.sin(animTime * 2 + i) * 2

            -- Draw different snowflake shapes
            if i % 5 == 0 then
                -- Star-shaped snowflake
                local points = {}
                local rays = 6
                for r = 1, rays do
                    local angle = r * math.pi * 2 / rays
                    -- Inner points
                    table.insert(points, x + wobble + math.cos(angle) * size * 0.4)
                    table.insert(points, y + math.sin(angle) * size * 0.4)
                    -- Outer points
                    local angleOffset = math.pi / rays
                    table.insert(points, x + wobble + math.cos(angle + angleOffset) * size)
                    table.insert(points, y + math.sin(angle + angleOffset) * size)
                end
                love.graphics.polygon("fill", points)
            else
                -- Simple circle snowflake
                love.graphics.circle("fill", x + wobble, y, size * 0.8)
            end
        end

        -- Add sparkle effect to some snowflakes
        love.graphics.setColor(1, 1, 1, 1) -- Full opacity for sparkles
        for i = 1, 20 do
            local x = (width * i / 20 + animTime * 5) % width
            local y = (height * 0.5 * math.sin(i * 0.5) + animTime * 10) % height
            local sparkleSize = 1 + math.sin(animTime * 3 + i) * 0.5
            love.graphics.circle("fill", x, y, sparkleSize)
        end

        -- Snow accumulation on ground - pure white
        love.graphics.setColor(1, 1, 1, 0.4)
        love.graphics.rectangle("fill", 0, height * 0.9, width, height * 0.1)

        -- Snow drifts - varied heights
        love.graphics.setColor(1, 1, 1, 0.6)
        for i = 1, 10 do
            local x = width * (i - 1) / 10
            local driftWidth = width / 10
            local driftHeight = height * (0.05 + math.sin(i * 0.7) * 0.03)
            love.graphics.rectangle("fill", x, height - driftHeight, driftWidth, driftHeight)
        end
    elseif weatherType == "aurora" then
        -- Draw aurora weather effect
        -- This is a special weather type that shows northern lights

        -- First aurora layer (green-blue)
        local auroraPoints1 = {}
        local auroraHeight1 = 120
        local waveFreq1 = 0.005
        local waveSpeed1 = animTime * 0.2

        love.graphics.setColor(0.2, 0.6, 0.8, 0.3)

        table.insert(auroraPoints1, 0)
        table.insert(auroraPoints1, height * 0.4)

        for x = 0, width, 10 do
            local wave = math.sin((x + waveSpeed1) * waveFreq1) * auroraHeight1
            table.insert(auroraPoints1, x)
            table.insert(auroraPoints1, height * 0.4 - wave - 20)
        end

        table.insert(auroraPoints1, width)
        table.insert(auroraPoints1, height * 0.4)

        love.graphics.polygon("fill", auroraPoints1)

        -- Second aurora layer (purple-pink)
        local auroraPoints2 = {}
        local auroraHeight2 = 80
        local waveFreq2 = 0.008
        local waveSpeed2 = animTime * 0.3

        love.graphics.setColor(0.6, 0.3, 0.8, 0.25)

        table.insert(auroraPoints2, 0)
        table.insert(auroraPoints2, height * 0.4)

        for x = 0, width, 10 do
            local wave = math.sin((x + waveSpeed2) * waveFreq2) * auroraHeight2
            table.insert(auroraPoints2, x)
            table.insert(auroraPoints2, height * 0.4 - wave - 40)
        end

        table.insert(auroraPoints2, width)
        table.insert(auroraPoints2, height * 0.4)

        love.graphics.polygon("fill", auroraPoints2)

        -- Third aurora layer (green)
        local auroraPoints3 = {}
        local auroraHeight3 = 60
        local waveFreq3 = 0.01
        local waveSpeed3 = animTime * 0.4

        love.graphics.setColor(0.3, 0.8, 0.5, 0.2)

        table.insert(auroraPoints3, 0)
        table.insert(auroraPoints3, height * 0.4)

        for x = 0, width, 10 do
            local wave = math.sin((x + waveSpeed3) * waveFreq3) * auroraHeight3
            table.insert(auroraPoints3, x)
            table.insert(auroraPoints3, height * 0.4 - wave - 60)
        end

        table.insert(auroraPoints3, width)
        table.insert(auroraPoints3, height * 0.4)

        love.graphics.polygon("fill", auroraPoints3)

        -- Add stars in the background
        love.graphics.setColor(1, 1, 1, 0.9)
        for i = 1, 50 do
            local x = math.random(0, width)
            local y = math.random(0, height * 0.4)
            local size = math.random(1, 3) * 0.5
            -- Twinkle effect
            local twinkle = (math.sin(animTime * 2 + i * 0.3) + 1) * 0.5
            love.graphics.circle("fill", x, y, size * (0.7 + twinkle * 0.3))
        end
    end

    -- Reset color and line width
    love.graphics.setColor(1, 1, 1, 1)
    love.graphics.setLineWidth(1)
end

return Renderer
