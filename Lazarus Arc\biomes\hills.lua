-- biomes/hills.lua
HillsBiome = {
    id = "hills",
    name = "Rolling Hills",
    description = "An area characterized by rolling terrain, steeper than plains but less rugged than mountains, often covered in grass, shrubs, or sparse trees.",

    -- Environmental factors (Moderate, slightly windy)
    environment = {
        humidity = 0.6,
        temperature = 0.55, -- Moderate
        sunlight = 0.8,    -- Mostly open, but shadows from slopes
        windStrength = 0.5 -- Windier than plains due to elevation changes
    },

    -- Core tile types used in this biome
    -- Uses existing: grass, dirt, rocky_ground, stone, water, forest_woodlot (if defined)
    primaryTiles = {"grass", "dirt"}, -- Grassy slopes, patches of bare earth
    secondaryTiles = {"rocky_ground", "stone", "forest_woodlot"}, -- Rockier areas, small woods
    rareTiles = {"water", "ore_deposit_surface", "ancient_barrow"}, -- Small streams/ponds, exposed ore, burial mounds

    -- Default proportions (Adjustable through variants)
    tileDistribution = {
        grass = 0.55,
        dirt = 0.20,
        rocky_ground = 0.12,
        stone = 0.05, -- Rock outcrops
        forest_woodlot = 0.05,
        water = 0.02,
        ore_deposit_surface = 0.005,
        ancient_barrow = 0.005
    },

    -- Entities common to this biome
    commonEntities = {
        "rabbit", "sheep", "hawk", "field_mouse", "common_shrub" -- Added shrub
    },
    uncommonEntities = {
        "goat", "fox", "shepherd", "hill_giant_young", "badger" -- Added badger/giant
    },
    rareEntities = {
        "hill_giant", "griffin_nesting", "earth_elemental_minor", "hermit_hilltop" -- Added griffin/elemental/hermit
    },

    -- Biome variants for diverse generation
    variants = {
        grassy_downs = {
            name = "Grassy Downs",
            environment = { windStrength=0.6 },
            tileAdjustments = { grass=0.7, dirt=0.15, rocky_ground=0.05 },
            entityAdjustments = { sheep=1.5, rabbit=1.3, hawk=1.2 },
            specialEffects = {"windswept_grass_visual", "wide_views"}
        },
        rocky_highlands = {
            name = "Rocky Highlands",
            environment = { temperature=0.5 },
            tileAdjustments = { grass=0.3, dirt=0.1, rocky_ground=0.3, stone=0.2, ore_deposit_surface=0.02 },
            entityAdjustments = { goat=1.5, hill_giant_young=1.1, rock_elemental_small=1.0 }, -- Added rock elemental
            specialEffects = {"rock_outcrops", "mineral_presence", "sturdy_footing"}
        },
        wooded_hills = {
            name = "Wooded Hills",
            environment = { sunlight=0.6, windStrength=0.3 },
            tileAdjustments = { grass=0.3, dirt=0.15, forest_woodlot=0.3, rocky_ground=0.1 }, -- Assumes forest_woodlot tile/entity exists
            entityAdjustments = { deer=1.2, fox=1.1, hermit_hilltop=1.0 },
            specialEffects = {"mixed_forest_patches", "dappled_sunlight"}
        },
        ancient_barrows = {
             name = "Ancient Barrow Downs",
             environment = { magicResidue=0.2 }, -- Lingering magic?
             tileAdjustments = { grass=0.5, dirt=0.2, ancient_barrow=0.15, stone=0.05 },
             entityAdjustments = { ghost_minor=1.2, cursed_spirit_weak=1.0, archaeologist=0.5 }, -- Added spirits/archaeologist
             specialEffects = {"eerie_mist_night", "ancient_stones", "sense_of_history"}
        }
    },

    -- Structures that can generate in this biome
    structures = {
        { name = "shepherd_hut", chance = 0.1 },
        { name = "watchtower", chance = 0.08 }, -- Good visibility from hills
        { name = "ruined_hill_fort", chance = 0.05 }, -- Use ruins tiles
        { name = "standing_stones_circle", chance = 0.06 }, -- Feature/structure
        { name = "small_mine_entrance", chance = 0.04, requiresVariant="rocky_highlands" }, -- Cave entrance maybe?
        { name = "hermitage", chance = 0.03 }
    },

    -- Weather patterns (Standard temperate, maybe more wind/fog)
    weather = {
        transitions = {
             clear = { clear = 55, cloudy = 30, rain_light = 10, fog = 5 },
             cloudy = { clear = 40, cloudy = 40, rain_light = 15, fog = 5 },
             rain_light = { cloudy = 60, rain_light = 30, clear = 10 },
             fog = { clear = 40, cloudy = 30, fog = 30 } -- Fog can linger in valleys
        },
        default = "clear"
    },

    -- Unique features generation
    features = {
        { name = "hilltop_vista", chance = 0.2 }, -- Point providing good view
        { name = "secluded_valley", chance = 0.1 }, -- Area with different micro-climate/spawns?
        { name = "rocky_tor", chance = 0.15, requiresVariant="rocky_highlands" }, -- Large rock outcrop feature
        { name = "ancient_burial_mound", chance = 0.1, requiresVariant="ancient_barrows"}, -- Places barrow tiles/structure
        { name = "natural_spring_hillside", chance = 0.12 } -- Places water source/stream start
    },

    -- Generation algorithm (Focus on creating rolling terrain)
    generate = function(chunk, world, variant)
        print("Generating " .. (variant and HillsBiome.variants[variant].name or "Rolling Hills") .. " biome")
        local variantData = variant and HillsBiome.variants[variant] or nil

        -- Adjust tile distribution
        local tileDistribution = {}
        for tileType, chance in pairs(HillsBiome.tileDistribution) do
            tileDistribution[tileType] = chance
            if variantData and variantData.tileAdjustments and variantData.tileAdjustments[tileType] then
                tileDistribution[tileType] = variantData.tileAdjustments[tileType]
            end
        end

        -- Noise functions
        local seed = world.seed + (chunk.x * 823 + chunk.y * 307)
        math.randomseed(seed)
        local function pnoise2D(x, y, frequency, seed_offset) return love.math.noise(x * frequency, y * frequency, (world.seed + (seed_offset or 0)) * 0.01) end

        -- Generate tiles
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                local worldX = chunk.x * world.CHUNK_SIZE + x
                local worldY = chunk.y * world.CHUNK_SIZE + y

                local elevationNoise = pnoise2D(worldX, worldY, 0.04, 1) -- Controls hill height/shape
                local roughnessNoise = pnoise2D(worldX, worldY, 0.15, 2) -- Controls rockiness vs grass/dirt
                local featureNoise = pnoise2D(worldX, worldY, 0.08, 3) -- Controls rarer features like water/forest patches

                -- Determine tile type
                local tileType = "grass" -- Default is grassy slopes
                local tileSlope = math.abs(pnoise2D(worldX, worldY, 0.1, 4) * 2 - 1) -- Crude slope calculation from noise

                if roughnessNoise > 0.4 then
                    tileType = "rocky_ground"
                    if roughnessNoise > 0.7 then tileType = "stone" end
                elseif roughnessNoise < -0.3 then
                    tileType = "dirt"
                end

                -- Place forest woodlots or water based on feature noise in lower/sheltered areas
                if elevationNoise < 0 and featureNoise > 0.5 then
                    tileType = "forest_woodlot" -- Assumes this tile exists
                elseif elevationNoise < -0.2 and featureNoise < -0.5 then
                    tileType = "water" -- Small ponds/streams
                end

                -- Apply variant adjustments (simplified)
                if variant == "rocky_highlands" and roughnessNoise > 0.1 then
                     tileType = math.random() < 0.6 and "rocky_ground" or "stone"
                elseif variant == "wooded_hills" and roughnessNoise > -0.5 then -- More trees unless very rocky
                     if math.random() < 0.3 then tileType = "forest_woodlot" end
                end

                -- Create the tile
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = math.random(1, 3),
                    slope = tileSlope * 0.5, -- Scale noise to slope value 0-0.5 approx
                    passable = true, -- Assume all base hill tiles are passable
                    isUnderground = false
                }

                 -- Adjust movement speed based on slope/type
                 tile.movementSpeed = 1.0 - tile.slope * 0.4 -- Slower on steeper slopes
                 if tileType == "rocky_ground" or tileType == "stone" then tile.movementSpeed = tile.movementSpeed * 0.9 end

                -- Add to chunk
                chunk.tiles[x][y] = tile
            end
        end

        return chunk
    end,

    -- Initialize the biome
    init = function(biome, world)
        -- Initialize biome properties
        biome.properties = biome.properties or {}
        
        -- Copy all fields from HillsBiome template to biome instance
        for k, v in pairs(HillsBiome) do
            if type(v) ~= "function" and biome[k] == nil then
                if type(v) == "table" then
                    -- Deep copy for tables
                    biome[k] = {}
                    for subk, subv in pairs(v) do
                        biome[k][subk] = subv
                    end
                else
                    biome[k] = v
                end
            end
        end

        return biome
    end
}

return HillsBiome