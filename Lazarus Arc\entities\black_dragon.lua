-- entities/black_dragon.lua
-- Black dragon with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local BlackDragon = {
    id = "black_dragon",
    name = "Black Dragon",
    type = "black_dragon",
    shape = {
        {0, -20}, {15, -15}, {25, 0}, {15, 15},
        {0, 20}, {-15, 15}, {-25, 0}, {-15, -15}
    },
    size = 25,

    -- Entity categories
    categories = {"dragon", "flying", "acid", "swamp", "evil"},
    threatCategories = {"player", "knight", "treasure_hunter", "dragon_slayer"},
    
    -- Base stats (acid-breathing swamp dragon)
    maxHealth = 720,
    health = 720,
    maxStamina = 260,
    stamina = 260,
    maxMana = 380,
    mana = 380,
    speed = 2.5,
    attack = 55,
    defense = 38,
    magicAttack = 45,
    magicDefense = 40,
    acidResistance = 100,
    holyVulnerability = 2.0,
    flight_altitude = 30,
    swamp_mastery = 25,
    corruption = 20,
    
    -- Behaviors
    behaviors = {"swamp_lurk", "acid_breath", "corruption_aura", "ambush_tactics"},
    behaviorConfigs = {
        swamp_lurk = {
            swampCamouflage = 2.0,
            underwaterMovement = true,
            murkyWaters = true,
            patientStalker = true
        },
        acid_breath = {
            breathRange = 30,
            acidDamage = 2.5,
            armorCorrosion = true,
            acidPool = 12,
            meltingEffect = 2.0
        },
        corruption_aura = {
            corruptionRadius = 15,
            plantWithering = true,
            waterPollution = true,
            moraleDrain = 1.5
        },
        ambush_tactics = {
            surpriseAttack = 3.0,
            retreatToSwamp = true,
            hitAndRun = true,
            psychologicalTerror = 2.0
        }
    },
    
    -- Enhanced variant system for black dragons
    variantChances = {
        normal = 0.50,          -- 50% young black dragon
        shiny = 0.35,           -- 35% adult black dragon (shiny)
        rare = 0.13,            -- 13% ancient black dragon (rare)
        legendary = 0.02        -- 2% shadow wyrm (legendary)
    },
    
    variants = {
        normal = {
            name = "Young Black Dragon",
            description = "A vicious young dragon that dwells in fetid swamps",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.2, 0.2, 0.3, 1.0}  -- Dark black scales
            }
        },
        
        shiny = {
            name = "Adult Black Dragon",
            description = "A mature swamp dragon with mastery over acid and corruption",
            statModifiers = {
                maxHealth = 1.4,    -- 1008 health
                attack = 1.5,       -- 82.5 attack
                defense = 1.3,      -- 49.4 defense
                corruption = 1.8,   -- Enhanced corruption
                acid_mastery = 2.0,
                swamp_dominion = 2.5
            },
            appearanceModifiers = {
                scale = 1.3,
                glow = true,
                colorTint = {0.1, 0.1, 0.2, 1.0},  -- Deep black with acid glow
                acid_drool = true,
                corruption_aura = true,
                swamp_integration = true
            },
            soundModifiers = {
                pitch = 0.7,
                volume = 1.3,
                menacing = true
            }
        },
        
        rare = {
            name = "Ancient Black Dragon",
            description = "An ancient wyrm that has corrupted entire regions",
            statModifiers = {
                maxHealth = 2.1,    -- 1512 health
                attack = 1.9,       -- 104.5 attack
                defense = 1.6,      -- 60.8 defense
                corruption = 2.5,   -- Master of corruption
                acid_mastery = 3.5,
                regional_corruption = 4.0,
                ancient_malice = 5.0
            },
            appearanceModifiers = {
                scale = 1.6,
                colorTint = {0.05, 0.05, 0.15, 1.0},  -- Ancient black with corruption
                permanent_acid_rain = true,
                corruption_scars = true,
                withered_landscape = true,
                ancient_evil = true
            },
            soundModifiers = {
                pitch = 0.5,
                volume = 1.5,
                reverb = true,
                malevolent = true
            }
        },
        
        legendary = {
            name = "Shadow Wyrm",
            description = "A legendary dragon that embodies darkness and despair itself",
            statModifiers = {
                maxHealth = 2.8,    -- 2016 health
                attack = 2.3,       -- 126.5 attack
                defense = 2.0,      -- 76 defense
                corruption = 3.5,   -- Ultimate corruption
                shadow_mastery = 10.0,
                despair_embodiment = 5.0,
                reality_corruption = 3.0
            },
            appearanceModifiers = {
                scale = 2.0,
                glow = true,
                colorTint = {0.0, 0.0, 0.1, 1.0},  -- Void black with shadow energy
                shadow_embodiment = true,
                despair_aura = "legendary",
                reality_corruption = true,
                darkness_mastery = true
            },
            soundModifiers = {
                pitch = 0.3,
                volume = 1.8,
                reverb = true,
                echo = true,
                despair_resonance = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "black_dragon_scale", chance = 1.0, quantity = {5, 10}},
        {id = "dragon_blood", chance = 0.9, quantity = {2, 4}},
        {id = "acid_fang", chance = 0.8, quantity = {1, 2}},
        {id = "corruption_essence", chance = 1.0, quantity = {3, 6}},
        {id = "malevolent_heart", chance = 0.7, quantity = {1, 1}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "swamp_dragon_scale", chance = 1.0, quantity = {6, 12}},
            {id = "acid_mastery_gland", chance = 0.9, quantity = {1, 1}},
            {id = "corruption_crystal", chance = 0.8, quantity = {1, 1}},
            {id = "swamp_dominion_essence", chance = 0.7, quantity = {1, 2}}
        },
        rare = {
            {id = "ancient_corruption_scale", chance = 1.0, quantity = {8, 15}},
            {id = "regional_corruption_core", chance = 0.9, quantity = {1, 2}},
            {id = "ancient_malice_crystal", chance = 0.8, quantity = {1, 1}},
            {id = "withering_breath_essence", chance = 0.7, quantity = {2, 4}},
            {id = "corruption_mastery_orb", chance = 0.8, quantity = {1, 1}}
        },
        legendary = {
            {id = "shadow_wyrm_scale", chance = 1.0, quantity = {10, 20}},
            {id = "despair_embodiment_core", chance = 0.95, quantity = {1, 1}},
            {id = "shadow_mastery_orb", chance = 0.9, quantity = {1, 1}},
            {id = "reality_corruption_essence", chance = 0.8, quantity = {1, 2}},
            {id = "darkness_mastery_codex", chance = 0.7, quantity = {1, 1}},
            {id = "despair_dominion_crown", chance = 0.6, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "black_dragon",
        scale = 1.8,
        animations = {
            "swamp_glide", "acid_breath", "corruption_roar", "shadow_merge", "despair_aura"
        },
        variants = {
            "young_black", "adult_black", "ancient_black", "shadow_wyrm"
        }
    },
    
    -- Sound effects with dark characteristics
    sounds = {
        corruption_roar = {
            file = "black_dragon_roar",
            synth = {
                instrument = "organ",
                notes = {"Bb0", "F1", "Bb1", "F2"},
                durations = {1.4, 1.2, 1.3, 1.6},
                volume = 1.1,
                ominous = true
            }
        },
        acid_breath = {
            file = "acid_breath",
            synth = {
                instrument = "synthesizer",
                notes = {"C2", "Eb2", "G2", "C3"},
                durations = {0.7, 0.6, 0.8, 0.9},
                volume = 0.9,
                corrosive = true
            }
        },
        shadow_whisper = {
            file = "shadow_whisper",
            synth = {
                instrument = "choir",
                notes = {"D2", "F2", "Ab2", "D3"},
                durations = {1.0, 0.8, 1.0, 1.2},
                volume = 0.7,
                haunting = true
            }
        },
        despair_echo = {
            file = "despair_echo",
            synth = {
                instrument = "organ",
                notes = {"C1", "Eb1", "G1", "C2"},
                durations = {1.5, 1.2, 1.4, 2.0},
                volume = 0.8,
                soul_crushing = true
            }
        }
    },
    
    -- Special black dragon abilities
    abilities = {
        acid_breath = {
            type = "active",
            description = "Corrosive acid that melts armor and flesh",
            effect = "acid_breath_attack",
            cooldown = 12
        },
        corruption_aura = {
            type = "passive",
            description = "Corrupts the environment and weakens enemies",
            effect = "corruption_field"
        },
        swamp_mastery = {
            type = "passive",
            description = "Moves freely through swamps and murky water",
            effect = "swamp_movement"
        },
        ambush_predator = {
            type = "passive",
            description = "Gains massive damage bonus when attacking from hiding",
            effect = "ambush_damage"
        },
        acid_immunity = {
            type = "passive",
            description = "Complete immunity to acid and corrosive damage",
            effect = "acid_immunity"
        },
        despair_aura = {
            type = "active",
            description = "Instills hopelessness and fear in all nearby enemies",
            effect = "despair_field",
            cooldown = 25
        }
    }
}

-- Initialize the black dragon entity using enhanced template
function BlackDragon.init(entity, world)
    -- Copy all fields from BlackDragon template to entity instance
    for k, v in pairs(BlackDragon) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return BlackDragon
