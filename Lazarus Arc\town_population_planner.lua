-- town_population_planner.lua
-- Determines town population composition based on size, region, culture

local TownPlanner = {}

-- Town tiers
TownPlanner.tiers = {
    hamlet = {min = 5, max = 20},
    village = {min = 21, max = 100},
    town = {min = 101, max = 500},
    city = {min = 501, max = 2000},
    capital = {min = 2001, max = 10000}
}

-- Example profession breakdowns
TownPlanner.professionWeights = {
    base = {
        farmer = 0.3,
        laborer = 0.2,
        merchant = 0.1,
        guard = 0.1,
        artisan = 0.15,
        scholar = 0.05,
        noble = 0.01,
        clergy = 0.09
    }
}

function TownPlanner.plan(sizeCategory)
    local tier = TownPlanner.tiers[sizeCategory]
    if not tier then error("Invalid size category: " .. tostring(sizeCategory)) end

    local popSize = math.random(tier.min, tier.max)
    local weights = TownPlanner.professionWeights.base
    local breakdown = {}

    for profession, weight in pairs(weights) do
        breakdown[profession] = math.floor(popSize * weight + 0.5)
    end

    breakdown.total = popSize
    return breakdown
end

return TownPlanner
