local SandPit = {
    id = "sand_pit",
    name = "Sand Pit",
    type = "trap",
    
    -- Entity categories
    categories = {"trap", "environmental", "desert", "sand"},
    
    -- Target categories
    targetCategories = {"player", "animal", "monster"},
    
    -- Stats
    maxHealth = 40,
    health = 40,
    duration = 20,
    cooldown = 45,
    
    -- Trap properties
    properties = {
        sinkSpeed = 0.5,
        slowAmount = 0.7,
        damagePerSecond = 3,
        radius = 4,
        setupTime = 4,
        isArmed = false,
        isTriggered = false,
        maxDepth = 3
    },
    
    -- Appearance
    appearance = {
        sprite = "sand_pit",
        scale = 1.2,
        animations = {
            "idle", "trigger", "sink"
        },
        variants = {
            "light", "dark", "red", "golden"
        }
    },
    
    -- Sound effects
    sounds = {
        trigger = "sand_pit_trigger",
        sink = "sand_pit_sink",
        collapse = "sand_pit_collapse"
    },
    
    -- Effects
    effects = {
        sink = {
            type = "status",
            duration = 8,
            effects = {
                slow = 0.7,
                damage = 3,
                interval = 1,
                immobilize = true
            }
        }
    }
}

-- Initialize the trap
function SandPit.init(entity, world)
    -- Copy all fields from SandPit template to entity instance
    for k, v in pairs(SandPit) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position
    entity.position = entity.position or {x = 0, y = 0}

    -- Set random variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize trap state
    entity.properties.isArmed = false
    entity.properties.isTriggered = false
    entity.properties.triggerTime = 0
    entity.properties.currentDepth = 0

    return entity
end

-- Update the trap
function SandPit.update(entity, world, dt)
    -- Check if trap is armed and not triggered
    if entity.properties.isArmed and not entity.properties.isTriggered then
        -- Check for targets in radius
        if world and world.entities then
            for _, target in ipairs(world.entities) do
                if target.position and target.categories then
                    -- Check if target is in trap categories
                    local isTarget = false
                    for _, category in ipairs(entity.targetCategories) do
                        if table.contains(target.categories, category) then
                            isTarget = true
                            break
                        end
                    end
                    
                    if isTarget then
                        local distance = math.sqrt(
                            (target.position.x - entity.position.x)^2 + 
                            (target.position.y - entity.position.y)^2
                        )
                        
                        if distance <= entity.properties.radius then
                            -- Trigger trap
                            entity.properties.isTriggered = true
                            entity.properties.triggerTime = world.time
                            
                            -- Apply sink effect to target
                            if target.applyEffect then
                                target.applyEffect(entity.effects.sink)
                            end
                            
                            -- Play trigger sound
                            if world.playSound then
                                world.playSound(entity.sounds.trigger)
                            end
                            
                            break
                        end
                    end
                end
            end
        end
    end
    
    -- Handle sinking effect
    if entity.properties.isTriggered then
        entity.properties.currentDepth = math.min(
            entity.properties.currentDepth + entity.properties.sinkSpeed * dt,
            entity.properties.maxDepth
        )
        
        -- Apply additional slow effect based on depth
        if world and world.entities then
            for _, target in ipairs(world.entities) do
                if target.position and target.categories then
                    local distance = math.sqrt(
                        (target.position.x - entity.position.x)^2 + 
                        (target.position.y - entity.position.y)^2
                    )
                    if distance <= entity.properties.radius then
                        local depthFactor = entity.properties.currentDepth / entity.properties.maxDepth
                        if target.applyEffect then
                            target.applyEffect({
                                type = "status",
                                duration = 1,
                                effects = {
                                    slow = 0.7 + (0.3 * depthFactor)
                                }
                            })
                        end
                    end
                end
            end
        end
    end
    
    -- Check if trap should be destroyed
    if entity.properties.isTriggered and world.time - entity.properties.triggerTime >= entity.duration then
        if world.removeEntity then
            world.removeEntity(entity)
        end
    end
    
    -- Check if trap can be rearmed
    if not entity.properties.isArmed and world.time - entity.properties.triggerTime >= entity.cooldown then
        entity.properties.isArmed = true
        entity.properties.isTriggered = false
        entity.properties.currentDepth = 0
    end
end

-- Arm the trap
function SandPit.arm(entity, world)
    if not entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = true
        entity.properties.triggerTime = world.time
        entity.properties.currentDepth = 0
        return true
    end
    return false
end

-- Disarm the trap
function SandPit.disarm(entity, world)
    if entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = false
        entity.properties.triggerTime = world.time
        entity.properties.currentDepth = 0
        return true
    end
    return false
end

return SandPit 