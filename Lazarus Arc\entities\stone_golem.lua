-- entities/stone_golem.lua
-- Stone golem with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local StoneGolem = {
    id = "stone_golem",
    name = "Stone Golem",
    type = "stone_golem",
    shape = {
        {0, -10}, {7, -7}, {10, 0}, {7, 7},
        {0, 10}, {-7, 7}, {-10, 0}, {-7, -7}
    },
    size = 12,

    -- Entity categories
    categories = {"golem", "construct", "earth", "guardian", "magical"},
    threatCategories = {"player", "intruder", "destroyer"},
    
    -- Base stats (sturdy construct)
    maxHealth = 200,
    health = 200,
    maxStamina = 80,
    stamina = 80,
    maxMana = 50,
    mana = 50,
    speed = 1.5,  -- Slow but steady
    attack = 35,
    defense = 45,  -- Very high defense
    magicDefense = 25,
    stoneResistance = 50,
    lightningVulnerability = 1.5,
    construct_durability = 30,
    earth_affinity = 25,
    
    -- Behaviors
    behaviors = {"guardian_patrol", "earth_magic", "construct_repair", "stone_slam"},
    behaviorConfigs = {
        guardian_patrol = {
            patrolRadius = 30,
            guardianDuty = true,
            protectiveInstinct = 2.0,
            territorialDefense = 1.8
        },
        earth_magic = {
            stoneShaping = 2.0,
            earthMovement = 1.5,
            rockThrow = 2.2,
            seismicPower = 1.8
        },
        construct_repair = {
            selfRepair = 1.5,
            stoneAbsorption = true,
            durabilityRestore = 2.0,
            constructResilience = 2.5
        },
        stone_slam = {
            slamDamage = 2.5,
            groundShake = true,
            stunChance = 0.4,
            areaImpact = 8
        }
    },
    
    -- Enhanced variant system for stone golems
    variantChances = {
        normal = 0.60,          -- 60% granite golem
        shiny = 0.28,           -- 28% marble golem (shiny)
        rare = 0.10,            -- 10% obsidian golem (rare)
        legendary = 0.02        -- 2% ancient stone titan (legendary)
    },
    
    variants = {
        normal = {
            name = "Granite Golem",
            description = "A sturdy golem carved from solid granite",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.6, 0.6, 0.7, 1.0}  -- Gray granite
            }
        },
        
        shiny = {
            name = "Marble Golem",
            description = "An elegant golem crafted from pristine white marble",
            statModifiers = {
                maxHealth = 1.4,    -- 280 health
                defense = 1.3,      -- 58.5 defense
                magicDefense = 1.5, -- 37.5 magic defense
                construct_durability = 1.6,
                artistic_perfection = 2.0,
                marble_elegance = 1.8
            },
            appearanceModifiers = {
                scale = 1.2,
                glow = true,
                colorTint = {1.1, 1.1, 1.2, 1.0},  -- Pristine white marble
                marble_polish = true,
                elegant_form = true,
                artistic_details = true
            },
            soundModifiers = {
                pitch = 1.1,
                volume = 1.2,
                refined = true
            }
        },
        
        rare = {
            name = "Obsidian Golem",
            description = "A fearsome golem forged from volcanic obsidian",
            statModifiers = {
                maxHealth = 1.6,    -- 320 health
                attack = 1.8,       -- 63 attack
                defense = 1.5,      -- 67.5 defense
                volcanic_power = 3.0,
                obsidian_sharpness = 2.5,
                molten_core = 2.0
            },
            appearanceModifiers = {
                scale = 1.3,
                colorTint = {0.2, 0.1, 0.1, 1.0},  -- Dark volcanic obsidian
                obsidian_sheen = true,
                volcanic_glow = true,
                sharp_edges = true,
                molten_veins = true
            },
            soundModifiers = {
                pitch = 0.8,
                volume = 1.4,
                reverb = true,
                volcanic = true
            }
        },
        
        legendary = {
            name = "Ancient Stone Titan",
            description = "A colossal golem from the dawn of civilization",
            statModifiers = {
                maxHealth = 2.5,    -- 500 health
                attack = 2.2,       -- 77 attack
                defense = 2.0,      -- 90 defense
                construct_durability = 3.0,
                ancient_power = 10.0,
                titan_strength = 5.0,
                geological_mastery = 4.0
            },
            appearanceModifiers = {
                scale = 1.8,
                glow = true,
                colorTint = {0.8, 0.7, 0.6, 1.0},  -- Ancient weathered stone
                titan_proportions = true,
                ancient_runes = true,
                geological_aura = "legendary",
                time_weathered = true
            },
            soundModifiers = {
                pitch = 0.5,
                volume = 1.8,
                reverb = true,
                echo = true,
                ancient_resonance = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "stone_chunk", chance = 1.0, quantity = {3, 6}},
        {id = "golem_core", chance = 0.8, quantity = {1, 1}},
        {id = "earth_essence", chance = 0.7, quantity = {1, 3}},
        {id = "construct_fragment", chance = 0.6, quantity = {2, 4}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "marble_block", chance = 1.0, quantity = {2, 4}},
            {id = "artistic_golem_core", chance = 0.9, quantity = {1, 1}},
            {id = "elegance_essence", chance = 0.8, quantity = {1, 2}},
            {id = "marble_polish", chance = 0.7, quantity = {1, 1}}
        },
        rare = {
            {id = "obsidian_shard", chance = 1.0, quantity = {3, 6}},
            {id = "volcanic_golem_core", chance = 0.9, quantity = {1, 1}},
            {id = "molten_essence", chance = 0.8, quantity = {1, 2}},
            {id = "obsidian_blade", chance = 0.7, quantity = {1, 1}}
        },
        legendary = {
            {id = "titan_stone", chance = 1.0, quantity = {4, 8}},
            {id = "ancient_titan_core", chance = 0.95, quantity = {1, 1}},
            {id = "geological_mastery_crystal", chance = 0.9, quantity = {1, 1}},
            {id = "titan_strength_essence", chance = 0.8, quantity = {1, 2}},
            {id = "ancient_rune_tablet", chance = 0.7, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "stone_golem",
        scale = 1.3,
        animations = {
            "stomp", "stone_slam", "earth_magic", "guardian_stance", "titan_roar"
        },
        variants = {
            "granite_golem", "marble_golem", "obsidian_golem", "ancient_stone_titan"
        }
    },
    
    -- Sound effects with stone characteristics
    sounds = {
        stone_rumble = {
            file = "stone_golem_rumble",
            synth = {
                instrument = "organ",
                notes = {"C1", "F1", "C2"},
                durations = {1.2, 1.0, 1.5},
                volume = 0.9,
                stony = true
            }
        },
        stone_slam = {
            file = "stone_slam",
            synth = {
                instrument = "percussion",
                notes = {"D1", "G1", "D2"},
                durations = {0.4, 0.3, 0.6},
                volume = 1.1,
                crushing = true
            }
        },
        earth_magic = {
            file = "earth_magic",
            synth = {
                instrument = "bass_guitar",
                notes = {"E1", "A1", "E2"},
                durations = {0.8, 0.6, 1.0},
                volume = 0.7,
                earthen = true
            }
        },
        construct_hum = {
            file = "construct_hum",
            synth = {
                instrument = "synthesizer",
                notes = {"G2", "C3", "G3"},
                durations = {1.0, 0.8, 1.2},
                volume = 0.5,
                mechanical = true
            }
        }
    },
    
    -- Special golem abilities
    abilities = {
        stone_skin = {
            type = "passive",
            description = "Natural stone armor provides high physical defense",
            effect = "physical_resistance"
        },
        earth_manipulation = {
            type = "active",
            description = "Shapes stone and earth to create barriers or weapons",
            effect = "earth_shaping",
            manaCost = 30,
            cooldown = 15
        },
        seismic_slam = {
            type = "active",
            description = "Powerful ground slam that shakes the earth",
            effect = "area_earthquake",
            cooldown = 18
        },
        construct_repair = {
            type = "active",
            description = "Absorbs nearby stone to repair damage",
            effect = "stone_healing",
            cooldown = 25
        },
        guardian_duty = {
            type = "passive",
            description = "Gains bonuses when protecting designated areas",
            effect = "guardian_bonus"
        },
        immovable_object = {
            type = "active",
            description = "Becomes completely immobile but nearly invulnerable",
            effect = "defensive_stance",
            duration = 10,
            cooldown = 40
        }
    }
}

-- Initialize the stone golem entity using enhanced template
function StoneGolem.init(entity, world)
    -- Copy all fields from StoneGolem template to entity instance
    for k, v in pairs(StoneGolem) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return StoneGolem
