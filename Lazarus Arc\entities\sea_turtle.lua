-- entities/sea_turtle.lua
-- Underwater sea turtle with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local SeaTurtle = {
    id = "sea_turtle",
    name = "Sea Turtle",
    type = "sea_turtle",
    shape = {
        {0, -1}, {0.9, -0.7}, {1.2, 0}, {0.9, 0.7},
        {0, 1}, {-0.9, 0.7}, {-1.2, 0}, {-0.9, -0.7}
    },
    size = 9,

    -- Entity categories
    categories = {"aquatic", "peaceful", "ancient", "wise"},
    threatCategories = {"shark", "predator"},
    
    -- Base stats (defensive and long-lived)
    maxHealth = 100,
    health = 100,
    maxStamina = 60,
    stamina = 60,
    speed = 1.5,  -- Slow but steady
    attack = 8,   -- Low attack, mostly defensive
    defense = 20, -- Very high defense
    wisdom = 15,
    longevity = 25,
    waterBreathing = true,
    
    -- Behaviors
    behaviors = {"peaceful_swim", "deep_dive", "ancient_wisdom", "migration"},
    behaviorConfigs = {
        peaceful_swim = {
            moveSpeed = 1.8,
            graceful = true,
            avoidConflict = true,
            currentRiding = true
        },
        deep_dive = {
            maxDepth = 100,
            breathHold = 60,  -- Can hold breath for long periods
            pressureResistance = true
        },
        ancient_wisdom = {
            memorySpan = 100,  -- Remembers locations for decades
            navigationSkill = 2.0,
            weatherPrediction = true
        },
        migration = {
            seasonalMovement = true,
            longDistance = true,
            magneticNavigation = true
        }
    },
    
    -- Enhanced variant system for sea turtles
    variantChances = {
        normal = 0.75,          -- 75% green sea turtle
        shiny = 0.18,           -- 18% leatherback turtle (shiny)
        rare = 0.06,            -- 6% ancient turtle (rare)
        legendary = 0.01        -- 1% world turtle (legendary)
    },
    
    variants = {
        normal = {
            name = "Green Sea Turtle",
            description = "A peaceful sea turtle grazing on sea grass",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.6, 0.8, 0.6, 1.0}  -- Green shell
            }
        },
        
        shiny = {
            name = "Leatherback Turtle",
            description = "A massive turtle with a leathery shell and incredible diving ability",
            statModifiers = {
                maxHealth = 1.6,    -- 160 health
                speed = 1.3,        -- 1.95 speed
                defense = 1.5,      -- 30 defense
                deep_diving = 2.0,
                size_advantage = 1.4
            },
            appearanceModifiers = {
                scale = 1.4,
                glow = true,
                colorTint = {0.4, 0.4, 0.5, 1.0},  -- Dark leathery appearance
                leathery_shell = true,
                massive_flippers = true,
                deep_sea_adaptation = true
            },
            soundModifiers = {
                pitch = 0.8,
                volume = 1.2,
                deep_resonance = true
            }
        },
        
        rare = {
            name = "Ancient Turtle",
            description = "A centuries-old turtle with incredible wisdom and magical properties",
            statModifiers = {
                maxHealth = 2.0,    -- 200 health
                defense = 2.0,      -- 40 defense
                wisdom = 3.0,       -- 45 wisdom
                longevity = 5.0,    -- 125 longevity
                ancient_magic = 2.0
            },
            appearanceModifiers = {
                scale = 1.3,
                colorTint = {0.7, 0.9, 0.8, 1.0},  -- Moss-covered ancient shell
                moss_covered = true,
                ancient_markings = true,
                wise_eyes = true,
                barnacle_growth = true
            },
            soundModifiers = {
                pitch = 0.7,
                volume = 1.3,
                reverb = true,
                ancient_wisdom = true
            }
        },
        
        legendary = {
            name = "World Turtle",
            description = "A mythical turtle so large it carries islands on its shell",
            statModifiers = {
                maxHealth = 4.0,    -- 400 health
                defense = 3.0,      -- 60 defense
                wisdom = 5.0,       -- 75 wisdom
                world_bearing = 10.0,
                cosmic_connection = 5.0
            },
            appearanceModifiers = {
                scale = 2.0,
                glow = true,
                colorTint = {0.8, 1.0, 0.9, 1.0},  -- Mystical green with world energy
                island_shell = true,
                cosmic_aura = "legendary",
                world_tree = true,
                reality_anchor = true
            },
            soundModifiers = {
                pitch = 0.5,
                volume = 1.8,
                reverb = true,
                echo = true,
                world_resonance = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "turtle_meat", chance = 0.6, quantity = {2, 3}},  -- Lower chance, peaceful creature
        {id = "turtle_shell_fragment", chance = 0.8, quantity = {1, 3}},
        {id = "sea_grass", chance = 0.9, quantity = {2, 4}},
        {id = "wisdom_essence", chance = 0.3, quantity = {1, 1}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "leatherback_shell", chance = 0.9, quantity = {1, 1}},
            {id = "deep_sea_adaptation_gland", chance = 0.7, quantity = {1, 1}},
            {id = "pressure_resistance_organ", chance = 0.6, quantity = {1, 1}},
            {id = "massive_flipper", chance = 0.5, quantity = {1, 2}}
        },
        rare = {
            {id = "ancient_turtle_shell", chance = 0.9, quantity = {1, 1}},
            {id = "centuries_wisdom_crystal", chance = 0.8, quantity = {1, 1}},
            {id = "ancient_magic_essence", chance = 0.7, quantity = {1, 2}},
            {id = "moss_covered_shell_piece", chance = 0.6, quantity = {1, 2}},
            {id = "longevity_elixir", chance = 0.4, quantity = {1, 1}}
        },
        legendary = {
            {id = "world_turtle_shell_fragment", chance = 0.95, quantity = {1, 1}},
            {id = "cosmic_wisdom_orb", chance = 0.9, quantity = {1, 1}},
            {id = "world_bearing_essence", chance = 0.8, quantity = {1, 1}},
            {id = "island_seed", chance = 0.7, quantity = {1, 2}},
            {id = "reality_anchor_crystal", chance = 0.6, quantity = {1, 1}},
            {id = "world_tree_sapling", chance = 0.5, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "sea_turtle",
        scale = 1.1,
        animations = {
            "swim_graceful", "dive", "surface", "rest", "ancient_meditation"
        },
        variants = {
            "green_turtle", "leatherback", "ancient_turtle", "world_turtle"
        }
    },
    
    -- Sound effects with peaceful aquatic characteristics
    sounds = {
        swim_graceful = {
            file = "turtle_swim",
            synth = {
                instrument = "harp",
                notes = {"C3", "E3", "G3"},
                durations = {0.6, 0.5, 0.8},
                volume = 0.2,
                peaceful = true
            }
        },
        surface_breath = {
            file = "turtle_surface",
            synth = {
                instrument = "flute",
                notes = {"A3", "C4"},
                durations = {0.4, 0.6},
                volume = 0.3,
                breathing = true
            }
        },
        ancient_wisdom = {
            file = "ancient_wisdom",
            synth = {
                instrument = "celesta",
                notes = {"F3", "A3", "C4", "F4"},
                durations = {0.8, 0.6, 0.8, 1.0},
                volume = 0.4,
                mystical = true
            }
        },
        world_resonance = {
            file = "world_resonance",
            synth = {
                instrument = "organ",
                notes = {"C2", "G2", "C3", "G3"},
                durations = {1.2, 1.0, 1.2, 1.5},
                volume = 0.6,
                cosmic = true
            }
        }
    },
    
    -- Special peaceful abilities
    abilities = {
        shell_defense = {
            type = "active",
            description = "Retreat into shell for maximum protection",
            effect = "damage_immunity",
            duration = 5,
            cooldown = 20
        },
        ancient_wisdom = {
            type = "passive",
            description = "Provides knowledge of ocean currents and weather",
            effect = "navigation_bonus"
        },
        peaceful_aura = {
            type = "passive",
            description = "Calms aggressive creatures nearby",
            effect = "pacification_aura"
        },
        deep_dive = {
            type = "active",
            description = "Dive to extreme depths to avoid danger",
            effect = "escape_ability",
            cooldown = 30
        },
        longevity_blessing = {
            type = "passive",
            description = "Extremely long lifespan and resistance to aging",
            effect = "time_resistance"
        }
    }
}

-- Initialize the sea turtle entity using enhanced template
function SeaTurtle.init(entity, world)
    -- Copy all fields from SeaTurtle template to entity instance
    for k, v in pairs(SeaTurtle) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return SeaTurtle
