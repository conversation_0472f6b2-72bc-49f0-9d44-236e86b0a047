-- tiles/stone.lua
local StoneTile = {
    id = "stone",
    name = "Stone Ground",
    passable = true,
    variants = 6, -- e.g., Granite Bedrock, Slate Slabs, Limestone Flats, Weathered Stone, Mossy Stone, Veined Stone

    -- Entities that can spawn here (mostly surface life)
    spawns = {"moss_patch", "lichen", "hardy_wildflower", "small_lizard", "stone_beetle", "loose_pebble"},

    -- Movement properties
    movementSpeed = 1.0, -- Solid footing, standard speed

    -- Stone-specific properties
    rockType = "granite", -- Default, changes with variant
    hardness = 4, -- Harder than rocky ground, less than mountain (1-10)
    isNaturallyFlat = false, -- Set true for variants like slabs/flats
    hasVeins = false, -- Mineral veins present? Set by variant/generation

    -- Interaction function
    interact = function(tile, entity)
        -- Examine the stone type
        local description = "A solid expanse of " .. tile.rockType .. " bedrock forms the ground here."
        if tile.isNaturallyFlat then description = "Flat slabs of natural " .. tile.rockType .. " provide a solid surface." end
        
        -- Check for mineral veins (if applicable)
        if tile.hasVeins and entity.hasItem and entity.hasItem("pickaxe") then
             local veinType = tile.veinType or "quartz" -- Default vein type
             return {
                 success = true,
                 message = description .. " You notice veins of " .. veinType .. " embedded in the stone. You could try mining them.",
                 effects = {{type="suggest_action", action="mine_vein"}}
             }
        end

        -- Look for fossils or interesting features
        if math.random() < 0.08 then
             local features = {
                 {id="fossil_imprint", chance=0.5},
                 {id="interesting_crystal_formation", chance=0.3},
                 {id="ancient_carving_faint", chance=0.2}
             }
             for _, feature in ipairs(features) do
                 if math.random() < feature.chance then
                     return {
                         success = true,
                         message = description .. " Examining the stone closely, you spot a " .. feature.id .. ".",
                         effects = {{type="discover_feature", feature=feature.id}}
                     }
                 end
             end
        end

        -- Gather loose pebbles
        if math.random() < 0.2 then
             local amount = math.random(1, 2)
             return {
                 success = true,
                 message = description .. " You gather " .. amount .. " loose pebbles from the surface.",
                 effects = {{type="add_item", item="pebble", quantity=amount}}
             }
        end

        -- General examination
        return {
            success = false,
            message = description
        }
    end,

    -- Weather effects
    weatherEffects = {
        rain = function(tile)
            tile.isWet = true
            tile.slipperiness = 0.4 -- Less slippery than smooth ice, but noticeable
            -- Enhances moss/lichen visually
            return "visual_effect", "wet_stone"
        end,
        
        heat = function(tile)
            tile.isWet = false
            tile.slipperiness = 0
            -- Stone retains heat
            tile.temperatureOffset = 5 -- Warmer than air temp
            return nil
        end,

        sun = function(tile)
            tile.isWet = false
            tile.slipperiness = 0
            tile.temperatureOffset = 0
            return "remove_visual_effect", "wet_stone"
        end,

        freeze = function(tile)
            if tile.isWet or world.humidity > 0.7 then -- Freezing requires moisture
                 tile.isIcy = true
                 tile.slipperiness = 0.7 -- Becomes quite slippery
                 tile.footstepSound = "stone_step_icy"
                 return "visual_effect", "icy_stone_sheen"
            end
            return nil
        end,
        
        snow = function(tile)
            tile.isSnowCovered = true
            tile.slipperiness = 0.3 -- Snow provides some traction over potential ice
             return "visual_effect", "snow_covered_stone"
        end
    },

     -- Time effects
    timeEffects = {
        night = function(tile)
            -- Radiates stored heat slowly
            if world.time.hour < 24 then tile.temperatureOffset = 2 else tile.temperatureOffset = -2 end
            tile.ambientSound = "wind_over_stone_low"
            return nil
        end,
        
        dawn = function(tile)
             tile.temperatureOffset = 0
             tile.ambientSound = "wind_over_stone_gentle"
              -- Reset icy state if temp rises
             if tile.isIcy and world.temperature > 0 then
                  tile.isIcy = false
                  tile.slipperiness = 0
                  tile.footstepSound = "stone_step_solid"
                  return "remove_visual_effect", "icy_stone_sheen"
             end
              -- Reset snow cover if temp rises significantly / melts
             if tile.isSnowCovered and world.temperature > 5 then
                  tile.isSnowCovered = false
                  return "remove_visual_effect", "snow_covered_stone"
             end
            return nil
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {0.6, 0.6, 0.55}, -- Neutral stone grey
        variants = {
            {name = "Granite Bedrock", rockType="granite", color_shift={0.05,0.05,0.05}, texture="granite_solid"},
            {name = "Slate Slabs", rockType="slate", color_shift={-0.1, -0.1, 0}, texture="slate_flat", isNaturallyFlat=true},
            {name = "Limestone Flats", rockType="limestone", color_shift={0.1, 0.1, 0.05}, texture="limestone_flat", isNaturallyFlat=true},
            {name = "Weathered Stone", texture="stone_weathered", cracks=0.3},
            {name = "Mossy Stone", overlay_objects={{name="moss_patch_dense", chance=0.7}}, color_shift={-0.1, 0.05, -0.05}},
            {name = "Veined Stone", hasVeins=true, overlay_texture="mineral_vein_pattern"}
        },
        decoration_objects = {
            {name = "lichen_patch", chance = 0.4},
            {name = "small_crack", chance = 0.3},
            {name = "hardy_plant_crack", chance = 0.15} -- Plant growing in a crack
        },
         special_effects = {
             wet_sheen = {intensity=0.5, when="isWet"},
             heat_shimmer_subtle = {intensity=0.2, when="temperatureOffset > 4"}
         },
          weather_overlays = {
             icy_sheen = {shininess=0.6, when="isIcy"},
             snow_cover_light = {color={0.95, 0.95, 1.0}, coverage=0.5, when="isSnowCovered"}
         }
    },
    
    -- Audio properties
    ambientSound = "wind_over_stone_gentle",
    footstepSound = "stone_step_solid", -- Solid, firm step sound
     specialSounds = {
        {name = "pebble_skitter", trigger = "on_walk", chance = 0.05}, -- Kicking a loose pebble
        {name = "water_drip_echo", trigger = "rainy", chance = 0.1, condition = "near_cave_or_overhang"},
        {name = "stone_chip", trigger = "action_mine", chance = 0.5, condition = "mining_nearby"}
    },

    -- Connections to other tiles
    connects_to = {
        "stone", -- Itself
        "rocky_ground", -- Transition to looser rocks
        "dirt", -- Edge of stone area
        "grass", -- Edge of stone area
        "mountain_base", -- Foot of mountains
        "ruins", -- Often built on stone
        "stone_path", -- Path built over or leading from stone
        "cave_entrance" -- Can have cave entrances
    },

    -- Pathfinding properties
     pathfinding = {
        travel_cost = 0.95, -- Slightly faster/easier than dirt? Or 1.0 for standard.
        provides_good_footing = true,
        slip_hazard_chance = function(tile) return 0.1 * tile.slipperiness end -- Slip chance when wet/icy
    },

    -- Mining properties (Less yield than rocky ground/mountain, focused on veins if present)
    mineable = true, -- Can be mined, but hard
    miningYields = {
        default = { -- Mining the base stone
            {item = "stone_fragment", quantity = {1, 2}, chance = 0.8}, -- Less yield than rocky_ground
            {item = "pebble", quantity = {2, 5}, chance = 1.0}
        },
        pickaxe_vein = { -- Mining a specific vein
             {item = "stone_fragment", quantity = {1, 1}, chance = 0.5},
             {item = "vein_material", quantity = {1, 3}, chance = 0.9}, -- e.g., quartz, iron_ore based on veinType
             {item = "gem_rough", quantity = {0, 1}, chance = 0.05}
        }
    },

    -- Effects on entities that walk on this tile
    onWalk = function(tile, entity)
        local sound = tile.footstepSound or "stone_step_solid"
        if tile.isIcy then sound = "stone_step_icy" end
        local effects = {}
        local fatigue = 0.06 -- Low fatigue cost on solid ground

        -- Check for slipping
        if (tile.isWet or tile.isIcy) and math.random() < (tile.slipperiness * 0.5) then -- Moderate slip chance
            sound = "stone_slip_scrape" -- Different slip sound?
             table.insert(effects, {type="apply_status", status="slipping_minor", duration=1}) -- Minor slip, less impactful than ice
             return {
                 sound = sound,
                 volume = 0.8,
                 message = "You slip momentarily on the " .. (tile.isIcy and "icy" or "wet") .. " stone!",
                 effects = effects
             }
        end

         table.insert(effects, {type="increase_fatigue", amount = fatigue})

        return {
            sound = sound,
            volume = 0.9,
            effects = effects
        }
    end,
    
    -- Effect when discovering stone ground
    onDiscover = function(tile, entity)
        if entity.journal and entity.journal.addDiscovery then
             entity.journal.addDiscovery({
                type = "terrain",
                name = "Stone Ground",
                location = {x = tile.x, y = tile.y},
                notes = "Solid stone bedrock or natural paving. Provides good footing."
            })
        end
        return {
            message = "You find an area where solid stone forms the ground.",
            effects = {
                {type = "reveal_map", radius = 2}
            }
        }
    end
}

function StoneTile.init(world)
    print("Stone Ground tile module initialized")
    -- Register with geology system, etc.
    if world.systems and world.systems.geology then
        world.systems.geology:registerTerrain("stone", {hardness = 4, base_rock_type = "granite"})
    end
     if world.systems and world.systems.pathfinding then
        world.systems.pathfinding:registerTerrainFooting("stone", {cost=0.95, stable=true})
    end
end

return StoneTile