<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Procedural Character Generator</title>
  <style>
    body { background: #222; display: flex; justify-content: center; align-items: center; height: 100vh; }
    canvas { image-rendering: pixelated; border: 2px solid #555; }
  </style>
</head>
<body>
<canvas id="characterCanvas" width="128" height="128"></canvas>
<script>
const canvas = document.getElementById('characterCanvas');
const ctx = canvas.getContext('2d');
const pixelSize = 4;

// Simple palettes
const palettes = {
  skin: ['#F5CBA7', '#D2B48C', '#A0522D'],
  clothes: ['#1E90FF', '#DC143C', '#32CD32', '#FFD700'],
  hair: ['#000000', '#8B4513', '#D2691E', '#FFD700']
};

// Character part templates (simplified examples)
const parts = {
  head: [
    [0,0,1,1,1,1,0,0],
    [0,1,1,1,1,1,1,0],
    [1,1,2,1,1,2,1,1],
    [1,1,1,1,1,1,1,1],
    [0,1,1,3,3,1,1,0],
    [0,0,1,1,1,1,0,0]
  ],
  body: [
    [0,0,4,4,4,4,0,0],
    [0,4,4,4,4,4,4,0],
    [0,4,4,4,4,4,4,0],
    [0,4,4,4,4,4,4,0]
  ],
  legs: [
    [0,4,4,0,0,4,4,0],
    [0,4,4,0,0,4,4,0],
    [0,4,4,0,0,4,4,0],
    [0,4,4,0,0,4,4,0]
  ]
};

function renderPart(template, xOff, yOff, colorMap) {
  template.forEach((row, y) => {
    row.forEach((px, x) => {
      if(px) {
        ctx.fillStyle = colorMap[px];
        ctx.fillRect((x + xOff) * pixelSize, (y + yOff) * pixelSize, pixelSize, pixelSize);
      }
    });
  });
}

function generateCharacter() {
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  const skinColor = palettes.skin[Math.floor(Math.random() * palettes.skin.length)];
  const clothesColor = palettes.clothes[Math.floor(Math.random() * palettes.clothes.length)];
  const hairColor = palettes.hair[Math.floor(Math.random() * palettes.hair.length)];

  const colorMap = {1: skinColor, 2: '#000', 3: hairColor, 4: clothesColor};

  renderPart(parts.head, 4, 2, colorMap);
  renderPart(parts.body, 4, 8, colorMap);
  renderPart(parts.legs, 4, 12, colorMap);
}

generateCharacter();
canvas.onclick = generateCharacter;
</script>
</body>
</html>
