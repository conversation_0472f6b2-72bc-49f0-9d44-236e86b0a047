-- entities/wyvern.lua
-- Wyvern flying entity with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local Wyvern = {
    id = "wyvern",
    name = "Wyvern",
    type = "wyvern",
    shape = {
        {0, -1.5}, {1.2, -1.0}, {2.0, 0}, {1.2, 1.0},
        {0, 1.5}, {-1.2, 1.0}, {-2.0, 0}, {-1.2, -1.0}
    },
    size = 15,

    -- Entity categories
    categories = {"flying", "dragon_kin", "predator", "aggressive"},
    threatCategories = {"player", "large_creatures", "territory_invaders"},
    
    -- Base stats (aggressive flying predator)
    maxHealth = 180,
    health = 180,
    maxStamina = 120,
    stamina = 120,
    speed = 3.5,
    attack = 35,
    defense = 20,
    flight_altitude = 30,
    territorial_range = 50,
    dive_speed = 6.0,
    
    -- Behaviors
    behaviors = {"aerial_hunt", "territorial_patrol", "dive_attack", "roost_guard"},
    behaviorConfigs = {
        aerial_hunt = {
            huntingAltitude = 25,
            preyDetection = 40,
            huntSpeed = 4.0,
            packHunting = false
        },
        territorial_patrol = {
            patrolRadius = 60,
            aggressionRadius = 30,
            chaseDistance = 80,
            neverRetreat = true
        },
        dive_attack = {
            diveSpeed = 6.5,
            diveDamage = 2.5,
            pullUpDistance = 5,
            stunChance = 0.4
        },
        roost_guard = {
            roostRadius = 20,
            guardBonus = 1.8,
            alertness = 2.0
        }
    },
    
    -- Enhanced variant system for wyverns
    variantChances = {
        normal = 0.65,          -- 65% forest wyvern
        shiny = 0.25,           -- 25% frost wyvern (shiny)
        rare = 0.08,            -- 8% shadow wyvern (rare)
        legendary = 0.02        -- 2% ancient wyvern (legendary)
    },
    
    variants = {
        normal = {
            name = "Forest Wyvern",
            description = "A territorial wyvern that hunts from forest canopies",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.6, 0.8, 0.5, 1.0}  -- Forest green
            }
        },
        
        shiny = {
            name = "Frost Wyvern",
            description = "An ice-breathing wyvern from the frozen peaks",
            statModifiers = {
                maxHealth = 1.5,    -- 270 health
                attack = 1.4,       -- 49 attack
                defense = 1.3,      -- 26 defense
                speed = 1.2,        -- 4.2 speed
                ice_breath = 2.0,
                cold_immunity = 5.0
            },
            appearanceModifiers = {
                scale = 1.2,
                glow = true,
                colorTint = {0.7, 0.9, 1.3, 1.0},  -- Icy blue-white
                frost_breath = true,
                ice_crystals = true,
                frozen_aura = true
            },
            soundModifiers = {
                pitch = 0.9,
                volume = 1.3,
                crystalline = true
            }
        },
        
        rare = {
            name = "Shadow Wyvern",
            description = "A dark wyvern that hunts from the shadows",
            statModifiers = {
                maxHealth = 1.7,    -- 306 health
                attack = 1.6,       -- 56 attack
                speed = 1.4,        -- 4.9 speed
                stealth = 3.0,
                shadow_magic = 2.0,
                night_vision = 5.0
            },
            appearanceModifiers = {
                scale = 1.15,
                colorTint = {0.3, 0.2, 0.4, 1.0},  -- Dark purple-black
                shadow_aura = true,
                darkness_trail = true,
                glowing_eyes = true,
                void_wings = true
            },
            soundModifiers = {
                pitch = 0.8,
                volume = 1.2,
                reverb = true,
                ominous = true
            }
        },
        
        legendary = {
            name = "Ancient Wyvern",
            description = "A primordial wyvern with centuries of experience",
            statModifiers = {
                maxHealth = 2.5,    -- 450 health
                attack = 2.0,       -- 70 attack
                defense = 1.8,      -- 36 defense
                speed = 1.3,        -- 4.55 speed
                ancient_wisdom = 5.0,
                elemental_mastery = 3.0,
                territorial_dominance = 10.0
            },
            appearanceModifiers = {
                scale = 1.6,
                glow = true,
                colorTint = {1.2, 1.0, 0.6, 1.0},  -- Ancient gold-bronze
                ancient_scars = true,
                weathered_scales = true,
                primordial_aura = "legendary",
                time_distortion = true
            },
            soundModifiers = {
                pitch = 0.6,
                volume = 1.6,
                reverb = true,
                echo = true,
                ancient_roar = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "wyvern_scale", chance = 0.9, quantity = {3, 6}},
        {id = "wyvern_claw", chance = 0.8, quantity = {2, 4}},
        {id = "wing_membrane", chance = 0.7, quantity = {1, 2}},
        {id = "predator_essence", chance = 0.6, quantity = {1, 2}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "frost_wyvern_scale", chance = 0.9, quantity = {3, 5}},
            {id = "ice_breath_gland", chance = 0.8, quantity = {1, 1}},
            {id = "frozen_wing_crystal", chance = 0.7, quantity = {1, 2}},
            {id = "eternal_ice_core", chance = 0.6, quantity = {1, 1}}
        },
        rare = {
            {id = "shadow_wyvern_scale", chance = 0.9, quantity = {3, 5}},
            {id = "void_wing_membrane", chance = 0.8, quantity = {1, 1}},
            {id = "shadow_essence", chance = 0.8, quantity = {2, 3}},
            {id = "darkness_crystal", chance = 0.6, quantity = {1, 1}}
        },
        legendary = {
            {id = "ancient_wyvern_scale", chance = 0.95, quantity = {4, 6}},
            {id = "primordial_claw", chance = 0.9, quantity = {1, 2}},
            {id = "ancient_wisdom_crystal", chance = 0.8, quantity = {1, 1}},
            {id = "territorial_dominance_orb", chance = 0.7, quantity = {1, 1}},
            {id = "time_weathered_fang", chance = 0.6, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "wyvern",
        scale = 1.4,
        animations = {
            "soar", "dive_attack", "roar", "breath_weapon", "territorial_display"
        },
        variants = {
            "forest_wyvern", "frost_wyvern", "shadow_wyvern", "ancient_wyvern"
        }
    },
    
    -- Sound effects with aggressive flying characteristics
    sounds = {
        wyvern_roar = {
            file = "wyvern_roar",
            synth = {
                instrument = "brass",
                notes = {"D2", "G2", "D3"},
                durations = {0.8, 0.6, 1.0},
                volume = 0.9,
                aggressive = true
            }
        },
        wing_beat = {
            file = "wing_beat",
            synth = {
                instrument = "percussion",
                notes = {"C2", "C2"},
                durations = {0.2, 0.2},
                volume = 0.6,
                powerful = true
            }
        },
        dive_screech = {
            file = "dive_screech",
            synth = {
                instrument = "flute",
                notes = {"A4", "F4", "D4"},
                durations = {0.3, 0.2, 0.4},
                volume = 0.8,
                piercing = true
            }
        },
        frost_breath = {
            file = "frost_breath",
            synth = {
                instrument = "synthesizer",
                notes = {"C3", "E3", "G3"},
                durations = {0.6, 0.5, 0.8},
                volume = 0.7,
                icy = true
            }
        }
    },
    
    -- Special wyvern abilities
    abilities = {
        aerial_superiority = {
            type = "passive",
            description = "Gains bonuses when fighting airborne enemies",
            effect = "flight_combat_bonus"
        },
        dive_bomb = {
            type = "active",
            description = "Devastating diving attack with stun chance",
            effect = "dive_attack",
            cooldown = 12
        },
        territorial_rage = {
            type = "passive",
            description = "Becomes more powerful when defending territory",
            effect = "territorial_bonus"
        },
        breath_weapon = {
            type = "active",
            description = "Elemental breath attack based on variant",
            effect = "breath_attack",
            cooldown = 15
        },
        keen_sight = {
            type = "passive",
            description = "Exceptional vision for spotting prey from great distances",
            effect = "enhanced_detection"
        }
    }
}

-- Initialize the wyvern entity using enhanced template
function Wyvern.init(entity, world)
    -- Copy all fields from Wyvern template to entity instance
    for k, v in pairs(Wyvern) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return Wyvern
