-- classes/sorcerer.lua
-- Sorcerer class definition for Lazarus Arc

local Sorcerer = {
    name = "Sorcerer",
    description = "A wielder of innate, chaotic magic, drawing power from within rather than extensive study.",
    icon = "sorcerer_icon.png",

    -- 🎭 **Playstyle & Trade-offs**
    pros = {
        "High burst magic damage",
        "Stronger elemental specialization",
        "Faster casting speed for select spells",
        "Potential for unpredictable, powerful effects"
    },
    cons = {
        "Limited versatility in spell selection",
        "Fragile with weak physical defense",
        "Mana management can be challenging",
        "Chaotic magic may produce unintended side effects"
    },

    -- 🏋 **Base Stats & Growth**
    base_stats = { 
        health = 82, maxHealth = 82,
        stamina = 45, maxStamina = 45,
        mana = 115, maxMana = 115,
        pattack = 3, pdefense = 4,
        mattack = 17, mdefense = 9,
        speed = 5, luck = 8
    },

    stat_growth = {
        health = 6.5, stamina = 4,
        mana = 11.5, pattack = 0.2,
        pdefense = 0.4, mattack = 2.3,
        mdefense = 1.1, speed = 0.4,
        luck = 0.7
    },

    -- ⚔️ **Weapon Proficiency**
    proficiencies = { 
        staff = 2.3,
        wand = 2.4,
        dagger = 1.1,
        magic = 2.8
    },

    -- 🌿 **Elemental Affinities**
    element_affinities = {
        fire = 12,
        air = 8,
        dark = 9,
        earth = 3,
        ice = 6
    },

    -- 💥 **Overdrive Abilities**
    overdrive = { 
        "elemental_unleashed",
        "chaotic_surge",
        "inner_flame"
    },

    -- 🎯 **Starting Skills**
    starting_skills = {
        "ember_strike",
        "arcane_dart",
        "instinctive_shield"
    },

    -- 📈 **Skill Progression**
    skill_tree = {
        tier1 = {
            "searing_touch",
            "lightning_jolt",
            "shadow_tendril"
        },
        tier2 = {
            "firestorm",
            "gale_force",
            "void_step",
            "empower_magic"
        },
        tier3 = {
            "inferno_wave",
            "maelstrom",
            "soul_drain"
        }
    },

    -- 🎒 **Inventory & Carrying Capacity**
    inventory_limit = 85,

    -- ✨ **Special Abilities (Passive Perks)**
    special_abilities = {
        "Innate Font",
        "Elemental Attunement",
        "Chaotic Chance",
        "Arcane Resilience",
        "Volatile Power"
    },

    -- 🎭 **Prestige Paths**
    prestige_paths = {
        elemental_fury = {
            description = "Fully embraces one element, becoming a devastating conduit for its power.",
            bonuses = {
                "Massive boost for chosen element",
                "Element-specific ultimate abilities",
                "Resistance to chosen element"
            },
            weaknesses = {
                "Weaker with other elements",
                "Vulnerable to opposing elements"
            }
        },

        shadow_weaver = {
            description = "Uses shadow and void magic, trading safety for raw power.",
            bonuses = {
                "Enhanced dark magic",
                "Potent debuffs and life-drain",
                "Shadow-based mobility"
            },
            weaknesses = {
                "Lower defense",
                "Vulnerable to holy magic",
                "Risk of attracting dangerous entities"
            }
        },

        dragon_soul = {
            description = "Manifests draconic traits, blending powerful magic with resilience.",
            bonuses = {
                "Draconic spells and abilities",
                "Increased health and magic defense",
                "Enhanced resistance to fire and fear"
            },
            weaknesses = {
                "Slower casting for draconic abilities",
                "Less elemental specialization"
            }
        }
    },

    -- ⚔️ **Hybrid & Multiclass Options**
    hybrid_options = {
        spellblade = {
            description = "Channels innate magic through weapons, blending spellcasting and melee.",
            pros = {
                "Elemental and chaotic weapon buffs",
                "Better survivability",
                "Versatile combat"
            },
            cons = {
                "Reduced spellcasting power",
                "Stamina and mana balancing required",
                "Fewer high-tier Sorcerer spells"
            }
        },

        chaos_caller = {
            description = "Summons unstable entities and elementals, enhancing battlefield control.",
            pros = {
                "Temporary powerful summons",
                "Enhanced elemental affinity for summons",
                "Adds control options"
            },
            cons = {
                "Unstable or short-lived summons",
                "Reduced direct damage",
                "Requires managing summons with spellcasting"
            }
        }
    }
}

return Sorcerer
