-- biomes/robot_exchange.lua
-- Declare RobotExchangeBiome at the top level so it can be referenced from within functions
local RobotExchangeBiome = {}

-- Define the biome properties
RobotExchangeBiome = {
    id = "robot_exchange",
    name = "Robot Exchange",
    description = "A hyper-efficient hub of commerce and logistics, operated by robots adhering to strict transaction protocols. Goods, data, and contracts are exchanged under constant monitoring.",

    -- Environmental factors (Controlled, busy, data-rich)
    environment = {
        humidity = 0.5,    -- Climate controlled
        temperature = 0.6, -- Comfortable/optimal for machines
        sunlight = 0.7,    -- Bright, often artificial lighting
        windStrength = 0.1, -- Internal circulation only
        airQuality = 0.9,  -- Filtered
        noiseLevel = 0.6,  -- Busy but not industrial: whirring, terminals, transport
        dataTraffic = 0.8  -- High network activity (0-1)
    },

    -- Core tile types used in this biome
    -- NOTE: Needs 'exchange_floor', 'trade_terminal', 'goods_conveyor', 'automated_stall', 'security_scanner_tile', 'data_port'. Uses 'metal_floor'.
    primaryTiles = {"exchange_floor", "metal_floor"}, -- Main thoroughfares
    secondaryTiles = {"goods_conveyor", "trade_terminal", "automated_stall", "maintenance_path"},
    rareTiles = {"security_scanner_tile", "data_port", "central_ledger_node", "customs_checkpoint"},

    -- Default proportions (Structured layout)
    tileDistribution = {
        exchange_floor = 0.5,
        metal_floor = 0.15, -- Walkways/less busy areas
        goods_conveyor = 0.1,
        trade_terminal = 0.05,
        automated_stall = 0.05,
        maintenance_path = 0.05,
        building_wall_metal = 0.05, -- Walls separating sections
        security_scanner_tile = 0.02,
        data_port = 0.02,
        central_ledger_node = 0.005,
        customs_checkpoint = 0.005
    },

    -- Entities common to this biome
    commonEntities = {
        "merchant_robot", "cargo_lifter_robot", "customer_service_bot", "goods_pallet_entity" -- Represents goods
    },
    uncommonEntities = {
        "broker_bot", "customs_robot", "security_bot_market", "data_packet_entity" -- Represent data trade
    },
    rareEntities = {
        "exchange_administrator_robot", "high_value_cargo_entity", "corporate_spy_robot", "auditor_robot" -- corporate_spy_robot is illicit
    },

    -- Specific biome properties
    isCommercial = true,
    isAutomated = true,
    controllingFaction = "automaton_collective", -- Or specific Trade Guild
    marketType = "Mixed Goods", -- Mixed Goods, Data, Futures, Raw Materials
    economicLevel = 0.8, -- High level of economic activity
    securityLevel = 0.7, -- High security presence
    tradeProtocols = { -- Rules checked during interactions
         valid_registration = { consequence="access_denial", escalation="escort_out" },
         contraband_detected = { consequence="confiscation_and_fine", escalation="detainment" },
         transaction_fraud = { consequence="asset_freeze_and_ban", escalation="permanent_ban_and_alert" },
         unauthorized_data_access = { consequence="network_ban", escalation="security_response_critical" },
         loitering_inefficiently = { consequence="move_along_order", escalation="escort_out" }
    },

    -- Biome variants for diverse generation (Sections of the Exchange)
    variants = {
        goods_depot = {
            name = "Goods Depot",
            environment = { noiseLevel=0.7 },
            tileAdjustments = { exchange_floor=0.3, goods_conveyor=0.3, storage_unit_tile=0.2, metal_floor=0.1 }, -- Need storage tile
            entityAdjustments = { cargo_lifter_robot=1.8, merchant_robot=0.5, security_bot_market=1.2 },
            specialEffects = {"moving_cargo_visuals", "inventory_scanner_beams"}
        },
        data_bazaar = {
            name = "Data Bazaar",
            environment = { noiseLevel=0.4, dataTraffic=1.0 },
            tileAdjustments = { exchange_floor=0.4, trade_terminal=0.2, data_port=0.2, secure_booth_tile=0.1 }, -- Need booth tile
            entityAdjustments = { broker_bot=1.5, data_courier_bot=1.3, corporate_spy_robot=1.0 },
            specialEffects = {"data_stream_visuals", "high_network_activity", "encrypted_terminals"}
        },
        customs_entry = {
            name = "Customs & Entry Point",
            securityLevel = 0.9,
            tileAdjustments = { exchange_floor=0.3, security_scanner_tile=0.3, customs_checkpoint=0.2, holding_cell_tile=0.05 }, -- Need holding cell
            entityAdjustments = { customs_robot=2.0, police_robot=1.5, organic_visitor=0.8 },
            specialEffects = {"scanning_beams", "automated_voice_warnings", "high_alert_status"}
        },
        black_market_access = { -- Hidden/rare variant
             name = "Black Market Access Point",
             description = "A hidden, unregulated corner operating outside the main exchange protocols.",
             environment = { noiseLevel=0.3, dataTraffic=0.5, securityLevel=0.1}, -- Lower security presence, maybe interference?
             tileAdjustments = { alley=0.3, ruins=0.2, hidden_terminal_tile=0.2, scrap_pile=0.1 }, -- Need hidden terminal
             entityAdjustments = { rogue_robot=1.3, black_market_dealer_bot=1.0, smuggler_organic=0.8 },
             specialEffects = {"flickering_lights", "jamming_field", "illicit_goods_visuals"}
        }
    },

    -- Structures (Key areas or facilities within the Exchange)
    structures = {
        { name = "Main Trading Floor", chance = 0.5 },
        { name = "Brokerage Hub", chance = 0.15, requiresVariant="data_bazaar" },
        { name = "Secure Vault Entrance", chance = 0.05 },
        { name = "Central Ledger Facility", chance = 0.03, requiresVariant="logic_core" }, -- Connects to other robot biome?
        { name = "Shipping/Receiving Bay", chance = 0.2, requiresVariant="goods_depot" },
        { name = "Arbitration Chamber", chance = 0.08 } -- Where disputes are settled by logic?
    },

    -- Environmental effects (No weather indoors)
    environmentalEffects = {
        market_activity_level = 0.8, -- High traffic/transactions
        network_latency = 0.1, -- Low latency usually
        security_scan_pulse = 0.3, -- Periodic scans
        emergency_lockdown_protocol = 0.01 -- Rare event chance
    },

    -- Unique features generation
    features = {
        { name = "Automated Kiosk Network", chance = 0.4 }, -- Places info/service kiosks
        { name = "Cargo Conveyor Nexus", chance = 0.2 }, -- Major intersection of conveyors
        { name = "Public Contract Board", chance = 0.25 }, -- Entity displaying available tasks/trades
        { name = "Reputation Monitoring Station", chance = 0.15 }, -- Terminal to check standing
        { name = "High-Security Storage Unit", chance = 0.1 }, -- Locked feature with valuable goods
        { name = "Smuggler's Hidden Cache", chance = 0.05, requiresVariant="black_market_access" } -- Hidden loot
    },

    -- Generation algorithm (Placeholder - Needs structured layout logic)
    generate = function(chunk, world, variant)
        print("Generating " .. (variant and RobotExchangeBiome.variants[variant].name or "Robot Exchange") .. " biome")
        local variantData = variant and RobotExchangeBiome.variants[variant] or nil

        -- Adjust tile distribution
        -- ... (similar logic) ...

        -- Noise/Grid based generation
        local seed = world.seed + (chunk.x * 401 + chunk.y * 907)
        math.randomseed(seed)
        local function pnoise2D(x, y, frequency, seed_offset) return love.math.noise(x * frequency, y * frequency, (world.seed + (seed_offset or 0)) * 0.01) end

        -- Initialize 2D tiles array if not exists
        chunk.tiles = chunk.tiles or {}
        for x = 0, world.CHUNK_SIZE - 1 do
            chunk.tiles[x] = chunk.tiles[x] or {}
        end

        -- Generate tiles
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                local worldX = chunk.x * world.CHUNK_SIZE + x
                local worldY = chunk.y * world.CHUNK_SIZE + y

                local layoutNoise = pnoise2D(worldX, worldY, 0.1, 1) -- Defines main areas vs secondary
                local featureNoise = pnoise2D(worldX, worldY, 0.25, 2) -- Placement of terminals, conveyors

                local tileType = "exchange_floor" -- Default

                if layoutNoise < -0.4 then
                    tileType = "maintenance_path"
                elseif layoutNoise > 0.6 then -- Walls for separation
                     tileType = "building_wall_metal"
                end

                if tileType == "exchange_floor" then
                    if featureNoise > 0.7 then tileType = "trade_terminal"
                    elseif featureNoise > 0.5 then tileType = "automated_stall"
                    elseif featureNoise < -0.6 then tileType = "goods_conveyor"
                    elseif math.abs(featureNoise) < 0.1 then tileType = "security_scanner_tile" -- Along pathways?
                    end
                end

                -- Apply variant specifics (e.g., more data ports in bazaar)
                if variant == "data_bazaar" and featureNoise > 0.6 then
                    if math.random() < 0.4 then tileType = "data_port" end
                end

                -- Create the tile
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = math.random(1, 2),
                    isCommercial = true,
                    isAutomated = true,
                    passable = (tileType ~= "building_wall_metal" and tileType ~= "trade_terminal" and tileType ~= "automated_stall"),
                    isUnderground = false
                }
                if tileType == "goods_conveyor" then tile.moveDirection = math.random(0,3) end -- 0=N, 1=E, 2=S, 3=W

                -- Add to chunk using 2D indexing
                chunk.tiles[x][y] = tile
            end
        end

        -- Call helper functions
        RobotExchangeBiome.generateFeatures(chunk, world, variant)
        RobotExchangeBiome.populateEntities(chunk, world, variant)
        RobotExchangeBiome.generateStructures(chunk, world, variant)
        RobotExchangeBiome.postProcess(chunk, world, variant)
    end,

    -- Helper function implementations (Placeholders)
    generateFeatures = function(chunk, world, variant)
        print("Generating Exchange features...")
        -- Place conveyor networks, terminal banks, security gates, contract boards etc.
    end,

    populateEntities = function(chunk, world, variant)
        print("Populating Exchange entities...")
        local variantData = variant and RobotExchangeBiome.variants[variant] or nil
        local function spawnEntity(entityType, count)
            local multiplier = 1.0
            if variantData and variantData.entityAdjustments and variantData.entityAdjustments[entityType] then 
                multiplier = variantData.entityAdjustments[entityType] 
            end
            local adjustedCount = math.floor(count * multiplier + 0.5)
            for i = 1, adjustedCount do
                local attempts = 0; local placed = false
                while attempts < 5 and not placed do
                    local x = math.random(0, world.CHUNK_SIZE - 1)
                    local y = math.random(0, world.CHUNK_SIZE - 1)
                    local tile = chunk.tiles[x][y]
                    if tile and tile.passable then
                        local props = { factionID = RobotExchangeBiome.factionID }
                        world.entitySystem:addEntity(entityType, chunk.x * world.CHUNK_SIZE + x, chunk.y * world.CHUNK_SIZE + y, props)
                        placed = true
                    end
                    attempts = attempts + 1
                end
            end
        end
        -- Spawn based on lists
        for _, et in ipairs(RobotExchangeBiome.commonEntities) do spawnEntity(et, math.random(6,15)) end
        for _, et in ipairs(RobotExchangeBiome.uncommonEntities) do spawnEntity(et, math.random(3,8)) end
        for _, et in ipairs(RobotExchangeBiome.rareEntities) do if math.random() < 0.08 then spawnEntity(et, 1) end end
    end,

    generateStructures = function(chunk, world, variant)
        print("Generating Exchange structures (key areas)...")
        for _, structure in ipairs(RobotExchangeBiome.structures) do
            if math.random() < structure.chance then
                if not structure.requiresVariant or structure.requiresVariant == variant then
                    local attempts = 0; local placed = false; local sx, sy
                    while attempts < 10 and not placed do
                        sx = math.random(2, world.CHUNK_SIZE - 3)
                        sy = math.random(2, world.CHUNK_SIZE - 3)
                        local tile = chunk.tiles[sx][sy]
                        if tile and tile.passable and tile.type == "exchange_floor" then placed = true end
                        attempts = attempts + 1
                    end
                    if placed then
                        local worldX = chunk.x * world.CHUNK_SIZE + sx
                        local worldY = chunk.y * world.CHUNK_SIZE + sy
                        print("Marking structure area: "..structure.name.." at "..worldX..","..worldY)
                        world.entitySystem:addEntity("structure_poi", worldX, worldY, { 
                            structureType = structure.name, 
                            description="The "..structure.name.." is centered here." 
                        })
                    end
                end
            end
        end
    end,

    postProcess = function(chunk, world, variant)
        print("Post-processing Exchange chunk...")
        -- Apply security levels, network traffic effects, link terminals?
        local variantData = variant and RobotExchangeBiome.variants[variant] or nil
        chunk.environmentalEffects = chunk.environmentalEffects or {}
        table.insert(chunk.environmentalEffects, {type="high_security_zone", level=RobotExchangeBiome.securityLevel})
        table.insert(chunk.environmentalEffects, {type="market_network_active"})

        if variantData and variantData.specialEffects then
             for _, effect in ipairs(variantData.specialEffects) do table.insert(chunk.environmentalEffects, effect) end
         end
    end,

    init = function(worldCore)
        print("Robot Exchange biome module initialized")
        RobotExchangeBiome.worldCore = worldCore
        print("Robot Exchange biome registered successfully for later use")
    end,

    registerWithWorld = function(world)
        print("Registering Robot Exchange biome generators with world")
        if not world or not world.chunkSystem then print("Warning: Cannot register Robot Exchange biome - world or chunkSystem is nil") return false end
        world.chunkSystem:registerGenerator(RobotExchangeBiome.id, RobotExchangeBiome.generate)
        for variantId, variantData in pairs(RobotExchangeBiome.variants) do
            local fullVariantId = RobotExchangeBiome.id .. "_" .. variantId
            world.chunkSystem:registerGenerator(fullVariantId, function(chunk, worldInstance)
                RobotExchangeBiome.generate(chunk, worldInstance, variantId)
            end)
        end
        return true
    end,
}

return RobotExchangeBiome