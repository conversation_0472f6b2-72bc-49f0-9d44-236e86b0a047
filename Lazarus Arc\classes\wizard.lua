-- classes/wizard.lua
-- Wizard class definition for Lazarus Arc

local Wizard = {
    name = "Wizard",
    description = "A master of arcane knowledge, commanding powerful spells but requiring careful mana management.",
    icon = "wizard_icon.png",

    -- 🎭 **Playstyle & Trade-offs**
    pros = {
        "Highest raw magic damage of any class",
        "Extremely versatile spell selection",
        "Can manipulate the battlefield with teleportation and barriers"
    },
    cons = {
        "Extremely fragile—low defense and health",
        "Highly dependent on mana",
        "Slow casting speed compared to hybrid casters"
    },

    -- 🏋 **Base Stats & Growth**
    base_stats = { 
        health = 80, 
        maxHealth = 80,
        stamina = 40, 
        maxStamina = 40,
        mana = 120, 
        maxMana = 120,
        pattack = 3, 
        pdefense = 3, 
        mattack = 18, 
        mdefense = 10, 
        speed = 4, 
        luck = 7 
    },

    stat_growth = {
        health = 6,
        stamina = 3,
        mana = 12,
        pattack = 0.2,
        pdefense = 0.3,
        mattack = 2.5,
        mdefense = 1.2,
        speed = 0.3,
        luck = 0.5
    },

    -- ⚔️ **Weapon Proficiency**
    proficiencies = { 
        staff = 2.5,  -- The traditional Wizard weapon
        wand = 2.3,  
        dagger = 1.2,  
        sword = 0.7,  
        greatsword = 0.3,  
        bow = 0.4,  
        gun = 0.3,  
        magic = 3.0  -- Pure magic mastery
    },

    -- 🌿 **Elemental Affinities**
    element_affinities = {
        fire = 10,    -- Mastery over flames
        ice = 10,     -- Deep understanding of frost magic
        air = 10,     -- Control over wind and lightning
        earth = 5,    -- Some knowledge of nature magic
        dark = 5,     -- Capable of using void magic if trained
        light = 5,    -- Can wield divine magic in rare cases
    },

    -- 💥 **Overdrive Abilities**
    overdrive = { 
        "arcane_cataclysm", -- Unleashes a devastating multi-element explosion
        "mana_torrent", -- Restores a portion of mana and boosts spellcasting speed
        "time_stop", -- Freezes all enemies in place for a short duration
    },

    -- 🎯 **Starting Skills**
    starting_skills = {
        "fireball",
        "arcane_missile",
        "mana_shield"
    },

    -- 📈 **Skill Progression**
    skill_tree = {
        tier1 = {
            "ice_spike",
            "chain_lightning",
            "arcane_bolt"
        },
        tier2 = {
            "blizzard",
            "meteor_strike", 
            "mana_surge",
            "arcane_reflection"
        },
        tier3 = {
            "dimensional_rift",
            "cosmic_flare",
            "astral_projection"
        }
    },

    -- 🎒 **Inventory & Carrying Capacity**
    inventory_limit = 80, -- Lighter due to reliance on magical items

    -- ✨ **Special Abilities (Passive Perks)**
    special_abilities = {
        "Arcane Mastery", -- All spells have a slight power increase
        "Mana Conservation", -- Reduces mana cost of non-damaging spells
        "Ethereal Mind", -- Can detect hidden magical traps or illusions
        "Spellweaver", -- Casting multiple spells in succession increases potency
        "Dimensional Awareness", -- Slightly reduces cooldown on teleportation-based spells
    },

    -- 🎭 **Prestige Paths**
    prestige_paths = {
        archmage = {
            description = "A true master of all forms of magic, pushing the limits of spellcraft.",
            bonuses = {
                "Access to unique, ultra-powerful spells",
                "Increased mana regeneration",
                "Can cast two spells at once under certain conditions"
            },
            weaknesses = {
                "Extremely mana-dependent",
                "Weak to silence and anti-magic effects"
            }
        },

        warlock = {
            description = "A Wizard who taps into forbidden magic, gaining immense power at a cost.",
            bonuses = {
                "Void and dark magic become stronger",
                "Life-draining abilities restore mana",
                "Can summon magical minions to aid in combat"
            },
            weaknesses = {
                "Can suffer corruption effects",
                "Weaker against divine and light-based enemies"
            }
        },

        chronomancer = {
            description = "A Wizard who bends time itself, altering the flow of battle.",
            bonuses = {
                "Can slow or accelerate time",
                "Improved teleportation abilities",
                "Spells have a chance to trigger twice"
            },
            weaknesses = {
                "Highly specialized—less effective in raw damage output",
                "Limited ability to sustain fights without mana"
            }
        }
    },

    -- ⚔️ **Hybrid & Multiclass Options**
    hybrid_options = {
        battle_mage = {
            description = "A Wizard who enhances physical weapons with magical force.",
            pros = {
                "Can wield melee weapons infused with magic",
                "Less reliant on mana for dealing damage"
            },
            cons = {
                "Loses access to some higher-level spells",
                "Melee weapons drain mana when used for enhanced strikes"
            }
        },

        arcane_gunslinger = {
            description = "A Wizard who fuses magic and ranged combat, using enchanted bullets and wands.",
            pros = {
                "Can fire magically enhanced projectiles",
                "Greater speed and mobility"
            },
            cons = {
                "Less control over battlefield-wide effects",
                "Requires proper ammo management"
            }
        }
    }
}

return Wizard
