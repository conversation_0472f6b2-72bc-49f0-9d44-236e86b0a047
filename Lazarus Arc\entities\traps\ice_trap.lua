local IceTrap = {
    id = "ice_trap",
    name = "Ice Trap",
    type = "trap",
    
    -- Entity categories
    categories = {"trap", "environmental", "arctic", "ice"},
    
    -- Target categories
    targetCategories = {"player", "animal", "monster"},
    
    -- Stats
    maxHealth = 30,
    health = 30,
    duration = 15,
    cooldown = 30,
    
    -- Trap properties
    properties = {
        freezeDuration = 5,
        slowAmount = 0.5,
        damagePerSecond = 2,
        radius = 3,
        setupTime = 3,
        isArmed = false,
        isTriggered = false
    },
    
    -- Appearance
    appearance = {
        sprite = "ice_trap",
        scale = 1.0,
        animations = {
            "idle", "trigger", "freeze"
        },
        variants = {
            "clear", "frosted", "crystal"
        }
    },
    
    -- Sound effects
    sounds = {
        trigger = "ice_trap_trigger",
        freeze = "ice_trap_freeze",
        break = "ice_trap_break"
    },
    
    -- Effects
    effects = {
        freeze = {
            type = "status",
            duration = 5,
            effects = {
                slow = 0.5,
                damage = 2,
                interval = 1
            }
        }
    }
}

-- Initialize the trap
function IceTrap.init(entity, world)
    -- Copy all fields from IceTrap template to entity instance
    for k, v in pairs(IceTrap) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position
    entity.position = entity.position or {x = 0, y = 0}

    -- Set random variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize trap state
    entity.properties.isArmed = false
    entity.properties.isTriggered = false
    entity.properties.triggerTime = 0

    return entity
end

-- Update the trap
function IceTrap.update(entity, world, dt)
    -- Check if trap is armed and not triggered
    if entity.properties.isArmed and not entity.properties.isTriggered then
        -- Check for targets in radius
        if world and world.entities then
            for _, target in ipairs(world.entities) do
                if target.position and target.categories then
                    -- Check if target is in trap categories
                    local isTarget = false
                    for _, category in ipairs(entity.targetCategories) do
                        if table.contains(target.categories, category) then
                            isTarget = true
                            break
                        end
                    end
                    
                    if isTarget then
                        local distance = math.sqrt(
                            (target.position.x - entity.position.x)^2 + 
                            (target.position.y - entity.position.y)^2
                        )
                        
                        if distance <= entity.properties.radius then
                            -- Trigger trap
                            entity.properties.isTriggered = true
                            entity.properties.triggerTime = world.time
                            
                            -- Apply freeze effect to target
                            if target.applyEffect then
                                target.applyEffect(entity.effects.freeze)
                            end
                            
                            -- Play trigger sound
                            if world.playSound then
                                world.playSound(entity.sounds.trigger)
                            end
                            
                            break
                        end
                    end
                end
            end
        end
    end
    
    -- Check if trap should be destroyed
    if entity.properties.isTriggered and world.time - entity.properties.triggerTime >= entity.duration then
        if world.removeEntity then
            world.removeEntity(entity)
        end
    end
    
    -- Check if trap can be rearmed
    if not entity.properties.isArmed and world.time - entity.properties.triggerTime >= entity.cooldown then
        entity.properties.isArmed = true
        entity.properties.isTriggered = false
    end
end

-- Arm the trap
function IceTrap.arm(entity, world)
    if not entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = true
        entity.properties.triggerTime = world.time
        return true
    end
    return false
end

-- Disarm the trap
function IceTrap.disarm(entity, world)
    if entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = false
        entity.properties.triggerTime = world.time
        return true
    end
    return false
end

return IceTrap 