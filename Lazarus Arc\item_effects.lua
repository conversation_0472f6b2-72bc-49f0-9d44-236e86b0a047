-- item_effects.lua
-- Handles applying effects when using items

local ItemDatabase = require("item_database")

local ItemEffects = {}

-- Effect functions
local handlers = {}

handlers.restore_hp = function(item, target)
    local amt = item.amount or 10
    if target.heal then target.heal(amt) end
    return {
        message = "Restored " .. amt .. " HP.",
        success = true,
        consumed = true
    }
end

handlers.restore_mana = function(item, target)
    local amt = item.restore_amount or item.amount or 10
    if target.stats and target.stats.mana and target.stats.maxMana then
        target.stats.mana = math.min(target.stats.maxMana, target.stats.mana + amt)
    end
    return {
        message = "Restored " .. amt .. " mana.",
        success = true,
        consumed = true
    }
end

handlers.learn_spell = function(item, target)
    local spell = item.spell_id
    if not target or not target.discoverSpell then
        return { message = "Target cannot learn spells.", success = false, consumed = false }
    end

    local learned = target.discoverSpell(spell)
    return {
        message = learned and ("You learned a new spell: " .. spell) or "You already know this spell.",
        success = true,
        consumed = learned
    }
end

-- Main useItem logic
function ItemEffects.useItem(item, target)
    if not item or not item.usable then
        return { success = false, message = "Item is not usable.", consumed = false }
    end

    local effect = item.effect
    if effect and handlers[effect] then
        return handlers[effect](item, target)
    end

    return {
        message = "You used " .. (item.name or item.baseId or "an item") .. ".",
        success = true,
        consumed = item.consumable or false
    }
end

-- Optional: register new effect
function ItemEffects.registerEffect(effectName, func)
    handlers[effectName] = func
end

return ItemEffects
