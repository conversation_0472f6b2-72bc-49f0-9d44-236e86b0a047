-- entities/crystal_golem.lua
-- Crystal golem with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local CrystalGolem = {
    id = "crystal_golem",
    name = "Crystal Golem",
    type = "crystal_golem",
    shape = {
        {0, -10}, {7, -7}, {10, 0}, {7, 7},
        {0, 10}, {-7, 7}, {-10, 0}, {-7, -7}
    },
    size = 12,

    -- Entity categories
    categories = {"golem", "construct", "crystal", "magical", "guardian"},
    threatCategories = {"player", "intruder", "destroyer", "sonic_attacks"},
    
    -- Base stats (magical crystal construct)
    maxHealth = 180,
    health = 180,
    maxStamina = 90,
    stamina = 90,
    maxMana = 120,
    mana = 120,
    speed = 2.2,
    attack = 30,
    defense = 35,
    magicAttack = 45,
    magicDefense = 55,  -- High magic defense
    crystalResonance = 40,
    sonicVulnerability = 2.0,
    construct_durability = 25,
    crystal_affinity = 35,
    
    -- Behaviors
    behaviors = {"crystal_patrol", "prismatic_magic", "resonance_field", "crystal_growth"},
    behaviorConfigs = {
        crystal_patrol = {
            patrolRadius = 25,
            crystallineMovement = 1.8,
            harmonicGait = true,
            prismaticTrail = 2.0
        },
        prismatic_magic = {
            lightRefraction = 2.5,
            prismSpells = 2.0,
            crystalBeam = 2.2,
            spectralMagic = 1.8
        },
        resonance_field = {
            resonanceRadius = 15,
            harmonicDisruption = 2.0,
            crystalHarmonics = true,
            magicAmplification = 1.5
        },
        crystal_growth = {
            crystalFormation = 2.0,
            structuralGrowth = true,
            crystallineHealing = 2.5,
            geometricExpansion = 1.8
        }
    },
    
    -- Enhanced variant system for crystal golems
    variantChances = {
        normal = 0.50,          -- 50% quartz golem
        shiny = 0.35,           -- 35% amethyst golem (shiny)
        rare = 0.12,            -- 12% diamond golem (rare)
        legendary = 0.03        -- 3% prismatic titan (legendary)
    },
    
    variants = {
        normal = {
            name = "Quartz Golem",
            description = "A translucent golem formed from pure quartz crystal",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {1.0, 1.0, 1.1, 1.0}  -- Clear quartz
            }
        },
        
        shiny = {
            name = "Amethyst Golem",
            description = "A beautiful golem carved from deep purple amethyst",
            statModifiers = {
                maxHealth = 1.3,    -- 234 health
                maxMana = 1.5,      -- 180 mana
                magicAttack = 1.4,  -- 63 magic attack
                magicDefense = 1.3, -- 71.5 magic defense
                amethyst_power = 2.0,
                psychic_resonance = 1.8
            },
            appearanceModifiers = {
                scale = 1.1,
                glow = true,
                colorTint = {0.8, 0.6, 1.2, 1.0},  -- Deep purple amethyst
                amethyst_glow = true,
                psychic_aura = true,
                crystal_facets = true
            },
            soundModifiers = {
                pitch = 1.2,
                volume = 1.2,
                crystalline = true
            }
        },
        
        rare = {
            name = "Diamond Golem",
            description = "An incredibly hard golem forged from pure diamond",
            statModifiers = {
                maxHealth = 1.6,    -- 288 health
                attack = 1.8,       -- 54 attack
                defense = 2.0,      -- 70 defense
                magicDefense = 1.8, -- 99 magic defense
                diamond_hardness = 3.0,
                light_mastery = 2.5,
                indestructible_form = 2.0
            },
            appearanceModifiers = {
                scale = 1.2,
                colorTint = {1.2, 1.2, 1.3, 1.0},  -- Brilliant diamond
                diamond_brilliance = true,
                light_refraction = true,
                perfect_clarity = true,
                rainbow_prisms = true
            },
            soundModifiers = {
                pitch = 1.3,
                volume = 1.3,
                reverb = true,
                brilliant = true
            }
        },
        
        legendary = {
            name = "Prismatic Titan",
            description = "A colossal golem that embodies all crystal properties",
            statModifiers = {
                maxHealth = 2.5,    -- 450 health
                attack = 2.2,       -- 66 attack
                defense = 2.2,      -- 77 defense
                magicAttack = 2.5,  -- 112.5 magic attack
                magicDefense = 2.5, -- 137.5 magic defense
                prismatic_mastery = 10.0,
                crystal_dominion = 5.0,
                reality_refraction = 3.0
            },
            appearanceModifiers = {
                scale = 1.6,
                glow = true,
                colorTint = {1.4, 1.3, 1.5, 1.0},  -- Prismatic rainbow crystal
                prismatic_aura = "legendary",
                reality_refraction = true,
                crystal_mastery = true,
                geometric_perfection = true
            },
            soundModifiers = {
                pitch = 1.0,
                volume = 1.6,
                reverb = true,
                echo = true,
                prismatic_harmony = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "crystal_shard", chance = 1.0, quantity = {3, 6}},
        {id = "golem_core", chance = 0.8, quantity = {1, 1}},
        {id = "prismatic_essence", chance = 0.7, quantity = {1, 3}},
        {id = "resonance_crystal", chance = 0.6, quantity = {1, 2}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "amethyst_cluster", chance = 1.0, quantity = {2, 4}},
            {id = "psychic_golem_core", chance = 0.9, quantity = {1, 1}},
            {id = "amethyst_power_crystal", chance = 0.8, quantity = {1, 1}},
            {id = "psychic_resonance_gem", chance = 0.7, quantity = {1, 1}}
        },
        rare = {
            {id = "diamond_fragment", chance = 1.0, quantity = {1, 3}},
            {id = "brilliant_golem_core", chance = 0.9, quantity = {1, 1}},
            {id = "diamond_hardness_crystal", chance = 0.8, quantity = {1, 1}},
            {id = "light_mastery_prism", chance = 0.7, quantity = {1, 1}}
        },
        legendary = {
            {id = "prismatic_crystal", chance = 1.0, quantity = {2, 4}},
            {id = "titan_crystal_core", chance = 0.95, quantity = {1, 1}},
            {id = "reality_refraction_prism", chance = 0.9, quantity = {1, 1}},
            {id = "crystal_dominion_orb", chance = 0.8, quantity = {1, 1}},
            {id = "prismatic_mastery_gem", chance = 0.7, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "crystal_golem",
        scale = 1.3,
        animations = {
            "crystal_walk", "prismatic_beam", "resonance_pulse", "crystal_growth", "prismatic_storm"
        },
        variants = {
            "quartz_golem", "amethyst_golem", "diamond_golem", "prismatic_titan"
        }
    },
    
    -- Sound effects with crystalline characteristics
    sounds = {
        crystal_chime = {
            file = "crystal_golem_chime",
            synth = {
                instrument = "celesta",
                notes = {"C3", "E3", "G3", "C4"},
                durations = {0.5, 0.4, 0.5, 0.8},
                volume = 0.8,
                crystalline = true
            }
        },
        prismatic_beam = {
            file = "prismatic_beam",
            synth = {
                instrument = "kalimba",
                notes = {"G3", "B3", "D4", "G4"},
                durations = {0.3, 0.2, 0.3, 0.4},
                volume = 0.7,
                prismatic = true
            }
        },
        resonance_pulse = {
            file = "resonance_pulse",
            synth = {
                instrument = "xylophone",
                notes = {"F3", "A3", "C4", "F4"},
                durations = {0.4, 0.3, 0.4, 0.6},
                volume = 0.6,
                resonant = true
            }
        },
        crystal_harmony = {
            file = "crystal_harmony",
            synth = {
                instrument = "harp",
                notes = {"D3", "F#3", "A3", "D4", "F#4"},
                durations = {0.6, 0.5, 0.6, 0.5, 0.8},
                volume = 0.7,
                harmonious = true
            }
        }
    },
    
    -- Special crystal golem abilities
    abilities = {
        crystal_armor = {
            type = "passive",
            description = "Natural crystal armor provides high magic defense",
            effect = "magic_resistance"
        },
        prismatic_beam = {
            type = "active",
            description = "Fires concentrated light beam that refracts into multiple rays",
            effect = "prismatic_attack",
            manaCost = 30,
            cooldown = 12
        },
        resonance_field = {
            type = "passive",
            description = "Creates harmonic field that disrupts enemy magic",
            effect = "magic_disruption"
        },
        crystal_growth = {
            type = "active",
            description = "Grows crystal formations for defense or healing",
            effect = "crystal_creation",
            manaCost = 40,
            cooldown = 18
        },
        light_refraction = {
            type = "active",
            description = "Bends light to become invisible or blind enemies",
            effect = "light_manipulation",
            manaCost = 25,
            cooldown = 15
        },
        harmonic_resonance = {
            type = "active",
            description = "Creates destructive resonance in crystal and metal",
            effect = "resonance_shatter",
            cooldown = 20
        }
    }
}

-- Initialize the crystal golem entity using enhanced template
function CrystalGolem.init(entity, world)
    -- Copy all fields from CrystalGolem template to entity instance
    for k, v in pairs(CrystalGolem) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return CrystalGolem
