local Mimic = {
    id = "mimic",
    name = "Mimic",
    type = "mimic",

    -- Entity categories
    categories = {"monster", "rare", "trap"},

    -- Threat and food categories
    threatCategories = {"player"}, -- Primarily targets players
    foodCategories = {"player"}, -- It "eats" players

    -- Stats
    maxHealth = 60,
    health = 60,
    speed = 0, -- Doesn't move until triggered

    -- Behaviors
    behaviors = {"mimic"},

    -- Behavior configurations
    behaviorConfigs = {
        mimic = {
            disguise = "chest", -- Can mimic other objects
            detectionRange = 5, -- How close the player needs to be to trigger it
            attackCooldown = 2, -- Time between attacks
            surpriseAttack = true -- Gets a bonus on the first attack
        }
    },

    -- Abilities
    abilities = {
        camouflage = {
            -- (Implementation for changing appearance)
        },
        bite = {
            damage = 20
        }
    },

    -- Appearance
    appearance = {
        sprite = "mimic", -- Replace with your mimic sprite
        scale = 1.0,
        animations = {
            "idle", -- Disguised animation
            "attack",
            "take_damage"
        }
    },

    -- Sound effects
    sounds = {
        reveal = "mimic_reveal",
        attack = "mimic_attack"
    },

    -- Loot drops
    drops = {
        {
            id = "gold",
            chance = 0.8,
            quantity = {5, 15}
        },
        {
            id = "mimic_tooth",
            chance = 0.3,
            quantity = {1, 2}
        }
    }
}

-- Initialize the mimic entity
function Mimic.init(entity, world)
    entity.x = entity.x or 0
    entity.y = entity.y or 0
    entity.rotation = entity.rotation or 0
    entity.width = 1
    entity.height = 1
    entity.type = "mimic"
    
    -- Apply behavior
    local behaviorName = "mimic_behavior"
    -- Check if world is not nil before trying to access it
    if world and world.modules and world.modules.behaviors then
        local behavior = world.modules.behaviors[behaviorName]
        if behavior then
            behavior.apply(entity)
        end
    end
    
    return entity
end

return Mimic