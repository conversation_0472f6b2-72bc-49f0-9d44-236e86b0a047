-- items/potions/health_potion.lua

local Potion = {
    id = "health_potion",
    name = "Health Potion",
    description = "A red potion that restores health when consumed.",
    type = "potion",
    category = "consumable",
    stackable = true,
    maxStack = 10,

    stats = {
        heal_amount = 25.0,
        weight = 0.5,
    },

    sprite = "res://Sprite/items/potions/health_potion.png",
    size = { width = 32, height = 32 },

    effects = {
        instant_heal = true,
        duration = 0
    },

    meta = {
        rarity = "common"
    },

    onUse = function(self, user)
        if user and user.stats and user.stats.health then
            local maxHealth = user.stats.maxHealth or 100
            local currentHealth = user.stats.health
            local healAmount = self.stats.heal_amount
            
            -- Apply healing
            user.stats.health = math.min(maxHealth, currentHealth + healAmount)
            
            -- Notify the user
            print(user.name .. " used a Health Potion and recovered " .. healAmount .. " health!")
            return true
        end
        return false
    end
}

return Potion
