# Lazarus Arc - Class System Restructuring Guide

## Overview

This guide explains the refactoring of the Lazarus Arc class system from a single monolithic file to a modular approach with individual class files. This change makes it easier to add new classes in the future while maintaining the same functionality.

## Directory Structure

```
lazarus_arc/
│
├── classes.lua            # Main loader that handles all class functionality
└── classes/               # Directory containing individual class files
    ├── warrior.lua        # Warrior class definition
    ├── mage.lua           # Mage class definition
    ├── archer.lua         # Archer class definition
    ├── paladin.lua        # Paladin class definition
    ├── rogue.lua          # Rogue class definition
    └── berserker.lua      # Berserker class definition
```

## Implementation Notes

1. **No Change to API**: The public interface remains identical - all existing code that uses the classes system should continue to work without changes.

2. **Adding New Classes**: To add a new class:
   - Create a new file in the `classes/` directory (e.g., `classes/necromancer.lua`)
   - Format it following the pattern of the existing class files
   - Add the class name to the `Classes.baseClasses` table in `classes.lua` if it's a base class

3. **Usage Remains the Same**: 
   ```lua
   local Classes = require("classes")
   local warriorData = Classes.getClass("warrior")
   local availableClasses = Classes.getAvailableClasses()
   ```

## Migration Process

1. Create the `classes/` directory
2. Move each class definition to its own file in the directory
3. Ensure `classes.lua` loads all of the class files correctly

There's no need to modify any other code that uses the Classes module - the refactoring is completely internal to the module.

## Benefits

- Easier to maintain individual class definitions
- Simpler to add new classes without modifying a large file
- Better organization for future expansion
- More aligned with the modular structure used in the rest of the game
