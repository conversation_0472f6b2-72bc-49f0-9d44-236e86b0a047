local Sunfish = {
    id = "sunfish",
    name = "Sunfish",
    type = "sunfish",

    -- Entity categories
    categories = { "animal", "fish", "small", "prey" },

    -- Threat categories
    threatCategories = { "predator", "large" },
    foodCategories = { "insect", "worm" },

    -- Stats
    maxHealth = 5,
    health = 5,
    speed = 2.0,

    -- Behaviors
    behaviors = { "schooling", "flee" },

    -- Behavior configurations
    behaviorConfigs = {
        schooling = {
            neighborRadius = 4,
            separationDistance = 0.8,
        },
        flee = {
            useCategories = true,
            moveSpeed = 4.0,
            detectionRadius = 5
        }
    },

    -- Appearance
    appearance = {
        sprite = "sunfish", -- Replace with your sunfish sprite
        scale = 0.8,
        animations = {
            "swim",
            "flee"
        },
        -- Could have variations in color
        variants = {
            "orange",
            "blue",
            "green"
        }
    },

    -- Sound effects
    sounds = {
        flop = "fish_flop" -- Generic fish sound
    }
}

-- Initialize the sunfish entity
function Sunfish.init(entity, world)
    entity.position = entity.position or { x = 0, y = 0 }
    entity.velocity = entity.velocity or { x = 0, y = 0 }

    -- Initialize behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.init then
            behavior.init(entity, entity.behaviorConfigs[behaviorName])
        end
    end

    return entity
end

return Sunfish