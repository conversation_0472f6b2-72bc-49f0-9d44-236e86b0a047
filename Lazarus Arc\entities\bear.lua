-- Enhanced bear with variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local Bear = {
    id = "bear",
    name = "Bear",
    type = "bear",
    shape = {
        {0, -10}, {7, -7}, {10, 0}, {7, 7},
        {0, 10}, {-7, 7}, {-10, 0}, {-7, -7}
    },
    size = 10,

    -- Entity categories
    categories = {"animal", "predator", "mammal", "large"},

    -- Threat and food categories
    threatCategories = {"monster", "player"},
    foodCategories = {"medium_prey", "small_prey", "fish", "honey"},

    -- Stats
    maxHealth = 100,
    health = 100,
    maxStamina = 70,
    stamina = 70,
    speed = 2.0,

    -- Behaviors
    behaviors = {"hunt", "wander", "territorial", "hibernate"},

    -- Behavior configurations
    behaviorConfigs = {
        hunt = {
            moveSpeed = 3.0,
            huntRadius = 20,
            chaseRadius = 25,
            attackRange = 2.0,
            attackDamage = 25,
            attackCooldown = 1.5,
            preferredTargets = {"deer", "rabbit", "fish"}
        },
        wander = {
            moveSpeed = 1.5,
            changeDirectionChance = 0.02,
            wanderRadius = 15
        },
        territorial = {
            territoryRadius = 30,
            aggressionRadius = 15,
            warningDuration = 5,
            chaseDuration = 10
        },
        hibernate = {
            startSeason = "winter",
            endSeason = "spring",
            healthRegen = 0.1,
            staminaRegen = 0.2
        }
    },

    -- Special abilities
    abilities = {
        charge = {
            damageMultiplier = 2.0,
            range = 5,
            cooldown = 8
        },
        swipe = {
            damageMultiplier = 1.5,
            range = 2,
            cooldown = 3
        },
        roar = {
            range = 20,
            duration = 3,
            cooldown = 15,
            effect = "fear"
        }
    },

    -- Appearance
    appearance = {
        sprite = "bear",
        scale = 1.5,
        animations = {
            "idle", "walk", "run", "charge", "swipe", "roar", "hibernate"
        },
        variants = {
            "brown", "black", "grizzly", "polar"
        }
    },

    -- Sound effects with synth configuration
    sounds = {
        roar = {
            file = "bear_roar",
            synth = {
                instrument = "bass_guitar",
                notes = {"C2", "E2", "G2"},
                durations = {0.5, 0.4, 0.8},
                volume = 0.7,
                vibrato = true,
                vibratoRate = 3.0
            }
        },
        growl = {
            file = "bear_growl",
            synth = {
                instrument = "bass_guitar",
                notes = {"E2", "C2"},
                durations = {0.4, 0.6},
                volume = 0.6,
                vibrato = true,
                vibratoRate = 4.0
            }
        },
        snarl = {
            file = "bear_snarl",
            synth = {
                instrument = "electric_guitar",
                notes = {"G2", "E2"},
                durations = {0.3, 0.4},
                volume = 0.6,
                vibrato = true,
                vibratoRate = 8.0
            }
        },
        footstep = {
            synth = {
                instrument = "marimba",
                notes = {"C2"},
                duration = 0.2,
                volume = 0.4
            }
        },
        sniff = {
            synth = {
                instrument = "harmonica",
                notes = {"F3"},
                duration = 0.3,
                volume = 0.25
            }
        },
        hurt = {
            synth = {
                instrument = "bass_guitar",
                notes = {"D#2"},
                duration = 0.5,
                volume = 0.7,
                vibrato = true,
                vibratoRate = 6.0
            }
        },
        death = {
            synth = {
                instrument = "bass_guitar",
                notes = {"C2", "A1", "F1"},
                durations = {0.8, 0.8, 1.5},
                volume = 0.7,
                vibrato = true,
                vibratoRate = 1.5
            }
        },
        charge = {
            synth = {
                instrument = "bass_guitar",
                notes = {"G2", "C3", "E3"},
                durations = {0.2, 0.2, 0.3},
                volume = 0.65
            }
        },
        idle = {
            synth = {
                instrument = "cello",
                notes = {"C3"},
                duration = 0.8,
                volume = 0.3,
                vibrato = true,
                vibratoRate = 2.0
            }
        }
    },

    -- === ENHANCED VARIANT SYSTEM ===
    variantChances = {
        normal = 0.65,          -- 65% brown bear
        shiny = 0.25,           -- 25% grizzly bear (shiny)
        rare = 0.08,            -- 8% cave bear (rare)
        legendary = 0.02        -- 2% great bear spirit (legendary)
    },

    variants = {
        normal = {
            name = "Brown Bear",
            description = "A common forest bear with brown fur",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.8, 0.6, 0.4, 1.0}  -- Brown fur
            }
        },

        shiny = {
            name = "Grizzly Bear",
            description = "A massive, aggressive bear with silver-tipped fur",
            statModifiers = {
                maxHealth = 1.5,    -- 150 health instead of 100
                speed = 1.1,        -- 2.2 speed instead of 2.0
                maxStamina = 1.3,   -- 91 stamina instead of 70
                attack_power = 1.4, -- Enhanced damage
                territorial_range = 1.5
            },
            appearanceModifiers = {
                scale = 1.2,
                glow = true,
                colorTint = {0.7, 0.6, 0.5, 1.0},  -- Darker with silver tips
                intimidating_size = true,
                battle_scars = true
            },
            soundModifiers = {
                pitch = 0.8,
                volume = 1.3,
                aggressive = true
            }
        },

        rare = {
            name = "Cave Bear",
            description = "An ancient bear species of enormous size and strength",
            statModifiers = {
                maxHealth = 2.2,    -- 220 health
                speed = 0.9,        -- 1.8 speed (slower but stronger)
                maxStamina = 1.6,   -- 112 stamina
                attack_power = 1.8,
                ancient_wisdom = 1.5
            },
            appearanceModifiers = {
                scale = 1.5,
                colorTint = {0.5, 0.5, 0.6, 1.0},  -- Dark gray-brown
                massive_build = true,
                ancient_markings = true,
                cave_dwelling_features = true
            },
            soundModifiers = {
                pitch = 0.6,
                volume = 1.5,
                reverb = true,
                ancient_echo = true
            }
        },

        legendary = {
            name = "Great Bear Spirit",
            description = "A legendary bear spirit guardian of the forest",
            statModifiers = {
                maxHealth = 3.5,    -- 350 health
                speed = 1.3,        -- 2.6 speed
                maxStamina = 2.5,   -- 175 stamina
                spirit_power = 5.0,
                nature_connection = 10.0
            },
            appearanceModifiers = {
                scale = 2.0,
                glow = true,
                colorTint = {0.8, 1.2, 0.8, 1.0},  -- Mystical green glow
                spirit_aura = "legendary",
                nature_symbols = true,
                ethereal_presence = true,
                forest_guardian_marks = true
            },
            soundModifiers = {
                pitch = 0.5,
                volume = 1.8,
                reverb = true,
                echo = true,
                nature_harmony = true
            }
        }
    },

    -- === ENHANCED DROP SYSTEM ===

    -- Base drops (available from all variants)
    baseDrops = {
        {id = "meat", chance = 0.9, quantity = {3, 5}},
        {id = "hide", chance = 0.8, quantity = {2, 3}},
        {id = "claw", chance = 0.6, quantity = {2, 4}},
        {id = "fat", chance = 0.7, quantity = {1, 2}}
    },

    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "grizzly_pelt", chance = 0.9, quantity = {1, 1}},
            {id = "massive_bear_claw", chance = 0.8, quantity = {2, 4}},
            {id = "intimidation_musk", chance = 0.6, quantity = {1, 1}},
            {id = "territorial_essence", chance = 0.5, quantity = {1, 1}}
        },
        rare = {
            {id = "cave_bear_hide", chance = 0.9, quantity = {1, 2}},
            {id = "ancient_bear_tooth", chance = 0.8, quantity = {1, 2}},
            {id = "primordial_essence", chance = 0.7, quantity = {1, 1}},
            {id = "cave_bear_skull", chance = 0.4, quantity = {1, 1}},
            {id = "ancient_wisdom_stone", chance = 0.3, quantity = {1, 1}}
        },
        legendary = {
            {id = "spirit_bear_pelt", chance = 0.95, quantity = {1, 1}},
            {id = "nature_guardian_essence", chance = 0.9, quantity = {1, 3}},
            {id = "forest_spirit_claw", chance = 0.8, quantity = {1, 2}},
            {id = "great_bear_totem", chance = 0.7, quantity = {1, 1}},
            {id = "nature_blessing_orb", chance = 0.6, quantity = {1, 1}},
            {id = "forest_guardian_heart", chance = 0.4, quantity = {1, 1}}
        }
    }
}

-- Initialize the entity using enhanced template
function Bear.init(entity, world)
    -- Copy all fields from Bear template to entity instance
    for k, v in pairs(Bear) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return Bear