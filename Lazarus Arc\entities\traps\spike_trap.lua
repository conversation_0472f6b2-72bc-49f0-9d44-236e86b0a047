local SpikeTrap = {
    id = "spike_trap",
    name = "Spike Trap",
    type = "trap",
    
    -- Entity categories
    categories = {"trap", "dungeon", "physical"},
    
    -- Target categories
    targetCategories = {"player", "monster"},
    
    -- Stats
    maxHealth = 25,
    health = 25,
    duration = 0, -- Instant effect
    cooldown = 0, -- No cooldown needed as it's destroyed
    
    -- Trap properties
    properties = {
        damage = 35,
        radius = 1.5,
        setupTime = 2,
        isArmed = false,
        isTriggered = false,
        destroyOnActivate = true,
        pierceAmount = 0.3 -- 30% of damage ignores armor
    },
    
    -- Appearance
    appearance = {
        sprite = "spike_trap",
        scale = 1.0,
        animations = {
            "idle", "trigger"
        },
        variants = {
            "iron", "steel", "bone", "crystal"
        }
    },
    
    -- Sound effects
    sounds = {
        trigger = "spike_trap_trigger",
        hit = "spike_trap_hit"
    },
    
    -- Effects
    effects = {
        pierce = {
            type = "damage",
            amount = 35,
            pierceAmount = 0.3,
            effects = {
                bleed = {
                    duration = 5,
                    damage = 3,
                    interval = 1
                }
            }
        }
    }
}

-- Initialize the trap
function SpikeTrap.init(entity, world)
    -- Copy all fields from SpikeTrap template to entity instance
    for k, v in pairs(SpikeTrap) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position
    entity.position = entity.position or {x = 0, y = 0}

    -- Set random variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize trap state
    entity.properties.isArmed = false
    entity.properties.isTriggered = false
    entity.properties.triggerTime = 0

    return entity
end

-- Update the trap
function SpikeTrap.update(entity, world, dt)
    -- Check if trap is armed and not triggered
    if entity.properties.isArmed and not entity.properties.isTriggered then
        -- Check for targets in radius
        if world and world.entities then
            for _, target in ipairs(world.entities) do
                if target.position and target.categories then
                    -- Check if target is in trap categories
                    local isTarget = false
                    for _, category in ipairs(entity.targetCategories) do
                        if table.contains(target.categories, category) then
                            isTarget = true
                            break
                        end
                    end
                    
                    if isTarget then
                        local distance = math.sqrt(
                            (target.position.x - entity.position.x)^2 + 
                            (target.position.y - entity.position.y)^2
                        )
                        
                        if distance <= entity.properties.radius then
                            -- Trigger trap
                            entity.properties.isTriggered = true
                            entity.properties.triggerTime = world.time
                            
                            -- Apply damage to target
                            if target.takeDamage then
                                local damage = entity.properties.damage
                                local pierceAmount = damage * entity.properties.pierceAmount
                                local normalDamage = damage - pierceAmount
                                
                                -- Apply normal damage (can be blocked by armor)
                                target.takeDamage(normalDamage)
                                
                                -- Apply piercing damage (ignores armor)
                                target.takeDamage(pierceAmount, true)
                                
                                -- Apply bleed effect
                                if target.applyEffect then
                                    target.applyEffect(entity.effects.pierce.effects.bleed)
                                end
                            end
                            
                            -- Play trigger sound
                            if world.playSound then
                                world.playSound(entity.sounds.trigger)
                            end
                            
                            -- Destroy trap if configured
                            if entity.properties.destroyOnActivate then
                                if world.removeEntity then
                                    world.removeEntity(entity)
                                end
                            end
                            
                            break
                        end
                    end
                end
            end
        end
    end
end

-- Arm the trap
function SpikeTrap.arm(entity, world)
    if not entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = true
        entity.properties.triggerTime = world.time
        return true
    end
    return false
end

-- Disarm the trap
function SpikeTrap.disarm(entity, world)
    if entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = false
        entity.properties.triggerTime = world.time
        return true
    end
    return false
end

return SpikeTrap
