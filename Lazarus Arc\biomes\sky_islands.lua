-- biomes/sky_islands.lua
-- Declare SkyIslandsBiome at the top level so it can be referenced from within functions
local SkyIslandsBiome = {}

-- Define the biome properties
SkyIslandsBiome = {
    id = "sky_islands",
    name = "Sky Islands",
    description = "Floating islands in the sky with unique ecosystems and creatures.", --

    -- Environmental factors (High altitude, windy, variable temp, possible low pressure?)
    environment = {
        humidity = 0.6,    -- From clouds
        temperature = 0.4, -- Can be cool due to altitude
        sunlight = 0.9,    -- Above clouds, usually bright
        windStrength = 0.8 -- Often very windy
        -- airPressure = 0.7? -- Optional, if low pressure has effects
    },

    -- Core tile types used in this biome
    -- NOTE: Needs 'cloud_floor'(impassable space/surface?), 'sky_grass', 'crystal_formations'(tile/feature?), 'bottomless_drop'(hazard). Uses 'ruins'. 'waterfall' is a feature.
    primaryTiles = {"sky_grass", "cloud_floor"}, -- Island surface and the surrounding 'empty' space
    secondaryTiles = {"stone", "crystal_formations", "water"}, -- Rocky parts, crystal growth, maybe pools/streams on islands
    rareTiles = {"ancient_ruins", "ancient_technology", "bottomless_drop"}, -- Ruins, tech, and the hazard of falling off

    -- Default proportions (Represents island surfaces primarily)
    tileDistribution = {
        sky_grass = 0.7,
        stone = 0.15, -- Rocky parts of islands
        crystal_formations = 0.05,
        water = 0.05, -- Small pools or streams originating waterfalls
        ancient_ruins = 0.03,
        ancient_technology = 0.01,
        bottomless_drop = 0.01 -- Represents edges or holes in islands
        -- cloud_floor makes up the space between islands implicitly
    },

    -- Entities common to this biome
    commonEntities = {
        "flying_creature_small", "wind_spirit_lesser", "sky_plant_glowing", "cloud_serpent_minor" -- Added plant/serpent
    },
    uncommonEntities = {
        "cloud_giant", "winged_lion", "sky_whale_calf", "floating_crystal" -- Added crystal
    },
    rareEntities = {
        "sky_whale", "storm_elemental", "ancient_sky_guardian" -- Added elemental/guardian
    },

    -- Specific biome properties
    isFloating = true,
    baseAltitude = 1000, -- Arbitrary base height units

    -- Biome variants for diverse generation
    variants = {
        lush_isles = {
            name = "Lush Sky Isles",
            humidity = 0.8,
            temperature = 0.5,
            tileAdjustments = { sky_grass = 0.8, water = 0.1 },
            entityAdjustments = { flying_creature_small = 1.5, sky_plant_glowing = 1.8 },
            specialEffects = {"abundant_flora", "gentle_breezes"}
        },
        crystal_spires = {
            name = "Crystal Spire Isles",
            tileAdjustments = { sky_grass = 0.4, stone = 0.2, crystal_formations = 0.35 },
            entityAdjustments = { wind_spirit_lesser = 1.3, floating_crystal = 1.5, crystal_golem_sky = 1.0 }, -- Sky golem?
            specialEffects = {"crystalline_glow", "energy_resonance"}
        },
        ancient_ruins_sky = {
            name = "Sky Ruins",
            tileAdjustments = { sky_grass = 0.5, stone = 0.1, ancient_ruins = 0.3, ancient_technology = 0.05 },
            entityAdjustments = { ancient_sky_guardian = 1.5, wind_spirit_lesser = 1.0, cloud_giant = 0.8 },
            specialEffects = {"crumbling_structures", "ancient_glyphs", "tech_remnants"}
        },
        storm_peaks = {
             name = "Storm Peak Isles",
             windStrength = 1.0, -- Max wind
             environment = { temperature = 0.3, sunlight = 0.4 }, -- Colder, darker
             tileAdjustments = { sky_grass = 0.4, stone = 0.4, bottomless_drop = 0.1 }, -- More rock, more gaps
             entityAdjustments = { storm_elemental = 1.5, flying_creature_predator = 1.3, wind_spirit_lesser = 1.8 },
             specialEffects = {"frequent_lightning", "howling_winds", "turbulent_air"}
        }
    },

    -- Structures that can generate in this biome
    structures = {
        {
            name = "cloud_castle", -- Large structure, maybe on its own island?
            chance = 0.02,
            unique = true,
            entities = {"sky_knight", "cloud_mage", "castle_golem"}
        },
        {
            name = "sky_village", -- Village built on an island
            chance = 0.06,
            entities = {"sky_villager", "wind_farmer", "griffin_mount"} -- Added mount
        },
        {
            name = "abandoned_observatory",
            chance = 0.05,
            entities = {"star_gazer_ghost", "broken_telescope", "celestial_map_fragment"}
        },
        {
            name = "hidden_garden", -- Sheltered garden area
            chance = 0.04,
            requiresVariant = "lush_isles",
            entities = {"garden_spirit", "rare_sky_flower", "glowing_fruit_tree"}
        }
    },

    -- Weather patterns (Focus on wind, clouds, storms)
    weather = {
        transitions = {
             clear_sky = { clear_sky = 60, cloudy = 30, wind_storm = 10 },
             cloudy = { clear_sky = 40, cloudy = 40, rain_cloud = 15, wind_storm = 5 }, -- Clouds below islands?
             rain_cloud = { cloudy = 60, clear_sky = 30, storm = 10 }, -- Rain falls down
             wind_storm = { cloudy = 50, wind_storm = 40, storm = 10 },
             storm = { wind_storm = 60, rain_cloud = 30, cloudy = 10 } -- Electrical storms
        },
        default = "clear_sky"
    },

    -- Unique features generation
    features = {
        {
            name = "floating_city", -- Massive structure, likely unique per region
            chance = 0.01,
            unique = true
        },
        {
            name = "sky_temple", -- Structure or large feature
            chance = 0.04,
            -- requiresVariant = "ancient_ruins_sky"?
        },
        {
            name = "rainbow_bridge", -- Feature connecting islands
            chance = 0.05,
            isConnector = true
        },
        {
            name = "bottomless_drop", -- Hazard feature, places void/hazard tiles
            chance = 0.15,
            danger = true
        },
        {
            name = "ancient_artifacts", -- Places artifact entities/tiles
            chance = 0.08
        },
        {
             name = "waterfall_edge", -- Feature creating waterfall effect tiles at edge
             chance = 0.1,
             requiresTile = "water" -- Needs source water tile on island
        }
    },

    -- Generation algorithm (Needs logic to create islands in empty space)
    generate = function(chunk, world, variant)
        print("Generating " .. (variant and SkyIslandsBiome.variants[variant].name or "Sky Islands") .. " biome")
        local variantData = variant and SkyIslandsBiome.variants[variant] or nil

        -- Adjust tile distribution for island surfaces
        local islandTileDistribution = {}
        for tileType, chance in pairs(SkyIslandsBiome.tileDistribution) do
             -- Exclude cloud_floor and bottomless_drop from island surface generation directly
             if tileType ~= "cloud_floor" and tileType ~= "bottomless_drop" then
                 islandTileDistribution[tileType] = chance
                 if variantData and variantData.tileAdjustments and variantData.tileAdjustments[tileType] then
                     islandTileDistribution[tileType] = variantData.tileAdjustments[tileType]
                 end
             end
        end
        -- Normalize chances if needed

        -- Noise functions
        local seed = world.seed + (chunk.x * 191 + chunk.y * 877)
        math.randomseed(seed)
        local function pnoise2D(x, y, frequency, seed_offset) return love.math.noise(x * frequency, y * frequency, (world.seed + (seed_offset or 0)) * 0.01) end

        -- Initialize 2D tiles array if not exists
        chunk.tiles = chunk.tiles or {}
        for x = 0, world.CHUNK_SIZE - 1 do
            chunk.tiles[x] = chunk.tiles[x] or {}
        end

        -- Generate tiles (Island vs Cloud Floor)
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                local worldX = chunk.x * world.CHUNK_SIZE + x
                local worldY = chunk.y * world.CHUNK_SIZE + y

                local islandShapeNoise = pnoise2D(worldX, worldY, 0.05, 1) -- Defines island shapes
                local islandSurfaceNoise = pnoise2D(worldX, worldY, 0.2, 2) -- Defines surface variation on islands

                local tileType = "cloud_floor" -- Default is empty space/clouds
                local isIsland = false

                if islandShapeNoise > 0.2 then -- Threshold for being on an island
                    isIsland = true
                    tileType = "sky_grass" -- Default island surface

                    -- Determine island surface tile type
                    local tileRoll = math.random()
                    local cumulativeChance = 0
                    for tType, chance in pairs(islandTileDistribution) do
                         cumulativeChance = cumulativeChance + chance
                         if tileRoll <= cumulativeChance then
                              tileType = tType
                              break
                         end
                    end

                    -- Refine based on noise (e.g., higher parts rockier)
                    if islandSurfaceNoise > 0.6 then tileType = "stone" end
                    if tileType=="sky_grass" and islandSurfaceNoise < -0.5 then tileType="water" end -- Pools

                    -- Variant specific overrides
                    if variant == "crystal_spires" and islandSurfaceNoise > 0.5 then
                         if math.random() < 0.4 then tileType = "crystal_formations" end
                    end
                end

                -- Create the tile
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = math.random(1, 3),
                    isFloating = true,
                    altitude = SkyIslandsBiome.baseAltitude + islandShapeNoise * 100, -- Example altitude calculation
                    passable = isIsland and (tileType ~= "bottomless_drop"), -- Cloud floor is impassable by default walking
                    isIslandSurface = isIsland,
                    isUnderground = false
                }

                -- Apply specific properties
                if tileType == "cloud_floor" then tile.passable = false end -- Or allow flying?

                chunk.tiles[x][y] = tile
            end
        end

        -- Call helper functions
        SkyIslandsBiome.generateFeatures(chunk, world, variant)
        SkyIslandsBiome.populateEntities(chunk, world, variant)
        SkyIslandsBiome.generateStructures(chunk, world, variant)
        SkyIslandsBiome.postProcess(chunk, world, variant)
    end,

    -- Helper function implementations (Placeholders - need detailed logic)
    generateFeatures = function(chunk, world, variant)
        print("Generating Sky Islands features...")
        -- Logic for placing features: cities/temples (structures?), bridges (connectors), drops (hazards), artifacts (entities), waterfalls (effects/tiles at edges)
        -- Ensure features are placed ON islands (isIslandSurface=true) unless it's a bridge or drop.
        for _, feature in ipairs(SkyIslandsBiome.features) do
             if math.random() < feature.chance then
                  if not feature.requiresVariant or feature.requiresVariant == variant then
                      if not feature.unique or not (chunk.uniqueFeatures and chunk.uniqueFeatures[feature.name]) then
                         local attempts = 0; local placed = false; local fx, fy
                         while attempts < 10 and not placed do
                             fx = math.random(1, world.CHUNK_SIZE - 2)
                             fy = math.random(1, world.CHUNK_SIZE - 2)
                             local tile = chunk.tiles[fx][fy]
                             -- Place most features on island surface
                             if tile and tile.isIslandSurface then
                                 if feature.name == "bottomless_drop" then -- Place drops at edges or as holes
                                      -- Find an edge tile instead?
                                 else
                                     placed = true
                                 end
                             end
                             attempts = attempts + 1
                         end
                         if placed then
                             local fwx = chunk.x * world.CHUNK_SIZE + fx
                             local fwy = chunk.y * world.CHUNK_SIZE + fy
                             print("Placing feature: " .. feature.name .. " at " .. fwx .. "," .. fwy)
                             -- Add specific generation logic per feature
                             if feature.name == "rainbow_bridge" then
                                  -- Needs logic to find another island nearby and place bridge entity/tiles
                             elseif feature.name == "waterfall_edge" then
                                  -- Needs logic to find edge near water and place waterfall effect/tiles
                             end
                             if feature.unique then chunk.uniqueFeatures = chunk.uniqueFeatures or {}; chunk.uniqueFeatures[feature.name] = true end
                         end
                     end
                 end
             end
        end
    end,

    populateEntities = function(chunk, world, variant)
        print("Populating Sky Islands entities...")
        -- Spawn entities, ensuring flying creatures can spawn over cloud_floor, others on island surfaces.
        local variantData = variant and SkyIslandsBiome.variants[variant] or nil
        local function spawnEntity(entityType, count)
            local multiplier = 1.0
            if variantData and variantData.entityAdjustments and variantData.entityAdjustments[entityType] then multiplier = variantData.entityAdjustments[entityType] end
            local adjustedCount = math.floor(count * multiplier + 0.5)
            for i = 1, adjustedCount do
                 local attempts = 0; local placed = false
                 while attempts < 5 and not placed do
                     local x = math.random(0, world.CHUNK_SIZE - 1)
                     local y = math.random(0, world.CHUNK_SIZE - 1)
                     local tile = chunk.tiles[x][y]
                     local entityInfo = world.entitySystem:getEntityInfo(entityType) -- Assume this exists
                     -- Check placement: Flying can be anywhere, others need island surface
                     if tile and (entityInfo.canFly or tile.isIslandSurface) then
                          world.entitySystem:addEntity(entityType, chunk.x * world.CHUNK_SIZE + x, chunk.y * world.CHUNK_SIZE + y, {isFloatingEnv=true})
                          placed = true
                     end
                     attempts = attempts + 1
                 end
            end
        end
        -- Spawn based on lists
        for _, et in ipairs(SkyIslandsBiome.commonEntities) do spawnEntity(et, math.random(2,6)) end
        for _, et in ipairs(SkyIslandsBiome.uncommonEntities) do spawnEntity(et, math.random(1,3)) end
        for _, et in ipairs(SkyIslandsBiome.rareEntities) do if math.random() < 0.1 then spawnEntity(et, 1) end end
        -- Add variant specifics
    end,

    generateStructures = function(chunk, world, variant)
        print("Generating Sky Islands structures...")
         -- Similar logic: Iterate list, check chance/variant, find location ON an island surface, place structure & entities
          for _, structure in ipairs(SkyIslandsBiome.structures) do
             if math.random() < structure.chance then
                  if not structure.requiresVariant or structure.requiresVariant == variant then
                      local attempts = 0; local placed = false; local sx, sy
                      while attempts < 15 and not placed do
                           sx = math.random(2, world.CHUNK_SIZE - 3)
                           sy = math.random(2, world.CHUNK_SIZE - 3)
                           local tile = chunk.tiles[sx][sy]
                           -- Check space and ensure it's on island surface
                           if tile and tile.isIslandSurface and tile.type == "sky_grass" then -- Prefer placing on grass
                                -- Basic space check: Ensure immediate neighbors are also island surface
                                local spaceOk = true
                                for dx = -1, 1 do for dy = -1, 1 do
                                    local nx, ny = sx+dx, sy+dy
                                    if nx>=0 and nx<world.CHUNK_SIZE and ny>=0 and ny<world.CHUNK_SIZE then
                                         if not chunk.tiles[nx][ny].isIslandSurface then spaceOk=false; break end
                                    else spaceOk=false; break end -- Don't build on edge
                                end if not spaceOk then break end end
                                if spaceOk then placed = true end
                           end
                           attempts = attempts + 1
                      end
                      if placed then
                           local worldX = chunk.x * world.CHUNK_SIZE + sx
                           local worldY = chunk.y * world.CHUNK_SIZE + sy
                           print("Placing structure: "..structure.name.." at "..worldX..","..worldY)
                           if world.chunkSystem and world.chunkSystem.placeStructure then
                                world.chunkSystem:placeStructure(chunk, structure.name, worldX, worldY)
                           else
                               world.entitySystem:addEntity("structure_marker", worldX, worldY, { structureType = structure.name })
                           end
                           if structure.entities then
                                for _, et in ipairs(structure.entities) do
                                     world.entitySystem:addEntity(et, worldX + math.random(-1,1), worldY + math.random(-1,1))
                                end
                           end
                      end
                  end
             end
         end
    end,

    postProcess = function(chunk, world, variant)
        print("Post-processing Sky Islands chunk...")
        -- Apply wind effects, maybe connect nearby islands with bridges? Check for bottomless drop edges.
         local variantData = variant and SkyIslandsBiome.variants[variant] or nil
         local windStrength = variantData and variantData.windStrength or SkyIslandsBiome.environment.windStrength
         chunk.environmentalEffects = chunk.environmentalEffects or {}
         table.insert(chunk.environmentalEffects, {type="high_altitude_wind", strength=windStrength})
         table.insert(chunk.environmentalEffects, {type="falling_hazard"}) -- General hazard

         if variantData and variantData.specialEffects then
             for _, effect in ipairs(variantData.specialEffects) do table.insert(chunk.environmentalEffects, effect) end
         end

         -- Add edge tiles ('bottomless_drop') around islands
         for x=0, world.CHUNK_SIZE-1 do for y=0, world.CHUNK_SIZE-1 do
             local tile = chunk.tiles[x][y]
             if tile.isIslandSurface then
                  -- Check neighbors
                  for dx=-1,1 do for dy=-1,1 do
                       if dx==0 and dy==0 then goto next_neighbor end
                       local nx, ny = x+dx, y+dy
                       if nx<0 or nx>=world.CHUNK_SIZE or ny<0 or ny>=world.CHUNK_SIZE or not chunk.tiles[nx][ny].isIslandSurface then
                            -- This is an edge tile next to non-island space
                            tile.isEdge = true
                            -- Optionally change type to island_edge if desired, or just use flag
                            goto next_tile -- Stop checking neighbors for this tile
                       end
                       ::next_neighbor::
                  end end
             end
             ::next_tile::
         end end

    end,

    init = function(worldCore)
        print("Sky Islands biome module initialized")
        SkyIslandsBiome.worldCore = worldCore
        print("Sky Islands biome registered successfully for later use")
    end,

    registerWithWorld = function(world)
        print("Registering Sky Islands biome generators with world")
        if not world or not world.chunkSystem then print("Warning: Cannot register Sky Islands biome - world or chunkSystem is nil") return false end
        world.chunkSystem:registerGenerator(SkyIslandsBiome.id, SkyIslandsBiome.generate)
        for variantId, variantData in pairs(SkyIslandsBiome.variants) do
            local fullVariantId = SkyIslandsBiome.id .. "_" .. variantId
            world.chunkSystem:registerGenerator(fullVariantId, function(chunk, worldInstance)
                SkyIslandsBiome.generate(chunk, worldInstance, variantId)
            end)
        end
        return true
    end,
}

return SkyIslandsBiome