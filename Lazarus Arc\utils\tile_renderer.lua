-- utils/tile_renderer.lua
-- Unified interface for tile rendering that routes to appropriate system based on configuration

local TileSystemConfig = require("utils.tile_system_config")
local TileVisuals = require("utils.tile_visuals")
local LODSystem = require("utils.lod_system")
local TileDefinitions = require("tiles.tile_definitions")
local TileDecorations = require("tiles.tile_decorations")

local TileRenderer = {
    initialized = false,
    performanceMetrics = {
        lastFrameTime = 0,
        tilesDrawn = 0,
        systemSwitches = 0
    }
}

-- Initialize the tile renderer
function TileRenderer:init(config)
    config = config or {}
    
    -- Initialize the configuration system
    TileSystemConfig:init(config)
    
    -- Initialize subsystems
    if not LODSystem.initialized then
        LODSystem.init(config.lod or {})
    end
    
    -- Load tile definitions
    TileDefinitions:loadAll()
    
    self.initialized = true
    print("TileRenderer initialized with system: " .. TileSystemConfig:getActiveSystem())
    
    return self
end

-- Main tile rendering function - routes to appropriate system
function TileRenderer:drawWorldTiles(world, player, renderer)
    if not self.initialized then
        print("Warning: TileRenderer not initialized, using fallback")
        return self:_drawFallbackTiles(world, player, renderer)
    end
    
    local startTime = love.timer.getTime()
    local activeSystem = TileSystemConfig:getActiveSystem()
    
    -- Reset performance metrics
    self.performanceMetrics.tilesDrawn = 0
    
    -- Route to appropriate system
    if activeSystem == TileSystemConfig.SYSTEMS.COMPLEX then
        self:_drawComplexTiles(world, player, renderer)
    elseif activeSystem == TileSystemConfig.SYSTEMS.SIMPLE then
        self:_drawSimpleTiles(world, player, renderer)
    elseif activeSystem == TileSystemConfig.SYSTEMS.DEFINITIONS then
        self:_drawDefinitionTiles(world, player, renderer)
    else
        print("Warning: Unknown tile system '" .. activeSystem .. "', using fallback")
        self:_drawFallbackTiles(world, player, renderer)
    end
    
    -- Update performance metrics
    self.performanceMetrics.lastFrameTime = love.timer.getTime() - startTime
    
    -- Show debug info if enabled
    if TileSystemConfig.debug.enabled and TileSystemConfig.debug.showPerformanceMetrics then
        self:_drawDebugInfo()
    end
end

-- Complex system rendering (TileVisuals + TileDecorations)
function TileRenderer:_drawComplexTiles(world, player, renderer)
    if not world.chunkSystem then
        print("Warning: Complex tile system requires chunk system, falling back to simple")
        return self:_drawSimpleTiles(world, player, renderer)
    end
    
    local playerX, playerY = 0, 0
    if player and player.position then
        playerX = player.position.x
        playerY = player.position.y
    end
    
    local cs = world.chunkSystem
    local visibleRange = renderer.simplifiedTiles and 20 or 35
    local season = world.season or "summer"
    
    local minX = playerX - visibleRange
    local maxX = playerX + visibleRange
    local minY = playerY - visibleRange
    local maxY = playerY + visibleRange
    
    -- Collect tiles for rendering
    local regularTiles = {}
    local waterBatches = {}
    
    for x = minX, maxX do
        for y = minY, maxY do
            local tile = cs:getTileAt(x, y)
            if tile then
                local isoX, isoY
                if renderer.colorUtils and renderer.colorUtils.toIsometric then
                    isoX, isoY = renderer.colorUtils.toIsometric(x, y)
                else
                    isoX = (x - y) * (renderer.tileWidth / 2)
                    isoY = (x + y) * (renderer.tileHeight / 2)
                end
                
                -- Check if tile is water for batching
                if tile.type == "water" or tile.biome == "ocean" then
                    -- Add to water batch (simplified for now)
                    table.insert(waterBatches, {tile = tile, isoX = isoX, isoY = isoY})
                else
                    table.insert(regularTiles, {tile = tile, isoX = isoX, isoY = isoY})
                end
                
                self.performanceMetrics.tilesDrawn = self.performanceMetrics.tilesDrawn + 1
            end
        end
    end
    
    -- Draw water tiles first (background)
    for _, tileInfo in ipairs(waterBatches) do
        TileVisuals.draw(tileInfo.tile, tileInfo.isoX, tileInfo.isoY, 
                        renderer.tileWidth, renderer.tileHeight, season, cs)
    end
    
    -- Draw regular tiles
    for _, tileInfo in ipairs(regularTiles) do
        TileVisuals.draw(tileInfo.tile, tileInfo.isoX, tileInfo.isoY, 
                        renderer.tileWidth, renderer.tileHeight, season, cs)
    end
end

-- Simple system rendering (LODSystem)
function TileRenderer:_drawSimpleTiles(world, player, renderer)
    local playerX, playerY = 0, 0
    if player and player.position then
        playerX = player.position.x
        playerY = player.position.y
    end
    
    local visibleRange = renderer.simplifiedTiles and 20 or 35
    
    -- Use LOD system for rendering
    LODSystem.drawTilesAroundPlayer(playerX, playerY, visibleRange, 
                                   renderer.drawingSystem or require("utils.drawing_system"), 
                                   renderer.colorUtils)
    
    -- Estimate tiles drawn (LOD system doesn't provide exact count)
    self.performanceMetrics.tilesDrawn = math.floor((visibleRange * 2) ^ 2 * 0.7)
end

-- Tile definitions system rendering
function TileRenderer:_drawDefinitionTiles(world, player, renderer)
    local playerX, playerY = 0, 0
    if player and player.position then
        playerX = player.position.x
        playerY = player.position.y
    end
    
    local visibleRange = renderer.simplifiedTiles and 20 or 35
    local minX = playerX - visibleRange
    local maxX = playerX + visibleRange
    local minY = playerY - visibleRange
    local maxY = playerY + visibleRange
    
    for x = minX, maxX do
        for y = minY, maxY do
            -- Get tile type (use chunk system if available, otherwise generate)
            local tileType = "grass" -- default
            if world.chunkSystem then
                local tile = world.chunkSystem:getTileAt(x, y)
                if tile and tile.type then
                    tileType = tile.type
                end
            end
            
            -- Draw using tile definitions
            TileDefinitions:drawTile(renderer, tileType, x, y, 
                                   renderer.tileWidth, renderer.tileHeight, {})
            
            self.performanceMetrics.tilesDrawn = self.performanceMetrics.tilesDrawn + 1
        end
    end
end

-- Fallback rendering
function TileRenderer:_drawFallbackTiles(world, player, renderer)
    print("Using fallback tile rendering")
    -- Use simple LOD system as fallback
    if not LODSystem.initialized then
        LODSystem.init()
    end
    return self:_drawSimpleTiles(world, player, renderer)
end

-- Draw debug information
function TileRenderer:_drawDebugInfo()
    local debugInfo = TileSystemConfig:getDebugInfo()
    local metrics = self.performanceMetrics
    
    love.graphics.setColor(1, 1, 1, 0.8)
    love.graphics.print("Tile System: " .. debugInfo.activeSystem, 10, 10)
    love.graphics.print("Frame Time: " .. string.format("%.2fms", metrics.lastFrameTime * 1000), 10, 30)
    love.graphics.print("Tiles Drawn: " .. metrics.tilesDrawn, 10, 50)
    love.graphics.print("Press T to toggle systems", 10, 70)
end

-- Switch to next tile system (for debugging)
function TileRenderer:switchSystem()
    local success = TileSystemConfig:toggleSystem()
    if success then
        self.performanceMetrics.systemSwitches = self.performanceMetrics.systemSwitches + 1
        print("Switched to tile system: " .. TileSystemConfig:getActiveSystem())
    end
    return success
end

-- Get current system information
function TileRenderer:getSystemInfo()
    return TileSystemConfig:getDebugInfo()
end

-- Enable/disable debug mode
function TileRenderer:setDebugMode(enabled)
    TileSystemConfig:setDebugMode(enabled)
end

return TileRenderer
