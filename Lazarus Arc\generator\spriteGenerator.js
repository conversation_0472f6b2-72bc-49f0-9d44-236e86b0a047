// Color palettes for different materials
const PALETTES = {
    metal: {
        bronze: ['#CD7F32', '#B87333', '#A66D32', '#8B4513'],
        copper: ['#B87333', '#A66D32', '#8B4513', '#6B4423'],
        iron: ['#A9A9A9', '#808080', '#696969', '#4A4A4A'],
        steel: ['#C0C0C0', '#A9A9A9', '#808080', '#696969']
    },
    wood: {
        oak: ['#8B4513', '#6B4423', '#4A3C2C', '#2D2010'],
        pine: ['#DEB887', '#CD853F', '#8B4513', '#6B4423'],
        maple: ['#D2691E', '#CD853F', '#8B4513', '#6B4423']
    },
    cloth: {
        cotton: ['#F5F5F5', '#E0E0E0', '#C0C0C0', '#A9A9A9'],
        leather: ['#8B4513', '#6B4423', '#4A3C2C', '#2D2010'],
        silk: ['#FFD700', '#FFA500', '#FF8C00', '#FF4500']
    }
};

// Animation templates
const ANIMATION_TEMPLATES = {
    idle: {
        frames: 4,
        duration: 0.5,
        keyframes: [
            { offset: 0, scale: 1.0 },
            { offset: 0.25, scale: 1.05 },
            { offset: 0.5, scale: 1.0 },
            { offset: 0.75, scale: 0.95 }
        ]
    },
    walk: {
        frames: 6,
        duration: 0.4,
        keyframes: [
            { offset: 0, x: 0, y: 0 },
            { offset: 0.17, x: 2, y: -1 },
            { offset: 0.33, x: 4, y: 0 },
            { offset: 0.5, x: 2, y: 1 },
            { offset: 0.67, x: 0, y: 0 },
            { offset: 0.83, x: -2, y: -1 }
        ]
    },
    attack: {
        frames: 4,
        duration: 0.3,
        keyframes: [
            { offset: 0, rotation: 0 },
            { offset: 0.25, rotation: -30 },
            { offset: 0.5, rotation: 45 },
            { offset: 0.75, rotation: 0 }
        ]
    }
};

class SpriteGenerator {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.size = 32;
        this.currentPalette = ['#4CAF50', '#8BC34A', '#CDDC39', '#FFEB3B'];
        this.isShiny = false;
        this.animationFrame = 0;
        this.lastFrameTime = 0;
        this.animationSpeed = 1000 / 8; // 8 FPS
    }

    // Set sprite size
    setSize(size) {
        this.size = size;
        this.canvas.width = size;
        this.canvas.height = size;
    }

    // Set palette colors
    setPalette(colors) {
        this.currentPalette = colors;
    }

    // Toggle shiny effect
    toggleShiny(enabled) {
        this.isShiny = enabled;
    }

    // Clear canvas
    clear() {
        this.ctx.clearRect(0, 0, this.size, this.size);
    }

    // Save sprite as PNG
    saveSprite() {
        return this.canvas.toDataURL('image/png');
    }

    // Export animation frames
    exportAnimation() {
        const frames = [];
        const originalFrame = this.animationFrame;
        
        // Generate frames
        for (let i = 0; i < 8; i++) {
            this.animationFrame = i;
            this.drawBaseShape();
            frames.push(this.canvas.toDataURL('image/png'));
        }
        
        // Restore original frame
        this.animationFrame = originalFrame;
        return frames;
    }

    // Update animation
    updateAnimation(currentTime) {
        if (this.lastFrameTime === 0) {
            this.lastFrameTime = currentTime;
            return;
        }

        const deltaTime = currentTime - this.lastFrameTime;
        if (deltaTime >= this.animationSpeed) {
            this.animationFrame = (this.animationFrame + 1) % 8;
            this.lastFrameTime = currentTime;
            this.drawBaseShape();
        }
    }

    // Set animation template
    setTemplate(template) {
        // Override in child classes
    }

    // Draw base shape (to be implemented by child classes)
    drawBaseShape() {
        // Override in child classes
    }
}

export default SpriteGenerator; 