local NatureBow = {
    id = "nature_bow",
    name = "Nature Bow",
    type = "weapon",
    category = "bow",
    
    -- Properties
    properties = {
        damage = 20,
        attackSpeed = 1.5,
        range = 8.0,
        durability = 180,
        maxDurability = 180,
        arrowSpeed = 12,
        arrowSpread = 0.1,
        vineChance = 0.2,
        vineDamage = 5,
        vineDuration = 3,
        vineRadius = 1.5,
        poisonChance = 0.15,
        poisonDamage = 3,
        poisonDuration = 5,
        poisonRadius = 1.0,
        thornChance = 0.1,
        thornDamage = 8,
        thornCount = 3,
        thornSpread = 0.3,
        rootChance = 0.1,
        rootDuration = 2,
        rootRadius = 2.0,
        natureCharge = 0,
        maxNatureCharge = 100,
        chargeRate = 3,
        dischargeRate = 15,
        natureResistance = 0.4
    },
    
    -- Appearance
    appearance = {
        sprite = "nature_bow",
        scale = 1.0,
        animations = {
            "idle",
            "draw",
            "shoot",
            "charge"
        },
        variants = {
            "oak",
            "maple",
            "willow",
            "elder"
        },
        blendMode = "normal",
        tint = {0.3, 0.6, 0.2},
        alpha = 0.9
    },
    
    -- Sound effects
    sounds = {
        draw = "nature_bow_draw",
        shoot = "nature_bow_shoot",
        vine = "nature_bow_vine",
        charge = "nature_bow_charge"
    },
    
    -- Effects
    effects = {
        vine = {
            type = "status",
            duration = 3,
            effects = {
                damage = 5,
                slow = 0.3,
                radius = 1.5
            }
        },
        poison = {
            type = "status",
            duration = 5,
            effects = {
                damage = 3,
                radius = 1.0
            }
        },
        root = {
            type = "status",
            duration = 2,
            effects = {
                immobilize = true,
                radius = 2.0
            }
        }
    }
}

-- Initialize the weapon
function NatureBow.init(weapon, world)
    -- Copy all fields from NatureBow template to weapon instance
    for k, v in pairs(NatureBow) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        natureCharge = 0,
        lastChargeTime = 0,
        isCharging = false
    }

    return weapon
end

-- Update the weapon
function NatureBow.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Update nature charge
        if world.temperature and world.moisture then
            local chargeFactor = (world.temperature * 0.3 + world.moisture * 0.7) * dt
            weapon.properties.weaponState.natureCharge = math.min(weapon.properties.maxNatureCharge,
                weapon.properties.weaponState.natureCharge + chargeFactor * weapon.properties.chargeRate)
        else
            weapon.properties.weaponState.natureCharge = math.max(0,
                weapon.properties.weaponState.natureCharge - weapon.properties.dischargeRate * dt)
        end
    end
end

-- Handle attack
function NatureBow.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Calculate arrow direction
    local angle = math.atan2(
        target.position.y - attacker.position.y,
        target.position.x - attacker.position.x
    )
    
    -- Add spread to arrow direction
    local spread = (math.random() - 0.5) * weapon.properties.arrowSpread
    angle = angle + spread
    
    -- Create arrow projectile
    if world.createProjectile then
        world.createProjectile({
            type = "nature_arrow",
            position = attacker.position,
            direction = {
                x = math.cos(angle) * weapon.properties.arrowSpeed,
                y = math.sin(angle) * weapon.properties.arrowSpeed
            },
            damage = weapon.properties.damage,
            range = weapon.properties.range
        })
    end
    
    -- Check for vine effect
    if math.random() < weapon.properties.vineChance then
        -- Create vine effect
        if world.createEffect then
            world.createEffect({
                type = "vine",
                position = target.position,
                radius = weapon.properties.vineRadius,
                duration = weapon.properties.vineDuration
            })
        end
        
        -- Apply vine effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.vine)
        end
        
        -- Play vine sound
        if world.playSound then
            world.playSound(weapon.sounds.vine)
        end
    end
    
    -- Check for poison effect
    if math.random() < weapon.properties.poisonChance then
        -- Create poison effect
        if world.createEffect then
            world.createEffect({
                type = "poison",
                position = target.position,
                radius = weapon.properties.poisonRadius,
                duration = weapon.properties.poisonDuration
            })
        end
        
        -- Apply poison effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.poison)
        end
    end
    
    -- Check for thorn effect
    if math.random() < weapon.properties.thornChance then
        -- Create thorn projectiles
        for i = 1, weapon.properties.thornCount do
            local thornAngle = angle + (i - 1) * (weapon.properties.thornSpread / (weapon.properties.thornCount - 1))
            
            if world.createProjectile then
                world.createProjectile({
                    type = "thorn",
                    position = target.position,
                    direction = {
                        x = math.cos(thornAngle) * weapon.properties.arrowSpeed * 0.8,
                        y = math.sin(thornAngle) * weapon.properties.arrowSpeed * 0.8
                    },
                    damage = weapon.properties.thornDamage,
                    range = weapon.properties.range * 0.5
                })
            end
        end
    end
    
    -- Check for root effect
    if math.random() < weapon.properties.rootChance then
        -- Create root effect
        if world.createEffect then
            world.createEffect({
                type = "root",
                position = target.position,
                radius = weapon.properties.rootRadius,
                duration = weapon.properties.rootDuration
            })
        end
        
        -- Apply root effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.root)
        end
    end
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function NatureBow.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough nature charge
    if weapon.properties.weaponState.natureCharge < 50 then
        return false
    end
    
    -- Create nature storm effect
    if world.createEffect then
        world.createEffect({
            type = "nature_storm",
            position = attacker.position,
            radius = weapon.properties.vineRadius * 3,
            duration = 5,
            damage = weapon.properties.damage * 1.5
        })
    end
    
    -- Apply nature effects to nearby entities
    if world.entities then
        for _, entity in ipairs(world.entities) do
            if entity.position then
                local distance = math.sqrt(
                    (entity.position.x - attacker.position.x)^2 + 
                    (entity.position.y - attacker.position.y)^2
                )
                
                if distance <= weapon.properties.vineRadius * 3 then
                    -- Apply random nature effect
                    local effect = math.random()
                    if effect < 0.3 then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.vine)
                        end
                    elseif effect < 0.6 then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.poison)
                        end
                    else
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.root)
                        end
                    end
                end
            end
        end
    end
    
    -- Reset nature charge
    weapon.properties.weaponState.natureCharge = 0
    weapon.properties.weaponState.lastChargeTime = world.time
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 5)
    
    return true
end

return NatureBow 