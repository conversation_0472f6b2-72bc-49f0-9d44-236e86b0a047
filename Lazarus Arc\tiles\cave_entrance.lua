-- tiles/cave_entrance.lua
local CaveEntranceTile = {
    id = "cave_entrance",
    name = "Cave Entrance",
    passable = true,
    variants = 3, -- Different cave entrance patterns
    
    -- Properties
    properties = {
        caveType = nil, -- Set during init
        depth = 0.0,
        darkness = 0.8,
        temperature = 10,
        humidity = 0.9,
        hasStalactites = true,
        hasStalagmites = true,
        hasBats = true,
        hasWater = true
    },
    
    -- Visual properties
    visual = {
        base_color = {0.3, 0.3, 0.3}, -- Dark grey
        variants = {
            {name = "narrow_entrance", type = "narrow"},
            {name = "wide_entrance", type = "wide"},
            {name = "water_entrance", type = "water"}
        },
        cave_features = {
            {name = "stalactite", type = "ceiling"},
            {name = "stalagmite", type = "floor"},
            {name = "water_pool", type = "water"}
        }
    },
    
    -- Sound properties
    sounds = {
        ambient = {
            "cave_wind",
            "water_dripping",
            "bat_squeaking"
        },
        footsteps = {
            "stone_step",
            "water_splash"
        }
    },
    
    -- Resources
    resources = {
        water = {
            amount = 5,
            regenRate = 0.2,
            harvestAmount = 1
        },
        minerals = {
            amount = 3,
            regenRate = 0.1,
            harvestAmount = 1
        }
    },
    
    -- Effects
    effects = {
        darkness = {
            active = true,
            intensity = 0.8,
            radius = 3
        },
        water = {
            active = true,
            flow = "dripping",
            amount = 0.5
        }
    },
    
    -- Spawn rules
    spawnRules = {
        bats = {
            chance = 0.6,
            minDistance = 2,
            maxDensity = 0.4
        },
        water_pools = {
            chance = 0.4,
            minDistance = 3,
            maxDensity = 0.3
        },
        minerals = {
            chance = 0.3,
            minDistance = 4,
            maxDensity = 0.2
        }
    },
    
    -- Weather effects
    weatherEffects = {
        rain = function(tile)
            -- Rain can flood the entrance
            tile.properties.water = math.min(1.0, tile.properties.water + 0.2)
            return "water_accumulate"
        end,
        snow = function(tile)
            -- Snow can block the entrance
            if tile.properties.temperature < 0 then
                tile.properties.depth = math.min(1.0, tile.properties.depth + 0.1)
                return "entrance_block"
            end
            return nil
        end,
        wind = function(tile)
            -- Wind can affect cave temperature
            tile.properties.temperature = math.max(5, tile.properties.temperature - 1)
            return "temperature_drop"
        end
    },
    
    -- Time effects
    timeEffects = {
        day = function(tile)
            -- Daytime can warm the entrance
            tile.properties.temperature = math.min(15, tile.properties.temperature + 1)
            return "temperature_rise"
        end,
        night = function(tile)
            -- Nighttime cools the entrance
            tile.properties.temperature = math.max(5, tile.properties.temperature - 2)
            return "temperature_drop"
        end
    }
}

-- Initialize the tile
function CaveEntranceTile.init(tile, world)
    -- Initialize properties first
    tile.properties = tile.properties or {}
    
    -- Set random cave type if not set
    if not tile.properties.caveType then
        local caveTypes = {"limestone", "crystal", "ice", "lava", "water"}
        tile.properties.caveType = caveTypes[math.random(#caveTypes)]
    end
    
    -- Copy all fields from CaveEntranceTile template to tile instance
    for k, v in pairs(CaveEntranceTile) do
        if type(v) ~= "function" and tile[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                tile[k] = {}
                for subk, subv in pairs(v) do
                    tile[k][subk] = subv
                end
            else
                tile[k] = v
            end
        end
    end
    
    -- Set random variant
    if tile.visual and tile.visual.variants then
        local variants = tile.visual.variants
        tile.currentVariant = variants[math.random(#variants)]
    end
    
    -- Initialize resources
    if tile.resources then
        for resourceType, resource in pairs(tile.resources) do
            resource.currentAmount = resource.amount
            resource.lastHarvest = 0
            resource.growthStage = "mature"
        end
    end
    
    return tile
end

return CaveEntranceTile 