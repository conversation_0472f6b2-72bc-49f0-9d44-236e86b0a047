local AncientTreant = {
    id = "ancient_treant",
    name = "Ancient Treant",
    type = "ancient_treant",
    shape = {
        {0, -20}, {15, -15}, {20, 0}, {15, 15},
        {0, 20}, {-15, 15}, {-20, 0}, {-15, -15}
    },
    size = 20,
    categories = {"plant", "monster", "boss"},
    threatCategories = {"hostile"},
    foodCategories = {},
    maxHealth = 300,
    health = 300,
    speed = 0.6,
    armor = 0.3,
    behaviors = {"guard", "summon"},
    behaviorConfigs = {
        guard = {
            guardRadius = 50,
            alertRadius = 70,
            protectedTypes = {"tree", "bush", "forest_golem"},
            attackRadius = 5,
            attackCooldown = 4,
            threatTypes = {"animal", "player", "monster"}
        },
        summon = {
            summonTypes = {"forest_golem", "wolf"},
            summonCooldown = 30,
            maxSummons = 5
        }
    },
    abilities = {
        regeneration = { regenerationRate = 0.1 },
        tremor = {},
        rootBarrage = {}
    },
    appearance = {
        sprite = "ancient_treant",
        scale = 2.0,
        animations = {"idle", "walk", "attack", "summon", "take_damage"}
    },
    -- Sound effects with synth configuration (deep, ancient, mystical sounds)
    sounds = {
        step = {
            file = "treant_step",
            synth = {
                instrument = "bass_guitar",
                notes = {"C1", "G1"},
                durations = {0.3, 0.4},
                volume = 0.6,
                vibrato = true,
                vibratoRate = 2.0
            }
        },
        attack = {
            file = "treant_attack",
            synth = {
                instrument = "bass_guitar",
                notes = {"E1", "C2", "G2"},
                durations = {0.4, 0.3, 0.5},
                volume = 0.7,
                vibrato = true,
                vibratoRate = 3.0
            }
        },
        summon = {
            file = "treant_summon",
            synth = {
                instrument = "vibraphone",
                notes = {"C3", "E3", "G3", "C4", "E4"},
                durations = {0.5, 0.5, 0.5, 0.5, 0.8},
                volume = 0.5,
                vibrato = true,
                vibratoRate = 4.0
            }
        },
        groan = {
            file = "treant_groan",
            synth = {
                instrument = "bass_guitar",
                notes = {"G1", "E1", "C1"},
                durations = {0.6, 0.6, 1.0},
                volume = 0.6,
                vibrato = true,
                vibratoRate = 1.5
            }
        },
        rustle = {
            synth = {
                instrument = "classical_guitar",
                notes = {"G3", "A3", "C4"},
                durations = {0.3, 0.3, 0.4},
                volume = 0.3,
                vibrato = true,
                vibratoRate = 6.0
            }
        },
        hurt = {
            synth = {
                instrument = "bass_guitar",
                notes = {"D#1"},
                duration = 0.8,
                volume = 0.7,
                vibrato = true,
                vibratoRate = 4.0
            }
        },
        death = {
            synth = {
                instrument = "bass_guitar",
                notes = {"C1", "G0", "E0", "C0"},
                durations = {1.0, 1.0, 1.0, 2.0},
                volume = 0.8,
                vibrato = true,
                vibratoRate = 1.0
            }
        },
        magic = {
            synth = {
                instrument = "vibraphone",
                notes = {"E4", "G4", "B4", "E5"},
                durations = {0.4, 0.4, 0.4, 0.6},
                volume = 0.4,
                vibrato = true,
                vibratoRate = 5.0
            }
        }
    },
    drops = {
        { id = "ancient_heartwood", chance = 1.0, quantity = {1, 1} },
        { id = "living_vine", chance = 0.8, quantity = {3, 7} },
        { id = "treant_sap", chance = 0.5, quantity = {2, 4} }
    }
}

function AncientTreant.init(entity, worldCore)
    -- First, handle case where init is called during module registration
    -- by world_core.lua line 77: module.init(module, WorldCore)
    if entity == AncientTreant and worldCore then
        -- This is a module registration call, not an entity initialization call
        -- Can store WorldCore reference if needed
        AncientTreant.worldCore = worldCore
        return AncientTreant
    end
    
    -- Regular entity initialization - Copy properties from template to entity
    for key, value in pairs(AncientTreant) do
        if entity[key] == nil and key ~= "init" then
            entity[key] = value
        end
    end
    
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}
    entity.behaviors = entity.behaviors or {}
    
    -- Initialize behaviors with safety check
    if worldCore and worldCore.modules and worldCore.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = worldCore.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end
    
    return entity
end

return AncientTreant