local SleepTrap = {
    id = "sleep_trap",
    name = "Sleep Trap",
    type = "trap",
    
    -- Entity categories
    categories = {"trap", "dungeon", "magic"},
    
    -- Target categories
    targetCategories = {"player", "monster"},
    
    -- Stats
    maxHealth = 15,
    health = 15,
    duration = 0, -- Instant effect
    cooldown = 45,
    
    -- Trap properties
    properties = {
        radius = 2.0,
        setupTime = 2,
        isArmed = false,
        isTriggered = false,
        destroyOnActivate = false,
        sleepDuration = 8,
        wakeChance = 0.3,
        vulnerabilityAmount = 0.5
    },
    
    -- Appearance
    appearance = {
        sprite = "sleep_trap",
        scale = 1.0,
        animations = {
            "idle", "trigger", "sleep"
        },
        variants = {
            "crystal", "rune", "mist", "dream"
        }
    },
    
    -- Sound effects
    sounds = {
        trigger = "sleep_trap_trigger",
        sleep = "sleep_trap_sleep",
        wake = "sleep_trap_wake"
    },
    
    -- Effects
    effects = {
        sleep = {
            type = "status",
            duration = 8,
            effects = {
                sleep = true,
                vulnerability = 0.5,
                wakeChance = 0.3
            }
        }
    }
}

-- Initialize the trap
function SleepTrap.init(entity, world)
    -- Copy all fields from SleepTrap template to entity instance
    for k, v in pairs(SleepTrap) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position
    entity.position = entity.position or {x = 0, y = 0}

    -- Set random variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize trap state
    entity.properties.isArmed = false
    entity.properties.isTriggered = false
    entity.properties.triggerTime = 0
    entity.properties.sleepingTargets = {}

    return entity
end

-- Update the trap
function SleepTrap.update(entity, world, dt)
    -- Check if trap is armed and not triggered
    if entity.properties.isArmed and not entity.properties.isTriggered then
        -- Check for targets in radius
        if world and world.entities then
            for _, target in ipairs(world.entities) do
                if target.position and target.categories then
                    -- Check if target is in trap categories
                    local isTarget = false
                    for _, category in ipairs(entity.targetCategories) do
                        if table.contains(target.categories, category) then
                            isTarget = true
                            break
                        end
                    end
                    
                    if isTarget then
                        local distance = math.sqrt(
                            (target.position.x - entity.position.x)^2 + 
                            (target.position.y - entity.position.y)^2
                        )
                        
                        if distance <= entity.properties.radius then
                            -- Trigger trap
                            entity.properties.isTriggered = true
                            entity.properties.triggerTime = world.time
                            
                            -- Add target to sleeping list
                            table.insert(entity.properties.sleepingTargets, target)
                            
                            -- Apply sleep effect to target
                            if target.applyEffect then
                                target.applyEffect(entity.effects.sleep)
                            end
                            
                            -- Play trigger sound
                            if world.playSound then
                                world.playSound(entity.sounds.trigger)
                            end
                            
                            break
                        end
                    end
                end
            end
        end
    end
    
    -- Handle sleeping targets
    if entity.properties.isTriggered then
        for i = #entity.properties.sleepingTargets, 1, -1 do
            local target = entity.properties.sleepingTargets[i]
            if target.position then
                -- Check if target should wake up
                if math.random() < entity.properties.wakeChance then
                    -- Remove sleep effect
                    if target.removeEffect then
                        target.removeEffect("sleep")
                    end
                    
                    -- Play wake sound
                    if world.playSound then
                        world.playSound(entity.sounds.wake)
                    end
                    
                    -- Remove from sleeping list
                    table.remove(entity.properties.sleepingTargets, i)
                end
            end
        end
    end
    
    -- Check if trap can be rearmed
    if not entity.properties.isArmed and world.time - entity.properties.triggerTime >= entity.cooldown then
        entity.properties.isArmed = true
        entity.properties.isTriggered = false
        entity.properties.sleepingTargets = {}
    end
end

-- Arm the trap
function SleepTrap.arm(entity, world)
    if not entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = true
        entity.properties.triggerTime = world.time
        entity.properties.sleepingTargets = {}
        return true
    end
    return false
end

-- Disarm the trap
function SleepTrap.disarm(entity, world)
    if entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = false
        entity.properties.triggerTime = world.time
        entity.properties.sleepingTargets = {}
        return true
    end
    return false
end

return SleepTrap 