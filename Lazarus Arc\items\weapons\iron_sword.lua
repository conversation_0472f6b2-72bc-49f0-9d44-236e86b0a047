-- items/weapons/iron_sword.lua

local Weapon = {
    id = "iron_sword",
    name = "Iron Sword",
    description = "A standard iron sword, reliable and effective in combat.",
    type = "sword",
    category = "weapon",
    stackable = false,

    stats = {
        attack_power = 10.0,
        attack_speed = 1.0,
        durability = 100.0,
        repair_cost = 25,
        weight = 5.0,
    },

    damage_type = "physical",
    material = "iron",

    sprite = "res://Sprite/items/weapons/iron_sword.png",
    size = { width = 64, height = 64 },

    effects = {
        critical_chance = 0.05
    },

    meta = {
        rarity = "common"
    },

    take_damage = function(self, amount)
        self.stats.durability = math.max(0, self.stats.durability - amount)
        if self.stats.durability == 0 then
            print(self.name .. " is broken! Penalties applied.")
            -- Potential penalties could be applied here
        end
    end,

    repair = function(self)
        local repair_amount = 100 - self.stats.durability
        self.stats.durability = 100
        print(self.name .. " repaired by " .. repair_amount .. " points.")
    end
}

return Weapon
