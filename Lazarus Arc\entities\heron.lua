local Heron = {
    id = "heron",
    name = "Heron",
    type = "bird",
    
    -- Entity categories
    categories = {"animal", "bird", "flying", "water", "wading"},
    
    -- Threat and food categories
    threatCategories = {"player", "predator"},
    foodCategories = {"fish", "frog", "insect", "small_prey"},
    
    -- Stats
    maxHealth = 35,
    health = 35,
    maxStamina = 60,
    stamina = 60,
    speed = 1.8,
    
    -- Flight properties
    flight = {
        maxHeight = 12,
        minHeight = 1,
        ascentSpeed = 0.7,
        descentSpeed = 1.2,
        hoverHeight = 3,
        currentHeight = 3,
        wingFlapRate = 0.1,
        soarChance = 0.3
    },
    
    -- Water properties
    water = {
        wadeDepth = 2,
        swimSpeed = 1.5,
        fishingRange = 3,
        fishingSuccess = 0.7
    },
    
    -- Behaviors
    behaviors = {"fish", "patrol", "roost", "wade"},
    
    -- Behavior configurations
    behaviorConfigs = {
        fish = {
            moveSpeed = 1.0,
            searchRadius = 15,
            strikeSpeed = 2.0,
            attackRange = 2,
            damage = 15,
            preferredPrey = {"fish", "frog"},
            patience = 10,
            successChance = 0.7
        },
        patrol = {
            moveSpeed = 1.8,
            patrolRadius = 30,
            soarChance = 0.3,
            restInterval = {10, 15},
            preferredTime = "dawn"
        },
        roost = {
            startTime = "dusk",
            endTime = "dawn",
            roostHeight = {2, 4},
            healthRegen = 0.03,
            staminaRegen = 0.1
        },
        wade = {
            moveSpeed = 1.2,
            maxDepth = 2,
            preferredTerrain = "water",
            searchRadius = 8
        }
    },
    
    -- Special abilities
    abilities = {
        strike = {
            speed = 2.0,
            damage = 15,
            duration = 0.5,
            cooldown = 2,
            staminaCost = 5
        },
        spear = {
            damage = 20,
            duration = 1,
            cooldown = 3,
            staminaCost = 10
        },
        alert = {
            range = 12,
            duration = 1,
            cooldown = 4,
            effect = "flee"
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "heron",
        scale = 1.1,
        animations = {
            "idle", "fly", "wade", "fish", "strike", "roost"
        },
        variants = {
            "blue", "gray", "white", "black"
        }
    },
    
    -- Sound effects
    sounds = {
        call = "heron_call",
        wingFlap = "heron_wing_flap",
        alert = "heron_alert"
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.6, quantity = {1, 2}},
        {id = "feather", chance = 0.7, quantity = {2, 3}},
        {id = "beak", chance = 0.3, quantity = {1, 1}},
        {id = "fish", chance = 0.4, quantity = {1, 1}}
    }
}

-- Initialize the entity
function Heron.init(entity, world)
    -- Copy all fields from Heron template to entity instance
    for k, v in pairs(Heron) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random heron variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Heron.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update flight properties
    if entity.flight then
        -- Update wing flapping based on height and activity
        if entity.flight.currentHeight > entity.flight.minHeight then
            if entity.flight.soarChance > math.random() then
                entity.flight.wingFlapRate = 0.05
            else
                entity.flight.wingFlapRate = 0.1
            end
        else
            entity.flight.wingFlapRate = 0.2
        end
    end

    -- Update water properties
    if entity.water then
        -- Adjust fishing success based on time of day
        if world and world.timeOfDay == "dawn" or world.timeOfDay == "dusk" then
            entity.water.fishingSuccess = 0.8
        else
            entity.water.fishingSuccess = 0.7
        end
    end
end

return Heron 