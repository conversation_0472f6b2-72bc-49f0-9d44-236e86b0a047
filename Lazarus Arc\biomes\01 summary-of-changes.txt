## Changes Required to Fix the Biome Loading System

### 1. For Each Biome File (already modified):
- **forest-biome.lua** - Updated
- **plains-biome.lua** - Updated
- **hub-biome.lua** - Updated
- **ocean-biome.lua** - Updated

For each biome module, make these changes:
1. Change the `init` function to store the WorldCore reference but not access it
2. Add a new `registerWithWorld` function to handle registration with a specific world instance

### 2. In world_core.lua (already modified):
- Updated the `createWorld` function to call `registerWithWorld` for each biome after the world is created
- This ensures biomes are registered with each world instance rather than trying to access a non-existent world during loading

### 3. What These Changes Accomplish:
- Separates the module loading phase from the world creation phase
- Avoids trying to access `world.chunkSystem` when it doesn't exist yet
- Ensures each biome can register its functionality with a specific world instance

### 4. For Any Additional Biome Files:
If there are other biome files not covered, follow the same pattern:

```lua
-- Initialize the biome module 
function [BiomeName].init(worldCore)
    print("[Biome] biome module initialized")
    
    -- Store reference to WorldCore for later use
    [BiomeName].worldCore = worldCore
    
    -- Don't try to register generators at init time
    print("[Biome] biome registered successfully")
end

-- Add a new function to register generators with a specific world instance
function [BiomeName].registerWithWorld(world)
    print("Registering [biome] generators with world")
    
    if not world or not world.chunkSystem then
        print("Warning: Cannot register [biome] - world or chunkSystem is nil")
        return false
    end
    
    -- Register generation algorithms with the world
    world.chunkSystem:registerGenerator("[biome]", [BiomeName].generate)
    
    -- Register biome variants if any
    for variantId, variant in pairs([BiomeName].variants) do
        world.chunkSystem:registerGenerator("[biome]_" .. variantId, function(chunk, world)
            [BiomeName].generate(chunk, world, variantId)
        end)
    end
    
    return true
end
```

### 5. Final Testing:
After making these changes, start the game and observe if:
- All biomes load properly without errors
- Chunks generate correctly
- World generation features work as expected
