local ThunderHammer = {
    id = "thunder_hammer",
    name = "Thunder Hammer",
    type = "weapon",
    category = "hammer",
    
    -- Properties
    properties = {
        damage = 35,
        attackSpeed = 0.8,
        range = 1.2,
        durability = 150,
        maxDurability = 150,
        thunderChance = 0.25,
        thunderDamage = 15,
        thunderRadius = 2.0,
        chainLightningChance = 0.2,
        chainLightningDamage = 10,
        chainLightningJumps = 3,
        chainLightningRange = 3.0,
        stunChance = 0.15,
        stunDuration = 1.0,
        shockwaveChance = 0.1,
        shockwaveDamage = 20,
        shockwaveRadius = 2.5,
        shockwaveForce = 2.0,
        thunderCharge = 0,
        maxThunderCharge = 100,
        chargeRate = 5,
        dischargeRate = 20,
        thunderResistance = 0.3
    },
    
    -- Appearance
    appearance = {
        sprite = "thunder_hammer",
        scale = 1.0,
        animations = {
            "idle",
            "swing",
            "thunder",
            "discharge"
        },
        variants = {
            "copper",
            "bronze",
            "steel",
            "crystal"
        },
        blendMode = "add",
        tint = {0.8, 0.9, 1.0},
        alpha = 0.9
    },
    
    -- Sound effects
    sounds = {
        swing = "thunder_hammer_swing",
        hit = "thunder_hammer_hit",
        thunder = "thunder_hammer_thunder",
        discharge = "thunder_hammer_discharge"
    },
    
    -- Effects
    effects = {
        thunder = {
            type = "status",
            duration = 0.5,
            effects = {
                damage = 15,
                radius = 2.0
            }
        },
        stun = {
            type = "status",
            duration = 1.0,
            effects = {
                immobilize = true
            }
        },
        shockwave = {
            type = "status",
            duration = 0.3,
            effects = {
                damage = 20,
                knockback = 2.0
            }
        }
    }
}

-- Initialize the weapon
function ThunderHammer.init(weapon, world)
    -- Copy all fields from ThunderHammer template to weapon instance
    for k, v in pairs(ThunderHammer) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        thunderCharge = 0,
        lastDischargeTime = 0
    }

    return weapon
end

-- Update the weapon
function ThunderHammer.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Update thunder charge
        if world.weather and world.weather.type == "storm" then
            weapon.properties.weaponState.thunderCharge = math.min(weapon.properties.maxThunderCharge,
                weapon.properties.weaponState.thunderCharge + weapon.properties.chargeRate * dt)
        else
            weapon.properties.weaponState.thunderCharge = math.max(0,
                weapon.properties.weaponState.thunderCharge - weapon.properties.dischargeRate * dt)
        end
    end
end

-- Handle attack
function ThunderHammer.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Apply base damage
    if target.takeDamage then
        target.takeDamage(weapon.properties.damage)
    end
    
    -- Check for thunder effect
    if math.random() < weapon.properties.thunderChance then
        -- Apply thunder effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.thunder)
        end
        
        -- Create thunder effect
        if world.createEffect then
            world.createEffect({
                type = "thunder",
                position = target.position,
                radius = weapon.properties.thunderRadius
            })
        end
        
        -- Play thunder sound
        if world.playSound then
            world.playSound(weapon.sounds.thunder)
        end
    end
    
    -- Check for chain lightning
    if math.random() < weapon.properties.chainLightningChance then
        local currentTarget = target
        local jumps = 0
        
        while jumps < weapon.properties.chainLightningJumps do
            -- Find next target
            local nextTarget = nil
            local closestDistance = weapon.properties.chainLightningRange
            
            if world.entities then
                for _, entity in ipairs(world.entities) do
                    if entity.position and entity ~= currentTarget then
                        local distance = math.sqrt(
                            (entity.position.x - currentTarget.position.x)^2 + 
                            (entity.position.y - currentTarget.position.y)^2
                        )
                        
                        if distance < closestDistance then
                            closestDistance = distance
                            nextTarget = entity
                        end
                    end
                end
            end
            
            if nextTarget then
                -- Apply chain lightning damage
                if nextTarget.takeDamage then
                    nextTarget.takeDamage(weapon.properties.chainLightningDamage)
                end
                
                -- Create chain lightning effect
                if world.createEffect then
                    world.createEffect({
                        type = "chain_lightning",
                        start = currentTarget.position,
                        target = nextTarget.position
                    })
                end
                
                currentTarget = nextTarget
                jumps = jumps + 1
            else
                break
            end
        end
    end
    
    -- Check for stun
    if math.random() < weapon.properties.stunChance then
        -- Apply stun effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.stun)
        end
    end
    
    -- Check for shockwave
    if math.random() < weapon.properties.shockwaveChance then
        -- Create shockwave effect
        if world.createEffect then
            world.createEffect({
                type = "shockwave",
                position = target.position,
                radius = weapon.properties.shockwaveRadius,
                damage = weapon.properties.shockwaveDamage,
                force = weapon.properties.shockwaveForce
            })
        end
        
        -- Apply knockback to nearby entities
        if world.entities then
            for _, entity in ipairs(world.entities) do
                if entity.position then
                    local distance = math.sqrt(
                        (entity.position.x - target.position.x)^2 + 
                        (entity.position.y - target.position.y)^2
                    )
                    
                    if distance <= weapon.properties.shockwaveRadius then
                        local force = (1 - distance / weapon.properties.shockwaveRadius) * 
                            weapon.properties.shockwaveForce
                        
                        if entity.move then
                            local angle = math.atan2(
                                entity.position.y - target.position.y,
                                entity.position.x - target.position.x
                            )
                            entity.move({
                                x = math.cos(angle) * force,
                                y = math.sin(angle) * force
                            })
                        end
                    end
                end
            end
        end
    end
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function ThunderHammer.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough thunder charge
    if weapon.properties.weaponState.thunderCharge < 50 then
        return false
    end
    
    -- Create thunder storm effect
    if world.createEffect then
        world.createEffect({
            type = "thunder_storm",
            position = attacker.position,
            radius = weapon.properties.thunderRadius * 2,
            duration = 3,
            damage = weapon.properties.damage * 2
        })
    end
    
    -- Apply thunder effects to nearby entities
    if world.entities then
        for _, entity in ipairs(world.entities) do
            if entity.position then
                local distance = math.sqrt(
                    (entity.position.x - attacker.position.x)^2 + 
                    (entity.position.y - attacker.position.y)^2
                )
                
                if distance <= weapon.properties.thunderRadius * 2 then
                    if entity.takeDamage then
                        entity.takeDamage(weapon.properties.thunderDamage)
                    end
                    
                    if entity.applyEffect then
                        entity.applyEffect(weapon.effects.thunder)
                    end
                end
            end
        end
    end
    
    -- Reset thunder charge
    weapon.properties.weaponState.thunderCharge = 0
    weapon.properties.weaponState.lastDischargeTime = world.time
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 5)
    
    return true
end

return ThunderHammer 