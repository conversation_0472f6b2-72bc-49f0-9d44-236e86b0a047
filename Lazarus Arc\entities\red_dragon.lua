-- entities/red_dragon.lua
-- Red dragon with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local RedDragon = {
    id = "red_dragon",
    name = "Red Dragon",
    type = "red_dragon",
    shape = {
        {0, -20}, {15, -15}, {25, 0}, {15, 15},
        {0, 20}, {-15, 15}, {-25, 0}, {-15, -15}
    },
    size = 25,  -- Massive dragon size

    -- Entity categories
    categories = {"dragon", "flying", "fire", "ancient", "magical"},
    threatCategories = {"player", "knight", "treasure_hunter", "dragon_slayer"},
    
    -- Base stats (legendary creature)
    maxHealth = 800,
    health = 800,
    maxStamina = 300,
    stamina = 300,
    maxMana = 400,
    mana = 400,
    speed = 3.0,
    attack = 60,
    defense = 40,
    magicAttack = 50,
    magicDefense = 45,
    fireResistance = 100,  -- Immune to fire
    iceVulnerability = 1.5,
    flight_altitude = 50,
    breath_range = 30,
    
    -- Behaviors
    behaviors = {"territorial_flight", "fire_breath", "treasure_hoard", "ancient_wisdom"},
    behaviorConfigs = {
        territorial_flight = {
            territoryRadius = 200,
            patrolAltitude = 40,
            diveBombSpeed = 8.0,
            aerialSupremacy = true
        },
        fire_breath = {
            breathRange = 35,
            breathDamage = 3.0,
            breathCooldown = 8,
            areaEffect = 15,
            igniteChance = 0.8
        },
        treasure_hoard = {
            hoardRadius = 50,
            treasureAttraction = 2.0,
            greedLevel = 5.0,
            hoardDefense = 3.0
        },
        ancient_wisdom = {
            magicKnowledge = 5.0,
            spellcasting = true,
            ancientMemory = 10.0
        }
    },
    
    -- Enhanced variant system for red dragons
    variantChances = {
        normal = 0.50,          -- 50% young red dragon
        shiny = 0.35,           -- 35% adult red dragon (shiny)
        rare = 0.13,            -- 13% ancient red dragon (rare)
        legendary = 0.02        -- 2% red dragon lord (legendary)
    },
    
    variants = {
        normal = {
            name = "Young Red Dragon",
            description = "A fierce young dragon with burning ambition",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {1.2, 0.6, 0.4, 1.0}  -- Bright red scales
            }
        },
        
        shiny = {
            name = "Adult Red Dragon",
            description = "A mature dragon with mastery over flame and fury",
            statModifiers = {
                maxHealth = 1.5,    -- 1200 health
                attack = 1.4,       -- 84 attack
                defense = 1.3,      -- 52 defense
                magicAttack = 1.4,  -- 70 magic attack
                breath_power = 1.6,
                territorial_dominance = 2.0
            },
            appearanceModifiers = {
                scale = 1.3,
                glow = true,
                colorTint = {1.4, 0.5, 0.3, 1.0},  -- Deep crimson with flame aura
                flame_aura = true,
                intimidating_presence = true,
                battle_scars = true
            },
            soundModifiers = {
                pitch = 0.8,
                volume = 1.4,
                commanding = true
            }
        },
        
        rare = {
            name = "Ancient Red Dragon",
            description = "An ancient wyrm with centuries of power and cunning",
            statModifiers = {
                maxHealth = 2.2,    -- 1760 health
                attack = 1.8,       -- 108 attack
                defense = 1.6,      -- 64 defense
                magicAttack = 1.8,  -- 90 magic attack
                breath_power = 2.5,
                ancient_magic = 3.0,
                wisdom = 5.0
            },
            appearanceModifiers = {
                scale = 1.6,
                colorTint = {1.3, 0.4, 0.2, 1.0},  -- Dark crimson with ancient power
                ancient_scars = true,
                weathered_scales = true,
                massive_horns = true,
                ancient_aura = true
            },
            soundModifiers = {
                pitch = 0.6,
                volume = 1.6,
                reverb = true,
                ancient_power = true
            }
        },
        
        legendary = {
            name = "Red Dragon Lord",
            description = "A legendary dragon emperor ruling over all dragonkind",
            statModifiers = {
                maxHealth = 3.0,    -- 2400 health
                attack = 2.5,       -- 150 attack
                defense = 2.0,      -- 80 defense
                magicAttack = 2.5,  -- 125 magic attack
                breath_power = 4.0,
                dragon_lordship = 10.0,
                reality_dominance = 5.0
            },
            appearanceModifiers = {
                scale = 2.0,
                glow = true,
                colorTint = {1.6, 0.3, 0.1, 1.0},  -- Molten gold-red with imperial power
                dragon_crown = true,
                imperial_aura = "legendary",
                reality_distortion = true,
                flame_mastery = true
            },
            soundModifiers = {
                pitch = 0.4,
                volume = 2.0,
                reverb = true,
                echo = true,
                imperial_roar = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "dragon_scale", chance = 1.0, quantity = {5, 10}},
        {id = "dragon_blood", chance = 0.9, quantity = {2, 4}},
        {id = "dragon_fang", chance = 0.8, quantity = {1, 2}},
        {id = "fire_essence", chance = 1.0, quantity = {3, 6}},
        {id = "dragon_heart", chance = 0.7, quantity = {1, 1}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "adult_dragon_scale", chance = 1.0, quantity = {6, 12}},
            {id = "flame_mastery_orb", chance = 0.9, quantity = {1, 1}},
            {id = "territorial_dominance_crystal", chance = 0.8, quantity = {1, 1}},
            {id = "dragon_wing_membrane", chance = 0.7, quantity = {1, 2}}
        },
        rare = {
            {id = "ancient_dragon_scale", chance = 1.0, quantity = {8, 15}},
            {id = "ancient_wisdom_crystal", chance = 0.9, quantity = {1, 2}},
            {id = "dragon_magic_core", chance = 0.8, quantity = {1, 1}},
            {id = "centuries_old_fang", chance = 0.7, quantity = {1, 1}},
            {id = "ancient_fire_essence", chance = 0.8, quantity = {2, 4}}
        },
        legendary = {
            {id = "dragon_lord_scale", chance = 1.0, quantity = {10, 20}},
            {id = "imperial_dragon_crown", chance = 0.95, quantity = {1, 1}},
            {id = "reality_dominance_orb", chance = 0.9, quantity = {1, 1}},
            {id = "dragon_lordship_essence", chance = 0.8, quantity = {1, 2}},
            {id = "flame_mastery_codex", chance = 0.7, quantity = {1, 1}},
            {id = "dragonkind_authority_scepter", chance = 0.6, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "red_dragon",
        scale = 1.8,
        animations = {
            "soar", "fire_breath", "roar", "treasure_guard", "ancient_magic"
        },
        variants = {
            "young_red", "adult_red", "ancient_red", "red_dragon_lord"
        }
    },
    
    -- Sound effects with draconic characteristics
    sounds = {
        dragon_roar = {
            file = "red_dragon_roar",
            synth = {
                instrument = "organ",
                notes = {"C1", "G1", "C2", "G2"},
                durations = {1.5, 1.0, 1.2, 1.8},
                volume = 1.2,
                earth_shaking = true
            }
        },
        fire_breath = {
            file = "fire_breath",
            synth = {
                instrument = "distorted_guitar",
                notes = {"E2", "G2", "B2", "E3"},
                durations = {0.8, 0.6, 0.8, 1.0},
                volume = 1.0,
                infernal = true
            }
        },
        wing_beat = {
            file = "dragon_wing_beat",
            synth = {
                instrument = "percussion",
                notes = {"D1", "D1"},
                durations = {0.4, 0.4},
                volume = 0.8,
                massive = true
            }
        },
        ancient_magic = {
            file = "dragon_magic",
            synth = {
                instrument = "choir",
                notes = {"F2", "A2", "C3", "F3", "A3"},
                durations = {1.0, 0.8, 1.0, 0.8, 1.5},
                volume = 0.9,
                draconic = true
            }
        }
    },
    
    -- Special dragon abilities
    abilities = {
        fire_breath = {
            type = "active",
            description = "Devastating cone of dragonfire",
            effect = "fire_breath_attack",
            cooldown = 12
        },
        flight_mastery = {
            type = "passive",
            description = "Supreme aerial combat abilities",
            effect = "flight_superiority"
        },
        dragon_fear = {
            type = "passive",
            description = "Instills terror in lesser creatures",
            effect = "fear_aura"
        },
        treasure_sense = {
            type = "passive",
            description = "Can detect valuable items from great distances",
            effect = "treasure_detection"
        },
        ancient_magic = {
            type = "active",
            description = "Casts powerful draconic spells",
            effect = "dragon_spellcasting",
            manaCost = 80,
            cooldown = 20
        },
        fire_immunity = {
            type = "passive",
            description = "Complete immunity to fire damage",
            effect = "fire_immunity"
        }
    }
}

-- Initialize the red dragon entity using enhanced template
function RedDragon.init(entity, world)
    -- Copy all fields from RedDragon template to entity instance
    for k, v in pairs(RedDragon) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return RedDragon
