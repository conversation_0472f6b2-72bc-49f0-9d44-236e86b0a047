-- tiles/rocky_ground.lua
local RockyGroundTile = {
    id = "rocky_ground",
    name = "Rocky Ground",
    passable = true,
    variants = 5, -- Different rock patterns and densities [cite: 131]

    -- Which entities can spawn naturally on this tile
    spawns = {"pebble", "stone", "small_cactus", "lizard", "scorpion", "rock_viper"}, -- Added rock_viper [cite: 131]

    -- Movement properties
    movementSpeed = 0.9, -- Slightly slower movement on rocky ground [cite: 131]

    -- Resource type for AI / general classification
    resourceType = "stone_area", -- More specific than just "stone"

    -- Interaction function when player activates this tile
    interact = function(tile, entity)
        -- Chance to find stones [cite: 131]
        if math.random() < 0.25 then
            local amount = math.random(1, 3)
            return {
                success = true, 
                message = "You gather " .. amount .. " stones.", -- [cite: 132]
                effects = {
                    {type = "add_item", item = "stone", quantity = amount} -- [cite: 132]
                }
            }
        end
        
        -- Chance to find ore samples [cite: 133]
        if math.random() < 0.05 then
            local ores = {"iron_trace", "copper_trace", "quartz"}
            local foundOre = ores[math.random(#ores)] -- [cite: 133]
            return {
                success = true, 
                message = "You find a small sample of " .. foundOre .. ".", -- [cite: 134]
                effects = {
                    {type = "add_item", item = foundOre, quantity = 1} -- [cite: 134]
                }
            }
        end

        -- Chance to find other minor items
        if math.random() < 0.08 then
             local miscItems = {
                 {id="sturdy_stick", chance=0.5},
                 {id="sharp_flint", chance=0.3},
                 {id="dried_insect_carapace", chance=0.2}
             }
             for _, item in ipairs(miscItems) do
                 if math.random() < item.chance then
                      return {
                         success = true,
                         message = "Searching among the rocks, you find a " .. item.id .. ".",
                         effects = {{type="add_item", item=item.id, quantity=1}}
                      }
                 end
             end
        end
        
        -- Standard examination message
        return {
            success = false,
            message = "Rocky soil with scattered stones. Difficult terrain, but might hold resources." -- Slightly expanded message
        }
    end,
    
    -- Weather effects
    weatherEffects = {
        rain = function(tile)
            -- Rain can reveal minerals [cite: 136]
            if math.random() < 0.1 then
                tile.mineralVisible = true 
                local mineralTypes = {"iron", "copper", "quartz"}
                tile.mineralType = mineralTypes[math.random(#mineralTypes)]
                -- Make stones slippery
                tile.isSlippery = true
                return "visual_effect", "wet_rocks"
            end
            tile.isSlippery = true -- Rocks are generally slippery when wet
            return "visual_effect", "wet_rocks"
        end,
        
        heat = function(tile)
            -- Extreme heat can crack rocks [cite: 137]
            if math.random() < 0.05 then 
                return "create_object", "cracked_boulder" -- [cite: 138]
            end
             -- Rocks become hot to touch (minor effect potential)
             tile.isHot = true
            return nil
        end,

        sun = function(tile)
             -- Dry out rocks
             tile.isSlippery = false
             tile.isHot = false -- Reset hot status unless in extreme heat wave
             tile.mineralVisible = false -- Hide minerals again unless permanent
            return "remove_visual_effect", "wet_rocks"
        end,

        snow = function(tile)
            -- Snow covers rocks, making footing precarious
            tile.isSnowCovered = true
            tile.movementSpeed = tile.movementSpeed * 0.85 -- Further reduce speed
            tile.isSlippery = true
             return "visual_effect", "snow_covered_rocks"
        end
    },

     -- Time effects
    timeEffects = {
        night = function(tile)
            -- Rocks radiate stored heat initially, then cool down
            if world.time.hour < 22 then -- Still warm early night
                 tile.temperatureOffset = -2
            else -- Cooler late night
                 tile.temperatureOffset = -8
            end
            -- Nocturnal creatures might be more active
            tile.spawnModifier = { type="scorpion", chanceMultiplier=1.5 }
            tile.ambientSound = "night_rock_wind"
            return nil
        end,
        
        dawn = function(tile)
             -- Reset night effects
             tile.temperatureOffset = 0
             tile.spawnModifier = nil
             tile.ambientSound = "gentle_rock_wind"
            return nil
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {0.5, 0.45, 0.4}, -- Gray-brown [cite: 138]
        overlay_objects = {
            {name = "small_rock", chance = 0.5, count = {1, 4}}, -- [cite: 139]
            {name = "medium_rock", chance = 0.3, count = {0, 2}}, -- [cite: 139]
            {name = "boulder", chance = 0.1, count = {0, 1}}, -- [cite: 139]
            {name = "sparse_dry_grass", chance = 0.2} -- Added vegetation
        },
        variants = {
            {name = "sparse", rock_density = 0.3, overlay_multiplier = 0.5}, -- [cite: 140]
            {name = "medium", rock_density = 0.5, overlay_multiplier = 1.0}, -- [cite: 140]
            {name = "dense", rock_density = 0.7, overlay_multiplier = 1.5}, -- [cite: 140]
            {name = "reddish", base_color = {0.6, 0.4, 0.35}}, -- [cite: 140]
            {name = "grayish", base_color = {0.45, 0.45, 0.5}} -- [cite: 140]
        },
         condition_effects = {
            cracks = {threshold = 0.3, intensity = 0.5, related_weather="heat"}, -- Inspired by stone_path
            erosion = {threshold = 0.4, intensity = 0.4, related_weather="rain"}
        },
         weather_overlays = {
            wet = {shininess = 0.4, when = "is_slippery"}, -- Inspired by stone_path
            snow = {color = {0.9, 0.9, 0.95}, coverage = 0.8, when = "is_snow_covered"} -- Inspired by stone_path
        }
    },
    
    -- Audio properties
    footstepSound = "stone_step", -- [cite: 141]
    ambientSound = "gentle_rock_wind", -- Added ambient
    specialSounds = { -- Added special sounds
        {name = "rock_skitter", trigger = "entity_nearby", chance = 0.1},
        {name = "small_rock_fall", trigger = "weather_change", chance = 0.05},
        {name = "wind_whistle", trigger = "windy", chance = 0.2}
    },
    
    -- Mining properties
    mineable = true, -- [cite: 141]
    rockHardness = 2, -- How hard to mine (1-10) [cite: 141]
    miningYields = { --
        default = {
            {item = "stone", quantity = {2, 4}, chance = 1.0}
        },
        pickaxe = {
            {item = "stone", quantity = {3, 6}, chance = 1.0},
            {item = "iron_ore", quantity = {0, 1}, chance = 0.1},
            {item = "copper_ore", quantity = {0, 1}, chance = 0.15},
            {item = "gem", quantity = {0, 1}, chance = 0.01}
        }
    },

    -- Pathfinding properties
     pathfinding = {
        travel_cost = 1.1, -- Slightly more costly than dirt/grass
        hazard_chance = 0.05 -- Small chance to trigger stumble effect
    },

    -- Connections to other tiles
    connects_to = {
        "rocky_ground", -- Itself
        "dirt", -- Common transition
        "grass", -- Common transition
        "mountain_base", -- Transition to mountains
        "desert_sand", -- Transition to desert
        "ruins", -- Can be found near or within ruins
        "cliff" -- Can lead up to cliffs
    },

    -- Effects on entities that walk on this tile
    onWalk = function(tile, entity)
        local effects = {}
        local sound = "stone_step" -- Default footstep [cite: 141]
        local volume = 0.9
        
        -- Chance of stumbling, higher if slippery
        local stumbleChance = 0.03
        if tile.isSlippery then
            stumbleChance = 0.08
        end
        
        if math.random() < stumbleChance then
             table.insert(effects, {type = "apply_status", status = "stumbled", duration = 2})
             -- Maybe a different sound for stumbling?
             -- sound = "rock_scrape"
             return {
                 sound = sound,
                 volume = volume,
                 message = "You stumble on the uneven rocky ground!",
                 effects = effects
             }
        end

         -- Slightly increase fatigue
         table.insert(effects, {type="increase_fatigue", amount = 0.1})

        return {
            sound = sound,
            volume = volume,
            effects = effects
        }
    end,
    
    -- Effect when discovering a new rocky area
    onDiscover = function(tile, entity)
        local message = "You enter an area of rough, rocky ground."
        if tile.mineralVisible then
             message = "You enter an area of rocky ground. Rain seems to have washed some " .. tile.mineralType .. " traces clear."
        end

        if entity.journal and entity.journal.addDiscovery then
             entity.journal.addDiscovery({
                type = "terrain",
                name = "Rocky Ground",
                location = {x = tile.x, y = tile.y},
                notes = "Difficult footing, potential source of stone and minerals."
            })
        end
        return {
            message = message,
            effects = {
                {type = "reveal_map", radius = 2}
            }
        }
    end
}

function RockyGroundTile.init(world) -- [cite: 143]
    print("Rocky ground tile module initialized") -- [cite: 143]
    -- Register with relevant systems if needed
    if world.systems and world.systems.geology then
        world.systems.geology:registerTerrain("rocky_ground", {hardness = 2})
    end
end -- [cite: 143]

return RockyGroundTile -- [cite: 143]