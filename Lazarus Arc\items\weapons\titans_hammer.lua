local TitansHammer = {
    id = "titans_hammer",
    name = "Titan's Hammer",
    type = "weapon",
    category = "hammer",
    
    -- Properties
    properties = {
        damage = 50,
        attackSpeed = 0.8,
        range = 2.0,
        durability = 250,
        maxDurability = 250,
        earthDamage = 40,
        earthRadius = 2.5,
        earthDuration = 4,
        seismicChance = 0.35,
        seismicDamage = 35,
        seismicRadius = 3.0,
        seismicWaves = 3,
        mountainShatterChance = 0.2,
        mountainShatterDamage = 60,
        mountainShatterRadius = 4.0,
        mountainShatterForce = 3.0,
        earthCharge = 0,
        maxEarthCharge = 100,
        chargeRate = 4,
        dischargeRate = 8,
        earthResistance = 0.8,
        momentum = 0,
        maxMomentum = 5,
        momentumPower = 1.5
    },
    
    -- Appearance
    appearance = {
        sprite = "titans_hammer",
        scale = 1.0,
        animations = {
            "idle",
            "swing",
            "slam",
            "charge"
        },
        variants = {
            "stone",
            "granite",
            "obsidian",
            "titan"
        },
        blendMode = "normal",
        tint = {0.6, 0.4, 0.2},
        alpha = 1.0
    },
    
    -- Sound effects
    sounds = {
        swing = "titans_hammer_swing",
        slam = "titans_hammer_slam",
        seismic = "titans_hammer_seismic",
        shatter = "titans_hammer_shatter"
    },
    
    -- Effects
    effects = {
        earth = {
            type = "status",
            duration = 4,
            effects = {
                damage = 40,
                slow = true,
                radius = 2.5
            }
        },
        seismic = {
            type = "status",
            duration = 0.5,
            effects = {
                damage = 35,
                waves = 3
            }
        },
        mountainShatter = {
            type = "status",
            duration = 0.5,
            effects = {
                damage = 60,
                knockback = 3.0,
                radius = 4.0
            }
        }
    }
}

-- Initialize the weapon
function TitansHammer.init(weapon, world)
    -- Copy all fields from TitansHammer template to weapon instance
    for k, v in pairs(TitansHammer) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        earthCharge = 0,
        lastChargeTime = 0,
        momentum = 0
    }

    return weapon
end

-- Update the weapon
function TitansHammer.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Update earth charge
        if world.terrain and world.terrain == "mountain" then
            local chargeFactor = (world.stability * 0.7 + 0.3) * dt
            weapon.properties.weaponState.earthCharge = math.min(weapon.properties.maxEarthCharge,
                weapon.properties.weaponState.earthCharge + chargeFactor * weapon.properties.chargeRate)
        else
            weapon.properties.weaponState.earthCharge = math.max(0,
                weapon.properties.weaponState.earthCharge - weapon.properties.dischargeRate * dt)
        end
        
        -- Create earth effect
        if world.createEffect then
            world.createEffect({
                type = "earth",
                position = weapon.position,
                radius = weapon.properties.earthRadius,
                duration = dt
            })
        end
        
        -- Decay momentum
        if weapon.properties.weaponState.momentum > 0 then
            weapon.properties.weaponState.momentum = math.max(0,
                weapon.properties.weaponState.momentum - dt)
        end
    end
end

-- Handle attack
function TitansHammer.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Calculate swing direction
    local angle = math.atan2(
        target.position.y - attacker.position.y,
        target.position.x - attacker.position.x
    )
    
    -- Create earth slam effect
    if world.createEffect then
        world.createEffect({
            type = "earth_slam",
            position = attacker.position,
            angle = angle,
            range = weapon.properties.range,
            duration = 0.5
        })
    end
    
    -- Check for earth effect
    if math.random() < weapon.properties.earthDamage / weapon.properties.damage then
        -- Create earth effect
        if world.createEffect then
            world.createEffect({
                type = "earth",
                position = target.position,
                radius = weapon.properties.earthRadius,
                duration = weapon.properties.earthDuration
            })
        end
        
        -- Apply earth effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.earth)
        end
        
        -- Play slam sound
        if world.playSound then
            world.playSound(weapon.sounds.slam)
        end
    end
    
    -- Check for seismic effect
    if math.random() < weapon.properties.seismicChance then
        -- Create seismic effect
        if world.createEffect then
            world.createEffect({
                type = "seismic",
                position = target.position,
                waves = weapon.properties.seismicWaves,
                duration = 0.5
            })
        end
        
        -- Create seismic waves
        for i = 1, weapon.properties.seismicWaves do
            local waveRadius = weapon.properties.seismicRadius * (i / weapon.properties.seismicWaves)
            
            -- Apply seismic effect to entities in wave
            if world.entities then
                for _, entity in ipairs(world.entities) do
                    if entity.position then
                        local distance = math.sqrt(
                            (entity.position.x - target.position.x)^2 + 
                            (entity.position.y - target.position.y)^2
                        )
                        
                        if distance <= waveRadius then
                            if entity.applyEffect then
                                entity.applyEffect(weapon.effects.seismic)
                            end
                        end
                    end
                end
            end
        end
        
        -- Play seismic sound
        if world.playSound then
            world.playSound(weapon.sounds.seismic)
        end
    end
    
    -- Check for mountain shatter effect
    if math.random() < weapon.properties.mountainShatterChance then
        -- Create mountain shatter effect
        if world.createEffect then
            world.createEffect({
                type = "mountain_shatter",
                position = target.position,
                radius = weapon.properties.mountainShatterRadius,
                duration = 0.5
            })
        end
        
        -- Apply mountain shatter effect to nearby entities
        if world.entities then
            for _, entity in ipairs(world.entities) do
                if entity.position then
                    local distance = math.sqrt(
                        (entity.position.x - target.position.x)^2 + 
                        (entity.position.y - target.position.y)^2
                    )
                    
                    if distance <= weapon.properties.mountainShatterRadius then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.mountainShatter)
                        end
                        
                        -- Apply knockback
                        if entity.move then
                            local force = (1 - distance / weapon.properties.mountainShatterRadius) * 
                                weapon.properties.mountainShatterForce
                            local angle = math.atan2(
                                entity.position.y - target.position.y,
                                entity.position.x - target.position.x
                            )
                            entity.move({
                                x = math.cos(angle) * force,
                                y = math.sin(angle) * force
                            })
                        end
                    end
                end
            end
        end
        
        -- Play shatter sound
        if world.playSound then
            world.playSound(weapon.sounds.shatter)
        end
    end
    
    -- Update momentum
    weapon.properties.weaponState.momentum = math.min(weapon.properties.maxMomentum,
        weapon.properties.weaponState.momentum + 1)
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function TitansHammer.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough earth charge and momentum
    if weapon.properties.weaponState.earthCharge < 50 or weapon.properties.weaponState.momentum < 3 then
        return false
    end
    
    -- Create titan's hammer effect
    if world.createEffect then
        world.createEffect({
            type = "titans_hammer",
            position = attacker.position,
            radius = weapon.properties.earthRadius * 3,
            duration = 6,
            damage = weapon.properties.damage * 2
        })
    end
    
    -- Apply earth effects to nearby entities
    if world.entities then
        for _, entity in ipairs(world.entities) do
            if entity.position then
                local distance = math.sqrt(
                    (entity.position.x - attacker.position.x)^2 + 
                    (entity.position.y - attacker.position.y)^2
                )
                
                if distance <= weapon.properties.earthRadius * 3 then
                    -- Apply random earth effect
                    local effect = math.random()
                    if effect < 0.4 then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.earth)
                        end
                    elseif effect < 0.7 then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.seismic)
                        end
                    else
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.mountainShatter)
                        end
                    end
                end
            end
        end
    end
    
    -- Reset earth charge and consume momentum
    weapon.properties.weaponState.earthCharge = 0
    weapon.properties.weaponState.lastChargeTime = world.time
    weapon.properties.weaponState.momentum = math.max(0,
        weapon.properties.weaponState.momentum - 3)
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 5)
    
    return true
end

return TitansHammer 