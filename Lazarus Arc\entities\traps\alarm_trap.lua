local AlarmTrap = {
    id = "alarm_trap",
    name = "Alarm Trap",
    type = "trap",
    
    -- Entity categories
    categories = {"trap", "dungeon", "alert"},
    
    -- Target categories
    targetCategories = {"player"},
    
    -- Stats
    maxHealth = 15,
    health = 15,
    duration = 0, -- Instant effect
    cooldown = 60,
    
    -- Trap properties
    properties = {
        radius = 3.0,
        setupTime = 2,
        isArmed = false,
        isTriggered = false,
        destroyOnActivate = false,
        alertRadius = 20,
        alertDuration = 10,
        alertStrength = 1.0
    },
    
    -- Appearance
    appearance = {
        sprite = "alarm_trap",
        scale = 1.0,
        animations = {
            "idle", "trigger", "alert"
        },
        variants = {
            "bell", "horn", "crystal", "rune"
        }
    },
    
    -- Sound effects
    sounds = {
        trigger = "alarm_trap_trigger",
        alert = "alarm_trap_alert",
        deactivate = "alarm_trap_deactivate"
    },
    
    -- Effects
    effects = {
        alert = {
            type = "alert",
            duration = 10,
            radius = 20,
            strength = 1.0,
            effects = {
                aggro = true,
                search = true,
                investigate = true
            }
        }
    }
}

-- Initialize the trap
function AlarmTrap.init(entity, world)
    -- Copy all fields from AlarmTrap template to entity instance
    for k, v in pairs(AlarmTrap) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position
    entity.position = entity.position or {x = 0, y = 0}

    -- Set random variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize trap state
    entity.properties.isArmed = false
    entity.properties.isTriggered = false
    entity.properties.triggerTime = 0
    entity.properties.alertActive = false

    return entity
end

-- Update the trap
function AlarmTrap.update(entity, world, dt)
    -- Check if trap is armed and not triggered
    if entity.properties.isArmed and not entity.properties.isTriggered then
        -- Check for targets in radius
        if world and world.entities then
            for _, target in ipairs(world.entities) do
                if target.position and target.categories then
                    -- Check if target is in trap categories
                    local isTarget = false
                    for _, category in ipairs(entity.targetCategories) do
                        if table.contains(target.categories, category) then
                            isTarget = true
                            break
                        end
                    end
                    
                    if isTarget then
                        local distance = math.sqrt(
                            (target.position.x - entity.position.x)^2 + 
                            (target.position.y - entity.position.y)^2
                        )
                        
                        if distance <= entity.properties.radius then
                            -- Trigger trap
                            entity.properties.isTriggered = true
                            entity.properties.triggerTime = world.time
                            entity.properties.alertActive = true
                            
                            -- Create alert effect
                            if world.createEffect then
                                world.createEffect({
                                    type = "alert",
                                    position = entity.position,
                                    radius = entity.properties.alertRadius,
                                    duration = entity.properties.alertDuration,
                                    strength = entity.properties.alertStrength,
                                    effects = entity.effects.alert.effects
                                })
                            end
                            
                            -- Alert nearby enemies
                            if world.entities then
                                for _, enemy in ipairs(world.entities) do
                                    if enemy.position and enemy.categories and 
                                       table.contains(enemy.categories, "monster") then
                                        local enemyDistance = math.sqrt(
                                            (enemy.position.x - entity.position.x)^2 + 
                                            (enemy.position.y - entity.position.y)^2
                                        )
                                        
                                        if enemyDistance <= entity.properties.alertRadius then
                                            -- Alert the enemy
                                            if enemy.alert then
                                                enemy.alert({
                                                    position = entity.position,
                                                    strength = entity.properties.alertStrength,
                                                    duration = entity.properties.alertDuration
                                                })
                                            end
                                        end
                                    end
                                end
                            end
                            
                            -- Play trigger sound
                            if world.playSound then
                                world.playSound(entity.sounds.trigger)
                            end
                            
                            break
                        end
                    end
                end
            end
        end
    end
    
    -- Handle alert duration
    if entity.properties.alertActive then
        if world.time - entity.properties.triggerTime >= entity.properties.alertDuration then
            entity.properties.alertActive = false
            if world.playSound then
                world.playSound(entity.sounds.deactivate)
            end
        end
    end
    
    -- Check if trap can be rearmed
    if not entity.properties.isArmed and world.time - entity.properties.triggerTime >= entity.cooldown then
        entity.properties.isArmed = true
        entity.properties.isTriggered = false
        entity.properties.alertActive = false
    end
end

-- Arm the trap
function AlarmTrap.arm(entity, world)
    if not entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = true
        entity.properties.triggerTime = world.time
        entity.properties.alertActive = false
        return true
    end
    return false
end

-- Disarm the trap
function AlarmTrap.disarm(entity, world)
    if entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = false
        entity.properties.triggerTime = world.time
        entity.properties.alertActive = false
        return true
    end
    return false
end

return AlarmTrap 