-- Initialize the biome module
function ForestBiome.init(worldCore)
    print("Forest biome module initialized")
    
    -- Store reference to WorldCore for later use
    ForestBiome.worldCore = worldCore
    
    -- Don't try to register generators at init time
    -- Instead, we'll register them when a world is created
    
    -- Store the biome in the biomes registry
    -- This allows the module to be used later when worlds are created
    print("Forest biome registered successfully")
end

-- Add a new function to register generators with a specific world instance
function ForestBiome.registerWithWorld(world)
    print("Registering forest biome generators with world")
    
    if not world or not world.chunkSystem then
        print("Warning: Cannot register forest biome - world or chunkSystem is nil")
        return false
    end
    
    -- Register generation algorithms with the world
    world.chunkSystem:registerGenerator("forest", ForestBiome.generate)
    
    -- Register biome variants
    for variantId, variant in pairs(ForestBiome.variants) do
        world.chunkSystem:registerGenerator("forest_" .. variantId, function(chunk, world)
            ForestBiome.generate(chunk, world, variantId)
            ForestBiome.postProcess(chunk, world, variantId)
        end)
    end
    
    return true
end