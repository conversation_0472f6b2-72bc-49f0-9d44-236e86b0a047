-- items/robot.lua
-- Robot-specific items and components

local RobotItems = {
    -- Robot Parts
    spare_robot_part_basic = {
        id = "spare_robot_part_basic",
        name = "Basic Robot Part",
        description = "A standard replacement part for basic robots.",
        type = "robot_part",
        rarity = 2,
        value = 20,
        weight = 2,
        stackable = true,
        maxStack = 15,
        tags = {"robot", "part", "basic"}
    },
    
    spare_robot_part_advanced = {
        id = "spare_robot_part_advanced",
        name = "Advanced Robot Part",
        description = "A sophisticated replacement part for advanced robots.",
        type = "robot_part",
        rarity = 3,
        value = 45,
        weight = 2,
        stackable = true,
        maxStack = 10,
        tags = {"robot", "part", "advanced"}
    },

    -- Robot Software & Data
    diagnostic_log_chip = {
        id = "diagnostic_log_chip",
        name = "Diagnostic Log Chip",
        description = "A data chip containing robot diagnostic information.",
        type = "data",
        rarity = 2,
        value = 15,
        weight = 0.5,
        stackable = true,
        maxStack = 20,
        tags = {"robot", "data", "diagnostic"}
    },
    
    maintenance_log_chip = {
        id = "maintenance_log_chip",
        name = "Maintenance Log Chip",
        description = "A data chip containing robot maintenance records.",
        type = "data",
        rarity = 2,
        value = 18,
        weight = 0.5,
        stackable = true,
        maxStack = 20,
        tags = {"robot", "data", "maintenance"}
    },
    
    diagnostic_software = {
        id = "diagnostic_software",
        name = "Diagnostic Software",
        description = "Software used for robot diagnostics and troubleshooting.",
        type = "software",
        rarity = 3,
        value = 35,
        weight = 1,
        stackable = false,
        tags = {"robot", "software", "diagnostic"}
    },

    -- Robot Sensors & Components
    advanced_sensor_module = {
        id = "advanced_sensor_module",
        name = "Advanced Sensor Module",
        description = "A sophisticated sensor module used in advanced robots.",
        type = "component",
        rarity = 3,
        value = 40,
        weight = 1,
        stackable = true,
        maxStack = 10,
        tags = {"robot", "sensor", "advanced"}
    },
    
    atmosphere_sensor = {
        id = "atmosphere_sensor",
        name = "Atmosphere Sensor",
        description = "A sensor module for monitoring atmospheric conditions.",
        type = "component",
        rarity = 2,
        value = 25,
        weight = 1,
        stackable = true,
        maxStack = 10,
        tags = {"robot", "sensor", "atmosphere"}
    },

    -- Robot Power Systems
    grow_light_component = {
        id = "grow_light_component",
        name = "Grow Light Component",
        description = "A specialized light component for agricultural robots.",
        type = "component",
        rarity = 2,
        value = 30,
        weight = 2,
        stackable = true,
        maxStack = 10,
        tags = {"robot", "light", "agriculture"}
    }
}

return RobotItems 