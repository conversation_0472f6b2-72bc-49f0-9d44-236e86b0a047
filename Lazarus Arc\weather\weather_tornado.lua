-- weather/tornado.lua
-- Tornado weather pattern - destructive wind vortex with debris

local TornadoWeather = {}
TornadoWeather.__index = TornadoWeather

-- Basic properties
TornadoWeather.id = "tornado"
TornadoWeather.name = "Tornado"

-- Visual properties
TornadoWeather.visual = {
    skyColor = {r = 80, g = 80, b = 100}, -- Dark stormy sky
    sunIntensity = 0.2,
    cloudCoverage = 1.0,
    cloudColor = {r = 60, g = 60, b = 80}, -- Dark storm clouds
    ambientLightLevel = 0.3,
    tornadoColor = {r = 100, g = 100, b = 120} -- Tornado funnel color
}

-- Environment modifiers
TornadoWeather.environment = {
    temperature = 0.8, -- Slightly cooler
    visibility = 0.3, -- Very poor visibility
    humidity = 0.9, -- High humidity
    windStrength = 1.0, -- Maximum wind strength
    debrisDensity = 0.9, -- High debris concentration
    pressureDrop = 0.7 -- Significant pressure drop
}

-- Particle systems
TornadoWeather.particles = {
    tornadoFunnel = {
        enabled = true,
        intensity = 0.9,
        color = {r = 100, g = 100, b = 120},
        size = {min = 5, max = 15},
        speed = {min = 200, max = 400}
    },
    debris = {
        enabled = true,
        intensity = 0.8,
        color = {r = 80, g = 80, b = 80},
        size = {min = 1, max = 5},
        speed = {min = 300, max = 600}
    },
    dustClouds = {
        enabled = true,
        intensity = 0.7,
        color = {r = 120, g = 120, b = 140},
        size = {min = 3, max = 8},
        speed = {min = 150, max = 300}
    }
}

-- Sound effects
TornadoWeather.sounds = {
    ambient = "tornado_ambient",
    wind = "tornado_wind",
    debris = "debris_impact",
    thunder = "thunder_clap",
    volume = 0.9
}

-- Effects on game entities and tiles
TornadoWeather.effects = {
    -- Damage to exposed entities
    exposureDamage = 10, -- Very high damage per second
    
    -- Equipment damage
    equipmentDamageRate = 2.5,
    
    -- Movement effects
    movementSpeedMultiplier = 0.3,
    
    -- Vision effects
    visionRange = 0.2,
    
    -- Lift effects
    liftChance = 0.4, -- Chance to be lifted by tornado
    
    -- Debris damage
    debrisDamage = 5,
    
    -- Shield effects
    shieldEffectiveness = 0.5,
    
    -- Structure damage
    structureDamageMultiplier = 2.0
}

-- Transition probabilities to other weather (per game hour)
TornadoWeather.transitions = {
    clear = 0.1,
    cloudy = 0.2,
    rain = 0.2,
    hail = 0.1,
    -- Implied: 0.4 chance to stay tornado
}

-- Day/night cycle modifiers
TornadoWeather.timeModifiers = {
    dawn = {
        skyColor = {r = 100, g = 100, b = 120},
        ambientLightLevel = 0.2
    },
    day = {
        -- Uses default properties
    },
    dusk = {
        skyColor = {r = 70, g = 70, b = 90},
        ambientLightLevel = 0.1
    },
    night = {
        skyColor = {r = 40, g = 40, b = 60},
        ambientLightLevel = 0.1
    }
}

-- Helper function to deep copy a table
local function deepCopy(orig)
    local copy
    if type(orig) == 'table' then
        copy = {}
        for orig_key, orig_value in next, orig, nil do
            copy[deepCopy(orig_key)] = deepCopy(orig_value)
        end
        setmetatable(copy, deepCopy(getmetatable(orig)))
    else
        copy = orig
    end
    return copy
end

-- Create a new instance of the weather pattern
function TornadoWeather.new()
    local instance = setmetatable({}, TornadoWeather)
    -- Copy all properties
    for k, v in pairs(TornadoWeather) do
        if k ~= "__index" and k ~= "new" and type(v) ~= "function" then
            instance[k] = deepCopy(v)
        end
    end
    return instance
end

-- Initialize the weather pattern
function TornadoWeather:init(world)
    -- Handle the case where init is called directly on the module
    if not world and type(self) ~= "table" then
        -- world parameter was passed as self
        world = self
        self = TornadoWeather
    end
    
    if not world then
        print("Warning: TornadoWeather:init called without world parameter")
        return self
    end
    
    print("Initializing tornado weather")
    
    -- Set global light level
    if self.visual and self.visual.ambientLightLevel then
        world.lightLevel = self.visual.ambientLightLevel
    end
    
    -- Start ambient sounds
    if self.sounds and self.sounds.ambient then
        -- Play ambient tornado sound
        print("Playing sound: " .. self.sounds.ambient)
    end
    
    -- Create particle systems
    if self.particles and self.particles.tornadoFunnel and self.particles.tornadoFunnel.enabled then
        -- Create tornado funnel particles
        print("Creating tornado funnel particles with intensity: " .. self.particles.tornadoFunnel.intensity)
    end
    
    if self.particles and self.particles.debris and self.particles.debris.enabled then
        -- Create debris particles
        print("Creating debris particles with intensity: " .. self.particles.debris.intensity)
    end
    
    if self.particles and self.particles.dustClouds and self.particles.dustClouds.enabled then
        -- Create dust cloud particles
        print("Creating dust cloud particles with intensity: " .. self.particles.dustClouds.intensity)
    end
    
    -- Display warning message to players
    if world.messageSystem then
        world.messageSystem:broadcast("WARNING: Tornado detected. Seek immediate shelter in underground structures.")
    end
    
    return self
end

-- Update function called every frame
function TornadoWeather:update(world, dt)
    -- Handle the case where update is called directly on the module
    if not dt and type(world) == "number" then
        -- Parameters were passed as (self, world, dt)
        dt = world
        world = self
        self = TornadoWeather
    end
    
    if not world then return end
    
    -- Update day/night cycle based on world time
    local timeOfDay = world.timeOfDay or "day"
    local modifiers = self.timeModifiers and self.timeModifiers[timeOfDay]
    
    if modifiers then
        -- Apply time-specific modifiers
        if modifiers.skyColor then
            -- Change sky color
        end
        
        if modifiers.ambientLightLevel then
            world.lightLevel = modifiers.ambientLightLevel
        end
    end
    
    -- Apply effects to game world
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            -- Check if entity is exposed to tornado
            if entity.isExposed then
                -- Apply combined damage
                if entity.health and self.effects then
                    entity.health = entity.health - 
                        ((self.effects.exposureDamage + 
                          self.effects.debrisDamage) * dt)
                end
                
                -- Apply equipment damage
                if entity.equipment and self.effects then
                    for _, item in pairs(entity.equipment) do
                        if item.durability then
                            item.durability = item.durability - 
                                (self.effects.equipmentDamageRate * dt)
                        end
                    end
                end
                
                -- Apply movement speed reduction
                if entity.speed and self.effects then
                    entity.speed = entity.speed * self.effects.movementSpeedMultiplier
                end
                
                -- Check for being lifted by tornado
                if self.effects and math.random() < self.effects.liftChance * dt then
                    if entity.onLift then
                        entity:onLift()
                    end
                end
            end
            
            -- Special effects for different entity types
            if entity.categories and self.effects then
                -- Effect on structures
                if table.contains(entity.categories, "structure") then
                    if entity.health then
                        entity.health = entity.health - 
                            (self.effects.exposureDamage * 
                             self.effects.structureDamageMultiplier * dt)
                    end
                end
                
                -- Effect on shields
                if table.contains(entity.categories, "shield") then
                    if entity.effectiveness then
                        entity.effectiveness = entity.effectiveness * 
                            self.effects.shieldEffectiveness
                    end
                end
                
                -- Effect on sensors and vision devices
                if table.contains(entity.categories, "sensor") then
                    if entity.range then
                        entity.range = entity.range * self.effects.visionRange
                    end
                end
            end
        end
    end
    
    -- Random debris impact sounds
    if math.random() < 0.15 and self.sounds and self.sounds.debris then
        -- Play debris impact sound
        print("Playing sound: " .. self.sounds.debris)
    end
    
    -- Random thunder sounds
    if math.random() < 0.1 and self.sounds and self.sounds.thunder then
        -- Play thunder sound
        print("Playing sound: " .. self.sounds.thunder)
    end
end

-- Clean up when weather changes
function TornadoWeather:cleanUp(world)
    -- Handle the case where cleanUp is called directly on the module
    if type(self) ~= "table" then
        -- world parameter was passed as self
        world = self
        self = TornadoWeather
    end
    
    if not world then return end
    
    -- Stop ongoing sounds and particle effects
    print("Tornado weather ending")
    
    -- Notify players
    if world.messageSystem then
        world.messageSystem:broadcast("Tornado dissipating. Conditions improving.")
    end
    
    -- Reset entity states
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            -- Reset movement speed
            if entity.speed and entity.defaultSpeed then
                entity.speed = entity.defaultSpeed
            end
            
            -- Reset shield effectiveness
            if entity.categories and table.contains(entity.categories, "shield") then
                if entity.effectiveness and entity.defaultEffectiveness then
                    entity.effectiveness = entity.defaultEffectiveness
                end
            end
            
            -- Reset sensor ranges
            if entity.categories and table.contains(entity.categories, "sensor") then
                if entity.range and entity.defaultRange then
                    entity.range = entity.defaultRange
                end
            end
        end
    end
end

-- Table contains helper function
function table.contains(table, element)
    if not table then return false end
    
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

return TornadoWeather 