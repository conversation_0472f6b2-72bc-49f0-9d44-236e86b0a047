-- structures/blacksmith.lua
-- Blacksmith structure definition for metalworking and crafting
local StructureComponents = require("structure_components")

-- Declare <PERSON> at the top level so it can be referenced from within functions
local Blacksmith = {}

-- Define the structure properties
Blacksmith = {
    id = "blacksmith",
    name = "Blacksmith",
    category = "crafting",
    
    -- Physical properties
    width = 6,    -- Tiles wide
    height = 6,   -- Tiles tall
    solid = true, -- Solid structure
    providesShelter = true,
    
    -- Structure properties
    hasInterior = true,
    hasUnderground = false,
    condition = 0.8, -- 0.0 = completely ruined, 1.0 = pristine
    age = 0, -- Will be set during init, in years
    decayRate = 0.00001, -- Much slower base decay rate per year
    forgeHeat = 0.8, -- Affected by component status and maintenance
    craftingQuality = 0.7, -- Affected by forge heat and season
    lastMaintenance = 0, -- When the structure was last maintained
    maintenanceCycle = 25, -- Years between natural maintenance cycles
    seasonalEffects = {
        spring = 1.1, -- Good forging season
        summer = 1.0, -- Normal season
        autumn = 0.9, -- Cooler season
        winter = 0.8  -- Cold season
    },
    cosmeticDamage = {
        walls = 0.0, -- 0.0 = pristine, 1.0 = heavily damaged
        forge = 0.0,
        anvil = 0.0,
        tools = 0.0,
        chimney = 0.0
    },
    
    -- Define structure components
    components = {
        {
            id = "outer_walls",
            type = "WALL",
            options = {
                material = "stone",
                isExterior = true,
                critical = true
            }
        },
        {
            id = "inner_walls",
            type = "WALL",
            options = {
                material = "stone",
                isExterior = false,
                critical = true
            }
        },
        {
            id = "entrance",
            type = "DOOR",
            options = {
                material = "wood",
                isExterior = true,
                critical = true
            }
        },
        {
            id = "forge",
            type = "POWER_SOURCE",
            options = {
                material = "forge",
                critical = true,
                maxOutput = 600
            }
        },
        {
            id = "anvil",
            type = "SUPPORT",
            options = {
                material = "anvil",
                critical = true,
                isLoadBearing = true
            }
        },
        {
            id = "tool_rack",
            type = "SUPPORT",
            options = {
                material = "wood",
                critical = true,
                isLoadBearing = true
            }
        },
        {
            id = "chimney",
            type = "SUPPORT",
            options = {
                material = "stone",
                critical = true,
                isLoadBearing = true
            }
        },
        {
            id = "roof",
            type = "ROOF",
            options = {
                material = "wood",
                critical = true,
                isWeatherproof = true
            }
        }
    },
    
    -- Spawn properties
    spawnChance = {
        village = 0.8,
        town = 0.7,
        city = 0.6,
        ruins = 0.2
    },
    
    -- Spawn requirements
    requirements = {
        minDistanceFromOtherStructures = 3,
        validTiles = {"grass", "stone", "dirt"},
        invalidTiles = {"water", "lava", "void"},
        needsFlatGround = true
    },
    
    -- NPCs and creatures that may inhabit this structure
    inhabitants = {
        common = {
            "apprentice_blacksmith",
            "blacksmith",
            "customer",
            "merchant"
        },
        uncommon = {
            "master_blacksmith",
            "weaponsmith",
            "armorsmith",
            "toolmaker"
        },
        rare = {
            "grandmaster_blacksmith",
            "legendary_blacksmith",
            "artisan_blacksmith",
            "master_weaponsmith"
        }
    },
    
    -- Items that may spawn inside
    loot = {
        common = {
            {id = "iron_ingot", chance = 0.7, quantity = {1, 3}},
            {id = "steel_ingot", chance = 0.6, quantity = {1, 2}},
            {id = "hammer", chance = 0.5, quantity = {1, 2}},
            {id = "tongs", chance = 0.4, quantity = {1, 2}}
        },
        uncommon = {
            {id = "mithril_ingot", chance = 0.3, quantity = {1, 1}},
            {id = "master_hammer", chance = 0.25, quantity = {1, 1}},
            {id = "enchanted_tongs", chance = 0.2, quantity = {1, 1}},
            {id = "forge_manual", chance = 0.15, quantity = {1, 1}}
        },
        rare = {
            {id = "adamantine_ingot", chance = 0.1, quantity = {1, 1}},
            {id = "legendary_hammer", chance = 0.1, quantity = {1, 1}},
            {id = "ancient_tongs", chance = 0.15, quantity = {1, 1}},
            {id = "master_forge_manual", chance = 0.05, quantity = {1, 1}}
        }
    },
    
    -- Variant types for this structure
    variants = {
        {
            name = "standard",
            description = "Standard blacksmith shop",
            materials = {
                outer_walls = "stone",
                inner_walls = "stone",
                entrance = "wood",
                forge = "standard_forge",
                anvil = "standard_anvil",
                tool_rack = "wood",
                chimney = "stone",
                roof = "wood"
            },
            features = {
                "forge_area",
                "anvil_station",
                "tool_rack",
                "storage_area",
                "display_area"
            },
            forgeHeat = 0.8,
            craftingQuality = 0.7,
            inhabitants = {
                "blacksmith",
                "apprentice_blacksmith",
                "customer"
            }
        },
        {
            name = "advanced",
            description = "Advanced blacksmith workshop",
            materials = {
                outer_walls = "stone",
                inner_walls = "stone",
                entrance = "wood",
                forge = "advanced_forge",
                anvil = "advanced_anvil",
                tool_rack = "wood",
                chimney = "stone",
                roof = "wood"
            },
            features = {
                "advanced_forge",
                "master_anvil",
                "tool_workshop",
                "material_storage",
                "display_showroom"
            },
            forgeHeat = 0.9,
            craftingQuality = 0.8,
            inhabitants = {
                "master_blacksmith",
                "weaponsmith",
                "armorsmith"
            }
        },
        {
            name = "legendary",
            description = "Legendary blacksmith forge",
            materials = {
                outer_walls = "stone",
                inner_walls = "stone",
                entrance = "wood",
                forge = "legendary_forge",
                anvil = "legendary_anvil",
                tool_rack = "wood",
                chimney = "stone",
                roof = "wood"
            },
            features = {
                "legendary_forge",
                "masterwork_anvil",
                "master_tool_workshop",
                "rare_material_storage",
                "legendary_display"
            },
            forgeHeat = 1.0,
            craftingQuality = 0.9,
            inhabitants = {
                "grandmaster_blacksmith",
                "legendary_blacksmith",
                "master_weaponsmith"
            }
        }
    },
    
    -- Tile layout (exterior)
    tileLayout = {
        "wwwwww",
        "w....w",
        "w....w",
        "w....w",
        "w....w",
        "wwwwww"
    },
    
    -- Tile legend for exterior
    tileLegend = {
        w = "blacksmith_wall",
        ["."] = "blacksmith_floor"
    },
    
    -- Interior layout
    interiorLayout = {
        main_hall = {
            width = 3,
            height = 3,
            features = {"entrance", "forge", "anvil"}
        },
        side_chambers = {
            width = 2,
            height = 2,
            features = {"tool_rack", "storage_area"}
        }
    },
    
    -- Interior tile legend
    interiorLegend = {
        ["."] = "blacksmith_floor",
        w = "blacksmith_wall",
        d = "blacksmith_door",
        f = "forge",
        a = "anvil",
        t = "tool_rack"
    },
    
    -- Tags for search/filtering
    tags = {"crafting", "blacksmith", "metalworking", "tools"},
    
    -- Initialize the structure
    init = function(structure, world, x, y)
        -- Set structure position
        structure.x = x
        structure.y = y
        
        -- Set random variant if none specified
        local variantIndex = structure.variantIndex or math.random(#Blacksmith.variants)
        structure.variantIndex = variantIndex
        
        -- Get the variant from the index
        local variant = Blacksmith.variants[variantIndex]
        structure.variant = variant.name
        
        -- Set random age between 0 and 500 years
        structure.age = math.random(0, 500)
        
        -- Initialize cosmetic damage based on age
        local ageFactor = structure.age / 500
        structure.cosmeticDamage = {
            walls = math.min(0.8, ageFactor * 1.2),
            forge = math.min(0.9, ageFactor * 1.5),
            anvil = math.min(0.7, ageFactor),
            tools = math.min(0.6, ageFactor * 0.8),
            chimney = math.min(0.6, ageFactor * 0.8)
        }
        
        -- Set last maintenance time to current age
        structure.lastMaintenance = structure.age
        
        -- Initialize components FIRST
        structure.components = {}
        for _, componentData in ipairs(Blacksmith.components) do
            local component = StructureComponents.createComponent(componentData.type, componentData.options)
            structure.components[componentData.id] = component
        end
        
        -- THEN update component materials based on variant
        for componentId, material in pairs(variant.materials) do
            if structure.components[componentId] then
                structure.components[componentId].options.material = material
            end
        end
        
        -- Update tile legends based on variant
        structure.tileLegend = {
            w = variant.name .. "_blacksmith_wall",
            ["."] = "blacksmith_floor"
        }
        
        structure.interiorLegend = {
            ["."] = variant.name .. "_blacksmith_floor",
            w = variant.name .. "_blacksmith_wall",
            d = "blacksmith_door",
            f = "forge",
            a = "anvil",
            t = "tool_rack"
        }
        
        -- Set forge heat and crafting quality based on variant
        structure.forgeHeat = variant.forgeHeat
        structure.craftingQuality = variant.craftingQuality
        
        -- Adjust levels based on age
        structure.forgeHeat = structure.forgeHeat * (1 - (structure.age * structure.decayRate))
        structure.craftingQuality = structure.craftingQuality * (1 - (structure.age * structure.decayRate))
        
        -- Set up interior
        if structure.hasInterior then
            structure.interior = {
                illuminated = true,
                explored = false,
                integrity = structure.forgeHeat,
                tiles = {},
                features = variant.features
            }
            
            -- Generate interior tiles based on layout
            Blacksmith.generateInterior(structure)
        end
        
        return structure
    end,
    
    -- Update the structure
    update = function(structure, world, dt)
        -- Get current season
        local currentSeason = world.getCurrentSeason()
        local seasonMultiplier = structure.seasonalEffects[currentSeason]
        
        -- Update all components' seasonal state
        for componentId, component in pairs(structure.components) do
            StructureComponents.updateSeason(component, currentSeason)
        end
        
        -- Update forge heat based on component status and season
        local forge = structure.components["forge"]
        local anvil = structure.components["anvil"]
        local toolRack = structure.components["tool_rack"]
        local chimney = structure.components["chimney"]
        local supportPillars = structure.components["support_pillars"]
        
        if forge and anvil and toolRack and chimney and supportPillars then
            -- Calculate forge heat based on component health
            local forgeHealth = forge.health / forge.maxHealth
            local anvilHealth = anvil.health / anvil.maxHealth
            local toolHealth = toolRack.health / toolRack.maxHealth
            local chimneyHealth = chimney.health / chimney.maxHealth
            local supportHealth = supportPillars.health / supportPillars.maxHealth
            
            structure.forgeHeat = math.min(forgeHealth, anvilHealth, toolHealth, chimneyHealth, supportHealth)
            
            -- Apply seasonal decay
            local yearsSinceLastMaintenance = structure.age - structure.lastMaintenance
            local decayFactor = (yearsSinceLastMaintenance * structure.decayRate) * seasonMultiplier
            
            -- Natural maintenance cycle
            if yearsSinceLastMaintenance >= structure.maintenanceCycle then
                -- Structure naturally maintains some quality
                structure.lastMaintenance = structure.age
                structure.forgeHeat = math.min(1.0, structure.forgeHeat + 0.1)
                
                -- Reduce cosmetic damage
                for component, damage in pairs(structure.cosmeticDamage) do
                    structure.cosmeticDamage[component] = math.max(0.0, damage - 0.1)
                end
            else
                -- Apply decay
                structure.forgeHeat = math.max(0.0, structure.forgeHeat - decayFactor)
                
                -- Increase cosmetic damage
                for component, damage in pairs(structure.cosmeticDamage) do
                    structure.cosmeticDamage[component] = math.min(1.0, damage + (decayFactor * 0.1))
                end
            end
        end
        
        -- Update crafting quality based on forge heat and season
        structure.craftingQuality = structure.forgeHeat * seasonMultiplier
        
        -- Update visual effects based on component status and cosmetic damage
        for componentId, component in pairs(structure.components) do
            local visualState = StructureComponents.getVisualState(component)
            
            -- Update tile legend based on visual state
            if component.type == "WALL" then
                local wallDamage = structure.cosmeticDamage.walls
                if component.status == "destroyed" then
                    structure.tileLegend.w = "collapsed_" .. structure.variant .. "_wall"
                elseif component.status == "critical" then
                    structure.tileLegend.w = "damaged_" .. structure.variant .. "_wall"
                elseif wallDamage > 0.7 then
                    structure.tileLegend.w = "weathered_" .. structure.variant .. "_wall"
                elseif wallDamage > 0.3 then
                    structure.tileLegend.w = "worn_" .. structure.variant .. "_wall"
                end
                
                -- Apply seasonal overlay
                if visualState.overlay then
                    structure.tileLegend.w = structure.tileLegend.w .. "_" .. visualState.overlay.name
                end
            elseif component.type == "POWER_SOURCE" and component.status == "critical" then
                -- Add warning effects
                structure.interior.effects = {
                    "smoke_leak",
                    "forge_cooling",
                    "tool_damage"
                }
            end
        end
        
        -- Check structural integrity
        local supportPillars = structure.components["support_pillars"]
        if supportPillars and supportPillars.status == "destroyed" then
            -- Blacksmith is unstable
            structure.condition = structure.condition * 0.5
        end
    end,
    
    -- Repair the structure
    repair = function(structure, world, repairType)
        if repairType == "cosmetic" then
            -- Cosmetic repairs (can be done by NPCs)
            for component, damage in pairs(structure.cosmeticDamage) do
                structure.cosmeticDamage[component] = math.max(0.0, damage - 0.2)
            end
        elseif repairType == "structural" then
            -- Structural repairs (requires more effort)
            structure.lastMaintenance = structure.age
            structure.forgeHeat = math.min(1.0, structure.forgeHeat + 0.3)
            
            -- Repair components
            for componentId, component in pairs(structure.components) do
                if component.status ~= "destroyed" then
                    component.health = math.min(component.maxHealth, component.health + (component.maxHealth * 0.3))
                end
            end
        end
    end,
    
    -- Generate interior layout
    generateInterior = function(structure)
        -- Implementation of interior generation
        -- This would create the actual interior tiles based on the layout
        -- and variant-specific features
    end,
    
    -- Draw the structure
    draw = function(structure, world, x, y)
        -- Draw base structure
        for i = 1, #structure.tileLayout do
            for j = 1, #structure.tileLayout[i] do
                local tile = structure.tileLayout[i][j]
                if tile ~= "." then
                    local tileName = structure.tileLegend[tile]
                    if tileName then
                        -- Draw base tile
                        world.drawTile(tileName, x + j - 1, y + i - 1)
                        
                        -- Draw seasonal overlay if present
                        local component = structure.components["outer_walls"]
                        if component then
                            local visualState = StructureComponents.getVisualState(component)
                            if visualState.overlay then
                                world.drawTileOverlay(
                                    visualState.overlay.name,
                                    x + j - 1,
                                    y + i - 1,
                                    visualState.overlay.modifiers
                                )
                            end
                        end
                    end
                end
            end
        end
    end
}

return Blacksmith 