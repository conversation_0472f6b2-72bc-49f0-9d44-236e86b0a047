-- tiles/hub_grass.lua
local HubGrassTile = {
    id = "hub_grass",
    name = "Sanctuary Grass",
    passable = true,
    variants = 5, -- Different grass appearances for sanctuary
    
    -- Entities that can spawn on sanctuary grass
    spawns = {"memory_flower", "spirit_bunny", "glowing_butterfly", "sanctuary_seedling"},
    
    -- Hub-specific properties
    sanctuaryLevel = 1, -- Affected by hub level
    vitalityLevel = 0.8, -- How vibrant and alive the grass is (0-1)
    memoryInfluence = 0.5, -- How much it's influenced by player memories
    
    -- Movement properties
    movementSpeed = 1.0, -- Standard movement speed
    
    -- Interaction function
    interact = function(tile, entity)
        -- Chance to find special sanctuary items
        if math.random() < 0.15 + (tile.sanctuaryLevel * 0.05) then
            local items = {
                {name = "memory_flower", chance = 0.4},
                {name = "sanctuary_seeds", chance = 0.3},
                {name = "dream_herb", chance = 0.2},
                {name = "essence_clover", chance = 0.1}
            }
            
            for _, item in ipairs(items) do
                if math.random() < item.chance then
                    return {
                        success = true,
                        message = "You found a " .. item.name .. " growing in the sanctuary grass.",
                        effects = {
                            {type = "add_item", item = item.name, quantity = 1}
                        }
                    }
                end
            end
        end
        
        -- Memory recovery at higher sanctuary levels
        if tile.sanctuaryLevel >= 3 and math.random() < 0.1 then
            return {
                success = true,
                message = "As you touch the grass, a memory stirs within you.",
                effects = {
                    {type = "memory_flash", intensity = tile.sanctuaryLevel * 0.2}
                }
            }
        end
        
        -- Standard examination
        local grassDescriptions = {
            "Soft, luminescent grass covers the ground in your sanctuary.",
            "The sanctuary grass sways gently, though there is no wind.",
            "Tiny flowers of light bloom amongst the sanctuary grass.",
            "The grass in your sanctuary seems to pulse with inner life.",
            "This grass feels connected to your own life force."
        }
        
        local description = grassDescriptions[tile.variant] or grassDescriptions[1]
        
        return {
            success = false,
            message = description
        }
    end,
    
    -- Weather effects (limited in sanctuary)
    weatherEffects = {
        rain = function(tile)
            -- Grass becomes more vibrant after rain
            tile.vitalityLevel = math.min(1.0, tile.vitalityLevel + 0.1)
            if tile.vitalityLevel > 0.8 then
                tile.variant = math.random(3, 5) -- More lush variants
            end
            
            -- Chance for special growth
            if math.random() < 0.2 then
                return "spawn", "memory_flower"
            end
            return nil
        end,
        
        light_rain = function(tile)
            -- Even light rain helps sanctuary grass
            tile.vitalityLevel = math.min(1.0, tile.vitalityLevel + 0.05)
            return nil
        end,
        
        rainbow = function(tile)
            -- Rainbows especially enhance sanctuary grass
            tile.vitalityLevel = 1.0
            tile.memoryInfluence = math.min(1.0, tile.memoryInfluence + 0.2)
            
            -- Special rainbow effect
            return "visual_effect", "prismatic_grass"
        end
    },
    
    -- Time effects
    timeEffects = {
        night = function(tile)
            -- Grass glows more at night
            tile.glowIntensity = (tile.baseGlowIntensity or 0.2) * 2
            
            -- Night blooming flowers
            if tile.sanctuaryLevel >= 2 and math.random() < 0.3 then
                return "spawn", "night_blossom"
            end
            return nil
        end,
        
        dawn = function(tile)
            -- Reset glow to normal in day
            tile.glowIntensity = tile.baseGlowIntensity or 0.2
            return nil
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {0.3, 0.8, 0.4}, -- Vibrant green
        variants = {
            {name = "standard", color_shift = {0, 0.1, 0}},
            {name = "luminous", color_shift = {0.1, 0.2, 0}, glow = 0.3},
            {name = "flowered", with_objects = "memory_flowers"},
            {name = "tall", height = 1.2},
            {name = "prismatic", color_shift = {0.2, 0.2, 0.2}, rainbow_effect = true}
        },
        animation = {
            type = "wave",
            strength = 0.3,
            speed = 0.5
        },
        swayWithoutWind = true, -- Special sanctuary property
        glowing = true,
        glow_color = {0.7, 1.0, 0.8},
        glow_intensity = 0.2
    },
    
    -- Audio properties
    footstepSound = "sanctuary_grass_step",
    ambientSound = "gentle_chimes",
    
    -- Sanctuary properties
    sanctuary = {
        safeZone = true,
        energyRestoration = 0.2, -- Energy restored when resting here
        dreamEnhancement = 0.2, -- Enhances dreams when sleeping
        memoryResonance = 0.3 -- Base memory resonance
    },
    
    -- Hub level effects
    applyHubLevel = function(tile, level)
        tile.sanctuaryLevel = level
        tile.vitalityLevel = 0.6 + (level * 0.1)
        tile.memoryInfluence = 0.3 + (level * 0.15)
        
        -- Visual upgrades with levels
        if level >= 2 then
            tile.visual.glow_intensity = 0.2 + (0.1 * level)
            tile.baseGlowIntensity = tile.visual.glow_intensity
        end
        
        -- Higher levels unlock more variants
        if level >= 3 then
            -- Prismatic grass available
            tile.highLevelVariants = true
        end
        
        -- Sanctuary benefits improve with level
        tile.sanctuary.energyRestoration = 0.2 * level
        tile.sanctuary.dreamEnhancement = 0.2 * level
        tile.sanctuary.memoryResonance = 0.3 * level
        
        -- Special effects at higher levels
        if level >= 4 then
            tile.sanctuary.spontaneousHealing = 0.1 * (level - 3)
        end
    end,
    
    -- Connections to other tiles
    connects_to = {
        "hub_grass",
        "hub_floor",
        "hub_path",
        "water"
    },
    
    -- Effect on entities that rest on this tile
    onRest = function(tile, entity)
        -- Apply sanctuary effects
        if entity.stats then
            -- Energy restoration
            if entity.stats.energy then
                entity.stats.energy = math.min(entity.stats.energy + tile.sanctuary.energyRestoration, entity.stats.maxEnergy)
            end
            
            -- Healing effect if available
            if tile.sanctuary.spontaneousHealing and entity.stats.health then
                entity.stats.health = math.min(entity.stats.health + tile.sanctuary.spontaneousHealing, entity.stats.maxHealth)
            end
        end
        
        -- Dream enhancement on sleep
        if entity.sleeping then
            return {
                effect = "enhanced_dreams",
                intensity = tile.sanctuary.dreamEnhancement,
                message = "Your dreams are vivid and restorative."
            }
        end
        
        return {
            message = "The sanctuary grass comforts you as you rest.",
            effects = {
                {type = "apply_status", status = "sanctuary_comfort", duration = 180}
            }
        }
    end,
    
    -- Effect when walking on the grass
    onWalk = function(tile, entity)
        -- Grass rustles with a magical quality
        return {
            sound = "sanctuary_rustle",
            volume = 0.3,
            visual = "tiny_light_motes",
            intensity = tile.vitalityLevel
        }
    end
}

function HubGrassTile.init(world)
    print("Hub grass tile module initialized")
    
    -- Register with sanctuary system
    if world.systems and world.systems.sanctuary then
        world.systems.sanctuary:registerSanctuaryElement("hub_grass", "flora")
    end
end

return HubGrassTile