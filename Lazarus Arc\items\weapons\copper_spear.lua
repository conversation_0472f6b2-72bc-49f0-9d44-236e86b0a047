local CopperSpear = {
    id = "copper_spear",
    name = "Copper Spear",
    type = "weapon",
    category = "spear",
    
    -- Properties
    properties = {
        damage = 24,
        attackSpeed = 1.0,
        range = 2.6,
        durability = 160,
        maxDurability = 160,
        pierceChance = 0.3,
        pierceCount = 2,
        armorPenetration = 0.4,
        weight = 1.1,
        thrustPower = 1.2,
        staggerChance = 0.2,
        staggerDuration = 0.3
    },
    
    -- Appearance
    appearance = {
        sprite = "copper_spear",
        scale = 1.1,
        animations = {
            "idle",
            "thrust",
            "sweep",
            "heavy"
        },
        variants = {
            "copper",
            "bronze",
            "iron",
            "steel"
        },
        blendMode = "normal",
        tint = {0.8, 0.5, 0.2},
        alpha = 1.0
    },
    
    -- Sound effects
    sounds = {
        thrust = "spear_thrust",
        sweep = "spear_sweep",
        pierce = "spear_pierce",
        stagger = "spear_stagger"
    },
    
    -- Effects
    effects = {
        pierce = {
            type = "status",
            duration = 0.3,
            effects = {
                damage = 1.0,
                pierce = true
            }
        },
        stagger = {
            type = "status",
            duration = 0.3,
            effects = {
                stagger = true
            }
        }
    }
}

-- Initialize the weapon
function CopperSpear.init(weapon, world)
    -- Copy all fields from CopperSpear template to weapon instance
    for k, v in pairs(CopperSpear) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        thrustCount = 0,
        lastThrustTime = 0
    }

    return weapon
end

-- Update the weapon
function CopperSpear.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Decay thrust count
        if world.time - weapon.properties.weaponState.lastThrustTime > 1.0 then
            weapon.properties.weaponState.thrustCount = 0
        end
    end
end

-- Handle attack
function CopperSpear.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Calculate attack direction
    local angle = math.atan2(
        target.position.y - attacker.position.y,
        target.position.x - attacker.position.x
    )
    
    -- Create thrust effect
    if world.createEffect then
        world.createEffect({
            type = "thrust",
            position = attacker.position,
            angle = angle,
            range = weapon.properties.range,
            duration = 0.4
        })
    end
    
    -- Check for pierce effect
    if math.random() < weapon.properties.pierceChance then
        -- Create pierce effect
        if world.createEffect then
            world.createEffect({
                type = "pierce",
                position = target.position,
                duration = 0.3
            })
        end
        
        -- Apply pierce effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.pierce)
        end
        
        -- Play pierce sound
        if world.playSound then
            world.playSound(weapon.sounds.pierce)
        end
    end
    
    -- Update thrust count
    if world.time - weapon.properties.weaponState.lastThrustTime <= 1.0 then
        weapon.properties.weaponState.thrustCount = weapon.properties.weaponState.thrustCount + 1
    else
        weapon.properties.weaponState.thrustCount = 1
    end
    weapon.properties.weaponState.lastThrustTime = world.time
    
    -- Check for stagger
    if math.random() < weapon.properties.staggerChance then
        -- Create stagger effect
        if world.createEffect then
            world.createEffect({
                type = "stagger",
                position = target.position,
                duration = weapon.properties.staggerDuration
            })
        end
        
        -- Apply stagger effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.stagger)
        end
        
        -- Play stagger sound
        if world.playSound then
            world.playSound(weapon.sounds.stagger)
        end
    end
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function CopperSpear.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough thrusts
    if weapon.properties.weaponState.thrustCount < 2 then
        return false
    end
    
    -- Create heavy thrust effect
    if world.createEffect then
        world.createEffect({
            type = "heavy_thrust",
            position = attacker.position,
            angle = math.atan2(
                target.position.y - attacker.position.y,
                target.position.x - attacker.position.x
            ),
            range = weapon.properties.range * 1.2,
            duration = 0.6
        })
    end
    
    -- Apply pierce effect with increased power
    if target.applyEffect then
        target.applyEffect({
            type = "pierce",
            duration = 0.4,
            effects = {
                damage = weapon.properties.thrustPower,
                pierce = true
            }
        })
    end
    
    -- Reset thrust count
    weapon.properties.weaponState.thrustCount = 0
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 2)
    
    return true
end

return CopperSpear 