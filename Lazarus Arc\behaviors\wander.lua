-- behaviors/wander.lua
-- Random wandering behavior for entities

-- We'll define a function first that will be our module
local function createWanderBehavior()
    local WanderBehavior = {
        id = "wander",
        name = "Wander Behavior",
        
        -- Configuration parameters
        defaultConfig = {
            moveSpeed = 1.0,        -- Base movement speed
            changeDirectionChance = 0.02, -- Chance to change direction per update
            idleChance = 0.3,       -- Chance to stop and idle
            idleDuration = {2, 8},  -- Min/max seconds to idle
            wanderRadius = 10,      -- How far from origin to wander
            avoidEdges = true,      -- Avoid map boundaries
            avoidObstacles = true,  -- Avoid impassable tiles
            returnToOrigin = false, -- Whether to eventually return home
            originPullStrength = 0.1 -- How strongly to pull back to origin
        }
    }
    
    -- Initialize the behavior with an entity
    function WanderBehavior.init(entity, customConfig)
        -- Handle the case where this is being called during module registration
        if entity == WanderBehavior then
            -- Being called as part of module registration
            -- Store worldCore if needed: WanderBehavior.worldCore = customConfig
            return
        end
        
        -- Create behavior state for this entity
        entity.wanderState = entity.wanderState or {
            idleTimer = 0,
            isIdle = false,
            direction = {x = 0, y = 0},
            originX = entity.position and entity.position.x or 0,
            originY = entity.position and entity.position.y or 0,
            config = {}
        }
        
        -- Apply configuration (custom settings override defaults)
        local config = entity.wanderState.config
        for k, v in pairs(WanderBehavior.defaultConfig) do
            config[k] = v
        end
        
        if customConfig then
            for k, v in pairs(customConfig) do
                config[k] = v
            end
        end
        
        -- Set initial movement direction
        WanderBehavior.changeDirection(entity)
    end
    
    -- Update function called on every game tick
    function WanderBehavior.update(entity, world, dt)
        local state = entity.wanderState
        if not state then
            WanderBehavior.init(entity)
            state = entity.wanderState
        end
        
        local config = state.config
        
        -- Handle idle state
        if state.isIdle then
            state.idleTimer = state.idleTimer - dt
            if state.idleTimer <= 0 then
                state.isIdle = false
                WanderBehavior.changeDirection(entity)
            else
                -- Stay idle
                entity.velocity.x = 0
                entity.velocity.y = 0
                return
            end
        end
        
        -- Check if should switch to idle
        if not state.isIdle and math.random() < config.idleChance * dt then
            state.isIdle = true
            state.idleTimer = math.random(config.idleDuration[1], config.idleDuration[2])
            entity.velocity.x = 0
            entity.velocity.y = 0
            return
        end
        
        -- Check if should change direction
        if math.random() < config.changeDirectionChance then
            WanderBehavior.changeDirection(entity)
        end
        
        -- Apply velocity based on direction and speed
        entity.velocity.x = state.direction.x * config.moveSpeed
        entity.velocity.y = state.direction.y * config.moveSpeed
        
        -- Handle boundaries and obstacles if needed
        if config.avoidEdges then
            WanderBehavior.avoidEdges(entity, world)
        end
        
        if config.avoidObstacles then
            WanderBehavior.avoidObstacles(entity, world)
        end
        
        -- Pull back to origin if needed
        if config.returnToOrigin then
            WanderBehavior.pullToOrigin(entity)
        end
    end
    
    -- Change to a random direction
    function WanderBehavior.changeDirection(entity)
        local state = entity.wanderState
        
        -- Choose from 8 directions
        local directions = {
            {x = 0, y = -1},  -- N
            {x = 1, y = -1},  -- NE
            {x = 1, y = 0},   -- E
            {x = 1, y = 1},   -- SE
            {x = 0, y = 1},   -- S
            {x = -1, y = 1},  -- SW
            {x = -1, y = 0},  -- W
            {x = -1, y = -1}  -- NW
        }
        
        local dirIndex = math.random(#directions)
        state.direction = directions[dirIndex]
        
        -- Normalize for diagonal movement
        if state.direction.x ~= 0 and state.direction.y ~= 0 then
            local normalizer = 0.7071 -- 1/sqrt(2)
            state.direction.x = state.direction.x * normalizer
            state.direction.y = state.direction.y * normalizer
        end
    end
    
    -- Avoid map edges
    function WanderBehavior.avoidEdges(entity, world)
        local state = entity.wanderState
        local pos = entity.position
        local bounds = world.size or {width = 1000, height = 1000}
        local edgeBuffer = 5 -- How close to get to edge before turning
        
        -- Check world boundaries
        if pos.x < edgeBuffer and state.direction.x < 0 then
            state.direction.x = -state.direction.x
        elseif pos.x > bounds.width - edgeBuffer and state.direction.x > 0 then
            state.direction.x = -state.direction.x
        end
        
        if pos.y < edgeBuffer and state.direction.y < 0 then
            state.direction.y = -state.direction.y
        elseif pos.y > bounds.height - edgeBuffer and state.direction.y > 0 then
            state.direction.y = -state.direction.y
        end
    end
    
    -- Avoid obstacles like impassable tiles
    function WanderBehavior.avoidObstacles(entity, world)
        local state = entity.wanderState
        local pos = entity.position
        local chunkSystem = world.chunkSystem
        
        -- Check tile ahead in movement direction
        local tileAheadX = pos.x + state.direction.x * 2
        local tileAheadY = pos.y + state.direction.y * 2
        
        local tileAhead = chunkSystem:getTileAt(tileAheadX, tileAheadY)
        
        if tileAhead then
            -- Get tile definition to check if passable
            local tileModule = world.modules.tiles[tileAhead.type]
            
            if tileModule and not tileModule.passable then
                -- Hit an obstacle, change direction
                WanderBehavior.changeDirection(entity)
            end
        end
    end
    
    -- Pull entity back toward its origin point
    function WanderBehavior.pullToOrigin(entity)
        local state = entity.wanderState
        local config = state.config
        
        -- Calculate distance from origin
        local dx = state.originX - entity.position.x
        local dy = state.originY - entity.position.y
        local distSq = dx*dx + dy*dy
        
        -- If outside wander radius, apply pull force toward origin
        if distSq > config.wanderRadius * config.wanderRadius then
            -- Calculate pull direction
            local dist = math.sqrt(distSq)
            local pullX = dx / dist
            local pullY = dy / dist
            
            -- Blend current direction with pull direction
            state.direction.x = state.direction.x * (1 - config.originPullStrength) + 
                                pullX * config.originPullStrength
            state.direction.y = state.direction.y * (1 - config.originPullStrength) + 
                                pullY * config.originPullStrength
            
            -- Normalize
            local dirLength = math.sqrt(state.direction.x^2 + state.direction.y^2)
            if dirLength > 0 then
                state.direction.x = state.direction.x / dirLength
                state.direction.y = state.direction.y / dirLength
            end
        end
    end
    
    return WanderBehavior
end

-- Create and return the behavior module
return createWanderBehavior()