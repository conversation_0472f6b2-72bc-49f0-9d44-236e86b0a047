-- items/weapons/axe.lua

local Weapon = {
    id = "weapon_02",
    name = "Iron Axe",
    description = "A sturdy iron axe favored by warriors for its increased damage.",
    type = "axe",
    category = "weapon",
    stackable = false,

    stats = {
        attack_power = 15.0,
        attack_speed = 0.8,
        durability = 120.0,
        repair_cost = 30,
        weight = 7.0,
    },

    damage_type = "physical",
    material = "iron", -- Data from materials.lua

    sprite = "res://Sprite/items/weapons/weapon_02.png",
    size = { width = 64, height = 64 },

    effects = {
        critical_chance = 0.05,
        bleed_effect = {
            chance = 0.10,
            damage_per_second = 2,
            duration = 5
        }
    },

    meta = {
        rarity = "uncommon"
    },

    take_damage = function(self, amount)
        self.stats.durability = math.max(0, self.stats.durability - amount)
        if self.stats.durability == 0 then
            print(self.name .. " is broken! Penalties applied.")
            -- Potential penalties could be applied here
        end
    end,

    repair = function(self)
        local repair_amount = 120 - self.stats.durability
        self.stats.durability = 120
        print(self.name .. " repaired by " .. repair_amount .. " points.")
    end
}

return Weapon
