-- entities/sprite.lua
-- Sprite with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local Sprite = {
    id = "sprite",
    name = "Sprite",
    type = "sprite",
    shape = {
        {0, -0.5}, {0.4, -0.4}, {0.5, 0}, {0.4, 0.4},
        {0, 0.5}, {-0.4, 0.4}, {-0.5, 0}, {-0.4, -0.4}
    },
    size = 3,  -- Between pixie and fairy size

    -- Entity categories
    categories = {"fae", "elemental", "flying", "nature", "guardian"},
    threatCategories = {"pollution", "corruption", "undead"},
    
    -- Base stats (elemental guardian)
    maxHealth = 25,
    health = 25,
    maxStamina = 35,
    stamina = 35,
    maxMana = 80,
    mana = 80,
    speed = 3.8,
    attack = 8,
    defense = 6,
    magicAttack = 35,
    magicDefense = 30,
    flight_altitude = 18,
    elemental_affinity = 30,
    nature_guardian = 25,
    
    -- Behaviors
    behaviors = {"elemental_flight", "nature_protection", "elemental_magic", "guardian_duty"},
    behaviorConfigs = {
        elemental_flight = {
            elementalTrail = true,
            harmonicMovement = 1.8,
            naturalGrace = 2.0,
            elementalBoost = 1.5
        },
        nature_protection = {
            protectionRadius = 20,
            healingAura = 1.5,
            corruptionCleansing = 2.0,
            guardianVigilance = 2.5
        },
        elemental_magic = {
            elementalSpells = 2.5,
            naturalHarmony = 2.0,
            seasonalMagic = 1.8,
            elementalMastery = 2.2
        },
        guardian_duty = {
            protectiveInstinct = 3.0,
            territoryDefense = 2.0,
            allySupport = 2.5,
            righteousWrath = 1.8
        }
    },
    
    -- Enhanced variant system for sprites
    variantChances = {
        normal = 0.65,          -- 65% water sprite
        shiny = 0.25,           -- 25% fire sprite (shiny)
        rare = 0.08,            -- 8% earth sprite (rare)
        legendary = 0.02        -- 2% air sprite lord (legendary)
    },
    
    variants = {
        normal = {
            name = "Water Sprite",
            description = "A gentle sprite that guards streams and springs",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.6, 0.9, 1.2, 1.0}  -- Aqua blue
            }
        },
        
        shiny = {
            name = "Fire Sprite",
            description = "A passionate sprite that dances with flames",
            statModifiers = {
                maxHealth = 1.3,    -- 32.5 health
                magicAttack = 1.6,  -- 56 magic attack
                speed = 1.4,        -- 5.32 speed
                fire_mastery = 2.5,
                passion_power = 2.0,
                flame_dance = 1.8
            },
            appearanceModifiers = {
                scale = 1.1,
                glow = true,
                colorTint = {1.4, 0.8, 0.4, 1.0},  -- Fiery orange-red
                flame_aura = true,
                dancing_flames = true,
                heat_shimmer = true
            },
            soundModifiers = {
                pitch = 1.2,
                volume = 1.3,
                crackling = true
            }
        },
        
        rare = {
            name = "Earth Sprite",
            description = "A steadfast sprite that nurtures the soil and plants",
            statModifiers = {
                maxHealth = 1.8,    -- 45 health
                defense = 1.8,      -- 10.8 defense
                magicDefense = 1.6, -- 48 magic defense
                earth_mastery = 3.0,
                growth_power = 2.5,
                stability = 2.0
            },
            appearanceModifiers = {
                scale = 1.2,
                colorTint = {0.7, 1.1, 0.6, 1.0},  -- Rich earth green
                moss_covering = true,
                flower_crown = true,
                earth_integration = true,
                plant_growth = true
            },
            soundModifiers = {
                pitch = 0.9,
                volume = 1.2,
                reverb = true,
                earthy = true
            }
        },
        
        legendary = {
            name = "Air Sprite Lord",
            description = "A majestic sprite that commands the winds and storms",
            statModifiers = {
                maxHealth = 2.2,    -- 55 health
                magicAttack = 2.5,  -- 87.5 magic attack
                speed = 2.0,        -- 7.6 speed
                air_mastery = 5.0,
                storm_control = 3.0,
                wind_lordship = 10.0,
                atmospheric_dominion = 4.0
            },
            appearanceModifiers = {
                scale = 1.4,
                glow = true,
                colorTint = {1.2, 1.3, 1.6, 1.0},  -- Sky blue with wind currents
                wind_crown = true,
                storm_aura = "legendary",
                air_mastery = true,
                atmospheric_control = true
            },
            soundModifiers = {
                pitch = 1.1,
                volume = 1.5,
                reverb = true,
                echo = true,
                wind_harmony = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "sprite_essence", chance = 0.9, quantity = {1, 2}},
        {id = "elemental_wing", chance = 0.7, quantity = {1, 1}},
        {id = "nature_blessing", chance = 0.8, quantity = {1, 2}},
        {id = "guardian_spirit", chance = 0.5, quantity = {1, 1}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "fire_sprite_essence", chance = 0.9, quantity = {1, 2}},
            {id = "flame_wing", chance = 0.8, quantity = {1, 1}},
            {id = "passion_crystal", chance = 0.7, quantity = {1, 1}},
            {id = "dancing_flame_core", chance = 0.6, quantity = {1, 1}}
        },
        rare = {
            {id = "earth_sprite_essence", chance = 0.9, quantity = {1, 2}},
            {id = "growth_wing", chance = 0.8, quantity = {1, 1}},
            {id = "stability_crystal", chance = 0.7, quantity = {1, 1}},
            {id = "earth_mastery_core", chance = 0.6, quantity = {1, 1}}
        },
        legendary = {
            {id = "air_sprite_lord_essence", chance = 0.95, quantity = {1, 2}},
            {id = "wind_crown_fragment", chance = 0.9, quantity = {1, 1}},
            {id = "storm_control_orb", chance = 0.8, quantity = {1, 1}},
            {id = "atmospheric_dominion_crystal", chance = 0.7, quantity = {1, 1}},
            {id = "wind_lordship_gem", chance = 0.6, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "sprite",
        scale = 0.6,
        animations = {
            "elemental_dance", "guardian_patrol", "nature_blessing", "elemental_magic", "storm_command"
        },
        variants = {
            "water_sprite", "fire_sprite", "earth_sprite", "air_sprite_lord"
        }
    },
    
    -- Sound effects with elemental characteristics
    sounds = {
        elemental_chime = {
            file = "sprite_chime",
            synth = {
                instrument = "harp",
                notes = {"D4", "F4", "A4", "D5"},
                durations = {0.3, 0.25, 0.3, 0.4},
                volume = 0.4,
                elemental = true
            }
        },
        nature_whisper = {
            file = "nature_whisper",
            synth = {
                instrument = "flute",
                notes = {"G4", "A4", "C5", "D5"},
                durations = {0.4, 0.3, 0.4, 0.5},
                volume = 0.3,
                natural = true
            }
        },
        guardian_call = {
            file = "guardian_call",
            synth = {
                instrument = "choir",
                notes = {"F3", "A3", "C4", "F4"},
                durations = {0.6, 0.5, 0.6, 0.8},
                volume = 0.5,
                protective = true
            }
        },
        elemental_harmony = {
            file = "elemental_harmony",
            synth = {
                instrument = "celesta",
                notes = {"C4", "E4", "G4", "C5", "E5"},
                durations = {0.4, 0.3, 0.4, 0.3, 0.6},
                volume = 0.4,
                harmonious = true
            }
        }
    },
    
    -- Special sprite abilities
    abilities = {
        elemental_mastery = {
            type = "passive",
            description = "Commands elemental forces based on sprite type",
            effect = "elemental_control"
        },
        nature_healing = {
            type = "active",
            description = "Heals allies and purifies corruption",
            effect = "purifying_heal",
            manaCost = 25,
            cooldown = 10
        },
        guardian_shield = {
            type = "active",
            description = "Creates protective barriers around allies",
            effect = "elemental_shield",
            manaCost = 30,
            duration = 12,
            cooldown = 18
        },
        elemental_flight = {
            type = "passive",
            description = "Enhanced flight with elemental trail effects",
            effect = "elemental_movement"
        },
        nature_communion = {
            type = "passive",
            description = "Can communicate with and command natural forces",
            effect = "nature_control"
        },
        guardian_vigilance = {
            type = "passive",
            description = "Detects threats to nature from great distances",
            effect = "threat_detection"
        }
    }
}

-- Initialize the sprite entity using enhanced template
function Sprite.init(entity, world)
    -- Copy all fields from Sprite template to entity instance
    for k, v in pairs(Sprite) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return Sprite
