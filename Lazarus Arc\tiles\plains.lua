-- tiles/plains.lua
local PlainsTile = {
    id = "plains",
    name = "Plains",
    passable = true,
    variants = 6, -- e.g., Short Grass Prairie, Tall Grass Savanna, Rolling Hills, Wildflower Meadow, Dry Plains, Lush Green Plains

    -- Entities commonly found in open plains
    spawns = {"deer_herd", "wild_horse", "prairie_dog_mound", "hawk_circling", "grass_snake", "common_wildflower", "nomad_encampment", "bison_herd"},

    -- Movement properties
    movementSpeed = 1.0, -- Standard movement speed on open ground

    -- Plains-specific properties
    vegetationType = "grass",
    grassHeight = 0.5, -- Average height (0-1+), affects visibility/movement slightly
    visibility = 0.9, -- Generally good visibility (0-1)
    windExposure = 0.7, -- Open terrain is exposed to wind (0-1)
    fertility = 0.6, -- Soil fertility for farming potential (0-1)
    fireRisk = 0.2, -- Base risk of fire spread (increases when dry)

    -- Interaction function
    interact = function(tile, entity)
        -- Forage for plants/herbs
        if entity.skills and entity.skills.herbalism > 0 and math.random() < 0.3 then
             local commonPlants = {
                 {id="common_herb", chance=0.6},
                 {id="wild_grass_seed", chance=0.3},
                 {id="plains_wildflower", chance=0.25}
             }
             for _, plant in ipairs(commonPlants) do
                 if math.random() < plant.chance then
                     return { success=true, message="You forage for useful plants and find some " .. plant.id .. ".", effects={{type="add_item", item=plant.id, quantity=math.random(1,3)}} }
                 end
             end
        end

        -- Look for tracks (harder than mud/snow, easier than rock)
        if entity.skills and entity.skills.tracking > 1 and math.random() < 0.25 then
             local tracks = {"deer_tracks", "coyote_prints", "bison_trail_worn", "humanoid_footprints"}
             local foundTrack = tracks[math.random(#tracks)]
             return { success = true, message = "Scanning the ground, you spot " .. foundTrack .. ".", effects={{type="gain_tracking_clue", clue=foundTrack}} }
        end

        -- Scan the horizon (utilizing good visibility)
        if entity.skills and entity.skills.perception > 0 and math.random() < 0.4 then
             -- Placeholder: Check for distant features in world data
             local distantFeatures = {"distant_mountains", "smoke_on_horizon", "herd_of_animals", "traveler_on_road"}
             local feature = distantFeatures[math.random(#distantFeatures)]
             return { success=true, message="You scan the vast plains and spot " .. feature .. " in the distance.", effects={{type="discover_feature_distant", feature=feature}} }
        end

        -- General examination
        local grassDesc = " Grass stretches across the open land."
        if tile.grassHeight > 0.8 then grassDesc = " Tall grasses sway in the wind here."
        elseif tile.grassHeight < 0.2 then grassDesc = " Short, hardy grasses cover the ground." end
        local terrainDesc = "The plains roll gently here."
        if tile.isFlat then terrainDesc = "The plains are wide and flat here." end -- Assuming an isFlat property for variants

        return {
            success = false,
            message = terrainDesc .. grassDesc
        }
    end,

    -- Weather effects
    weatherEffects = {
        rain = function(tile)
            tile.isWet = true
            tile.fertility = tile.fertility + 0.01 -- Rain is good for plains
            tile.fireRisk = math.max(0, tile.fireRisk - 0.1) -- Reduces fire risk
            if world.rainIntensity > 0.7 and tile.fertility < 0.4 then -- Heavy rain on poor soil = muddy
                 return "transform", "mud_shallow" -- Temporary muddy state?
            end
            return "visual_effect", "wet_grassland"
        end,
        
        heat = function(tile) -- Drying, increases fire risk
            tile.isWet = false
            tile.fireRisk = math.min(0.9, tile.fireRisk + 0.1)
            -- Grass might turn yellow/brown visually
            if tile.fireRisk > 0.7 then return "visual_effect", "dry_grass_yellow" end
            return nil
        end,

        sun = function(tile)
            tile.isWet = false
             -- Slowly increases fire risk if no rain
             if not world.isRaining then tile.fireRisk = math.min(0.9, tile.fireRisk + 0.02) end
             if tile.fireRisk > 0.6 then return "visual_effect", "dry_grass_yellow" else return "remove_visual_effect", "dry_grass_yellow" end
             return "remove_visual_effect", "wet_grassland"
        end,

        freeze = function(tile) -- Ground freezes, grass becomes brittle
             tile.isFrozen = true
             tile.footstepSound = "frozen_grass_crunch"
             -- Fire risk drops significantly
             tile.fireRisk = 0.01
             return "visual_effect", "frosted_plains"
        end,
        
        snow = function(tile) -- Transforms to snow tile
             return "transform", "snow", {underlyingTile="plains", depth=0.3} -- Pass properties to snow tile
        end,

        wind = function(tile) -- Wind is very apparent
             if world.windStrength > 0.5 then
                 return "visual_effect", "wind_swept_grass"
             end
             return nil
        end
    },

     -- Time effects (Seasonal changes could be significant)
    timeEffects = {
        --[[ season_spring = function(tile) tile.grassHeight = 0.6; tile.fertility=0.7; tile.variant_name="Lush Green Plains" end,
        season_summer = function(tile) tile.grassHeight = 0.8; tile.fireRisk=0.4; end,
        season_autumn = function(tile) tile.grassHeight = 0.5; tile.variant_name="Dry Plains"; tile.base_color={0.7, 0.6, 0.3} end,
        season_winter = function(tile) return "transform", "snow" end, ]] -- Example seasonal logic

        night = function(tile)
            tile.ambientSound = "plains_night_crickets"
            tile.visibility = 0.3 -- Lower visibility at night
            -- Increase predator spawns?
            tile.spawnModifier = { type="wolf_pack", chanceMultiplier=1.3 }
            return nil
        end,
        
        day = function(tile)
             tile.ambientSound = "plains_day_wind_birds"
             tile.visibility = 0.9
             tile.spawnModifier = nil
             -- Reset frozen state if temp rises
             if tile.isFrozen and world.temperature > 0 then tile.isFrozen=false; tile.footstepSound="grass_step" end
            return nil
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {0.4, 0.6, 0.2}, -- Default green grass
        variants = {
            {name = "Short Grass Prairie", grassHeight=0.3, texture="grass_short"},
            {name = "Tall Grass Savanna", grassHeight=1.2, texture="grass_tall_savanna", movementSpeed=0.9, visibility=0.6, overlay_objects={{name="acacia_tree_sparse", chance=0.1}}},
            {name = "Rolling Hills", height_map=true, height_scale=0.4, slope_visuals=true},
            {name = "Wildflower Meadow", overlay_objects={{name="wildflower_patch_mixed", chance=0.6}}, fertility=0.8},
            {name = "Dry Plains", base_color={0.7, 0.6, 0.3}, grassHeight=0.4, fireRisk=0.6},
            {name = "Lush Green Plains", base_color={0.3, 0.7, 0.2}, grassHeight=0.7, fertility=0.9}
        },
        decoration_objects = {
            {name = "grass_clump_large", chance = 0.5},
            {name = "small_wildflower", chance = 0.3},
            {name = "animal_burrow_entrance", chance = 0.1},
            {name = "scattered_rock_small", chance = 0.15}
        },
         special_effects = {
             wind_waves_grass = {type="texture_scroll", speed_scale=0.5, direction="wind", when="windy"},
             pollen_particles = {type="particle", density=0.1, when="season_spring or season_summer"}
         },
          weather_overlays = {
             wet_look = {shininess=0.2, when="isWet"},
             frost_covered = {texture="frost_light", when="isFrozen"}
          }
    },
    
    -- Audio properties
    ambientSound = "plains_day_wind_birds",
    footstepSound = "grass_step",
     specialSounds = {
        {name = "grass_rustle_strong", trigger = "windy", chance = 0.5},
        {name = "distant_animal_call_grazer", trigger = "random", chance = 0.05},
        {name = "insect_buzz_low", trigger = "daytime", chance = 0.3},
        {name = "coyote_howl_distant", trigger = "night", chance = 0.04}
    },

    -- Connections to other tiles (Very common connector)
    connects_to = {
        "plains", -- Itself
        "dirt", -- Transition
        "sand", -- Edge of deserts or near coasts/rivers
        "rocky_ground", -- Transition to rockier areas
        "stone", -- Where bedrock emerges
        "forest_edge", -- Border with forests
        "hills", -- Transition to higher elevation
        "mountain_base", -- Leading to mountains
        "riverbank", -- Bordering rivers
        "road", -- Roads often cross plains
        "farmstead", -- Farms common on plains
        "swamp" -- Can border marshy areas
    },

    -- Pathfinding properties
     pathfinding = {
        travel_cost = 1.0, -- Base cost
        visibility = 0.9, -- Good visibility aids navigation
        open_terrain = true -- Flag for AI behavior (e.g., line of sight, travel speed)
    },

    -- Effects on entities that walk on this tile
    onWalk = function(tile, entity)
        local sound = tile.footstepSound or "grass_step"
        if tile.isFrozen then sound = "frozen_grass_crunch" end
        local fatigue = 0.07 * (1 + math.max(0, tile.grassHeight - 0.5)) -- Slightly more fatigue in taller grass
        local effects = {{type="increase_fatigue", amount = fatigue}}
        
        -- Movement speed might be slightly affected by tall grass
        if tile.grassHeight > 1.0 then entity.currentSpeedModifier = 0.9 else entity.currentSpeedModifier = 1.0 end

        return {
            sound = sound,
            volume = 0.85,
            effects = effects
        }
    end,
    
    -- Effect when discovering plains
    onDiscover = function(tile, entity)
        if entity.journal and entity.journal.addDiscovery then
             entity.journal.addDiscovery({
                type = "biome",
                name = "Open Plains",
                location = {x = tile.x, y = tile.y},
                notes = "Wide, open grasslands. Offers good visibility."
            })
        end
        return {
            message = "You enter wide, open plains.",
            effects = {
                {type = "reveal_map", radius = 5} -- Good visibility reveals more map
            }
        }
    end
}

function PlainsTile.init(world)
    print("Plains tile module initialized")
    -- Register with biome/ecosystem systems
     if world.systems and world.systems.ecosystem then
        world.systems.ecosystem:registerBiome("plains", {base_fertility=0.6, primary_veg="grass"})
    end
end

return PlainsTile