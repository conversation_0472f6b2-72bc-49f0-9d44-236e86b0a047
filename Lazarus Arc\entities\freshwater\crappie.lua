local Crappie = {
    id = "crappie",
    name = "Crappie",
    type = "crappie",

    -- Entity categories
    categories = { "animal", "fish", "small", "prey" },

    -- Threat categories
    threatCategories = { "predator", "large" },
    foodCategories = { "insect", "small_fish" }, -- Eats smaller fish

    -- Stats
    maxHealth = 10,
    health = 10,
    speed = 3.0, -- A bit quicker

    -- Behaviors
    behaviors = { "schooling", "flee", "Foraging" },

    -- Behavior configurations
    behaviorConfigs = {
        schooling = {
            neighborRadius = 6,  -- Larger school radius
            separationDistance = 1.2,
        },
        flee = {
            useCategories = true,
            moveSpeed = 5.0,
            detectionRadius = 7
        },
        觅食 = {
            foodTypes = { "insect", "small_fish" },
            foodRadius = 4, -- Can sense food further away
            chaseSpeed = 4.5 -- Increased speed when chasing food
        }
    },

    -- Appearance
    appearance = {
        sprite = "crappie", -- Replace with your crappie sprite
        scale = 1.0,
        animations = {
            "swim",
            "flee",
            "eat"
        }
    },

    -- Sound effects
    sounds = {
        flop = "fish_flop"
    }
}

-- Initialize the crappie entity
function Crappie.init(entity, world)
    entity.position = entity.position or { x = 0, y = 0 }
    entity.velocity = entity.velocity or { x = 0, y = 0 }

    -- Initialize behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.init then
            behavior.init(entity, entity.behaviorConfigs[behaviorName])
        end
    end

    return entity
end

return Crappie