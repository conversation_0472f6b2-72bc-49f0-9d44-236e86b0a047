-- behaviors/patrolling.lua
-- Patrolling behavior for entities that follow set paths or guard specific areas

local PatrollingBehavior = {}  -- create an empty table

PatrollingBehavior.id = "patrolling"
PatrollingBehavior.name = "Patrolling Behavior"

-- Configuration parameters
PatrollingBehavior.defaultConfig = {
    patrolPath = {},              -- A list of points to follow
    loopPath = true,              -- Whether the entity loops back to the first point
    moveSpeed = 1.5,              -- Speed at which the entity moves along the path
    pauseAtPoints = true,         -- Whether entity pauses at each patrol point
    pauseDuration = {2, 5},       -- Duration of pauses at points (seconds)
    detectionRadius = 8,          -- Radius for detecting intruders
    chaseIntruders = true,        -- Whether to chase entities entering guarded area
    chaseSpeedMultiplier = 1.5,   -- Speed multiplier during a chase
    alertRadius = 10,             -- Radius to alert other guards
    returnToPatrolAfterChase = true -- Return to patrol path after chase ends
}

-- Initialize the behavior
function PatrollingBehavior.init(entity, customConfig)
    entity.patrolState = entity.patrolState or {
        config = {},
        currentTargetIndex = 1,
        isChasing = false,
        currentPathIndex = 1,
        paused = false,
        pauseTimer = 0
    }

    local config = entity.patrolState.config
    for k, v in pairs(PatrollingBehavior.defaultConfig) do
        config[k] = v
    end

    if customConfig then
        for k, v in pairs(customConfig) do
            config[k] = v
        end
    end
end

-- Update function called every game tick
function PatrollingBehavior.update(entity, world, dt)
    local state = entity.patrolState
    local config = state.config

    -- Handle pause at patrol points
    if state.paused then
        state.pauseTimer = state.pauseTimer - dt
        if state.pauseTimer <= 0 then
            state.paused = false
        else
            entity.velocity.x, entity.velocity.y = 0, 0
            return
        end
    end

    -- Move along patrol path
    local target = config.patrolPath[state.currentPatrolIndex]
    if not target then return end

    local dx = target.x - entity.position.x
    local dy = target.y - entity.position.y
    local distance = math.sqrt(dx * dx + dy * dy)

    if distance < 0.5 then
        if config.pauseAtPoints then
            state.paused = true
            state.pauseTimer = math.random(config.pauseDuration[1], config.pauseDuration[2])
        end
        state.currentPatrolIndex = state.currentPatrolIndex % #config.patrolPath + 1
    else
        entity.velocity.x = (dx / distance) * config.moveSpeed
        entity.velocity.y = (dy / distance) * config.moveSpeed
    end

    -- Detect intruders
    if config.chaseIntruders then
        local threat = PatrollingBehavior.detectThreat(entity, world)
        if threat then
            PatrollingBehavior.startChase(entity, threat)
        end
    end

    -- Return to patrol after chasing
    if state.isChasing and not state.currentThreat then
        if config.returnToPatrolAfterChase then
            state.isChasing = false
            state.returningToPatrol = true
        end
    end

    -- Alert other guards if threat detected
    if state.isChasing then
        PatrollingBehavior.alertGuards(entity, world)
    end

    -- Movement towards next patrol point
    local target = config.patrolPath[state.currentPatrolIndex]
    entity.velocity.x = (target.x - entity.position.x) / distance * config.moveSpeed
    entity.velocity.y = (target.y - entity.position.y) / distance * config.moveSpeed
end

-- Detect nearby threats (implementation placeholder)
function PatrollingBehavior.detectThreat(entity, world)
    -- Implementation details...
end

-- Start chasing detected threat (implementation placeholder)
function PatrollingBehavior.startChase(entity, threat)
    -- Implementation details...
end

-- Alert other guards (implementation placeholder)
function PatrollingBehavior.alertGuards(entity, world)
    -- Implementation details...
end

return PatrollingBehavior
