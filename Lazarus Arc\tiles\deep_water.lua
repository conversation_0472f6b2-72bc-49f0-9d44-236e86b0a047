-- tiles/deep_water.lua
local DeepWaterTile = {
    id = "deep_water",
    name = "Deep Water",
    passable = false, -- Impassable without specific means (boat, advanced swimming/diving)
    variants = 5, -- e.g., Dark Blue, Choppy Waves, Murky Depths, Kelp Forest, Abyssal Zone

    -- Entities that can spawn in deep water
    spawns = {"large_fish", "shark", "kraken_juvenile", "deep_sea_angler", "shipwreck_fragment", "giant_squid"}, -- More dangerous/larger creatures

    -- Deep Water-specific properties
    depth = 5.0, -- Significantly deeper than standard water (measured in tiles/units) [cite: 193]
    pressure = 3.0, -- Represents water pressure, could affect diving (arbitrary scale)
    temperature = 5, -- Base temperature is colder than surface water (Celsius)
    hasStrongCurrents = true, -- Currents are more prevalent and stronger
    visibility = 0.4, -- Lower visibility due to depth/murkiness (0-1)

    -- Interaction function (Limited interaction from surface)
    interact = function(tile, entity)
        -- Fishing might yield different results
        if entity.hasItem and entity.hasItem("reinforced_fishing_rod") then -- Requires better gear?
            local fishingSkill = (entity.skills and entity.skills.fishing) or 0
            local catchChance = 0.1 + (fishingSkill * 0.04) -- Harder to fish here?
            
            if math.random() < catchChance then
                local deepFishTypes = { -- Different loot table
                    {id = "deep_sea_cod", name = "Deep Sea Cod", chance = 0.5},
                    {id = "giant_ocean_tuna", name = "Giant Ocean Tuna", chance = 0.3},
                    {id = "leviathan_scale_fragment", name = "Leviathan Scale Fragment", chance = 0.1}, -- Rare material
                    {id = "glowing_deep_coral", name = "Glowing Deep Coral", chance = 0.1}
                }
                -- Simplified roll (similar to water.lua)
                local cumulativeChance = 0
                local roll = math.random()
                local caughtItem = deepFishTypes[1] -- Default
                for _, fishType in ipairs(deepFishTypes) do
                    cumulativeChance = cumulativeChance + fishType.chance
                    if roll <= cumulativeChance then
                        caughtItem = fishType
                        break
                    end
                end
                return {
                    success = true,
                    message = "With great effort, you haul something from the depths: a " .. caughtItem.name .. "!",
                    effects = {{type = "add_item", item = caughtItem.id, quantity = 1}}
                }
            else
                return { success = false, message = "Whatever was on the line escapes back into the abyss." }
            end

        -- Check for diving ability/gear
        elseif entity.hasAbility and entity.hasAbility("deep_diving") then
             return {
                 success = true,
                 message = "You could attempt to dive into these dark depths, but be wary of the pressure.",
                 effects = {{type="suggest_action", action="dive"}}
             }
         -- Check for appropriate vessel
         elseif entity.isUsing and entity.isUsing("ocean_vessel") then
              return {
                 success = false, -- No direct interaction, just info
                 message = "Your vessel cuts through the deep water. The depths below remain mysterious."
              }
        else
            -- Standard message about impassability
            return {
                success = false,
                message = "The water here is incredibly deep and dark. Crossing without a sturdy vessel or specialized abilities seems impossible." -- Echoes water.txt [cite: 209-210, 226-227]
            }
        end
        return nil
    end,

    -- Weather effects
    weatherEffects = {
        freeze = function(tile)
            -- Deep water rarely freezes completely, maybe forms thin ice patches in extreme cold
            -- Assuming world context is available if needed, e.g., via tile.worldRef
            -- local world = tile.worldRef
            -- if world and world.temperature < -15 and math.random() < 0.1 then
            -- Simplified check for example:
            if math.random() < 0.01 then -- Placeholder for actual temperature check
                 return "create_overlay", "thin_ice_patch"
            end
            return nil -- Less likely to freeze solid than water.lua
        end,
        
        storm = function(tile) -- More significant effect during storms
            tile.waveHeight = (tile.baseWaveHeight or 1.0) + 1.5 -- Large waves
            tile.currentStrength = (tile.baseCurrentStrength or 0.5) + 0.8
            -- Increased danger for boats/swimming
            tile.hazardLevel = 0.8
            return "visual_effect", "stormy_seas"
        end,

        calm = function(tile) -- Reset storm effects
             tile.waveHeight = tile.baseWaveHeight or 1.0
             tile.currentStrength = tile.baseCurrentStrength or 0.5
             tile.hazardLevel = 0.3
             return "remove_visual_effect", "stormy_seas"
        end
    },

     -- Time effects
    timeEffects = {
        night = function(tile)
            tile.visibility = 0.2 -- Even lower visibility at night
            tile.ambientSound = "deep_water_night" -- More ominous sounds?
            -- Chance for bioluminescent creatures/effects
            if math.random() < 0.15 then
                 return "visual_effect", "bioluminescence"
            end
            return nil
        end,
        
        day = function(tile)
             tile.visibility = 0.4
             tile.ambientSound = "deep_water_lapping"
             return "remove_visual_effect", "bioluminescence"
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {0.05, 0.2, 0.5, 0.95}, -- Darker blue, less transparent than water.lua [cite: 219]
        variants = {
            {name = "dark_blue", color_shift={0, 0, -0.1}},
            {name = "choppy_waves", animation_speed=1.5, wave_height=1.5}, -- More pronounced waves
            {name = "murky_depths", transparency=0.1, color_shift={-0.05, -0.05, -0.05}},
            {name = "kelp_forest", overlay_objects={{name="giant_kelp", chance=0.6}}, visibility=0.5}, -- Kelp might improve visibility slightly but add clutter
            {name = "abyssal_zone", color_shift={-0.1, -0.1, -0.2}, transparency=0.05, light_absorption=0.9, isAbyssal=true} -- Almost opaque black/blue, added property
        },
        animation = {
            type = "wave_large", -- Larger, slower waves than water.lua [cite: 220]
            frames = 12,
            speed = 0.8,
            amplitude = 1.2
        },
        reflection = true,
        refraction = 0.1, -- Less light refracts back
        transparency = 0.3, -- Generally less transparent
        depthEffect = true, -- Strong depth effect
        surfaceHighlight = true,
         special_effects = {
             deep_shadows = {intensity=0.6}, -- Suggests things lurking below
             bioluminescence = {type="particle_emitter", particle="glowing_plankton", color={0.3, 0.8, 1.0}, density=0.2, when="night_bioluminescence_active"}
         }
    },
    
    -- Audio properties
    ambientSound = "deep_water_lapping", -- Deeper, heavier sound than water.lua [cite: 221]
     specialSounds = {
        {name = "large_splash", trigger = "nearby_movement_large", chance = 0.1},
        {name = "whale_song_distant", trigger = "random", chance = 0.02},
        {name = "unsettling_depth_groan", trigger = "random", chance = 0.01, condition_property="isAbyssal"} -- Changed condition=tile.isAbyssal
    },

    -- Swimming/Diving/Boating properties (More dangerous than water.lua)
    swimming = {
        speedMultiplier = 0.4, -- Even slower
        staminaDrain = 1.5, -- Higher drain
        drowningRisk = true, -- Higher risk
        hazard_effects = {"strong_current", "deep_pressure", "large_predator"}
    },
     diving = { -- Assumes a diving mechanic exists
         maxDepth = 10, -- How deep can normally dive (affected by gear/skills)
         pressureDamageThreshold = 4.0, -- Pressure level where damage starts
         requiredGear = "diving_suit"
     },
     boatNavigable = true, -- Still navigable by boat
     boatHazardChance = 0.1, -- Higher chance of boat hazards (storms, creatures)

    -- Connections to other tiles
    connects_to = {
        "deep_water", -- Itself
        "water", -- Transition to shallower water
        "underwater_trench", -- Potential future tile
        "underwater_volcano", -- Potential future tile
        "cliff_ocean" -- Can border deep underwater cliffs
    },
    
    -- Harmful to certain entity types (More broadly harmful than water.lua)
    harmfulTo = {
        "fire_elemental",
        "desert_creature",
        "land_animal_non_aquatic", -- Most land animals can't survive here
        "undead_standard" -- Unless specifically aquatic undead
    },

    -- Effects on entities that try to move into this tile
    onMoveAttempt = function(tile, entity)
        -- Check for appropriate vessel
        if entity.isUsing and entity.isUsing("ocean_vessel") then
            return true -- Can enter with a suitable boat
        end
         -- Check for advanced swimming/diving abilities
        if entity.canSwim and entity.skills.swimming > 5 and entity.stats.stamina > 50 then -- Requires high skill/stamina
             -- Still apply significant drain/risk even if possible
            return true, "Entering the deep water will be extremely taxing and dangerous."
        end
        -- Cannot enter
        return false, "The water is far too deep and treacherous to enter without a proper vessel or exceptional swimming ability." -- More prohibitive message than water.txt
    end,

     -- Effect when discovering deep water (e.g., by boat)
    onDiscover = function(tile, entity)
        if entity.journal and entity.journal.addDiscovery then
             entity.journal.addDiscovery({
                type = "water_body",
                name = "Deep Ocean/Sea", -- Or specific name
                location = {x = tile.x, y = tile.y},
                notes = "Vast, deep water. Dangerous depths, requires a boat for safe travel."
            })
        end
        return {
            message = "You sail out into deep, open water. The land fades behind you.",
            effects = {
                {type = "reveal_map", radius = 5} -- Reveal a large area when discovered by boat
            }
        }
    end
}

function DeepWaterTile.init(world)
    print("Deep Water tile module initialized")
    -- Register with navigation, ecosystem, weather systems
    if world and world.systems and world.systems.navigation then
        world.systems.navigation:registerWaterway("deep_water", {hazard_level=0.7, vessel_required="ocean_vessel"})
    end
    if world and world.systems and world.systems.ecosystem then
        world.systems.ecosystem:registerDeepWaterBiome("deep_water")
    end
     if world and world.systems and world.systems.weather then
        world.systems.weather:registerWaterBody("deep_water", {influences_storms=true})
    end
end

return DeepWaterTile
