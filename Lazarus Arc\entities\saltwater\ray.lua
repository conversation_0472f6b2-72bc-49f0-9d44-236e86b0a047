local Ray = {
    id = "ray",
    name = "Electric Ray",
    type = "fish",
    
    -- Entity categories
    categories = {"animal", "fish", "saltwater", "bottom_feeder", "electric"},
    
    -- Threat and food categories
    threatCategories = {"player", "shark"},
    foodCategories = {"crustacean", "mollusk", "small_fish"},
    
    -- Stats
    maxHealth = 40,
    health = 40,
    maxStamina = 60,
    stamina = 60,
    speed = 1.8,
    
    -- Water properties
    water = {
        swimSpeed = 1.8,
        maxDepth = 15,
        preferredDepth = 8,
        oxygenLevel = 1.0,
        temperature = "warm",
        currentSpeed = 1.0
    },
    
    -- Behaviors
    behaviors = {"bottom_feed", "patrol", "hide", "territorial"},
    
    -- Behavior configurations
    behaviorConfigs = {
        bottom_feed = {
            moveSpeed = 1.0,
            searchRadius = 10,
            preferredFood = {"crustacean", "mollusk", "small_fish"},
            successChance = 0.7,
            electricBonus = 0.2
        },
        patrol = {
            moveSpeed = 1.5,
            patrolRadius = 20,
            preferredDepth = 8,
            restInterval = {15, 20}
        },
        hide = {
            moveSpeed = 0.8,
            camouflageChance = 0.8,
            preferredDepth = 10,
            duration = {10, 15}
        },
        territorial = {
            moveSpeed = 1.8,
            territoryRadius = 15,
            preferredDepth = 8,
            aggressionLevel = 0.7
        }
    },
    
    -- Special abilities
    abilities = {
        shock = {
            range = 5,
            damage = 30,
            duration = 0.3,
            cooldown = 4,
            staminaCost = 15,
            effect = "stun"
        },
        electricField = {
            range = 3,
            duration = 2,
            cooldown = 6,
            effect = "damage",
            staminaCost = 20
        },
        camouflage = {
            duration = 5,
            cooldown = 8,
            effect = "hide",
            staminaCost = 10
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "ray",
        scale = 1.2,
        animations = {
            "idle", "swim", "shock", "hide", "feed"
        },
        variants = {
            "spotted", "striped", "mottled", "dark"
        }
    },
    
    -- Sound effects
    sounds = {
        splash = "fish_splash",
        swim = "ray_swim",
        shock = "ray_shock",
        hide = "ray_hide"
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.7, quantity = {1, 2}},
        {id = "electric_organ", chance = 0.5, quantity = {1, 1}},
        {id = "fin", chance = 0.4, quantity = {1, 1}},
        {id = "bone", chance = 0.3, quantity = {1, 1}}
    }
}

-- Initialize the entity
function Ray.init(entity, world)
    -- Copy all fields from Ray template to entity instance
    for k, v in pairs(Ray) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random ray variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Ray.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update water properties
    if entity.water then
        -- Adjust bottom feeding success based on time of day
        if world and world.timeOfDay == "night" then
            entity.behaviorConfigs.bottom_feed.successChance = 0.8
        else
            entity.behaviorConfigs.bottom_feed.successChance = 0.7
        end

        -- Adjust preferred depth based on time of day
        if world and world.timeOfDay == "night" then
            entity.water.preferredDepth = 5
        else
            entity.water.preferredDepth = 8
        end

        -- Apply electric bonus when near prey
        if world and world.entities then
            local nearbyPrey = 0
            for _, other in ipairs(world.entities) do
                if other.type == "fish" and 
                   (other.categories and table.contains(other.categories, "small_fish") or
                    other.categories and table.contains(other.categories, "crustacean")) then
                    local distance = math.sqrt(
                        (other.position.x - entity.position.x)^2 + 
                        (other.position.y - entity.position.y)^2
                    )
                    if distance < 6 then
                        nearbyPrey = nearbyPrey + 1
                    end
                end
            end
            if nearbyPrey > 0 then
                entity.behaviorConfigs.bottom_feed.successChance = 
                    entity.behaviorConfigs.bottom_feed.successChance + 
                    entity.behaviorConfigs.bottom_feed.electricBonus
            end
        end

        -- Adjust hiding behavior based on threats
        if world and world.entities then
            local nearbyThreats = 0
            for _, other in ipairs(world.entities) do
                if other.type == "fish" and other.id == "shark" then
                    local distance = math.sqrt(
                        (other.position.x - entity.position.x)^2 + 
                        (other.position.y - entity.position.y)^2
                    )
                    if distance < 10 then
                        nearbyThreats = nearbyThreats + 1
                    end
                end
            end
            if nearbyThreats > 0 then
                entity.behaviorConfigs.hide.camouflageChance = 0.9
            else
                entity.behaviorConfigs.hide.camouflageChance = 0.8
            end
        end
    end
end

return Ray 