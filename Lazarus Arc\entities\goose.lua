local Goose = {
    id = "goose",
    name = "Goose",
    type = "bird",
    
    -- Entity categories
    categories = {"animal", "bird", "flying", "water", "social"},
    
    -- Threat and food categories
    threatCategories = {"player", "predator"},
    foodCategories = {"plant", "insect", "food"},
    
    -- Stats
    maxHealth = 40,
    health = 40,
    maxStamina = 70,
    stamina = 70,
    speed = 1.8,
    
    -- Flight properties
    flight = {
        maxHeight = 10,
        minHeight = 1,
        ascentSpeed = 0.8,
        descentSpeed = 1.5,
        hoverHeight = 2,
        currentHeight = 2,
        wingFlapRate = 0.12,
        soarChance = 0.3
    },
    
    -- Water properties
    water = {
        swimSpeed = 1.5,
        wadeDepth = 1,
        forageRange = 5
    },
    
    -- Behaviors
    behaviors = {"forage", "swim", "flock", "territorial"},
    
    -- Behavior configurations
    behaviorConfigs = {
        forage = {
            moveSpeed = 1.2,
            searchRadius = 10,
            preferredFood = {"plant", "insect", "food"},
            successChance = 0.7
        },
        swim = {
            moveSpeed = 1.5,
            preferredTerrain = "water",
            groupSpacing = 3,
            directionChange = 0.15
        },
        flock = {
            moveSpeed = 2.0,
            followDistance = 4,
            separationDistance = 2,
            alignmentStrength = 0.4,
            cohesionStrength = 0.5,
            maxGroupSize = 8,
            preferredTime = "day"
        },
        territorial = {
            territoryRadius = 15,
            chaseRadius = 10,
            warningRadius = 5,
            aggressionLevel = 0.8,
            preferredTime = "day"
        }
    },
    
    -- Special abilities
    abilities = {
        charge = {
            speed = 2.5,
            damage = 10,
            duration = 1,
            cooldown = 3,
            staminaCost = 15
        },
        honk = {
            range = 12,
            duration = 1,
            cooldown = 2,
            effect = "intimidate"
        },
        flap = {
            damage = 5,
            duration = 0.5,
            cooldown = 1,
            staminaCost = 5
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "goose",
        scale = 1.0,
        animations = {
            "idle", "fly", "swim", "charge", "honk", "flap"
        },
        variants = {
            "canada", "snow", "gray", "white"
        }
    },
    
    -- Sound effects with synth configuration
    sounds = {
        honk = {
            file = "goose_honk",
            synth = {
                instrument = "saxophone",
                notes = {"Bb3", "F3"},
                durations = {0.4, 0.6},
                volume = 0.5,
                vibrato = true,
                vibratoRate = 3.5
            }
        },
        wingFlap = {
            file = "goose_wing_flap",
            synth = {
                instrument = "marimba",
                notes = {"F3", "A3", "F3"},
                durations = {0.15, 0.1, 0.15},
                volume = 0.35
            }
        },
        hiss = {
            file = "goose_hiss",
            synth = {
                instrument = "electric_guitar",
                notes = {"F#3"},
                duration = 0.5,
                volume = 0.4,
                vibrato = true,
                vibratoRate = 12.0
            }
        },
        footstep = {
            synth = {
                instrument = "marimba",
                notes = {"F3"},
                duration = 0.1,
                volume = 0.2
            }
        },
        fly = {
            synth = {
                instrument = "accordion",
                notes = {"Bb3", "D4"},
                durations = {0.25, 0.25},
                volume = 0.4
            }
        },
        swim = {
            synth = {
                instrument = "vibraphone",
                notes = {"F3", "A3"},
                durations = {0.3, 0.3},
                volume = 0.3
            }
        },
        hurt = {
            synth = {
                instrument = "saxophone",
                notes = {"D#3"},
                duration = 0.4,
                volume = 0.55,
                vibrato = true,
                vibratoRate = 8.0
            }
        },
        death = {
            synth = {
                instrument = "bass_guitar",
                notes = {"Bb3", "F3", "D3"},
                durations = {0.5, 0.5, 1.0},
                volume = 0.5,
                vibrato = true,
                vibratoRate = 2.0
            }
        },
        alert = {
            synth = {
                instrument = "trumpet",
                notes = {"Bb4", "F4"},
                durations = {0.3, 0.4},
                volume = 0.5
            }
        }
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.7, quantity = {2, 3}},
        {id = "feather", chance = 0.8, quantity = {2, 4}},
        {id = "egg", chance = 0.4, quantity = {1, 1}},
        {id = "down", chance = 0.5, quantity = {2, 3}},
        {id = "beak", chance = 0.3, quantity = {1, 1}}
    }
}

-- Initialize the entity
function Goose.init(entity, world)
    -- Copy all fields from Goose template to entity instance
    for k, v in pairs(Goose) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random goose variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Goose.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update flight properties
    if entity.flight then
        -- Update wing flapping based on height and activity
        if entity.flight.currentHeight > entity.flight.minHeight then
            if entity.flight.soarChance > math.random() then
                entity.flight.wingFlapRate = 0.06
            else
                entity.flight.wingFlapRate = 0.12
            end
        else
            entity.flight.wingFlapRate = 0.2
        end
    end

    -- Update water properties
    if entity.water then
        -- Adjust aggression based on time of day
        if world and world.timeOfDay == "dawn" or world.timeOfDay == "dusk" then
            entity.behaviorConfigs.territorial.aggressionLevel = 0.9
        else
            entity.behaviorConfigs.territorial.aggressionLevel = 0.8
        end
    end
end

return Goose 