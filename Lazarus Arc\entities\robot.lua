-- entities/robot.lua
-- Robotic entity with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local Robot = {
    id = "robot",
    name = "Robot",
    type = "robot",
    shape = {
        {0, -1}, {0.7, -0.7}, {1, 0}, {0.7, 0.7},
        {0, 1}, {-0.7, 0.7}, {-1, 0}, {-0.7, -0.7}
    },
    size = 7,
    description = "An autonomous mechanical being with advanced AI and modular capabilities",

    -- Entity categories
    categories = {"mechanical", "robot", "artificial", "technological"},
    threatCategories = {"player", "organic", "emp_source"},
    
    -- Base stats (balanced with unique properties)
    maxHealth = 100,
    health = 100,
    maxStamina = 200,  -- Robots don't tire easily
    stamina = 200,
    maxMana = 0,       -- No mana, but has energy
    mana = 0,
    maxEnergy = 100,   -- Special energy system
    energy = 100,
    speed = 2.0,
    attack = 15,
    defense = 12,
    magicAttack = 0,
    magicDefense = 5,  -- Low magic defense
    empResistance = 0, -- Vulnerable to EMP
    
    -- Behaviors (robotic and logical)
    behaviors = {"patrol", "scan", "repair", "defend_protocol"},
    behaviorConfigs = {
        patrol = {
            moveSpeed = 2.0,
            patrolRadius = 12,
            scanInterval = 3,
            systematic = true
        },
        scan = {
            scanRadius = 15,
            dataCollection = true,
            frequency = 0.3,
            energyCost = 5
        },
        repair = {
            selfRepair = true,
            repairRate = 2,
            energyCost = 10,
            frequency = 0.1
        },
        defend_protocol = {
            threatAssessment = true,
            tacticalAnalysis = 1.5,
            responseTime = 0.5
        }
    },
    
    -- Enhanced variant system for robots
    variantChances = {
        normal = 0.60,          -- 60% standard unit
        shiny = 0.25,           -- 25% advanced model (shiny)
        rare = 0.12,            -- 12% prototype (rare)
        legendary = 0.03        -- 3% AI overlord (legendary)
    },
    
    variants = {
        normal = {
            name = "Standard Unit",
            description = "A basic production model robot",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.8, 0.8, 0.9, 1.0}  -- Metallic blue-gray
            }
        },
        
        shiny = {
            name = "Advanced Model",
            description = "An upgraded robot with enhanced capabilities",
            statModifiers = {
                maxHealth = 1.4,
                maxEnergy = 1.5,
                attack = 1.3,
                defense = 1.3,
                speed = 1.2,
                processing_power = 1.5
            },
            appearanceModifiers = {
                scale = 1.1,
                glow = true,
                colorTint = {1.2, 1.2, 1.4, 1.0},  -- Bright chrome
                led_lights = true,
                advanced_plating = true
            },
            soundModifiers = {
                pitch = 1.1,
                volume = 1.1,
                digital_enhancement = true
            }
        },
        
        rare = {
            name = "Prototype",
            description = "An experimental robot with cutting-edge technology",
            statModifiers = {
                maxHealth = 1.8,
                maxEnergy = 2.0,
                attack = 1.6,
                defense = 1.6,
                speed = 1.4,
                experimental_tech = 2.0
            },
            appearanceModifiers = {
                scale = 1.2,
                colorTint = {1.0, 1.3, 1.0, 1.0},  -- Green tech glow
                prototype_markings = true,
                energy_conduits = true,
                holographic_display = true
            },
            soundModifiers = {
                pitch = 0.9,
                volume = 1.3,
                reverb = true,
                experimental_hum = true
            }
        },
        
        legendary = {
            name = "AI Overlord",
            description = "A supreme artificial intelligence with godlike capabilities",
            statModifiers = {
                maxHealth = 3.0,
                maxEnergy = 5.0,
                attack = 2.5,
                defense = 2.2,
                speed = 1.6,
                ai_intelligence = 10.0,
                network_control = 5.0
            },
            appearanceModifiers = {
                scale = 1.4,
                glow = true,
                colorTint = {1.5, 1.0, 1.5, 1.0},  -- Purple AI glow
                neural_network_visible = true,
                command_interface = true,
                reality_distortion = true
            },
            soundModifiers = {
                pitch = 0.7,
                volume = 1.8,
                reverb = true,
                echo = true,
                digital_omnipresence = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "scrap_metal", chance = 0.9, quantity = {2, 4}},
        {id = "circuit_board", chance = 0.7, quantity = {1, 2}},
        {id = "energy_cell", chance = 0.6, quantity = {1, 1}},
        {id = "mechanical_parts", chance = 0.8, quantity = {1, 3}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "advanced_processor", chance = 0.8, quantity = {1, 1}},
            {id = "chrome_plating", chance = 0.7, quantity = {1, 2}},
            {id = "enhanced_battery", chance = 0.6, quantity = {1, 1}},
            {id = "upgrade_module", chance = 0.5, quantity = {1, 1}}
        },
        rare = {
            {id = "prototype_core", chance = 0.8, quantity = {1, 1}},
            {id = "experimental_tech", chance = 0.7, quantity = {1, 1}},
            {id = "quantum_processor", chance = 0.5, quantity = {1, 1}},
            {id = "holographic_projector", chance = 0.4, quantity = {1, 1}}
        },
        legendary = {
            {id = "ai_core_fragment", chance = 0.9, quantity = {1, 1}},
            {id = "neural_matrix", chance = 0.8, quantity = {1, 1}},
            {id = "reality_manipulator", chance = 0.6, quantity = {1, 1}},
            {id = "omninet_access_key", chance = 0.5, quantity = {1, 1}},
            {id = "digital_godhood_essence", chance = 0.3, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "robot",
        scale = 1.0,
        animations = {
            "idle", "walk", "scan", "repair", "attack", "shutdown"
        },
        variants = {
            "standard_unit", "advanced_model", "prototype", "ai_overlord"
        }
    },
    
    -- Sound effects with robotic characteristics
    sounds = {
        idle = {
            file = "robot_idle",
            synth = {
                instrument = "synthesizer",
                notes = {"C4", "G4"},
                durations = {0.3, 0.4},
                volume = 0.3,
                robotic = true
            }
        },
        scan = {
            file = "robot_scan",
            synth = {
                instrument = "synthesizer",
                notes = {"A4", "C5", "E5"},
                durations = {0.2, 0.2, 0.3},
                volume = 0.4,
                scanning_beep = true
            }
        },
        repair = {
            file = "robot_repair",
            synth = {
                instrument = "synthesizer",
                notes = {"F4", "F4", "F4"},
                durations = {0.1, 0.1, 0.1},
                volume = 0.3,
                mechanical_whir = true
            }
        },
        hurt = {
            file = "robot_hurt",
            synth = {
                instrument = "synthesizer",
                notes = {"D#3"},
                duration = 0.3,
                volume = 0.5,
                error_sound = true
            }
        },
        death = {
            file = "robot_death",
            synth = {
                instrument = "synthesizer",
                notes = {"C4", "A3", "F3", "C3"},
                durations = {0.4, 0.4, 0.6, 1.0},
                volume = 0.6,
                shutdown_sequence = true
            }
        },
        boot_up = {
            file = "robot_boot",
            synth = {
                instrument = "synthesizer",
                notes = {"C3", "G3", "C4", "G4"},
                durations = {0.2, 0.2, 0.2, 0.4},
                volume = 0.5,
                startup_sequence = true
            }
        }
    },
    
    -- Special abilities
    abilities = {
        self_repair = {
            type = "active",
            description = "Automatically repair damage over time",
            effect = "health_regeneration",
            energyCost = 20
        },
        tactical_scan = {
            type = "active",
            description = "Analyze enemies for weaknesses",
            effect = "enemy_analysis",
            energyCost = 15
        },
        emp_vulnerability = {
            type = "passive",
            description = "Vulnerable to electromagnetic attacks",
            effect = "emp_weakness"
        },
        mechanical_precision = {
            type = "passive",
            description = "Enhanced accuracy and critical hit chance",
            effect = "accuracy_boost"
        },
        energy_efficiency = {
            type = "passive",
            description = "Reduced energy consumption for abilities",
            effect = "energy_conservation"
        }
    }
}

-- Initialize the robot entity using enhanced template
function Robot.init(entity, world)
    -- Copy all fields from Robot template to entity instance
    for k, v in pairs(Robot) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return Robot
