import SpriteGenerator from './spriteGenerator.js';

class WeaponGenerator extends SpriteGenerator {
    constructor(canvas) {
        super(canvas);
        this.weaponType = 'sword';
        this.weaponStyle = 'basic';
        this.currentPalette = ['#CD7F32', '#B87333', '#A66D32', '#8B4513']; // Bronze palette
    }

    // Set weapon type
    setWeaponType(type) {
        this.weaponType = type;
        this.drawBaseShape();
    }

    // Set weapon style
    setWeaponStyle(style) {
        this.weaponStyle = style;
        this.drawBaseShape();
    }

    // Draw base shape
    drawBaseShape() {
        this.ctx.clearRect(0, 0, this.size, this.size);
        
        // Center the weapon
        this.ctx.save();
        this.ctx.translate(this.size / 2, this.size / 2);
        
        // Draw weapon based on type
        switch (this.weaponType) {
            case 'sword':
                this.drawSword();
                break;
            case 'axe':
                this.drawAxe();
                break;
            case 'mace':
                this.drawMace();
                break;
            case 'spear':
                this.drawSpear();
                break;
        }
        
        this.ctx.restore();
    }

    // Draw sword
    drawSword() {
        // Blade
        this.ctx.fillStyle = this.currentPalette[0];
        this.ctx.fillRect(-this.size * 0.1, -this.size * 0.4, this.size * 0.2, this.size * 0.8);
        
        // Handle
        this.ctx.fillStyle = this.currentPalette[1];
        this.ctx.fillRect(-this.size * 0.05, this.size * 0.4, this.size * 0.1, this.size * 0.2);
        
        // Guard
        this.ctx.fillStyle = this.currentPalette[2];
        this.ctx.fillRect(-this.size * 0.15, this.size * 0.35, this.size * 0.3, this.size * 0.1);
    }

    // Draw axe
    drawAxe() {
        // Head
        this.ctx.fillStyle = this.currentPalette[0];
        this.ctx.beginPath();
        this.ctx.moveTo(-this.size * 0.2, -this.size * 0.2);
        this.ctx.lineTo(this.size * 0.2, -this.size * 0.2);
        this.ctx.lineTo(this.size * 0.2, this.size * 0.2);
        this.ctx.lineTo(-this.size * 0.2, this.size * 0.2);
        this.ctx.closePath();
        this.ctx.fill();
        
        // Handle
        this.ctx.fillStyle = this.currentPalette[1];
        this.ctx.fillRect(-this.size * 0.05, this.size * 0.2, this.size * 0.1, this.size * 0.3);
    }

    // Draw mace
    drawMace() {
        // Head
        this.ctx.fillStyle = this.currentPalette[0];
        this.ctx.beginPath();
        this.ctx.arc(0, -this.size * 0.1, this.size * 0.2, 0, Math.PI * 2);
        this.ctx.fill();
        
        // Spikes
        this.ctx.fillStyle = this.currentPalette[1];
        for (let i = 0; i < 8; i++) {
            const angle = (Math.PI / 4) * i;
            const x = Math.cos(angle) * this.size * 0.25;
            const y = Math.sin(angle) * this.size * 0.25 - this.size * 0.1;
            this.ctx.beginPath();
            this.ctx.arc(x, y, this.size * 0.05, 0, Math.PI * 2);
            this.ctx.fill();
        }
        
        // Handle
        this.ctx.fillStyle = this.currentPalette[2];
        this.ctx.fillRect(-this.size * 0.05, this.size * 0.1, this.size * 0.1, this.size * 0.4);
    }

    // Draw spear
    drawSpear() {
        // Head
        this.ctx.fillStyle = this.currentPalette[0];
        this.ctx.beginPath();
        this.ctx.moveTo(0, -this.size * 0.4);
        this.ctx.lineTo(this.size * 0.1, -this.size * 0.2);
        this.ctx.lineTo(-this.size * 0.1, -this.size * 0.2);
        this.ctx.closePath();
        this.ctx.fill();
        
        // Shaft
        this.ctx.fillStyle = this.currentPalette[1];
        this.ctx.fillRect(-this.size * 0.05, -this.size * 0.2, this.size * 0.1, this.size * 0.6);
    }
}

export default WeaponGenerator; 