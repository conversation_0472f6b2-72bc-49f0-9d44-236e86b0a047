-- classes/gunslinger.lua
-- Gunslinger class definition for Lazarus Arc

local Gunslinger = {
    name = "Gunslinger",
    description = "A master of firearms, using speed, precision, and advanced gunplay to take down enemies before they can react.",
    icon = "gunslinger_icon.png",

    -- 🎭 **Playstyle & Trade-offs**
    pros = {
        "High precision and ranged burst damage",
        "Extremely mobile, able to reposition quickly",
        "Specializes in tactical combat, outmaneuvering enemies"
    },
    cons = {
        "Ammo-dependent—reloading takes time",
        "Less effective in close combat compared to melee classes",
        "Loses effectiveness if caught off-guard or out of position"
    },

    -- 🏋 **Base Stats & Growth**
    base_stats = { 
        health = 95, 
        maxHealth = 95,
        stamina = 70, 
        maxStamina = 70,
        mana = 40,  
        maxMana = 40,
        pattack = 14, 
        pdefense = 5, 
        mattack = 6, 
        mdefense = 5, 
        speed = 9, 
        luck = 7 
    },

    stat_growth = {
        health = 8,
        stamina = 6,
        mana = 3,
        pattack = 1.5,
        pdefense = 0.5,
        mattack = 0.3,
        mdefense = 0.4,
        speed = 0.9,
        luck = 0.7
    },

    -- ⚔️ **Weapon Proficiency**
    proficiencies = { 
        gun = 2.5, -- Core weapon choice
        pistol = 2.0, -- Fastest reload speed, good mobility
        rifle = 1.8, -- Stronger damage at long range
        shotgun = 1.5, -- Devastating in close quarters
        crossbow = 1.2,  
        dagger = 1.0,  
        sword = 0.8,  
        magic = 0.5  -- Minimal magical ability
    },

    -- 🌿 **Elemental Affinities**
    element_affinities = {
        air = 10,   -- Enhanced reaction speed and movement
        fire = 7,   -- Firearms-based explosive potential
        earth = -5, -- Not focused on defense or tanking
    },

    -- 💥 **Overdrive Abilities**
    overdrive = { 
        "deadeye", -- Increases critical hit chance and damage for a short time
        "fan_the_hammer", -- Unloads an entire magazine in rapid succession
        "ricochet_hell", -- Bullets bounce between enemies for chain damage
    },

    -- 🎯 **Starting Skills**
    starting_skills = {
        "quick_draw",
        "double_tap",
        "trick_shot"
    },

    -- 📈 **Skill Progression**
    skill_tree = {
        tier1 = {
            "evasive_roll",
            "steady_aim",
            "ammo_scavenge"
        },
        tier2 = {
            "piercing_round",
            "flashbang", 
            "hot_lead",
            "gun_kata"
        },
        tier3 = {
            "bullet_storm",
            "phantom_reload",
            "tracer_rounds"
        }
    },

    -- 🎒 **Inventory & Carrying Capacity**
    inventory_limit = 100, -- Average weight capacity, but must carry ammo

    -- ✨ **Special Abilities (Passive Perks)**
    special_abilities = {
        "Gunslinger Reflexes", -- Faster reload and weapon swap speed
        "High Noon", -- Bonus accuracy and critical chance when dueling a single opponent
        "Cover Tactics", -- Increased evasion when taking cover
        "Trick Shots", -- Some shots can ricochet for extra damage
        "Ammo Mastery", -- Higher chance to recover used ammo
    },

    -- 🎭 **Prestige Paths**
    prestige_paths = {
        sharpshooter = {
            description = "A master of precision shooting, capable of landing deadly headshots from any range.",
            bonuses = {
                "Massively increased long-range damage",
                "Scoped weapons have zero recoil",
                "Headshots deal critical damage and ignore armor"
            },
            weaknesses = {
                "Slower reload times",
                "Struggles in close-range combat"
            }
        },

        outlaw = {
            description = "A rogue gunfighter who relies on fast movement, deception, and rapid-fire tactics.",
            bonuses = {
                "Higher evasion and movement speed",
                "Faster reload and weapon switching",
                "Can intimidate or disarm enemies"
            },
            weaknesses = {
                "Lower damage per shot compared to Sharpshooters",
                "Requires constant movement to avoid getting hit"
            }
        },

        demolitionist = {
            description = "A firearms expert who specializes in explosive rounds, high-impact shots, and AOE destruction.",
            bonuses = {
                "Can use grenade launchers and explosive rounds",
                "Splash damage affects multiple enemies",
                "Increased knockback from gunfire"
            },
            weaknesses = {
                "More reliant on expensive ammo",
                "Less effective in precision shooting"
            }
        }
    },

    -- ⚔️ **Hybrid & Multiclass Options**
    hybrid_options = {
        gun_mage = {
            description = "A Gunslinger who enhances bullets with elemental magic.",
            pros = {
                "Can infuse rounds with fire, ice, or lightning",
                "Enhanced bullets ignore resistances"
            },
            cons = {
                "Requires mana to sustain enhancements",
                "Lower clip size due to magical infusion"
            }
        },

        trickshot_rogue = {
            description = "A Gunslinger who incorporates acrobatics, stealth, and deception into gunplay.",
            pros = {
                "Can dual-wield pistols and melee weapons",
                "Higher critical hit chance from flanking"
            },
            cons = {
                "Weaker against armored opponents",
                "Needs careful positioning for effectiveness"
            }
        }
    }
}

return Gunslinger
