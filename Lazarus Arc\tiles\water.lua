-- tiles/water.lua
local WaterTile = {
    id = "water",
    name = "Water",
    passable = false, -- Cannot walk through water (without swimming ability)
    variants = 4, -- Different water appearances (clear, rippled, etc.)
    
    -- Entities that can spawn in water
    spawns = {"fish", "lily_pad", "frog", "water_snake", "duck"},
    
    -- Water-specific properties
    depth = 1.5, -- How deep in tiles
    flowDirection = nil, -- For rivers, the direction water flows
    flowStrength = 0, -- For rivers, how strong the current is
    
    -- Resource type for AI entities to find
    resourceType = "water",
    
    -- Interaction function when player activates this tile
    interact = function(tile, entity)
        if entity.type == "player" then
            -- If player has fishing ability/equipment
            if entity.hasItem and entity.hasItem("fishing_rod") then
                -- Fishing mechanic
                local fishingSkill = (entity.skills and entity.skills.fishing) or 0
                local catchChance = 0.2 + (fishingSkill * 0.05)
                
                if math.random() < catchChance then
                    -- Determine fish type based on various factors
                    local fish = WaterTile.determineFishCatch(tile)
                    
                    return {
                        success = true,
                        message = "You caught a " .. fish.name .. "!",
                        effects = {
                            {type = "add_item", item = fish.id, quantity = 1}
                        }
                    }
                else
                    return {
                        success = false,
                        message = "The fish got away."
                    }
                end
            elseif entity.hasItem and entity.hasItem("water_container") then
                -- Fill water container
                local container = "water_container" -- Generic container
                
                -- Check for specific container types
                if entity.inventory.hasItem("empty_bottle") then
                    container = "empty_bottle"
                elseif entity.inventory.hasItem("empty_waterskin") then
                    container = "empty_waterskin"
                elseif entity.inventory.hasItem("bucket") then
                    container = "bucket"
                end
                
                -- Convert to filled container
                local filledContainer
                if container == "empty_bottle" then
                    filledContainer = "water_bottle"
                elseif container == "empty_waterskin" then
                    filledContainer = "filled_waterskin"
                elseif container == "bucket" then
                    filledContainer = "water_bucket"
                else
                    filledContainer = "filled_water_container"
                end
                
                entity.inventory.removeItem(container, 1)
                
                return {
                    success = true,
                    message = "You fill your " .. container .. " with water.",
                    effects = {
                        {type = "add_item", item = filledContainer, quantity = 1}
                    }
                }
            elseif entity.canSwim then
                return {
                    success = true,
                    message = "You could swim across this water.",
                    effects = {
                        {type = "suggest_action", action = "swim"}
                    }
                }
            else
                return {
                    success = false,
                    message = "The water is too deep to cross. You need a boat or swimming ability."
                }
            end
        end
        return nil
    end,
    
    -- Specialized function to determine fish type caught
    determineFishCatch = function(tile)
        local fishTypes = {
            {id = "common_fish", name = "Common Fish", chance = 0.6},
            {id = "rare_fish", name = "Rare Fish", chance = 0.3},
            {id = "exotic_fish", name = "Exotic Fish", chance = 0.1}
        }
        
        -- Add biome-specific fish if defined
        if tile.biomeFish then
            for _, fishType in ipairs(tile.biomeFish) do
                table.insert(fishTypes, fishType)
            end
        end
        
        -- Roll for fish type
        local roll = math.random()
        local cumulativeChance = 0
        
        for _, fishType in ipairs(fishTypes) do
            cumulativeChance = cumulativeChance + fishType.chance
            if roll <= cumulativeChance then
                return fishType
            end
        end
        
        -- Default if somehow no fish is selected
        return fishTypes[1]
    end,
    
    -- Weather effects
    weatherEffects = {
        freeze = function(tile)
            -- Water freezes in cold weather
            if tile.depth < 2 then -- Shallow water freezes more easily
                return "transform", "ice"
            end
            return nil
        end,
        
        heat = function(tile)
            -- Extreme heat can cause evaporation
            if tile.depth < 1 and math.random() < 0.1 then
                return "transform", "mud"
            end
            
            -- Water quality decreases in heat
            tile.waterQuality = (tile.waterQuality or 1.0) - 0.1
            if tile.waterQuality < 0 then
                tile.waterQuality = 0
            end
            
            return nil
        end,
        
        rain = function(tile)
            -- Rain replenishes water
            tile.waterQuality = 1.0 -- Fresh water
            
            -- May increase depth slightly
            tile.depth = tile.depth + 0.1
            if tile.depth > 3 then
                tile.depth = 3
            end
            
            return nil
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {0.1, 0.4, 0.8, 0.9}, -- Blue with transparency
        variants = {
            {name = "calm", animation_speed = 0.5},
            {name = "rippled", animation_speed = 1.0},
            {name = "flowing", animation_speed = 1.5},
            {name = "deep", color_shift = {-0.1, -0.1, 0.1}}
        },
        animation = {
            type = "wave",
            frames = 8,
            speed = 1.0
        },
        reflection = true,
        refraction = 0.3,
        transparency = 0.7,
        depthEffect = true, -- Darker in deeper areas
        surfaceHighlight = true -- Sun glint on surface
    },
    
    -- Audio properties
    ambientSound = "water_lapping",
    specialSounds = {
        {name = "splash", trigger = "nearby_movement", chance = 0.2},
        {name = "fish_jump", trigger = "random", chance = 0.05, minInterval = 30}
    },
    
    -- Swimming properties
    swimming = {
        speedMultiplier = 0.5, -- Swimming is slower than walking
        staminaDrain = 1.0, -- Swimming drains stamina
        itemRestriction = true, -- Can't use certain items while swimming
        drowningRisk = true -- Can drown if stamina depleted
    },
    
    -- Fishing properties
    fishing = {
        baseSuccessChance = 0.2,
        skillBonus = 0.05, -- Per level of fishing skill
        timeRequired = {5, 15}, -- Random time in seconds to catch
        rareSpawnChance = 0.05, -- Chance for rare fish to appear
        bestTimeOfDay = "dawn" -- When fish are most active
    },
    
    -- Connections to other tiles
    connects_to = {
        "shallow_water",
        "deep_water",
        "ice",
        "sand",
        "mud",
        "grass"
    },
    
    -- Boat navigation
    boatNavigable = true,
    
    -- Harmful to certain entity types
    harmfulTo = {
        "fire_elemental",
        "desert_creature"
    },
    
    -- Effects on entities that try to move into this tile
    onMoveAttempt = function(tile, entity)
        -- Check if entity can enter water
        if entity.canSwim or entity.type == "fish" or entity.type == "water_creature" then
            return true -- Can enter water
        end
        
        -- Check if entity has a boat
        if entity.hasItem and entity.hasItem("boat") then
            return true -- Can enter with boat
        end
        
        -- Cannot enter
        return false, "You cannot enter deep water without swimming ability or a boat."
    end
}

function WaterTile.init(world)
    print("Water tile module initialized")
    
    -- Register water with the ecosystem
    if world.systems and world.systems.ecosystem then
        world.systems.ecosystem:registerWaterTerrain("water")
    end
    
    -- Register with navigation system for water routes
    if world.systems and world.systems.navigation then
        world.systems.navigation:registerWaterway("water")
    end
    
    -- Register with weather system
    if world.systems and world.systems.weather then
        world.systems.weather:registerWaterBody("water")
    end
end

return WaterTile