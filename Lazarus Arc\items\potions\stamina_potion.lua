-- items/potions/stamina_potion.lua

local Potion = {
    id = "stamina_potion",
    name = "Stamina Potion",
    description = "A green potion that restores stamina when consumed.",
    type = "potion",
    category = "consumable",
    stackable = true,
    maxStack = 10,

    stats = {
        stamina_restore = 25.0,
        weight = 0.5,
    },

    sprite = "res://Sprite/items/potions/stamina_potion.png",
    size = { width = 32, height = 32 },

    effects = {
        instant_restore = true,
        duration = 0
    },

    meta = {
        rarity = "common"
    },

    onUse = function(self, user)
        if user and user.stats and user.stats.stamina then
            local maxStamina = user.stats.maxStamina or 100
            local currentStamina = user.stats.stamina
            local restoreAmount = self.stats.stamina_restore
            
            -- Apply stamina restoration
            user.stats.stamina = math.min(maxStamina, currentStamina + restoreAmount)
            
            -- Notify the user
            print(user.name .. " used a Stamina Potion and recovered " .. restoreAmount .. " stamina!")
            return true
        end
        return false
    end
}

return Potion
