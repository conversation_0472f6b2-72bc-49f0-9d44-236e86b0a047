local Trigger = {}

function Trigger.init(entity, config)
    entity.triggerConfig = config
    entity.cooldownTimer = 0
end

function Trigger.update(entity, world, dt)
    entity.cooldownTimer = math.max(0, entity.cooldownTimer - dt)
    if entity.cooldownTimer > 0 then return end

    local nearby = world.entitySystem:getEntitiesInRadius(entity.position, entity.triggerConfig.radius)
    for _, other in ipairs(nearby) do
        if other ~= entity and table.contains(entity.triggerConfig.triggersOn, other.type) then
            -- Deal damage
            if other.health then
                other.health = other.health - entity.triggerConfig.damage
                print(entity.name .. " triggered on " .. other.name .. " for " .. entity.triggerConfig.damage .. " damage.")
            end

            -- Play sound/animation
            if entity.sounds and entity.sounds.trigger then
                world.soundSystem:play(entity.sounds.trigger, entity.position)
            end
            if entity.animations then
                entity.currentAnimation = "triggered"
            end

            -- Set cooldown or destroy
            if entity.triggerConfig.destroyOnActivate then
                entity.toBeRemoved = true
            else
                entity.cooldownTimer = entity.triggerConfig.cooldown
            end

            break
        end
    end
end

return Trigger
