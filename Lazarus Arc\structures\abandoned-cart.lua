-- structures/abandoned-cart.lua
-- Declare AbandonedCart at the top level so it can be referenced from within functions
local AbandonedCart = {}

-- Define the structure properties
AbandonedCart = {
    id = "abandoned-cart",
    name = "Abandoned Cart",
    category = "debris",
    
    -- Physical properties
    width = 3,  -- Tiles wide
    height = 2, -- Tiles tall
    solid = true,
    providesShelter = false,
    
    -- Structure properties
    hasInterior = false,
    condition = 0.5, -- 0.0 = completely broken, 1.0 = just abandoned
    hasLoot = true,
    
    -- Spawn properties
    spawnChance = {
        road = 0.2,
        plains = 0.1,
        forest = 0.08,
        hills = 0.05
    },
    
    -- Spawn requirements
    requirements = {
        minDistanceFromOtherStructures = 10,
        validTiles = {"road", "dirt_path", "grass", "forest_floor"},
        invalidTiles = {"water", "lava", "void", "deep_forest"},
        needsFlatGround = true
    },
    
    -- Items that may be found in the cart
    loot = {
        common = {
            {id = "wood_plank", chance = 0.8, quantity = {2, 5}},
            {id = "rope", chance = 0.6, quantity = {1, 2}},
            {id = "cloth", chance = 0.5, quantity = {1, 3}}
        },
        uncommon = {
            {id = "tool", chance = 0.3, quantity = {1, 1}},
            {id = "preserved_food", chance = 0.4, quantity = {1, 2}},
            {id = "leather", chance = 0.35, quantity = {1, 2}}
        },
        rare = {
            {id = "map_fragment", chance = 0.15, quantity = {1, 1}},
            {id = "small_lockbox", chance = 0.1, quantity = {1, 1}},
            {id = "trinket", chance = 0.2, quantity = {1, 1}}
        }
    },
    
    -- Variant types for this structure
    variants = {
        "merchant", -- Abandoned merchant cart
        "settler", -- Settler/pioneer wagon
        "military", -- Military supply cart
        "farm" -- Farm wagon with agricultural supplies
    },
    
    -- Story behind the abandonment
    abandonmentReasons = {
        "attack", -- Attacked by bandits/monsters
        "breakdown", -- Mechanical failure
        "weather", -- Bad weather forced abandonment
        "illness", -- Occupants fell ill
        "unknown" -- No clear reason
    },
    
    -- Tile layout (top-down view, using characters to represent tiles)
    tileLayout = {
        "ccc", -- c = cart tiles
        "www"  -- w = wheel tiles
    },
    
    -- Tile legend (characters to tile types)
    tileLegend = {
        c = "cart_body",
        w = "cart_wheel"
    },
    
    -- Tags for search/filtering
    tags = {"abandoned", "loot", "road", "debris"},
    
    -- Interactions available with this structure
    interactions = {
        {
            name = "search_cart",
            prompt = "Search Cart",
            condition = function(structure, entity, world)
                return not structure.alreadySearched
            end,
            action = function(structure, entity, world)
                -- Mark as searched
                structure.alreadySearched = true
                
                -- Check if anything is left to find
                if not structure.hasLoot then
                    return {
                        success = false,
                        message = "You search the abandoned cart, but it has been thoroughly picked clean already."
                    }
                end
                
                -- Determine what might be found
                local perceptionCheck = (entity.attributes and entity.attributes.perception) or 10
                local luckCheck = (entity.attributes and entity.attributes.luck) or 10
                
                local findChance = 0.7 + (perceptionCheck / 50) + (luckCheck / 50)
                
                if math.random() < findChance then
                    -- Roll for loot quality
                    local lootRoll = math.random()
                    local lootPool
                    
                    if lootRoll < 0.6 then
                        lootPool = structure.loot.common
                    elseif lootRoll < 0.9 then
                        lootPool = structure.loot.uncommon
                    else
                        lootPool = structure.loot.rare
                    end
                    
                    -- Select random item from pool
                    local selectedLoot = lootPool[math.random(#lootPool)]
                    
                    -- Check if it spawns based on chance
                    if math.random() <= selectedLoot.chance then
                        local quantity = math.random(selectedLoot.quantity[1], selectedLoot.quantity[2])
                        
                        -- Add contextual description based on variant
                        local description = "After searching the abandoned "
                        
                        if structure.variant == "merchant" then
                            description = description .. "merchant cart, you find " .. 
                                         (quantity > 1 and quantity .. " " or "a ") .. selectedLoot.id .. 
                                         " among the remaining trade goods."
                        elseif structure.variant == "settler" then
                            description = description .. "settler wagon, you find " .. 
                                         (quantity > 1 and quantity .. " " or "a ") .. selectedLoot.id .. 
                                         " tucked away in one of the storage compartments."
                        elseif structure.variant == "military" then
                            description = description .. "military supply cart, you find " .. 
                                         (quantity > 1 and quantity .. " " or "a ") .. selectedLoot.id .. 
                                         " among the abandoned supplies."
                        else -- farm
                            description = description .. "farm wagon, you find " .. 
                                         (quantity > 1 and quantity .. " " or "a ") .. selectedLoot.id .. 
                                         " mixed in with agricultural supplies."
                        end
                        
                        -- Handle special case for lockbox
                        if selectedLoot.id == "small_lockbox" then
                            description = description .. " It appears to be locked."
                        end
                        
                        return {
                            success = true,
                            message = description,
                            effects = {
                                {type = "add_item", item = selectedLoot.id, quantity = quantity}
                            }
                        }
                    end
                end
                
                -- Failed to find anything valuable
                return {
                    success = false,
                    message = "You thoroughly search the abandoned cart but find nothing of value. It seems others have picked it clean already."
                }
            end
        },
        {
            name = "examine",
            prompt = "Examine Cart",
            action = function(structure, entity, world)
                local description = "An abandoned "
                
                -- Description based on variant
                if structure.variant == "merchant" then
                    description = description .. "merchant cart with faded paint and empty display shelves."
                elseif structure.variant == "settler" then
                    description = description .. "covered wagon once used by settlers traveling to new lands."
                elseif structure.variant == "military" then
                    description = description .. "military supply cart with official markings on its side."
                else -- farm
                    description = description .. "farm wagon with compartments for carrying crops and supplies."
                end
                
                -- Add details about condition
                if structure.condition < 0.3 then
                    description = description .. " It's heavily damaged and falling apart."
                elseif structure.condition < 0.7 then
                    description = description .. " It shows significant wear and damage."
                else
                    description = description .. " It appears to be in relatively good condition despite being abandoned."
                end
                
                -- Add clues about why it was abandoned
                if structure.abandonmentReason == "attack" then
                    description = description .. " There are signs of violence - splintered wood and what might be dried blood."
                elseif structure.abandonmentReason == "breakdown" then
                    description = description .. " One of the wheels is broken, which likely forced its abandonment."
                elseif structure.abandonmentReason == "weather" then
                    description = description .. " Weather damage suggests it was abandoned during a storm."
                elseif structure.abandonmentReason == "illness" then
                    description = description .. " There are faded quarantine markings barely visible on its side."
                end
                
                -- Add comment about search state
                if structure.alreadySearched then
                    description = description .. " You've already searched it thoroughly."
                elseif not structure.hasLoot then
                    description = description .. " It looks like it's been thoroughly looted already."
                else
                    description = description .. " It might still contain some useful items."
                end
                
                return {
                    success = true,
                    message = description
                }
            end
        },
        {
            name = "salvage_parts",
            prompt = "Salvage Parts",
            condition = function(structure, entity, world)
                -- Need a tool to salvage effectively
                return entity.inventory and 
                      (entity.inventory.hasItem("axe") or 
                       entity.inventory.hasItem("hammer") or
                       entity.inventory.hasItem("saw"))
            end,
            action = function(structure, entity, world)
                -- Check if already salvaged
                if structure.alreadySalvaged then
                    return {
                        success = false,
                        message = "You've already salvaged all usable parts from this cart."
                    }
                end
                
                -- Mark as salvaged
                structure.alreadySalvaged = true
                
                -- Determine what tool is being used
                local toolUsed = ""
                if entity.inventory.hasItem("axe") then
                    toolUsed = "axe"
                elseif entity.inventory.hasItem("hammer") then
                    toolUsed = "hammer"
                else
                    toolUsed = "saw"
                end
                
                -- Calculate salvage yield based on condition and tool
                local baseWoodAmount = math.ceil(4 * structure.condition)
                local baseMaterialAmount = math.ceil(2 * structure.condition)
                
                -- Boost from appropriate tools
                if toolUsed == "axe" or toolUsed == "saw" then
                    baseWoodAmount = baseWoodAmount + 2
                end
                if toolUsed == "hammer" then
                    baseMaterialAmount = baseMaterialAmount + 1
                end
                
                -- Get salvage materials based on variant
                local mainMaterial = "wood_plank"
                local secondaryMaterial = "nail"
                
                if structure.variant == "merchant" then
                    secondaryMaterial = "metal_fitting"
                elseif structure.variant == "settler" then
                    secondaryMaterial = "canvas"
                elseif structure.variant == "military" then
                    secondaryMaterial = "iron_part"
                    baseMaterialAmount = baseMaterialAmount + 1 -- More metal in military carts
                end
                
                -- Create salvage results
                local results = {
                    {item = mainMaterial, quantity = baseWoodAmount},
                    {item = secondaryMaterial, quantity = baseMaterialAmount}
                }
                
                -- Add wheel-specific material
                table.insert(results, {item = "wheel_part", quantity = math.random(1, 2)})
                
                -- Create effect list
                local effects = {}
                for _, result in ipairs(results) do
                    table.insert(effects, {
                        type = "add_item", 
                        item = result.item, 
                        quantity = result.quantity
                    })
                end
                
                -- Add skill experience
                table.insert(effects, {
                    type = "gain_skill",
                    skill = "crafting",
                    amount = 0.2
                })
                
                -- Create message based on tool
                local message = "You use your " .. toolUsed .. " to salvage usable parts from the cart. "
                message = message .. "You recover " .. baseWoodAmount .. " " .. mainMaterial .. ", " 
                          .. baseMaterialAmount .. " " .. secondaryMaterial
                          .. ", and some wheel parts."
                
                -- Update the cart's appearance to reflect salvaging
                structure.tileLegend.c = "salvaged_cart_body"
                structure.tileLegend.w = "salvaged_cart_wheel"
                
                return {
                    success = true,
                    message = message,
                    effects = effects
                }
            end
        },
        {
            name = "check_tracks",
            prompt = "Examine Tracks",
            action = function(structure, entity, world)
                -- Check survivor tracks
                if structure.hasSurvivorTracks then
                    local trackAge = ""
                    if structure.trackAge < 1 then
                        trackAge = "very recent"
                    elseif structure.trackAge < 3 then
                        trackAge = "a few days old"
                    else
                        trackAge = "old and faded"
                    end
                    
                    local trackDirection = structure.trackDirection or "north"
                    
                    -- Survival skill check for better information
                    local survivalCheck = (entity.skills and entity.skills.survival) or 0
                    local perceptionCheck = (entity.attributes and entity.attributes.perception) or 10
                    
                    local trackDetail = ""
                    if survivalCheck > 2 or perceptionCheck > 12 then
                        trackDetail = " You can tell that " .. structure.trackCount .. " people left on foot."
                        
                        if structure.trackCount == 1 then
                            trackDetail = trackDetail .. " They appeared to be injured based on the irregular stride."
                        end
                    end
                    
                    return {
                        success = true,
                        message = "You find " .. trackAge .. " tracks leading away from the cart to the " 
                                 .. trackDirection .. "." .. trackDetail,
                        effects = {
                            {type = "discover_location_direction", direction = trackDirection}
                        }
                    }
                else
                    -- No tracks to find
                    local reason = ""
                    if structure.tracklessReason == "weather" then
                        reason = "Any tracks have been washed away by rain."
                    elseif structure.tracklessReason == "age" then
                        reason = "Too much time has passed to see any tracks."
                    else
                        reason = "The ground around the cart has been disturbed too much to find clear tracks."
                    end
                    
                    return {
                        success = false,
                        message = "You search the area around the cart for tracks but find nothing conclusive. " .. reason
                    }
                end
            end
        },
        {
            name = "hide_inside",
            prompt = "Hide in Cart",
            condition = function(structure, entity, world)
                return structure.condition > 0.3 and structure.variant ~= "farm"
            end,
            action = function(structure, entity, world)
                -- Stealth check
                local stealthBonus = 0
                if entity.skills and entity.skills.stealth then
                    stealthBonus = entity.skills.stealth * 0.1
                end
                
                -- Calculate hiding effectiveness
                local hideQuality = 0.4 + stealthBonus
                
                -- Settler wagons provide better hiding spots
                if structure.variant == "settler" then
                    hideQuality = hideQuality + 0.2
                end
                
                -- Apply stealth effect
                return {
                    success = true,
                    message = "You find a hiding spot inside the abandoned cart and conceal yourself.",
                    effects = {
                        {type = "apply_status", status = "hidden", effectiveness = hideQuality, duration = 600},
                        {type = "move_to", x = structure.x + 1, y = structure.y}
                    }
                }
            end
        }
    },
    
    -- Initialize the structure with specific properties
    init = function(structure, world, x, y)
        -- Set structure position
        structure.x = x
        structure.y = y
        
        -- Copy the tileLegend from AbandonedCart to the structure
        structure.tileLegend = {}
        for k, v in pairs(AbandonedCart.tileLegend) do
            structure.tileLegend[k] = v
        end
        
        -- Copy the loot table from AbandonedCart to the structure
        structure.loot = {
            common = {},
            uncommon = {},
            rare = {}
        }
        
        -- Deep copy of loot table
        for category, items in pairs(AbandonedCart.loot) do
            structure.loot[category] = {}
            for _, item in ipairs(items) do
                table.insert(structure.loot[category], {
                    id = item.id,
                    chance = item.chance,
                    quantity = {item.quantity[1], item.quantity[2]}
                })
            end
        end
        
        -- Choose random variant if none specified
        structure.variant = structure.variant or AbandonedCart.variants[math.random(#AbandonedCart.variants)]
        
        -- Choose random condition
        structure.condition = 0.2 + math.random() * 0.7
        
        -- Choose abandonment reason
        structure.abandonmentReason = AbandonedCart.abandonmentReasons[math.random(#AbandonedCart.abandonmentReasons)]
        
        -- Adjust condition based on abandonment reason
        if structure.abandonmentReason == "attack" then
            structure.condition = structure.condition * 0.8 -- More damage
        elseif structure.abandonmentReason == "breakdown" then
            structure.condition = structure.condition * 0.7 -- Most damage
        elseif structure.abandonmentReason == "weather" then
            structure.condition = structure.condition * 0.9 -- Some weather damage
        end
        
        -- Adjust appearance based on variant and condition
        if structure.variant == "merchant" then
            structure.tileLegend.c = "merchant_cart_body"
            structure.tileLegend.w = "cart_wheel"
            
            -- Add merchant-specific loot
            table.insert(structure.loot.uncommon, {id = "trade_goods", chance = 0.4, quantity = {1, 2}})
        elseif structure.variant == "settler" then
            structure.tileLegend.c = "settler_wagon_body"
            structure.tileLegend.w = "wagon_wheel"
            
            -- Add settler-specific loot
            table.insert(structure.loot.common, {id = "blanket", chance = 0.5, quantity = {1, 1}})
            table.insert(structure.loot.uncommon, {id = "family_keepsake", chance = 0.3, quantity = {1, 1}})
        elseif structure.variant == "military" then
            structure.tileLegend.c = "military_cart_body"
            structure.tileLegend.w = "reinforced_wheel"
            
            -- Add military-specific loot
            table.insert(structure.loot.uncommon, {id = "bandage", chance = 0.5, quantity = {1, 3}})
            table.insert(structure.loot.rare, {id = "weapon_part", chance = 0.25, quantity = {1, 1}})
        else -- farm
            structure.tileLegend.c = "farm_wagon_body"
            structure.tileLegend.w = "wagon_wheel"
            
            -- Add farm-specific loot
            table.insert(structure.loot.common, {id = "seed_pouch", chance = 0.6, quantity = {1, 1}})
            table.insert(structure.loot.common, {id = "farming_tool", chance = 0.4, quantity = {1, 1}})
        end
        
        -- Modify tiles if cart is in poor condition
        if structure.condition < 0.4 then
            structure.tileLegend.c = "damaged_" .. structure.tileLegend.c
            if math.random() < 0.5 then -- 50% chance of broken wheel
                structure.tileLegend.w = "broken_" .. structure.tileLegend.w
            end
        end
        
        -- Determine if cart has been looted already
        structure.hasLoot = math.random() < (0.3 + (structure.condition * 0.5))
        
        -- Determine if there are survivor tracks
        structure.hasSurvivorTracks = math.random() < 0.6 -- 60% chance
        if structure.hasSurvivorTracks then
            -- Direction tracks lead
            local directions = {"north", "northeast", "east", "southeast", "south", "southwest", "west", "northwest"}
            structure.trackDirection = directions[math.random(#directions)]
            
            -- Age of tracks in days
            structure.trackAge = math.random(0, 7)
            
            -- Number of people
            structure.trackCount = math.random(1, 4)
        else
            local tracklessReasons = {"weather", "age", "traffic"}
            structure.tracklessReason = tracklessReasons[math.random(#tracklessReasons)]
        end
        
        -- Initialize search state
        structure.alreadySearched = false
        structure.alreadySalvaged = false
        
        return structure
    end,
    
    -- Draw the structure (for debugging/visualization)
    draw = function(structure, renderer)
        -- This would be implemented in the game's rendering system
        print("Drawing " .. structure.name .. " at " .. structure.x .. "," .. structure.y)
    end
}

return AbandonedCart