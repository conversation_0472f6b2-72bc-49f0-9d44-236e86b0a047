-- items/tools/compass.lua

local Tool = {
    id = "compass",
    name = "Compass",
    description = "A navigation tool that helps you find your way.",
    type = "tool",
    category = "tool",
    stackable = false,

    stats = {
        accuracy = 0.95,
        weight = 0.5,
    },

    sprite = "res://Sprite/items/tools/compass.png",
    size = { width = 32, height = 32 },

    effects = {
        navigation_bonus = true
    },

    meta = {
        rarity = "uncommon"
    },

    onUse = function(self, user)
        if user then
            -- Show direction to nearest point of interest or home
            print(user.name .. " used a compass to get their bearings.")
            
            -- In a real implementation, this would show the direction to a point of interest
            local directions = {"North", "Northeast", "East", "Southeast", "South", "Southwest", "West", "Northwest"}
            local randomDirection = directions[math.random(1, #directions)]
            
            print("The nearest point of interest is to the " .. randomDirection .. ".")
            return true
        end
        return false
    end
}

return Tool
