-- biomes/robot_metropolis.lua
-- Declare RobotMetropolisBiome at the top level so it can be referenced from within functions
local RobotMetropolisBiome = {}

-- Define the biome properties
RobotMetropolisBiome = {
    id = "robot_metropolis",
    name = "Robot Metropolis",
    description = "A meticulously planned and efficiently run urban environment governed and primarily inhabited by robots. Order, logic, and adherence to protocols are paramount.",

    -- Environmental factors (Controlled, clean, structured)
    environment = {
        humidity = 0.5,    -- Controlled atmosphere
        temperature = 0.6, -- Regulated temperature
        sunlight = 0.5,    -- Mix of artificial and potentially filtered natural light
        windStrength = 0.1, -- Minimal internal drafts, controlled airflow
        airQuality = 0.95, -- Filtered, clean air
        noiseLevel = 0.3,  -- Efficient machinery, humming, less chaotic noise
        networkStatus = 1.0 -- Stability of the city's control network (0-1)
    },

    -- Core tile types used in this biome
    -- NOTE: Needs 'metal_street', 'pavement_clean', 'building_wall_metal', 'charging_station', 'data_conduit', 'park_synthetic', 'automated_transport_line', 'logic_core_tile'. Uses 'ruins'(maybe old organic sector).
    primaryTiles = {"metal_street", "pavement_clean", "building_wall_metal"},
    secondaryTiles = {"charging_station", "data_conduit", "park_synthetic", "automated_transport_line"},
    rareTiles = {"logic_core_tile", "power_hub", "security_checkpoint_tile", "ruins"},

    -- Default proportions (Orderly layout)
    tileDistribution = {
        pavement_clean = 0.4,
        metal_street = 0.2,
        building_wall_metal = 0.25, -- Structured buildings
        automated_transport_line = 0.05,
        park_synthetic = 0.03,
        charging_station = 0.02,
        data_conduit = 0.02,
        control_panel = 0.01, -- Reuse from factory?
        ruins = 0.01, -- Small derelict/organic areas
        logic_core_tile = 0.005,
        power_hub = 0.005
    },

    -- Entities common to this biome
    commonEntities = {
        "citizen_robot", "maintenance_bot", "transport_drone", "information_kiosk_robot"
    },
    uncommonEntities = {
        "police_robot", "specialized_worker_robot", "organic_visitor", "data_courier_bot" -- specialized_worker_robot includes medical and construction types
    },
    rareEntities = {
        "governor_ai_terminal", "heavy_police_robot", "ambassador_robot", "rogue_robot" -- rogue_robot is malfunctioning/hiding
    },

    -- Specific biome properties
    isUrban = true,
    isAutomated = true,
    governance = "Central AI / Logic Council",
    ruleSet = { -- Examples of rules player interaction triggers checks against
        theft = { consequence = "confiscation_and_fine", escalation = "detainment" },
        violence = { consequence = "immediate_nonlethal_subdual", escalation = "detainment_level_2" },
        unauthorized_access = { consequence = "access_lockout", escalation = "security_alert" },
        property_damage = { consequence = "repair_fine", escalation = "detainment" },
        inefficiency = { consequence = "minor_fine_or_warning" } -- Obstructing pathways etc.
    },
    factionID = "automaton_collective", -- Link to faction system

    -- Biome variants for diverse generation (Districts)
    variants = {
        logic_core = {
            name = "Logic Core District",
            environment = { noiseLevel=0.1, networkStatus=1.0 },
            tileAdjustments = { logic_core_tile=0.1, data_conduit=0.1, pavement_clean=0.5 },
            entityAdjustments = { governor_ai_terminal=1.0, data_courier_bot=1.5, police_robot=1.2 },
            specialEffects = {"data_stream_visuals", "high_security_zones"}
        },
        energy_grid = {
            name = "Energy Grid District",
            environment = { temperature=0.7 },
            tileAdjustments = { power_hub=0.1, data_conduit=0.05, metal_floor=0.5, steam_pipe=0.05 }, -- Assuming mixed power? Or specific generator type.
            entityAdjustments = { maintenance_bot=1.8, energy_elemental_bound=1.0 }, -- Elementals bound for power?
            specialEffects = {"power_hum_sound", "energy_field_visible"}
        },
        habitation_blocks = {
            name = "Habitation Blocks", -- Where do robots "live"? Charging/maintenance/storage?
            tileAdjustments = { building_wall_metal=0.4, robot_domicile_tile=0.3, charging_station=0.1 }, -- Need domicile tile
            entityAdjustments = { citizen_robot=2.0, maintenance_bot=1.0 },
        },
        automated_production = {
            name = "Automated Production", -- Cleaner than the raw factory
            inherits = "robot_factory", -- Inherit some tiles/entities?
            environment = { noiseLevel=0.6 },
            tileAdjustments = { factory_floor=0.4, conveyor_belt=0.2, assembly_machine=0.15 },
            entityAdjustments = { worker_robot_basic=2.0, factory_foreman_robot=1.1 },
            specialEffects = {"precision_assembly_sound", "item_production_output"}
        },
        reintegration_zone = { -- Area for dealing with organics or waste?
            name = "Reintegration Zone",
            environment = { airQuality=0.5 },
            tileAdjustments = { scrap_pile=0.1, recycling_unit_tile=0.1, ruins=0.2, dirt=0.2 }, -- Need recycling tile
            entityAdjustments = { maintenance_bot=1.3, organic_visitor=1.0, rogue_robot=1.1, scavenger_humanoid=0.5 },
        }
    },

    -- Structures (Key buildings/facilities)
    structures = {
        { name = "Central Processing Core", chance = 0.05, unique=true, requiresVariant="logic_core" },
        { name = "Main Power Station", chance = 0.1, requiresVariant="energy_grid" },
        { name = "Transit System Hub", chance = 0.2 },
        { name = "Police/Enforcement HQ", chance = 0.15 },
        { name = "Resource Input/Output Port", chance = 0.1 },
        { name = "Data Archive", chance = 0.08, requiresVariant="logic_core" }
    },

    -- No natural weather, environmental events
    environmentalEffects = {
        network_surge = 0.05, -- Temporary boost or disruption?
        power_fluctuation = 0.08,
        security_sweep = 0.1, -- Increased police patrols/scans
        maintenance_cycle = 0.2 -- Areas might temporarily close for maintenance
    },

    -- Unique features generation
    features = {
        { name = "Automated Transit Line", chance = 0.4 }, -- Places transport line tiles
        { name = "Public Data Terminal", chance = 0.3 }, -- Entity providing info/lore/rules
        { name = "Energy Field Gate", chance = 0.15 }, -- Controlled access barrier
        { name = "Designated Charging Zone", chance = 0.25 }, -- Area with charging station tiles
        { name = "Logic Puzzle Lock", chance = 0.1 }, -- Environmental puzzle to gain access
        { name = "Recycling Processor", chance = 0.12, requiresVariant="reintegration_zone" } -- Large machine feature
    },

    -- Generation algorithm (Needs complex city planning logic - Placeholder)
    generate = function(chunk, world, variant)
        print("Generating " .. (variant and RobotMetropolisBiome.variants[variant].name or "Robot Metropolis") .. " biome")
        local variantData = variant and RobotMetropolisBiome.variants[variant] or nil

        -- Adjust tile distribution based on variant/district logic
        -- ... (similar distribution adjustment as other biomes) ...

        -- Noise/Grid based generation for structured layout
        local seed = world.seed + (chunk.x * 317 + chunk.y * 73)
        math.randomseed(seed)
        local function pnoise2D(x, y, frequency, seed_offset) return love.math.noise(x * frequency, y * frequency, (world.seed + (seed_offset or 0)) * 0.01) end

        -- *** City Grid / District Generation Placeholder ***
        -- Needs logic for placing streets in a grid, defining building blocks, placing district-specific tiles.
        -- Using simple noise for now.

        -- Initialize 2D tiles array if not exists
        chunk.tiles = chunk.tiles or {}
        for x = 0, world.CHUNK_SIZE - 1 do
            chunk.tiles[x] = chunk.tiles[x] or {}
        end

        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                local worldX = chunk.x * world.CHUNK_SIZE + x
                local worldY = chunk.y * world.CHUNK_SIZE + y

                local gridNoiseX = math.fmod(worldX, 15) -- Crude grid/block pattern
                local gridNoiseY = math.fmod(worldY, 15)
                local districtNoise = pnoise2D(worldX, worldY, 0.01, 1) -- Large scale district variation

                local tileType = "pavement_clean" -- Default

                -- Crude street grid
                if gridNoiseX < 2 or gridNoiseX > 13 or gridNoiseY < 2 or gridNoiseY > 13 then
                     tileType = "metal_street"
                -- Crude building block walls
                elseif gridNoiseX == 2 or gridNoiseX == 13 or gridNoiseY == 2 or gridNoiseY == 13 then
                     tileType = "building_wall_metal"
                -- Inside blocks - based on district/variant
                else
                    if variant == "habitation_blocks" then
                         tileType = math.random() < 0.6 and "robot_domicile_tile" or "pavement_clean"
                    elseif variant == "energy_grid" then
                         tileType = math.random() < 0.3 and "power_hub" or "metal_floor"
                    -- Apply other variant/distribution logic here...
                    end
                end

                -- Create the tile
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = math.random(1, 2),
                    isUrban = true,
                    isAutomated = true,
                    passable = (tileType ~= "building_wall_metal"),
                    isUnderground = false
                }

                chunk.tiles[x][y] = tile
            end
        end

        -- Call helper functions
        RobotMetropolisBiome.generateFeatures(chunk, world, variant)
        RobotMetropolisBiome.populateEntities(chunk, world, variant)
        RobotMetropolisBiome.generateStructures(chunk, world, variant)
        RobotMetropolisBiome.postProcess(chunk, world, variant)
    end,

    -- Helper function implementations (Placeholders)
    generateFeatures = function(chunk, world, variant)
        print("Generating Robot Metropolis features...")
        -- Place transit lines, terminals, energy barriers, charging zones, puzzles, etc.
        -- Requires logic to follow paths or place within specific zones.
    end,

    populateEntities = function(chunk, world, variant)
        print("Populating Robot Metropolis entities...")
        -- Spawn robots based on location type (street, building, charging bay) and district. Apply hostility rules.
        local variantData = variant and RobotMetropolisBiome.variants[variant] or nil
        local hostility = RobotMetropolisBiome.robotHostility -- Use biome base hostility

        local function spawnEntity(entityType, count)
             local multiplier = 1.0
             if variantData and variantData.entityAdjustments and variantData.entityAdjustments[entityType] then multiplier = variantData.entityAdjustments[entityType] end
             local adjustedCount = math.floor(count * multiplier + 0.5)
             for i = 1, adjustedCount do
                 local attempts = 0; local placed = false
                 while attempts < 5 and not placed do
                      local x = math.random(0, world.CHUNK_SIZE - 1)
                      local y = math.random(0, world.CHUNK_SIZE - 1)
                      local tile = chunk.tiles[x][y]
                      if tile and tile.passable then
                           local props = { hostility = hostility, currentTask = "patrol" } -- Default props
                           world.entitySystem:addEntity(entityType, chunk.x * world.CHUNK_SIZE + x, chunk.y * world.CHUNK_SIZE + y, props)
                           placed = true
                      end
                      attempts = attempts + 1
                 end
             end
         end
        -- Spawn based on lists (Density depends on district)
        for _, et in ipairs(RobotMetropolisBiome.commonEntities) do spawnEntity(et, math.random(5,12)) end
        for _, et in ipairs(RobotMetropolisBiome.uncommonEntities) do spawnEntity(et, math.random(2,6)) end
        for _, et in ipairs(RobotMetropolisBiome.rareEntities) do if math.random() < 0.05 then spawnEntity(et, 1) end end
    end,

    generateStructures = function(chunk, world, variant)
        print("Generating Robot Metropolis structures (key facilities)...")
        -- Place markers or simple representations for major facilities like Core, Power Plant, HQ.
         for _, structure in ipairs(RobotMetropolisBiome.structures) do
             if math.random() < structure.chance then
                  if not structure.requiresVariant or structure.requiresVariant == variant then
                       local attempts = 0; local placed = false; local sx, sy
                       while attempts < 10 and not placed do
                           sx = math.random(3, world.CHUNK_SIZE - 4)
                           sy = math.random(3, world.CHUNK_SIZE - 4)
                           local tile = chunk.tiles[sx][sy]
                           -- Place on designated floor types, check space
                           if tile and tile.passable and (tile.type=="pavement_clean" or tile.type=="metal_floor") then
                                -- Basic space check
                                local spaceOk = true -- Implement better check
                                if spaceOk then placed = true end
                           end
                           attempts = attempts + 1
                       end
                      if placed then
                           local worldX = chunk.x * world.CHUNK_SIZE + sx
                           local worldY = chunk.y * world.CHUNK_SIZE + sy
                           print("Marking structure area: "..structure.name.." at "..worldX..","..worldY)
                           world.entitySystem:addEntity("structure_poi", worldX, worldY, { structureType = structure.name, description="The "..structure.name.." is located here." })
                      end
                  end
             end
         end
    end,

    postProcess = function(chunk, world, variant)
        print("Post-processing Robot Metropolis chunk...")
        -- Apply network status effects, security levels, lighting effects.
        local variantData = variant and RobotMetropolisBiome.variants[variant] or nil
        chunk.environmentalEffects = chunk.environmentalEffects or {}
        table.insert(chunk.environmentalEffects, {type="network_status", level=RobotMetropolisBiome.environment.networkStatus})
        table.insert(chunk.environmentalEffects, {type="city_security_level", level=0.2}) -- Base security level
        if variantData and variantData.specialEffects then
             for _, effect in ipairs(variantData.specialEffects) do table.insert(chunk.environmentalEffects, effect) end
        end
    end,

    init = function(worldCore)
        print("Robot Metropolis biome module initialized")
        RobotMetropolisBiome.worldCore = worldCore
        print("Robot Metropolis biome registered successfully for later use")
    end,

    registerWithWorld = function(world)
        print("Registering Robot Metropolis biome generators with world")
        if not world or not world.chunkSystem then print("Warning: Cannot register Robot Metropolis biome - world or chunkSystem is nil") return false end
        world.chunkSystem:registerGenerator(RobotMetropolisBiome.id, RobotMetropolisBiome.generate)
        for variantId, variantData in pairs(RobotMetropolisBiome.variants) do
            local fullVariantId = RobotMetropolisBiome.id .. "_" .. variantId
            world.chunkSystem:registerGenerator(fullVariantId, function(chunk, worldInstance)
                RobotMetropolisBiome.generate(chunk, worldInstance, variantId)
            end)
        end
        return true
    end,
}

return RobotMetropolisBiome