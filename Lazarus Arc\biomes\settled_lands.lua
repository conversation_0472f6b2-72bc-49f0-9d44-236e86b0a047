-- biomes/settled_lands.lua
-- Declare SettledLandsBiome at the top level so it can be referenced from within functions
local SettledLandsBiome = {}

-- Define the biome properties
SettledLandsBiome = {
    id = "settled_lands",
    name = "Settled Lands",
    description = "Cultivated and inhabited lands featuring farms, villages, roads, and managed resources, typical of a kingdom's core territory.",

    -- Environmental factors (Temperate, cleared land)
    environment = {
        humidity = 0.6,
        temperature = 0.6, -- Moderate temperature
        sunlight = 0.9,    -- Mostly cleared land
        windStrength = 0.4, -- Broken by structures/woodlots
        airQuality = 0.8   -- Generally good
    },

    -- Core tile types used in this biome
    -- NOTE: Needs 'farmland_field', 'village_tiles', 'forest_woodlot'. Uses 'grass', 'dirt', 'road', 'stone_path', 'water', 'ruins'.
    primaryTiles = {"grass", "farmland_field", "dirt"}, -- Pastures, fields, bare earth
    secondaryTiles = {"road", "village_tiles", "forest_woodlot", "stone_path"},
    rareTiles = {"water", "ruins"}, -- Ponds/rivers, old ruins

    -- Default proportions (Adjustable through variants)
    tileDistribution = {
        grass = 0.35, -- Pastures
        farmland_field = 0.35, -- Cultivated land
        dirt = 0.1, -- Fallow fields, paths
        road = 0.05,
        village_tiles = 0.05, -- Clustered areas representing villages
        forest_woodlot = 0.05,
        stone_path = 0.02,
        water = 0.02,
        ruins = 0.01
    },

    -- Entities common to this biome
    commonEntities = {
        "villager", "farmer", "domestic_cow", "domestic_chicken", "common_bird", "field_mouse"
    },
    uncommonEntities = {
        "merchant", "village_guard", "domestic_horse", "stray_dog", "fox" -- Fox adapted to edges
    },
    rareEntities = {
        "traveling_noble", "local_mayor", "retired_adventurer", "bandit_scout" -- Potential threat
    },

    -- Biome variants for diverse generation
    variants = {
        farming_valley = {
            name = "Farming Valley",
            environment = { humidity=0.7, fertility=0.9 }, -- Assumes fertility is tracked somewhere
            tileAdjustments = { farmland_field=0.5, grass=0.2, water=0.05, village_tiles=0.05 },
            entityAdjustments = { farmer=1.5, domestic_cow=1.3 },
            specialEffects = {"fertile_soil", "irrigation_ditches_visual"}
        },
        rolling_pastures = {
            name = "Rolling Pastures",
            environment = { windStrength=0.5 },
            tileAdjustments = { grass=0.6, farmland_field=0.1, dirt=0.1, stone_wall_feature=0.05 }, -- Added stone wall feature
            entityAdjustments = { domestic_sheep=1.8, shepherd=1.2, wolf=0.5 }, -- Added sheep/shepherd
            specialEffects = {"gentle_hills_visual", "grazing_herds"}
        },
        riverlands_settlement = {
            name = "Riverlands Settlement",
            environment = { humidity=0.7 },
            tileAdjustments = { farmland_field=0.3, grass=0.2, water=0.15, riverbank=0.1, village_tiles=0.1 }, -- Added riverbank
            entityAdjustments = { fisherman=1.5, merchant=1.2 },
            specialEffects = {"river_access", "ferry_crossing_point"}
        },
        border_marches = {
            name = "Border Marches",
            environment = { temperature=0.5 },
            tileAdjustments = { grass=0.4, dirt=0.2, forest_woodlot=0.1, road=0.1, watchtower_structure=0.05 }, -- Added watchtower
            entityAdjustments = { village_guard=1.5, patrol=1.2, bandit_scout=1.0 },
            specialEffects = {"fortified_points", "increased_patrols"}
        }
    },

    -- Structures (Common features of settled lands)
    structures = {
        { name = "village_center", chance = 0.1, size={min=5,max=10}}, -- Places cluster of village tiles/buildings
        { name = "farmstead", chance = 0.2, size={min=4,max=8} }, -- Places farmhouse, barn, field tiles
        { name = "watchtower", chance = 0.08, requiresVariant="border_marches" },
        { name = "roadside_inn", chance = 0.06, requiresNearby="road" },
        { name = "water_mill", chance = 0.05, requiresNearby="water" }, -- Assumes river/stream
        { name = "bridge", chance = 0.1, requiresNearby="water" },
        { name = "local_shrine", chance = 0.07 }
    },

    -- Weather patterns (Standard temperate mix)
    weather = {
        transitions = {
             clear = { clear = 60, cloudy = 30, rain_light = 10 },
             cloudy = { clear = 40, cloudy = 40, rain_light = 20 },
             rain_light = { cloudy = 60, rain_light = 30, clear = 10, rain_heavy = 5 },
             rain_heavy = { rain_light = 70, rain_heavy = 30 }
        },
        default = "clear"
    },

    -- Unique features generation
    features = {
        { name = "managed_woodlot", chance = 0.15, size={min=8, max=15} }, -- Places forest_woodlot tiles or tree entities
        { name = "orchard", chance = 0.1, size={min=5, max=10} }, -- Places fruit tree entities
        { name = "field_boundary", chance = 0.3 }, -- Places hedge/fence entities or modifies tile edges
        { name = "crossroads", chance = 0.1, requiresNearby="road" }, -- Intersection point feature
        { name = "pond", chance = 0.08, size={min=4, max=8} } -- Places water/shallow_water tiles
    },

    -- Generation algorithm (Simplified - needs logic for roads, fields, villages)
    generate = function(chunk, world, variant)
        print("Generating " .. (variant and SettledLandsBiome.variants[variant].name or "Settled Lands") .. " biome")
        local variantData = variant and SettledLandsBiome.variants[variant] or nil

        -- Adjust tile distribution
        local tileDistribution = {}
        for tileType, chance in pairs(SettledLandsBiome.tileDistribution) do
            tileDistribution[tileType] = chance
            if variantData and variantData.tileAdjustments and variantData.tileAdjustments[tileType] then
                tileDistribution[tileType] = variantData.tileAdjustments[tileType]
            end
        end

        -- Noise functions
        local seed = world.seed + (chunk.x * 577 + chunk.y * 373)
        math.randomseed(seed)
        local function pnoise2D(x, y, frequency, seed_offset) return love.math.noise(x * frequency, y * frequency, (world.seed + (seed_offset or 0)) * 0.01) end

        -- Initialize 2D tiles array if not exists
        chunk.tiles = chunk.tiles or {}
        for x = 0, world.CHUNK_SIZE - 1 do
            chunk.tiles[x] = chunk.tiles[x] or {}
        end

        -- Generate tiles (Very basic placeholder needs road/field/village logic)
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                local worldX = chunk.x * world.CHUNK_SIZE + x
                local worldY = chunk.y * world.CHUNK_SIZE + y

                local baseNoise = pnoise2D(worldX, worldY, 0.05, 1) -- General terrain layout
                local fieldNoise = pnoise2D(worldX, worldY, 0.1, 2) -- Field vs pasture areas

                -- Determine tile type
                local tileType = "grass" -- Default pasture
                if fieldNoise > 0.2 then
                    tileType = "farmland_field"
                elseif fieldNoise < -0.5 then
                    tileType = "dirt"
                end

                -- Basic road grid (placeholder)
                if x % 10 == 0 or y % 10 == 0 then -- Crude grid
                    if math.random() < 0.3 then tileType = "road" end
                end

                -- Apply rarer tiles based on distribution (crude override)
                local tileRoll = math.random()
                local cumulativeChance = 0
                for tType, chance in pairs(tileDistribution) do
                    cumulativeChance = cumulativeChance + chance
                    if (tileType=="grass" or tileType=="dirt" or tileType=="farmland_field") and tileRoll <= cumulativeChance then
                        if tType=="village_tiles" or tType=="forest_woodlot" or tType=="water" or tType=="ruins" then
                            tileType = tType
                            break
                        end
                    end
                end

                -- Create the tile
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = math.random(1, 4),
                    isSettled = true,
                    passable = true, -- Assume most tiles are passable initially
                    isUnderground = false
                }

                chunk.tiles[x][y] = tile
            end
        end

        -- Call helper functions
        SettledLandsBiome.generateFeatures(chunk, world, variant)
        SettledLandsBiome.populateEntities(chunk, world, variant)
        SettledLandsBiome.generateStructures(chunk, world, variant)
        SettledLandsBiome.postProcess(chunk, world, variant)
    end,

    -- Helper function implementations (Placeholders - need detail)
    generateFeatures = function(chunk, world, variant)
        print("Generating Settled Lands features...")
        -- Logic to place woodlots, orchards, field boundaries, crossroads, ponds
        -- This would likely involve placing specific tree entities, fence entities, or modifying tiles
    end,

    populateEntities = function(chunk, world, variant)
        print("Populating Settled Lands entities...")
        -- Spawn villagers, farmers, guards, domestic animals, fewer wild animals
        local variantData = variant and SettledLandsBiome.variants[variant] or nil
        local function spawnEntity(entityType, count)
            local multiplier = 1.0
            if variantData and variantData.entityAdjustments and variantData.entityAdjustments[entityType] then multiplier = variantData.entityAdjustments[entityType] end
            local adjustedCount = math.floor(count * multiplier + 0.5)
            for i = 1, adjustedCount do
                 local attempts = 0; local placed = false
                 while attempts < 5 and not placed do
                      local x = math.random(0, world.CHUNK_SIZE - 1)
                      local y = math.random(0, world.CHUNK_SIZE - 1)
                      local tile = chunk.tiles[x][y]
                      if tile and tile.passable then -- Ensure spawning on passable tile
                           world.entitySystem:addEntity(entityType, chunk.x * world.CHUNK_SIZE + x, chunk.y * world.CHUNK_SIZE + y)
                           placed = true
                      end
                      attempts = attempts + 1
                 end
            end
        end
        -- Spawn based on lists
        for _, et in ipairs(SettledLandsBiome.commonEntities) do spawnEntity(et, math.random(4,10)) end
        for _, et in ipairs(SettledLandsBiome.uncommonEntities) do spawnEntity(et, math.random(2,6)) end
        for _, et in ipairs(SettledLandsBiome.rareEntities) do if math.random() < 0.08 then spawnEntity(et, 1) end end
    end,

    generateStructures = function(chunk, world, variant)
        print("Generating Settled Lands structures...")
        -- Logic to place villages (clusters of tiles/buildings), farmsteads, towers, inns, mills, bridges
        -- This requires more sophisticated placement logic than random points.
         for _, structure in ipairs(SettledLandsBiome.structures) do
             if math.random() < structure.chance then
                  if not structure.requiresVariant or structure.requiresVariant == variant then
                       -- Find suitable location (needs space, maybe near road/water)
                       local attempts = 0; local placed = false; local sx, sy
                       while attempts < 10 and not placed do
                           sx = math.random(2, world.CHUNK_SIZE - 3)
                           sy = math.random(2, world.CHUNK_SIZE - 3)
                           local tile = chunk.tiles[sx][sy]
                           -- Basic check: place on grass/dirt/pavement, avoid roads/water directly for most
                           if tile and tile.passable and (tile.type == "grass" or tile.type == "dirt") then
                                -- Add checks for proximity to road/water if required by structure
                                placed = true
                           end
                           attempts = attempts + 1
                       end
                      if placed then
                           local worldX = chunk.x * world.CHUNK_SIZE + sx
                           local worldY = chunk.y * world.CHUNK_SIZE + sy
                           print("Placing structure: "..structure.name.." at "..worldX..","..worldY)
                           if world.chunkSystem and world.chunkSystem.placeStructure then
                                world.chunkSystem:placeStructure(chunk, structure.name, worldX, worldY, structure.size) -- Pass size maybe
                           else
                               world.entitySystem:addEntity("structure_marker", worldX, worldY, { structureType = structure.name })
                           end
                           if structure.entities then
                                for _, et in ipairs(structure.entities) do
                                     world.entitySystem:addEntity(et, worldX + math.random(-1,1), worldY + math.random(-1,1))
                                end
                           end
                      end
                  end
             end
         end
    end,

    postProcess = function(chunk, world, variant)
        print("Post-processing Settled Lands chunk...")
        -- Apply effects related to civilization: maybe slightly reduced wild spawns, path generation connecting structures?
        chunk.environmentalEffects = chunk.environmentalEffects or {}
        table.insert(chunk.environmentalEffects, {type="civilized_presence", level=0.5})
    end,

    init = function(worldCore)
        print("Settled Lands biome module initialized")
        SettledLandsBiome.worldCore = worldCore
        print("Settled Lands biome registered successfully for later use")
    end,

    registerWithWorld = function(world)
        print("Registering Settled Lands biome generators with world")
        if not world or not world.chunkSystem then print("Warning: Cannot register Settled Lands biome - world or chunkSystem is nil") return false end
        world.chunkSystem:registerGenerator(SettledLandsBiome.id, SettledLandsBiome.generate)
        for variantId, variantData in pairs(SettledLandsBiome.variants) do
            local fullVariantId = SettledLandsBiome.id .. "_" .. variantId
            world.chunkSystem:registerGenerator(fullVariantId, function(chunk, worldInstance)
                SettledLandsBiome.generate(chunk, worldInstance, variantId)
            end)
        end
        return true
    end,
}

return SettledLandsBiome