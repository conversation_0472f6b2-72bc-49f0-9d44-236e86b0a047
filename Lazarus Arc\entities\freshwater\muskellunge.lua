local Muskellunge = {
    id = "muskellunge",
    name = "Muskellunge",
    type = "muskellunge",

    -- Entity categories
    categories = { "animal", "fish", "large", "predator", "rare" }, -- Rare and powerful

    -- Threat categories
    threatCategories = { "predator", "large" }, -- Only very large predators are a threat
    foodCategories = { "fish", "prey" },

    -- Stats
    maxHealth = 50,  -- High health
    health = 50,
    speed = 5.0, -- Fast
    chaseSpeed = 7.0, -- Very fast when chasing

    -- Behaviors
    behaviors = { "hunt", "flee" },

    -- Behavior configurations
    behaviorConfigs = {
        hunt = {
            huntRadius = 15, -- Large hunting radius
            chaseRadius = 25, -- Persistent chase
            attackRadius = 3, -- Large attack range
            preferredTargets = { "northern_pike", "walleye", "perch" } -- Can even hunt other predators
        },
        flee = {
            useCategories = true,
            moveSpeed = 8.0,
            detectionRadius = 12
        }
    },

    -- Appearance
    appearance = {
        sprite = "muskellunge", -- Replace with your muskellunge sprite
        scale = 2.0, -- Large size
        animations = {
            "swim",
            "flee",
            "attack"
        }
    },

    -- Sound effects
    sounds = {
        flop = "fish_flop"
    },

    -- Loot drops
    drops = {
        {
            id = "muskellunge_meat",
            chance = 1.0,
            quantity = { 2, 5 } -- High yield meat
        },
        {
            id = "rare_fish_scales",
            chance = 0.3,
            quantity = { 1, 3 }
        }
    }
}

-- Initialize the muskellunge entity
function Muskellunge.init(entity, world)
    entity.position = entity.position or { x = 0, y = 0 }
    entity.velocity = entity.velocity or { x = 0, y = 0 }

    -- Initialize behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.init then
            behavior.init(entity, entity.behaviorConfigs[behaviorName])
        end
    end

    return entity
end

return Muskellunge