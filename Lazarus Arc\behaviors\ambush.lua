-- behaviors/ambush.lua
-- Ambush behavior combined with camouflage/stealth

local AmbushBehavior = {}
AmbushBehavior.id = "ambush"
AmbushBehavior.name = "Ambush Behavior"

-- Configuration parameters
AmbushBehavior.defaultConfig = {
    detectionRadius = 8,          -- Radius to detect potential targets
    ambushRadius = 2,             -- Distance to initiate ambush attack
    camouflage = true,            -- Whether entity uses camouflage
    stealthEffectiveness = 0.8,   -- Stealth effectiveness (0-1)
    moveSpeed = 3.0,              -- Speed during ambush
    patienceTime = {5, 15},       -- Waiting time in stealth before resetting position
    stealthVisibility = 0.2,      -- Visibility when in stealth
    revealAfterAttack = true,     -- Reveals entity after attacking
    returnToStealth = true,       -- En<PERSON><PERSON> returns to stealth if target escapes
    stealthCooldown = {10, 20},   -- Cooldown before re-entering stealth
    predatorAvoidance = false     -- Whether entity flees from predators
}

-- Initialize the behavior
function AmbushBehavior.init(entity, customConfig)
    -- Ensure entity has a valid position
    local pos = entity.position or { x = 0, y = 0 }
    entity.position = pos

    entity.ambushState = entity.ambushState or {
        hidingSpot = { x = pos.x, y = pos.y },
        isHidden = true,
        waitingTimer = math.random(AmbushBehavior.defaultConfig.patienceTime[1], AmbushBehavior.defaultConfig.patienceTime[2]),
        config = {}
    }

    local config = entity.ambushState.config
    for k, v in pairs(AmbushBehavior.defaultConfig) do
        config[k] = v
    end

    if customConfig then
        for k, v in pairs(customConfig) do
            config[k] = v
        end
    end
end

-- Update ambush behavior
function AmbushBehavior.update(entity, world, dt)
    local state = entity.ambushState
    local config = state.config

    if state.isHidden then
        -- Look for prey within ambush radius
        local target = AmbushBehavior.detectPrey(entity, world)
        if target then
            -- Attack target
            AmbushBehavior.attack(entity, target)
            state.isHidden = not config.revealAfterAttack
        else
            -- Countdown waiting timer
            state.waitingTimer = state.waitingTimer - dt
            if state.waitingTimer <= 0 then
                -- Reset position or change hiding spot
                state.waitingTimer = math.random(config.patienceTime[1], config.patienceTime[2])
                AmbushBehavior.resetHidingSpot(entity)
            end
        end
    else
        -- Return to stealth if needed
        if config.returnToStealth then
            AmbushBehavior.returnToStealth(entity, world, dt)
        end
    end
end

-- Detect potential targets
function AmbushBehavior.detectPrey(entity, world)
    -- Implementation details...
end

-- Reset hiding spot or move to a new one
function AmbushBehavior.resetHidingSpot(entity)
    -- Implementation details...
end

-- Attack logic
function AmbushBehavior.attack(entity, target)
    -- Implementation details...
end

-- Return to stealth logic
function AmbushBehavior.returnToStealth(entity, world, dt)
    -- Implementation details...
end

return AmbushBehavior
