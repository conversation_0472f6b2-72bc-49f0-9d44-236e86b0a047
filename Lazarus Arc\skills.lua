-- skills.lua
-- Skill system for Lazarus Arc

local Skills = {
    skills = {}, -- Will store all loaded skill data
    categories = {}, -- Skills organized by category
    loaded = {}, -- Tracks which skill category files have been loaded
    skillTiers = {}, -- Tracks tier requirements for skills
    skillDependencies = {} -- Tracks skill prerequisites
}

-- Initialize the skills system
function Skills.init()
    print("Initializing skills system...")
    
    -- Load core skill categories
    Skills.loadCategory("combat")
    Skills.loadCategory("magic")
    Skills.loadCategory("stealth")
    Skills.loadCategory("survival")
    Skills.loadCategory("crafting")
    Skills.loadCategory("social")
    
    print("Skills system initialized with " .. Skills.getSkillCount() .. " skills")
    return true
end

-- Load all skills in a category
function Skills.loadCategory(category)
    -- Prevent reloading
    if Skills.loaded[category] then
        return true
    end
    
    print("Loading skill category: " .. category)
    
    -- Try to load the category module
    local success, categoryModule = pcall(require, "skills." .. category)
    
    if not success then
        print("Failed to load skill category: " .. category)
        print(categoryModule) -- Show error
        return false
    end
    
    -- Register each skill from the category
    local count = 0
    for skillId, skillData in pairs(categoryModule) do
        -- Skip non-skill entries
        if type(skillData) == "table" and skillData.name then
            -- Add category to skill data
            skillData.category = skillData.category or category
            
            -- Register the skill
            Skills.registerSkill(skillId, skillData)
            count = count + 1
        end
    end
    
    -- Mark as loaded
    Skills.loaded[category] = true
    
    print("Loaded " .. count .. " skills from " .. category)
    return true
end

-- Register a single skill
function Skills.registerSkill(skillId, skillData)
    -- Store the skill
    Skills.skills[skillId] = skillData
    
    -- Add to category index
    local category = skillData.category or "misc"
    Skills.categories[category] = Skills.categories[category] or {}
    table.insert(Skills.categories[category], skillId)
    
    -- Register tier if specified
    if skillData.tier then
        Skills.skillTiers[skillId] = skillData.tier
    end
    
    -- Register dependencies
    if skillData.requires then
        Skills.skillDependencies[skillId] = skillData.requires
    end
    
    return true
end

-- Check if a skill exists
function Skills.exists(skillId)
    -- Direct lookup
    if Skills.skills[skillId] then
        return true
    end
    
    -- Try to load the skill directly
    local success, skillModule = pcall(require, "skills.individual." .. skillId)
    
    if success and type(skillModule) == "table" and skillModule.name then
        Skills.registerSkill(skillId, skillModule)
        return true
    end
    
    return false
end

-- Load a skill by ID
function Skills.load(skillId)
    -- Return cached skill if available
    if Skills.skills[skillId] then
        return Skills.skills[skillId]
    end
    
    -- Try to load from individual skill file
    local success, skillModule = pcall(require, "skills.individual." .. skillId)
    
    if success and type(skillModule) == "table" and skillModule.name then
        Skills.registerSkill(skillId, skillModule)
        return skillModule
    end
    
    -- Check each category (if not already loaded)
    for _, category in ipairs({"combat", "magic", "stealth", "survival", "crafting", "social"}) do
        if not Skills.loaded[category] then
            Skills.loadCategory(category)
            
            -- Check if skill was loaded with category
            if Skills.skills[skillId] then
                return Skills.skills[skillId]
            end
        end
    end
    
    return nil
end

-- Get all skills by category
function Skills.getByCategory(category)
    if not Skills.loaded[category] then
        Skills.loadCategory(category)
    end
    
    return Skills.categories[category] or {}
end

-- Get skills by tier
function Skills.getByTier(tier)
    local skills = {}
    
    for skillId, skillTier in pairs(Skills.skillTiers) do
        if skillTier == tier then
            table.insert(skills, skillId)
        end
    end
    
    return skills
end

-- Get skills available for a specific class
function Skills.getForClass(className)
    local classSkills = {}
    
    for skillId, skillData in pairs(Skills.skills) do
        if not skillData.class_restricted or 
           (skillData.class_restricted and skillData.classes and 
            table.contains(skillData.classes, className)) then
            table.insert(classSkills, skillId)
        end
    end
    
    -- Attempt to load class-specific skills module
    local success, classSkillsModule = pcall(require, "skills.classes." .. className)
    
    if success and type(classSkillsModule) == "table" then
        for skillId, skillData in pairs(classSkillsModule) do
            if type(skillData) == "table" and skillData.name and 
               not table.contains(classSkills, skillId) then
                -- Register the skill
                skillData.class_restricted = true
                skillData.classes = skillData.classes or {className}
                Skills.registerSkill(skillId, skillData)
                
                -- Add to result
                table.insert(classSkills, skillId)
            end
        end
    end
    
    return classSkills
end

-- Check if character meets requirements for a skill
function Skills.meetsRequirements(character, skillId)
    -- Load skill if not loaded
    local skillData = Skills.load(skillId)
    
    if not skillData then
        return false, "Skill does not exist"
    end
    
    -- Check level requirement
    if skillData.level_required and character.level < skillData.level_required then
        return false, "Character level too low"
    end
    
    -- Check class restrictions
    if skillData.class_restricted and skillData.classes then
        local hasClass = false
        
        for _, characterClass in ipairs(character.classes or {}) do
            if table.contains(skillData.classes, characterClass.name) then
                hasClass = true
                break
            end
        end
        
        if not hasClass then
            return false, "Character does not have required class"
        end
    end
    
    -- Check attribute requirements
    if skillData.attribute_requirements then
        for attribute, requiredValue in pairs(skillData.attribute_requirements) do
            if (character.attributes[attribute] or 0) < requiredValue then
                return false, "Character does not meet attribute requirements"
            end
        end
    end
    
    -- Check skill dependencies
    if skillData.requires then
        for _, requiredSkill in ipairs(skillData.requires) do
            local hasSkill = false
            
            if type(requiredSkill) == "string" then
                -- Simple string requirement
                if character.skills[requiredSkill] then
                    hasSkill = true
                end
            elseif type(requiredSkill) == "table" then
                -- Complex requirement with skill ID and level
                local reqSkillId = requiredSkill[1]
                local reqSkillLevel = requiredSkill[2] or 1
                
                if character.skills[reqSkillId] and 
                   character.skills[reqSkillId].level >= reqSkillLevel then
                    hasSkill = true
                end
            end
            
            if not hasSkill then
                return false, "Missing prerequisite skill"
            end
        end
    end
    
    -- All checks passed
    return true, nil
end

-- Use a skill (execute its effect)
function Skills.useSkill(character, skillId, targets, options)
    options = options or {}
    
    -- Load skill if not loaded
    local skillData = Skills.load(skillId)
    
    if not skillData then
        return { success = false, message = "Skill not found" }
    end
    
    -- Check if character has the skill
    if not character.skills[skillId] then
        return { success = false, message = "Character does not have this skill" }
    end
    
    -- Check if the skill is on cooldown
    if character.skills[skillId].cooldown and character.skills[skillId].cooldown > 0 then
        return { success = false, message = "Skill is on cooldown" }
    end
    
    -- Check resource costs
    if skillData.costType and skillData.costAmount then
        local costType = skillData.costType
        local costAmount = skillData.costAmount
        
        -- Apply cost reduction from skill level
        if character.skills[skillId].level > 1 and skillData.costReductionPerLevel then
            costAmount = costAmount - ((character.skills[skillId].level - 1) * skillData.costReductionPerLevel)
            costAmount = math.max(costAmount, skillData.minCost or 1) -- Ensure minimum cost
        end
        
        -- Check if character has enough resources
        if costType == "stamina" and character.stats.stamina < costAmount then
            return { success = false, message = "Not enough stamina" }
        elseif costType == "mana" and character.stats.mana < costAmount then
            return { success = false, message = "Not enough mana" }
        elseif costType == "health" and character.stats.health <= costAmount then
            return { success = false, message = "Not enough health" }
        end
        
        -- Apply resource cost
        if not options.previewOnly then
            if costType == "stamina" then
                character.stats.stamina = math.max(0, character.stats.stamina - costAmount)
            elseif costType == "mana" then
                character.stats.mana = math.max(0, character.stats.mana - costAmount)
            elseif costType == "health" then
                character.stats.health = math.max(1, character.stats.health - costAmount)
            end
        end
    end
    
    -- Just return success if this is a preview check
    if options.previewOnly then
        return { success = true, message = "Can use skill" }
    end
    
    -- Execute the skill effect
    local result
    if type(skillData.execute) == "function" then
        result = skillData.execute(character, targets, character.skills[skillId].level, options)
    else
        -- Process based on skill type
        if skillData.type == "attack" then
            result = Skills.executeAttackSkill(character, skillData, targets, character.skills[skillId].level, options)
        elseif skillData.type == "heal" then
            result = Skills.executeHealSkill(character, skillData, targets, character.skills[skillId].level, options)
        elseif skillData.type == "buff" then
            result = Skills.executeBuffSkill(character, skillData, targets, character.skills[skillId].level, options)
        elseif skillData.type == "debuff" then
            result = Skills.executeDebuffSkill(character, skillData, targets, character.skills[skillId].level, options)
        elseif skillData.type == "utility" then
            result = Skills.executeUtilitySkill(character, skillData, targets, character.skills[skillId].level, options)
        else
            result = { success = true, message = "Used " .. skillData.name }
        end
    end
    
    -- Apply cooldown
    if skillData.cooldown then
        character.skills[skillId].cooldown = skillData.cooldown
        
        -- Apply cooldown reduction from skill level
        if character.skills[skillId].level > 1 and skillData.cooldownReductionPerLevel then
            character.skills[skillId].cooldown = character.skills[skillId].cooldown - 
                ((character.skills[skillId].level - 1) * skillData.cooldownReductionPerLevel)
            
            -- Ensure minimum cooldown
            character.skills[skillId].cooldown = math.max(
                character.skills[skillId].cooldown, 
                skillData.minCooldown or 0.5
            )
        end
    end
    
    -- Gain skill experience
    if not options.noExperience then
        Skills.gainSkillExperience(character, skillId, skillData.experienceGain or 1)
    end
    
    return result or { success = true, message = "Used " .. skillData.name }
end

-- Execute an attack skill
function Skills.executeAttackSkill(character, skillData, targets, skillLevel, options)
    local result = {
        success = true,
        message = "Used " .. skillData.name,
        targets = {},
        totalDamage = 0
    }
    
    -- Base damage calculation
    local baseDamage = skillData.baseDamage or 10
    
    -- Scale damage by skill level
    if skillLevel > 1 and skillData.damagePerLevel then
        baseDamage = baseDamage + ((skillLevel - 1) * skillData.damagePerLevel)
    end
    
    -- Scale by relevant attribute
    local attributeScaling = skillData.attributeScaling or { strength = 1.0 }
    for attribute, scale in pairs(attributeScaling) do
        baseDamage = baseDamage + ((character.attributes[attribute] or 10) * scale)
    end
    
    -- Apply weapon damage if relevant
    if skillData.weaponDamage and character.equipment and character.equipment.mainhand then
        local weapon = character.getEquippedItemData("mainhand")
        if weapon then
            baseDamage = baseDamage + (weapon.damage or 0) * skillData.weaponDamage
        end
    end
    
    -- Process each target
    for _, target in ipairs(targets or {}) do
        local targetResult = {
            target = target,
            damage = 0,
            effects = {}
        }
        
        -- Calculate final damage
        local damage = baseDamage
        
        -- Apply random variance
        if skillData.damageVariance then
            local variance = skillData.damageVariance
            damage = damage * (1 + (math.random() * variance * 2 - variance))
        end
        
        -- Apply critical hit
        local criticalHit = false
        local critChance = (skillData.criticalChance or 5) + 
                           ((character.attributes.luck or 0) * 0.2) +
                           ((skillLevel - 1) * (skillData.critChancePerLevel or 0.5))
        
        if math.random(100) <= critChance then
            criticalHit = true
            local critMultiplier = skillData.criticalMultiplier or 1.5
            damage = damage * critMultiplier
            
            table.insert(targetResult.effects, "critical")
            character.statistics.criticalHits = (character.statistics.criticalHits or 0) + 1
        end
        
        -- Round damage to integer
        damage = math.floor(damage)
        
        -- Apply damage to target
        if target.takeDamage then
            local damageType = skillData.damageType or "physical"
            local actualDamage = target.takeDamage(damage, damageType, character)
            
            targetResult.damage = actualDamage
            result.totalDamage = result.totalDamage + actualDamage
            
            -- Update statistics
            character.statistics.damageDealt = (character.statistics.damageDealt or 0) + actualDamage
        else
            -- Target doesn't have takeDamage method
            targetResult.damage = damage
            result.totalDamage = result.totalDamage + damage
        end
        
        -- Apply secondary effects
        if skillData.effects then
            for effectName, effectChance in pairs(skillData.effects) do
                -- Scale effect chance with skill level
                local scaledChance = effectChance
                if skillLevel > 1 and skillData.effectChancePerLevel then
                    scaledChance = scaledChance + ((skillLevel - 1) * (skillData.effectChancePerLevel or 1))
                end
                
                -- Roll for effect
                if math.random(100) <= scaledChance then
                    -- Determine effect duration
                    local duration = skillData.effectDuration or 3
                    
                    -- Scale duration with skill level
                    if skillLevel > 1 and skillData.effectDurationPerLevel then
                        duration = duration + ((skillLevel - 1) * skillData.effectDurationPerLevel)
                    end
                    
                    -- Apply the effect
                    if target.addStatusEffect then
                        target.addStatusEffect(effectName, duration, 1.0)
                        table.insert(targetResult.effects, effectName)
                    end
                end
            end
        end
        
        table.insert(result.targets, targetResult)
    end
    
    -- Create result message
    if #targets == 1 then
        local targetResult = result.targets[1]
        result.message = "Used " .. skillData.name .. " on " .. (targets[1].name or "target") ..
                        " for " .. targetResult.damage .. " damage"
        
        if #targetResult.effects > 0 then
            result.message = result.message .. " (" .. table.concat(targetResult.effects, ", ") .. ")"
        end
    else
        result.message = "Used " .. skillData.name .. " on " .. #targets .. " targets for " ..
                        result.totalDamage .. " total damage"
    end
    
    return result
end

-- Execute a healing skill
function Skills.executeHealSkill(character, skillData, targets, skillLevel, options)
    local result = {
        success = true,
        message = "Used " .. skillData.name,
        targets = {},
        totalHealing = 0
    }
    
    -- Base healing calculation
    local baseHealing = skillData.baseHealing or 10
    
    -- Scale healing by skill level
    if skillLevel > 1 and skillData.healingPerLevel then
        baseHealing = baseHealing + ((skillLevel - 1) * skillData.healingPerLevel)
    end
    
    -- Scale by relevant attribute (usually wisdom or intelligence)
    local attributeScaling = skillData.attributeScaling or { wisdom = 1.0 }
    for attribute, scale in pairs(attributeScaling) do
        baseHealing = baseHealing + ((character.attributes[attribute] or 10) * scale)
    end
    
    -- Process each target
    for _, target in ipairs(targets or {}) do
        local targetResult = {
            target = target,
            healing = 0,
            effects = {}
        }
        
        -- Calculate final healing
        local healing = baseHealing
        
        -- Apply random variance
        if skillData.healingVariance then
            local variance = skillData.healingVariance
            healing = healing * (1 + (math.random() * variance * 2 - variance))
        end
        
        -- Check for critical healing
        local criticalHeal = false
        local critChance = (skillData.criticalChance or 5) + 
                           ((character.attributes.luck or 0) * 0.2) +
                           ((skillLevel - 1) * (skillData.critChancePerLevel or 0.5))
        
        if math.random(100) <= critChance then
            criticalHeal = true
            local critMultiplier = skillData.criticalMultiplier or 1.5
            healing = healing * critMultiplier
            
            table.insert(targetResult.effects, "critical")
        end
        
        -- Round healing to integer
        healing = math.floor(healing)
        
        -- Apply healing to target
        if target.heal then
            local actualHealing = target.heal(healing, character)
            
            targetResult.healing = actualHealing
            result.totalHealing = result.totalHealing + actualHealing
        else
            -- Target doesn't have heal method
            targetResult.healing = healing
            result.totalHealing = result.totalHealing + healing
        end
        
        -- Apply secondary effects
        if skillData.effects then
            for effectName, effectChance in pairs(skillData.effects) do
                -- Scale effect chance with skill level
                local scaledChance = effectChance
                if skillLevel > 1 and skillData.effectChancePerLevel then
                    scaledChance = scaledChance + ((skillLevel - 1) * (skillData.effectChancePerLevel or 1))
                end
                
                -- Roll for effect
                if math.random(100) <= scaledChance then
                    -- Determine effect duration
                    local duration = skillData.effectDuration or 3
                    
                    -- Scale duration with skill level
                    if skillLevel > 1 and skillData.effectDurationPerLevel then
                        duration = duration + ((skillLevel - 1) * skillData.effectDurationPerLevel)
                    end
                    
                    -- Apply the effect
                    if target.addStatusEffect then
                        target.addStatusEffect(effectName, duration, 1.0)
                        table.insert(targetResult.effects, effectName)
                    end
                end
            end
        end
        
        table.insert(result.targets, targetResult)
    end
    
    -- Create result message
    if #targets == 1 then
        local targetResult = result.targets[1]
        result.message = "Used " .. skillData.name .. " on " .. (targets[1].name or "target") ..
                        " for " .. targetResult.healing .. " healing"
        
        if #targetResult.effects > 0 then
            result.message = result.message .. " (" .. table.concat(targetResult.effects, ", ") .. ")"
        end
    else
        result.message = "Used " .. skillData.name .. " on " .. #targets .. " targets for " ..
                        result.totalHealing .. " total healing"
    end
    
    return result
end

-- Execute a buff skill
function Skills.executeBuffSkill(character, skillData, targets, skillLevel, options)
    local result = {
        success = true,
        message = "Used " .. skillData.name,
        targets = {},
        effects = {}
    }
    
    -- Determine base duration
    local baseDuration = skillData.baseDuration or 30
    
    -- Scale duration by skill level
    if skillLevel > 1 and skillData.durationPerLevel then
        baseDuration = baseDuration + ((skillLevel - 1) * skillData.durationPerLevel)
    end
    
    -- Process each target
    for _, target in ipairs(targets or {}) do
        local targetResult = {
            target = target,
            effects = {}
        }
        
        -- Apply each buff effect
        if skillData.buffs then
            for buffName, buffData in pairs(skillData.buffs) do
                -- Determine buff strength
                local strength = buffData.strength or 1.0
                
                -- Scale strength with skill level
                if skillLevel > 1 and buffData.strengthPerLevel then
                    strength = strength + ((skillLevel - 1) * buffData.strengthPerLevel)
                end
                
                -- Determine duration (can be overridden per buff)
                local duration = buffData.duration or baseDuration
                
                -- Apply the buff
                if target.addStatusEffect then
                    target.addStatusEffect(buffName, duration, strength)
                    table.insert(targetResult.effects, buffName)
                    
                    -- Track applied effects
                    if not result.effects[buffName] then
                        result.effects[buffName] = 0
                    end
                    result.effects[buffName] = result.effects[buffName] + 1
                end
            end
        end
        
        table.insert(result.targets, targetResult)
    end
    
    -- Create result message
    if #targets == 1 then
        local targetResult = result.targets[1]
        result.message = "Used " .. skillData.name .. " on " .. (targets[1].name or "target")
        
        if #targetResult.effects > 0 then
            result.message = result.message .. " (" .. table.concat(targetResult.effects, ", ") .. ")"
        end
    else
        result.message = "Used " .. skillData.name .. " on " .. #targets .. " targets"
        
        -- Add effect summary
        local effectList = {}
        for effect, count in pairs(result.effects) do
            table.insert(effectList, effect .. " x" .. count)
        end
        
        if #effectList > 0 then
            result.message = result.message .. " (" .. table.concat(effectList, ", ") .. ")"
        end
    end
    
    return result
end

-- Execute a debuff skill
function Skills.executeDebuffSkill(character, skillData, targets, skillLevel, options)
    local result = {
        success = true,
        message = "Used " .. skillData.name,
        targets = {},
        effects = {}
    }
    
    -- Determine base duration
    local baseDuration = skillData.baseDuration or 15
    
    -- Scale duration by skill level
    if skillLevel > 1 and skillData.durationPerLevel then
        baseDuration = baseDuration + ((skillLevel - 1) * skillData.durationPerLevel)
    end
    
    -- Process each target
    for _, target in ipairs(targets or {}) do
        local targetResult = {
            target = target,
            effects = {},
            resisted = {}
        }
        
        -- Apply each debuff effect
        if skillData.debuffs then
            for debuffName, debuffData in pairs(skillData.debuffs) do
                -- Calculate resist chance
                local resistChance = 0
                
                if target.stats then
                    if debuffData.type == "physical" then
                        resistChance = target.stats.pdefense / 2
                    elseif debuffData.type == "magical" then
                        resistChance = target.stats.mdefense / 2
                    end
                end
                
                -- Check if target resists
                if math.random(100) <= resistChance then
                    table.insert(targetResult.resisted, debuffName)
                else
                    -- Determine debuff strength
                    local strength = debuffData.strength or 1.0
                    
                    -- Scale strength with skill level
                    if skillLevel > 1 and debuffData.strengthPerLevel then
                        strength = strength + ((skillLevel - 1) * debuffData.strengthPerLevel)
                    end
                    
                    -- Determine duration (can be overridden per debuff)
                    local duration = debuffData.duration or baseDuration
                    
                    -- Apply the debuff
                    if target.addStatusEffect then
                        target.addStatusEffect(debuffName, duration, strength)
                        table.insert(targetResult.effects, debuffName)
                        
                        -- Track applied effects
                        if not result.effects[debuffName] then
                            result.effects[debuffName] = 0
                        end
                        result.effects[debuffName] = result.effects[debuffName] + 1
                    end
                end
            end
        end
        
        table.insert(result.targets, targetResult)
    end
    
    -- Create result message
    if #targets == 1 then
        local targetResult = result.targets[1]
        result.message = "Used " .. skillData.name .. " on " .. (targets[1].name or "target")
        
        -- Add applied effects
        if #targetResult.effects > 0 then
            result.message = result.message .. " (" .. table.concat(targetResult.effects, ", ") .. ")"
        end
        
        -- Add resisted effects
        if #targetResult.resisted > 0 then
            result.message = result.message .. " [Resisted: " .. table.concat(targetResult.resisted, ", ") .. "]"
        end
    else
        result.message = "Used " .. skillData.name .. " on " .. #targets .. " targets"
        
        -- Add effect summary
        local effectList = {}
        for effect, count in pairs(result.effects) do
            table.insert(effectList, effect .. " x" .. count)
        end
        
        if #effectList > 0 then
            result.message = result.message .. " (" .. table.concat(effectList, ", ") .. ")"
        end
    end
    
    return result
end

-- Execute a utility skill
function Skills.executeUtilitySkill(character, skillData, targets, skillLevel, options)
    local result = {
        success = true,
        message = "Used " .. skillData.name
    }
    
    -- This is very skill-specific, so it's mostly just a placeholder
    -- Real implementation would handle different utility skills differently
    
    -- Handle common utility types
    if skillData.utilityType == "identification" then
        -- Item identification
        if targets and targets[1] and character.identifyItem then
            local itemId = targets[1].id or targets[1]
            local identified = character.identifyItem(itemId)
            
            if identified then
                result.message = "Successfully identified " .. itemId
            else
                result.message = "Could not identify " .. itemId
                result.success = false
            end
        else
            result.message = "No valid target to identify"
            result.success = false
        end
    elseif skillData.utilityType == "lockpicking" then
        -- Lockpicking
        if targets and targets[1] and targets[1].unlock then
            local difficulty = targets[1].lockDifficulty or 10
            local skillCheckValue = character.attributes.dexterity + (skillLevel * 2)
            
            if skillCheckValue >= difficulty then
                targets[1].unlock()
                result.message = "Successfully picked the lock"
            else
                result.message = "Failed to pick the lock"
                result.success = false
            end
        else
            result.message = "No valid lock to pick"
            result.success = false
        end
    elseif skillData.utilityType == "trap_detection" then
        -- Trap detection
        -- Implementation would vary based on game's trap system
        result.message = "Searched for traps"
    end
    
    return result
end

-- Gain skill experience
function Skills.gainSkillExperience(character, skillId, amount)
    if not character.skills[skillId] then
        return false
    end
    
    -- Get current skill data
    local skill = character.skills[skillId]
    
    -- Add experience
    skill.experience = (skill.experience or 0) + amount
    
    -- Check for level up
    local experienceToLevel = Skills.getExperienceToNextLevel(skill.level or 1)
    
    if skill.experience >= experienceToLevel then
        -- Level up the skill
        skill.level = (skill.level or 1) + 1
        skill.experience = skill.experience - experienceToLevel
        
        -- Log level up
        print(character.name .. "'s " .. skillId .. " skill improved to level " .. skill.level)
        
        -- Check for secondary level ups
        return Skills.gainSkillExperience(character, skillId, 0)
    end
    
    return true
end

-- Calculate experience needed for next skill level
function Skills.getExperienceToNextLevel(currentLevel)
    -- Experience curve - each level requires more experience
    return math.floor(100 * (currentLevel * 1.5))
end

-- Get skill cooldown with modifiers
function Skills.getModifiedCooldown(character, skillId)
    local skill = character.skills[skillId]
    if not skill then return 0 end
    
    local skillData = Skills.load(skillId)
    if not skillData then return 0 end
    
    local baseCooldown = skillData.cooldown or 0
    
    -- Apply level-based cooldown reduction
    if skill.level > 1 and skillData.cooldownReductionPerLevel then
        baseCooldown = baseCooldown - ((skill.level - 1) * skillData.cooldownReductionPerLevel)
    end
    
    -- Apply equipment cooldown reduction
    local cooldownReduction = 0
    
    if character.equipment then
        for slot, itemId in pairs(character.equipment) do
            if itemId then
                local item = character.getEquippedItemData(slot)
                if item and item.cooldownReduction then
                    -- General cooldown reduction
                    cooldownReduction = cooldownReduction + (item.cooldownReduction or 0)
                    
                    -- Skill-specific cooldown reduction
                    if item.skillCooldownReduction and item.skillCooldownReduction[skillId] then
                        cooldownReduction = cooldownReduction + item.skillCooldownReduction[skillId]
                    end
                    
                    -- Category-specific cooldown reduction
                    if item.categoryCooldownReduction and skillData.category and 
                       item.categoryCooldownReduction[skillData.category] then
                        cooldownReduction = cooldownReduction + item.categoryCooldownReduction[skillData.category]
                    end
                end
            end
        end
    end
    
    -- Apply status effects that modify cooldowns
    if character.statusEffects then
        for effectName, effect in pairs(character.statusEffects) do
            if effect.cooldownModifier then
                cooldownReduction = cooldownReduction + effect.cooldownModifier
            end
        end
    end
    
    -- Apply cooldown reduction (cap at 80% reduction)
    local maxReduction = baseCooldown * 0.8
    cooldownReduction = math.min(cooldownReduction, maxReduction)
    
    -- Ensure minimum cooldown
    local finalCooldown = math.max(baseCooldown - cooldownReduction, skillData.minCooldown or 0.5)
    
    return finalCooldown
end

-- Get a list of all skills
function Skills.getAllSkills()
    local allSkills = {}
    
    for skillId, _ in pairs(Skills.skills) do
        table.insert(allSkills, skillId)
    end
    
    return allSkills
end

-- Get the total number of loaded skills
function Skills.getSkillCount()
    local count = 0
    for _ in pairs(Skills.skills) do
        count = count + 1
    end
    return count
end

-- Get skill info (for UI display)
function Skills.getSkillInfo(skillId)
    local skillData = Skills.load(skillId)
    if not skillData then return nil end
    
    -- Return a display-friendly subset of skill data
    return {
        id = skillId,
        name = skillData.name,
        description = skillData.description,
        icon = skillData.icon,
        category = skillData.category,
        type = skillData.type,
        cooldown = skillData.cooldown,
        costType = skillData.costType,
        costAmount = skillData.costAmount,
        tier = skillData.tier or 1,
        levelRequired = skillData.level_required
    }
end

-- Get skill dependency tree
function Skills.getDependencyTree(skillId)
    local skillData = Skills.load(skillId)
    if not skillData then return {} end
    
    local tree = {
        id = skillId,
        name = skillData.name,
        requires = {}
    }
    
    -- Process requirements recursively
    if skillData.requires then
        for _, requirement in ipairs(skillData.requires) do
            local reqId = requirement
            if type(requirement) == "table" then
                reqId = requirement[1]
            end
            
            table.insert(tree.requires, Skills.getDependencyTree(reqId))
        end
    end
    
    return tree
end

-- Check if a skill depends on another
function Skills.dependsOn(skillId, dependencyId)
    local skillData = Skills.load(skillId)
    if not skillData or not skillData.requires then return false end
    
    for _, requirement in ipairs(skillData.requires) do
        local reqId = requirement
        if type(requirement) == "table" then
            reqId = requirement[1]
        end
        
        if reqId == dependencyId then
            return true
        end
        
        -- Check recursively
        if Skills.dependsOn(reqId, dependencyId) then
            return true
        end
    end
    
    return false
end

-- Get all skills that depend on a specific skill
function Skills.getDependants(skillId)
    local dependants = {}
    
    -- Check all skills to see if they depend on the specified skill
    for id, _ in pairs(Skills.skills) do
        if Skills.dependsOn(id, skillId) then
            table.insert(dependants, id)
        end
    end
    
    return dependants
end

-- Find skills based on search criteria
function Skills.findSkills(criteria)
    local matches = {}
    criteria = criteria or {}
    
    for skillId, skillData in pairs(Skills.skills) do
        local match = true
        
        -- Filter by name
        if criteria.name and not string.find(string.lower(skillData.name), string.lower(criteria.name)) then
            match = false
        end
        
        -- Filter by category
        if criteria.category and skillData.category ~= criteria.category then
            match = false
        end
        
        -- Filter by type
        if criteria.type and skillData.type ~= criteria.type then
            match = false
        end
        
        -- Filter by tier
        if criteria.tier and (skillData.tier or 1) ~= criteria.tier then
            match = false
        end
        
        -- Filter by level requirement
        if criteria.minLevel and (skillData.level_required or 1) < criteria.minLevel then
            match = false
        end
        if criteria.maxLevel and (skillData.level_required or 1) > criteria.maxLevel then
            match = false
        end
        
        -- Filter by classes
        if criteria.class and skillData.classes then
            local classMatch = false
            for _, class in ipairs(skillData.classes) do
                if class == criteria.class then
                    classMatch = true
                    break
                end
            end
            if not classMatch then
                match = false
            end
        end
        
        if match then
            table.insert(matches, skillId)
        end
    end
    
    return matches
end

-- Utility helper function for tables
function table.contains(tbl, value)
    for _, v in ipairs(tbl) do
        if v == value then
            return true
        end
    end
    return false
end

-- Check if a skill is usable by character
function Skills.isUsable(character, skillId)
    -- Check if character has the skill
    if not character.skills[skillId] then
        return false, "Skill not known"
    end
    
    -- Check cooldown
    if character.skills[skillId].cooldown and character.skills[skillId].cooldown > 0 then
        return false, "On cooldown"
    end
    
    -- Check resource costs
    local skillData = Skills.load(skillId)
    if not skillData then
        return false, "Skill not found"
    end
    
    if skillData.costType and skillData.costAmount then
        local costAmount = skillData.costAmount
        
        -- Apply level-based cost reduction
        if character.skills[skillId].level > 1 and skillData.costReductionPerLevel then
            costAmount = costAmount - ((character.skills[skillId].level - 1) * skillData.costReductionPerLevel)
            costAmount = math.max(costAmount, skillData.minCost or 1)
        end
        
        if skillData.costType == "stamina" and character.stats.stamina < costAmount then
            return false, "Not enough stamina"
        elseif skillData.costType == "mana" and character.stats.mana < costAmount then
            return false, "Not enough mana"
        elseif skillData.costType == "health" and character.stats.health <= costAmount then
            return false, "Not enough health"
        end
    end
    
    return true, nil
end

-- Get recommended skills for a character
function Skills.getRecommendedSkills(character, limit)
    limit = limit or 5
    local recommendations = {}
    
    -- Get all available skills for the character's classes
    local availableSkills = {}
    for _, class in ipairs(character.classes or {}) do
        local classSkills = Skills.getForClass(class.name)
        for _, skillId in ipairs(classSkills) do
            if not character.skills[skillId] and not availableSkills[skillId] then
                local canLearn, _ = Skills.meetsRequirements(character, skillId)
                if canLearn then
                    availableSkills[skillId] = true
                end
            end
        end
    end
    
    -- Convert to array
    local skillArray = {}
    for skillId, _ in pairs(availableSkills) do
        local skillData = Skills.load(skillId)
        table.insert(skillArray, {
            id = skillId,
            name = skillData.name,
            level = skillData.level_required or 1,
            synergy = 0  -- Will calculate synergy score
        })
    end
    
    -- Calculate synergy score based on character's attributes and existing skills
    for _, skill in ipairs(skillArray) do
        local skillData = Skills.load(skill.id)
        
        -- Attribute synergy
        if skillData.attributeScaling then
            for attr, scale in pairs(skillData.attributeScaling) do
                skill.synergy = skill.synergy + ((character.attributes[attr] or 10) * scale * 0.2)
            end
        end
        
        -- Skill synergy (if it complements skills the character already has)
        for existingSkillId, existingSkill in pairs(character.skills) do
            local existingSkillData = Skills.load(existingSkillId)
            
            -- Same category bonus
            if existingSkillData.category == skillData.category then
                skill.synergy = skill.synergy + 1
            end
            
            -- Complementary types (e.g. attack + debuff)
            if (existingSkillData.type == "attack" and skillData.type == "debuff") or
               (existingSkillData.type == "debuff" and skillData.type == "attack") then
                skill.synergy = skill.synergy + 2
            end
            
            if (existingSkillData.type == "heal" and skillData.type == "buff") or
               (existingSkillData.type == "buff" and skillData.type == "heal") then
                skill.synergy = skill.synergy + 2
            end
        end
    end
    
    -- Sort by synergy score
    table.sort(skillArray, function(a, b) return a.synergy > b.synergy end)
    
    -- Take top recommendations
    for i = 1, math.min(limit, #skillArray) do
        table.insert(recommendations, skillArray[i].id)
    end
    
    return recommendations
end

-- Reset skill cooldowns for a character
function Skills.resetCooldowns(character, category)
    for skillId, skill in pairs(character.skills) do
        if not category or Skills.load(skillId).category == category then
            skill.cooldown = 0
        end
    end
end

return Skills
