-- behaviors/territorial.lua
-- Territorial behavior for entities that defend specific areas

local TerritorialBehavior = {}  -- create an empty table

TerritorialBehavior.id = "territorial"
TerritorialBehavior.name = "Territorial Behavior"

-- Configuration parameters
TerritorialBehavior.defaultConfig = {
    aggressionRadius = 8,         -- Radius at which intruders trigger aggression
    returnToTerritory = true,     -- Return to territory after chasing
    chaseSpeed = 2.5,             -- Speed when chasing intruders
    patrolSpeed = 1.2,            -- Speed when patrolling territory
    warnIntruders = true,         -- Issue warning before attacking
    warningDuration = {2, 4},     -- Duration of warning state
    maxChaseDistance = 15,        -- Maximum chase distance from territory center
    attackIntruders = true,       -- Attack intruders who enter territory
    territoryRadius = 12,         -- Radius defining the territory
    restPeriod = {5, 10}          -- Rest period after chasing intruders
}

-- Initialize the behavior
function TerritorialBehavior.init(entity, customConfig)
    -- Ensure the entity has a valid position
    if not entity.position then
        entity.position = { x = 0, y = 0 }
    end

    entity.territorialState = entity.territorialState or {
        territoryCenter = { x = entity.position.x, y = entity.position.y },
        isChasing = false,
        warningIssued = false,
        restTimer = 0,
        config = {}
    }

    local config = entity.territorialState.config
    for k, v in pairs(TerritorialBehavior.defaultConfig) do
        config[k] = v
    end

    if customConfig then
        for k, v in pairs(customConfig) do
            config[k] = v
        end
    end
end

-- Update function called every game tick
function TerritorialBehavior.update(entity, world, dt)
    local state = entity.territorialState
    local config = state.config

    -- Detect intruders
    local intruder = TerritorialBehavior.detectIntruder(entity, world)
    if intruder then
        if config.warnIntruders and not state.warningIssued then
            TerritorialBehavior.issueWarning(entity, intruder)
            state.warningIssued = true
            return
        end

        -- Chase intruder
        entity.velocity.x = (intruder.position.x - entity.position.x) * config.chaseSpeed
        entity.velocity.y = (intruder.position.y - entity.position.y) * config.chaseSpeed
        state.isChasing = true
    elseif state.warningIssued or state.isChasing then
        -- Return to territory after chase
        entity.velocity.x = (state.territoryCenter.x - entity.position.x) * config.patrolSpeed
        entity.velocity.y = (state.territoryCenter.y - entity.position.y) * config.patrolSpeed

        -- Check if back in territory
        local dx = entity.position.x - state.territoryCenter.x
        local dy = entity.position.y - state.territoryCenter.y
        local distance = math.sqrt(dx * dx + dy * dy)

        if distance < config.territoryRadius then
            state.warningIssued = false
            state.isChasing = false
            state.restTimer = math.random(config.restPeriod[1], config.restPeriod[2])
        end
    end

    -- Handle resting period
    if state.restTimer > 0 then
        state.restTimer = state.restTimer - dt
        entity.velocity.x, entity.velocity.y = 0, 0
    end
end

-- Placeholder: Detect intruders entering territory
function TerritorialBehavior.detectIntruder(entity, world)
    -- Implementation details...
end

-- Placeholder: Issue a warning to intruders
function TerritorialBehavior.issueWarning(entity, intruder)
    -- Implementation details...
end

return TerritorialBehavior
