-- weather/snow.lua
-- Snowy weather pattern

local SnowWeather = {}
SnowWeather.__index = SnowWeather

-- Basic properties
SnowWeather.id = "snow"
SnowWeather.name = "Snow"

-- Visual properties
SnowWeather.visual = {
    skyColor = {r = 200, g = 210, b = 225}, -- Pale blue-white
    sunIntensity = 0.4, -- Sun partially obscured
    cloudCoverage = 0.85, -- Heavy cloud cover
    cloudColor = {r = 230, g = 230, b = 240}, -- White-gray clouds
    snowColor = {r = 255, g = 255, b = 255}, -- Pure white snow
    snowIntensity = 0.6, -- How heavy the snowfall is
    snowflakeSize = 0.3, -- Size of snowflakes (0-1)
    snowDrift = 0.4, -- How much snow drifts in the wind
    ambientLightLevel = 0.8, -- Brighter due to snow's reflectivity
    groundReflectivity = 0.3 -- Snow reflects light
}

-- Environment modifiers
SnowWeather.environment = {
    temperature = 0.1, -- Very cold
    visibility = 0.7, -- Moderate visibility (reduced by snowfall)
    humidity = 0.6, -- Moderate humidity
    windStrength = 0.3, -- Light to moderate wind
    snowAccumulation = 0.02 -- Snow buildup rate per minute
}

-- Particle systems
SnowWeather.particles = {
    snowflakes = {
        enabled = true,
        intensity = 0.6,
        speed = 2,
        wobble = 0.5 -- How much snowflakes wobble as they fall
    },
    breath = {
        enabled = true, -- Visible breath in cold
        intensity = 0.8
    },
    groundSnowDust = {
        enabled = true, -- Snow kicked up from ground
        intensity = 0.4
    }
}

-- Sound effects
SnowWeather.sounds = {
    ambient = "snow_ambient",
    wind = "winter_wind",
    footsteps = "snow_crunch",
    volume = 0.5
}

-- Synth orchestra sound configurations
SnowWeather.synthSounds = {
    snow_ambient = {
        notes = {523, 659, 784, 988}, -- C5, E5, G5, B5 - crystalline snow tones
        durations = {1.0, 1.0, 1.0, 1.0},
        instrument = "xylophone",
        volume = 0.2,
        vibrato = false
    },
    winter_wind = {
        notes = {196, 147, 110, 82.5}, -- G3, D3, A2, E2 - cold wind
        durations = {2.5, 2.5, 2.5, 2.5},
        instrument = "flute",
        volume = 0.3,
        vibrato = true,
        vibratoRate = 0.4,
        vibratoDepth = 0.25
    },
    snow_crunch = {
        notes = {1047, 1319, 1568}, -- C6, E6, G6 - crisp snow sounds
        durations = {0.1, 0.1, 0.1},
        instrument = "kalimba",
        volume = 0.15,
        vibrato = false
    }
}

-- Effects on game entities and tiles
SnowWeather.effects = {
    -- Plants dormant or damaged
    plantFreezeChance = 0.1,
    plantGrowthMultiplier = 0.1,
    
    -- Animals slow down, seek shelter
    animalSpeedMultiplier = 0.7,
    animalShelterSeekingChance = 0.8,
    animalBodyHeatDecay = 0.15, -- Animals lose heat
    
    -- Tiles become snow-covered
    snowCoverageIncrease = 0.3,
    
    -- Water tiles freeze
    waterFreezeChance = 0.5,
    
    -- Player effects
    moveSpeedMultiplier = 0.8, -- Slower in snow
    staminaDrainMultiplier = 1.2, -- Faster stamina drain
    bodyHeatDecay = 0.2, -- Player loses body heat
    hypothermiaRisk = 0.4 -- Risk of hypothermia effect
}

-- Transition probabilities to other weather (per game hour)
SnowWeather.transitions = {
    clear = 0.1,
    sunny = 0.05, -- Least likely to become sunny
    cloudy = 0.2,
    foggy = 0.15,
    rain = 0.05, -- Can become rain if warming up
    -- Implied: 0.45 chance to stay snowing
}

-- Snow intensity variation
SnowWeather.intensityVariation = {
    current = 0.6, -- Current intensity
    target = 0.6, -- Target intensity for smooth transitions
    transitionSpeed = 0.1, -- How fast intensity changes
    minIntensity = 0.2, -- Lightest snow
    maxIntensity = 1.0, -- Heaviest snow
    changeChance = 0.1 -- Chance per minute to change target
}

-- Blizzard state
SnowWeather.blizzard = {
    active = false,
    intensity = 0.0, -- Additional intensity for blizzard
    windStrengthBonus = 0.5,
    visibilityReduction = 0.5,
    activationChance = 0.1, -- Per hour
    duration = {10, 30} -- Min/max minutes
}

-- Snow accumulation system
SnowWeather.snowAccumulation = {
    enabled = true,
    maxDepth = 5, -- Maximum snow depth
    currentDepth = 0, -- Current average snow depth
    driftingEnabled = true,
    drifts = {} -- Areas of deeper snow
}

-- Day/night cycle modifiers
SnowWeather.timeModifiers = {
    dawn = {
        skyColor = {r = 230, g = 210, b = 200},
        snowColor = {r = 255, g = 240, b = 230}, -- Slightly pink snow at dawn
        ambientLightLevel = 0.6
    },
    day = {
        -- Uses default properties
    },
    dusk = {
        skyColor = {r = 180, g = 190, b = 210},
        snowColor = {r = 240, g = 240, b = 255}, -- Slightly blue snow at dusk
        ambientLightLevel = 0.5
    },
    night = {
        skyColor = {r = 50, g = 60, b = 90},
        snowColor = {r = 200, g = 210, b = 230}, -- Blue-tinted snow at night
        ambientLightLevel = 0.2,
        sunIntensity = 0,
        starVisibility = 0.3, -- Some stars visible
        moonReflection = 0.6 -- Snow reflects moonlight
    }
}

-- Helper function to deep copy a table
local function deepCopy(orig)
    local copy
    if type(orig) == 'table' then
        copy = {}
        for orig_key, orig_value in next, orig, nil do
            copy[deepCopy(orig_key)] = deepCopy(orig_value)
        end
        setmetatable(copy, deepCopy(getmetatable(orig)))
    else
        copy = orig
    end
    return copy
end

-- Create a new instance of the weather pattern
function SnowWeather.new()
    local instance = setmetatable({}, SnowWeather)
    -- Copy all properties
    for k, v in pairs(SnowWeather) do
        if k ~= "__index" and k ~= "new" and type(v) ~= "function" then
            instance[k] = deepCopy(v)
        end
    end
    return instance
end

-- Initialize the weather pattern
function SnowWeather:init(world)
    if not world then return self end
    
    print("Initializing snow weather")
    
    -- Set global light level
    if self.visual and self.visual.ambientLightLevel then
        world.lightLevel = self.visual.ambientLightLevel
    end
    
    -- Initialize snow pattern
    if self.intensityVariation then
        self.intensityVariation.current = self.intensityVariation.target
    end
    
    -- Decide if this starts as a blizzard
    if math.random() < 0.2 then
        self:startBlizzard(world)
    end
    
    -- Start ambient sounds
    -- In a real implementation, this would play the appropriate sounds
    if self.sounds then
        print("Playing sound: " .. self.sounds.ambient)
        print("Playing sound: " .. self.sounds.wind)
    end
    
    -- Add snow particles
    if self.particles and self.particles.snowflakes and self.particles.snowflakes.enabled then
        -- In a real implementation, this would create snow particles
        print("Creating snow particles with intensity: " .. 
              self.particles.snowflakes.intensity * self.intensityVariation.current)
    end
    
    if self.particles and self.particles.breath and self.particles.breath.enabled then
        -- In a real implementation, this would create breath vapor particles for entities
        print("Creating breath vapor particles")
    end
    
    -- Initialize snow accumulation
    if self.snowAccumulation then
        self.snowAccumulation.currentDepth = 0.2 -- Start with a light dusting
        print("Initial snow depth: " .. self.snowAccumulation.currentDepth)
    end
    
    return self
end

-- Update function called every frame
function SnowWeather:update(world, dt)
    if not world then return end
    
    -- Update day/night cycle based on world time
    local timeOfDay = world.timeOfDay or "day"
    local modifiers = self.timeModifiers and self.timeModifiers[timeOfDay]
    
    if modifiers then
        -- Apply time-specific modifiers
        if modifiers.skyColor then
            -- In a real implementation, this would change the sky color
        end
        
        if modifiers.snowColor then
            -- In a real implementation, this would change the snow color
        end
        
        if modifiers.ambientLightLevel then
            -- Apply ambient light based on time of day and ground reflectivity
            local baseLight = modifiers.ambientLightLevel
            local reflectionBoost = 0
            
            -- Snow makes night brighter through reflection
            if timeOfDay == "night" and self.snowAccumulation and self.snowAccumulation.currentDepth > 1 and self.visual then
                reflectionBoost = self.visual.groundReflectivity * 
                                 (self.snowAccumulation.currentDepth / 
                                 self.snowAccumulation.maxDepth) * 
                                 modifiers.moonReflection
            end
            
            world.lightLevel = baseLight + reflectionBoost
        end
    end
    
    -- Update snow intensity variation
    if self.intensityVariation then
        local intensityVar = self.intensityVariation
        if intensityVar.current ~= intensityVar.target then
            -- Smooth transition to target intensity
            local diff = intensityVar.target - intensityVar.current
            local change = diff * intensityVar.transitionSpeed * dt
            intensityVar.current = intensityVar.current + change
            
            -- Update particle systems based on new intensity
            self:updateParticleIntensity()
            
            -- Update sound volume based on intensity
            self:updateSoundIntensity()
        end
        
        -- Occasionally change target intensity
        if math.random() < intensityVar.changeChance * dt * 60 then -- Convert per minute to per second
            -- Blizzard affects intensity range
            local minIntensity = intensityVar.minIntensity
            local maxIntensity = intensityVar.maxIntensity
            
            if self.blizzard and self.blizzard.active then
                minIntensity = 0.7
                maxIntensity = 1.0
            end
            
            intensityVar.target = math.random() * 
                                 (maxIntensity - minIntensity) + 
                                 minIntensity
                                 
            print("Snow intensity changing to: " .. intensityVar.target)
        end
    end
    
    -- Update blizzard state
    self:updateBlizzard(world, dt)
    
    -- Update snow accumulation
    if self.snowAccumulation and self.snowAccumulation.enabled then
        self:updateSnowAccumulation(world, dt)
    end
    
    -- Apply effects to game world
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            -- Affect plants - they can be damaged by cold
            if entity.categories and table.contains(entity.categories, "plant") and self.effects then
                -- Reduce growth rate in snow
                if entity.growthStage ~= nil then
                    entity.growthStage = entity.growthStage + 
                        (0.0001 * self.effects.plantGrowthMultiplier * dt)
                end
                
                -- Chance to freeze and take damage
                if math.random() < self.effects.plantFreezeChance * dt then
                    if entity.health then
                        entity.health = entity.health - 1
                    end
                    
                    -- Apply frozen effect
                    entity.frozen = true
                    
                    -- In a real implementation, would change appearance
                end
            end
            
            -- Affect animals - they slow down and seek shelter
            if entity.categories and table.contains(entity.categories, "animal") and self.effects then
                -- Reduce movement speed
                if entity.wanderState and entity.wanderState.config then
                    local originalSpeed = entity.wanderState.config.originalMoveSpeed or entity.wanderState.config.moveSpeed
                    entity.wanderState.config.originalMoveSpeed = originalSpeed
                    entity.wanderState.config.moveSpeed = originalSpeed * self.effects.animalSpeedMultiplier
                end
                
                -- Chance to seek shelter
                if math.random() < self.effects.animalShelterSeekingChance * dt then
                    -- In a real implementation, this would make animals seek shelter
                    -- For now, we make them prefer places with less snow
                    
                    -- If their temperature can drop, it does
                    if entity.stats and entity.stats.bodyTemperature then
                        entity.stats.bodyTemperature = entity.stats.bodyTemperature - 
                                                      self.effects.animalBodyHeatDecay * dt
                    end
                end
            end
            
            -- Affect water - it can freeze
            if (entity.type == "water" or 
               (entity.categories and table.contains(entity.categories, "water"))) and self.effects then
                if math.random() < self.effects.waterFreezeChance * dt then
                    -- Convert to ice
                    entity.type = "ice"
                    entity.categories = entity.categories or {}
                    table.insert(entity.categories, "ice")
                    
                    -- If it was in categories "water", replace with "ice"
                    for i, category in ipairs(entity.categories) do
                        if category == "water" then
                            entity.categories[i] = "ice"
                        end
                    end
                    
                    print("Water froze into ice")
                    
                    -- In a real implementation, would change appearance and properties
                end
            end
            
            -- Affect players
            if entity.type == "player" and self.effects then
                -- Movement is slower in snow
                entity.moveSpeedMultiplier = self.effects.moveSpeedMultiplier
                
                -- More stamina used
                entity.staminaDrainMultiplier = self.effects.staminaDrainMultiplier
                
                -- Body temperature drops
                if entity.stats and entity.stats.bodyTemperature then
                    -- Check if player is sheltered
                    local isSheltered = self:isEntitySheltered(entity, world)
                    
                    if not isSheltered then
                        entity.stats.bodyTemperature = entity.stats.bodyTemperature - 
                                                      self.effects.bodyHeatDecay * dt
                    end
                end
            end
        end
    end
end

-- Start a blizzard
function SnowWeather:startBlizzard(world)
    if not self.blizzard then return end
    
    self.blizzard.active = true
    self.blizzard.intensity = 0.0 -- Start at 0, build up
    self.blizzard.duration = math.random(self.blizzard.duration[1], self.blizzard.duration[2]) * 60 -- Convert to seconds
    
    -- Adjust wind and visibility
    if self.environment then
        self.environment.windStrength = self.environment.windStrength + self.blizzard.windStrengthBonus
        self.environment.visibility = self.environment.visibility - self.blizzard.visibilityReduction
    end
    
    print("Blizzard starting! Duration: " .. self.blizzard.duration .. " seconds")
    
    -- Notify players
    if world and world.messageSystem then
        world.messageSystem:broadcast("WARNING: Blizzard conditions forming. Seek shelter immediately.")
    end
end

-- End blizzard
function SnowWeather:endBlizzard(world)
    if not self.blizzard or not self.blizzard.active then return end
    
    self.blizzard.active = false
    self.blizzard.intensity = 0.0
    
    -- Restore wind and visibility
    if self.environment then
        self.environment.windStrength = self.environment.windStrength - self.blizzard.windStrengthBonus
        self.environment.visibility = self.environment.visibility + self.blizzard.visibilityReduction
    end
    
    print("Blizzard ending")
    
    -- Notify players
    if world and world.messageSystem then
        world.messageSystem:broadcast("Blizzard conditions easing. It is now safe to travel.")
    end
end

-- Update blizzard state
function SnowWeather:updateBlizzard(world, dt)
    if not self.blizzard then return end
    
    if self.blizzard.active then
        -- Count down duration
        self.blizzard.duration = self.blizzard.duration - dt
        
        -- Gradually increase intensity in first few minutes
        if self.blizzard.intensity < 1.0 then
            self.blizzard.intensity = math.min(1.0, self.blizzard.intensity + dt / 60) -- Full intensity after 1 minute
        end
        
        -- End if duration expired
        if self.blizzard.duration <= 0 then
            self:endBlizzard(world)
        end
    else
        -- Random chance to start a blizzard
        if math.random() < self.blizzard.activationChance * dt / 3600 then -- Convert per hour to per second
            self:startBlizzard(world)
        end
    end
end

-- Update particle systems based on intensity
function SnowWeather:updateParticleIntensity()
    -- This would update particle emission rates, etc.
    print("Particle intensity updated")
end

-- Update sound volume based on intensity
function SnowWeather:updateSoundIntensity()
    -- This would adjust sound volume based on snow/blizzard intensity
    print("Sound intensity updated")
end

-- Update snow accumulation
function SnowWeather:updateSnowAccumulation(world, dt)
    if not self.snowAccumulation or not self.environment then return end
    
    -- Calculate snow accumulation rate based on intensity
    local accumulationRate = self.environment.snowAccumulation
    
    if self.intensityVariation then
        accumulationRate = accumulationRate * self.intensityVariation.current
    end
    
    if self.blizzard and self.blizzard.active then
        accumulationRate = accumulationRate * (1 + self.blizzard.intensity)
    end
    
    -- Convert from per minute to per second
    accumulationRate = accumulationRate / 60
    
    -- Update snow depth
    self.snowAccumulation.currentDepth = math.min(
        self.snowAccumulation.maxDepth,
        self.snowAccumulation.currentDepth + accumulationRate * dt
    )
    
    -- In a real implementation, this would update all the terrain tiles
    if (self.snowAccumulation.currentDepth % 0.5) < accumulationRate * dt then
        print("Snow depth now: " .. self.snowAccumulation.currentDepth)
    end
    
    -- Update snow drifts if enabled
    if self.snowAccumulation.driftingEnabled then
        -- This would create deeper snow around obstacles, etc.
    end
end

-- Check if an entity is sheltered
function SnowWeather:isEntitySheltered(entity, world)
    -- In a real implementation, this would check if the entity is under a roof
    -- or inside a structure
    if entity.isIndoors then
        return true
    end
    
    -- Check if under a tree or other cover
    -- This would require spatial queries in the world
    
    return false -- Default to not sheltered
end

-- Clean up when weather changes
function SnowWeather:cleanUp(world)
    if not world then return end
    
    print("Snow weather ending")
    
    -- Stop particle systems
    if self.particles then
        -- In a real implementation, this would stop particle systems
    end
    
    -- Stop sound effects
    -- In a real implementation, this would fade out sounds
    
    -- End any active blizzard
    if self.blizzard and self.blizzard.active then
        self:endBlizzard(world)
    end
    
    -- Reset entity states
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            -- Reset movement speed for players
            if entity.type == "player" then
                entity.moveSpeedMultiplier = 1.0
                entity.staminaDrainMultiplier = 1.0
            end
            
            -- Reset animal movement speed
            if entity.categories and table.contains(entity.categories, "animal") then
                if entity.wanderState and entity.wanderState.config and entity.wanderState.config.originalMoveSpeed then
                    entity.wanderState.config.moveSpeed = entity.wanderState.config.originalMoveSpeed
                    entity.wanderState.config.originalMoveSpeed = nil
                end
            end
        end
    end
    
    -- Snow gradually melts away after the weather changes
    -- This would happen over time in a real implementation
end

-- Table contains helper function
function table.contains(table, element)
    if not table then return false end
    
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

return SnowWeather
