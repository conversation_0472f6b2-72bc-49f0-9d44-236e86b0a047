-- entities/undead_wolf.lua
-- Undead wolf variant with enhanced system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local UndeadWolf = {
    id = "undead_wolf",
    name = "Undead Wolf",
    type = "undead_wolf",
    shape = {
        {0, -1}, {0.8, -0.8}, {1, 0}, {0.8, 0.8},
        {0, 1}, {-0.8, 0.8}, {-1, 0}, {-0.8, -0.8}
    },
    size = 9,

    -- Entity categories
    categories = {"undead", "predator", "cursed", "large"},
    threatCategories = {"player", "living", "holy"},
    
    -- Base stats (undead are resilient but slower)
    maxHealth = 60,
    health = 60,
    maxStamina = 40,  -- Lower stamina than living wolves
    stamina = 40,
    speed = 2.2,      -- Slightly slower than living wolves
    attack = 16,
    defense = 8,
    undeadResistance = 15,  -- Resistance to necromancy
    holyVulnerability = 2.0, -- Vulnerable to holy damage
    
    -- Behaviors (undead have different behaviors)
    behaviors = {"hunt_living", "pack_undead", "wander_aimless"},
    behaviorConfigs = {
        hunt_living = {
            moveSpeed = 2.5,
            huntRadius = 20,  -- Better at detecting living
            chaseRadius = 25,
            preferredTargets = {"player", "rabbit", "deer", "any_living"}
        },
        pack_undead = {
            packRadius = 12,
            minPackSize = 2,
            maxPackSize = 8,  -- Larger packs than living wolves
            undeadBonus = 0.3
        },
        wander_aimless = {
            moveSpeed = 1.5,
            changeDirectionChance = 0.08,  -- More erratic movement
            restless = true
        }
    },
    
    -- Enhanced variant system for undead wolves
    variantChances = {
        normal = 0.65,          -- 65% zombie wolf
        shiny = 0.25,           -- 25% skeletal wolf (shiny)
        rare = 0.08,            -- 8% wraith wolf (rare)
        legendary = 0.02        -- 2% lich wolf (legendary)
    },
    
    variants = {
        normal = {
            name = "Zombie Wolf",
            description = "A reanimated wolf with rotting flesh",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.6, 0.7, 0.5, 1.0},  -- Sickly green-gray
                decay_effects = true
            }
        },
        
        shiny = {
            name = "Skeletal Wolf",
            description = "A wolf reduced to animated bones and dark energy",
            statModifiers = {
                maxHealth = 1.3,    -- 78 health
                speed = 1.4,        -- 3.08 speed (faster without flesh)
                attack = 1.2,       -- 19.2 attack
                defense = 0.8,      -- 6.4 defense (bones are fragile)
                bone_armor = 1.5
            },
            appearanceModifiers = {
                scale = 1.1,
                glow = true,
                colorTint = {0.9, 0.9, 1.0, 1.0},  -- Bone white with blue glow
                skeletal_form = true,
                dark_energy_aura = true
            },
            soundModifiers = {
                pitch = 1.2,
                volume = 1.1,
                bone_rattle = true
            }
        },
        
        rare = {
            name = "Wraith Wolf",
            description = "A spectral wolf that phases between worlds",
            statModifiers = {
                maxHealth = 1.6,    -- 96 health
                speed = 1.6,        -- 3.52 speed
                attack = 1.4,       -- 22.4 attack
                defense = 1.2,      -- 9.6 defense
                phase_ability = 2.0,
                spectral_power = 1.5
            },
            appearanceModifiers = {
                scale = 1.2,
                colorTint = {0.7, 0.7, 1.2, 0.7},  -- Translucent blue
                spectral_form = true,
                phase_shifting = true,
                ghostly_aura = true
            },
            soundModifiers = {
                pitch = 0.8,
                volume = 1.3,
                reverb = true,
                ethereal_howl = true
            }
        },
        
        legendary = {
            name = "Lich Wolf",
            description = "An ancient wolf transformed into an undead spellcaster",
            statModifiers = {
                maxHealth = 2.2,    -- 132 health
                speed = 1.3,        -- 2.86 speed
                attack = 1.8,       -- 28.8 attack
                defense = 1.6,      -- 12.8 defense
                necromantic_power = 5.0,
                spell_casting = 3.0
            },
            appearanceModifiers = {
                scale = 1.4,
                glow = true,
                colorTint = {0.8, 0.4, 1.0, 1.0},  -- Purple necromantic energy
                lich_crown = true,
                necromantic_aura = "legendary",
                floating_runes = true,
                death_magic_swirl = true
            },
            soundModifiers = {
                pitch = 0.6,
                volume = 1.8,
                reverb = true,
                echo = true,
                necromantic_chant = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "bone_fragment", chance = 0.9, quantity = {2, 4}},
        {id = "dark_essence", chance = 0.7, quantity = {1, 2}},
        {id = "cursed_fang", chance = 0.5, quantity = {1, 2}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "pristine_wolf_skull", chance = 0.8, quantity = {1, 1}},
            {id = "bone_armor_fragment", chance = 0.7, quantity = {1, 2}},
            {id = "dark_energy_core", chance = 0.6, quantity = {1, 1}}
        },
        rare = {
            {id = "spectral_wolf_essence", chance = 0.8, quantity = {1, 1}},
            {id = "phase_crystal", chance = 0.7, quantity = {1, 1}},
            {id = "wraith_howl_echo", chance = 0.6, quantity = {1, 1}},
            {id = "ethereal_fang", chance = 0.5, quantity = {1, 1}}
        },
        legendary = {
            {id = "lich_wolf_phylactery", chance = 0.9, quantity = {1, 1}},
            {id = "necromantic_crown", chance = 0.8, quantity = {1, 1}},
            {id = "death_magic_orb", chance = 0.7, quantity = {1, 1}},
            {id = "ancient_necromancy_scroll", chance = 0.6, quantity = {1, 1}},
            {id = "undeath_mastery_tome", chance = 0.4, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "undead_wolf",
        scale = 1.1,
        animations = {
            "idle", "shamble", "hunt", "howl_undead", "phase", "cast_spell"
        },
        variants = {
            "zombie_wolf", "skeletal_wolf", "wraith_wolf", "lich_wolf"
        }
    },
    
    -- Sound effects with undead characteristics
    sounds = {
        howl_undead = {
            file = "undead_wolf_howl",
            synth = {
                instrument = "organ",
                notes = {"D2", "F#2", "A2", "F#2", "D2"},
                durations = {0.8, 0.6, 1.0, 0.6, 1.0},
                volume = 0.7,
                haunting = true
            }
        },
        growl = {
            file = "undead_wolf_growl",
            synth = {
                instrument = "distorted_guitar",
                notes = {"E2", "C2"},
                durations = {0.5, 0.7},
                volume = 0.6,
                menacing = true
            }
        },
        bone_rattle = {
            file = "bone_rattle",
            synth = {
                instrument = "percussion",
                notes = {"C3", "D3", "C3"},
                durations = {0.1, 0.1, 0.1},
                volume = 0.4,
                skeletal = true
            }
        },
        death_magic = {
            file = "death_magic",
            synth = {
                instrument = "organ",
                notes = {"C2", "Eb2", "G2", "Bb2"},
                durations = {0.4, 0.4, 0.4, 0.8},
                volume = 0.8,
                necromantic = true
            }
        }
    },
    
    -- Special undead abilities
    abilities = {
        undead_resilience = {
            type = "passive",
            description = "Immunity to poison, disease, and fear",
            effect = "status_immunity"
        },
        life_drain = {
            type = "active",
            description = "Drain life force from living enemies",
            effect = "health_steal",
            cooldown = 8
        },
        pack_necromancy = {
            type = "passive",
            description = "Raise fallen pack members as undead",
            effect = "resurrection"
        },
        unholy_howl = {
            type = "active",
            description = "Howl that terrifies living creatures",
            effect = "fear_aura",
            cooldown = 15
        }
    }
}

-- Initialize the undead wolf entity using enhanced template
function UndeadWolf.init(entity, world)
    -- Copy all fields from UndeadWolf template to entity instance
    for k, v in pairs(UndeadWolf) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return UndeadWolf
