-- biomes/savanna.lua
-- Declare SavannaBiome at the top level so it can be referenced from within functions
local SavannaBiome = {}

-- Define the biome properties
SavannaBiome = {
    id = "savanna",
    name = "Savanna",
    description = "A grassland with scattered trees and a distinct wet and dry season.", --

    -- Environmental factors (Hot, variable humidity, high sunlight)
    environment = {
        humidity = 0.4,    -- Variable, base is dry side
        temperature = 0.8, -- Represents hot
        sunlight = 0.9,    -- Mostly open to sun
        windStrength = 0.4
    },

    -- Core tile types used in this biome
    -- NOTE: Uses 'grass' (dry variant?), 'dirt', 'water', 'mud', 'rocky_ground'. Assumes trees/termite mounds are entities/features.
    primaryTiles = {"grass", "dirt"}, -- Mix of grass and dry earth
    secondaryTiles = {"rocky_ground", "water"}, -- Scattered rocks and watering holes
    rareTiles = {"mud", "ruins"}, -- Mud near water holes, rare ruins

    -- Default proportions (adjustable through variants/season)
    tileDistribution = {
        grass = 0.6, -- Often dry grass
        dirt = 0.25,
        rocky_ground = 0.08,
        water = 0.04, -- Concentrated in watering holes
        mud = 0.02,   -- Around watering holes
        ruins = 0.01
    },

    -- Entities common to this biome
    commonEntities = {
        "gazelle", "zebra", "termite_mound", "acacia_tree", "savanna_bird" -- Added bird
    },
    uncommonEntities = {
        "lion", "giraffe", "elephant", "hyena" -- Added hyena
    },
    rareEntities = {
        "rhino", "savanna_guardian_spirit", "baobab_tree_ancient" -- Added rhino/spirit
    },

    -- Biome variants for diverse generation
    variants = {
        wooded_savanna = {
            name = "Wooded Savanna",
            treeDensity = 0.3, -- Higher tree density
            tileAdjustments = {
                grass = 0.5,
                dirt = 0.2,
                rocky_ground = 0.05
            },
            entityAdjustments = {
                 giraffe = 1.3, -- More browsers
                 elephant = 1.2,
                 lion = 0.8 -- Predators might have harder time?
            },
        },
        dry_savanna = {
            name = "Dry Savanna",
            humidity = 0.1,
            temperature = 0.85,
            fireRisk = 0.7,
            tileAdjustments = {
                grass = 0.4, -- More dry grass
                dirt = 0.4,
                rocky_ground = 0.15,
                water = 0.02, -- Less water
                mud = 0.01
            },
            entityAdjustments = {
                 zebra = 1.2, -- Adapted grazers
                 gazelle = 1.3,
                 elephant = 0.7 -- Need more water
            },
            specialEffects = {"high_fire_risk", "dust_devils", "heat_haze"}
        },
        wet_savanna = {
            name = "Wet Season Savanna",
            humidity = 0.7,
            temperature = 0.75,
            tileAdjustments = {
                grass = 0.7, -- Lush grass
                dirt = 0.1,
                water = 0.1, -- More water
                mud = 0.08, -- More mud
            },
            entityAdjustments = {
                 bison = 1.5, -- Or other large grazers thrive
                 wildebeest = 1.5, -- Added specific grazer
                 lion = 1.2 -- Prey is plentiful
            },
            specialEffects = {"lush_vegetation_visual", "temporary_streams"}
        },
        rocky_savanna = {
             name = "Rocky Savanna",
             tileAdjustments = {
                 grass = 0.4,
                 dirt = 0.2,
                 rocky_ground = 0.35, -- Significantly more rock
                 stone = 0.05 -- Added stone possibility
             },
             entityAdjustments = {
                  baboon = 1.5, -- Added rock-dwelling animal
                  leopard = 1.2, -- Added predator using rocks
                  zebra = 0.7
             },
             specialEffects = {"rock_outcrops", "hidden_crevices"}
        }
    },

    -- Structures that can generate in this biome
    structures = {
        {
            name = "tribal_village", -- Different from jungle/swamp village
            chance = 0.05,
            entities = {"savanna_villager", "herd_tender", "village_elder"}
        },
        {
            name = "hunting_camp",
            chance = 0.08,
            entities = {"hunter", "tracker", "trophy_rack"}
        },
        {
            name = "abandoned_outpost",
            chance = 0.06,
            entities = {"weathered_remains", "scavenging_hyena", "broken_equipment"}
        },
        {
            name = "ancient_ruins", -- Generic ruins, less common than specific variants
            chance = 0.03,
            entities = {"ruin_guardian_spirit", "ancient_pottery", "faded_carving"}
        }
    },

    -- Weather patterns (Distinct wet/dry season would ideally be handled by a global season system)
    weather = {
        transitions = {
             -- Simplified: Dry state vs Wet state
             clear_hot = { clear_hot = 70, wind_dust = 20, rain_light = 5, storm = 5 }, -- Dry Season Mix
             wind_dust = { clear_hot = 60, wind_dust = 30, rain_light = 10 }, -- Dry Season Mix
             rain_light = { clear_humid = 30, rain_light = 40, rain_heavy = 25, storm = 5 }, -- Wet Season Mix
             rain_heavy = { rain_light = 50, rain_heavy = 30, storm = 20 }, -- Wet Season Mix
             storm = { rain_heavy = 60, rain_light = 30, clear_humid = 10 }, -- Wet Season Mix
             clear_humid = { clear_humid = 50, rain_light = 40, fog_morning = 10 } -- Wet Season Mix
        },
        -- Default depends on current game season, placeholder:
        default = "clear_hot"
    },

    -- Unique features generation
    features = {
        {
            name = "baobab_tree", -- Large, unique tree entity/feature
            chance = 0.05,
            unique = true -- Often solitary and significant
        },
        {
            name = "animal_herd", -- Dynamic event or large group spawn
            chance = 0.15,
            -- Logic needed to spawn/manage a moving herd (zebra, wildebeest, etc.)
            isEvent = true
        },
        {
            name = "dry_riverbed", -- Feature placing dirt/sand/rocky tiles in a path
            chance = 0.1,
            length = {min=15, max=40}
        },
        {
            name = "oasis", -- Places oasis tiles (rare in savanna context maybe?)
            chance = 0.03,
            size = {min=4, max=10}
        },
        {
            name = "rock_formation", -- Feature placing rock/stone tiles (e.g., kopje)
            chance = 0.12,
            size = {min=5, max=15}
        },
         { -- Added from list
             name = "termite_mound", -- Entity/feature
             chance = 0.2,
             size = {min=1, max=3} -- In tile units?
         },
         { -- Added from list
             name = "watering_hole", -- Feature placing water/mud tiles
             chance = 0.1,
             size = {min=6, max=18}
         }
    },

    -- Generation algorithm (Structure based on forest/plains)
    generate = function(chunk, world, variant)
        print("Generating " .. (variant and SavannaBiome.variants[variant].name or "Savanna") .. " biome")
        local variantData = variant and SavannaBiome.variants[variant] or nil
        -- Determine base grass/dirt ratio based on variant/season (simplified)
        local grassChance = 0.6
        if variant == "dry_savanna" then grassChance = 0.4 elseif variant == "wet_savanna" then grassChance = 0.7 end

        -- Adjust tile distribution
        local tileDistribution = {}
        for tileType, chance in pairs(SavannaBiome.tileDistribution) do
            tileDistribution[tileType] = chance
            if variantData and variantData.tileAdjustments and variantData.tileAdjustments[tileType] then
                tileDistribution[tileType] = variantData.tileAdjustments[tileType]
            end
        end

        -- Noise functions
        local seed = world.seed + (chunk.x * 439 + chunk.y * 607)
        math.randomseed(seed)
        local function pnoise2D(x, y, frequency, seed_offset) return love.math.noise(x * frequency, y * frequency, (world.seed + (seed_offset or 0)) * 0.01) end

        -- Initialize 2D tiles array if not exists
        chunk.tiles = chunk.tiles or {}
        for x = 0, world.CHUNK_SIZE - 1 do
            chunk.tiles[x] = chunk.tiles[x] or {}
        end

        -- Generate tiles
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                local worldX = chunk.x * world.CHUNK_SIZE + x
                local worldY = chunk.y * world.CHUNK_SIZE + y

                local baseNoise = pnoise2D(worldX, worldY, 0.06, 1) -- Gentle rolling terrain
                local moistureNoise = pnoise2D(worldX, worldY, 0.04, 2) -- Broader moisture patterns (for wet/dry season feel)
                local rockNoise = pnoise2D(worldX, worldY, 0.15, 3) -- Rocky patches

                -- Determine tile type
                local tileType = "grass" -- Default
                if math.random() > grassChance or moistureNoise < -0.3 then -- Less grass if overall dry, or in dry patches
                    tileType = "dirt"
                end
                if rockNoise > 0.6 then
                    tileType = "rocky_ground"
                end

                -- Apply distribution for rarer types (simplified override)
                local tileRoll = math.random()
                local cumulativeChance = 0
                for tType, chance in pairs(tileDistribution) do
                    cumulativeChance = cumulativeChance + chance
                    if (tileType == "grass" or tileType == "dirt") and tileRoll <= cumulativeChance then
                         -- Only override base ground with rarer types like ruins/mud
                         if tType == "mud" or tType == "ruins" then
                              tileType = tType
                              break
                         -- Water placed by features mostly, don't override randomly
                         elseif tType == "water" and moistureNoise < -0.5 and baseNoise < -0.4 then -- Only place random water in very low/wet spots
                              tileType = "water"
                              break
                         end
                    end
                end

                 -- Variant specific placement (e.g. more dirt in savanna variant)
                 if variant == "savanna" and baseNoise > 0.7 and math.random() < 0.3 then tileType = "dirt" end

                -- Create the tile
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = math.random(1, 3), -- Generic variant
                    temperature = SavannaBiome.environment.temperature + (baseNoise * 0.1),
                    humidity = SavannaBiome.environment.humidity + (moistureNoise * 0.2),
                    isUnderground = false
                }

                 -- Apply properties based on type
                 if tileType == "grass" then tile.grassVariant = (moistureNoise < -0.2) and "dry" or "normal" end
                 if tileType == "dirt" then tile.cracked = (moistureNoise < -0.4) end

                chunk.tiles[x][y] = tile
            end
        end

        -- Call helper functions
        SavannaBiome.generateFeatures(chunk, world, variant)
        SavannaBiome.populateEntities(chunk, world, variant)
        SavannaBiome.generateStructures(chunk, world, variant)
        SavannaBiome.postProcess(chunk, world, variant)
    end,

    -- Helper function implementations (Basic Placeholders)
    generateFeatures = function(chunk, world, variant)
        print("Generating Savanna features...")
        -- Logic for placing features: Baobab/Acacia trees (entities), watering holes (water/mud tiles), dry riverbeds (dirt/sand path), rock formations, termite mounds (entity)
        for _, feature in ipairs(SavannaBiome.features) do
             if math.random() < feature.chance then
                  if not feature.requiresVariant or feature.requiresVariant == variant then
                      if not feature.unique or not (chunk.uniqueFeatures and chunk.uniqueFeatures[feature.name]) then
                         local fx = math.random(1, world.CHUNK_SIZE - 2)
                         local fy = math.random(1, world.CHUNK_SIZE - 2)
                         local fwx = chunk.x * world.CHUNK_SIZE + fx
                         local fwy = chunk.y * world.CHUNK_SIZE + fy
                         print("Placing feature: " .. feature.name .. " at " .. fwx .. "," .. fwy)
                         -- Add specific generation logic per feature
                         if feature.name == "watering_hole" then
                              local radius = math.random(feature.size.min, feature.size.max) / 2
                              for dx = -radius, radius do for dy = -radius, radius do
                                  if math.sqrt(dx*dx + dy*dy) <= radius then
                                      local tx, ty = fx+dx, fy+dy
                                      if tx >= 0 and tx < world.CHUNK_SIZE and ty >= 0 and ty < world.CHUNK_SIZE then
                                           local tileIdx = tx + ty * world.CHUNK_SIZE
                                           -- Center is water, edge is mud
                                           if math.sqrt(dx*dx + dy*dy) < radius * 0.7 then
                                                chunk.tiles[tx][ty].type = "water"
                                           else
                                                chunk.tiles[tx][ty].type = "mud"
                                           end
                                      end
                                  end
                              end end
                         elseif feature.name == "baobab_tree" or feature.name == "acacia_tree" then -- Assuming trees are entities
                              world.entitySystem:addEntity(feature.name, fwx, fwy, {size=math.random()+1.0})
                         elseif feature.name == "termite_mound" then
                              world.entitySystem:addEntity("termite_mound", fwx, fwy, {size=math.random()+0.5})
                         elseif feature.name == "dry_riverbed" then
                             -- Carve a path using dirt/sand tiles
                         end
                         -- Mark unique
                         if feature.unique then chunk.uniqueFeatures = chunk.uniqueFeatures or {}; chunk.uniqueFeatures[feature.name] = true end
                     end
                 end
             end
        end
         -- Place scattered Acacia trees commonly
         local numTrees = math.random(5, 15) * (variantData and variantData.treeDensity or 0.1) -- Use treeDensity if defined
         for i = 1, numTrees do
             local x = math.random(0, world.CHUNK_SIZE - 1)
             local y = math.random(0, world.CHUNK_SIZE - 1)
             if chunk.tiles[x][y].type ~= "water" then
                 world.entitySystem:addEntity("acacia_tree", chunk.x*world.CHUNK_SIZE+x, chunk.y*world.CHUNK_SIZE+y)
             end
         end
    end,

    populateEntities = function(chunk, world, variant)
        print("Populating Savanna entities...")
        local variantData = variant and SavannaBiome.variants[variant] or nil
        local function spawnEntity(entityType, count)
            local multiplier = 1.0
            if variantData and variantData.entityAdjustments and variantData.entityAdjustments[entityType] then multiplier = variantData.entityAdjustments[entityType] end
            local adjustedCount = math.floor(count * multiplier + 0.5)
            for i = 1, adjustedCount do
                 local attempts = 0; local placed = false
                 while attempts < 5 and not placed do
                      local x = math.random(0, world.CHUNK_SIZE - 1)
                      local y = math.random(0, world.CHUNK_SIZE - 1)
                      local tile = chunk.tiles[x][y]
                      if tile and tile.type ~= "water" and tile.type ~= "mud" then -- Avoid spawning directly in water/mud
                           world.entitySystem:addEntity(entityType, chunk.x * world.CHUNK_SIZE + x, chunk.y * world.CHUNK_SIZE + y)
                           placed = true
                      end
                      attempts = attempts + 1
                 end
            end
        end
        -- Spawn based on lists
        for _, et in ipairs(SavannaBiome.commonEntities) do spawnEntity(et, math.random(3,8)) end -- Savannas often have large populations
        for _, et in ipairs(SavannaBiome.uncommonEntities) do spawnEntity(et, math.random(1,4)) end
        for _, et in ipairs(SavannaBiome.rareEntities) do if math.random() < 0.1 then spawnEntity(et, 1) end end
        -- Add variant specifics (e.g., elephant, lion from variant list)
    end,

    generateStructures = function(chunk, world, variant)
        print("Generating Savanna structures...")
         -- Similar logic: Iterate structures, check chance/variant, find location, place structure & entities
          for _, structure in ipairs(SavannaBiome.structures) do
             if math.random() < structure.chance then
                  if not structure.requiresVariant or structure.requiresVariant == variant then
                      local attempts = 0; local placed = false; local sx, sy
                      while attempts < 10 and not placed do
                           sx = math.random(2, world.CHUNK_SIZE - 3)
                           sy = math.random(2, world.CHUNK_SIZE - 3)
                           local tile = chunk.tiles[sx][sy]
                           if tile and (tile.type=="grass" or tile.type=="dirt") then
                                placed = true
                           end
                           attempts = attempts + 1
                      end
                      if placed then
                           local worldX = chunk.x * world.CHUNK_SIZE + sx
                           local worldY = chunk.y * world.CHUNK_SIZE + sy
                           print("Placing structure: "..structure.name.." at "..worldX..","..worldY)
                           if world.chunkSystem and world.chunkSystem.placeStructure then
                                world.chunkSystem:placeStructure(chunk, structure.name, worldX, worldY)
                           else
                               world.entitySystem:addEntity("structure_marker", worldX, worldY, { structureType = structure.name })
                           end
                           if structure.entities then
                                for _, et in ipairs(structure.entities) do
                                     world.entitySystem:addEntity(et, worldX + math.random(-1,1), worldY + math.random(-1,1))
                                end
                           end
                      end
                  end
             end
         end
    end,

    postProcess = function(chunk, world, variant)
        print("Post-processing Savanna chunk...")
        -- Apply dry/wet season visuals? Fire risk?
        local variantData = variant and SavannaBiome.variants[variant] or nil
        local fireRisk = variantData and variantData.fireRisk or SavannaBiome.fireRisk or 0.2
        local humidity = variantData and variantData.humidity or SavannaBiome.environment.humidity

         for _, tile in pairs(chunk.tiles) do
              tile.fireRisk = fireRisk
              tile.humidity = humidity -- Ensure consistent humidity
              if tile.type == "grass" and humidity < 0.3 then
                   tile.grassVariant = "dry" -- Set visual variant
              end
         end

        if variantData and variantData.specialEffects then
             chunk.environmentalEffects = chunk.environmentalEffects or {}
             for _, effect in ipairs(variantData.specialEffects) do table.insert(chunk.environmentalEffects, effect) end
        end
    end,

    init = function(worldCore)
        print("Savanna biome module initialized")
        SavannaBiome.worldCore = worldCore
        print("Savanna biome registered successfully for later use")
    end,

    registerWithWorld = function(world)
        print("Registering Savanna biome generators with world")
        if not world or not world.chunkSystem then print("Warning: Cannot register Savanna biome - world or chunkSystem is nil") return false end
        world.chunkSystem:registerGenerator(SavannaBiome.id, SavannaBiome.generate)
        for variantId, variantData in pairs(SavannaBiome.variants) do
            local fullVariantId = SavannaBiome.id .. "_" .. variantId
            world.chunkSystem:registerGenerator(fullVariantId, function(chunk, worldInstance)
                SavannaBiome.generate(chunk, worldInstance, variantId)
            end)
        end
        return true
    end,
}

return SavannaBiome