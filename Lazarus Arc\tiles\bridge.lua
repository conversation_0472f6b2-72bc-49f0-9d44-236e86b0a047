local Bridge = {
    id = "bridge",
    name = "Bridge",
    type = "tile",
    
    -- Categories
    categories = {"structure", "bridge", "crossing", "manmade"},
    
    -- Properties
    properties = {
        walkable = true,
        swimable = false,
        climbable = false,
        flyable = true,
        diggable = true,
        buildable = true,
        flammable = true,
        maxHealth = 100,
        health = 100,
        decayRate = 0.1,
        decayChance = 0.01,
        supportStrength = 0.8,
        supportRadius = 2,
        spanLength = 3,
        spanWidth = 1,
        railHeight = 0.5,
        railStrength = 0.6,
        waterFlow = 0,
        waterLevel = 0,
        windResistance = 0.7,
        windDamage = 0.1,
        loadWeight = 0,
        maxLoad = 5,
        stability = 1.0,
        connectionPoints = {},
        supports = {}
    },
    
    -- Appearance
    appearance = {
        sprite = "bridge",
        scale = 1.0,
        animations = {
            "idle",
            "wind",
            "water",
            "decay"
        },
        variants = {
            "wooden",
            "stone",
            "metal",
            "rope"
        },
        blendMode = "normal",
        tint = {0.8, 0.8, 0.8},
        alpha = 1.0
    },
    
    -- Sound effects
    sounds = {
        walk = "bridge_walk",
        creak = "bridge_creak",
        break_sound = "bridge_break",
        water = "bridge_water"
    },
    
    -- Effects
    effects = {
        wind = {
            type = "environment",
            duration = 0,
            effects = {
                windResistance = 0.7,
                windDamage = 0.1
            }
        },
        water = {
            type = "environment",
            duration = 0,
            effects = {
                waterFlow = 0,
                waterLevel = 0
            }
        }
    },
    
    -- Spawn rules
    spawnRules = {
        structures = {
            "bridge_support",
            "bridge_rail",
            "bridge_tower"
        }
    }
}

-- Initialize the tile
function Bridge.init(tile, world)
    -- Copy all fields from Bridge template to tile instance
    for k, v in pairs(Bridge) do
        if type(v) ~= "function" and tile[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                tile[k] = {}
                for subk, subv in pairs(v) do
                    tile[k][subk] = subv
                end
            else
                tile[k] = v
            end
        end
    end

    -- Set random variant
    if tile.appearance and tile.appearance.variants then
        local variants = tile.appearance.variants
        tile.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize bridge state
    tile.properties.bridgeState = {
        health = tile.properties.maxHealth,
        decay = 0,
        load = 0,
        stability = 1.0,
        lastDamageTime = 0,
        waterLevel = 0,
        windDamage = 0
    }

    -- Initialize connection points
    tile.properties.connectionPoints = {
        north = {x = 0, y = -1},
        south = {x = 0, y = 1},
        east = {x = 1, y = 0},
        west = {x = -1, y = 0}
    }

    -- Initialize supports
    tile.properties.supports = {
        north = false,
        south = false,
        east = false,
        west = false
    }

    return tile
end

-- Update the tile
function Bridge.update(tile, world, dt)
    -- Update bridge state
    if tile.properties.bridgeState then
        -- Update decay
        if math.random() < tile.properties.decayChance then
            tile.properties.bridgeState.decay = math.min(1.0,
                tile.properties.bridgeState.decay + tile.properties.decayRate * dt)
            
            -- Apply decay damage
            if tile.properties.bridgeState.decay > 0.5 then
                tile.properties.bridgeState.health = math.max(0,
                    tile.properties.bridgeState.health - tile.properties.decayRate * dt)
            end
        end
        
        -- Update water effects
        if world.water and world.water.level then
            tile.properties.bridgeState.waterLevel = world.water.level
            
            -- Apply water damage
            if tile.properties.bridgeState.waterLevel > 0.8 then
                tile.properties.bridgeState.health = math.max(0,
                    tile.properties.bridgeState.health - tile.properties.decayRate * dt * 2)
            end
        end
        
        -- Update wind effects
        if world.wind then
            local windStrength = world.wind.strength * (1 - tile.properties.windResistance)
            tile.properties.bridgeState.windDamage = math.min(1.0,
                tile.properties.bridgeState.windDamage + windStrength * tile.properties.windDamage * dt)
            
            -- Apply wind damage
            if windStrength > 0.8 then
                tile.properties.bridgeState.health = math.max(0,
                    tile.properties.bridgeState.health - tile.properties.windDamage * dt)
            end
        end
        
        -- Update stability
        local supportCount = 0
        for _, hasSupport in pairs(tile.properties.supports) do
            if hasSupport then
                supportCount = supportCount + 1
            end
        end
        
        tile.properties.bridgeState.stability = supportCount / 4
        
        -- Check for collapse
        if tile.properties.bridgeState.health <= 0 or tile.properties.bridgeState.stability < 0.3 then
            -- Create collapse effect
            if world.createEffect then
                world.createEffect({
                    type = "collapse",
                    position = tile.position,
                    radius = tile.properties.spanLength
                })
            end
            
            -- Play break sound
            if world.playSound then
                world.playSound(tile.sounds.break_sound)
            end
            
            -- Convert to rubble
            if world.setTile then
                world.setTile(tile.position, "rubble")
            end
        end
    end
    
    -- Update load
    if world.entities then
        local totalLoad = 0
        for _, entity in ipairs(world.entities) do
            if entity.position then
                local distance = math.sqrt(
                    (entity.position.x - tile.position.x)^2 + 
                    (entity.position.y - tile.position.y)^2
                )
                
                if distance <= 1.0 then
                    totalLoad = totalLoad + (entity.weight or 1)
                end
            end
        end
        
        tile.properties.bridgeState.load = totalLoad
        
        -- Apply load damage
        if totalLoad > tile.properties.maxLoad then
            tile.properties.bridgeState.health = math.max(0,
                tile.properties.bridgeState.health - (totalLoad - tile.properties.maxLoad) * 0.1 * dt)
        end
    end
end

-- Handle interaction
function Bridge.interact(tile, world, entity, action)
    if action == "walk" then
        -- Check stability
        if tile.properties.bridgeState.stability < 0.5 then
            -- Create creak effect
            if world.createEffect then
                world.createEffect({
                    type = "creak",
                    position = tile.position,
                    strength = 1 - tile.properties.bridgeState.stability
                })
            end
            
            -- Play creak sound
            if world.playSound then
                world.playSound(tile.sounds.creak)
            end
        end
        
        -- Play walk sound
        if world.playSound then
            world.playSound(tile.sounds.walk)
        end
    elseif action == "dig" then
        -- Handle digging
        if tile.properties.diggable then
            -- Create material item based on variant
            if world.createItem then
                world.createItem({
                    type = tile.appearance.currentVariant,
                    amount = 1,
                    position = tile.position
                })
            end
            
            -- Play break sound
            if world.playSound then
                world.playSound(tile.sounds.break_sound)
            end
            
            -- Convert to water or air tile
            if world.setTile then
                if tile.properties.bridgeState.waterLevel > 0.5 then
                    world.setTile(tile.position, "water")
                else
                    world.setTile(tile.position, "air")
                end
            end
        end
    end
end

return Bridge 