-- structures/clock_tower.lua
-- Clock tower structure definition for community timekeeping

local StructureComponents = require("structure_components") -- Ensure this is present if needed

-- Declare ClockTower at the top level so it can be referenced from within functions
local ClockTower = {}

-- Define the structure properties
ClockTower = {
    id = "clock_tower",
    name = "Clock Tower",
    category = "civic",
    
    -- Physical properties
    width = 8,   -- Tiles wide
    height = 16, -- Tiles tall
    solid = true,
    providesShelter = true,
    
    -- Structure properties
    hasInterior = true,
    hasUnderground = false,
    condition = 0.8, -- 0.0 = completely ruined, 1.0 = pristine
    age = 0, -- Will be set during init, in years
    decayRate = 0.00001, -- Much slower base decay rate per year
    powerLevel = 0.7, -- For clock mechanism and lighting
    stabilityLevel = 0.8, -- Affected by component status and age
    lastRepairTime = 0, -- When the structure was last repaired
    repairCycle = 75, -- Years between natural repair cycles (longer than watchtower)
    seasonalEffects = {
        spring = 1.1, -- Growth/repair season
        summer = 1.0, -- Stable season
        autumn = 0.9, -- Decay season
        winter = 0.8  -- Harsh decay season
    },
    cosmeticDamage = {
        walls = 0.0, -- 0.0 = pristine, 1.0 = heavily damaged
        roof = 0.0,
        clock = 0.0,
        bells = 0.0
    },
    
    -- Define structure components
    components = {
        {
            id = "main_walls",
            type = "WALL",
            options = {
                material = "stone",
                isExterior = true,
                critical = true
            }
        },
        {
            id = "main_door",
            type = "DOOR",
            options = {
                material = "wood",
                isExterior = true,
                critical = true,
                requiresPower = false
            }
        },
        {
            id = "clock_face",
            type = "WINDOW",
            options = {
                material = "glass",
                isExterior = true,
                critical = true
            }
        },
        {
            id = "clock_mechanism",
            type = "POWER_SOURCE",
            options = {
                material = "clock_power",
                critical = true,
                maxOutput = 100
            }
        },
        {
            id = "bell_system",
            type = "POWER_SOURCE",
            options = {
                material = "bell_power",
                critical = true,
                maxOutput = 150
            }
        },
        {
            id = "lighting_system",
            type = "POWER_SOURCE",
            options = {
                material = "light_power",
                critical = false,
                maxOutput = 50
            }
        },
        {
            id = "support_beams",
            type = "SUPPORT",
            options = {
                material = "wood",
                critical = true,
                isLoadBearing = true
            }
        },
        {
            id = "roof",
            type = "ROOF",
            options = {
                material = "wood",
                critical = true,
                isWeatherproof = true
            }
        }
    },
    
    -- Spawn properties
    spawnChance = {
        village = 0.8,
        town = 0.9,
        city = 1.0,
        ruins = 0.2
    },
    
    -- Spawn requirements
    requirements = {
        minDistanceFromOtherStructures = 30,
        validTiles = {"grass", "stone", "dirt"},
        invalidTiles = {"water", "lava", "void"},
        needsFlatGround = true,
        preferredLocation = "center" -- Prefers to be in the center of settlements
    },
    
    -- NPCs and creatures that may inhabit this structure
    inhabitants = {
        common = {
            "clock_keeper",
            "bell_ringer",
            "maintenance_worker",
            "town_crier"
        },
        uncommon = {
            "master_clockmaker",
            "timekeeper",
            "bell_master",
            "tower_guardian"
        },
        rare = {
            "time_mage",
            "ancient_clockmaker",
            "tower_elder",
            "time_weaver"
        }
    },
    
    -- Items that may spawn inside
    loot = {
        common = {
            {id = "clock_part", chance = 0.7, quantity = {1, 3}},
            {id = "tool", chance = 0.6, quantity = {1, 2}},
            {id = "potion", chance = 0.5, quantity = {1, 2}},
            {id = "book", chance = 0.4, quantity = {1, 2}}
        },
        uncommon = {
            {id = "magic_clock_part", chance = 0.3, quantity = {1, 1}},
            {id = "time_crystal", chance = 0.25, quantity = {1, 1}},
            {id = "bell_mechanism", chance = 0.2, quantity = {1, 1}},
            {id = "clock_tool", chance = 0.15, quantity = {1, 1}}
        },
        rare = {
            {id = "legendary_clock_part", chance = 0.1, quantity = {1, 1}},
            {id = "time_artifact", chance = 0.1, quantity = {1, 1}},
            {id = "ancient_mechanism", chance = 0.15, quantity = {1, 1}},
            {id = "time_weaver_tool", chance = 0.05, quantity = {1, 1}}
        }
    },
    
    -- Variant types for this structure
    variants = {
        {
            name = "classical",
            description = "Classical stone clock tower",
            materials = {
                main_walls = "stone",
                main_door = "wood",
                clock_face = "glass",
                clock_mechanism = "basic_clock",
                bell_system = "basic_bell",
                lighting_system = "torch",
                support_beams = "wood",
                roof = "wood"
            },
            features = {
                "clock_room",
                "bell_chamber",
                "maintenance_room",
                "timekeeper_office",
                "public_gallery"
            },
            powerOutput = 100,
            stabilityLevel = 0.8,
            inhabitants = {
                "classical_clock_keeper",
                "basic_bell_ringer",
                "regular_maintainer"
            }
        },
        {
            name = "gothic",
            description = "Gothic clock tower",
            materials = {
                main_walls = "gothic_stone",
                main_door = "gothic_wood",
                clock_face = "stained_glass",
                clock_mechanism = "gothic_clock",
                bell_system = "gothic_bell",
                lighting_system = "gothic_light",
                support_beams = "gothic_wood",
                roof = "gothic_wood"
            },
            features = {
                "gothic_clock_room",
                "gothic_bell_chamber",
                "gothic_maintenance",
                "gothic_office",
                "gothic_gallery"
            },
            powerOutput = 150,
            stabilityLevel = 0.85,
            inhabitants = {
                "gothic_clock_keeper",
                "gothic_bell_ringer",
                "gothic_maintainer"
            }
        },
        {
            name = "magical",
            description = "Magical clock tower",
            materials = {
                main_walls = "magic_stone",
                main_door = "magic_wood",
                clock_face = "magic_glass",
                clock_mechanism = "magic_clock",
                bell_system = "magic_bell",
                lighting_system = "magic_light",
                support_beams = "magic_wood",
                roof = "magic_wood"
            },
            features = {
                "magic_clock_room",
                "magic_bell_chamber",
                "magic_maintenance",
                "magic_office",
                "magic_gallery"
            },
            powerOutput = 200,
            stabilityLevel = 0.9,
            inhabitants = {
                "magic_clock_keeper",
                "magic_bell_ringer",
                "magic_maintainer"
            }
        },
        {
            name = "ancient",
            description = "Ancient clock tower",
            materials = {
                main_walls = "ancient_stone",
                main_door = "ancient_wood",
                clock_face = "ancient_glass",
                clock_mechanism = "ancient_clock",
                bell_system = "ancient_bell",
                lighting_system = "ancient_light",
                support_beams = "ancient_wood",
                roof = "ancient_wood"
            },
            features = {
                "ancient_clock_room",
                "ancient_bell_chamber",
                "ancient_maintenance",
                "ancient_office",
                "ancient_gallery"
            },
            powerOutput = 250,
            stabilityLevel = 0.75,
            inhabitants = {
                "ancient_clock_keeper",
                "ancient_bell_ringer",
                "ancient_maintainer"
            }
        },
        {
            name = "ruined",
            description = "Ruined clock tower",
            materials = {
                main_walls = "ruined_stone",
                main_door = "ruined_wood",
                clock_face = "broken_glass",
                clock_mechanism = "ruined_clock",
                bell_system = "ruined_bell",
                lighting_system = "ruined_light",
                support_beams = "ruined_wood",
                roof = "ruined_wood"
            },
            features = {
                "ruined_clock_room",
                "ruined_bell_chamber",
                "ruined_maintenance",
                "ruined_office",
                "ruined_gallery"
            },
            powerOutput = 50,
            stabilityLevel = 0.4,
            inhabitants = {
                "ruined_clock_keeper",
                "ruined_bell_ringer",
                "ruined_maintainer"
            }
        }
    },
    
    -- Tile layout (exterior)
    tileLayout = {
        "wwwwwwww",
        "w......w",
        "w......w",
        "w......w",
        "w......w",
        "w......w",
        "w......w",
        "w......w",
        "w......w",
        "w......w",
        "w......w",
        "w......w",
        "w......w",
        "w......w",
        "w......w",
        "wwwwwwww"
    },
    
    -- Tile legend for exterior
    tileLegend = {
        w = "clock_tower_wall",
        ["."] = "clock_tower_floor"
    },
    
    -- Interior layout
    interiorLayout = {
        ground_floor = {
            width = 6,
            height = 6,
            features = {"entrance", "public_gallery", "maintenance_room"}
        },
        middle_floors = {
            width = 6,
            height = 6,
            features = {"timekeeper_office", "clock_room", "bell_chamber"}
        },
        top_floor = {
            width = 6,
            height = 6,
            features = {"observation_deck", "clock_mechanism", "bell_system"}
        }
    },
    
    -- Interior tile legend
    interiorLegend = {
        ["."] = "clock_tower_floor",
        w = "clock_tower_wall",
        d = "clock_tower_door",
        s = "clock_tower_stairs",
        c = "clock_face",
        b = "bell_chamber"
    },
    
    -- Tags for search/filtering
    tags = {"civic", "timekeeping", "community", "landmark"},
    
    -- Initialize the structure
    init = function(structure, world, x, y)
        -- Set structure position
        structure.x = x
        structure.y = y
        
        -- Set random variant if none specified
        structure.variant = structure.variant or ClockTower.variants[math.random(#ClockTower.variants)]
        
        -- Set random age between 0 and 200 years (older than watchtower)
        structure.age = math.random(0, 200)
        
        -- Initialize cosmetic damage based on age
        local ageFactor = structure.age / 200
        structure.cosmeticDamage = {
            walls = math.min(0.8, ageFactor * 1.2),
            roof = math.min(0.9, ageFactor * 1.5),
            clock = math.min(0.7, ageFactor),
            bells = math.min(0.6, ageFactor * 0.8)
        }
        
        -- Set last repair time to current age
        structure.lastRepairTime = structure.age
        
        -- Apply variant-specific properties
        local variant = ClockTower.variants[structure.variant]
        
        -- Update component materials based on variant
        for componentId, material in pairs(variant.materials) do
            if structure.components[componentId] then
                structure.components[componentId].options.material = material
            end
        end
        
        -- Update tile legends based on variant
        structure.tileLegend.w = variant.name .. "_clock_tower_wall"
        structure.interiorLegend["."] = variant.name .. "_clock_tower_floor"
        
        -- Set power output and stability level based on variant
        structure.components["clock_mechanism"].options.maxOutput = variant.powerOutput
        structure.stabilityLevel = variant.stabilityLevel
        
        -- Adjust stability based on age
        structure.stabilityLevel = structure.stabilityLevel * (1 - (structure.age * structure.decayRate))
        
        -- Initialize components
        if not structure.components then
            structure.components = {}
            for _, componentData in ipairs(ClockTower.components) do
                local component = StructureComponents.createComponent(componentData.type, componentData.options)
                structure.components[componentData.id] = component
            end
        end
        
        -- Set up interior
        if structure.hasInterior then
            structure.interior = {
                illuminated = false,
                explored = false,
                integrity = structure.stabilityLevel,
                tiles = {},
                features = variant.features
            }
            
            -- Generate interior tiles based on layout
            ClockTower.generateInterior(structure)
        end
        
        return structure
    end,
    
    -- Update the structure
    update = function(structure, world, dt)
        -- Get current season
        local currentSeason = world.getCurrentSeason()
        local seasonMultiplier = structure.seasonalEffects[currentSeason]
        
        -- Update all components' seasonal state
        for componentId, component in pairs(structure.components) do
            StructureComponents.updateSeason(component, currentSeason)
        end
        
        -- Update stability level based on component status and season
        local supportBeams = structure.components["support_beams"]
        local clockMechanism = structure.components["clock_mechanism"]
        local mainWalls = structure.components["main_walls"]
        
        if supportBeams and clockMechanism and mainWalls then
            -- Calculate stability level based on component health
            local supportHealth = supportBeams.health / supportBeams.maxHealth
            local clockHealth = clockMechanism.health / clockMechanism.maxHealth
            local wallHealth = mainWalls.health / mainWalls.maxHealth
            
            structure.stabilityLevel = math.min(supportHealth, clockHealth, wallHealth)
            
            -- Apply seasonal decay
            local yearsSinceLastRepair = structure.age - structure.lastRepairTime
            local decayFactor = (yearsSinceLastRepair * structure.decayRate) * seasonMultiplier
            
            -- Natural repair cycle
            if yearsSinceLastRepair >= structure.repairCycle then
                -- Structure naturally repairs some damage
                structure.lastRepairTime = structure.age
                structure.stabilityLevel = math.min(1.0, structure.stabilityLevel + 0.1)
                
                -- Reduce cosmetic damage
                for component, damage in pairs(structure.cosmeticDamage) do
                    structure.cosmeticDamage[component] = math.max(0.0, damage - 0.1)
                end
            else
                -- Apply decay
                structure.stabilityLevel = math.max(0.0, structure.stabilityLevel - decayFactor)
                
                -- Increase cosmetic damage
                for component, damage in pairs(structure.cosmeticDamage) do
                    structure.cosmeticDamage[component] = math.min(1.0, damage + (decayFactor * 0.1))
                end
            end
        end
        
        -- Update power level based on component status and season
        local clockMechanism = structure.components["clock_mechanism"]
        local bellSystem = structure.components["bell_system"]
        
        if clockMechanism and bellSystem then
            -- Calculate power level based on component health
            local clockHealth = clockMechanism.health / clockMechanism.maxHealth
            local bellHealth = bellSystem.health / bellSystem.maxHealth
            
            structure.powerLevel = math.min(clockHealth, bellHealth)
            
            -- Apply seasonal effects to power
            structure.powerLevel = structure.powerLevel * seasonMultiplier
            
            -- Update interior illumination
            structure.interior.illuminated = structure.powerLevel > 0.3
        end
        
        -- Update visual effects based on component status and cosmetic damage
        for componentId, component in pairs(structure.components) do
            local visualState = StructureComponents.getVisualState(component)
            
            -- Update tile legend based on visual state
            if component.type == "WALL" then
                local wallDamage = structure.cosmeticDamage.walls
                if component.status == "destroyed" then
                    structure.tileLegend.w = "collapsed_" .. structure.variant.name .. "_wall"
                elseif component.status == "critical" then
                    structure.tileLegend.w = "damaged_" .. structure.variant.name .. "_wall"
                elseif wallDamage > 0.7 then
                    structure.tileLegend.w = "weathered_" .. structure.variant.name .. "_wall"
                elseif wallDamage > 0.3 then
                    structure.tileLegend.w = "worn_" .. structure.variant.name .. "_wall"
                end
                
                -- Apply seasonal overlay
                if visualState.overlay then
                    structure.tileLegend.w = structure.tileLegend.w .. "_" .. visualState.overlay.name
                end
            elseif component.type == "POWER_SOURCE" and component.status == "critical" then
                -- Add warning effects
                structure.interior.effects = {
                    "flickering_lights",
                    "unstable_clock",
                    "wavering_bells"
                }
            end
        end
        
        -- Check structural integrity
        local supportBeams = structure.components["support_beams"]
        if supportBeams and supportBeams.status == "destroyed" then
            -- Tower is unstable
            structure.condition = structure.condition * 0.5
        end
    end,
    
    -- Repair the structure
    repair = function(structure, world, repairType)
        if repairType == "cosmetic" then
            -- Cosmetic repairs (can be done by NPCs)
            for component, damage in pairs(structure.cosmeticDamage) do
                structure.cosmeticDamage[component] = math.max(0.0, damage - 0.2)
            end
        elseif repairType == "structural" then
            -- Structural repairs (requires more effort)
            structure.lastRepairTime = structure.age
            structure.stabilityLevel = math.min(1.0, structure.stabilityLevel + 0.3)
            
            -- Repair components
            for componentId, component in pairs(structure.components) do
                if component.status ~= "destroyed" then
                    component.health = math.min(component.maxHealth, component.health + (component.maxHealth * 0.3))
                end
            end
        end
    end,
    
    -- Generate interior layout
    generateInterior = function(structure)
        -- Implementation of interior generation
        -- This would create the actual interior tiles based on the layout
        -- and variant-specific features
    end,
    
    -- Draw the structure
    draw = function(structure, world, x, y)
        -- Draw base structure
        for i = 1, #structure.tileLayout do
            for j = 1, #structure.tileLayout[i] do
                local tile = structure.tileLayout[i][j]
                if tile ~= "." then
                    local tileName = structure.tileLegend[tile]
                    if tileName then
                        -- Draw base tile
                        world.drawTile(tileName, x + j - 1, y + i - 1)
                        
                        -- Draw seasonal overlay if present
                        local component = structure.components["main_walls"]
                        if component then
                            local visualState = StructureComponents.getVisualState(component)
                            if visualState.overlay then
                                world.drawTileOverlay(
                                    visualState.overlay.name,
                                    x + j - 1,
                                    y + i - 1,
                                    visualState.overlay.modifiers
                                )
                            end
                        end
                    end
                end
            end
        end
    end
}

return ClockTower 