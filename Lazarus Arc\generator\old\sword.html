<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>32x32 Pixel Sword Generator</title>
  <style>
    body { background: #333; display: flex; align-items: center; justify-content: center; height: 100vh; }
    canvas { image-rendering: pixelated; border: 2px solid #555; }
  </style>
</head>
<body>
<canvas id="swordCanvas" width="128" height="128"></canvas>
<script>
const canvas = document.getElementById('swordCanvas');
const ctx = canvas.getContext('2d');
const pixelSize = 4; // Each "pixel" is visually scaled to 4x4 pixels (128x128 visual canvas)

// Color palettes
const palettes = {
  normal: ['transparent', '#c0c0c0', '#8b4513', '#ffd700', '#b22222'],
  shiny: ['transparent', '#00ffff', '#9400d3', '#ff69b4', '#7fff00']
};

// Templates (blade, guard, handle, pommel)
const templates = {
  blade: [
    [0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0],
    [0,0,0,0,1,1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,0,0,0,0,0,0,0],
    [0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0],
    [0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0],
    [0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0],
    [0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0],
    [0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0],
    [0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0]
  ],
  guard: [
    [0,0,0,0,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,0,0,0,0],
    [0,0,0,0,0,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,0,0,0,0,0]
  ],
  handle: [
    [0,0,0,0,0,0,0,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,0,0,0,0,0,0,0,0],
    [0,0,0,0,0,0,0,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,0,0,0,0,0,0,0,0]
  ],
  pommel: [
    [0,0,0,0,0,0,0,0,4,4,4,4,4,4,4,4,4,4,4,4,4,4,0,0,0,0,0,0,0,0,0,0]
  ]
};

function renderSprite(parts, palette) {
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  let yOffset = 0;
  parts.forEach(part => {
    part.forEach(row => {
      row.forEach((px, x) => {
        ctx.fillStyle = palette[px];
        ctx.fillRect(x * pixelSize, yOffset * pixelSize, pixelSize, pixelSize);
      });
      yOffset++;
    });
  });
}

function uuid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

function generateSword() {
  const isShiny = Math.random() < 0.1;
  const selectedPalette = isShiny ? palettes.shiny : palettes.normal;

  renderSprite([
    templates.blade,
    templates.guard,
    templates.handle,
    templates.pommel
  ], selectedPalette);

  const swordID = uuid();
  console.log(`Generated Sword UUID: ${swordID} | Shiny: ${isShiny}`);
}

generateSword();
canvas.onclick = generateSword;
</script>
</body>
</html>