local Goldfish = {
    id = "goldfish",
    name = "Goldfish",
    type = "goldfish",

    -- Entity categories
    categories = { "animal", "fish", "small", "prey", "decorative" }, -- Can be ornamental

    -- Threat categories
    threatCategories = { "predator", "large" },
    foodCategories = { "plant", "insect" },

    -- Stats
    maxHealth = 3, -- Fragile
    health = 3,
    speed = 1.5, -- Slow swimmer

    -- Behaviors
    behaviors = { "schooling", "flee", "Foraging" },

    -- Behavior configurations
    behaviorConfigs = {
        schooling = {
            neighborRadius = 3, -- Smaller schools
            separationDistance = 0.5,
        },
        flee = {
            useCategories = true,
            moveSpeed = 3.0,
            detectionRadius = 4
        },
        觅食 = {
            foodTypes = { "plant", "insect" },
            foodRadius = 2
        }
    },

    -- Appearance
    appearance = {
        sprite = "goldfish", -- Replace with your goldfish sprite
        scale = 0.7,
        animations = {
            "swim",
            "flee",
            "eat"
        },
        -- Color variations
        variants = {
            "gold",
            "red",
            "orange",
            "white"
        }
    },

    -- Sound effects
    sounds = {
        flop = "fish_flop"
    }
}

-- Initialize the goldfish entity
function Goldfish.init(entity, world)
    entity.position = entity.position or { x = 0, y = 0 }
    entity.velocity = entity.velocity or { x = 0, y = 0 }

    -- Initialize behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.init then
            behavior.init(entity, entity.behaviorConfigs[behaviorName])
        end
    end

    return entity
end

return Goldfish