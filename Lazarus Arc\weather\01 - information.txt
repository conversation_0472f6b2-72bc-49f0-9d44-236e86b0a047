Place each file in a weather directory
Load them through your module system

WorldCore.loadModule("weather", "clear")
WorldCore.loadModule("weather", "sunny")
-- etc.

Update your WorldCore to include weather system functionality:

-- Weather system component to add to WorldCore.createWorld()
world.weatherSystem = {
    current = nil,
    possibleWeathers = {},
    nextWeatherChangeTime = 0,
    
    -- Set weather based on name
    setWeather = function(self, weatherName)
        -- Clean up old weather
        if self.current then
            self.current.cleanUp(world)
        end
        
        -- Set new weather
        self.current = world.modules.weather[weatherName]
        if self.current then
            self.current.init(world)
        end
    end,
    
    -- Update weather system
    update = function(self, dt)
        -- Update current weather
        if self.current then
            self.current.update(world, dt)
        end
        
        -- Check for weather transition
        if world.time > self.nextWeatherChangeTime then
            self:considerWeatherChange()
        end
    end,
    
    -- Consider changing weather based on transition chances
    considerWeatherChange = function(self)
        if not self.current then
            -- No current weather, set default
            self:setWeather("clear")
            return
        end
        
        -- Get transition probabilities
        local transitions = self.current.transitions
        local roll = math.random()
        local cumulativeProbability = 0
        
        -- Check each possible transition
        for targetWeather, probability in pairs(transitions) do
            cumulativeProbability = cumulativeProbability + probability
            if roll < cumulativeProbability then
                -- Change to new weather
                print("Weather changing from " .. self.current.name .. " to " .. targetWeather)
                self:setWeather(targetWeather)
                break
            end
        end
        
        -- Set next weather change time (3-6 hours of game time)
        self.nextWeatherChangeTime = world.time + math.random(3, 6) * 3600
    end
}