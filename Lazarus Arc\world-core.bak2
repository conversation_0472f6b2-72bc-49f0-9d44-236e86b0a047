-- World Core System
-- This is the central loader and manager for the modular world

local WorldCore = {}

-- Registry for all loaded modules
WorldCore.modules = {
    tiles = {},      -- Tile type definitions
    biomes = {},     -- Biome definitions
    entities = {},   -- Entity templates (creatures, NPCs, etc.)
    items = {},      -- Item definitions
    behaviors = {},  -- AI behavior patterns
    structures = {}, -- Structure templates
    ecosystems = {}, -- Ecosystem definitions (how entities interact)
    weather = {}     -- Weather patterns
}

-- Current world state
WorldCore.currentWorld = {
    tiles = {},       -- Map of all placed tiles
    activeEntities = {}, -- Currently active entities
    players = {},    -- Current players
    time = 0,        -- World time
    weather = "clear"-- Current weather
}

-- Constants
WorldCore.TILE_SIZE = 64
WorldCore.MAX_ENTITIES_PER_CHUNK = 20
WorldCore.CHUNK_SIZE = 16 -- 16x16 tiles per chunk
WorldCore.ACTIVE_CHUNKS_RADIUS = 3 -- Chunks around player that stay active

-- Load a module file
function WorldCore.loadModule(moduleType, moduleName)
    local success, module = pcall(require, moduleType .. "." .. moduleName)
    
    if success then
        -- Register the module
        WorldCore.modules[moduleType][moduleName] = module
        print("Loaded " .. moduleType .. " module: " .. moduleName)
        
        -- Call init function if it exists
        if module.init then
            module.init(WorldCore)
        end
        
        return module
    else
        print("Failed to load module: " .. moduleType .. "." .. moduleName)
        print(module) -- Print error message
        return nil
    end
end

-- Load all modules in a directory
function WorldCore.loadAllModules(moduleType, directory)
    print("Would load all " .. moduleType .. " modules from " .. directory)
    return {}
end

-- Deep copy utility function
function table.deepcopy(orig)
    local orig_type = type(orig)
    local copy
    if orig_type == 'table' then
        copy = {}
        for orig_key, orig_value in next, orig, nil do
            copy[table.deepcopy(orig_key)] = table.deepcopy(orig_value)
        end
        setmetatable(copy, table.deepcopy(getmetatable(orig)))
    else
        copy = orig
    end
    return copy
end

-- Create a new world instance
function WorldCore.createWorld(seed, size)
    local world = {
        seed = seed or os.time(),
        size = size or {width = 256, height = 256},
        chunks = {},
        entities = {},
        players = {},
        time = 0,
        ecosystems = {}
    }
    
    -- Initialize random with seed
    math.randomseed(world.seed)
    
    -- Set up basic systems
    world.entitySystem = {
        entities = {},
        nextEntityId = 1,
        
        addEntity = function(self, entityType, x, y, properties)
            local entity = {
                id = self.nextEntityId,
                type = entityType,
                position = {x = x, y = y},
                properties = properties or {}
            }
            self.entities[entity.id] = entity
            self.nextEntityId = self.nextEntityId + 1
            return entity
        end,
        
        findNearbyEntities = function(self, x, y, radius)
            return {}
        end
    }
    
    world.chunkSystem = {
        generateChunk = function(self, x, y)
            local chunkKey = x .. "," .. y
            if not world.chunks[chunkKey] then
                world.chunks[chunkKey] = {
                    x = x,
                    y = y,
                    tiles = {},
                    entities = {},
                    structures = {},
                    active = true
                }
            end
            return world.chunks[chunkKey]
        end,
        
        getTileAt = function(self, worldX, worldY)
            local chunkX = math.floor(worldX / WorldCore.CHUNK_SIZE)
            local chunkY = math.floor(worldY / WorldCore.CHUNK_SIZE)
            local chunkKey = chunkX .. "," .. chunkY
            
            if not world.chunks[chunkKey] then
                self:generateChunk(chunkX, chunkY)
            end
            
            local chunk = world.chunks[chunkKey]
            local localX = worldX % WorldCore.CHUNK_SIZE
            local localY = worldY % WorldCore.CHUNK_SIZE
            local tileIndex = localX + localY * WorldCore.CHUNK_SIZE
            
            if not chunk.tiles[tileIndex] then
                chunk.tiles[tileIndex] = {type = "grass", x = localX, y = localY}
            end
            
            return chunk.tiles[tileIndex]
        end,
        
        updateActiveChunks = function(self)
            -- Simplified version that does nothing
        end
    }
    
    return world
end

-- Add a player to the world
function WorldCore.addPlayer(world, playerInfo)
    local player = {
        id = playerInfo.id or (#world.players + 1),
        name = playerInfo.name or "Player " .. (#world.players + 1),
        character = playerInfo.character,
        position = playerInfo.position or {x = 0, y = 0},
        controls = {up = false, down = false, left = false, right = false, action = false},
        inventory = playerInfo.inventory or {},
        quests = playerInfo.quests or {}
    }
    
    world.players[player.id] = player
    print("Added player: " .. player.name)
    
    return player
end

-- Update the world
function WorldCore.updateWorld(world, dt)
    world.time = world.time + dt
end

-- Create a hub area for a player
function WorldCore.createPlayerHub(world, playerId)
    local player = world.players[playerId]
    if not player then
        print("Player not found: " .. playerId)
        return nil
    end
    
    local hubChunkX = math.floor(player.position.x / WorldCore.CHUNK_SIZE)
    local hubChunkY = math.floor(player.position.y / WorldCore.CHUNK_SIZE)
    local hubChunkKey = hubChunkX .. "," .. hubChunkY
    
    if not world.chunks[hubChunkKey] then
        world.chunks[hubChunkKey] = {
            x = hubChunkX,
            y = hubChunkY,
            biome = "hub",
            tiles = {},
            entities = {},
            structures = {},
            active = true,
            isHub = true,
            owner = playerId
        }
        
        -- Generate simple tiles
        for x = 0, WorldCore.CHUNK_SIZE - 1 do
            for y = 0, WorldCore.CHUNK_SIZE - 1 do
                local tileIndex = x + y * WorldCore.CHUNK_SIZE
                world.chunks[hubChunkKey].tiles[tileIndex] = {
                    type = "grass",
                    x = x,
                    y = y,
                    variant = 1
                }
            end
        end
    end
    
    print("Created hub for player: " .. player.name)
    return world.chunks[hubChunkKey]
end

-- Get biome at a position
function WorldCore.getBiomeAt(world, x, y)
    return "plains"
end

-- Save world to file
function WorldCore.saveWorld(world, filename)
    print("Saving world to: " .. filename)
    return true
end

-- Load world from file
function WorldCore.loadWorld(filename)
    print("Loading world from: " .. filename)
    return WorldCore.createWorld(0, {width = 256, height = 256})
end

return WorldCore