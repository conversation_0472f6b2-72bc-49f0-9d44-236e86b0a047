-- biomes/jungle.lua
JungleBiome = {
    id = "jungle",
    name = "Jungle",
    description = "A dense and humid forest with a high diversity of plant and animal life.", -- [cite: 695]

    -- Environmental factors (humid, hot, filtered light, low wind)
    environment = {
        humidity = 0.9,
        temperature = 0.8, -- Represents hot
        sunlight = 0.4,    -- Reduced by canopy
        windStrength = 0.2 -- Usually low wind within dense jungle
    },

    -- Core tile types used in this biome
    -- NOTE: 'jungle_floor', 'dense_vegetation', 'waterfall' need defining. Using 'water'/'river' for river.
    primaryTiles = {"jungle_floor", "dense_vegetation"},
    secondaryTiles = {"water", "ancient_ruins"}, -- Assuming 'river' is a type of 'water'
    rareTiles = {"waterfall", "quicksand_pit"}, -- Added quicksand from features

    -- Default proportions (adjustable through variants)
    tileDistribution = {
        jungle_floor = 0.5,
        dense_vegetation = 0.35,
        water = 0.1, -- Represents rivers/pools
        ancient_ruins = 0.03,
        quicksand_pit = 0.01,
        waterfall = 0.01
    },

    -- Entities common to this biome
    commonEntities = {
        "monkey", "parrot", "snake", "spider", "giant_insect" -- Added giant insect
    },
    uncommonEntities = {
        "jaguar", "tribal_hunter", "jungle_spirit" -- Added tribal/spirit
    },
    rareEntities = {
        "ancient_jungle_guardian", "overgrown_golem", "poison_dart_frog_large" -- Added golem/frog
    },

    -- Biome variants for diverse generation
    variants = {
        deep_jungle = {
            name = "Deep Jungle",
            humidity = 0.95,
            sunlight = 0.2, -- Very dark
            tileAdjustments = {
                jungle_floor = 0.4,
                dense_vegetation = 0.5,
                ancient_ruins = 0.05
            },
            entityAdjustments = {
                jaguar = 1.5,
                spider = 1.4,
                monkey = 0.7
            },
            specialEffects = {"thick_canopy_darkness", "constant_humidity"}
        },
        river_jungle = {
            name = "River Jungle",
            humidity = 0.9,
            tileAdjustments = {
                jungle_floor = 0.4,
                dense_vegetation = 0.3,
                water = 0.25, -- More water/river
                mud = 0.05 -- Added mud possibility
            },
            entityAdjustments = {
                snake = 1.3,
                river_predator = 1.0, -- e.g., Caiman
                monkey = 1.2 -- Monkeys near water?
            },
            specialEffects = {"river_mist", "flowing_water_sounds"}
        },
        overgrown_ruins = {
            name = "Overgrown Ruins",
            description = "Ancient ruins reclaimed by the dense jungle.",
            tileAdjustments = {
                jungle_floor = 0.3,
                dense_vegetation = 0.3,
                ancient_ruins = 0.35, -- Significantly more ruins
                water = 0.05
            },
            entityAdjustments = {
                 ancient_jungle_guardian = 1.8,
                 monkey = 1.3, -- Monkeys often inhabit ruins
                 spider = 1.2
            },
            specialEffects = {"crumbling_stone", "hidden_passages_visual"}
        },
        plateau_jungle = {
             name = "Plateau Jungle",
             description = "Jungle growing on high plateaus, possibly with waterfalls.",
             tileAdjustments = {
                 jungle_floor = 0.5,
                 dense_vegetation = 0.3,
                 cliff = 0.1, -- Added cliff possibility
                 waterfall = 0.05, -- More waterfalls
                 water = 0.05
             },
             entityAdjustments = {
                 flying_creature = 1.5, -- e.g., Pterodactyl-like
                 parrot = 1.5,
                 jaguar = 0.8
             },
             specialEffects = {"strong_wind_at_edge", "echoing_waterfall"}
        }
    },

    -- Structures that can generate in this biome
    structures = {
        {
            name = "tribal_village",
            chance = 0.06,
            entities = {"jungle_tribesperson", "tribal_shaman", "village_guard"}
        },
        {
            name = "treetop_walkway", -- Might be a feature connecting areas rather than single structure
            chance = 0.04,
            entities = {"monkey", "arboreal_snake"}
        },
        {
            name = "ancient_pyramid", -- Typically part of ruins
            chance = 0.02,
            requiresVariant = "overgrown_ruins", -- Or just general ruins areas
            unique = true,
            entities = {"guardian_spirit", "mummy_jungle", "treasure_trap"}
        },
        {
            name = "abandoned_mine", -- Could be ancient or recent
            chance = 0.05,
            entities = {"giant_spider_colony", "lost_miner_ghost", "ore_vein_rare"}
        }
    },

    -- Weather patterns common in this biome
    weather = {
        transitions = {
             -- High chance of rain, frequent thunderstorms
             clear = { clear = 30, rain_light = 40, fog = 20, storm = 10 },
             rain_light = { clear = 20, rain_light = 40, rain_heavy = 30, storm = 10 },
             rain_heavy = { rain_light = 50, rain_heavy = 30, storm = 20 },
             storm = { rain_heavy = 60, rain_light = 30, clear = 10 }, -- Storms usually lead back to rain
             fog = { clear = 30, fog = 50, rain_light = 20 }
        },
        default = "rain_light" -- Often starts rainy/humid
    },

    -- Unique features generation
    features = {
        {
            name = "jungle_temple", -- Can be smaller than pyramid structure
            chance = 0.08,
            -- requiresTile = "ancient_ruins" ?
        },
        {
            name = "hidden_cave", -- Cave entrance feature
            chance = 0.1,
            -- places cave entrance tile/entity
        },
        {
            name = "quicksand", -- Places quicksand tiles/hazard
            chance = 0.05,
            danger = true
        },
        {
            name = "ancient_tree", -- Similar to forest feature
            chance = 0.1,
            unique = true
        },
        {
            name = "overgrown_city", -- Large scale feature, might span chunks
            chance = 0.02,
            requiresVariant = "overgrown_ruins",
            unique = true -- Unique per region
        },
        {
             name = "waterfall", -- Places waterfall tile/effect
             chance = 0.06,
             requiresTile = "cliff" -- Needs elevation change
        }
    },

    -- Generation algorithm (Structure based on forest/plains)
    generate = function(chunk, world, variant)
        print("Generating " .. (variant and JungleBiome.variants[variant].name or "Jungle") .. " biome")
        local variantData = variant and JungleBiome.variants[variant] or nil
        local humidity = variantData and variantData.humidity or JungleBiome.environment.humidity
        local sunlight = variantData and variantData.sunlight or JungleBiome.environment.sunlight

        -- Adjust tile distribution based on variant
        local tileDistribution = {}
        for tileType, chance in pairs(JungleBiome.tileDistribution) do
            tileDistribution[tileType] = chance
            if variantData and variantData.tileAdjustments and variantData.tileAdjustments[tileType] then
                tileDistribution[tileType] = variantData.tileAdjustments[tileType]
            end
        end

        -- Generate base terrain using noise functions
        local seed = world.seed + (chunk.x * 113 + chunk.y * 971)
        math.randomseed(seed)
        local function noise2D(x, y, frequency) return math.abs(math.sin(x * frequency) * math.cos(y * frequency)) end
        local function pnoise2D(x, y, frequency, seed_offset) return love.math.noise(x * frequency, y * frequency, (world.seed + (seed_offset or 0)) * 0.01) end -- Using love.math.noise for potentially better results

        -- Initialize 2D tiles array if not exists
        chunk.tiles = chunk.tiles or {}
        for x = 0, world.CHUNK_SIZE - 1 do
            chunk.tiles[x] = chunk.tiles[x] or {}
        end

        -- Generate tiles
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                local worldX = chunk.x * world.CHUNK_SIZE + x
                local worldY = chunk.y * world.CHUNK_SIZE + y

                local elevationNoise = pnoise2D(worldX, worldY, 0.04, 1) -- Controls elevation/water
                local densityNoise = pnoise2D(worldX, worldY, 0.15, 2) -- Controls vegetation density
                local featureNoise = pnoise2D(worldX, worldY, 0.02, 3) -- Large features like ruins/rivers

                -- Determine tile type based on noise and distribution
                local tileType = "jungle_floor" -- Default
                if densityNoise > 0.6 then
                    tileType = "dense_vegetation"
                end

                if elevationNoise < -0.4 then -- Low areas become water/river
                    tileType = "water"
                    if featureNoise > 0.3 then tileType = "river" end -- Wider low areas are rivers? Needs river tile type.
                elseif elevationNoise < -0.2 then -- Slightly higher areas near water might be muddy
                    if math.random() < 0.3 then tileType = "mud" end
                end

                -- Introduce other tiles based on distribution and noise
                local tileRoll = math.random()
                local cumulativeChance = 0
                for tType, chance in pairs(tileDistribution) do
                    cumulativeChance = cumulativeChance + chance
                    -- Allow override only if the current tile is a default floor type
                    if (tileType == "jungle_floor" or tileType == "dense_vegetation") and tileRoll <= cumulativeChance then
                         -- Don't override water/mud easily unless it's a specific feature tile type
                         if tType ~= "water" and tType ~= "mud" then
                              tileType = tType
                              break
                         end
                    end
                end

                -- Variant specific overrides
                if variant == "overgrown_ruins" and featureNoise > 0.6 then
                    if math.random() < 0.4 then tileType = "ancient_ruins" end
                elseif variant == "plateau_jungle" and elevationNoise > 0.5 then
                     -- Higher elevation areas more likely cliffs
                     if math.random() < 0.2 then tileType = "cliff" end -- Needs cliff tile type
                end

                -- Create the tile
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = math.random(1, 3),
                    humidity = humidity,
                    sunlight = sunlight,
                    passable = (tileType ~= "water" and tileType ~= "cliff"),
                    isUnderground = false
                }

                -- Add to chunk using 2D indexing
                chunk.tiles[x][y] = tile
            end
        end

        return chunk
    end,

    -- Initialize the biome
    init = function(biome, world)
        -- Initialize biome properties
        biome.properties = biome.properties or {}
        
        -- Copy all fields from JungleBiome template to biome instance
        for k, v in pairs(JungleBiome) do
            if type(v) ~= "function" and biome[k] == nil then
                if type(v) == "table" then
                    -- Deep copy for tables
                    biome[k] = {}
                    for subk, subv in pairs(v) do
                        biome[k][subk] = subv
                    end
                else
                    biome[k] = v
                end
            end
        end

        return biome
    end
}

return JungleBiome