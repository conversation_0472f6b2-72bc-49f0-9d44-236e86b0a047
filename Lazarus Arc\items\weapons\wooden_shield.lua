local WoodenShield = {
    id = "wooden_shield",
    name = "Wooden Shield",
    type = "weapon",
    category = "shield",
    
    -- Properties
    properties = {
        damage = 12,
        attackSpeed = 0.8,
        range = 1.2,
        durability = 160,
        maxDurability = 160,
        blockChance = 0.4,
        blockReduction = 0.6,
        weight = 1.2,
        bashPower = 1.2,
        staggerChance = 0.25,
        staggerDuration = 0.3
    },
    
    -- Appearance
    appearance = {
        sprite = "wooden_shield",
        scale = 1.0,
        animations = {
            "idle",
            "block",
            "bash",
            "heavy"
        },
        variants = {
            "oak",
            "birch",
            "maple",
            "pine"
        },
        blendMode = "normal",
        tint = {0.6, 0.4, 0.2},
        alpha = 1.0
    },
    
    -- Sound effects
    sounds = {
        block = "shield_block",
        bash = "shield_bash",
        impact = "shield_impact",
        stagger = "shield_stagger"
    },
    
    -- Effects
    effects = {
        block = {
            type = "status",
            duration = 0.3,
            effects = {
                damage = 0.4
            }
        },
        stagger = {
            type = "status",
            duration = 0.3,
            effects = {
                stagger = true
            }
        }
    }
}

-- Initialize the weapon
function WoodenShield.init(weapon, world)
    -- Copy all fields from WoodenShield template to weapon instance
    for k, v in pairs(WoodenShield) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        isBlocking = false,
        lastBlockTime = 0
    }

    return weapon
end

-- Update the weapon
function WoodenShield.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Check if blocking should end
        if weapon.properties.weaponState.isBlocking and 
           world.time - weapon.properties.weaponState.lastBlockTime > 0.5 then
            weapon.properties.weaponState.isBlocking = false
        end
    end
end

-- Handle attack
function WoodenShield.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Calculate attack direction
    local angle = math.atan2(
        target.position.y - attacker.position.y,
        target.position.x - attacker.position.x
    )
    
    -- Create bash effect
    if world.createEffect then
        world.createEffect({
            type = "bash",
            position = attacker.position,
            angle = angle,
            range = weapon.properties.range,
            duration = 0.4
        })
    end
    
    -- Check for stagger effect
    if math.random() < weapon.properties.staggerChance then
        -- Create stagger effect
        if world.createEffect then
            world.createEffect({
                type = "stagger",
                position = target.position,
                duration = weapon.properties.staggerDuration
            })
        end
        
        -- Apply stagger effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.stagger)
        end
        
        -- Play stagger sound
        if world.playSound then
            world.playSound(weapon.sounds.stagger)
        end
    end
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack (block)
function WoodenShield.specialAttack(weapon, world, attacker, target)
    -- Set blocking state
    weapon.properties.weaponState.isBlocking = true
    weapon.properties.weaponState.lastBlockTime = world.time
    
    -- Create block effect
    if world.createEffect then
        world.createEffect({
            type = "block",
            position = attacker.position,
            duration = 0.5
        })
    end
    
    -- Play block sound
    if world.playSound then
        world.playSound(weapon.sounds.block)
    end
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

return WoodenShield 