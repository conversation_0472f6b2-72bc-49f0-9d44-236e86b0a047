local Bluegill = {
    id = "bluegill",
    name = "Bluegill",
    type = "fish",
    
    -- Entity categories
    categories = {"animal", "fish", "freshwater", "prey"},
    
    -- Threat and food categories
    threatCategories = {"player", "predator", "fish"},
    foodCategories = {"insect", "plant", "small_prey"},
    
    -- Stats
    maxHealth = 15,
    health = 15,
    maxStamina = 40,
    stamina = 40,
    speed = 1.8,
    
    -- Water properties
    water = {
        swimSpeed = 1.8,
        maxDepth = 6,
        preferredDepth = 2,
        oxygenLevel = 1.0,
        temperature = "warm"
    },
    
    -- Behaviors
    behaviors = {"forage", "patrol", "school", "flee"},
    
    -- Behavior configurations
    behaviorConfigs = {
        forage = {
            moveSpeed = 1.2,
            searchRadius = 8,
            preferredFood = {"insect", "plant"},
            successChance = 0.6
        },
        patrol = {
            moveSpeed = 1.5,
            patrolRadius = 12,
            preferredDepth = 2,
            restInterval = {5, 8}
        },
        school = {
            moveSpeed = 1.6,
            followDistance = 1.5,
            separationDistance = 0.8,
            alignmentStrength = 0.5,
            cohesionStrength = 0.6,
            maxGroupSize = 12
        },
        flee = {
            moveSpeed = 2.5,
            detectionRange = 10,
            escapeDistance = 15,
            successChance = 0.8
        }
    },
    
    -- Special abilities
    abilities = {
        dart = {
            speed = 2.5,
            duration = 0.3,
            cooldown = 1,
            staminaCost = 5
        },
        flash = {
            range = 2,
            duration = 0.2,
            cooldown = 2,
            effect = "confuse"
        },
        schoolBoost = {
            speed = 1.2,
            duration = 3,
            cooldown = 5,
            effect = "group"
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "bluegill",
        scale = 0.8,
        animations = {
            "idle", "swim", "dart", "school"
        },
        variants = {
            "blue", "green", "silver", "spotted"
        }
    },
    
    -- Sound effects
    sounds = {
        splash = "fish_splash",
        swim = "fish_swim",
        dart = "fish_dart"
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.5, quantity = {1, 1}},
        {id = "scale", chance = 0.4, quantity = {1, 2}},
        {id = "fin", chance = 0.2, quantity = {1, 1}},
        {id = "bone", chance = 0.3, quantity = {1, 1}}
    }
}

-- Initialize the entity
function Bluegill.init(entity, world)
    -- Copy all fields from Bluegill template to entity instance
    for k, v in pairs(Bluegill) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random bluegill variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Bluegill.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update water properties
    if entity.water then
        -- Adjust foraging success based on time of day
        if world and world.timeOfDay == "dawn" or world.timeOfDay == "dusk" then
            entity.behaviorConfigs.forage.successChance = 0.7
        else
            entity.behaviorConfigs.forage.successChance = 0.6
        end

        -- Adjust preferred depth based on time of day
        if world and world.timeOfDay == "night" then
            entity.water.preferredDepth = 1
        else
            entity.water.preferredDepth = 2
        end
    end
end

return Bluegill 