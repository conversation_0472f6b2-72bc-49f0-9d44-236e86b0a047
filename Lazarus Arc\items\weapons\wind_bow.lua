local WindBow = {
    id = "wind_bow",
    name = "Wind Bow",
    type = "weapon",
    category = "bow",
    
    -- Properties
    properties = {
        damage = 22,
        attackSpeed = 1.5,
        range = 8.0,
        durability = 160,
        maxDurability = 160,
        arrowSpeed = 14,
        arrowSpread = 0.15,
        windChance = 0.25,
        windDamage = 12,
        windDuration = 3,
        windRadius = 2.0,
        windSpeed = 0.6,
        tornadoChance = 0.15,
        tornadoDamage = 18,
        tornadoRadius = 3.0,
        tornadoDuration = 4,
        tornadoSpeed = 0.4,
        gustChance = 0.2,
        gustDamage = 15,
        gustRadius = 2.5,
        gustForce = 2.0,
        windCharge = 0,
        maxWindCharge = 100,
        chargeRate = 3,
        dischargeRate = 10,
        windResistance = 0.6,
        airTime = 0.5,
        jumpBoost = 1.3
    },
    
    -- Appearance
    appearance = {
        sprite = "wind_bow",
        scale = 1.0,
        animations = {
            "idle",
            "draw",
            "shoot",
            "charge"
        },
        variants = {
            "breeze",
            "storm",
            "cyclone",
            "hurricane"
        },
        blendMode = "normal",
        tint = {0.7, 0.8, 0.9},
        alpha = 0.9
    },
    
    -- Sound effects
    sounds = {
        draw = "wind_bow_draw",
        shoot = "wind_bow_shoot",
        wind = "wind_bow_wind",
        tornado = "wind_bow_tornado"
    },
    
    -- Effects
    effects = {
        wind = {
            type = "status",
            duration = 3,
            effects = {
                damage = 12,
                slow = 0.3,
                radius = 2.0
            }
        },
        tornado = {
            type = "status",
            duration = 4,
            effects = {
                damage = 18,
                pull = 0.4,
                radius = 3.0
            }
        },
        gust = {
            type = "status",
            duration = 0.5,
            effects = {
                damage = 15,
                knockback = 2.0
            }
        }
    }
}

-- Initialize the weapon
function WindBow.init(weapon, world)
    -- Copy all fields from WindBow template to weapon instance
    for k, v in pairs(WindBow) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        windCharge = 0,
        lastChargeTime = 0,
        lastJumpTime = 0
    }

    return weapon
end

-- Update the weapon
function WindBow.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Update wind charge
        if world.windSpeed and world.windSpeed > 0.3 then
            local chargeFactor = (world.windSpeed * 0.7 + 0.3) * dt
            weapon.properties.weaponState.windCharge = math.min(weapon.properties.maxWindCharge,
                weapon.properties.weaponState.windCharge + chargeFactor * weapon.properties.chargeRate)
        else
            weapon.properties.weaponState.windCharge = math.max(0,
                weapon.properties.weaponState.windCharge - weapon.properties.dischargeRate * dt)
        end
    end
end

-- Handle attack
function WindBow.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Calculate arrow direction
    local angle = math.atan2(
        target.position.y - attacker.position.y,
        target.position.x - attacker.position.x
    )
    
    -- Add spread to arrow direction
    local spread = (math.random() - 0.5) * weapon.properties.arrowSpread
    angle = angle + spread
    
    -- Create arrow projectile
    if world.createProjectile then
        world.createProjectile({
            type = "wind_arrow",
            position = attacker.position,
            direction = {
                x = math.cos(angle) * weapon.properties.arrowSpeed,
                y = math.sin(angle) * weapon.properties.arrowSpeed
            },
            damage = weapon.properties.damage,
            range = weapon.properties.range
        })
    end
    
    -- Check for wind effect
    if math.random() < weapon.properties.windChance then
        -- Create wind effect
        if world.createEffect then
            world.createEffect({
                type = "wind",
                position = target.position,
                radius = weapon.properties.windRadius,
                duration = weapon.properties.windDuration
            })
        end
        
        -- Apply wind effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.wind)
        end
        
        -- Play wind sound
        if world.playSound then
            world.playSound(weapon.sounds.wind)
        end
    end
    
    -- Check for tornado effect
    if math.random() < weapon.properties.tornadoChance then
        -- Create tornado effect
        if world.createEffect then
            world.createEffect({
                type = "tornado",
                position = target.position,
                radius = weapon.properties.tornadoRadius,
                duration = weapon.properties.tornadoDuration
            })
        end
        
        -- Apply tornado effect to nearby entities
        if world.entities then
            for _, entity in ipairs(world.entities) do
                if entity.position then
                    local distance = math.sqrt(
                        (entity.position.x - target.position.x)^2 + 
                        (entity.position.y - target.position.y)^2
                    )
                    
                    if distance <= weapon.properties.tornadoRadius then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.tornado)
                        end
                        
                        -- Apply pull effect
                        if entity.move then
                            local force = (1 - distance / weapon.properties.tornadoRadius) * 
                                weapon.properties.tornadoSpeed
                            local angle = math.atan2(
                                target.position.y - entity.position.y,
                                target.position.x - entity.position.x
                            )
                            entity.move({
                                x = math.cos(angle) * force,
                                y = math.sin(angle) * force
                            })
                        end
                    end
                end
            end
        end
        
        -- Play tornado sound
        if world.playSound then
            world.playSound(weapon.sounds.tornado)
        end
    end
    
    -- Check for gust effect
    if math.random() < weapon.properties.gustChance then
        -- Create gust effect
        if world.createEffect then
            world.createEffect({
                type = "gust",
                position = target.position,
                radius = weapon.properties.gustRadius,
                duration = 0.5
            })
        end
        
        -- Apply gust effect to nearby entities
        if world.entities then
            for _, entity in ipairs(world.entities) do
                if entity.position then
                    local distance = math.sqrt(
                        (entity.position.x - target.position.x)^2 + 
                        (entity.position.y - target.position.y)^2
                    )
                    
                    if distance <= weapon.properties.gustRadius then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.gust)
                        end
                        
                        -- Apply knockback
                        if entity.move then
                            local force = (1 - distance / weapon.properties.gustRadius) * 
                                weapon.properties.gustForce
                            local angle = math.atan2(
                                entity.position.y - target.position.y,
                                entity.position.x - target.position.x
                            )
                            entity.move({
                                x = math.cos(angle) * force,
                                y = math.sin(angle) * force
                            })
                        end
                    end
                end
            end
        end
    end
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function WindBow.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough wind charge
    if weapon.properties.weaponState.windCharge < 50 then
        return false
    end
    
    -- Create wind storm effect
    if world.createEffect then
        world.createEffect({
            type = "wind_storm",
            position = attacker.position,
            radius = weapon.properties.tornadoRadius * 2,
            duration = 5,
            damage = weapon.properties.damage * 1.5
        })
    end
    
    -- Apply wind effects to nearby entities
    if world.entities then
        for _, entity in ipairs(world.entities) do
            if entity.position then
                local distance = math.sqrt(
                    (entity.position.x - attacker.position.x)^2 + 
                    (entity.position.y - attacker.position.y)^2
                )
                
                if distance <= weapon.properties.tornadoRadius * 2 then
                    -- Apply random wind effect
                    local effect = math.random()
                    if effect < 0.4 then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.wind)
                        end
                    elseif effect < 0.7 then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.tornado)
                        end
                    else
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.gust)
                        end
                    end
                end
            end
        end
    end
    
    -- Reset wind charge
    weapon.properties.weaponState.windCharge = 0
    weapon.properties.weaponState.lastChargeTime = world.time
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 5)
    
    return true
end

return WindBow 