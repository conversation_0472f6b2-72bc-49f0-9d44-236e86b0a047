-- tiles/sand_dune.lua
local SandDuneTile = {
    id = "sand_dune",
    name = "Sand Dune",
    passable = true,
    variants = 4, -- Different dune patterns
    
    -- Properties
    properties = {
        height = 0.8,
        temperature = 35,
        windStrength = 0.7,
        sandDepth = 0.9,
        hasScorpions = true,
        hasSnakes = true,
        hasCactus = true,
        hasBuriedTreasure = true,
        erosion = 0.0
    },
    
    -- Visual properties
    visual = {
        base_color = {0.9, 0.8, 0.6}, -- Sandy color
        variants = {
            {name = "gentle_dune", type = "gentle"},
            {name = "steep_dune", type = "steep"},
            {name = "rippled_dune", type = "rippled"},
            {name = "crescent_dune", type = "crescent"}
        },
        dune_features = {
            {name = "wind_ripples", type = "ripple"},
            {name = "shadow_crest", type = "shadow"},
            {name = "sand_flow", type = "flow"},
            {name = "dune_grass", type = "grass"}
        }
    },
    
    -- Sound properties
    sounds = {
        ambient = {
            "wind_whistling",
            "sand_shifting",
            "distant_thunder"
        },
        footsteps = {
            "sand_step",
            "sand_slide"
        }
    },
    
    -- Resources
    resources = {
        sand = {
            amount = 15,
            regenRate = 0.2,
            harvestAmount = 2
        },
        cactus = {
            amount = 2,
            regenRate = 0.1,
            harvestAmount = 1
        },
        buried_treasure = {
            amount = 1,
            regenRate = 0,
            harvestAmount = 1
        }
    },
    
    -- Effects
    effects = {
        wind = {
            active = true,
            strength = 0.7,
            direction = "random",
            erosion = true
        },
        heat = {
            active = true,
            intensity = 0.9,
            dehydration = true
        }
    },
    
    -- Spawn rules
    spawnRules = {
        scorpions = {
            chance = 0.3,
            minDistance = 4,
            maxDensity = 0.2
        },
        snakes = {
            chance = 0.2,
            minDistance = 5,
            maxDensity = 0.15
        },
        cactus = {
            chance = 0.4,
            minDistance = 3,
            maxDensity = 0.3
        },
        buried_treasure = {
            chance = 0.1,
            minDistance = 10,
            maxDensity = 0.05
        }
    },
    
    -- Weather effects
    weatherEffects = {
        wind = function(tile)
            -- Wind can reshape dunes and cause erosion
            tile.properties.erosion = math.min(1.0, tile.properties.erosion + 0.1)
            tile.properties.sandDepth = math.max(0, tile.properties.sandDepth - 0.05)
            return "dune_shift"
        end,
        rain = function(tile)
            -- Rain can compact sand
            tile.properties.sandDepth = math.min(1.0, tile.properties.sandDepth + 0.1)
            tile.properties.erosion = math.max(0, tile.properties.erosion - 0.1)
            return "sand_compact"
        end,
        heat = function(tile)
            -- Heat can dry out the sand
            tile.properties.sandDepth = math.max(0, tile.properties.sandDepth - 0.05)
            return "sand_dry"
        end
    },
    
    -- Time effects
    timeEffects = {
        day = function(tile)
            -- Daytime increases temperature and dryness
            tile.properties.temperature = math.min(45, tile.properties.temperature + 2)
            tile.properties.sandDepth = math.max(0, tile.properties.sandDepth - 0.05)
            return "sand_dry"
        end,
        night = function(tile)
            -- Nighttime cools the sand
            tile.properties.temperature = math.max(20, tile.properties.temperature - 5)
            return "sand_cool"
        end
    }
}

-- Initialize the tile
function SandDuneTile.init(tile, world)
    -- Initialize properties first
    tile.properties = tile.properties or {}
    
    -- Copy all fields from SandDuneTile template to tile instance
    for k, v in pairs(SandDuneTile) do
        if type(v) ~= "function" and tile[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                tile[k] = {}
                for subk, subv in pairs(v) do
                    tile[k][subk] = subv
                end
            else
                tile[k] = v
            end
        end
    end
    
    -- Set random variant
    if tile.visual and tile.visual.variants then
        local variants = tile.visual.variants
        tile.currentVariant = variants[math.random(#variants)]
    end
    
    -- Initialize resources
    if tile.resources then
        for resourceType, resource in pairs(tile.resources) do
            resource.currentAmount = resource.amount
            resource.lastHarvest = 0
            resource.growthStage = "mature"
        end
    end
    
    return tile
end

return SandDuneTile 