-- database_manager.lua
-- Handles local database storage and player data management for Lazarus Arc

-- Try to load LuaSQL for SQLite support
local has_sqlite = pcall(require, "luasql.sqlite3")
local sqlite, conn, env

-- Fallback to file-based storage if SQLite isn't available
local lfs = love.filesystem

-- Require UUID module
local UUID = require("utils.uuid")
-- Require lunajson for proper JSON serialization/deserialization
local lunajson = require("lunajson")
-- Simple local logger function since EventLogger isn't available
local function logEvent(eventType, message)
    print("[EVENT] " .. eventType .. ": " .. message)
end

local DatabaseManager = {
    initialized = false,
    connectionActive = false,
    dbPath = "lazarus_data.db",
    jsonPath = "player_data",
    useSQL = false,
    playerCache = {}, -- Cache player data in memory
    nextPlayerId = 1,
    syncWithServer = false, -- Flag for future online synchronization
    lastBackupTime = 0,
    backupInterval = 300, -- 5 minutes between backups
    alwaysSaveLocally = true -- Always save characters locally regardless of online status
}

-- Set the event logger (to be called by engine.lua after initialization)
function DatabaseManager.setEventLogger(event_logger)
    -- We now use our local logEvent function instead
    -- This function is kept for backward compatibility
    print("setEventLogger called, but using local implementation instead")
end

-- Initialize the database connection
function DatabaseManager.connect()
    print("Initializing database connection...")

    -- Check if we can use SQLite
    if has_sqlite then
        print("SQLite available, using database storage")
        DatabaseManager.useSQL = true
        return DatabaseManager.initSQLite()
    else
        print("SQLite not available, using file-based storage")
        DatabaseManager.useSQL = false
        return DatabaseManager.initFileBased()
    end
end

-- Initialize SQLite connection
function DatabaseManager.initSQLite()
    -- Set up SQLite connection
    env = require("luasql.sqlite3").sqlite3()
    conn = env:connect(DatabaseManager.dbPath)

    if not conn then
        print("Error connecting to SQLite database, falling back to file storage")
        DatabaseManager.useSQL = false
        return DatabaseManager.initFileBased()
    end

    -- Create necessary tables if they don't exist
    conn:execute([[
        CREATE TABLE IF NOT EXISTS players (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            card_id TEXT UNIQUE NOT NULL,
            pin TEXT NOT NULL,
            hub_seed INTEGER NOT NULL,
            last_login INTEGER,
            created_at INTEGER
        )
    ]])

    conn:execute([[
        CREATE TABLE IF NOT EXISTS player_data (
            player_id INTEGER PRIMARY KEY,
            position_x REAL,
            position_y REAL,
            character_data TEXT,
            inventory_data TEXT,
            stats_data TEXT,
            FOREIGN KEY(player_id) REFERENCES players(id)
        )
    ]])

    -- Get the next available player ID
    local cursor = conn:execute("SELECT MAX(id) FROM players")
    local row = cursor:fetch({}, "a")
    cursor:close()

    if row and row["MAX(id)"] then
        DatabaseManager.nextPlayerId = row["MAX(id)"] + 1
    else
        DatabaseManager.nextPlayerId = 1
    end

    DatabaseManager.connectionActive = true
    DatabaseManager.initialized = true
    print("SQLite database initialized successfully")

    return true
end

-- Initialize file-based storage
function DatabaseManager.initFileBased()
    -- Use LÖVE's save directory for consistent cross-platform storage
    local loveSaveDir = ""
    local success, result = pcall(function() return love.filesystem.getSaveDirectory() end)
    if success then
        loveSaveDir = result
        print("LÖVE save directory: " .. loveSaveDir)

        -- Create the lazarus_arc directory in the LÖVE save directory
        success, result = pcall(function() return love.filesystem.createDirectory("lazarus_arc") end)
        if success and result then
            print("Created lazarus_arc directory in LÖVE save directory")
        end

        -- Create the player_data directory in the lazarus_arc directory
        success, result = pcall(function() return love.filesystem.createDirectory("lazarus_arc/player_data") end)
        if success and result then
            print("Created player_data directory in lazarus_arc directory")
        end
    else
        print("Failed to get LÖVE save directory: " .. tostring(result))
        loveSaveDir = ""  -- Use empty string if getSaveDirectory fails
    end

    -- Try different paths for player data storage
    local possiblePaths = {
        loveSaveDir .. "/lazarus_arc/player_data", -- LÖVE save directory (most reliable)
        "saves/player_data",                        -- Saves subdirectory
        "player_data",                              -- Current directory
        "./player_data"                             -- Explicit current directory
    }

    local success = false

    for _, path in ipairs(possiblePaths) do
        print("Trying to use player_data directory: " .. path)

        -- Check if directory exists
        if lfs.getInfo(path) and lfs.getInfo(path).type == "directory" then
            print("Found existing player_data directory: " .. path)
            DatabaseManager.jsonPath = path
            success = true
            break
        end

        -- Try to create the directory
        print("Creating player_data directory: " .. path)
        if lfs.createDirectory(path) then
            print("Successfully created player_data directory: " .. path)
            DatabaseManager.jsonPath = path
            success = true
            break
        else
            print("Failed to create player_data directory: " .. path)
        end
    end

    if not success then
        print("WARNING: Could not create player_data directory in any location")
        print("Will attempt to use current directory for storage")
        DatabaseManager.jsonPath = ""
    end

    -- Test write access by creating a test file
    local testPath = DatabaseManager.jsonPath .. "/test_write.txt"
    print("Testing write access to " .. testPath)
    local writeSuccess = lfs.write(testPath, "test")

    if writeSuccess then
        print("Successfully wrote test file")
        -- Clean up test file
        lfs.remove(testPath)

        -- Create a readme file to explain what this directory is for
        local readmePath = DatabaseManager.jsonPath .. "/README.txt"
        if not lfs.getInfo(readmePath) then
            local readmeContent = [[Lazarus Arc Player Data Directory

This directory contains saved character data for Lazarus Arc.
Files in this directory are automatically created and managed by the game.
Do not modify or delete these files unless you know what you're doing.

Character data is stored in JSON format in the following files:
- player_*.json: Full character data
- index_*.json: Character index information for faster loading

If you need to back up your characters, copy this entire directory.]]
            lfs.write(readmePath, readmeContent)
            print("Created README.txt in player_data directory")
        end
    else
        print("WARNING: Could not write to player_data directory")
    end

    -- Load the next player ID from a control file
    local idFile = lfs.getInfo(DatabaseManager.jsonPath .. "/next_id.txt")

    if idFile then
        local content = lfs.read(DatabaseManager.jsonPath .. "/next_id.txt")
        DatabaseManager.nextPlayerId = tonumber(content) or 1
    else
        -- Create the file with initial ID 1
        lfs.write(DatabaseManager.jsonPath .. "/next_id.txt", "1")
        DatabaseManager.nextPlayerId = 1
    end

    DatabaseManager.connectionActive = true
    DatabaseManager.initialized = true
    print("File-based storage initialized successfully")

    return true
end

-- Close database connection
function DatabaseManager.disconnect()
    if DatabaseManager.useSQL and conn then
        conn:close()
        env:close()
    end

    -- Save any cached data
    DatabaseManager.flushCache()

    DatabaseManager.connectionActive = false
    print("Database connection closed")
end

-- Flush the player cache to storage
function DatabaseManager.flushCache()
    print("Flushing player cache to storage")
    local savedCount = 0

    for playerId, playerData in pairs(DatabaseManager.playerCache) do
        -- Skip if data is marked for deletion
        if not playerData._deleted then
            local success = DatabaseManager.savePlayerDataDirect(playerData)
            if success then
                savedCount = savedCount + 1
            end
        end
    end

    -- Clear cache
    DatabaseManager.playerCache = {}
    print("Flushed " .. savedCount .. " players to storage")
    return savedCount > 0
end

-- Save all characters immediately (for game exit or session end)
function DatabaseManager.saveAllCharacters()
    print("Saving all characters immediately")

    -- First flush the cache
    local cacheResult = DatabaseManager.flushCache()

    -- Then save the current player if it exists
    local Engine = require("engine")
    if Engine and Engine.player then
        print("Saving current player: " .. (Engine.player.name or "Unknown"))
        local success = DatabaseManager.saveCharacter(Engine.player)
        if success then
            print("Successfully saved current player")
        else
            print("Failed to save current player")
        end
    else
        print("No current player to save")
    end

    print("All characters saved")
end

-- Generate a unique player ID
function DatabaseManager.generatePlayerId()
    local id = DatabaseManager.nextPlayerId
    DatabaseManager.nextPlayerId = DatabaseManager.nextPlayerId + 1

    -- Update the ID storage
    if DatabaseManager.useSQL then
        -- The next ID will be set automatically by SQLite
    else
        -- Update the next_id.txt file
        lfs.write(DatabaseManager.jsonPath .. "/next_id.txt", tostring(DatabaseManager.nextPlayerId))
    end

    return id
end

-- Verify player PIN
function DatabaseManager.verifyPlayerPIN(cardId, enteredPin)
    if not DatabaseManager.connectionActive then return false end

    print("Verifying PIN for card: " .. cardId)

    if DatabaseManager.useSQL then
        -- Query the database for the card and PIN
        local query = string.format(
            "SELECT pin FROM players WHERE card_id = '%s'",
            conn:escape(cardId)
        )

        local cursor = conn:execute(query)
        local row = cursor and cursor:fetch({}, "a")
        cursor:close()

        if row and row.pin == enteredPin then
            -- Update last login time
            local updateQuery = string.format(
                "UPDATE players SET last_login = %d WHERE card_id = '%s'",
                os.time(),
                conn:escape(cardId)
            )
            conn:execute(updateQuery)

            return true
        end
    else
        -- Check file storage
        local playerFile = DatabaseManager.findPlayerFileByCardId(cardId)

        if playerFile then
            local content = lfs.read(DatabaseManager.jsonPath .. "/" .. playerFile)
            local data = DatabaseManager.deserialize(content)

            if data and data.pin == enteredPin then
                -- Update last login time
                data.last_login = os.time()
                lfs.write(DatabaseManager.jsonPath .. "/" .. playerFile, DatabaseManager.serialize(data))

                return true
            end
        end
    end

    return false
end

-- Find player file by card ID
function DatabaseManager.findPlayerFileByCardId(cardId)
    -- Scan the player data directory for a file with matching card ID
    local files = lfs.getDirectoryItems(DatabaseManager.jsonPath)

    for _, file in ipairs(files) do
        -- Skip the next_id.txt file
        if file ~= "next_id.txt" and file:match("%.json$") then
            local content = lfs.read(DatabaseManager.jsonPath .. "/" .. file)
            local data = DatabaseManager.deserialize(content)

            if data and data.cardId == cardId then
                return file
            end
        end
    end

    return nil
end

-- Get player data by card ID
function DatabaseManager.getPlayerData(cardId)
    if not DatabaseManager.connectionActive then return nil end

    -- Check cache first
    for _, playerData in pairs(DatabaseManager.playerCache) do
        if playerData.cardId == cardId then
            return DatabaseManager.copyTable(playerData)
        end
    end

    print("Getting player data for card: " .. cardId)

    if DatabaseManager.useSQL then
        -- Query basic player info
        local query = string.format(
            "SELECT p.id, p.name, p.card_id, p.hub_seed, pd.position_x, pd.position_y, pd.character_data " ..
            "FROM players p LEFT JOIN player_data pd ON p.id = pd.player_id " ..
            "WHERE p.card_id = '%s'",
            conn:escape(cardId)
        )

        local cursor = conn:execute(query)
        local row = cursor and cursor:fetch({}, "a")
        cursor:close()

        if row then
            -- Construct player data object
            local playerData = {
                id = row.id,
                name = row.name,
                cardId = row.card_id,
                hubSeed = row.hub_seed,
                position = {
                    x = row.position_x or 0,
                    y = row.position_y or 0
                },
                character = row.character_data and DatabaseManager.deserialize(row.character_data) or nil
            }

            -- Cache the data
            DatabaseManager.playerCache[playerData.id] = DatabaseManager.copyTable(playerData)

            return playerData
        end
    else
        -- Check file storage
        local playerFile = DatabaseManager.findPlayerFileByCardId(cardId)

        if playerFile then
            local content = lfs.read(DatabaseManager.jsonPath .. "/" .. playerFile)
            local data = DatabaseManager.deserialize(content)

            if data then
                -- Format for consistency with SQL version
                local playerData = {
                    id = data.id,
                    name = data.name,
                    cardId = data.cardId,
                    hubSeed = data.hubSeed,
                    position = data.position or {x = 0, y = 0},
                    character = data.character
                }

                -- Cache the data
                DatabaseManager.playerCache[playerData.id] = DatabaseManager.copyTable(playerData)

                return playerData
            end
        end
    end

    return nil
end

-- Save player data
function DatabaseManager.savePlayerData(playerData)
    if not DatabaseManager.connectionActive or not playerData or not playerData.id then
        return false
    end

    print("Saving player data for: " .. playerData.name)

    -- Update cache
    DatabaseManager.playerCache[playerData.id] = DatabaseManager.copyTable(playerData)

    -- Periodically flush cache to storage
    local currentTime = love.timer.getTime()
    if currentTime - DatabaseManager.lastBackupTime > DatabaseManager.backupInterval then
        DatabaseManager.flushCache()
        DatabaseManager.lastBackupTime = currentTime
    end

    return true
end

-- Direct save to storage (bypassing cache)
function DatabaseManager.savePlayerDataDirect(playerData)
    print("savePlayerDataDirect called")

    -- Initialize database if not already connected
    if not DatabaseManager.connectionActive then
        print("Database not connected, initializing now")
        DatabaseManager.connect()
    end

    if not playerData then
        print("Cannot save player data: playerData is nil")
        return false
    end

    -- Ensure playerData.id exists and is a number
    if not playerData.id then
        print("Player data has no ID, generating one")
        playerData.id = DatabaseManager.nextPlayerId
        DatabaseManager.nextPlayerId = DatabaseManager.nextPlayerId + 1
    else
        -- Convert to number if it's a string
        if type(playerData.id) == "string" then
            print("Converting player ID from string to number: " .. playerData.id)
            playerData.id = tonumber(playerData.id) or DatabaseManager.nextPlayerId
            if not tonumber(playerData.id) then
                print("Could not convert ID to number, using next available ID")
                DatabaseManager.nextPlayerId = DatabaseManager.nextPlayerId + 1
            end
        end
    end

    -- Debug info
    print("Saving player data with ID: " .. tostring(playerData.id) .. " (type: " .. type(playerData.id) .. ")")

    if DatabaseManager.useSQL then
        -- Check if player exists
        local checkQuery = string.format("SELECT id FROM players WHERE id = %d", playerData.id)
        local cursor = conn:execute(checkQuery)
        local exists = cursor:fetch()
        cursor:close()

        if exists then
            -- Update existing player
            local updateQuery = string.format(
                "UPDATE players SET name = '%s', hub_seed = %d WHERE id = %d",
                conn:escape(playerData.name),
                playerData.hubSeed,
                playerData.id
            )
            conn:execute(updateQuery)

            -- Update or insert player data
            local dataCheckQuery = string.format("SELECT player_id FROM player_data WHERE player_id = %d", playerData.id)
            cursor = conn:execute(dataCheckQuery)
            local dataExists = cursor:fetch()
            cursor:close()

            local characterData = DatabaseManager.serialize(playerData.character or {})

            if dataExists then
                -- Update player data
                local dataUpdateQuery = string.format(
                    "UPDATE player_data SET position_x = %f, position_y = %f, character_data = '%s' WHERE player_id = %d",
                    playerData.position.x,
                    playerData.position.y,
                    conn:escape(characterData),
                    playerData.id
                )
                conn:execute(dataUpdateQuery)
            else
                -- Insert player data
                local dataInsertQuery = string.format(
                    "INSERT INTO player_data (player_id, position_x, position_y, character_data) VALUES (%d, %f, %f, '%s')",
                    playerData.id,
                    playerData.position.x,
                    playerData.position.y,
                    conn:escape(characterData)
                )
                conn:execute(dataInsertQuery)
            end
        else
            -- Insert new player
            local insertQuery = string.format(
                "INSERT INTO players (id, name, card_id, pin, hub_seed, last_login, created_at) VALUES (%d, '%s', '%s', '%s', %d, %d, %d)",
                playerData.id,
                conn:escape(playerData.name),
                conn:escape(playerData.cardId),
                conn:escape(playerData.pin or "0000"), -- Default PIN if not set
                playerData.hubSeed,
                os.time(),
                os.time()
            )
            conn:execute(insertQuery)

            -- Insert player data
            local characterData = DatabaseManager.serialize(playerData.character or {})

            local dataInsertQuery = string.format(
                "INSERT INTO player_data (player_id, position_x, position_y, character_data) VALUES (%d, %f, %f, '%s')",
                playerData.id,
                playerData.position.x,
                playerData.position.y,
                conn:escape(characterData)
            )
            conn:execute(dataInsertQuery)
        end
    else
        -- Save to file
        local filename
        if type(playerData.id) == "number" then
            filename = string.format("%s/player_%d.json", DatabaseManager.jsonPath, playerData.id)
        else
            -- Fallback for non-numeric IDs
            filename = string.format("%s/player_%s.json", DatabaseManager.jsonPath, tostring(playerData.id))
        end
        print("Saving to filename: " .. filename)

        -- Prepare data for saving
        local saveData = {
            id = playerData.id,
            name = playerData.name or "Unknown Player",
            cardId = playerData.cardId,
            pin = playerData.pin or "0000", -- Default PIN if not set
            hubSeed = playerData.hubSeed,
            position = playerData.position or { x = 0, y = 0 },
            last_login = os.time(),
            created_at = playerData.created_at or os.time()
        }

        -- Handle character data carefully
        if playerData.character then
            -- Create a deep copy of character data to preserve all fields
            saveData.character = {}

            -- Copy all basic fields
            saveData.character.id = playerData.character.id
            saveData.character.name = playerData.character.name or saveData.name
            saveData.character.class = playerData.character.class or "Warrior"
            saveData.character.level = playerData.character.level or 1
            saveData.character.experience = playerData.character.experience or 0
            saveData.character.health = playerData.character.health or 100
            saveData.character.maxHealth = playerData.character.maxHealth or 100
            saveData.character.mana = playerData.character.mana or 100
            saveData.character.maxMana = playerData.character.maxMana or 100
            saveData.character.strength = playerData.character.strength or 10
            saveData.character.dexterity = playerData.character.dexterity or 10
            saveData.character.intelligence = playerData.character.intelligence or 10
            saveData.character.wisdom = playerData.character.wisdom or 10
            saveData.character.constitution = playerData.character.constitution or 10
            saveData.character.charisma = playerData.character.charisma or 10

            -- Deep copy equipment
            if playerData.character.equipment then
                saveData.character.equipment = DatabaseManager.deepCopy(playerData.character.equipment)
                local equipCount = 0
                for _ in pairs(saveData.character.equipment) do equipCount = equipCount + 1 end
                print("Saved " .. equipCount .. " equipment items")
            else
                saveData.character.equipment = {}
            end

            -- Deep copy inventory
            if playerData.character.inventory then
                saveData.character.inventory = DatabaseManager.deepCopy(playerData.character.inventory)
                local invCount = 0
                for _ in pairs(saveData.character.inventory) do invCount = invCount + 1 end
                print("Saved " .. invCount .. " inventory items")
            else
                saveData.character.inventory = {}
            end

            -- Deep copy skills
            if playerData.character.skills then
                saveData.character.skills = DatabaseManager.deepCopy(playerData.character.skills)
                local skillCount = 0
                for _ in pairs(saveData.character.skills) do skillCount = skillCount + 1 end
                print("Saved " .. skillCount .. " skills")
            else
                saveData.character.skills = {}
            end

            -- Copy any other fields that might exist
            for k, v in pairs(playerData.character) do
                if saveData.character[k] == nil and k ~= "_deleted" and type(v) ~= "function" then
                    saveData.character[k] = v
                    print("Copied additional field: " .. k)
                end
            end
        else
            -- Create a basic character if none exists
            saveData.character = {
                name = saveData.name,
                class = "Warrior",
                level = 1,
                experience = 0,
                health = 100,
                maxHealth = 100,
                mana = 100,
                maxMana = 100,
                strength = 10,
                dexterity = 10,
                intelligence = 10,
                wisdom = 10,
                constitution = 10,
                charisma = 10,
                skills = {},
                inventory = {},
                equipment = {}
            }
        end

        print("Character data prepared for saving with " .. (saveData.character and "character data" or "no character data"))

        -- Ensure the directory exists with proper permissions
        if not DatabaseManager.jsonPath or DatabaseManager.jsonPath == "" then
            print("No valid save path, using default")
            DatabaseManager.jsonPath = "saves/player_data"
        end

        -- Check if the directory exists
        if not lfs.getInfo(DatabaseManager.jsonPath) then
            print("Save directory doesn't exist, creating it: " .. DatabaseManager.jsonPath)

            -- If it's a nested path, we need to create parent directories first
            if DatabaseManager.jsonPath:find("/") then
                local parentDir = DatabaseManager.jsonPath:match("(.+)/[^/]+")
                if parentDir and not lfs.getInfo(parentDir) then
                    print("Creating parent directory: " .. parentDir)
                    lfs.createDirectory(parentDir)
                end
            end

            local success = lfs.createDirectory(DatabaseManager.jsonPath)
            if success then
                print("Successfully created save directory")
            else
                print("Failed to create save directory, trying alternate location")
                DatabaseManager.jsonPath = "player_data"
                lfs.createDirectory(DatabaseManager.jsonPath)
            end
        end

        -- Ensure character data is properly structured
        if saveData.character then
            -- Make sure character has required fields
            if not saveData.character.class then
                saveData.character.class = saveData.characterClass or "Warrior"
            end
            if not saveData.character.level then
                saveData.character.level = 1
            end
        end

        -- Serialize the data
        local content = DatabaseManager.serialize(saveData)
        print("Serialized content: " .. content:sub(1, 100) .. "...")

        -- Save to file
        local success = false
        local errorMsg = nil

        -- Try to write the file with error handling
        print("Current working directory: " .. love.filesystem.getWorkingDirectory())
        print("LÖVE save directory: " .. love.filesystem.getSaveDirectory())

        -- Try to create the directory again just to be sure
        local dirPath = DatabaseManager.jsonPath
        if dirPath:find("/") then
            local parentDir = dirPath:match("(.+)/[^/]+")
            if parentDir then
                print("Ensuring parent directory exists: " .. parentDir)
                lfs.createDirectory(parentDir)

                -- Also try with love.filesystem
                if parentDir:find(love.filesystem.getSaveDirectory()) then
                    local lovePath = parentDir:gsub(love.filesystem.getSaveDirectory() .. "/", "")
                    print("Creating LÖVE directory: " .. lovePath)
                    love.filesystem.createDirectory(lovePath)
                end
            end
        end
        print("Ensuring save directory exists: " .. dirPath)
        lfs.createDirectory(dirPath)

        -- Check if we're using the LÖVE save directory
        if dirPath:find(love.filesystem.getSaveDirectory()) then
            local lovePath = dirPath:gsub(love.filesystem.getSaveDirectory() .. "/", "")
            print("Creating LÖVE directory: " .. lovePath)
            love.filesystem.createDirectory(lovePath)

            -- Extract the filename without the path
            local loveFilename = filename:match("[^/]+$")
            if not loveFilename then loveFilename = filename end

            -- Get the full path within LÖVE's filesystem
            local loveFullPath = lovePath .. "/" .. loveFilename
            print("Trying to write using LÖVE filesystem: " .. loveFullPath)

            -- Try to write using LÖVE's filesystem functions
            success, errorMsg = pcall(function()
                local result = love.filesystem.write(loveFullPath, content)
                if result then
                    print("Successfully wrote file using LÖVE filesystem")
                    return true
                else
                    print("Failed to write file using LÖVE filesystem")
                    return false
                end
            end)
        end

        -- If LÖVE filesystem didn't work or we're not using it, try standard IO
        if not success or not errorMsg then
            -- Try with absolute path
            local absolutePath = love.filesystem.getWorkingDirectory() .. "/" .. filename
            print("Trying to write to absolute path: " .. absolutePath)

            local file, openError = io.open(absolutePath, "w")
            if file then
                success, errorMsg = pcall(function()
                    file:write(content)
                    file:close()
                    return true
                end)
            else
                success = false
                errorMsg = "Could not open file: " .. tostring(openError)

                -- Try with relative path as fallback
                print("Trying with relative path as fallback")
                local relativeFile, relativeError = io.open(filename, "w")
                if relativeFile then
                    success, errorMsg = pcall(function()
                        relativeFile:write(content)
                        relativeFile:close()
                        return true
                    end)
                else
                    success = false
                    errorMsg = "Could not open file (relative path): " .. tostring(relativeError)
                end
            end
        end

        if success and errorMsg then
            print("Successfully saved character to file: " .. filename)

            -- Create a simple index file to help with character listing
            local indexData = {
                id = saveData.id,
                name = saveData.name,
                cardId = saveData.cardId,
                class = (saveData.character and saveData.character.class) or "Unknown",
                level = (saveData.character and saveData.character.level) or 1
            }

            -- Create index filename based on ID type
            local indexFilename
            if type(saveData.id) == "number" then
                indexFilename = string.format("%s/index_%d.json", DatabaseManager.jsonPath, saveData.id)
            else
                indexFilename = string.format("%s/index_%s.json", DatabaseManager.jsonPath, tostring(saveData.id))
            end

            local indexContent = DatabaseManager.serialize(indexData)
            -- Check if we're using the LÖVE save directory
            local dirPath = DatabaseManager.jsonPath
            if dirPath:find(love.filesystem.getSaveDirectory()) then
                local lovePath = dirPath:gsub(love.filesystem.getSaveDirectory() .. "/", "")

                -- Extract the filename without the path
                local loveIndexFilename = indexFilename:match("[^/]+$")
                if not loveIndexFilename then loveIndexFilename = indexFilename end

                -- Get the full path within LÖVE's filesystem
                local loveFullPath = lovePath .. "/" .. loveIndexFilename
                print("Trying to write index using LÖVE filesystem: " .. loveFullPath)

                -- Try to write using LÖVE's filesystem functions
                local indexSuccess, indexError = pcall(function()
                    local result = love.filesystem.write(loveFullPath, indexContent)
                    if result then
                        print("Successfully wrote index file using LÖVE filesystem")
                        return true
                    else
                        print("Failed to write index file using LÖVE filesystem")
                        return false
                    end
                end)

                if indexSuccess and indexError then
                    print("Created character index file: " .. indexFilename)
                    return -- Skip the standard IO methods if LÖVE filesystem worked
                end
            end

            -- If LÖVE filesystem didn't work or we're not using it, try standard IO
            -- Try with absolute path for index file
            local absoluteIndexPath = love.filesystem.getWorkingDirectory() .. "/" .. indexFilename
            print("Trying to write index to absolute path: " .. absoluteIndexPath)

            local indexFile, indexOpenError = io.open(absoluteIndexPath, "w")
            if indexFile then
                local indexSuccess, indexError = pcall(function()
                    indexFile:write(indexContent)
                    indexFile:close()
                    return true
                end)

                if indexSuccess and indexError then
                    print("Created character index file: " .. indexFilename)
                else
                    print("Warning: Failed to write index file: " .. tostring(indexError))

                    -- Try with relative path as fallback
                    local relativeIndexFile = io.open(indexFilename, "w")
                    if relativeIndexFile then
                        relativeIndexFile:write(indexContent)
                        relativeIndexFile:close()
                        print("Created index file using relative path")
                    end
                end
            else
                print("Warning: Failed to open index file: " .. tostring(indexOpenError))

                -- Try with relative path as fallback
                local relativeIndexFile = io.open(indexFilename, "w")
                if relativeIndexFile then
                    relativeIndexFile:write(indexContent)
                    relativeIndexFile:close()
                    print("Created index file using relative path")
                end
            end
        else
            print("Failed to save character to file: " .. filename)
            if not success then
                print("Error: " .. tostring(errorMsg))
            end

            -- Try an alternate location as fallback
            local altFilename = "player_" .. tostring(saveData.id) .. ".json"
            print("Trying alternate location: " .. altFilename)

            -- Try with absolute path for alternate location
            local absoluteAltPath = love.filesystem.getWorkingDirectory() .. "/" .. altFilename
            print("Trying to write to absolute alternate path: " .. absoluteAltPath)

            local altFile, altOpenError = io.open(absoluteAltPath, "w")
            if altFile then
                local altSuccess, altError = pcall(function()
                    altFile:write(content)
                    altFile:close()
                    return true
                end)

                if altSuccess and altError then
                    print("Successfully saved to alternate location: " .. altFilename)

                    -- Also create an index file in the same location
                    local altIndexFilename = "index_" .. tostring(saveData.id) .. ".json"
                    local absoluteAltIndexPath = love.filesystem.getWorkingDirectory() .. "/" .. altIndexFilename

                    local altIndexContent = DatabaseManager.serialize({
                        id = saveData.id,
                        name = saveData.name,
                        cardId = saveData.cardId,
                        class = (saveData.character and saveData.character.class) or "Unknown",
                        level = (saveData.character and saveData.character.level) or 1
                    })

                    local altIndexFile = io.open(absoluteAltIndexPath, "w")
                    if altIndexFile then
                        altIndexFile:write(altIndexContent)
                        altIndexFile:close()
                        print("Created index file in alternate location: " .. altIndexFilename)
                    else
                        -- Try with relative path
                        local relativeAltIndexFile = io.open(altIndexFilename, "w")
                        if relativeAltIndexFile then
                            relativeAltIndexFile:write(altIndexContent)
                            relativeAltIndexFile:close()
                            print("Created index file in alternate location using relative path")
                        end
                    end
                else
                    print("Failed to write to alternate location: " .. tostring(altError))

                    -- Try with relative path
                    local relativeAltFile = io.open(altFilename, "w")
                    if relativeAltFile then
                        relativeAltFile:write(content)
                        relativeAltFile:close()
                        print("Successfully saved to alternate location using relative path")
                    end
                end
            else
                print("Failed to open alternate location file: " .. tostring(altOpenError))

                -- Try with relative path
                local relativeAltFile = io.open(altFilename, "w")
                if relativeAltFile then
                    relativeAltFile:write(content)
                    relativeAltFile:close()
                    print("Successfully saved to alternate location using relative path")

                    -- Also create an index file
                    local altIndexFilename = "index_" .. tostring(saveData.id) .. ".json"
                    local altIndexContent = DatabaseManager.serialize({
                        id = saveData.id,
                        name = saveData.name,
                        cardId = saveData.cardId,
                        class = (saveData.character and saveData.character.class) or "Unknown",
                        level = (saveData.character and saveData.character.level) or 1
                    })

                    local relativeAltIndexFile = io.open(altIndexFilename, "w")
                    if relativeAltIndexFile then
                        relativeAltIndexFile:write(altIndexContent)
                        relativeAltIndexFile:close()
                        print("Created index file in alternate location using relative path")
                    end
                end
            end
        end
    end

    return true
end

-- Create a new player
function DatabaseManager.createPlayer(cardId, name, pin)
    if not DatabaseManager.connectionActive then return nil end

    print("Creating new player for card: " .. cardId)

    -- Generate player data
    local playerId = DatabaseManager.generatePlayerId()
    local hubSeed = love.math.random(1, 1000000)
    local defaultPin = pin or "0000"

    local playerData = {
        id = playerId,
        name = name,
        cardId = cardId,
        pin = defaultPin,
        hubSeed = hubSeed,
        position = {x = 0, y = 0},
        created_at = os.time(),
        last_login = os.time()
    }

    -- Save the new player
    if DatabaseManager.useSQL then
        local insertQuery = string.format(
            "INSERT INTO players (id, name, card_id, pin, hub_seed, last_login, created_at) VALUES (%d, '%s', '%s', '%s', %d, %d, %d)",
            playerId,
            conn:escape(name),
            conn:escape(cardId),
            conn:escape(defaultPin),
            hubSeed,
            os.time(),
            os.time()
        )
        conn:execute(insertQuery)

        -- Insert initial player data
        local dataInsertQuery = string.format(
            "INSERT INTO player_data (player_id, position_x, position_y) VALUES (%d, 0, 0)",
            playerId
        )
        conn:execute(dataInsertQuery)
    else
        -- Save to file
        local filename = string.format("%s/player_%d.json", DatabaseManager.jsonPath, playerId)
        lfs.write(filename, DatabaseManager.serialize(playerData))
    end

    -- Cache the new player
    DatabaseManager.playerCache[playerId] = DatabaseManager.copyTable(playerData)

    return playerData
end

-- Delete a player
function DatabaseManager.deletePlayer(playerId)
    if not DatabaseManager.connectionActive then return false end

    print("Deleting player: " .. playerId)

    if DatabaseManager.useSQL then
        -- Delete from database
        local deleteDataQuery = string.format("DELETE FROM player_data WHERE player_id = %d", playerId)
        conn:execute(deleteDataQuery)

        local deletePlayerQuery = string.format("DELETE FROM players WHERE id = %d", playerId)
        conn:execute(deletePlayerQuery)
    else
        -- Delete file
        local filename = string.format("%s/player_%d.json", DatabaseManager.jsonPath, playerId)
        if lfs.getInfo(filename) then
            lfs.remove(filename)
        end
    end

    -- Mark as deleted in cache
    if DatabaseManager.playerCache[playerId] then
        DatabaseManager.playerCache[playerId]._deleted = true
    end

    return true
end

-- Change player PIN
function DatabaseManager.changePlayerPIN(playerId, newPin)
    if not DatabaseManager.connectionActive then return false end

    print("Changing PIN for player: " .. playerId)

    if DatabaseManager.useSQL then
        -- Update PIN in database
        local updateQuery = string.format(
            "UPDATE players SET pin = '%s' WHERE id = %d",
            conn:escape(newPin),
            playerId
        )
        conn:execute(updateQuery)
    else
        -- Update PIN in file
        local filename = string.format("%s/player_%d.json", DatabaseManager.jsonPath, playerId)

        if lfs.getInfo(filename) then
            local content = lfs.read(filename)
            local data = DatabaseManager.deserialize(content)

            if data then
                data.pin = newPin
                lfs.write(filename, DatabaseManager.serialize(data))
            end
        end
    end

    -- Update cache if present
    if DatabaseManager.playerCache[playerId] then
        DatabaseManager.playerCache[playerId].pin = newPin
    end

    return true
end

-- Get the current save path
function DatabaseManager.getSavePath()
    return DatabaseManager.jsonPath
end

-- Get all players
function DatabaseManager.getAllPlayers()
    print("getAllPlayers called")

    -- Initialize database if not already connected
    if not DatabaseManager.connectionActive then
        print("Database not connected, initializing now")
        DatabaseManager.connect()

        if not DatabaseManager.connectionActive then
            print("Failed to initialize database, but will try to load locally anyway")
            -- Force connection to be active for local file loading
            DatabaseManager.connectionActive = true
        end
    end

    -- Make sure the player_data directory exists
    if not DatabaseManager.useSQL and not lfs.getInfo(DatabaseManager.jsonPath) then
        print("Creating player_data directory for getAllPlayers")
        lfs.createDirectory(DatabaseManager.jsonPath)
    end

    local players = {}

    if DatabaseManager.useSQL then
        -- Query all players
        local query = "SELECT id, name, card_id, hub_seed FROM players"
        local cursor = conn:execute(query)

        local row = cursor:fetch({}, "a")
        while row do
            table.insert(players, {
                id = row.id,
                name = row.name,
                cardId = row.card_id,
                hubSeed = row.hub_seed
            })
            row = cursor:fetch({}, "a")
        end
        cursor:close()
    else
        -- Scan player files
        print("Scanning for player files in: " .. DatabaseManager.jsonPath)

        -- Check if the directory exists
        if not lfs.getInfo(DatabaseManager.jsonPath) then
            print("player_data directory doesn't exist, creating it")
            lfs.createDirectory(DatabaseManager.jsonPath)
            return players -- Return empty list since we just created the directory
        end

        local files = lfs.getDirectoryItems(DatabaseManager.jsonPath)
        print("Found " .. #files .. " files in player_data directory")

        -- First try to use index files for faster loading
        local foundIndexFiles = false

        -- Function to read an index file
        local function readIndexFile(path, filename)
            print("Reading index file: " .. path .. "/" .. filename)

            -- Try with absolute path first
            local absolutePath = love.filesystem.getWorkingDirectory() .. "/" .. path .. "/" .. filename
            print("Trying to read index from absolute path: " .. absolutePath)

            local file = io.open(absolutePath, "r")
            if not file then
                print("Could not open index file with absolute path")

                -- Try with relative path
                file = io.open(path .. "/" .. filename, "r")
                if not file then
                    print("Could not open index file: " .. path .. "/" .. filename)
                    return nil
                end
            end

            local content = file:read("*all")
            file:close()

            local data = DatabaseManager.deserialize(content)
            if data and data.id then
                print("Found player from index: " .. (data.name or "Unknown"))
                return {
                    id = data.id,
                    name = data.name,
                    cardId = data.cardId,
                    class = data.class,
                    level = data.level,
                    hubSeed = data.hubSeed or math.random(1, 1000000)
                }
            else
                print("Failed to parse index data from file: " .. filename)
                return nil
            end
        end

        -- Function to read a player file
        local function readPlayerFile(path, filename)
            print("Reading player file: " .. path .. "/" .. filename)

            -- Try with absolute path first
            local absolutePath = love.filesystem.getWorkingDirectory() .. "/" .. path .. "/" .. filename
            print("Trying to read from absolute path: " .. absolutePath)

            local file = io.open(absolutePath, "r")
            if not file then
                print("Could not open player file with absolute path")

                -- Try with relative path
                file = io.open(path .. "/" .. filename, "r")
                if not file then
                    print("Could not open player file: " .. path .. "/" .. filename)
                    return nil
                end
            end

            local content = file:read("*all")
            file:close()

            local data = DatabaseManager.deserialize(content)
            if data and data.id then
                print("Found player: " .. (data.name or "Unknown"))
                return {
                    id = data.id,
                    name = data.name,
                    cardId = data.cardId,
                    class = (data.character and data.character.class) or "Unknown",
                    level = (data.character and data.character.level) or 1,
                    hubSeed = data.hubSeed
                }
            else
                print("Failed to parse player data from file: " .. filename)
                return nil
            end
        end

        -- Check primary location (saves/player_data)
        for _, file in ipairs(files) do
            -- Look for index files first
            if file:match("^index_.*%.json$") then
                foundIndexFiles = true
                local playerData = readIndexFile(DatabaseManager.jsonPath, file)
                if playerData then
                    table.insert(players, playerData)
                end
            end
        end

        -- If no index files found in primary location, check for player files there
        if not foundIndexFiles then
            print("No index files found in primary location, scanning player files")
            for _, file in ipairs(files) do
                -- Skip the next_id.txt file and index files
                if file ~= "next_id.txt" and file:match("^player_.*%.json$") then
                    local playerData = readPlayerFile(DatabaseManager.jsonPath, file)
                    if playerData then
                        table.insert(players, playerData)
                    end
                end
            end
        end

        -- Check LÖVE save directory
        if #players == 0 then
            print("No players found in primary location, checking LÖVE save directory")
            local lovePath = "lazarus_arc/player_data"

            -- Check if the directory exists in LÖVE's filesystem
            local success, result = pcall(function() return love.filesystem.getInfo(lovePath, "directory") end)
            if success and result then
                print("Found LÖVE save directory: " .. lovePath)

                local loveFiles = {}
                success, result = pcall(function() return love.filesystem.getDirectoryItems(lovePath) end)
                if success and result then
                    loveFiles = result
                    print("Found " .. #loveFiles .. " files in LÖVE save directory")
                else
                    print("Failed to get LÖVE directory items: " .. tostring(result))
                    loveFiles = {}
                end

                -- First try index files
                local foundLoveIndexFiles = false
                for _, file in ipairs(loveFiles) do
                    if file:match("^index_.*%.json$") then
                        foundLoveIndexFiles = true
                        print("Reading LÖVE index file: " .. lovePath .. "/" .. file)

                        local content = love.filesystem.read(lovePath .. "/" .. file)
                        if content then
                            local data = DatabaseManager.deserialize(content)
                            if data and data.id then
                                print("Found player from LÖVE index: " .. (data.name or "Unknown"))
                                table.insert(players, {
                                    id = data.id,
                                    name = data.name,
                                    cardId = data.cardId,
                                    class = data.class,
                                    level = data.level,
                                    hubSeed = data.hubSeed or math.random(1, 1000000)
                                })
                            end
                        end
                    end
                end

                -- If no index files, try player files
                if not foundLoveIndexFiles and #players == 0 then
                    print("No index files found in LÖVE save directory, scanning player files")
                    for _, file in ipairs(loveFiles) do
                        if file:match("^player_.*%.json$") then
                            print("Reading LÖVE player file: " .. lovePath .. "/" .. file)

                            local content = love.filesystem.read(lovePath .. "/" .. file)
                            if content then
                                local data = DatabaseManager.deserialize(content)
                                if data and data.id then
                                    print("Found player from LÖVE file: " .. (data.name or "Unknown"))
                                    table.insert(players, {
                                        id = data.id,
                                        name = data.name,
                                        cardId = data.cardId,
                                        class = (data.character and data.character.class) or "Unknown",
                                        level = (data.character and data.character.level) or 1,
                                        hubSeed = data.hubSeed
                                    })
                                end
                            end
                        end
                    end
                end
            else
                print("LÖVE save directory not found")
            end
        end

        -- Also check for files in the root directory (alternate location)
        if #players == 0 then
            print("No players found in LÖVE save directory, checking alternate location")

            -- Try to read directly from the current directory
            print("Checking for player files in current directory")
            local rootFiles = {}

            -- Try to get directory items
            local success, result = pcall(function() return lfs.getDirectoryItems(".") end)
            if success and result then
                rootFiles = result
                print("Found " .. #rootFiles .. " files in current directory")
            else
                print("Failed to get directory items: " .. tostring(result))
                rootFiles = {}
            end

            -- First try index files in root
            local foundRootIndexFiles = false
            for _, file in ipairs(rootFiles) do
                if file:match("^index_.*%.json$") then
                    foundRootIndexFiles = true
                    local playerData = readIndexFile(".", file)
                    if playerData then
                        table.insert(players, playerData)
                    end
                end
            end

            -- If no index files in root, try player files
            if not foundRootIndexFiles and #players == 0 then
                print("No index files found in alternate location, scanning player files")
                for _, file in ipairs(rootFiles) do
                    if file:match("^player_.*%.json$") then
                        local playerData = readPlayerFile(".", file)
                        if playerData then
                            table.insert(players, playerData)
                        end
                    end
                end
            end

            -- If still no players found, try direct file access
            if #players == 0 then
                print("Trying direct file access for player_1.json")
                local directFile = io.open("player_1.json", "r")
                if directFile then
                    print("Found player_1.json in current directory")
                    local content = directFile:read("*all")
                    directFile:close()

                    local data = DatabaseManager.deserialize(content)
                    if data and data.id then
                        print("Found player from direct file access: " .. (data.name or "Unknown"))
                        table.insert(players, {
                            id = data.id,
                            name = data.name,
                            cardId = data.cardId,
                            class = (data.character and data.character.class) or "Unknown",
                            level = (data.character and data.character.level) or 1,
                            hubSeed = data.hubSeed
                        })
                    end
                else
                    print("Could not find player_1.json in current directory")
                end
            end
        end
    end

    return players
end

-- Back up all data
function DatabaseManager.backupData(backupPath)
    backupPath = backupPath or "backup_" .. os.date("%Y%m%d_%H%M%S")

    print("Backing up database to: " .. backupPath)

    -- Create backup directory
    if not lfs.getInfo(backupPath) then
        lfs.createDirectory(backupPath)
    end

    -- Flush cache to ensure all data is saved
    DatabaseManager.flushCache()

    if DatabaseManager.useSQL then
        -- Export all data to JSON files
        local query = "SELECT p.id, p.name, p.card_id, p.pin, p.hub_seed, p.last_login, p.created_at, " ..
                      "pd.position_x, pd.position_y, pd.character_data, pd.inventory_data, pd.stats_data " ..
                      "FROM players p LEFT JOIN player_data pd ON p.id = pd.player_id"

        local cursor = conn:execute(query)

        local row = cursor:fetch({}, "a")
        while row do
            local playerData = {
                id = row.id,
                name = row.name,
                cardId = row.card_id,
                pin = row.pin,
                hubSeed = row.hub_seed,
                last_login = row.last_login,
                created_at = row.created_at,
                position = {
                    x = row.position_x or 0,
                    y = row.position_y or 0
                },
                character = row.character_data and DatabaseManager.deserialize(row.character_data) or {},
                inventory = row.inventory_data and DatabaseManager.deserialize(row.inventory_data) or {},
                stats = row.stats_data and DatabaseManager.deserialize(row.stats_data) or {}
            }

            -- Save to backup file
            local filename = string.format("%s/player_%d.json", backupPath, row.id)
            lfs.write(filename, DatabaseManager.serialize(playerData))

            row = cursor:fetch({}, "a")
        end
        cursor:close()
    else
        -- Copy all player files to backup directory
        local files = lfs.getDirectoryItems(DatabaseManager.jsonPath)

        for _, file in ipairs(files) do
            if file:match("%.json$") then
                local content = lfs.read(DatabaseManager.jsonPath .. "/" .. file)
                lfs.write(backupPath .. "/" .. file, content)
            end
        end

        -- Copy next_id.txt
        if lfs.getInfo(DatabaseManager.jsonPath .. "/next_id.txt") then
            local content = lfs.read(DatabaseManager.jsonPath .. "/next_id.txt")
            lfs.write(backupPath .. "/next_id.txt", content)
        end
    end

    return true
end

-- Deep copy a table (including nested tables)
function DatabaseManager.deepCopy(orig, depth)
    depth = depth or 0
    if depth > 10 then return nil end -- Prevent infinite recursion

    if type(orig) ~= 'table' then return orig end
    local copy = {}
    for k, v in pairs(orig) do
        if type(v) == 'table' then
            copy[k] = DatabaseManager.deepCopy(v, depth + 1)
        else
            copy[k] = v
        end
    end
    return copy
end

-- Sanitize table for JSON serialization
function DatabaseManager.sanitizeForJSON(data, depth)
    depth = depth or 0
    if depth > 10 then return nil end -- Prevent infinite recursion

    if data == nil then
        return nil
    elseif type(data) == "function" or type(data) == "userdata" or type(data) == "thread" then
        return nil -- Skip unsupported types
    elseif type(data) == "table" then
        local result = {}
        for k, v in pairs(data) do
            -- Skip keys that start with underscore (private fields)
            if type(k) ~= "string" or k:sub(1,1) ~= "_" then
                -- Only include string, number, or boolean keys
                if type(k) == "string" or type(k) == "number" or type(k) == "boolean" then
                    result[k] = DatabaseManager.sanitizeForJSON(v, depth + 1)
                end
            end
        end
        return result
    else
        return data -- Keep strings, numbers, booleans as is
    end
end

-- Serialize table to JSON using lunajson
function DatabaseManager.serialize(data)
    if data == nil then
        return "null"
    end

    -- Sanitize data for JSON serialization
    local sanitizedData = DatabaseManager.sanitizeForJSON(data)

    -- Use lunajson to serialize the sanitized data
    local success, result = pcall(function()
        return lunajson.encode(sanitizedData)
    end)

    if success then
        return result
    else
        print("Error serializing data: " .. tostring(result))
        -- Fallback to simple serialization for error cases
        if type(data) == "table" then
            return "{}"
        elseif type(data) == "string" then
            return '"' .. data:gsub('"', '\\"') .. '"'
        elseif type(data) == "number" or type(data) == "boolean" then
            return tostring(data)
        else
            return "null"
        end
    end
end

-- Deserialize JSON to table using lunajson
function DatabaseManager.deserialize(str)
    if not str or str == "" then
        print("Empty string passed to deserialize")
        return nil
    end

    print("Deserializing JSON string: " .. str:sub(1, 50) .. "...")

    -- Use lunajson to deserialize the data
    local success, result = pcall(function()
        return lunajson.decode(str)
    end)

    if success then
        print("Successfully deserialized JSON data")
        return result
    else
        print("Error deserializing JSON: " .. tostring(result))

        -- Fallback to simple deserialization for error cases
        if str:sub(1,1) == "{" then
            -- Try to extract some basic fields
            local data = {}

            -- Extract id
            local id = str:match('"id":(%d+)')
            if id then data.id = tonumber(id) end

            -- Extract name
            local name = str:match('"name":"([^"]+)"')
            if name then data.name = name end

            -- Extract cardId
            local cardId = str:match('"cardId":"([^"]+)"')
            if cardId then data.cardId = cardId end

            print("Fallback deserialization extracted basic fields")
            return data
        else
            -- Not valid JSON
            return nil
        end
    end
end

-- Deep copy a table
function DatabaseManager.copyTable(orig)
    local orig_type = type(orig)
    local copy
    if orig_type == 'table' then
        copy = {}
        for orig_key, orig_value in pairs(orig) do
            copy[orig_key] = DatabaseManager.copyTable(orig_value)
        end
    else
        copy = orig
    end
    return copy
end

-- Save a character
function DatabaseManager.saveCharacter(player)
    print("saveCharacter called with player: " .. tostring(player))

    -- Initialize database if not already connected
    if not DatabaseManager.connectionActive then
        print("Database not connected, initializing now")
        DatabaseManager.connect()

        if not DatabaseManager.connectionActive then
            print("Failed to initialize database, but will try to save locally anyway")
        end
    end

    if not player then
        print("Cannot save character: Player is nil")
        return false
    end

    -- Handle both player object and character object
    local character = player
    if player.character then
        character = player.character
        print("Player object detected, extracting character data")
    end

    print("Saving character: " .. (player.name or character.name or "Unknown"))

    -- Debug dump of player object
    print("Player object structure:")
    for k, v in pairs(player) do
        if type(v) ~= "table" then
            print("  " .. k .. ": " .. tostring(v))
        else
            print("  " .. k .. ": [table]")

            -- If this is a character or inventory table, print its contents
            if k == "character" or k == "inventory" or k == "equipment" or k == "skills" then
                print("  Contents of " .. k .. ":")
                for subk, subv in pairs(v) do
                    if type(subv) ~= "table" then
                        print("    " .. subk .. ": " .. tostring(subv))
                    else
                        print("    " .. subk .. ": [table]")
                    end
                end
            end
        end
    end

    -- Generate an ID if needed
    if not character.id then
        character.id = DatabaseManager.nextPlayerId
        DatabaseManager.nextPlayerId = DatabaseManager.nextPlayerId + 1
        print("Generated new character ID: " .. character.id)

        -- Save the next ID
        if not DatabaseManager.useSQL then
            lfs.write(DatabaseManager.jsonPath .. "/next_id.txt", tostring(DatabaseManager.nextPlayerId))
        end
    end

    -- Ensure character has a cardId
    if not player.cardId then
        player.cardId = "local_" .. os.time() .. "_" .. math.random(1000, 9999)
        print("Generated new card ID: " .. player.cardId)
    end

    -- Create or update player data
    local playerData = {
        id = character.id,
        name = player.name or character.name,
        cardId = player.cardId,
        hubSeed = player.hubSeed or character.hubSeed or math.random(1, 1000000),
        position = player.position or {x = 0, y = 0},
        character = character
    }

    print("Prepared player data for saving: ID=" .. playerData.id .. ", Name=" .. playerData.name)

    -- If we still don't have player data, create a basic record
    if not playerData then
        print("WARNING: Creating new player data record for character save")
        playerData = {
            id = characterId,
            name = character.name or "Unknown Player",
            cardId = character.cardId or UUID.generate(),
            hubSeed = character.hubSeed or os.time(),
            position = {x = 0, y = 0},
            character = {}
        }
    end

    -- Update character data
    playerData.character = character

    -- Update position if available
    if character.position then
        playerData.position = {
            x = character.position.x or 0,
            y = character.position.y or 0
        }
    end

    -- Save the updated player data
    return DatabaseManager.savePlayerData(playerData)
end

return DatabaseManager
