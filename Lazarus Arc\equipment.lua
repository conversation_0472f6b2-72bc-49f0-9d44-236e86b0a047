-- equipment.lua
-- Equipment management system for Lazarus Arc

local ItemDatabase = require("item_database") -- Central database of all items

local Equipment = {
    -- Valid equipment slots
    slots = {
        head = "Head",
        chest = "Chest",
        legs = "Legs",
        feet = "Feet",
        hands = "Hands",
        back = "Back",
        neck = "Neck",
        ring1 = "Ring 1",
        ring2 = "Ring 2",
        mainhand = "Main Hand",
        offhand = "Off Hand",
        ranged = "Ranged",
        ammo = "Ammunition",
        accessory1 = "Accessory 1",
        accessory2 = "Accessory 2"
    },
    
    -- Valid equipment types
    types = {
        armor = {
            cloth = "Cloth Armor",
            light = "Light Armor",
            medium = "Medium Armor",
            heavy = "Heavy Armor"
        },
        weapon = {
            dagger = "Dagger",
            sword = "Sword",
            axe = "Axe",
            mace = "Mace",
            hammer = "Hammer",
            greatsword = "Greatsword",
            polearm = "Polearm",
            staff = "Staff",
            wand = "Wand",
            bow = "Bow",
            crossbow = "Crossbow",
            gun = "Gun"
        },
        accessory = {
            ring = "Ring",
            amulet = "Amulet",
            charm = "Charm",
            relic = "Relic",
            trinket = "Trinket",
            belt = "Belt",
            gloves = "Gloves",
            boots = "Boots"
        },
        shield = {
            buckler = "Buckler",
            round = "Round Shield",
            kite = "Kite Shield",
            tower = "Tower Shield",
            energy = "Energy Shield"
        }
    },
    
    -- Cached equipment data
    cache = {}
}

-- Load equipment data by ID
function Equipment.load(itemId)
    -- Check cache first
    if Equipment.cache[itemId] then
        return Equipment.cache[itemId]
    end
    
    -- Try to load from database
    local itemData = ItemDatabase.getItem(itemId)
    
    -- Only cache if it's actual equipment
    if itemData and itemData.equipmentType then
        -- Store in cache
        Equipment.cache[itemId] = itemData
        return itemData
    end
    
    return nil
end

-- Check if an item can be equipped
function Equipment.isEquippable(itemData)
    if not itemData then
        return false
    end
    
    -- Check for equipment type
    if not itemData.equipmentType then
        return false
    end
    
    -- Check for valid equipment type
    local equipType = itemData.equipmentType
    local subType = itemData.equipmentSubType
    
    -- Check if the equipment type exists
    if not Equipment.types[equipType] then
        return false
    end
    
    -- Check if the equipment subtype exists
    if subType and not Equipment.types[equipType][subType] then
        return false
    end
    
    return true
end

-- Get equipment stats (includes all equipped items)
function Equipment.calculateStats(equippedItems)
    if not equippedItems then
        return {}
    end
    
    local stats = {
        pattack = 0,       -- Physical attack
        pdefense = 0,      -- Physical defense
        mattack = 0,       -- Magical attack
        mdefense = 0,      -- Magical defense
        health = 0,        -- Health bonus
        mana = 0,          -- Mana bonus
        stamina = 0,       -- Stamina bonus
        speed = 0,         -- Speed modifier
        critChance = 0,    -- Critical hit chance
        critDamage = 0,    -- Critical hit damage
        resistances = {},  -- Damage resistances
        cooldownReduction = 0, -- Cooldown reduction
        durability = {}    -- Current durability for each item
    }
    
    -- Process each equipped item
    for slot, itemId in pairs(equippedItems) do
        if itemId then
            local itemData = Equipment.load(itemId)
            if itemData then
                -- Add basic stats
                if itemData.stats then
                    for statName, value in pairs(itemData.stats) do
                        if stats[statName] ~= nil then
                            -- Additive stats
                            stats[statName] = stats[statName] + value
                        end
                    end
                end
                
                -- Add resistances
                if itemData.resistances then
                    for damageType, value in pairs(itemData.resistances) do
                        stats.resistances[damageType] = (stats.resistances[damageType] or 0) + value
                    end
                end
                
                -- Store durability
                if itemData.durability then
                    stats.durability[itemId] = itemData.durability
                end
            end
        end
    end
    
    -- Cap resistances at 80%
    for damageType, value in pairs(stats.resistances) do
        stats.resistances[damageType] = math.min(value, 0.8)
    end
    
    return stats
end

-- Determine which slots an item can be equipped to
function Equipment.getValidSlots(itemData)
    if not Equipment.isEquippable(itemData) then
        return {}
    end
    
    local equipType = itemData.equipmentType
    local subType = itemData.equipmentSubType
    
    -- Return valid slots based on equipment type
    if equipType == "armor" then
        if subType == "head" or itemData.slot == "head" then
            return {"head"}
        elseif subType == "chest" or itemData.slot == "chest" then
            return {"chest"}
        elseif subType == "legs" or itemData.slot == "legs" then
            return {"legs"}
        elseif subType == "feet" or itemData.slot == "feet" then
            return {"feet"}
        elseif subType == "hands" or itemData.slot == "hands" then
            return {"hands"}
        elseif subType == "back" or itemData.slot == "back" then
            return {"back"}
        else
            -- For any generic armor where the slot isn't specified
            return {"head", "chest", "legs", "feet", "hands"}
        end
    elseif equipType == "weapon" then
        if subType == "dagger" or subType == "sword" or subType == "axe" or subType == "mace" then
            return {"mainhand", "offhand"}
        elseif subType == "greatsword" or subType == "polearm" or subType == "hammer" then
            return {"mainhand"} -- Two-handed weapons
        elseif subType == "staff" or subType == "wand" then
            return {"mainhand"}
        elseif subType == "bow" or subType == "crossbow" or subType == "gun" then
            return {"ranged"}
        else
            return {"mainhand"}
        end
    elseif equipType == "accessory" then
        if subType == "ring" then
            return {"ring1", "ring2"}
        elseif subType == "amulet" or subType == "charm" then
            return {"neck"}
        elseif subType == "belt" then
            return {"accessory1", "accessory2"}
        elseif subType == "gloves" then
            return {"hands"}
        elseif subType == "boots" then
            return {"feet"}
        else
            return {"accessory1", "accessory2"}
        end
    elseif equipType == "shield" then
        return {"offhand"}
    elseif equipType == "ammo" then
        return {"ammo"}
    end
    
    -- If specific slot is provided, use that
    if itemData.slot and Equipment.slots[itemData.slot] then
        return {itemData.slot}
    end
    
    -- Fallback
    return {}
end

-- Check if an item can go in a specific slot
function Equipment.canEquipToSlot(itemData, slot)
    if not Equipment.isEquippable(itemData) or not slot then
        return false
    end
    
    local validSlots = Equipment.getValidSlots(itemData)
    
    for _, validSlot in ipairs(validSlots) do
        if validSlot == slot then
            return true
        end
    end
    
    return false
end

-- Determine if two items can be equipped together
function Equipment.areCompatible(item1, item2)
    if not item1 or not item2 then
        return true -- Nil items are always compatible
    end
    
    -- Load items if needed
    if type(item1) == "string" then
        item1 = Equipment.load(item1)
    end
    
    if type(item2) == "string" then
        item2 = Equipment.load(item2)
    end
    
    -- Two-handed check
    if item1.twoHanded and (item2.equipmentType == "weapon" or item2.equipmentType == "shield") then
        return false -- Can't use two-handed weapon with another weapon or shield
    end
    
    if item2.twoHanded and (item1.equipmentType == "weapon" or item1.equipmentType == "shield") then
        return false -- Can't use two-handed weapon with another weapon or shield
    end
    
    -- Check unique flag (can't equip two of the same unique item)
    if item1.unique and item2.unique and item1.id == item2.id then
        return false
    end
    
    -- Check unique category flag (can't equip two unique items in the same category)
    if item1.uniqueCategory and item2.uniqueCategory and item1.uniqueCategory == item2.uniqueCategory then
        return false
    end
    
    return true
end

-- Apply durability reduction to equipment
function Equipment.applyDurabilityLoss(itemData, amount)
    if not itemData or not itemData.durability then
        return itemData
    end
    
    -- Create a copy of the item to modify
    local updatedItem = {}
    for k, v in pairs(itemData) do
        updatedItem[k] = v
    end
    
    -- Apply durability loss
    updatedItem.currentDurability = (updatedItem.currentDurability or updatedItem.durability) - amount
    
    -- Check if broken
    if updatedItem.currentDurability <= 0 then
        updatedItem.broken = true
        updatedItem.currentDurability = 0
        
        -- Apply broken penalties
        if updatedItem.stats then
            local brokenStats = {}
            for stat, value in pairs(updatedItem.stats) do
                -- Reduce all stats by 75% when broken
                brokenStats[stat] = value * 0.25
            end
            updatedItem.stats = brokenStats
        end
    end
    
    return updatedItem
end

-- Repair equipment
function Equipment.repairItem(itemData, amount)
    if not itemData or not itemData.durability then
        return itemData
    end
    
    -- Create a copy of the item to modify
    local updatedItem = {}
    for k, v in pairs(itemData) do
        updatedItem[k] = v
    end
    
    -- Calculate current durability
    local currentDurability = updatedItem.currentDurability or 0
    
    -- Apply repair amount
    updatedItem.currentDurability = math.min(updatedItem.durability, currentDurability + amount)
    
    -- Clear broken flag if fully repaired
    if updatedItem.currentDurability > 0 then
        updatedItem.broken = false
        
        -- Restore original stats if previously broken
        if updatedItem.originalStats then
            updatedItem.stats = updatedItem.originalStats
            updatedItem.originalStats = nil
        end
    end
    
    return updatedItem
end

-- Check if character meets requirements for equipment
function Equipment.meetsRequirements(character, itemData)
    if not character or not itemData then
        return false, "Invalid character or item"
    end
    
    -- Level requirement
    if itemData.levelRequirement and character.level < itemData.levelRequirement then
        return false, "Level too low"
    end
    
    -- Attribute requirements
    if itemData.attributeRequirements then
        for attr, value in pairs(itemData.attributeRequirements) do
            if not character.attributes[attr] or character.attributes[attr] < value then
                return false, "Insufficient " .. attr
            end
        end
    end
    
    -- Class requirements
    if itemData.classRequirement then
        local hasRequiredClass = false
        for _, characterClass in ipairs(character.classes or {}) do
            if characterClass.name == itemData.classRequirement then
                hasRequiredClass = true
                break
            end
        end
        
        if not hasRequiredClass then
            return false, "Class requirement not met"
        end
    end
    
    -- Skill requirements
    if itemData.skillRequirement then
        local hasSkill = character.skills and character.skills[itemData.skillRequirement]
        
        if not hasSkill then
            return false, "Required skill not learned"
        end
        
        -- Check skill level if specified
        if itemData.skillLevelRequirement and 
           character.skills[itemData.skillRequirement].level < itemData.skillLevelRequirement then
            return false, "Skill level too low"
        end
    end
    
    -- Quest requirements
    if itemData.questRequirement then
        local completedQuest = character.quests and 
                              character.quests.completed and 
                              character.quests.completed[itemData.questRequirement]
        
        if not completedQuest then
            return false, "Required quest not completed"
        end
    end
    
    return true, nil
end

-- Calculate proficiency bonus for an equipment
function Equipment.calculateProficiencyBonus(character, itemData)
    if not character or not itemData then
        return 1.0 -- Default multiplier
    end
    
    local proficiencyMultiplier = 1.0
    
    -- Get weapon or armor type
    local equipType = itemData.equipmentType
    local subType = itemData.equipmentSubType
    
    -- Check character proficiencies
    if character.proficiencies then
        if equipType == "weapon" and subType and character.proficiencies[subType] then
            proficiencyMultiplier = character.proficiencies[subType]
        elseif equipType == "armor" and subType and character.proficiencies[subType .. "_armor"] then
            proficiencyMultiplier = character.proficiencies[subType .. "_armor"]
        end
    end
    
    -- Check class-based proficiencies
    for _, characterClass in ipairs(character.classes or {}) do
        local classData = require("classes")[characterClass.name]
        if classData and classData.proficiencies then
            if equipType == "weapon" and subType and classData.proficiencies[subType] then
                -- Use the higher of character proficiency or class proficiency
                proficiencyMultiplier = math.max(proficiencyMultiplier, classData.proficiencies[subType])
            elseif equipType == "armor" and subType and classData.proficiencies[subType .. "_armor"] then
                proficiencyMultiplier = math.max(proficiencyMultiplier, classData.proficiencies[subType .. "_armor"])
            end
        end
    end
    
    -- Scale proficiency with level for primary class
    local primaryClass = nil
    for _, class in ipairs(character.classes or {}) do
        if class.isPrimary then
            primaryClass = class
            break
        end
    end
    
    if primaryClass then
        local levelBonus = primaryClass.level * 0.01 -- 1% per level
        proficiencyMultiplier = proficiencyMultiplier * (1 + levelBonus)
    end
    
    return proficiencyMultiplier
end

-- Get equipment quality level description
function Equipment.getQualityDescription(itemData)
    if not itemData then
        return "Normal"
    end
    
    local quality = itemData.quality or 0
    
    if quality >= 90 then
        return "Legendary"
    elseif quality >= 80 then
        return "Epic"
    elseif quality >= 70 then
        return "Rare"
    elseif quality >= 50 then
        return "Uncommon"
    else
        return "Common"
    end
end

-- Get hidden properties based on identification level
function Equipment.getHiddenProperties(itemData, identificationLevel)
    if not itemData then
        return {}
    end
    
    -- Default to 0 identification level (unidentified)
    identificationLevel = identificationLevel or 0
    
    local visibleProperties = {}
    
    -- Basic properties visible regardless of identification
    visibleProperties.name = identificationLevel > 0 and itemData.name or (itemData.unidentifiedName or "Unknown Item")
    visibleProperties.description = identificationLevel > 0 and itemData.description or (itemData.unidentifiedDescription or "This item's properties are unknown.")
    visibleProperties.equipmentType = itemData.equipmentType
    visibleProperties.equipmentSubType = itemData.equipmentSubType
    visibleProperties.weight = itemData.weight
    visibleProperties.durability = itemData.durability
    visibleProperties.currentDurability = itemData.currentDurability
    
    -- Level 1 identification (basic properties)
    if identificationLevel >= 1 then
        visibleProperties.levelRequirement = itemData.levelRequirement
        visibleProperties.classRequirement = itemData.classRequirement
        visibleProperties.value = itemData.value
        visibleProperties.quality = itemData.quality
        
        -- Basic stats
        if itemData.stats then
            visibleProperties.stats = {}
            for stat, value in pairs(itemData.stats) do
                if stat == "pattack" or stat == "pdefense" or stat == "health" then
                    visibleProperties.stats[stat] = value
                end
            end
        end
    end
    
    -- Level 2 identification (advanced properties)
    if identificationLevel >= 2 then
        visibleProperties.attributeRequirements = itemData.attributeRequirements
        
        -- All basic stats
        if itemData.stats then
            visibleProperties.stats = visibleProperties.stats or {}
            for stat, value in pairs(itemData.stats) do
                visibleProperties.stats[stat] = value
            end
        end
        
        -- Basic resistances
        if itemData.resistances then
            visibleProperties.resistances = {}
            for damageType, value in pairs(itemData.resistances) do
                if damageType == "physical" or damageType == "magical" then
                    visibleProperties.resistances[damageType] = value
                end
            end
        end
    end
    
    -- Level 3 identification (full identification)
    if identificationLevel >= 3 then
        -- Show all properties except hidden ones
        for prop, value in pairs(itemData) do
            if prop ~= "hiddenEffects" and prop ~= "secretRequirements" and 
               prop ~= "unidentifiedName" and prop ~= "unidentifiedDescription" and
               prop ~= "hiddenStats" then
                visibleProperties[prop] = value
            end
        end
    end
    
    -- Level 4 identification (secret identification)
    if identificationLevel >= 4 then
        -- Show all properties including hidden ones
        visibleProperties.hiddenEffects = itemData.hiddenEffects
        visibleProperties.secretRequirements = itemData.secretRequirements
        visibleProperties.hiddenStats = itemData.hiddenStats
    end
    
    return visibleProperties
end

-- Apply equipment effects when equipped
function Equipment.applyEquipEffects(character, itemId, slot)
    local itemData = Equipment.load(itemId)
    if not itemData then
        return false
    end
    
    -- Record the equip event for quest/achievement tracking
    if character.statistics then
        character.statistics.itemsEquipped = (character.statistics.itemsEquipped or 0) + 1
    end
    
    -- Check for on-equip effects
    if itemData.onEquip then
        -- This would be handled by your event system
        -- For now, we'll just return true
    end
    
    -- Check for set bonuses
    Equipment.checkSetBonuses(character)
    
    return true
end

-- Apply equipment effects when unequipped
function Equipment.applyUnequipEffects(character, itemId, slot)
    local itemData = Equipment.load(itemId)
    if not itemData then
        return false
    end
    
    -- Check for on-unequip effects
    if itemData.onUnequip then
        -- This would be handled by your event system
        -- For now, we'll just return true
    end
    
    -- Recheck set bonuses (set might be broken)
    Equipment.checkSetBonuses(character)
    
    return true
end

-- Check for equipment set bonuses
function Equipment.checkSetBonuses(character)
    if not character or not character.equipment then
        return
    end
    
    -- Clear existing set bonuses
    character.activeSetBonuses = character.activeSetBonuses or {}
    
    -- Track sets and pieces
    local setSets = {}
    
    -- Count pieces for each set
    for slot, itemId in pairs(character.equipment) do
        local itemData = Equipment.load(itemId)
        if itemData and itemData.setId then
            setSets[itemData.setId] = (setSets[itemData.setId] or 0) + 1
        end
    end
    
    -- Check which set bonuses should be active
    for setId, count in pairs(setSets) do
        local setData = ItemDatabase.getSetData(setId)
        if setData and setData.bonuses then
            -- Make sure we aren't already calculating this set
            character.activeSetBonuses[setId] = character.activeSetBonuses[setId] or {}
            
            -- Check each bonus tier
            for tier, bonus in pairs(setData.bonuses) do
                local requiredPieces = tonumber(tier)
                if requiredPieces and count >= requiredPieces then
                    -- Mark this tier as active
                    character.activeSetBonuses[setId][tier] = true
                else
                    -- Mark this tier as inactive
                    character.activeSetBonuses[setId][tier] = false
                end
            end
        end
    end
    
    -- Remove bonuses for sets that are no longer active
    for setId, _ in pairs(character.activeSetBonuses) do
        if not setSets[setId] then
            character.activeSetBonuses[setId] = nil
        end
    end
    
    return true
end

-- Get actual item properties with all bonuses applied
function Equipment.getEffectiveItemProperties(character, itemId)
    local itemData = Equipment.load(itemId)
    if not itemData then
        return nil
    end
    
    -- Create a copy of the item data
    local effectiveItem = {}
    for k, v in pairs(itemData) do
        effectiveItem[k] = v
    end
    
    -- Apply proficiency bonuses
    local proficiency = Equipment.calculateProficiencyBonus(character, itemData)
    
    -- Apply proficiency to stats
    if effectiveItem.stats then
        for stat, value in pairs(effectiveItem.stats) do
            if stat == "pattack" or stat == "mattack" then
                effectiveItem.stats[stat] = value * proficiency
            end
        end
    end
    
    -- Apply character-specific bonuses (based on skills, etc.)
    -- This would be an extension point for special character abilities
    
    return effectiveItem
end

-- Generate equipment tooltip
function Equipment.generateTooltip(itemData, character)
    if not itemData then
        return "Invalid item"
    end
    
    local tooltip = {}
    
    -- Name with quality color
    local quality = Equipment.getQualityDescription(itemData)
    table.insert(tooltip, quality .. " " .. itemData.name)
    
    -- Equipment type
    local typeStr = ""
    if itemData.equipmentType and itemData.equipmentSubType then
        typeStr = Equipment.types[itemData.equipmentType][itemData.equipmentSubType] or ""
    end
    table.insert(tooltip, typeStr)
    
    -- Base stats
    if itemData.stats then
        for stat, value in pairs(itemData.stats) do
            if stat == "pattack" then
                table.insert(tooltip, "Attack: " .. value)
            elseif stat == "pdefense" then
                table.insert(tooltip, "Defense: " .. value)
            elseif stat == "mattack" then
                table.insert(tooltip, "Magic Attack: " .. value)
            elseif stat == "mdefense" then
                table.insert(tooltip, "Magic Defense: " .. value)
            elseif stat == "health" then
                table.insert(tooltip, "Health: +" .. value)
            elseif stat == "mana" then
                table.insert(tooltip, "Mana: +" .. value)
            elseif stat == "stamina" then
                table.insert(tooltip, "Stamina: +" .. value)
            end
        end
    end
    
    -- Resistances
    if itemData.resistances then
        for damageType, value in pairs(itemData.resistances) do
            table.insert(tooltip, damageType:gsub("^%l", string.upper) .. " Resistance: " .. (value * 100) .. "%")
        end
    end
    
    -- Durability
    if itemData.durability then
        local current = itemData.currentDurability or itemData.durability
        table.insert(tooltip, "Durability: " .. current .. "/" .. itemData.durability)
    end
    
    -- Requirements
    if itemData.levelRequirement then
        table.insert(tooltip, "Required Level: " .. itemData.levelRequirement)
    end
    
    if itemData.classRequirement then
        table.insert(tooltip, "Required Class: " .. itemData.classRequirement)
    end
    
    if itemData.attributeRequirements then
        for attr, value in pairs(itemData.attributeRequirements) do
            table.insert(tooltip, "Required " .. attr:gsub("^%l", string.upper) .. ": " .. value)
        end
    end
    
    -- Special effects
    if itemData.effects then
        for effect, data in pairs(itemData.effects) do
            if type(data) == "table" and data.description then
                table.insert(tooltip, data.description)
            end
        end
    end
    
    -- Set information
    if itemData.setId then
        local setData = ItemDatabase.getSetData(itemData.setId)
        if setData then
            table.insert(tooltip, "")
            table.insert(tooltip, "Set: " .. setData.name)
            
            -- Show set bonuses
            if setData.bonuses and character and character.activeSetBonuses and character.activeSetBonuses[itemData.setId] then
                local equippedCount = 0
                for _, itemId in pairs(character.equipment) do
                    local equippedItem = Equipment.load(itemId)
                    if equippedItem and equippedItem.setId == itemData.setId then
                        equippedCount = equippedCount + 1
                    end
                end
                
                for tier, bonus in pairs(setData.bonuses) do
                    local requiredPieces = tonumber(tier)
                    if requiredPieces then
                        local active = equippedCount >= requiredPieces
                        local marker = active and "✓" or "○"
                        table.insert(tooltip, marker .. " (" .. tier .. ") " .. bonus.description)
                    end
                end
            end
        end
    end
    
    -- Hidden note for unidentified items
    if not itemData.identified then
        table.insert(tooltip, "")
        table.insert(tooltip, "This item has not been fully identified.")
    end
    
    -- Join with newlines
    return table.concat(tooltip, "\n")
end

-- Clear the equipment cache
function Equipment.clearCache()
    Equipment.cache = {}
end

-- Get all valid equipment slots
function Equipment.getAllSlots()
    local slots = {}
    for slot, name in pairs(Equipment.slots) do
        table.insert(slots, {id = slot, name = name})
    end
    return slots
end

-- Check if equipment is two-handed
function Equipment.isTwoHanded(itemData)
    if not itemData then
        return false
    end
    
    -- Explicit two-handed flag
    if itemData.twoHanded then
        return true
    end
    
    -- Check weapon types that are typically two-handed
    if itemData.equipmentType == "weapon" then
        local twoHandedTypes = {
            greatsword = true,
            polearm = true,
            staff = true,
            bow = true,
            crossbow = true
        }
        
        if twoHandedTypes[itemData.equipmentSubType] then
            return true
        end
    end
    
    return false
end

return Equipment