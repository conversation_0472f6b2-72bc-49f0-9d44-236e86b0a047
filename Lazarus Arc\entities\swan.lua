local Swan = {
    id = "swan",
    name = "<PERSON>",
    type = "bird",
    
    -- Entity categories
    categories = {"animal", "bird", "flying", "water", "social"},
    
    -- Threat and food categories
    threatCategories = {"player", "predator"},
    foodCategories = {"plant", "insect", "fish", "food"},
    
    -- Stats
    maxHealth = 50,
    health = 50,
    maxStamina = 80,
    stamina = 80,
    speed = 1.8,
    
    -- Flight properties
    flight = {
        maxHeight = 12,
        minHeight = 1,
        ascentSpeed = 0.8,
        descentSpeed = 1.5,
        hoverHeight = 2,
        currentHeight = 2,
        wingFlapRate = 0.12,
        soarChance = 0.3
    },
    
    -- Water properties
    water = {
        swimSpeed = 1.5,
        wadeDepth = 1,
        forageRange = 5
    },
    
    -- Behaviors
    behaviors = {"forage", "swim", "territorial", "roost"},
    
    -- Behavior configurations
    behaviorConfigs = {
        forage = {
            moveSpeed = 1.2,
            searchRadius = 10,
            preferredFood = {"plant", "insect", "fish"},
            successChance = 0.7
        },
        swim = {
            moveSpeed = 1.5,
            preferredTerrain = "water",
            groupSpacing = 3,
            directionChange = 0.15
        },
        territorial = {
            territoryRadius = 20,
            chaseRadius = 15,
            warningRadius = 8,
            aggressionLevel = 0.9,
            preferredTime = "day"
        },
        roost = {
            startTime = "dusk",
            endTime = "dawn",
            roostHeight = {2, 4},
            healthRegen = 0.03,
            staminaRegen = 0.1
        }
    },
    
    -- Special abilities
    abilities = {
        charge = {
            speed = 2.5,
            damage = 15,
            duration = 1.5,
            cooldown = 4,
            staminaCost = 20
        },
        hiss = {
            range = 12,
            duration = 1,
            cooldown = 2,
            effect = "intimidate"
        },
        flap = {
            damage = 8,
            duration = 0.5,
            cooldown = 1,
            staminaCost = 5
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "swan",
        scale = 1.1,
        animations = {
            "idle", "fly", "swim", "charge", "hiss", "roost"
        },
        variants = {
            "mute", "trumpeter", "tundra", "black"
        }
    },
    
    -- Sound effects
    sounds = {
        hiss = "swan_hiss",
        wingFlap = "swan_wing_flap",
        call = "swan_call"
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.7, quantity = {2, 3}},
        {id = "feather", chance = 0.8, quantity = {2, 4}},
        {id = "egg", chance = 0.4, quantity = {1, 1}},
        {id = "down", chance = 0.5, quantity = {2, 3}}
    }
}

-- Initialize the entity
function Swan.init(entity, world)
    -- Copy all fields from Swan template to entity instance
    for k, v in pairs(Swan) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random swan variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Swan.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update flight properties
    if entity.flight then
        -- Update wing flapping based on height and activity
        if entity.flight.currentHeight > entity.flight.minHeight then
            if entity.flight.soarChance > math.random() then
                entity.flight.wingFlapRate = 0.06
            else
                entity.flight.wingFlapRate = 0.12
            end
        else
            entity.flight.wingFlapRate = 0.2
        end
    end

    -- Update water properties
    if entity.water then
        -- Adjust aggression based on time of day
        if world and world.timeOfDay == "dawn" or world.timeOfDay == "dusk" then
            entity.behaviorConfigs.territorial.aggressionLevel = 1.0
        else
            entity.behaviorConfigs.territorial.aggressionLevel = 0.9
        end
    end
end

return Swan 