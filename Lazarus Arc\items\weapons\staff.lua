-- items/weapons/staff.lua

local Weapon = {
    id = "staff",
    name = "Staff",
    description = "A wooden staff that can be used for both melee combat and channeling magic.",
    type = "staff",
    category = "weapon",
    stackable = false,

    stats = {
        attack_power = 6.0,
        attack_speed = 0.9,
        magic_power = 8.0,
        durability = 80.0,
        repair_cost = 20,
        weight = 3.0,
    },

    damage_type = "physical",
    material = "wood",

    sprite = "res://Sprite/items/weapons/staff.png",
    size = { width = 64, height = 64 },

    effects = {
        magic_boost = 0.05,
        mana_efficiency = 0.03
    },

    meta = {
        rarity = "common"
    },

    take_damage = function(self, amount)
        self.stats.durability = math.max(0, self.stats.durability - amount)
        if self.stats.durability == 0 then
            print(self.name .. " is broken! Penalties applied.")
            -- Potential penalties could be applied here
        end
    end,

    repair = function(self)
        local repair_amount = 80 - self.stats.durability
        self.stats.durability = 80
        print(self.name .. " repaired by " .. repair_amount .. " points.")
    end,
    
    onEquip = function(self, user)
        if user and user.stats then
            -- Apply magic boost
            if user.stats.magic_power then
                user.stats.magic_power = user.stats.magic_power * (1 + self.effects.magic_boost)
            end
            
            -- Apply mana efficiency
            if user.stats.mana_cost_modifier then
                user.stats.mana_cost_modifier = user.stats.mana_cost_modifier * (1 - self.effects.mana_efficiency)
            else
                user.stats.mana_cost_modifier = 1 - self.effects.mana_efficiency
            end
            
            print(user.name .. " equipped the " .. self.name .. ", gaining magical bonuses.")
        end
    end,
    
    onUnequip = function(self, user)
        if user and user.stats then
            -- Remove magic boost
            if user.stats.magic_power then
                user.stats.magic_power = user.stats.magic_power / (1 + self.effects.magic_boost)
            end
            
            -- Remove mana efficiency
            if user.stats.mana_cost_modifier then
                user.stats.mana_cost_modifier = user.stats.mana_cost_modifier / (1 - self.effects.mana_efficiency)
            end
            
            print(user.name .. " unequipped the " .. self.name .. ", losing magical bonuses.")
        end
    end
}

return Weapon
