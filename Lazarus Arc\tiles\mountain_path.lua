-- tiles/mountain_path.lua
local MountainPathTile = {
    id = "mountain_path",
    name = "Mountain Path",
    passable = true, -- It's a path, so it's meant to be traversed
    variants = 6, -- e.g., Narrow Goat Track, Steep Switchbacks, Carved Rock Steps, Eroded Trail, Marked Pilgrim Path, High Altitude Trail

    -- Entities found on mountain paths
    spawns = {"mountain_goat", "traveler_hardy", "pilgrim", "trail_marker", "loose_scree_pile", "mountain_wind_spirit"},

    -- Movement properties
    movementSpeed = 1.1, -- Faster than surrounding mountain terrain, but maybe slower than flat paths due to slope/condition

    -- Path-specific properties (Similar to road/stone_path but adapted)
    pathType = "dirt_and_rock", -- Default surface type
    pathQuality = 0.6, -- How well-maintained (0-1)
    steepness = 0.4, -- Average steepness of this path segment (0-1)
    hazardRating = 0.3, -- Risk of environmental hazards like rockfalls or drop-offs (0-1)
    isMarked = true, -- Are there trail markers?
    altitude = 1.5, -- Relative altitude, affecting weather/spawns

    -- Interaction function
    interact = function(tile, entity)
        -- Check trail markers
        if tile.isMarked and math.random() < 0.5 then
             local markerType = tile.markerType or "cairn" -- <PERSON><PERSON>n, blaze, signpost
             local destination = tile.markerDestination or "Higher Elevation"
             return {
                 success = true,
                 message = "You spot a " .. markerType .. " marking the path towards " .. destination .. ".",
                 effects = {{type="confirm_path_direction", destination=destination}}
             }
        end

        -- Assess path condition and hazards
        if entity.skills and entity.skills.survival > 1 then
             local conditionDesc = "The path seems stable here."
             if tile.pathQuality < 0.4 then conditionDesc = "The path is eroded and poorly maintained here." end
             local hazardDesc = ""
             if tile.hazardRating > 0.6 then hazardDesc = " Be wary; the drop-off is steep and there's evidence of recent rockfalls."
             elseif tile.hazardRating > 0.3 then hazardDesc = " Watch your footing near the edge." end
             return { success = false, message = conditionDesc .. hazardDesc }
        end

        -- Look for tracks
         if entity.skills and entity.skills.tracking > 0 and math.random() < 0.3 then
              local tracks = {"goat_tracks", "boot_prints_worn", "unknown_large_animal_scat"}
              local foundTrack = tracks[math.random(#tracks)]
              return { success = true, message = "You notice " .. foundTrack .. " along the path.", effects={{type="gain_tracking_clue", clue=foundTrack}} }
         end

        -- General examination
        return {
            success = false,
            message = "A narrow path winds its way up the mountainside, offering passage through the difficult terrain."
        }
    end,

    -- Weather effects (Significant impact in mountains)
    weatherEffects = {
        rain = function(tile)
            tile.isWet = true
            tile.slipperiness = 0.5 -- Slippery when wet
            tile.pathQuality = (tile.pathQuality or 0.6) - 0.02 -- Rain causes erosion
            if tile.pathQuality < 0.3 and math.random() < 0.1 then
                 return "trigger_event", "path_washout" -- Path might become impassable
            end
            return "visual_effect", "wet_mountain_path"
        end,
        
        snow = function(tile) -- Snow can make paths dangerous or invisible
            tile.isSnowCovered = true
            tile.snowDepth = (tile.snowDepth or 0) + 0.3
            tile.slipperiness = 0.6
            tile.movementSpeedModifier = 0.7 - ((tile.snowDepth or 0) * 0.2) -- Speed decreases with depth
            if tile.snowDepth > 1.0 then tile.isMarked = false end -- Markers might be hidden
            return "visual_effect", "snowy_mountain_path"
        end,

        freeze = function(tile) -- Ice is extremely dangerous
             if tile.isWet or (tile.snowDepth or 0) > 0.1 then
                 tile.isIcy = true
                 tile.slipperiness = 0.9 -- Very slippery
                 tile.movementSpeedModifier = 0.5 -- Very slow and dangerous
                 return "visual_effect", "icy_mountain_path"
             end
             return nil
        end,

        wind = function(tile) -- Strong winds in exposed sections
             -- Assuming world context is available if needed, e.g., via tile.worldRef
             local currentWindStrength = (tile.worldRef and tile.worldRef.windStrength) or 0
             if (tile.altitude or 1.5) > 2.0 and currentWindStrength > 0.8 then
                 -- Apply push effect? Reduce movement speed? Increase slip chance?
                 return "apply_effect_area", {type="strong_mountain_wind", intensity=0.5}
             end
             return nil
        end,

        sun = function(tile) -- Drying / Melting
             tile.isWet = false
             tile.isIcy = false
             tile.slipperiness = 0.1 -- Base slipperiness on uneven ground
             tile.movementSpeedModifier = 1.0
             -- Assuming world context is available if needed, e.g., via tile.worldRef
             local currentTemp = (tile.worldRef and tile.worldRef.temperature) or 0
             if tile.isSnowCovered and currentTemp > 2 then
                 tile.snowDepth = math.max(0, (tile.snowDepth or 0) - 0.2)
                 if tile.snowDepth == 0 then tile.isSnowCovered = false; tile.isMarked = true end -- Reset snow cover
             end
             return "remove_visual_effect", {"wet_mountain_path", "snowy_mountain_path", "icy_mountain_path"}
        end
    },

     -- Time effects
    timeEffects = {
        night = function(tile)
             tile.visibilityModifier = 0.5 -- Harder to see the path and hazards
             tile.hazardRating = (tile.hazardRating or 0.3) * 1.2 -- More dangerous at night
             tile.ambientSound = "mountain_wind_night"
            return nil
        end,
        
        day = function(tile)
             tile.visibilityModifier = 1.0
             tile.hazardRating = 0.3 -- Reset base hazard rating
             tile.ambientSound = "mountain_wind_day"
            return nil
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {0.6, 0.5, 0.4}, -- Earthy/rocky path color
        texture = "path_dirt_rock_mix", -- Default texture
        width = 0.6, -- Narrower than a road (tile units)
        variants = {
            {name = "Narrow Goat Track", pathType="dirt", width=0.4, pathQuality=0.4},
            {name = "Steep Switchbacks", shape="zigzag", steepness=0.7, texture="path_gravel"},
            {name = "Carved Rock Steps", pathType="stone_steps", texture="path_rock_steps", movementSpeed=1.0}, -- Easier footing?
            {name = "Eroded Trail", pathType="dirt_eroded", pathQuality=0.3, hazardRating=0.5},
            {name = "Marked Pilgrim Path", overlay_objects={{name="small_shrine_marker", chance=0.2}}, isMarked=true},
            {name = "High Altitude Trail", altitude=2.5, texture="path_bare_rock"} -- Higher up, more exposed
        },
        decoration_objects = {
            {name = "trail_marker_cairn", chance = 0.15, condition_property="isMarked"}, -- Changed condition=tile.isMarked
            {name = "loose_rock_on_path", chance = 0.2},
            {name = "exposed_tree_root", chance = 0.15}
        },
         special_effects = {
             edge_blur = {base_intensity=0.0, intensity_modifier="hazardRating", when="near_dropoff"}, -- Changed intensity_scale=tile.hazardRating
             dust_kickup = {type="particle", density=0.2, when="moving_fast"}
         },
          weather_overlays = {
             wet_muddy = {texture="path_muddy", shininess=0.4, when="isWet"},
             snow_drift = {coverage_property = "snowDepth", base_coverage=0.5, when="isSnowCovered"}, -- Changed coverage = tile.snowDepth or 0.5
             ice_slick = {texture="ice_thin_layer", shininess=0.7, when="isIcy"}
         },
         connection_rules = "path" -- Use path connection logic [Similar to stone_path: 177]
    },
    
    -- Audio properties
    ambientSound = "mountain_wind_day",
    footstepSound = "path_gravel_step", -- Default sound, can change with variant
     specialSounds = {
        {name = "rock_clatter_nearby", trigger = "random", base_chance = 0.05, chance_modifier="hazardRating"}, -- Changed chance = 0.05 * tile.hazardRating
        {name = "wind_gust_strong", trigger = "windy", chance = 0.3},
        {name = "foot_slip_scrape", trigger = "on_walk_slip", chance = 1.0}
    },
    -- Audio Modifiers
     audioModifiers = {
         base_wind_intensity = 0.5, wind_intensity_modifier_altitude = 0.2 -- Changed wind_intensity = 0.5 + tile.altitude * 0.2
     },

    -- Path segments (Similar to road/stone_path [cite: 31-32, 177-178])
    segments = {
        straight = {rotation = {0, 90, 180, 270}},
        corner = {rotation = {0, 90, 180, 270}},
        switchback_left = {rotation = {0, 90, 180, 270}}, -- Special segment for switchbacks
        switchback_right = {rotation = {0, 90, 180, 270}},
        t_junction = {rotation = {0, 90, 180, 270}},
        end_cap = {rotation = {0, 90, 180, 270}}
    },

    -- Connections to other tiles
    connects_to = {
        "mountain_path", -- Itself
        "mountain_base", -- Often starts/ends here
        "cave_entrance", -- Path might lead to a cave
        "mountain_peak", -- Might lead to the summit
        "lookout_point", -- Scenic spots
        "shrine_mountain", -- Religious sites
        "mountain_stream_crossing" -- Where path crosses water
    },

    -- Pathfinding properties (Similar to road/stone_path [cite: 33-34, 179-180])
     pathfinding = {
        preferred_path = true, -- NPCs strongly prefer paths in mountains
        base_travel_cost = 0.8, cost_divisor_property="movementSpeed", -- Changed travel_cost = 0.8 / tile.movementSpeed
        base_slope_penalty = 1.0, slope_penalty_modifier="steepness", slope_penalty_multiplier=1.5, -- Changed slope_penalty = 1.0 + tile.steepness * 1.5
        base_hazard_modifier = 1.0, hazard_modifier_property="hazardRating" -- Changed hazard_modifier = 1.0 + tile.hazardRating
    },

    -- Effects on entities that walk on this tile
    onWalk = function(tile, entity)
        local sound = tile.footstepSound or "path_gravel_step"
        -- Adjust sound based on pathType variant maybe?
        local steepness = tile.steepness or 0.4
        local fatigue = 0.12 * (1 + steepness * 1.5) -- Fatigue based on steepness
        local effects = {}
        local speedMod = tile.movementSpeedModifier or 1.0
        local slipChance = tile.slipperiness or 0.1

        -- Modify speed based on weather/conditions
        entity.currentSpeedModifier = speedMod 

        -- Check for slipping
        if (tile.isWet or tile.isIcy or tile.isSnowCovered) and math.random() < slipChance * 0.6 then
            sound = "foot_slip_scrape"
             table.insert(effects, {type="apply_status", status="slipping", duration=2, magnitude=slipChance})
             fatigue = fatigue * 1.2 -- Extra effort to recover from slip
             return { sound=sound, volume=0.9, message="You slip on the treacherous path!", effects=effects }
        end

         table.insert(effects, {type="increase_fatigue", amount = fatigue})

        return {
            sound = sound,
            volume = 0.85,
            effects = effects
        }
    end,
    
    -- Effect when discovering mountain path
    onDiscover = function(tile, entity)
        if entity.journal and entity.journal.addDiscovery then
             entity.journal.addDiscovery({
                type = "pathway",
                name = "Mountain Path",
                location = {x = tile.x, y = tile.y},
                notes = "A path providing passage through the mountains. Condition: " .. (tile.pathQuality or 0.6) .. "."
            })
        end
        return {
            message = "You find a path winding up the mountain.",
            effects = {
                {type = "reveal_map_line", direction = tile.direction or {x=0,y=1}, distance = 12} -- Reveal path ahead
            }
        }
    end
}

function MountainPathTile.init(world)
    print("Mountain Path tile module initialized")
    -- Register with pathfinding, mapping systems
     if world and world.systems and world.systems.pathfinding then
        world.systems.pathfinding:registerPathType("mountain_path", 0.8) -- Register base cost
    end
     if world and world.systems and world.systems.mapping then
        world.systems.mapping:registerPathType("mountain_path")
    end
end

return MountainPathTile
