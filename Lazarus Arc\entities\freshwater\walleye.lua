local Walleye = {
    id = "walleye",
    name = "Walleye",
    type = "walleye",

    -- Entity categories
    categories = { "animal", "fish", "medium", "predator" },

    -- Threat categories
    threatCategories = { "predator", "large" }, -- Larger predators
    foodCategories = { "fish", "prey" },

    -- Stats
    maxHealth = 25,
    health = 25,
    speed = 4.0,  -- Faster and stronger

    -- Behaviors
    behaviors = { "hunt", "flee" }, -- No schooling

    -- Behavior configurations
    behaviorConfigs = {
        hunt = {
            huntRadius = 10,
            chaseRadius = 15,
            attackRadius = 2,
            preferredTargets = { "perch", "bluegill", "crappie" }
        },
        flee = {
            useCategories = true,
            moveSpeed = 6.0,
            detectionRadius = 12
        }
    },

    -- Appearance
    appearance = {
        sprite = "walleye", -- Replace with your walleye sprite
        scale = 1.3,
        animations = {
            "swim",
            "flee",
            "attack" -- Add an attack animation
        }
    },

    -- Sound effects
    sounds = {
        flop = "fish_flop"
    },

    -- Loot drops
    drops = {
        {
            id = "walleye_meat",
            chance = 1.0,
            quantity = { 1, 2 }
        }
    }
}

-- Initialize the walleye entity
function Walleye.init(entity, world)
    entity.position = entity.position or { x = 0, y = 0 }
    entity.velocity = entity.velocity or { x = 0, y = 0 }

    -- Initialize behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.init then
            behavior.init(entity, entity.behaviorConfigs[behaviorName])
        end
    end

    return entity
end

return Walleye