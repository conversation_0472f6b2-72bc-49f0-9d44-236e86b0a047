-- utils/logger.lua
local Logger = {
    maxFiles = 7,
    currentFile = 1,
    logFile = nil
}

function Logger.init()
    -- Rotate log files if needed
    for i = Logger.maxFiles, 1, -1 do
        local oldName = string.format("logs/debug_%d.log", i)
        local newName = string.format("logs/debug_%d.log", i + 1)
        if love.filesystem.getInfo(oldName) then
            if i == Logger.maxFiles then
                love.filesystem.remove(oldName)
            else
                love.filesystem.write(newName, love.filesystem.read(oldName))
                love.filesystem.remove(oldName)
            end
        end
    end
    
    -- Create new log file
    Logger.logFile = string.format("logs/debug_1.log")
    love.filesystem.write(Logger.logFile, "=== New Game Session Started ===\n")
end

function Logger.log(message)
    if not Logger.logFile then Logger.init() end
    local timestamp = os.date("%Y-%m-%d %H:%M:%S")
    local logMessage = string.format("[%s] %s\n", timestamp, message)
    love.filesystem.append(Logger.logFile, logMessage)
    
    -- Only print to console if it's not a debug message
    if not message:match("^DEBUG:") and not message:match("^ERROR: getChunkAt") then
        print(message)
    end
end

return Logger 