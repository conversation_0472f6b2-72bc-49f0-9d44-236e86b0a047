-- behaviors/berserk.lua
-- Berserk behavior for entities becoming temporarily aggressive with increased combat capability

local BerserkBehavior = {}  -- create an empty table

BerserkBehavior.id = "berserk"
BerserkBehavior.name = "Berserk Behavior"

-- Configuration parameters
BerserkBehavior.defaultConfig = {
    triggerHealthThreshold = 0.3, -- Health percentage to trigger berserk
    berserkDuration = {8, 15},    -- Duration of the berserk state
    attackMultiplier = 1.5,       -- Damage multiplier during berserk
    speedMultiplier = 1.4,        -- Speed multiplier during berserk
    defensePenalty = 0.5,         -- Defense reduction during berserk
    cooldownTime = 20,            -- Cooldown time before can go berserk again
    chaseRadius = 15,             -- Radius to chase targets when berserk
    frenzyChance = 0.2,           -- Chance to randomly attack nearby entities
    returnToNormalAfter = true    -- Return to normal behavior after berserk period
}

-- Initialize the behavior
function BerserkBehavior.init(entity, customConfig)
    entity.berserkState = entity.berserkState or {
        isBerserk = false,
        berserkTimer = 0,
        cooldownTimer = 0,
        config = {}
    }

    local config = entity.berserkState.config
    for k, v in pairs(BerserkBehavior.defaultConfig) do
        config[k] = v
    end

    if customConfig then
        for k, v in pairs(customConfig) do
            config[k] = v
        end
    end
end

-- Update function called every game tick
function BerserkBehavior.update(entity, world, dt)
    local state = entity.berserkState
    local config = state.config

    -- Handle cooldown
    if state.cooldownTimer > 0 then
        state.cooldownTimer = state.cooldownTimer - dt
    end

    -- Check if entity should enter berserk state
    if entity.health / entity.maxHealth <= config.triggerHealthThreshold and state.cooldownTimer <= 0 then
        state.berserkTimer = 10 -- Berserk duration (seconds)
        state.cooldownTimer = config.cooldownTime
        state.isBerserk = true
    end

    -- Update berserk state
    if state.isBerserk then
        state.berserkTimer = state.berserkTimer - dt

        -- Increase aggression by attacking targets
        BerserkBehavior.attackTargets(entity, world)

        if state.berserkTimer <= 0 then
            state.isBerserk = false
            state.cooldownTimer = config.cooldownTime
        end
    end
end

-- Find and attack targets aggressively
function BerserkBehavior.attackTargets(entity, world)
    -- Implementation details...
end

-- Handle frenzy attacks on random nearby entities
function BerserkBehavior.frenzyAttack(entity, world)
    -- Implementation details...
end

return BerserkBehavior
