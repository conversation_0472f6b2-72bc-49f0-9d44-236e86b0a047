-- behaviors/quest_giver.lua
-- Quest giver behavior for entities that provide and manage quests

local QuestGiverBehavior = {}  -- create an empty table

QuestGiverBehavior.id = "quest_giver"
QuestGiverBehavior.name = "Quest Giver Behavior"

-- Configuration parameters
QuestGiverBehavior.defaultConfig = {
    interactionRadius = 6,           -- Radius to interact with players
    questCooldown = {60, 120},       -- Cooldown before offering another quest
    questList = {},                  -- List of available quests
    dynamicQuests = true,            -- Dynamically generate quests
    rewardItems = {},                -- Rewards given after completing quests
    dialogueOptions = {},            -- Dialogue interactions with players
    rememberPlayers = true,          -- Remember players who've completed quests
    maxActiveQuests = 3,             -- Maximum concurrent quests offered
    idleActionsBetweenQuests = true   -- Perform idle actions when no active quests
}

-- Initialize the behavior
function QuestGiverBehavior.init(entity, customConfig)
    entity.questGiverState = entity.questGiverState or {
        activeQuests = {},
        completedPlayers = {},
        dialogueState = {},
        config = {}
    }

    local config = entity.questGiverState.config
    for k, v in pairs(QuestGiverBehavior.defaultConfig) do
        config[k] = v
    end

    if customConfig then
        for k, v in pairs(customConfig) do
            config[k] = v
        end
    end
end

-- Update function called every game tick
function QuestGiverBehavior.update(entity, world, dt)
    local state = entity.questGiverState
    local config = state.config

    -- Manage available quests
    QuestGiverBehavior.updateQuests(entity, world, dt)

    -- Interaction with players
    QuestGiverBehavior.interactWithPlayers(entity, world)

    -- Idle animations/actions when no active interactions
    if config.idleActionsWhenNoInteraction and not state.interacting then
        QuestGiverBehavior.performIdleActions(entity, world, dt)
    end
end

-- Update available quests (placeholder)
function QuestGiverBehavior.updateQuests(entity, world, dt)
    -- Implementation details...
end

-- Handle interactions with players (placeholder)
function QuestGiverBehavior.interactWithPlayers(entity, world)
    -- Implementation details...
end

-- Provide quest rewards (placeholder)
function QuestGiverBehavior.provideRewards(entity, player, quest)
    -- Implementation details...
end

-- Remember player interactions (placeholder)
function QuestGiverBehavior.rememberPlayer(entity, player)
    -- Implementation details...
end

return QuestGiverBehavior
