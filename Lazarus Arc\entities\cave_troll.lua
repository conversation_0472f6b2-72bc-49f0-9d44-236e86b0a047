-- entities/cave_troll.lua
-- Underground cave troll with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local CaveTroll = {
    id = "cave_troll",
    name = "Cave Troll",
    type = "cave_troll",
    shape = {
        {0, -12}, {8, -8}, {12, 0}, {8, 8},
        {0, 12}, {-8, 8}, {-12, 0}, {-8, -8}
    },
    size = 16,

    -- Entity categories
    categories = {"underground", "troll", "large", "aggressive", "territorial"},
    threatCategories = {"player", "dwarf", "miner", "surface_dweller"},
    
    -- Base stats (massive underground brute)
    maxHealth = 250,
    health = 250,
    maxStamina = 100,
    stamina = 100,
    speed = 1.8,  -- Slow but powerful
    attack = 40,
    defense = 25,
    stone_resistance = 20,
    regeneration = 5,
    cave_adaptation = 25,
    
    -- Behaviors
    behaviors = {"cave_patrol", "territorial_rage", "stone_throw", "regenerate"},
    behaviorConfigs = {
        cave_patrol = {
            patrolRadius = 40,
            cavePreference = true,
            territorialBonus = 1.5,
            lowLightVision = true
        },
        territorial_rage = {
            triggerRadius = 20,
            rageBonus = 2.0,
            rageDuration = 15,
            intimidationAura = true
        },
        stone_throw = {
            throwRange = 25,
            stoneDamage = 1.8,
            stunChance = 0.4,
            rockSize = "large"
        },
        regenerate = {
            healRate = 3,
            healInterval = 5,
            combatRegeneration = 0.5,
            stoneEating = true
        }
    },
    
    -- Enhanced variant system for cave trolls
    variantChances = {
        normal = 0.60,          -- 60% stone troll
        shiny = 0.30,           -- 30% crystal troll (shiny)
        rare = 0.08,            -- 8% magma troll (rare)
        legendary = 0.02        -- 2% mountain heart troll (legendary)
    },
    
    variants = {
        normal = {
            name = "Stone Troll",
            description = "A massive troll with stone-like skin that dwells in caves",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.6, 0.5, 0.4, 1.0}  -- Stone gray-brown
            }
        },
        
        shiny = {
            name = "Crystal Troll",
            description = "A troll with crystalline growths that reflect light",
            statModifiers = {
                maxHealth = 1.4,    -- 350 health
                defense = 1.6,      -- 40 defense
                regeneration = 1.8, -- 9 regeneration
                crystal_armor = 2.0,
                light_reflection = 1.5
            },
            appearanceModifiers = {
                scale = 1.1,
                glow = true,
                colorTint = {0.8, 0.9, 1.2, 1.0},  -- Crystalline blue-white
                crystal_growths = true,
                light_refraction = true,
                prismatic_aura = true
            },
            soundModifiers = {
                pitch = 1.1,
                volume = 1.2,
                crystalline = true
            }
        },
        
        rare = {
            name = "Magma Troll",
            description = "A troll infused with volcanic energy and molten rock",
            statModifiers = {
                maxHealth = 1.8,    -- 450 health
                attack = 1.6,       -- 64 attack
                defense = 1.4,      -- 35 defense
                fire_immunity = 5.0,
                magma_blood = 2.0,
                volcanic_power = 3.0
            },
            appearanceModifiers = {
                scale = 1.2,
                colorTint = {1.2, 0.6, 0.3, 1.0},  -- Molten red-orange
                magma_veins = true,
                volcanic_glow = true,
                lava_drool = true,
                heat_distortion = true
            },
            soundModifiers = {
                pitch = 0.8,
                volume = 1.4,
                reverb = true,
                volcanic = true
            }
        },
        
        legendary = {
            name = "Mountain Heart Troll",
            description = "An ancient troll connected to the very heart of the mountain",
            statModifiers = {
                maxHealth = 2.5,    -- 625 health
                attack = 2.0,       -- 80 attack
                defense = 2.2,      -- 55 defense
                regeneration = 3.0, -- 15 regeneration
                mountain_connection = 10.0,
                earth_mastery = 5.0
            },
            appearanceModifiers = {
                scale = 1.5,
                glow = true,
                colorTint = {1.0, 0.8, 0.6, 1.0},  -- Ancient golden stone
                mountain_crown = true,
                earth_aura = "legendary",
                geological_integration = true,
                tectonic_presence = true
            },
            soundModifiers = {
                pitch = 0.5,
                volume = 1.8,
                reverb = true,
                echo = true,
                mountain_resonance = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "troll_hide", chance = 0.8, quantity = {2, 4}},
        {id = "stone_chunk", chance = 0.9, quantity = {3, 6}},
        {id = "troll_bone", chance = 0.7, quantity = {1, 3}},
        {id = "regeneration_essence", chance = 0.6, quantity = {1, 2}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "crystal_troll_shard", chance = 0.9, quantity = {2, 4}},
            {id = "prismatic_crystal", chance = 0.8, quantity = {1, 2}},
            {id = "light_refraction_gem", chance = 0.7, quantity = {1, 1}},
            {id = "crystal_armor_fragment", chance = 0.6, quantity = {1, 2}}
        },
        rare = {
            {id = "magma_troll_heart", chance = 0.9, quantity = {1, 1}},
            {id = "volcanic_essence", chance = 0.8, quantity = {1, 2}},
            {id = "molten_stone", chance = 0.8, quantity = {2, 3}},
            {id = "fire_immunity_gland", chance = 0.6, quantity = {1, 1}}
        },
        legendary = {
            {id = "mountain_heart_core", chance = 0.95, quantity = {1, 1}},
            {id = "earth_mastery_crystal", chance = 0.9, quantity = {1, 1}},
            {id = "tectonic_essence", chance = 0.8, quantity = {1, 2}},
            {id = "mountain_crown_fragment", chance = 0.7, quantity = {1, 1}},
            {id = "geological_wisdom_stone", chance = 0.6, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "cave_troll",
        scale = 1.4,
        animations = {
            "stomp", "stone_throw", "rage", "regenerate", "cave_patrol"
        },
        variants = {
            "stone_troll", "crystal_troll", "magma_troll", "mountain_heart_troll"
        }
    },
    
    -- Sound effects with underground troll characteristics
    sounds = {
        troll_roar = {
            file = "cave_troll_roar",
            synth = {
                instrument = "organ",
                notes = {"C1", "F1", "C2"},
                durations = {1.0, 0.8, 1.2},
                volume = 1.0,
                cave_echo = true
            }
        },
        stone_throw = {
            file = "stone_throw",
            synth = {
                instrument = "percussion",
                notes = {"D2", "G2"},
                durations = {0.3, 0.5},
                volume = 0.8,
                rocky = true
            }
        },
        stomp = {
            file = "troll_stomp",
            synth = {
                instrument = "percussion",
                notes = {"C1"},
                duration = 0.4,
                volume = 1.2,
                ground_shake = true
            }
        },
        regeneration = {
            file = "troll_regeneration",
            synth = {
                instrument = "synthesizer",
                notes = {"F2", "A2", "C3"},
                durations = {0.8, 0.6, 1.0},
                volume = 0.5,
                healing = true
            }
        }
    },
    
    -- Special cave troll abilities
    abilities = {
        stone_skin = {
            type = "passive",
            description = "Natural stone armor provides high damage reduction",
            effect = "damage_reduction"
        },
        regeneration = {
            type = "passive",
            description = "Slowly heals over time, faster when eating stone",
            effect = "health_regeneration"
        },
        cave_adaptation = {
            type = "passive",
            description = "Perfect vision in darkness and bonus in underground areas",
            effect = "underground_bonus"
        },
        boulder_throw = {
            type = "active",
            description = "Hurls massive stones at distant enemies",
            effect = "ranged_attack",
            cooldown = 10
        },
        territorial_rage = {
            type = "active",
            description = "Enters berserk state when territory is invaded",
            effect = "rage_mode",
            cooldown = 30
        }
    }
}

-- Initialize the cave troll entity using enhanced template
function CaveTroll.init(entity, world)
    -- Copy all fields from CaveTroll template to entity instance
    for k, v in pairs(CaveTroll) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return CaveTroll
