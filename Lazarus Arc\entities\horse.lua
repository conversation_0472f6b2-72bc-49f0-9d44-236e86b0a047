-- entities/horse.lua
local Horse = {
    id = "horse",
    name = "Horse",
    type = "horse",
    shape = {
        {0, -1}, {0.7, -0.7}, {1, 0}, {0.7, 0.7},
        {0, 1}, {-0.7, 0.7}, {-1, 0}, {-0.7, -0.7}
    },
    size = 15,
    
    -- Entity categories
    categories = {"animal", "mammal", "large", "mountable"},
    
    -- Threat and food categories
    threatCategories = {"predator", "monster"},
    foodCategories = {"grass", "hay", "apple"},
    
    -- Stats
    maxHealth = 80,
    health = 80,
    maxStamina = 100,
    stamina = 100,
    speed = 3.0,
    maxSpeed = 5.0,
    
    -- Mount properties
    mount = {
        maxRiderWeight = 200,
        saddleRequired = true,
        mountSpeed = 4.0,
        gallopSpeed = 6.0,
        jumpHeight = 2.0,
        staminaDrain = 0.5,
        staminaRegen = 0.2
    },
    
    -- Behaviors
    behaviors = {"wander", "graze", "flee", "follow", "socialize"},
    
    -- Behavior configurations
    behaviorConfigs = {
        wander = {
            moveSpeed = 2.0,
            changeDirectionChance = 0.1,
            idleChance = 0.2,
            idleDuration = {2, 5},
            wanderRadius = 20
        },
        graze = {
            moveSpeed = 0.5,
            searchRadius = 15,
            grazeTime = {5, 10},
            foodTypes = {"grass", "hay"},
            waterNeed = 0.3
        },
        flee = {
            moveSpeed = 4.0,
            detectionRadius = 15,
            groupFleeing = true,
            alertRadius = 10
        },
        follow = {
            moveSpeed = 2.5,
            followDistance = 3,
            maxDistance = 10,
            obedience = 0.8
        },
        socialize = {
            interactionRadius = 8,
            interactionTime = {3, 8},
            groupSize = {2, 4},
            socialBond = 0.1
        }
    },
    
    -- Special abilities
    abilities = {
        gallop = {
            speedBoost = 2.0,
            duration = 5,
            cooldown = 10,
            staminaCost = 20
        },
        neigh = {
            range = 12,
            duration = 1,
            cooldown = 5,
            effect = "alert"
        },
        kick = {
            damage = 15,
            range = 2,
            cooldown = 3,
            staminaCost = 10
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "horse",
        scale = 1.2,
        animations = {
            "idle", "walk", "trot", "gallop", "graze", "rear"
        },
        variants = {
            "brown", "black", "white", "chestnut", "appaloosa"
        }
    },
    
    -- Sound effects with synth configuration
    sounds = {
        neigh = {
            file = "horse_neigh",
            synth = {
                instrument = "grand_piano",
                notes = {"A3", "C4", "E4", "C4"},
                durations = {0.3, 0.2, 0.4, 0.3},
                volume = 0.5,
                vibrato = true,
                vibratoRate = 5.0
            }
        },
        gallop = {
            file = "horse_gallop",
            synth = {
                instrument = "marimba",
                notes = {"C3", "F3", "C3", "F3"},
                durations = {0.15, 0.15, 0.15, 0.15},
                volume = 0.4
            }
        },
        whinny = {
            file = "horse_whinny",
            synth = {
                instrument = "grand_piano",
                notes = {"E4", "G4", "E4"},
                durations = {0.2, 0.3, 0.4},
                volume = 0.45,
                vibrato = true,
                vibratoRate = 6.0
            }
        },
        footstep = {
            synth = {
                instrument = "marimba",
                notes = {"F3"},
                duration = 0.12,
                volume = 0.3
            }
        },
        trot = {
            synth = {
                instrument = "marimba",
                notes = {"F3", "A3"},
                durations = {0.2, 0.2},
                volume = 0.35
            }
        },
        snort = {
            synth = {
                instrument = "harmonica",
                notes = {"G4"},
                duration = 0.2,
                volume = 0.3
            }
        },
        hurt = {
            synth = {
                instrument = "grand_piano",
                notes = {"D#4"},
                duration = 0.4,
                volume = 0.5,
                vibrato = true,
                vibratoRate = 8.0
            }
        },
        death = {
            synth = {
                instrument = "bass_guitar",
                notes = {"A3", "F3", "C3"},
                durations = {0.6, 0.6, 1.0},
                volume = 0.5,
                vibrato = true,
                vibratoRate = 2.0
            }
        },
        alert = {
            synth = {
                instrument = "trumpet",
                notes = {"C5", "A4"},
                durations = {0.3, 0.4},
                volume = 0.45
            }
        }
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.8, quantity = {3, 5}},
        {id = "hide", chance = 0.7, quantity = {2, 4}},
        {id = "hoof", chance = 0.5, quantity = {1, 2}},
        {id = "tail", chance = 0.4, quantity = {1, 1}}
    }
}

-- Initialize the entity
function Horse.init(entity, world)
    -- Copy all fields from Horse template to entity instance
    for k, v in pairs(Horse) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random horse variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Horse.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update stamina
    if entity.mount and entity.mount.rider then
        -- Drain stamina while being ridden
        entity.stamina = math.max(0, entity.stamina - entity.mount.staminaDrain * dt)
    else
        -- Regenerate stamina when not being ridden
        entity.stamina = math.min(entity.maxStamina, entity.stamina + entity.mount.staminaRegen * dt)
    end
end

return Horse
