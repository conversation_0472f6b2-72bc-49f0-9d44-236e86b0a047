local Betta = {
    id = "betta",
    name = "Betta Fish",
    type = "betta",

    -- Entity categories
    categories = { "animal", "fish", "small", "predator", "territorial" },

    -- Threat categories
    threatCategories = { "predator", "medium", "large" },
    foodCategories = { "insect", "small_fish" },

    -- Stats
    maxHealth = 10,
    health = 10,
    speed = 3.5, -- Relatively fast

    -- Behaviors
    behaviors = { "territorial", "hunt", "flee" },

    -- Behavior configurations
    behaviorConfigs = {
        defend_territory = {
            territoryRadius = 5, -- Small territory
            threatTypes = { "betta", "fish" }, -- Aggressive towards other bettas and some fish
            attackRadius = 2
        },
        hunt = {
            huntRadius = 4,
            chaseRadius = 6,
            attackRadius = 1.5,
            preferredTargets = { "guppy", "minnow" } -- Will hunt smaller fish
        },
        flee = {
            useCategories = true,
            moveSpeed = 5.0,
            detectionRadius = 6
        }
    },

    -- Appearance
    appearance = {
        sprite = "betta", -- Replace with your betta sprite
        scale = 0.8,
        animations = {
            "swim",
            "flee",
            "attack",
            "display" -- Add a display animation for showing aggression
        },
        -- Variety of colors and fin types
        variants = {
            { color = "red", fins = "long" },
            { color = "blue", fins = "veiltail" },
            { color = "purple", fins = "crowntail" }
        }
    },

    -- Sound effects
    sounds = {
        -- May not have distinct sounds
    }
}

-- Initialize the betta entity
function Betta.init(entity, world)
    entity.position = entity.position or { x = 0, y = 0 }
    entity.velocity = entity.velocity or { x = 0, y = 0 }

    -- Initialize behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.init then
            behavior.init(entity, entity.behaviorConfigs[behaviorName])
        end
    end

    return entity
end

return Betta