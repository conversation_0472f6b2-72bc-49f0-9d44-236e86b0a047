-- items/armor/plate_armor.lua

local Armor = {
    id = "plate_armor",
    name = "Plate Armor",
    description = "Heavy armor made of interlocking metal plates, offering excellent protection at the cost of mobility.",
    type = "armor",
    category = "armor",
    stackable = false,

    stats = {
        defense = 20.0,
        durability = 200.0,
        repair_cost = 60,
        weight = 25.0,
    },

    material = "steel",

    sprite = "res://Sprite/items/armor/plate_armor.png",
    size = { width = 64, height = 64 },

    effects = {
        physical_resistance = 0.2,
        slash_resistance = 0.25,
        pierce_resistance = 0.15,
        speed_penalty = -0.15, -- 15% speed reduction
        magic_vulnerability = 0.1 -- 10% more damage from magic
    },

    meta = {
        rarity = "rare"
    },

    take_damage = function(self, amount, damage_type)
        local modified_amount = amount
        
        -- Apply damage type modifiers
        if damage_type == "physical" then
            modified_amount = amount * (1 - self.effects.physical_resistance)
        elseif damage_type == "slash" then
            modified_amount = amount * (1 - self.effects.slash_resistance)
        elseif damage_type == "pierce" then
            modified_amount = amount * (1 - self.effects.pierce_resistance)
        elseif damage_type == "magic" then
            modified_amount = amount * (1 + self.effects.magic_vulnerability)
        end
        
        self.stats.durability = math.max(0, self.stats.durability - modified_amount)
        if self.stats.durability == 0 then
            print(self.name .. " is broken! Penalties applied.")
            -- Potential penalties could be applied here
        end
    end,

    repair = function(self)
        local repair_amount = 200 - self.stats.durability
        self.stats.durability = 200
        print(self.name .. " repaired by " .. repair_amount .. " points.")
    end,
    
    onEquip = function(self, user)
        if user and user.stats then
            -- Apply speed penalty
            if user.stats.speed then
                user.stats.speed = user.stats.speed * (1 + self.effects.speed_penalty)
                print(user.name .. "'s movement is slowed by the weight of the plate armor.")
            end
            
            -- Apply damage resistances
            if user.resistances then
                user.resistances.physical = (user.resistances.physical or 0) + self.effects.physical_resistance
                user.resistances.slash = (user.resistances.slash or 0) + self.effects.slash_resistance
                user.resistances.pierce = (user.resistances.pierce or 0) + self.effects.pierce_resistance
                user.resistances.magic = (user.resistances.magic or 0) - self.effects.magic_vulnerability
                print(user.name .. " gains significant physical protection from the plate armor.")
            end
        end
    end,
    
    onUnequip = function(self, user)
        if user and user.stats then
            -- Remove speed penalty
            if user.stats.speed then
                user.stats.speed = user.stats.speed / (1 + self.effects.speed_penalty)
                print(user.name .. "'s movement returns to normal after unequipping the plate armor.")
            end
            
            -- Remove damage resistances
            if user.resistances then
                user.resistances.physical = (user.resistances.physical or 0) - self.effects.physical_resistance
                user.resistances.slash = (user.resistances.slash or 0) - self.effects.slash_resistance
                user.resistances.pierce = (user.resistances.pierce or 0) - self.effects.pierce_resistance
                user.resistances.magic = (user.resistances.magic or 0) + self.effects.magic_vulnerability
                print(user.name .. " loses the physical protection from the plate armor.")
            end
        end
    end
}

return Armor
