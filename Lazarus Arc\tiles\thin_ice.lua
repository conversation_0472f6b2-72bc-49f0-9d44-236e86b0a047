-- tiles/thin_ice.lua
local ThinIceTile = {
    id = "thin_ice",
    name = "Thin Ice",
    passable = true,
    variants = 3, -- Different ice patterns
    
    -- Properties
    properties = {
        thickness = 0.3, -- Very thin ice
        temperature = -5,
        cracking = false,
        hasFish = true,
        hasCracks = false,
        weightLimit = 50, -- Maximum weight before breaking
        lastCrack = 0
    },
    
    -- Visual properties
    visual = {
        base_color = {0.8, 0.9, 1.0}, -- Light blue
        variants = {
            {name = "smooth_ice", type = "normal"},
            {name = "cracked_ice", type = "cracked"},
            {name = "fish_visible", type = "fish"}
        },
        crack_patterns = {
            {name = "single_crack", type = "minor"},
            {name = "spider_crack", type = "major"},
            {name = "fractured", type = "critical"}
        }
    },
    
    -- Sound properties
    sounds = {
        ambient = {
            "ice_creaking",
            "wind_whistling"
        },
        footsteps = {
            "ice_step",
            "ice_crack"
        }
    },
    
    -- Resources
    resources = {
        fish = {
            amount = 2,
            regenRate = 0.1,
            harvestAmount = 1
        }
    },
    
    -- Effects
    effects = {
        cracking = {
            active = false,
            progress = 0.0,
            threshold = 0.8
        },
        melting = {
            active = false,
            rate = 0.1
        }
    },
    
    -- Spawn rules
    spawnRules = {
        fish = {
            chance = 0.4,
            minDistance = 2,
            maxDensity = 0.3
        }
    },
    
    -- Weather effects
    weatherEffects = {
        snow = function(tile)
            -- Snow can strengthen thin ice
            tile.properties.thickness = math.min(0.5, tile.properties.thickness + 0.1)
            return "ice_thicken"
        end,
        rain = function(tile)
            -- Rain weakens thin ice
            tile.properties.thickness = math.max(0.1, tile.properties.thickness - 0.2)
            if tile.properties.thickness < 0.2 then
                tile.properties.hasCracks = true
                return "ice_weaken"
            end
            return nil
        end,
        heat = function(tile)
            -- Heat can melt thin ice
            tile.properties.thickness = math.max(0, tile.properties.thickness - 0.3)
            if tile.properties.thickness <= 0 then
                return "ice_melt"
            end
            return "ice_thin"
        end
    },
    
    -- Time effects
    timeEffects = {
        day = function(tile)
            -- Daytime can weaken ice
            if tile.properties.temperature > 0 then
                tile.properties.thickness = math.max(0, tile.properties.thickness - 0.1)
                return "ice_thin"
            end
            return nil
        end,
        night = function(tile)
            -- Nighttime can strengthen ice
            if tile.properties.temperature < 0 then
                tile.properties.thickness = math.min(0.5, tile.properties.thickness + 0.1)
                return "ice_thicken"
            end
            return nil
        end
    }
}

-- Initialize the tile
function ThinIceTile.init(tile, world)
    -- Initialize properties first
    tile.properties = tile.properties or {}
    
    -- Copy all fields from ThinIceTile template to tile instance
    for k, v in pairs(ThinIceTile) do
        if type(v) ~= "function" and tile[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                tile[k] = {}
                for subk, subv in pairs(v) do
                    tile[k][subk] = subv
                end
            else
                tile[k] = v
            end
        end
    end
    
    -- Set random variant
    if tile.visual and tile.visual.variants then
        local variants = tile.visual.variants
        tile.currentVariant = variants[math.random(#variants)]
    end
    
    -- Initialize resources
    if tile.resources then
        for resourceType, resource in pairs(tile.resources) do
            resource.currentAmount = resource.amount
            resource.lastHarvest = 0
            resource.growthStage = "mature"
        end
    end
    
    return tile
end

return ThinIceTile 