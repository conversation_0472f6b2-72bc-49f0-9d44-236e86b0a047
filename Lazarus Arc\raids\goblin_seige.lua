local GoblinSiege = {
    id = "goblin_siege",
    name = "Goblin Siege",
    location = {x = 0, y = 0},
    currentStage = 0,
    stages = {
        -- Stage 1: Scouting Party
        {
            name = "Scouting Party",
            duration = 60, -- Stage lasts 60 seconds
            objectives = {
                "Defeat scouting party",
            },
            enemies = {
                {type = "goblin", count = 10},
                {type = "wolf", count = 3},
            },
        },
        -- Stage 2: Assault on the Gates
        {
            name = "Assault on the Gates",
            duration = 90,
            objectives = {
                "Defend the main gate",
                "Destroy the goblin ram",
            },
            enemies = {
                {type = "goblin", count = 20},
                {type = "goblin_archer", count = 5},
                {type = "hobgoblin", count = 5},
                {type = "goblin_ram", count = 1}, -- New enemy: Goblin Ram
            },
        },
        -- Stage 3: The Siege Lord
        {
            name = "The Siege Lord",
            duration = 120,
            objectives = {
                "Defeat the Goblin Siege Lord",
            },
            enemies = {
                {type = "goblin", count = 10},
                {type = "hobgoblin", count = 10},
                {type = "goblin_siege_lord", count = 1}, -- New enemy: Goblin Siege Lord
                {type = "ogre", count = 2},
            },
        },
    },
    timer = 0,
    isActive = false,
    -- Additional raid-specific data
    siegeRamHealth = 100,
}

function GoblinSiege.init(config)
    for k, v in pairs(config or {}) do
        GoblinSiege[k] = v
    end
    GoblinSiege.timer = 0
    GoblinSiege.currentStage = 0
    GoblinSiege.isActive = true
    GoblinSiege.siegeRamHealth = 100 -- Initialize siege ram health
end

function GoblinSiege.update(world, dt)
    if not GoblinSiege.isActive then
        return
    end

    GoblinSiege.timer = GoblinSiege.timer + dt
    local currentStage = GoblinSiege.stages[GoblinSiege.currentStage + 1]

    if currentStage then
        GoblinSiege.spawnEnemies(world, currentStage.enemies)

        -- Stage progression logic based on time
        if GoblinSiege.timer >= currentStage.duration then
            GoblinSiege.currentStage = GoblinSiege.currentStage + 1
            GoblinSiege.timer = 0 -- Reset timer for the next stage
            print("Stage " .. GoblinSiege.currentStage .. " complete!")
        end

        --Special stage logic
        if GoblinSiege.currentStage == 2 then
            GoblinSiege.updateRam(dt)
        end
        if GoblinSiege.currentStage == 3 then
            GoblinSiege.updateSiegeLord()
        end

    else
        GoblinSiege.isActive = false
        print("Goblin Siege complete!")
    end
end

function GoblinSiege.spawnEnemies(world, enemies)
    for _, enemyGroup in ipairs(enemies) do
        for i = 1, enemyGroup.count do
            local entity = world.entityFactory:create(enemyGroup.type)
            if entity then
                entity.position = {
                    x = GoblinSiege.location.x + math.random(-5, 5),
                    y = GoblinSiege.location.y + math.random(-5, 5),
                }
                world.entitySystem:addEntity(entity)
            end
        end
    end
end

function GoblinSiege.updateRam(dt)
    if GoblinSiege.siegeRamHealth <= 0 then
        print("Goblin Ram destroyed!")
        -- Additional logic for destroying the ram (e.g., open gates)
    else
        -- Logic for the ram attacking the gate or moving
        print("Goblin Ram attacking! Health: " .. GoblinSiege.siegeRamHealth)
        GoblinSiege.siegeRamHealth = GoblinSiege.siegeRamHealth - dt * 5 -- Example: Ram loses health over time or when attacked
    end
end

function GoblinSiege.updateSiegeLord()
    -- Logic for the Goblin Siege Lord's special abilities or behavior
    print("Goblin Siege Lord is enraged!")
end

return GoblinSiege