local Crossbow = {
    id = "crossbow",
    name = "Crossbow",
    type = "weapon",
    category = "bow",
    
    -- Properties
    properties = {
        damage = 28,
        attackSpeed = 0.8,
        range = 7.0,
        durability = 200,
        maxDurability = 200,
        boltSpeed = 16,
        boltSpread = 0.1,
        reloadTime = 1.5,
        piercingChance = 0.3,
        piercingCount = 2,
        piercingDamage = 0.7,
        criticalChance = 0.15,
        criticalMultiplier = 2.0,
        armorPenetration = 0.4,
        boltCount = 1,
        maxBolts = 5,
        boltReloadTime = 0.5,
        weight = 1.2,
        stability = 0.9
    },
    
    -- Appearance
    appearance = {
        sprite = "crossbow",
        scale = 1.0,
        animations = {
            "idle",
            "reload",
            "shoot",
            "aim"
        },
        variants = {
            "steel",
            "iron",
            "bronze",
            "wooden"
        },
        blendMode = "normal",
        tint = {0.8, 0.8, 0.8},
        alpha = 1.0
    },
    
    -- Sound effects
    sounds = {
        shoot = "crossbow_shoot",
        reload = "crossbow_reload",
        bolt = "crossbow_bolt",
        critical = "crossbow_critical"
    },
    
    -- Effects
    effects = {
        piercing = {
            type = "status",
            duration = 0.5,
            effects = {
                damage = 0.7,
                count = 2
            }
        },
        critical = {
            type = "status",
            duration = 0.5,
            effects = {
                damage = 2.0
            }
        }
    }
}

-- Initialize the weapon
function Crossbow.init(weapon, world)
    -- Copy all fields from Crossbow template to weapon instance
    for k, v in pairs(Crossbow) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        lastReloadTime = 0,
        boltsLoaded = weapon.properties.maxBolts,
        isReloading = false
    }

    return weapon
end

-- Update the weapon
function Crossbow.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Update reload state
        if weapon.properties.weaponState.isReloading then
            if world.time - weapon.properties.weaponState.lastReloadTime > weapon.properties.reloadTime then
                weapon.properties.weaponState.isReloading = false
                weapon.properties.weaponState.boltsLoaded = weapon.properties.maxBolts
            end
        end
    end
end

-- Handle attack
function Crossbow.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Check if weapon needs reloading
    if weapon.properties.weaponState.boltsLoaded <= 0 then
        if not weapon.properties.weaponState.isReloading then
            -- Start reloading
            weapon.properties.weaponState.isReloading = true
            weapon.properties.weaponState.lastReloadTime = world.time
            
            -- Play reload sound
            if world.playSound then
                world.playSound(weapon.sounds.reload)
            end
            
            return false
        end
    end
    
    -- Calculate bolt direction
    local angle = math.atan2(
        target.position.y - attacker.position.y,
        target.position.x - attacker.position.x
    )
    
    -- Add spread to bolt direction
    local spread = (math.random() - 0.5) * weapon.properties.boltSpread
    angle = angle + spread
    
    -- Calculate damage
    local damage = weapon.properties.damage
    
    -- Check for critical hit
    if math.random() < weapon.properties.criticalChance then
        damage = damage * weapon.properties.criticalMultiplier
        
        -- Play critical sound
        if world.playSound then
            world.playSound(weapon.sounds.critical)
        end
    end
    
    -- Create bolt projectile
    if world.createProjectile then
        world.createProjectile({
            type = "crossbow_bolt",
            position = attacker.position,
            direction = {
                x = math.cos(angle) * weapon.properties.boltSpeed,
                y = math.sin(angle) * weapon.properties.boltSpeed
            },
            damage = damage,
            range = weapon.properties.range,
            piercing = {
                chance = weapon.properties.piercingChance,
                count = weapon.properties.piercingCount,
                damage = weapon.properties.piercingDamage
            },
            armorPenetration = weapon.properties.armorPenetration
        })
    end
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce bolts loaded
    weapon.properties.weaponState.boltsLoaded = math.max(0,
        weapon.properties.weaponState.boltsLoaded - 1)
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function Crossbow.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough bolts
    if weapon.properties.weaponState.boltsLoaded < 3 then
        return false
    end
    
    -- Calculate spread pattern
    local baseAngle = math.atan2(
        target.position.y - attacker.position.y,
        target.position.x - attacker.position.x
    )
    
    -- Create multiple bolts in a spread pattern
    for i = 1, 3 do
        local angle = baseAngle + (i - 2) * 0.2
        
        if world.createProjectile then
            world.createProjectile({
                type = "crossbow_bolt",
                position = attacker.position,
                direction = {
                    x = math.cos(angle) * weapon.properties.boltSpeed,
                    y = math.sin(angle) * weapon.properties.boltSpeed
                },
                damage = weapon.properties.damage * 0.8,
                range = weapon.properties.range,
                piercing = {
                    chance = weapon.properties.piercingChance * 0.8,
                    count = weapon.properties.piercingCount,
                    damage = weapon.properties.piercingDamage
                },
                armorPenetration = weapon.properties.armorPenetration
            })
        end
    end
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce bolts loaded
    weapon.properties.weaponState.boltsLoaded = math.max(0,
        weapon.properties.weaponState.boltsLoaded - 3)
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 3)
    
    return true
end

return Crossbow 