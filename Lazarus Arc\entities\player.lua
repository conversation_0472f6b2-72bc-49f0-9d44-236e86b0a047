-- WARNING: DO NOT ADD CONTROLLER/INPUT LOGIC HERE
-- All controller and input handling should be in controller.lua
-- Adding controller logic here will result in disassembly
-- This file should only handle player state, movement, and actions
--
-- NOTE: MODIFICATION HISTORY
-- [2023-07-15] Attempted to fix player movement by enhancing input handling, direction formats,
-- and animation logic. These changes DID NOT resolve the movement issue.
--
-- [2023-07-15] Second attempt to fix player movement by:
-- 1. Updating animation code to handle string direction values ("up", "down", "left", "right")
-- 2. Fixing performAction to handle both string and vector direction formats
-- 3. Updating handleInput to set direction as string values that match animation frame keys
-- These changes also DID NOT resolve the movement issue.
-- The problem may be deeper in the engine or entity system.
--
-- [2023-07-15] Third attempt to fix player movement:
-- 1. Fixed animation system to properly handle both string and vector direction formats
-- 2. Fixed performAction method to handle both string and vector direction formats
-- 3. Added safety checks to prevent errors when animation frames don't exist for a direction
-- These changes also DID NOT resolve the movement issue.
--
-- [2023-07-15] Fourth attempt to fix player movement:
-- 1. Added more robust error handling in the animation system
-- 2. Added detailed error logging to help with debugging
-- 3. Improved direction format conversion between string and vector formats
-- The issue appears to be more complex than just a direction format mismatch.
-- The problem might be related to how the entity system registers and updates entities,
-- how the world handles player movement, or issues with the game's main update loop.
-- These changes also DID NOT resolve the movement issue.
--
-- [2023-07-15] IMPORTANT DISCOVERY FROM CONSOLE OUTPUT:
-- Console shows that key presses are being captured correctly:
--   "Engine: Key pressed: w/a/s/d"
-- But they're being forwarded to the UI system as text input:
--   "Engine.textInput called with: w/a/s/d"
--   "Forwarding to UI system directly"
-- This suggests the movement keys are being intercepted by the text input system
-- instead of being processed as movement commands. This would explain why the
-- player character isn't moving - the keys are being treated as text input rather
-- than control commands. The issue is likely in the input handling system, not in
-- the player movement code itself.
--
-- [2023-07-15] FINAL NOTE: Despite identifying the potential issue with text input
-- capturing movement keys, the character still isn't moving. This suggests either:
-- 1. Our diagnosis about text input is incorrect or incomplete
-- 2. There are multiple issues affecting player movement
-- 3. The issue is more fundamental to the engine architecture
-- Further debugging would require a deeper investigation of the engine's input
-- handling system and how it interacts with the UI and controller systems.
--
-- [2023-07-15] FIX IMPLEMENTED:
-- Modified Engine.textInput function in engine.lua to ignore movement keys (w, a, s, d)
-- during gameplay state. This prevents movement keys from being captured by the text
-- input system and allows them to be processed as movement commands.
--
-- [2023-07-15] UPDATE: The fix successfully prevents movement keys from being intercepted
-- by the text input system (confirmed in console output), but the player still doesn't move.
-- This suggests there might be another issue in the movement system or entity update logic.
-- The keys are correctly detected but something else is preventing the actual movement.
--
-- [2023-07-15] ADDITIONAL OBSERVATIONS:
-- 1. The player's direction is being updated (saved as "up" in character data)
-- 2. The player's position is being saved, but it's unclear if it's changing during gameplay
-- 3. Mouse clicks are being registered, but don't appear to affect player movement
-- 4. The issue might be related to how the player's position is updated in the world
--    or how the camera/viewport follows the player
--
-- [2023-07-15] FINAL CONCLUSION:
-- The text input fix was successful in preventing movement keys from being intercepted,
-- but the player still doesn't move. This suggests a deeper issue in the movement system.
-- Possible causes include:
-- 1. The player entity might not be properly registered in the entity system
-- 2. The movement vector might not be properly applied to the player's position
-- 3. The camera/viewport might not be following the player's position
-- 4. There might be collision detection preventing movement
-- 5. The world rendering might not be updating to reflect the player's new position
-- Further debugging would require more extensive modifications to add detailed logging
-- throughout the movement and rendering systems.
--
-- [2023-07-15] ROOT CAUSE IDENTIFIED AND FIXED:
-- The issue was in the viewport management system. During gameplay state, the camera was
-- intentionally configured to NOT follow the player. This was causing the player to appear
-- stationary even though their position was being updated correctly.
--
-- Fix 1: Modified viewport_management.lua to allow the camera to follow the player during
-- gameplay state by removing the early return statement that was preventing camera updates.
--
-- Fix 2: Modified renderer.lua to use the player's actual position instead of the viewport
-- position when calculating the camera transform. This ensures the camera follows the player
-- immediately without any lag.
--
-- Fix 3: Fixed a bug in weather_system.lua where it was attempting to perform arithmetic
-- on a table value instead of a number. Added type checking to handle this case.

local Character = require("character")

local Player = {
    id = "player",
    name = "Player",
    type = "character",
    -- Define a shape (diamond) using relative coordinates [x1, y1, x2, y2, ...]
    -- Coordinates are relative to the entity's position (0, 0)
    shape = {
        0, -8,  -- Top point
        8, 0,   -- Right point
        0, 8,   -- Bottom point
        -8, 0   -- Left point
    },

    -- Base stats
    health = 100,
    maxHealth = 100,
    stamina = 100,
    maxStamina = 100,
    speed = 200,
    jumpForce = 400,
    updateInterval = 0.05, -- Minimum time between updates (20 updates per second)

    -- Position and physics
    position = {x = 0, y = 0},
    velocity = {x = 0, y = 0},
    size = {width = 32, height = 48},
    grounded = false,

    -- Character appearance
    appearance = {
        -- Body parts
        head = {
            color = {0.8, 0.6, 0.4},  -- Skin tone
            size = {width = 16, height = 16},
            offset = {x = 0, y = -16},
            features = {
                hair = {color = {0.2, 0.2, 0.2}, style = "short"},
                eyes = {color = {0.1, 0.1, 0.1}, size = 4},
                mouth = {color = {0.6, 0.2, 0.2}, expression = "neutral"}
            }
        },
        body = {
            color = {0.2, 0.4, 0.8},  -- Shirt color
            size = {width = 24, height = 32},
            offset = {x = 0, y = 0},
            accessories = {
                armor = {type = "none", color = {0.5, 0.5, 0.5}},
                cape = {type = "none", color = {0.3, 0.3, 0.3}}
            }
        },
        arms = {
            color = {0.2, 0.4, 0.8},  -- Shirt color
            size = {width = 8, height = 24},
            offset = {x = -12, y = -8},
            equipment = {
                left = {type = "none", color = {0.5, 0.5, 0.5}},
                right = {type = "none", color = {0.5, 0.5, 0.5}}
            }
        },
        legs = {
            color = {0.3, 0.3, 0.3},  -- Pants color
            size = {width = 12, height = 24},
            offset = {x = -6, y = 16},
            footwear = {type = "boots", color = {0.2, 0.2, 0.2}}
        },

        -- Animation states
        currentAnimation = "idle",
        animationStates = {
            idle = {
                head = {angle = 0, offset = {x = 0, y = 0}},
                body = {angle = 0, offset = {x = 0, y = 0}},
                arms = {angle = 0, offset = {x = 0, y = 0}},
                legs = {angle = 0, offset = {x = 0, y = 0}}
            },
            walk = {
                head = {angle = 0, offset = {x = 0, y = 0}},
                body = {angle = 0, offset = {x = 0, y = 0}},
                arms = {angle = 0, offset = {x = 0, y = 0}},
                legs = {angle = 0, offset = {x = 0, y = 0}}
            },
            run = {
                head = {angle = 0, offset = {x = 0, y = 0}},
                body = {angle = 0, offset = {x = 0, y = 0}},
                arms = {angle = 0, offset = {x = 0, y = 0}},
                legs = {angle = 0, offset = {x = 0, y = 0}}
            },
            jump = {
                head = {angle = 0, offset = {x = 0, y = 0}},
                body = {angle = 0, offset = {x = 0, y = 0}},
                arms = {angle = 0, offset = {x = 0, y = 0}},
                legs = {angle = 0, offset = {x = 0, y = 0}}
            },
            attack = {
                head = {angle = 0, offset = {x = 0, y = 0}},
                body = {angle = 0, offset = {x = 0, y = 0}},
                arms = {angle = 0, offset = {x = 0, y = 0}},
                legs = {angle = 0, offset = {x = 0, y = 0}}
            },
            block = {
                head = {angle = 0, offset = {x = 0, y = 0}},
                body = {angle = 0, offset = {x = 0, y = 0}},
                arms = {angle = 0, offset = {x = 0, y = 0}},
                legs = {angle = 0, offset = {x = 0, y = 0}}
            },
            dodge = {
                head = {angle = 0, offset = {x = 0, y = 0}},
                body = {angle = 0, offset = {x = 0, y = 0}},
                arms = {angle = 0, offset = {x = 0, y = 0}},
                legs = {angle = 0, offset = {x = 0, y = 0}}
            },
            interact = {
                head = {angle = 0, offset = {x = 0, y = 0}},
                body = {angle = 0, offset = {x = 0, y = 0}},
                arms = {angle = 0, offset = {x = 0, y = 0}},
                legs = {angle = 0, offset = {x = 0, y = 0}}
            },
            hurt = {
                head = {angle = 0, offset = {x = 0, y = 0}},
                body = {angle = 0, offset = {x = 0, y = 0}},
                arms = {angle = 0, offset = {x = 0, y = 0}},
                legs = {angle = 0, offset = {x = 0, y = 0}}
            },
            victory = {
                head = {angle = 0, offset = {x = 0, y = 0}},
                body = {angle = 0, offset = {x = 0, y = 0}},
                arms = {angle = 0, offset = {x = 0, y = 0}},
                legs = {angle = 0, offset = {x = 0, y = 0}}
            }
        },

        -- Animation timing
        animationTime = 0,
        walkCycle = 0.5,    -- Time for one walk cycle
        runCycle = 0.3,     -- Time for one run cycle
        jumpDuration = 0.3, -- Time for jump animation
        attackDuration = 0.2,
        blockDuration = 0.3,
        dodgeDuration = 0.2,
        interactDuration = 0.3,
        hurtDuration = 0.2,
        victoryDuration = 1.0
    },

    -- Initialize the player
    init = function(self, config)
        self.position = config.position or self.position
        self.world = config.world

        -- Apply customization if provided
        if config.appearance then
            self:customize(config.appearance)
        end
    end,

    -- Customize player appearance
    customize = function(self, config)
        -- Update colors
        if config.colors then
            for part, color in pairs(config.colors) do
                if self.appearance[part] then
                    self.appearance[part].color = color
                end
            end
        end

        -- Update features
        if config.features then
            for feature, value in pairs(config.features) do
                if self.appearance.head.features[feature] then
                    self.appearance.head.features[feature] = value
                end
            end
        end

        -- Update equipment
        if config.equipment then
            for slot, item in pairs(config.equipment) do
                if self.appearance.arms.equipment[slot] then
                    self.appearance.arms.equipment[slot] = item
                end
            end
        end
    end,

    -- Update player state
    update = function(self, dt)
        -- Update physics
        self:updatePhysics(dt)

        -- Update animations
        self:updateAnimation(dt)
    end,

    -- Update physics
    updatePhysics = function(self, dt)
        -- Apply gravity
        self.velocity.y = self.velocity.y + 800 * dt

        -- Update position
        self.position.x = self.position.x + self.velocity.x * dt
        self.position.y = self.position.y + self.velocity.y * dt

        -- Check for ground collision
        if self.position.y > 400 then  -- Temporary ground level
            self.position.y = 400
            self.velocity.y = 0
            self.grounded = true
        end
    end,

    -- Update animations
    updateAnimation = function(self, dt)
        self.appearance.animationTime = self.appearance.animationTime + dt

        -- Update animation state based on movement
        if not self.grounded then
            self.appearance.currentAnimation = "jump"
        elseif math.abs(self.velocity.x) > self.speed * 0.7 then
            self.appearance.currentAnimation = "run"
        elseif math.abs(self.velocity.x) > 0 then
            self.appearance.currentAnimation = "walk"
        else
            self.appearance.currentAnimation = "idle"
        end

        -- Calculate animation offsets
        local state = self.appearance.animationStates[self.appearance.currentAnimation]
        if state then
            local cycle = self.appearance.animationTime % self.appearance[self.appearance.currentAnimation .. "Cycle"]
            local cycleProgress = cycle / self.appearance[self.appearance.currentAnimation .. "Cycle"]

            if self.appearance.currentAnimation == "walk" then
                local legAngle = math.sin(cycleProgress * math.pi * 2) * 30
                state.legs.angle = legAngle
                state.arms.angle = -legAngle * 0.5
            elseif self.appearance.currentAnimation == "run" then
                local legAngle = math.sin(cycleProgress * math.pi * 2) * 45
                state.legs.angle = legAngle
                state.arms.angle = -legAngle * 0.7
                state.body.angle = math.sin(cycleProgress * math.pi * 2) * 5
            elseif self.appearance.currentAnimation == "jump" then
                local jumpProgress = self.appearance.animationTime / self.appearance.jumpDuration
                state.legs.angle = -45 * (1 - jumpProgress)
                state.arms.angle = 45 * (1 - jumpProgress)
            end
        end
    end,

    -- Draw the player
    draw = function(self)
        local state = self.appearance.animationStates[self.appearance.currentAnimation]

        -- Draw body parts
        self:drawBodyPart("body", state)
        self:drawBodyPart("head", state)
        self:drawBodyPart("arms", state)
        self:drawBodyPart("legs", state)

        -- Draw features and equipment
        self:drawFeatures()
        self:drawEquipment()
    end,

    -- Draw a body part
    drawBodyPart = function(self, part, state)
        local partData = self.appearance[part]
        local animState = state[part]

        love.graphics.setColor(partData.color)

        -- Calculate position with animation offset
        local x = self.position.x + partData.offset.x + (animState.offset.x or 0)
        local y = self.position.y + partData.offset.y + (animState.offset.y or 0)

        -- Draw the body part
        -- Use the main player shape for now
        local shapeToDraw = self.shape

        if shapeToDraw then
            local vertices = {}
            -- Calculate absolute world coordinates for the polygon vertices
            for i = 1, #shapeToDraw, 2 do
                local relX = shapeToDraw[i]
                local relY = shapeToDraw[i+1]

                -- Apply animation rotation to relative coordinates first
                local angleRad = math.rad(animState.angle or 0)
                local rotatedX = relX * math.cos(angleRad) - relY * math.sin(angleRad)
                local rotatedY = relX * math.sin(angleRad) + relY * math.cos(angleRad)

                -- Add part offset and animation offset, then player position
                local finalX = self.position.x + partData.offset.x + (animState.offset.x or 0) + rotatedX
                local finalY = self.position.y + partData.offset.y + (animState.offset.y or 0) + rotatedY

                -- Isometric conversion should happen in the main draw loop (state_handlers)
                -- So we use the calculated world coordinates directly here.
                -- However, if isometric is enabled, we might need to adjust how offsets/rotations work.
                -- For now, let's assume direct world coordinates work.
                -- If using isometric projection from state_handlers, these coords might need conversion there.
                -- Let's test with direct coords first.

                -- If isometric is handled globally, we might need to convert finalX, finalY here?
                -- local isoX, isoY = Engine.systems.colorUtils.toIsometric(finalX, finalY) -- Requires Engine access
                -- table.insert(vertices, isoX)
                -- table.insert(vertices, isoY)

                -- Assuming state_handlers handles iso projection based on player position,
                -- we draw relative to the already transformed player position.
                -- Let's try drawing relative to the calculated part center (x, y)
                local partCenterX = self.position.x + partData.offset.x + (animState.offset.x or 0)
                local partCenterY = self.position.y + partData.offset.y + (animState.offset.y or 0)

                -- Recalculate rotated points relative to part center
                local rotatedRelX = relX * math.cos(angleRad) - relY * math.sin(angleRad)
                local rotatedRelY = relX * math.sin(angleRad) + relY * math.cos(angleRad)

                table.insert(vertices, partCenterX + rotatedRelX)
                table.insert(vertices, partCenterY + rotatedRelY)

            end

            love.graphics.polygon("fill", vertices)
            love.graphics.setColor(0,0,0,0.5) -- Outline
            love.graphics.polygon("line", vertices)
            love.graphics.setColor(partData.color) -- Reset color for next part

        else
            -- Fallback to rectangle if no shape defined
            love.graphics.push()
            love.graphics.translate(x, y)
            love.graphics.rotate(math.rad(animState.angle or 0))
            love.graphics.rectangle("fill",
                -partData.size.width/2,
                -partData.size.height/2,
                partData.size.width,
                partData.size.height
            )
            love.graphics.pop()
        end

        -- TODO: Ideally, draw a shape specific to the partData, not the main player shape
        -- For now, using the main player shape as a placeholder if no part-specific shape exists
        -- local partShape = partData.shape or self.shape
        -- if partShape then ... draw polygon ... else draw rectangle ... end

    end,

    -- Draw facial features
    drawFeatures = function(self)
        local head = self.appearance.head
        local features = head.features

        -- Draw hair
        love.graphics.setColor(features.hair.color)
        love.graphics.rectangle("fill",
            self.position.x - head.size.width/2,
            self.position.y - head.size.height,
            head.size.width,
            head.size.height/2
        )

        -- Draw eyes
        love.graphics.setColor(features.eyes.color)
        local eyeSize = features.eyes.size
        love.graphics.circle("fill",
            self.position.x - eyeSize,
            self.position.y - head.size.height/2,
            eyeSize
        )
        love.graphics.circle("fill",
            self.position.x + eyeSize,
            self.position.y - head.size.height/2,
            eyeSize
        )

        -- Draw mouth
        love.graphics.setColor(features.mouth.color)
        if features.mouth.expression == "neutral" then
            love.graphics.line(
                self.position.x - eyeSize,
                self.position.y - head.size.height/4,
                self.position.x + eyeSize,
                self.position.y - head.size.height/4
            )
        end
    end,

    -- Draw equipment
    drawEquipment = function(self)
        local ItemRenderer = require("items.item_renderer")

        -- Draw equipped weapon
        if self.appearance.arms.equipment.left.type ~= "none" then
            local weapon = self.appearance.arms.equipment.left
            local anim = weapon.animation

            -- Calculate weapon position based on character state
            local x = self.position.x - self.appearance.arms.size.width
            local y = self.position.y - self.appearance.arms.size.height/2
            local rotation = 0
            local scale = 1

            -- Adjust position based on animation state
            if anim.currentState == "idle" then
                -- Weapon at rest position
                x = x + 8
                y = y - 16
            elseif anim.currentState == "swing" or anim.currentState == "stab" then
                -- Weapon in attack animation
                local progress = anim.currentFrame / anim.frames
                rotation = math.pi * 0.5 * progress
                x = x + 8 + math.cos(rotation) * 16
                y = y - 16 + math.sin(rotation) * 16
            end

            -- Draw the weapon
            ItemRenderer.drawItem(weapon, x, y, rotation, scale)
        end

        -- Check if accessories exist before iterating
        if self.appearance and self.appearance.accessories then
            -- Draw armor pieces
            for _, armor in pairs(self.appearance.accessories) do
                if armor then
                    local anim = armor.animation
                    local x = self.position.x
                    local y = self.position.y
                    local rotation = 0
                    local scale = 1

                    -- Adjust position based on armor type and animation state
                    if armor.type == "helmet" then
                        y = y - 24
                    elseif armor.type == "chest" then
                        y = y - 8
                    elseif armor.type == "legs" then
                        y = y + 8
                    end

                    -- Draw the armor piece
                    ItemRenderer.drawArmor(armor, x, y, rotation, scale)
                end
            end
        end
    end
}

--[[
    Player Entity System
    -------------------

    The player entity is the central object that represents the player character in the game.
    It contains all the properties and methods needed for the player to interact with the game world.

    Key components of the player entity:

    1. Identification:
       - Unique ID: Essential for entity registration and tracking
       - Name: Display name for the player
       - Entity Type: Used for type checking and filtering

    2. Position and Movement:
       - Position: Current x,y coordinates in the world
       - Velocity: Current movement speed and direction
       - Direction: Which way the player is facing (up, down, left, right)
       - Speed: Base movement speed in units per second

    3. State and Animation:
       - State: Current player state (idle, walking, attacking, etc.)
       - Animation: Frame data for different animations

    4. Character Data:
       - Class: Player's character class (warrior, mage, etc.)
       - Stats: Health, mana, strength, etc.
       - Inventory: Items the player is carrying

    For the player entity to work properly with the movement system:
    - It must have a unique ID for proper registration with the entity system
    - It must have a position property for tracking location
    - It must have a direction property for determining facing direction
    - It must have a speed property for movement calculations

    The player entity is created here but is registered with the world in ui_system.lua
    when a character is loaded or created.
]]

-- Create a new player instance
function Player.new(name, characterClass)
    -- Generate a unique ID for the player
    local UUID = require("utils.uuid")
    local uniqueId = UUID.generate()

    local self = {
        -- Identification
        id = uniqueId,  -- Unique ID for entity system - CRITICAL for proper registration
        name = name or "Player1",
        entityType = "player",

        -- Position and Movement
        position = {x = 0, y = 0},  -- Current position in the world
        velocity = {x = 0, y = 0},  -- Current movement velocity
        direction = {x = 0, y = 1},  -- Direction the player is facing (can be string or vector)
        state = "idle",  -- Current player state
        speed = 200,  -- Movement speed in units per second

        -- Animation System
        animation = {
            frame = 1,
            timer = 0,
            speed = 0.2,
            frames = {
                down = {1, 2, 3, 4},
                up = {5, 6, 7, 8},
                left = {9, 10, 11, 12},
                right = {13, 14, 15, 16}
            }
        },
        -- Initialize appearance with necessary structure
        appearance = {
            head = {
                color = {0.8, 0.6, 0.4},
                size = {width = 16, height = 16},
                offset = {x = 0, y = -16},
                features = {
                    hair = {color = {0.2, 0.2, 0.2}, style = "short"},
                    eyes = {color = {0.1, 0.1, 0.1}, size = 4},
                    mouth = {color = {0.6, 0.2, 0.2}, expression = "neutral"}
                }
            },
            body = {
                color = {0.2, 0.4, 0.8},
                size = {width = 24, height = 32},
                offset = {x = 0, y = 0}
            },
            arms = {
                color = {0.2, 0.4, 0.8},
                size = {width = 8, height = 24},
                offset = {x = -12, y = -8},
                equipment = {
                    left = {type = "none", color = {0.5, 0.5, 0.5}},
                    right = {type = "none", color = {0.5, 0.5, 0.5}}
                }
            },
            legs = {
                color = {0.3, 0.3, 0.3},
                size = {width = 12, height = 24},
                offset = {x = -6, y = 16}
            },
            accessories = {},  -- Initialize empty accessories table
            animationStates = {
                idle = {
                    head = {angle = 0, offset = {x = 0, y = 0}},
                    body = {angle = 0, offset = {x = 0, y = 0}},
                    arms = {angle = 0, offset = {x = 0, y = 0}},
                    legs = {angle = 0, offset = {x = 0, y = 0}}
                }
            },
            currentAnimation = "idle"
        },
        sprite = nil,
        isMoving = false,
        isAttacking = false,
        isBlocking = false,
        isDodging = false,
        isPaused = false,
        isInteracting = false,
        isInvulnerable = false,
        invulnerabilityTimer = 0,
        currentTile = nil,
        lastPosition = {x = 0, y = 0},
        collisionRect = {
            width = 0.8,
            height = 0.8
        }
    }

    -- Initialize character class if provided
    if characterClass then
        self.character = Character.new(characterClass)
    end

    -- Initialize input handling
    self.handleInput = function(self, inputManager, world, dt)
        -- Check if input manager exists
        if not inputManager then
            print("WARNING: No input manager provided to player.handleInput")
            return
        end

        -- Ensure the input manager has the getInputState method
        if not inputManager.getInputState or type(inputManager.getInputState) ~= "function" then
            print("ERROR: Input manager does not have a getInputState function")
            print("Input manager type: " .. type(inputManager))
            if type(inputManager) == "table" then
                for k, v in pairs(inputManager) do
                    print("  " .. tostring(k) .. ": " .. tostring(v))
                end
            end
            return
        end

        -- Get input state with error handling
        local success, input = pcall(function() return inputManager:getInputState() end)

        if not success then
            print("ERROR: Failed to get input state: " .. tostring(input))
            return
        end

        if not input then
            print("WARNING: Input manager returned nil input state")
            return
        end

        -- Validate input structure
        if type(input) ~= "table" then
            print("ERROR: Input is not a table, got " .. type(input))
            return
        end

        -- Debug output with safe access
        local moveX = input.move and input.move.x or 0
        local moveY = input.move and input.move.y or 0

        print("Player.handleInput called with input: move.x=" .. tostring(moveX) ..
              ", move.y=" .. tostring(moveY))

        -- Process movement with improved error handling
        if input.move then
            -- Ensure move values are numbers
            if type(moveX) ~= "number" then
                print("WARNING: input.move.x is not a number, got " .. type(moveX))
                moveX = 0
            end

            if type(moveY) ~= "number" then
                print("WARNING: input.move.y is not a number, got " .. type(moveY))
                moveY = 0
            end

            -- Update velocity based on input
            self.velocity.x = moveX * 150 -- Scale by movement speed
            self.velocity.y = moveY * 150 -- Scale by movement speed

            -- Update direction
            if moveX ~= 0 or moveY ~= 0 then
                -- Convert vector direction to string direction for animation
                local dirString = "down" -- Default direction

                if math.abs(moveX) > math.abs(moveY) then
                    -- Horizontal movement is dominant
                    dirString = moveX > 0 and "right" or "left"
                else
                    -- Vertical movement is dominant
                    dirString = moveY > 0 and "down" or "up"
                end

                -- Store both vector and string direction
                self.direction = dirString

                -- Ensure directionVector is properly initialized
                if not self.directionVector then
                    self.directionVector = {x = 0, y = 0}
                end

                self.directionVector.x = moveX
                self.directionVector.y = moveY

                self.isMoving = true
                print("Player moving: direction=" .. dirString ..
                      ", velocity=(" .. self.velocity.x .. "," .. self.velocity.y .. ")")
            else
                self.isMoving = false
            end
        end

        -- Process actions with safe access
        if input.action and input.action.pressed then
            print("Player action triggered")
            self:performAction(world)
        end

        -- Handle inventory with safe access
        if input.inventory and input.inventory.pressed then
            print("Player inventory toggled")
            self:toggleInventory()
        end

        -- Handle character menu with safe access
        if input.character and input.character.pressed then
            print("Player character menu toggled")
            self:toggleCharacterMenu()
        end

        -- Handle pause with safe access
        if input.togglePause and input.togglePause.pressed then
            print("Player pause toggled")
            self:togglePause()
        end
    end

    return setmetatable(self, { __index = Player })
end

--[[ -- Removed conflicting/outdated sprite drawing function
-- Draw the player
function Player:draw()
    if not self.sprite then return end

    -- Get current animation frame
    local frame = self:getCurrentFrame()

    -- Draw the sprite
    love.graphics.draw(self.sprite, self.spriteSheet[frame],
        self.position.x, self.position.y, 0, 1, 1)
end
--]]

-- Helper methods for input handling
function Player:performAction(world)
    if self.isAttacking or self.isBlocking or self.isDodging then return end

    -- Get the tile the player is facing
    local dirX, dirY = 0, 1  -- Default direction (down)

    -- Handle different direction formats
    if type(self.direction) == "string" then
        -- Convert string direction to vector
        if self.direction == "up" then
            dirX, dirY = 0, -1
        elseif self.direction == "down" then
            dirX, dirY = 0, 1
        elseif self.direction == "left" then
            dirX, dirY = -1, 0
        elseif self.direction == "right" then
            dirX, dirY = 1, 0
        end
    elseif type(self.direction) == "table" then
        -- Use vector direction directly
        dirX = self.direction.x or 0
        dirY = self.direction.y or 0
    end

    -- If we have a directionVector, use that instead (from new handleInput method)
    if type(self.directionVector) == "table" then
        local dvx = self.directionVector.x
        local dvy = self.directionVector.y

        if type(dvx) == "number" and type(dvy) == "number" then
            dirX = dvx
            dirY = dvy

            -- Normalize to get a clean direction vector
            local length = math.sqrt(dirX * dirX + dirY * dirY)
            if length > 0 then
                dirX = dirX / length
                dirY = dirY / length
            end
        end
    end

    local targetX = self.position.x + dirX
    local targetY = self.position.y + dirY

    -- Check for interactable objects
    if world and world.getTile then
        local targetTile = world:getTile(targetX, targetY)
        if targetTile and targetTile.interactable then
            self:interact(targetTile, world)
        else
            -- Default action (attack)
            self:attack(world)
        end
    else
        -- No world or getTile function, just attack
        self:attack(world)
    end
end

function Player:interact(targetTile, world)
    if self.isInteracting then return end

    self.isInteracting = true
    -- Handle interaction based on tile type
    if targetTile.onInteract then
        targetTile:onInteract(self, world)
    end
    self.isInteracting = false
end

function Player:attack(world)
    if self.isAttacking then return end

    self.isAttacking = true
    -- Perform attack animation and logic
    if self.character then
        self.character:performAttack(self, world)
    end
    self.isAttacking = false
end

function Player:toggleInventory()
    -- Toggle inventory UI
    if Engine.systems.uiSystem then
        Engine.systems.uiSystem.openCharacterScreen(self)
    end
end

function Player:toggleCharacterMenu()
    -- Toggle character menu UI
    if Engine.systems.uiSystem then
        Engine.systems.uiSystem.openCharacterScreen(self)
    end
end

function Player:togglePause()
    -- Toggle pause menu
    if Engine.systems.uiSystem then
        Engine.systems.uiSystem.mainMenuActive = true
    end
end

-- Update player state (called each frame)
function Player:update(dt, world)
    local currentTime = love.timer.getTime()
    if not self.lastUpdate then
        self.lastUpdate = 0
    end

    if currentTime - self.lastUpdate < self.updateInterval then
        return
    end

    self.lastUpdate = currentTime

    -- Store last position for collision detection
    self.lastPosition = {
        x = self.position.x,
        y = self.position.y
    }

    -- Update position based on velocity
    -- Note: Main movement is now handled by Controller.applyPlayerMovement
    -- This is a fallback for physics-based movement
    if self.velocity.x ~= 0 or self.velocity.y ~= 0 then
        self.position.x = self.position.x + self.velocity.x * dt
        self.position.y = self.position.y + self.velocity.y * dt
        self.isMoving = true
    end

    -- Update animation
    if self.isMoving then
        self.animation.timer = self.animation.timer + dt
        if self.animation.timer >= self.animation.speed then
            self.animation.timer = 0
            self.animation.frame = self.animation.frame + 1

            -- Get the correct direction key for animation frames
            local directionKey = "down" -- Default direction

            -- Handle different direction formats
            if type(self.direction) == "string" then
                -- Direction is already a string, use it directly if it exists in frames
                if self.animation.frames[self.direction] then
                    directionKey = self.direction
                end
            elseif type(self.direction) == "table" then
                -- Convert vector direction to string direction
                if self.direction.y > 0 then directionKey = "down"
                elseif self.direction.y < 0 then directionKey = "up"
                elseif self.direction.x > 0 then directionKey = "right"
                elseif self.direction.x < 0 then directionKey = "left"
                end
            end

            -- If we have a directionVector, use that instead (from new handleInput method)
            if self.directionVector then
                local dvx = self.directionVector.x or 0
                local dvy = self.directionVector.y or 0

                if math.abs(dvx) > math.abs(dvy) then
                    -- Horizontal movement is dominant
                    directionKey = dvx > 0 and "right" or "left"
                else
                    -- Vertical movement is dominant
                    directionKey = dvy > 0 and "down" or "up"
                end
            end

            -- Make sure the animation frames exist for this direction
            if self.animation.frames and self.animation.frames[directionKey] then
                if self.animation.frame > #self.animation.frames[directionKey] then
                    self.animation.frame = 1
                end
            else
                -- If no animation frames exist for this direction, reset to frame 1
                self.animation.frame = 1
                -- Log an error to help with debugging
                print("Warning: No animation frames found for direction: " .. tostring(directionKey))
            end
        end
    else
        self.animation.frame = 1
    end

    -- Update current tile if world is provided
    if world and world.getTile then
        local tileX = math.floor(self.position.x)
        local tileY = math.floor(self.position.y)
        self.currentTile = world:getTile(tileX, tileY)
    end

    -- Update character if it exists
    if self.character and self.character.update then
        self.character:update(dt)
    end
end

-- Take damage (delegates to character)
function Player:takeDamage(amount, source, damageType)
    if not self.character then return amount end
    return self.character:takeDamage(amount, damageType, source)
end

-- Serialize player data
function Player:serialize()
    return {
        id = self.id,
        name = self.name,
        position = self.position,
        direction = self.direction,
        state = self.state,
        character = self.character and self.character:serialize() or nil
    }
end

-- Deserialize player data
function Player:deserialize(data)
    self.id = data.id
    self.name = data.name
    self.position = data.position
    self.direction = data.direction
    self.state = data.state

    if data.character then
        self.character = Character.new()
        self.character:deserialize(data.character)
    end
end

return Player
