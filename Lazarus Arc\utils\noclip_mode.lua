-- utils/noclip_mode.lua
-- No-clip mode functionality for debugging
--
-- ⚠️  SECURITY WARNING: This module allows bypassing collision detection
-- which could be exploited in MMO environments for cheating (wall hacking,
-- accessing restricted areas, etc.). MUST be disabled in production builds.
--
-- TODO: Add conditional compilation or build flags to exclude this entirely
-- from release builds.

local NoClipMode = {}

-- Initialize no-clip mode
function NoClipMode.init()
    NoClipMode.enabled = false
    NoClipMode.originalValues = {}
    
    -- No-clip settings
    NoClipMode.speedMultiplier = 3.0 -- Faster movement in no-clip
    NoClipMode.verticalMovement = true -- Allow vertical movement
    NoClipMode.showIndicator = true
    
    -- Movement keys for vertical movement
    NoClipMode.keys = {
        up = "space",
        down = "lshift"
    }
    
    -- Visual settings
    NoClipMode.indicatorColor = {0.2, 0.8, 1, 0.8}
    NoClipMode.playerAlpha = 0.7 -- Make player semi-transparent
end

-- Toggle no-clip mode
function NoClipMode.toggle()
    NoClipMode.enabled = not NoClipMode.enabled
    
    if NoClipMode.enabled then
        NoClipMode.activate()
        print("No-Clip Mode activated - you can now move through walls")
    else
        NoClipMode.deactivate()
        print("No-Clip Mode deactivated - collision detection restored")
    end
    
    return NoClipMode.enabled
end

-- Activate no-clip mode
function NoClipMode.activate()
    NoClipMode.enabled = true
    print("No-Clip: Collision detection disabled, enhanced movement enabled")
end

-- Deactivate no-clip mode
function NoClipMode.deactivate()
    NoClipMode.enabled = false
    NoClipMode.restoreOriginalValues()
    print("No-Clip: Normal collision detection restored")
end

-- Store original player values
function NoClipMode.storeOriginalValues(player)
    if not player then return end
    
    NoClipMode.originalValues = {
        speed = player.speed or player.moveSpeed,
        collisionEnabled = player.collisionEnabled,
        gravity = player.gravity,
        canFly = player.canFly or false
    }
end

-- Restore original player values
function NoClipMode.restoreOriginalValues()
    -- Values will be restored when no-clip is disabled
end

-- Update player with no-clip effects
function NoClipMode.updatePlayer(player, dt)
    if not NoClipMode.enabled or not player then return end
    
    -- Store original values on first activation
    if not NoClipMode.originalValues.speed then
        NoClipMode.storeOriginalValues(player)
    end
    
    -- Disable collision detection
    player.collisionEnabled = false
    
    -- Disable gravity
    if player.gravity then
        player.gravity = 0
    end
    
    -- Enable flying
    player.canFly = true
    
    -- Enhance movement speed
    if player.speed then
        if not NoClipMode.originalValues.speed then
            NoClipMode.originalValues.speed = player.speed
        end
        player.speed = NoClipMode.originalValues.speed * NoClipMode.speedMultiplier
    elseif player.moveSpeed then
        if not NoClipMode.originalValues.speed then
            NoClipMode.originalValues.speed = player.moveSpeed
        end
        player.moveSpeed = NoClipMode.originalValues.speed * NoClipMode.speedMultiplier
    end
    
    -- Handle vertical movement
    if NoClipMode.verticalMovement then
        NoClipMode.handleVerticalMovement(player, dt)
    end
end

-- Handle vertical movement input
function NoClipMode.handleVerticalMovement(player, dt)
    if not player.position then return end
    
    local verticalSpeed = (player.speed or player.moveSpeed or 100) * dt
    
    -- Move up
    if love.keyboard.isDown(NoClipMode.keys.up) then
        player.position.y = player.position.y - verticalSpeed
    end
    
    -- Move down
    if love.keyboard.isDown(NoClipMode.keys.down) then
        player.position.y = player.position.y + verticalSpeed
    end
end

-- Check if collision should be ignored
function NoClipMode.shouldIgnoreCollision(entity, obstacle)
    if not NoClipMode.enabled then return false end
    
    -- If the entity is the player and no-clip is enabled, ignore all collisions
    if entity and entity.isPlayer then
        return true
    end
    
    return false
end

-- Modify collision detection
function NoClipMode.modifyCollision(entity, newX, newY, world)
    if not NoClipMode.enabled then return newX, newY end
    
    -- If entity is player, allow movement to any position
    if entity and entity.isPlayer then
        return newX, newY
    end
    
    -- For other entities, use normal collision detection
    return newX, newY
end

-- Check if entity can move to position
function NoClipMode.canMoveTo(entity, x, y, world)
    if not NoClipMode.enabled then return true end -- Let normal collision handle it
    
    -- If entity is player and no-clip is enabled, can move anywhere
    if entity and entity.isPlayer then
        return true
    end
    
    return true -- Let normal collision handle other entities
end

-- Draw no-clip indicator
function NoClipMode.drawIndicator(x, y)
    if not NoClipMode.enabled or not NoClipMode.showIndicator then return end
    
    love.graphics.push()
    
    -- Draw background
    love.graphics.setColor(NoClipMode.indicatorColor[1], NoClipMode.indicatorColor[2], 
                          NoClipMode.indicatorColor[3], 0.3)
    love.graphics.rectangle("fill", x, y, 160, 40)
    
    -- Draw border
    love.graphics.setColor(NoClipMode.indicatorColor)
    love.graphics.setLineWidth(1)
    love.graphics.rectangle("line", x, y, 160, 40)
    
    -- Draw text
    love.graphics.setColor(1, 1, 1, 1)
    love.graphics.print("NO-CLIP MODE", x + 5, y + 3)
    love.graphics.print("Space: Up, Shift: Down", x + 5, y + 18)
    
    love.graphics.pop()
end

-- Draw player with no-clip effects
function NoClipMode.drawPlayer(player, originalDrawFunction)
    if not NoClipMode.enabled then
        -- Draw normally
        if originalDrawFunction then
            originalDrawFunction(player)
        end
        return
    end
    
    love.graphics.push()
    
    -- Make player semi-transparent to indicate no-clip mode
    love.graphics.setColor(1, 1, 1, NoClipMode.playerAlpha)
    
    -- Draw player with modified alpha
    if originalDrawFunction then
        originalDrawFunction(player)
    end
    
    -- Draw no-clip effect around player
    if player.position then
        love.graphics.setColor(NoClipMode.indicatorColor[1], NoClipMode.indicatorColor[2], 
                              NoClipMode.indicatorColor[3], 0.3)
        love.graphics.circle("line", player.position.x, player.position.y, 25)
    end
    
    love.graphics.pop()
end

-- Handle key press for no-clip specific controls
function NoClipMode.keyPressed(key)
    if not NoClipMode.enabled then return false end
    
    -- Handle any specific no-clip key commands here
    return false -- Let other systems handle the key
end

-- Get no-clip status for UI display
function NoClipMode.getStatus()
    if not NoClipMode.enabled then
        return "Disabled"
    end
    
    return string.format("Active (Speed: %.1fx, Vertical: %s)", 
                        NoClipMode.speedMultiplier,
                        NoClipMode.verticalMovement and "Yes" or "No")
end

-- Set speed multiplier
function NoClipMode.setSpeedMultiplier(multiplier)
    NoClipMode.speedMultiplier = math.max(0.1, math.min(10.0, multiplier))
    print("No-Clip speed multiplier set to: " .. NoClipMode.speedMultiplier)
end

-- Toggle vertical movement
function NoClipMode.toggleVerticalMovement()
    NoClipMode.verticalMovement = not NoClipMode.verticalMovement
    print("No-Clip vertical movement: " .. (NoClipMode.verticalMovement and "enabled" or "disabled"))
    return NoClipMode.verticalMovement
end

-- Set movement keys
function NoClipMode.setMovementKeys(upKey, downKey)
    NoClipMode.keys.up = upKey or "space"
    NoClipMode.keys.down = downKey or "lshift"
    print("No-Clip movement keys set - Up: " .. NoClipMode.keys.up .. ", Down: " .. NoClipMode.keys.down)
end

-- Check if position is valid (for other systems to query)
function NoClipMode.isValidPosition(x, y, world)
    if NoClipMode.enabled then
        return true -- All positions are valid in no-clip mode
    end
    
    -- Let normal collision detection handle this
    return true
end

-- Hook into movement system
function NoClipMode.onPlayerMove(player, deltaX, deltaY, world)
    if not NoClipMode.enabled then
        return deltaX, deltaY -- Normal movement
    end
    
    -- In no-clip mode, allow any movement
    return deltaX, deltaY
end

-- Hook into collision system
function NoClipMode.onCollisionCheck(entity, x, y, world)
    if NoClipMode.enabled and entity and entity.isPlayer then
        return false -- No collision for player
    end
    
    return nil -- Let normal collision system handle it
end

return NoClipMode
