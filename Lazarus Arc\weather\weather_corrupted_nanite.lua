-- weather/corrupted_nanite.lua
-- Corrupted nanite storm weather pattern - destructive nanite swarm with Hawken virus influence
-- Inspired by <PERSON><PERSON>'s virus outbreak events

local CorruptedNaniteWeather = {
    id = "corrupted_nanite",
    name = "Hawken Virus Storm",
    
    -- Visual properties
    visual = {
        skyColor = {r = 255, g = 50, b = 50}, -- Red sky
        sunIntensity = 0.3,
        cloudCoverage = 0.9,
        cloudColor = {r = 200, g = 0, b = 0}, -- Dark red clouds
        ambientLightLevel = 0.4,
        naniteColor = {r = 255, g = 0, b = 0} -- Red nanite color
    },
    
    -- Environment modifiers
    environment = {
        temperature = 1.5, -- Increased temperature
        visibility = 0.4, -- Poor visibility
        humidity = 0.3, -- Low humidity
        windStrength = 0.8, -- Strong wind
        naniteDensity = 0.9, -- High corrupted nanite concentration
        virusLevel = 1.0, -- Maximum virus influence
        corruptionSpread = 1.2 -- Enhanced virus spread rate
    },
    
    -- Particle systems
    particles = {
        corruptedNanites = {
            enabled = true,
            intensity = 0.9,
            color = {r = 255, g = 0, b = 0},
            size = {min = 2, max = 4},
            speed = {min = 150, max = 300}
        },
        corruptionBeams = {
            enabled = true,
            intensity = 0.7,
            color = {r = 200, g = 0, b = 0},
            size = {min = 3, max = 6},
            speed = {min = 100, max = 200}
        },
        virusWaves = {
            enabled = true,
            intensity = 0.6,
            color = {r = 255, g = 100, b = 0},
            size = {min = 4, max = 8},
            speed = {min = 50, max = 100}
        }
    },
    
    -- Sound effects
    sounds = {
        ambient = "hawken_virus_ambient",
        corruption = "hawken_corruption_effect",
        virus = "hawken_virus_warning",
        mechCorruption = "hawken_mech_corruption",
        volume = 0.8
    },
    
    -- Effects on game entities and tiles
    effects = {
        -- Damage effects
        damageRate = 5.0, -- High damage per second
        equipmentDamageRate = 3.0, -- Severe equipment damage
        virusInfectionChance = 0.2, -- Chance for virus infection
        maxInfections = 5, -- Maximum number of infections per entity
        
        -- Movement effects
        movementSpeedMultiplier = 0.7, -- Reduced speed
        
        -- Energy effects
        energyDrainRate = 2.0, -- Severe energy drain
        
        -- Shield effects
        shieldEffectiveness = 0.4, -- Severely reduced shield effectiveness
        
        -- Sensor effects
        sensorRange = 0.5, -- Severely reduced sensor range
        
        -- Structure effects
        structureDamageMultiplier = 2.0, -- Double structure damage
        
        -- Hawken-specific effects
        mechCorruptionRate = 3.0, -- Enhanced corruption rate for mechs
        weaponOverheatRate = 2.0, -- Increased weapon heat generation
        boostDrainRate = 2.5, -- Faster boost energy drain
        virusSpreadRadius = 1.5 -- Increased virus spread radius
    },
    
    -- Transition probabilities to other weather (per game hour)
    transitions = {
        clear = 0.1,
        cloudy = 0.2,
        rain = 0.1,
        -- Implied: 0.6 chance to stay corrupted nanite storm
    },
    
    -- Day/night cycle modifiers
    timeModifiers = {
        dawn = {
            skyColor = {r = 255, g = 70, b = 70},
            ambientLightLevel = 0.3
        },
        day = {
            -- Uses default properties
        },
        dusk = {
            skyColor = {r = 255, g = 30, b = 30},
            ambientLightLevel = 0.2
        },
        night = {
            skyColor = {r = 200, g = 0, b = 0},
            ambientLightLevel = 0.1,
            naniteIntensity = 1.3, -- More intense corrupted nanites at night
            virusSpreadRadius = 2.0 -- Increased virus spread at night
        }
    }
}

-- Initialize the weather pattern
function CorruptedNaniteWeather.init(world)
    print("Initializing Hawken virus storm weather")
    
    -- Set global light level
    world.lightLevel = CorruptedNaniteWeather.visual.ambientLightLevel
    
    -- Start ambient sounds
    if CorruptedNaniteWeather.sounds.ambient then
        -- Play ambient corrupted nanite sound
        print("Playing sound: " .. CorruptedNaniteWeather.sounds.ambient)
    end
    
    -- Create particle systems
    if CorruptedNaniteWeather.particles.corruptedNanites.enabled then
        -- Create corrupted nanite particles
        print("Creating Hawken virus nanite particles with intensity: " .. CorruptedNaniteWeather.particles.corruptedNanites.intensity)
    end
    
    if CorruptedNaniteWeather.particles.corruptionBeams.enabled then
        -- Create corruption beam particles
        print("Creating Hawken corruption beam particles with intensity: " .. CorruptedNaniteWeather.particles.corruptionBeams.intensity)
    end
    
    if CorruptedNaniteWeather.particles.virusWaves.enabled then
        -- Create virus wave particles
        print("Creating Hawken virus wave particles with intensity: " .. CorruptedNaniteWeather.particles.virusWaves.intensity)
    end
    
    -- Display warning message to players if messaging system exists
    if world.messageSystem then
        world.messageSystem:broadcast("CRITICAL WARNING: Hawken virus outbreak detected. Corrupted nanite storm approaching. All mechs seek immediate shelter!")
    else
        print("CRITICAL WARNING: Hawken virus outbreak detected. Corrupted nanite storm approaching. All mechs seek immediate shelter!")
    end
end

-- Update function called every frame
function CorruptedNaniteWeather.update(world, dt)
    -- Update day/night cycle based on world time
    local timeOfDay = world.timeOfDay or "day"
    local modifiers = CorruptedNaniteWeather.timeModifiers[timeOfDay]
    
    if modifiers then
        -- Apply time-specific modifiers
        if modifiers.skyColor then
            -- Change sky color
        end
        
        if modifiers.ambientLightLevel then
            world.lightLevel = modifiers.ambientLightLevel
        end
        
        if modifiers.naniteIntensity then
            CorruptedNaniteWeather.particles.corruptedNanites.intensity = modifiers.naniteIntensity
        end
        
        if modifiers.virusSpreadRadius then
            CorruptedNaniteWeather.effects.virusSpreadRadius = modifiers.virusSpreadRadius
        end
    end
    
    -- Apply effects to game world if entity system exists
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            -- Check if entity is exposed to corrupted nanites
            if entity.isExposed then
                -- Apply damage
                if entity.health then
                    entity.health = entity.health - 
                        (CorruptedNaniteWeather.effects.damageRate * dt)
                end
                
                -- Apply equipment damage
                if entity.equipment then
                    for _, item in pairs(entity.equipment) do
                        if item.durability then
                            item.durability = item.durability - 
                                (CorruptedNaniteWeather.effects.equipmentDamageRate * dt)
                        end
                    end
                end
                
                -- Apply movement speed reduction
                if entity.speed then
                    entity.speed = entity.speed * CorruptedNaniteWeather.effects.movementSpeedMultiplier
                end
                
                -- Apply energy drain
                if entity.energy then
                    entity.energy = entity.energy - 
                        (CorruptedNaniteWeather.effects.energyDrainRate * dt)
                end
                
                -- Chance for virus infection
                if math.random() < CorruptedNaniteWeather.effects.virusInfectionChance * dt then
                    if not entity.infections then entity.infections = 0 end
                    if entity.infections < CorruptedNaniteWeather.effects.maxInfections then
                        entity.infections = entity.infections + 1
                        -- Apply virus effects
                        if entity.onVirusInfection then
                            entity:onVirusInfection()
                        end
                    end
                end
                
                -- Hawken-specific effects
                if entity.categories and table.contains(entity.categories, "mech") then
                    -- Enhanced mech corruption
                    if entity.health and entity.maxHealth then
                        entity.health = entity.health - 
                            (CorruptedNaniteWeather.effects.mechCorruptionRate * dt)
                    end
                    
                    -- Increased weapon heat
                    if entity.weapons then
                        for _, weapon in pairs(entity.weapons) do
                            if weapon.heat then
                                weapon.heat = math.min(weapon.maxHeat,
                                    weapon.heat + (CorruptedNaniteWeather.effects.weaponOverheatRate * dt))
                            end
                        end
                    end
                    
                    -- Faster boost drain
                    if entity.boost and entity.maxBoost then
                        entity.boost = math.max(0,
                            entity.boost - (CorruptedNaniteWeather.effects.boostDrainRate * dt))
                    end
                    
                    -- Virus spread to nearby mechs
                    if math.random() < 0.1 and world.getEntitiesInRadius then
                        local nearbyMechs = world:getEntitiesInRadius(entity.position, 
                            CorruptedNaniteWeather.effects.virusSpreadRadius)
                        if nearbyMechs then
                            for _, nearbyMech in pairs(nearbyMechs) do
                                if nearbyMech.categories and table.contains(nearbyMech.categories, "mech") then
                                    if not nearbyMech.infections then nearbyMech.infections = 0 end
                                    if nearbyMech.infections < CorruptedNaniteWeather.effects.maxInfections then
                                        nearbyMech.infections = nearbyMech.infections + 1
                                        if nearbyMech.onVirusInfection then
                                            nearbyMech:onVirusInfection()
                                        end
                                    end
                                end
                            end
                        end
                    end
                end
            end
            
            -- Special effects for different entity types
            if entity.categories then
                -- Effect on structures
                if table.contains(entity.categories, "structure") then
                    if entity.health then
                        entity.health = entity.health - 
                            (CorruptedNaniteWeather.effects.damageRate * 
                             CorruptedNaniteWeather.effects.structureDamageMultiplier * dt)
                    end
                end
                
                -- Effect on shields
                if table.contains(entity.categories, "shield") then
                    if entity.effectiveness then
                        entity.effectiveness = entity.effectiveness * 
                            CorruptedNaniteWeather.effects.shieldEffectiveness
                    end
                end
                
                -- Effect on sensors and vision devices
                if table.contains(entity.categories, "sensor") then
                    if entity.range then
                        entity.range = entity.range * CorruptedNaniteWeather.effects.sensorRange
                    end
                end
            end
        end
    end
    
    -- Random corruption effects
    if math.random() < 0.1 then
        -- Play corruption sound
        print("Playing sound: " .. CorruptedNaniteWeather.sounds.corruption)
    end
    
    -- Random virus warning effects
    if math.random() < 0.05 then
        -- Play virus warning sound
        print("Playing sound: " .. CorruptedNaniteWeather.sounds.virus)
    end
    
    -- Random mech corruption effects
    if math.random() < 0.03 then
        -- Play mech corruption sound
        print("Playing sound: " .. CorruptedNaniteWeather.sounds.mechCorruption)
    end
end

-- Clean up when weather changes
function CorruptedNaniteWeather.cleanUp(world)
    -- Stop ongoing sounds and particle effects
    print("Hawken virus storm weather ending")
    
    -- Notify players if messaging system exists
    if world.messageSystem then
        world.messageSystem:broadcast("Hawken virus storm dissipating. Air quality returning to normal levels. System decontamination recommended.")
    else
        print("Hawken virus storm dissipating. Air quality returning to normal levels. System decontamination recommended.")
    end
    
    -- Reset virus infections if entities exist
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            if entity.infections and entity.infections > 0 then
                entity.infections = 0
                if entity.onVirusCleared then
                    entity:onVirusCleared()
                end
            end
        end
    end
end

-- Table contains helper function
function table.contains(table, element)
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

return CorruptedNaniteWeather 