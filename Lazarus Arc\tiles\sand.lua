-- tiles/sand.lua
local SandTile = {
    id = "sand",
    name = "Sand",
    passable = true,
    variants = 6, -- e.g., Fine Sand, Coarse Sand, Wet Sand, Shell-Strewn, Rippled, Damp Sand

    -- Entities that can spawn here (varies if coastal, riverside, or desert edge)
    spawns = {"crab", "shorebird", "sand_flea", "driftwood", "seashell", "lizard_small"}, -- Mix of coastal and generic sand creatures/items

    -- Movement properties
    movementSpeed = 0.9, -- Generally easier than desert sand/dunes, similar to rocky ground

    -- Sand-specific properties
    moistureLevel = 0.1, -- Base moisture (0-1), higher near water
    compactness = 0.4, -- How firm the sand is (0-1), affected by moisture
    temperatureSensitivity = 0.8, -- How quickly it heats/cools (0-1)

    -- Interaction function
    interact = function(tile, entity)
        -- Digging in the sand
        if math.random() < 0.2 then
            local buriedItems = {
                {id="seashell_common", chance=0.5, condition=tile.moistureLevel > 0.3}, -- More shells near water
                {id="crab_small_buried", chance=0.3, condition=tile.moistureLevel > 0.2}, -- Creature interaction
                {id="lost_coin", chance=0.15},
                {id="smooth_sea_glass", chance=0.1, condition=tile.moistureLevel > 0.3},
                {id="message_in_a_bottle", chance=0.05, condition=tile.isCoastal} -- Rare coastal item
            }
            -- Check conditions before adding to potential finds
            local possibleFinds = {}
            for _, item in ipairs(buriedItems) do
                 if item.condition == nil or item.condition == true then
                     table.insert(possibleFinds, item)
                 end
            end

            if #possibleFinds > 0 then
                 for _, item in ipairs(possibleFinds) do
                     if math.random() < item.chance then
                         local message = "You dig in the sand and find a " .. item.id .. "."
                         if item.id == "crab_small_buried" then
                              message = "You dig in the sand and uncover a small crab, which quickly scurries away!"
                              -- Could potentially trigger a small event or add crab item
                              return {success=true, message=message, effects={{type="spawn_temporary_entity", id="crab_small", duration=10}}}
                         end
                         return {
                             success = true,
                             message = message,
                             effects = {{type="add_item", item=item.id, quantity=1}}
                         }
                     end
                 end
            end
        end

        -- Examine the sand's condition
        local moistureDesc = "The sand is dry and loose underfoot."
        if tile.moistureLevel > 0.7 then
             moistureDesc = "The sand is wet and firmly packed near the water's edge."
        elseif tile.moistureLevel > 0.3 then
             moistureDesc = "The sand is damp and cool to the touch."
        end
        
        return {
            success = false,
            message = moistureDesc
        }
    end,

    -- Weather effects
    weatherEffects = {
        rain = function(tile)
            tile.moistureLevel = math.min(1.0, tile.moistureLevel + 0.5)
            tile.compactness = math.min(0.8, tile.compactness + 0.3) -- Rain makes sand firmer
            tile.movementSpeed = 0.95 -- Slightly faster when damp/firm
            return "visual_effect", "wet_sand"
        end,
        
        heat = function(tile)
            tile.moistureLevel = math.max(0, tile.moistureLevel - 0.2 * tile.temperatureSensitivity)
            tile.compactness = math.max(0.2, tile.compactness - 0.1) -- Dries out, becomes looser
            tile.movementSpeed = 0.9
            if tile.moistureLevel == 0 and world.temperature > 40 then
                 tile.isHot = true -- Surface can get hot if very dry
            end
            return nil
        end,

        sun = function(tile)
            tile.moistureLevel = math.max(0, tile.moistureLevel - 0.1 * tile.temperatureSensitivity)
            tile.compactness = math.max(0.2, tile.compactness - 0.05)
            tile.movementSpeed = 0.9
            tile.isHot = false
            return "remove_visual_effect", "wet_sand"
        end,

        wind = function(tile)
            if tile.compactness < 0.5 and world.windStrength > 0.5 then -- Only affects loose sand
                 return "visual_effect", "sand_drifting_light"
            end
            return nil
        end,

        freeze = function(tile) -- Freezing affects damp sand
             if tile.moistureLevel > 0.2 then
                 tile.isFrozen = true
                 tile.compactness = 0.9 -- Frozen sand is very hard
                 tile.movementSpeed = 1.0 -- Easier to walk on frozen sand
                 tile.footstepSound = "frozen_ground_step"
                 return "visual_effect", "frozen_sand"
             end
             return nil
        end
    },

     -- Time effects
    timeEffects = {
        -- Tide effects could be implemented here if near a large body of water
        -- high_tide = function(tile) tile.moistureLevel = 1.0 end,
        -- low_tide = function(tile) tile.moistureLevel = 0.3 end, 

        night = function(tile)
            tile.temperatureOffset = -5 * tile.temperatureSensitivity -- Cools reasonably fast
            tile.ambientSound = "sand_wind_gentle" -- Or "waves_lapping" if coastal
            -- Different spawns (e.g., ghost crabs)
            return nil
        end,
        
        dawn = function(tile)
             tile.temperatureOffset = 0
             tile.ambientSound = "sand_wind_gentle" -- Or "shorebirds_morning"
             -- Reset freeze effect if applicable
             if tile.isFrozen and world.temperature > 0 then
                  tile.isFrozen = false
                  tile.compactness = 0.5 -- Reset to average
                  tile.footstepSound = "sand_step" -- Reset sound
                  return "remove_visual_effect", "frozen_sand"
             end
            return nil
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {0.85, 0.78, 0.65}, -- Default light beige/yellow
        variants = {
            {name = "fine_sand", texture="sand_fine"},
            {name = "coarse_sand", texture="sand_coarse", color_shift={0, -0.05, -0.05}},
            {name = "wet_sand", color_shift={-0.2, -0.15, -0.1}, condition = function(tile) return tile.moistureLevel > 0.5 end, shininess=0.2},
            {name = "shell_strewn", overlay_objects={{name="seashell", chance=0.6}}, condition = function(tile) return tile.isCoastal end},
            {name = "rippled", pattern="ripple_small"},
            {name = "damp_sand", color_shift={-0.1, -0.08, -0.05}, condition = function(tile) return tile.moistureLevel > 0.2 and tile.moistureLevel <= 0.5 end}
        },
        decoration_objects = {
            {name = "driftwood_small", chance = 0.1, condition = function(tile) return tile.isCoastal end},
            {name = "seaweed_patch", chance = 0.15, condition = function(tile) return tile.isCoastal and tile.moistureLevel > 0.6 end},
            {name = "smooth_pebble", chance = 0.2},
            {name = "footprints", chance = 0.3} -- Decal overlay
        },
         weather_overlays = {
             wet = {shininess = 0.2, when = "moistureLevel > 0.5"},
             frozen = {color = {0.9, 0.9, 0.95}, shininess=0.4, when = "isFrozen"}
         }
    },
    
    -- Audio properties
    ambientSound = "sand_wind_gentle", -- Could be replaced by context (e.g., beach waves)
    footstepSound = "sand_step", -- Default sound
     specialSounds = {
        {name = "crab_scuttle", trigger = "entity_nearby", chance = 0.1, condition = function(tile) return tile.moistureLevel > 0.1 end},
        {name = "shorebird_cry", trigger = "daytime", chance = 0.08, condition = function(tile) return tile.isCoastal end},
        {name = "shell_crunch", trigger = "on_walk", chance = 0.1, condition = function(tile) return tile.hasShells end} -- Assuming tile might track shell density
    },

    -- Connections to other tiles (Versatile connector)
    connects_to = {
        "sand", -- Itself
        "shallow_water", -- Common connection
        "water", -- Beaches
        "grass", -- Transition to lusher areas
        "dirt", -- Common transition
        "desert_sand", -- Edge of desert
        "dunes", -- Edge of dune sea
        "rocky_ground", -- Rocky beaches or transitions
        "mud" -- If near stagnant water or after heavy rain
    },

    -- Pathfinding properties
     pathfinding = {
        travel_cost = 1.05, -- Slightly more costly than grass/dirt
    },

    -- Effects on entities that walk on this tile
    onWalk = function(tile, entity)
        local sound = tile.footstepSound or "sand_step"
        local fatigue = 0.08 -- Base fatigue
        
        if tile.isFrozen then
             sound = "frozen_ground_step"
             fatigue = 0.05 -- Easier on frozen ground
        elseif tile.moistureLevel > 0.5 then
             sound = "wet_sand_step"
             fatigue = 0.07 -- Slightly easier on packed wet sand
        end

        -- Adjust sound based on special sounds condition?
        if tile.hasShells and math.random() < 0.1 then
             sound = "shell_crunch"
        end

        return {
            sound = sound,
            volume = 0.85,
            effects = {
                {type="increase_fatigue", amount = fatigue},
                 -- Effect for hot sand?
                 {type="apply_status", status="hot_sand", duration=1, condition=tile.isHot} 
            }
        }
    end,
    
    -- Effect when discovering sand area
    onDiscover = function(tile, entity)
        local message = "You've reached an area of sand."
        if tile.isCoastal then
             message = "You arrive at a sandy shoreline."
        elseif tile.isDesertEdge then
             message = "The terrain transitions into loose sand at the edge of the desert."
        end

        if entity.journal and entity.journal.addDiscovery then
             entity.journal.addDiscovery({
                type = "terrain",
                name = "Sand",
                location = {x = tile.x, y = tile.y},
                notes = "Transitional sandy area." .. (tile.isCoastal and " Likely coastal." or "")
            })
        end
        return {
            message = message,
            effects = {
                {type = "reveal_map", radius = 2}
            }
        }
    end
}

function SandTile.init(world)
    print("Sand tile module initialized")
    -- Register with relevant systems if needed
    if world.systems and world.systems.geography then
        world.systems.geography:registerTerrainType("sand", {base_movement_cost=1.05})
    end
end

return SandTile