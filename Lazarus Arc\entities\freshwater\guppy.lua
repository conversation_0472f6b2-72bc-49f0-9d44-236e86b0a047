local Guppy = {
    id = "guppy",
    name = "Guppy",
    type = "guppy",

    -- Entity categories
    categories = { "animal", "fish", "small", "prey", "decorative" },

    -- Threat categories
    threatCategories = { "predator", "medium", "large" }, -- Vulnerable to many
    foodCategories = { "algae", "insect" },

    -- Stats
    maxHealth = 2, -- Very fragile
    health = 2,
    speed = 2.0,

    -- Behaviors
    behaviors = { "schooling", "flee", "Foraging" },

    -- Behavior configurations
    behaviorConfigs = {
        schooling = {
            neighborRadius = 2, -- Small, tight schools
            separationDistance = 0.4,
        },
        flee = {
            useCategories = true,
            moveSpeed = 4.0,
            detectionRadius = 3
        },
        觅食 = {
            foodTypes = { "algae", "insect" },
            foodRadius = 1.5
        }
    },

    -- Appearance
    appearance = {
        sprite = "guppy", -- Replace with your guppy sprite
        scale = 0.6,
        animations = {
            "swim",
            "flee"
        },
        -- Variety of colors
        variants = {
            "red",
            "blue",
            "yellow",
            "green",
            "orange"
        }
    },

    -- Sound effects
    sounds = {
        -- May not have distinct sounds
    }
}

-- Initialize the guppy entity
function Guppy.init(entity, world)
    entity.position = entity.position or { x = 0, y = 0 }
    entity.velocity = entity.velocity or { x = 0, y = 0 }

    -- Initialize behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.init then
            behavior.init(entity, entity.behaviorConfigs[behaviorName])
        end
    end

    return entity
end

return Guppy