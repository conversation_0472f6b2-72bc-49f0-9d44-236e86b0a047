-- weather/overcast.lua
-- Overcast weather pattern - cloudy skies with reduced visibility

local OvercastWeather = {
    id = "overcast",
    name = "Overcast",
    
    -- Visual properties
    visual = {
        skyColor = {r = 150, g = 150, b = 150}, -- Gray sky
        sunIntensity = 0.4,
        cloudCoverage = 0.8,
        cloudColor = {r = 180, g = 180, b = 180}, -- Light gray clouds
        ambientLightLevel = 0.5,
        fogColor = {r = 200, g = 200, b = 200} -- Light gray fog
    },
    
    -- Environment modifiers
    environment = {
        temperature = 0.9, -- Slightly cooler
        visibility = 0.6, -- Reduced visibility
        humidity = 0.7, -- High humidity
        windStrength = 0.4, -- Moderate wind
        fogDensity = 0.5, -- Moderate fog
        moistureLevel = 0.6 -- Moderate moisture
    },
    
    -- Particle systems
    particles = {
        clouds = {
            enabled = true,
            intensity = 0.7,
            color = {r = 180, g = 180, b = 180},
            size = {min = 2, max = 4},
            speed = {min = 20, max = 40},
            image = "assets/particles/Complex/smoke/smoke_1.png",
            blendMode = "alpha",
            lifetime = {min = 2.0, max = 4.0},
            rotation = {min = 0, max = 360},
            alpha = {min = 0.3, max = 0.6}
        },
        fog = {
            enabled = true,
            intensity = 0.5,
            color = {r = 200, g = 200, b = 200},
            size = {min = 1, max = 3},
            speed = {min = 5, max = 15},
            image = "assets/particles/Complex/smoke/smoke_2.png",
            blendMode = "alpha",
            lifetime = {min = 3.0, max = 5.0},
            rotation = {min = 0, max = 360},
            alpha = {min = 0.2, max = 0.4}
        },
        mist = {
            enabled = true,
            intensity = 0.3,
            color = {r = 180, g = 180, b = 180},
            size = {min = 1, max = 2},
            speed = {min = 10, max = 20},
            image = "assets/particles/Complex/smoke/smoke_3.png",
            blendMode = "alpha",
            lifetime = {min = 1.5, max = 3.0},
            rotation = {min = 0, max = 360},
            alpha = {min = 0.1, max = 0.3}
        }
    },
    
    -- Sound effects
    sounds = {
        ambient = "overcast_ambient",
        wind = "overcast_wind",
        moisture = "overcast_moisture",
        volume = 0.3
    },
    
    -- Effects on game entities and tiles
    effects = {
        -- Movement effects
        movementSpeedMultiplier = 0.9, -- Slightly reduced speed
        
        -- Vision effects
        visionRange = 0.6, -- Reduced vision
        
        -- Temperature effects
        temperatureModifier = 0.9, -- Slightly cooler
        
        -- Energy effects
        energyDrainRate = 1.1, -- Slightly increased energy drain
        
        -- Shield effects
        shieldEffectiveness = 0.95, -- Slightly reduced shield effectiveness
        
        -- Sensor effects
        sensorRange = 0.7, -- Reduced sensor range
        
        -- Special effects
        slipChance = 0.1, -- Slight chance of slipping
        moistureDamage = 0.05 -- Very slight moisture damage
    },
    
    -- Transition probabilities to other weather (per game hour)
    transitions = {
        clear = 0.3,
        cloudy = 0.4,
        light_rain = 0.2,
        -- Implied: 0.1 chance to stay overcast
    },
    
    -- Day/night cycle modifiers
    timeModifiers = {
        dawn = {
            skyColor = {r = 140, g = 140, b = 140},
            ambientLightLevel = 0.4,
            fogIntensity = 0.8 -- Less intense fog at dawn
        },
        day = {
            -- Uses default properties
        },
        dusk = {
            skyColor = {r = 130, g = 130, b = 130},
            ambientLightLevel = 0.3,
            fogIntensity = 1.1 -- More intense fog at dusk
        },
        night = {
            skyColor = {r = 100, g = 100, b = 100},
            ambientLightLevel = 0.2,
            fogIntensity = 1.2 -- More intense fog at night
        }
    }
}

-- Helper function to safely set particle system properties
local function setupParticleSystem(ps, config)
    if not ps or type(ps) ~= "table" then
        print("Warning: Invalid particle system")
        return false
    end
    
    local methods = {
        setParticleLifetime = function() return ps:setParticleLifetime(config.lifetime.min, config.lifetime.max) end,
        setEmissionRate = function() return ps:setEmissionRate(config.emissionRate or 0) end,
        setSizeVariation = function() return ps:setSizeVariation(config.sizeVariation or 0.3) end,
        setLinearAcceleration = function() return ps:setLinearAcceleration(config.acceleration.x1 or 0, config.acceleration.y1 or 0, config.acceleration.x2 or 0, config.acceleration.y2 or 0) end,
        setColors = function() return ps:setColors(config.colors.r1 or 255, config.colors.g1 or 255, config.colors.b1 or 255, config.colors.a1 or 255, config.colors.r2 or 255, config.colors.g2 or 255, config.colors.b2 or 255, config.colors.a2 or 0) end,
        setBlendMode = function() return ps:setBlendMode(config.blendMode or "normal") end
    }
    
    for methodName, method in pairs(methods) do
        if type(ps[methodName]) ~= "function" then
            print("Warning: Particle system missing method: " .. methodName)
            return false
        end
        
        local success, err = pcall(method)
        if not success then
            print("Warning: Failed to call " .. methodName .. ": " .. tostring(err))
            return false
        end
    end
    
    return true
end

-- Initialize the weather pattern
function OvercastWeather.init(world)
    if not world then
        print("Warning: world parameter is nil in OvercastWeather.init")
        return
    end
    
    print("Initializing overcast weather")
    
    -- Set global light level
    world.lightLevel = OvercastWeather.visual.ambientLightLevel
    
    -- Start ambient sounds if weather system exists
    if world.weatherSystem then
        if OvercastWeather.sounds.ambient then
            world.weatherSystem:playSound(OvercastWeather.sounds.ambient, {
                verbose = false,  -- Don't print warnings if sound is missing
                loop = true,      -- Ambient sounds should loop
                volume = 0.5      -- Lower volume for ambient weather
            })
        end
    end
    
    -- Create particle systems
    if OvercastWeather.particles.clouds.enabled then
        -- Create cloud particles
        print("Creating cloud particles with intensity: " .. OvercastWeather.particles.clouds.intensity)
        local success, image = pcall(love.graphics.newImage, OvercastWeather.particles.clouds.image)
        if success and image then
            local success2, ps = pcall(love.graphics.newParticleSystem, image)
            if success2 and ps then
                local config = {
                    lifetime = OvercastWeather.particles.clouds.lifetime,
                    emissionRate = 10,
                    sizeVariation = 0.3,
                    acceleration = {x1 = -5, y1 = -5, x2 = 5, y2 = 5},
                    colors = {r1 = 255, g1 = 255, b1 = 255, a1 = 255, r2 = 180, g2 = 180, b2 = 180, a2 = 0},
                    blendMode = "alpha"
                }
                
                if setupParticleSystem(ps, config) then
                    OvercastWeather.cloudSystem = ps
                else
                    print("Warning: Failed to setup cloud particle system")
                end
            else
                print("Warning: Failed to create cloud particle system")
            end
        else
            print("Warning: Could not load cloud particle image: " .. OvercastWeather.particles.clouds.image)
        end
    end
    
    if OvercastWeather.particles.fog.enabled then
        -- Create fog particles
        print("Creating fog particles with intensity: " .. OvercastWeather.particles.fog.intensity)
        local success, image = pcall(love.graphics.newImage, OvercastWeather.particles.fog.image)
        if success and image then
            local success2, ps = pcall(love.graphics.newParticleSystem, image)
            if success2 and ps then
                local config = {
                    lifetime = OvercastWeather.particles.fog.lifetime,
                    emissionRate = 15,
                    sizeVariation = 0.2,
                    acceleration = {x1 = -2, y1 = -2, x2 = 2, y2 = 2},
                    colors = {r1 = 255, g1 = 255, b1 = 255, a1 = 255, r2 = 200, g2 = 200, b2 = 200, a2 = 0},
                    blendMode = "alpha"
                }
                
                if setupParticleSystem(ps, config) then
                    OvercastWeather.fogSystem = ps
                else
                    print("Warning: Failed to setup fog particle system")
                end
            else
                print("Warning: Failed to create fog particle system")
            end
        else
            print("Warning: Could not load fog particle image: " .. OvercastWeather.particles.fog.image)
        end
    end
    
    if OvercastWeather.particles.mist.enabled then
        -- Create mist particles
        print("Creating mist particles with intensity: " .. OvercastWeather.particles.mist.intensity)
        local success, image = pcall(love.graphics.newImage, OvercastWeather.particles.mist.image)
        if success and image then
            local success2, ps = pcall(love.graphics.newParticleSystem, image)
            if success2 and ps then
                local config = {
                    lifetime = OvercastWeather.particles.mist.lifetime,
                    emissionRate = 20,
                    sizeVariation = 0.2,
                    acceleration = {x1 = -1, y1 = -1, x2 = 1, y2 = 1},
                    colors = {r1 = 255, g1 = 255, b1 = 255, a1 = 255, r2 = 180, g2 = 180, b2 = 180, a2 = 0},
                    blendMode = "alpha"
                }
                
                if setupParticleSystem(ps, config) then
                    OvercastWeather.mistSystem = ps
                else
                    print("Warning: Failed to setup mist particle system")
                end
            else
                print("Warning: Failed to create mist particle system")
            end
        else
            print("Warning: Could not load mist particle image: " .. OvercastWeather.particles.mist.image)
        end
    end
    
    -- Display message to players if message system exists
    if world.messageSystem then
        world.messageSystem:broadcast("Overcast conditions detected. Reduced visibility.")
    else
        print("Overcast conditions detected. Reduced visibility.")
    end
end

-- Update function called every frame
function OvercastWeather.update(world, dt)
    if not world then
        print("Warning: world parameter is nil in OvercastWeather.update")
        return
    end
    
    -- Update day/night cycle based on world time
    local timeOfDay = world.timeOfDay or "day"
    local modifiers = OvercastWeather.timeModifiers[timeOfDay]
    
    if modifiers then
        -- Apply time-specific modifiers
        if modifiers.skyColor then
            -- Change sky color
        end
        
        if modifiers.ambientLightLevel then
            world.lightLevel = modifiers.ambientLightLevel
        end
        
        if modifiers.fogIntensity then
            OvercastWeather.particles.fog.intensity = modifiers.fogIntensity
            if OvercastWeather.fogSystem then
                OvercastWeather.fogSystem:setEmissionRate(15 * modifiers.fogIntensity)
            end
        end
    end
    
    -- Update particle systems
    if OvercastWeather.cloudSystem then
        OvercastWeather.cloudSystem:update(dt)
    end
    if OvercastWeather.fogSystem then
        OvercastWeather.fogSystem:update(dt)
    end
    if OvercastWeather.mistSystem then
        OvercastWeather.mistSystem:update(dt)
    end
    
    -- Apply effects to game world if entity system exists
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            -- Apply movement speed reduction
            if entity.speed then
                entity.speed = entity.speed * OvercastWeather.effects.movementSpeedMultiplier
            end
            
            -- Apply energy drain
            if entity.energy then
                entity.energy = entity.energy - 
                    (OvercastWeather.effects.energyDrainRate * dt)
            end
            
            -- Random slip chance
            if math.random() < OvercastWeather.effects.slipChance * dt then
                if entity.health then
                    entity.health = entity.health - 
                        (OvercastWeather.effects.moistureDamage * dt)
                end
            end
        end
    end
    
    -- Apply weather effects if weather system exists
    if world.weatherSystem then
        world.weatherSystem:applyEffects(OvercastWeather.environment)
    end
end

-- Clean up when weather changes
function OvercastWeather.cleanUp(world)
    if not world then
        print("Warning: world parameter is nil in OvercastWeather.cleanUp")
        return
    end
    
    -- Stop sounds if weather system exists
    if world.weatherSystem then
        if OvercastWeather.sounds.ambient then
            world.weatherSystem:stopSound(OvercastWeather.sounds.ambient)
        end
    end
    
    -- Clean up particle systems
    if OvercastWeather.cloudSystem then
        OvercastWeather.cloudSystem:stop()
    end
    if OvercastWeather.fogSystem then
        OvercastWeather.fogSystem:stop()
    end
    if OvercastWeather.mistSystem then
        OvercastWeather.mistSystem:stop()
    end
    
    -- Notify players if message system exists
    if world.messageSystem then
        world.messageSystem:broadcast("Overcast conditions clearing. Visibility improving.")
    else
        print("Overcast conditions clearing. Visibility improving.")
    end
    
    print("Overcast weather ending")
end

-- Table contains helper function
function table.contains(table, element)
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

return OvercastWeather 