@echo off
setlocal EnableDelayedExpansion

echo --- Starting Build Script ---

:: --- Configuration ---
set WINRAR_EXE="%ProgramFiles%\WinRAR\WinRAR.exe"
if not exist %WINRAR_EXE% set WINRAR_EXE="%ProgramFiles(x86)%\WinRAR\WinRAR.exe"
echo Using WinRAR: %WINRAR_EXE%

:: --- Script Logic ---
for %%* in (.) do set PROJECT_FOLDER_NAME=%%~n*
echo Project Folder Name: %PROJECT_FOLDER_NAME%

set TARGET_DIR=..\love-11.5-win64
set LOVE_FILE=%TARGET_DIR%\!PROJECT_FOLDER_NAME!.love
echo Target .love file path: %LOVE_FILE%

:: --- Exclusions ---
:: Exclude version control folder
set EXCLUDE_GIT=-x.git\
:: Exclude this build script itself
set EXCLUDE_SCRIPT=-x"001 - build.bat"
:: Exclude archive files
set EXCLUDE_ZIPS=-x*.zip
set EXCLUDE_LOVES=-x*.love
:: Exclude backup files
set EXCLUDE_BAKS=-x*.bak
set EXCLUDE_OLD=-x*.old
:: Exclude text files (like READMEs, lists, potentially the original .txt source files)
set EXCLUDE_TXT=-x*.txt
:: Exclude the specific GM tool script
:: IMPORTANT: Adjust path "gm_tools.lua" if the file is in a subfolder (e.g., "-xscripts\gm_tools.lua")
set EXCLUDE_GM_TOOLS=-xgm_tools.lua
:: --- Alternative: Exclude a whole directory for GM scripts ---
:: set EXCLUDE_GM_DIR=-xgm_scripts\

:: Combine all exclusions
:: Make sure only ONE of the GM exclusion variables (EXCLUDE_GM_TOOLS or EXCLUDE_GM_DIR) is uncommented and added below
set ALL_EXCLUDES=%EXCLUDE_GIT% %EXCLUDE_SCRIPT% %EXCLUDE_ZIPS% %EXCLUDE_LOVES% %EXCLUDE_BAKS% %EXCLUDE_OLD% %EXCLUDE_TXT% %EXCLUDE_GM_TOOLS%
echo Exclusions: %ALL_EXCLUDES%

:: --- Pre-Checks ---
if not exist %WINRAR_EXE% (
    echo ERROR: WinRAR.exe not found. Halting.
    goto :EndScript
)
if not exist "%TARGET_DIR%" (
    echo ERROR: Target directory '%TARGET_DIR%' not found. Halting.
    goto :EndScript
)
echo Pre-checks passed.

:: --- Deletion Step ---
echo Checking for existing file: %LOVE_FILE%
if exist "%LOVE_FILE%" (
    echo Found existing file. Attempting to delete...
    del /F /Q "%LOVE_FILE%"
    if errorlevel 1 (
        echo ERROR: Failed to delete existing file '%LOVE_FILE%'. Check permissions or if file is locked. Halting.
        goto :EndScript
    ) else (
        echo Successfully deleted existing file.
    )
) else (
    echo No existing file found. Proceeding...
)

:: --- WinRAR Archiving Step ---
echo Starting WinRAR command...
echo Zipping project contents (excluding specified files)
%WINRAR_EXE% a -afzip -ap -r %ALL_EXCLUDES% "%LOVE_FILE%" .
set WINRAR_ERRORLEVEL=%ERRORLEVEL%
echo WinRAR command finished with ErrorLevel: %WINRAR_ERRORLEVEL%

:: --- Post-Checks ---
if %WINRAR_ERRORLEVEL% NEQ 0 (
    echo ERROR: WinRAR command failed! ErrorLevel: %WINRAR_ERRORLEVEL%. Check WinRAR output messages above. Halting.
    goto :EndScript
)

echo Checking if output file was created...
if exist "%LOVE_FILE%" (
     echo SUCCESS! %LOVE_FILE% seems to have been created/updated.
     echo --- CRITICAL: Please VERIFY the internal structure NOW! ---
     echo Open %LOVE_FILE% and check if main.lua is at the ROOT level.
) else (
     echo ERROR: WinRAR reported success (ErrorLevel 0), but output file '%LOVE_FILE%' was NOT found! Something is wrong.
)

:EndScript
echo --- Build Script Finished ---
pause
endlocal