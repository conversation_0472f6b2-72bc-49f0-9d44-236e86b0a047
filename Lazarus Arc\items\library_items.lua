-- items/library_items.lua
-- Library-related items for knowledge, research, and magical texts

local LibraryItems = {
    -- Common Items
    book = {
        id = "book",
        name = "Book",
        category = "knowledge",
        description = "A standard book containing knowledge",
        value = 5,
        weight = 1.0,
        stackable = true,
        maxStack = 10,
        properties = {
            material = "paper",
            quality = 1.0,
            durability = 100,
            knowledge = "basic"
        },
        onUse = function(user, target)
            -- Provides basic knowledge
            return true
        end
    },
    
    scroll = {
        id = "scroll",
        name = "Scroll",
        category = "knowledge",
        description = "A scroll with written information",
        value = 3,
        weight = 0.5,
        stackable = true,
        maxStack = 15,
        properties = {
            material = "parchment",
            quality = 1.0,
            durability = 50,
            knowledge = "basic"
        },
        onUse = function(user, target)
            -- Provides basic knowledge
            return true
        end
    },
    
    parchment = {
        id = "parchment",
        name = "Parchment",
        category = "material",
        description = "A blank piece of parchment",
        value = 1,
        weight = 0.2,
        stackable = true,
        maxStack = 20,
        properties = {
            material = "parchment",
            quality = 1.0,
            durability = 100
        },
        onUse = function(user, target)
            -- Can be used for writing
            return true
        end
    },
    
    ink = {
        id = "ink",
        name = "Ink",
        category = "material",
        description = "A bottle of ink for writing",
        value = 2,
        weight = 0.5,
        stackable = true,
        maxStack = 15,
        properties = {
            material = "liquid",
            quality = 1.0,
            durability = 100
        },
        onUse = function(user, target)
            -- Can be used for writing
            return true
        end
    },
    
    -- Uncommon Items
    rare_book = {
        id = "rare_book",
        name = "Rare Book",
        category = "knowledge",
        description = "A valuable book containing advanced knowledge",
        value = 50,
        weight = 1.0,
        stackable = true,
        maxStack = 5,
        properties = {
            material = "paper",
            quality = 2.0,
            durability = 150,
            knowledge = "advanced"
        },
        onUse = function(user, target)
            -- Provides advanced knowledge
            return true
        end
    },
    
    magical_scroll = {
        id = "magical_scroll",
        name = "Magical Scroll",
        category = "knowledge",
        description = "A scroll containing magical knowledge",
        value = 75,
        weight = 0.5,
        stackable = true,
        maxStack = 5,
        properties = {
            material = "enchanted_parchment",
            quality = 2.0,
            durability = 100,
            knowledge = "magical",
            magical = true
        },
        onUse = function(user, target)
            -- Provides magical knowledge
            return true
        end
    },
    
    ancient_text = {
        id = "ancient_text",
        name = "Ancient Text",
        category = "knowledge",
        description = "A text from ancient times",
        value = 100,
        weight = 1.0,
        stackable = false,
        properties = {
            material = "ancient_paper",
            quality = 2.5,
            durability = 200,
            knowledge = "ancient"
        },
        onUse = function(user, target)
            -- Provides ancient knowledge
            return true
        end
    },
    
    research_notes = {
        id = "research_notes",
        name = "Research Notes",
        category = "knowledge",
        description = "Detailed notes from research",
        value = 40,
        weight = 0.5,
        stackable = true,
        maxStack = 5,
        properties = {
            material = "paper",
            quality = 2.0,
            durability = 100,
            knowledge = "research"
        },
        onUse = function(user, target)
            -- Provides research knowledge
            return true
        end
    },
    
    -- Rare Items
    legendary_tome = {
        id = "legendary_tome",
        name = "Legendary Tome",
        category = "knowledge",
        description = "A legendary book containing powerful knowledge",
        value = 500,
        weight = 2.0,
        stackable = false,
        properties = {
            material = "enchanted_paper",
            quality = 3.0,
            durability = 300,
            knowledge = "legendary",
            magical = true
        },
        onUse = function(user, target)
            -- Provides legendary knowledge
            return true
        end
    },
    
    forbidden_scroll = {
        id = "forbidden_scroll",
        name = "Forbidden Scroll",
        category = "knowledge",
        description = "A scroll containing forbidden knowledge",
        value = 300,
        weight = 0.5,
        stackable = false,
        properties = {
            material = "dark_parchment",
            quality = 2.5,
            durability = 150,
            knowledge = "forbidden",
            magical = true
        },
        onUse = function(user, target)
            -- Provides forbidden knowledge
            return true
        end
    },
    
    lost_manuscript = {
        id = "lost_manuscript",
        name = "Lost Manuscript",
        category = "knowledge",
        description = "A long-lost manuscript of great importance",
        value = 400,
        weight = 1.0,
        stackable = false,
        properties = {
            material = "ancient_paper",
            quality = 2.5,
            durability = 200,
            knowledge = "lost"
        },
        onUse = function(user, target)
            -- Provides lost knowledge
            return true
        end
    },
    
    ancient_artifact = {
        id = "ancient_artifact",
        name = "Ancient Artifact",
        category = "knowledge",
        description = "An artifact containing ancient knowledge",
        value = 600,
        weight = 2.0,
        stackable = false,
        properties = {
            material = "ancient_metal",
            quality = 3.0,
            durability = 500,
            knowledge = "ancient",
            magical = true
        },
        onUse = function(user, target)
            -- Provides ancient knowledge
            return true
        end
    }
}

return LibraryItems 