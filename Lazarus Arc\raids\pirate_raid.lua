local PirateRaid = {
    id = "pirate_raid",
    name = "Pirate Raid",
    location = {x = 0, y = 0},
    currentStage = 0,
    stages = {
        -- Stage 1: The Shore Assault
        {
            name = "The Shore Assault",
            duration = 60,
            objectives = {
                "Defend the beachhead",
                "Repel the pirate landing party",
            },
            enemies = {
                {type = "pirate_swashbuckler", count = 15}, -- New enemy
                {type = "pirate_gunner", count = 10}, -- New enemy
            },
        },
        -- Stage 2: Naval Barrage
        {
            name = "Naval Barrage",
            duration = 90,
            objectives = {
                "Destroy the pirate ships",
                "Survive the cannon fire",
            },
            enemies = {
                {type = "pirate_swashbuckler", count = 10},
                {type = "pirate_gunner", count = 10},
                {type = "pirate_captain", count = 3}, -- New enemy
                {type = "pirate_ship", count = 2}, -- New "enemy" - a ship!
            },
        },
        -- Stage 3: The Pirate Lord
        {
            name = "The Pirate Lord",
            duration = 120,
            objectives = {
                "Defeat the Pirate Lord",
                "Claim the treasure",
            },
            enemies = {
                {type = "pirate_elite", count = 10}, -- New enemy
                {type = "pirate_first_mate", count = 2}, -- New enemy
                {type = "pirate_lord", count = 1}, -- New enemy
            },
        },
    },
    timer = 0,
    isActive = false,
    -- Raid-specific data
    shipsRemaining = 2, -- Track the number of pirate ships
}

function PirateRaid.init(config)
    for k, v in pairs(config or {}) do
        PirateRaid[k] = v
    end
    PirateRaid.timer = 0
    PirateRaid.currentStage = 0
    PirateRaid.isActive = true
    PirateRaid.shipsRemaining = 2
end

function PirateRaid.update(world, dt)
    if not PirateRaid.isActive then
        return
    end

    PirateRaid.timer = PirateRaid.timer + dt
    local currentStage = PirateRaid.stages[PirateRaid.currentStage + 1]

    if currentStage then
        PirateRaid.spawnEnemies(world, currentStage.enemies)

        -- Stage progression logic
        if PirateRaid.timer >= currentStage.duration then
            PirateRaid.currentStage = PirateRaid.currentStage + 1
            PirateRaid.timer = 0
            print("Stage " .. PirateRaid.currentStage .. " complete!")
        end

        -- Stage-specific logic
        if PirateRaid.currentStage == 2 then
            PirateRaid.updateNavalBattle()
        end
        if PirateRaid.currentStage == 3 then
            PirateRaid.updatePirateLord()
        end
    else
        PirateRaid.isActive = false
        print("Pirate Raid complete!")
    end
end

function PirateRaid.spawnEnemies(world, enemies)
    for _, enemyGroup in ipairs(enemies) do
        for i = 1, enemyGroup.count do
            local entity = world.entityFactory:create(enemyGroup.type)
            if entity then
                entity.position = {
                    x = PirateRaid.location.x + math.random(-5, 5),
                    y = PirateRaid.location.y + math.random(-5, 5),
                }
                world.entitySystem:addEntity(entity)
            end
        end
    end
end

function PirateRaid.updateNavalBattle()
    -- Logic for the naval battle (e.g., ships firing, ship destruction)
    print("Cannons roar across the water!")

    -- Example: Reduce ships remaining based on player actions
    if math.random(1, 100) < 1 then -- 1% chance (adjust as needed)
        PirateRaid.shipsRemaining = math.max(0, PirateRaid.shipsRemaining - 1)
        print("A pirate ship has been destroyed! Ships remaining: " .. PirateRaid.shipsRemaining)
    end

    if PirateRaid.shipsRemaining <= 0 then
        print("The pirate fleet is defeated!")
        -- Logic for ending the naval battle stage
    end
end

function PirateRaid.updatePirateLord()
    -- Logic for the Pirate Lord's abilities and behavior
    print("The Pirate Lord unleashes a powerful attack!")
end

return PirateRaid