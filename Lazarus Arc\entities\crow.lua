-- entities/crow.lua
-- Enhanced crow with variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local Crow = {
    id = "crow",
    name = "Crow",
    type = "crow",
    shape = {
        {0, -1}, {1, 0}, {0, 1}, {-1, 0}
    },
    size = 5,
    
    -- Entity categories for relationship handling
    categories = {"animal", "bird", "omnivore"},
    
    -- Threat and food categories
    threatCategories = {"predator", "monster", "large"},
    foodCategories = {"plant", "small", "carrion", "seeds"},
    
    -- Stats
    maxHealth = 12,
    health = 12,
    maxStamina = 25,
    stamina = 25,
    speed = 1.8,
    canFly = true,
    
    -- Behaviors
    behaviors = {"wander", "flee", "graze"},
    
    -- Behavior configurations
    behaviorConfigs = {
        wander = {
            moveSpeed = 1.5,
            changeDirectionChance = 0.08,
            idleChance = 0.3,
            idleDuration = {2, 6},
            wanderRadius = 20,
            flightChance = 0.3 -- Chance to take flight when wandering
        },
        flee = {
            useCategories = true,
            moveSpeed = 2.5,
            detectionRadius = 8,
            useHiding = false, -- Flies instead of hiding
            groupFleeing = true
        },
        graze = {
            foodTypes = {"seeds", "crops", "carrion", "worms"},
            foodValue = {
                seeds = 4,
                crops = 6,
                carrion = 10,
                worms = 5
            },
            fleeWhenThreatened = true,
            grazeTime = {3, 10},
            moveTime = {4, 8},
            grazeRadius = 15
        }
    },
    
    -- Special abilities
    abilities = {
        flight = {
            maxAltitude = 5,
            takeoffSpeed = 2,
            landingSpeed = 1,
            airSpeed = 3,
            energyCost = 0.2 -- Stamina per second
        },
        mimicry = {
            canMimic = true,
            soundsLearned = {}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "crow",
        scale = 0.9,
        animations = {
            "idle", "walk", "fly", "eat", "alert", "call"
        }
    },
    
    -- Sound effects with synth configuration
    sounds = {
        call = {
            file = "crow_caw",
            synth = {
                instrument = "electric_guitar",
                notes = {"E3", "C3"},
                durations = {0.4, 0.6},
                volume = 0.45,
                vibrato = true,
                vibratoRate = 3.0
            }
        },
        flap = {
            file = "wing_flap",
            synth = {
                instrument = "marimba",
                notes = {"F3", "G3"},
                durations = {0.1, 0.1},
                volume = 0.25
            }
        },
        alert = {
            file = "crow_alert",
            synth = {
                instrument = "electric_guitar",
                notes = {"G3", "E3", "C3"},
                durations = {0.2, 0.2, 0.3},
                volume = 0.5
            }
        },
        footstep = {
            synth = {
                instrument = "xylophone",
                notes = {"D3"},
                duration = 0.08,
                volume = 0.15
            }
        },
        fly = {
            synth = {
                instrument = "accordion",
                notes = {"F3", "A3"},
                durations = {0.2, 0.2},
                volume = 0.3
            }
        },
        hurt = {
            synth = {
                instrument = "electric_guitar",
                notes = {"D#3"},
                duration = 0.3,
                volume = 0.5,
                vibrato = true,
                vibratoRate = 8.0
            }
        },
        death = {
            synth = {
                instrument = "bass_guitar",
                notes = {"C3", "A2", "F2"},
                durations = {0.4, 0.4, 1.0},
                volume = 0.5,
                vibrato = true,
                vibratoRate = 2.0
            }
        },
        eat = {
            synth = {
                instrument = "xylophone",
                notes = {"C4", "E4"},
                durations = {0.1, 0.1},
                volume = 0.2
            }
        }
    },
    
    -- === ENHANCED VARIANT SYSTEM ===
    variantChances = {
        normal = 0.80,          -- 80% common crow
        shiny = 0.15,           -- 15% raven (shiny)
        rare = 0.04,            -- 4% wise crow (rare)
        legendary = 0.01        -- 1% odin's raven (legendary)
    },

    variants = {
        normal = {
            name = "Common Crow",
            description = "A typical black crow with keen intelligence",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.2, 0.2, 0.3, 1.0}  -- Black feathers
            }
        },

        shiny = {
            name = "Raven",
            description = "A larger, more intelligent corvid with iridescent feathers",
            statModifiers = {
                maxHealth = 1.5,    -- 18 health instead of 12
                speed = 1.2,        -- 2.16 speed instead of 1.8
                maxStamina = 1.4,   -- 35 stamina instead of 25
                intelligence = 1.8  -- Enhanced intelligence
            },
            appearanceModifiers = {
                scale = 1.2,
                glow = true,
                colorTint = {0.1, 0.1, 0.4, 1.0},  -- Iridescent black-blue
                larger_beak = true,
                intelligent_eyes = true
            },
            soundModifiers = {
                pitch = 0.8,
                volume = 1.3,
                deeper_caw = true
            }
        },

        rare = {
            name = "Wise Crow",
            description = "An ancient crow with supernatural wisdom and memory",
            statModifiers = {
                maxHealth = 2.0,    -- 24 health
                speed = 1.3,        -- 2.34 speed
                maxStamina = 1.8,   -- 45 stamina
                intelligence = 3.0,
                memory_capacity = 5.0
            },
            appearanceModifiers = {
                scale = 1.15,
                colorTint = {0.3, 0.3, 0.4, 1.0},  -- Gray-touched black
                wise_eyes = true,
                ancient_markings = true,
                mystical_aura = true
            },
            soundModifiers = {
                pitch = 0.9,
                volume = 1.2,
                reverb = true,
                wise_tone = true
            }
        },

        legendary = {
            name = "Odin's Raven",
            description = "A mythical raven with divine knowledge and power",
            statModifiers = {
                maxHealth = 3.0,    -- 36 health
                speed = 1.8,        -- 3.24 speed
                maxStamina = 2.5,   -- 62.5 stamina
                intelligence = 10.0,
                divine_knowledge = 5.0,
                reality_sight = 1.0
            },
            appearanceModifiers = {
                scale = 1.4,
                glow = true,
                colorTint = {0.4, 0.4, 0.8, 1.0},  -- Mystical blue-black
                divine_aura = "legendary",
                runic_markings = true,
                all_seeing_eyes = true,
                ethereal_presence = true
            },
            soundModifiers = {
                pitch = 0.7,
                volume = 1.6,
                reverb = true,
                echo = true,
                divine_resonance = true
            }
        }
    },

    -- === ENHANCED DROP SYSTEM ===

    -- Base drops (available from all variants)
    baseDrops = {
        {id = "crow_feather", chance = 0.9, quantity = {1, 3}},
        {id = "small_bone", chance = 0.4, quantity = {1, 1}}
    },

    -- Variant-specific drops
    variantDrops = {
        normal = {
            {id = "shiny_object", chance = 0.2, quantity = {1, 1}}
        },
        shiny = {
            {id = "raven_feather", chance = 0.9, quantity = {1, 2}},
            {id = "iridescent_plume", chance = 0.7, quantity = {1, 1}},
            {id = "intelligence_essence", chance = 0.5, quantity = {1, 1}},
            {id = "corvid_wisdom", chance = 0.3, quantity = {1, 1}}
        },
        rare = {
            {id = "wise_crow_feather", chance = 0.9, quantity = {1, 1}},
            {id = "memory_crystal", chance = 0.8, quantity = {1, 1}},
            {id = "ancient_knowledge", chance = 0.6, quantity = {1, 1}},
            {id = "wisdom_essence", chance = 0.7, quantity = {1, 1}},
            {id = "prophetic_vision", chance = 0.4, quantity = {1, 1}}
        },
        legendary = {
            {id = "odins_raven_feather", chance = 0.95, quantity = {1, 1}},
            {id = "divine_knowledge_orb", chance = 0.9, quantity = {1, 1}},
            {id = "all_seeing_eye", chance = 0.8, quantity = {1, 1}},
            {id = "runic_memory_stone", chance = 0.7, quantity = {1, 1}},
            {id = "divine_messenger_essence", chance = 0.6, quantity = {1, 1}},
            {id = "reality_sight_gem", chance = 0.5, quantity = {1, 1}}
        }
    },
    
    -- Intelligence and memory
    intelligence = {
        memoryCapacity = 5, -- Remember 5 locations/events
        memorizedLocations = {}, -- Important locations
        memoryDuration = 300 -- How long memories last
    }
}

function Crow.init(entity, worldCore)
    -- First, handle case where init is called during module registration
    -- by world_core.lua line 77: module.init(module, WorldCore)
    if entity == Crow and worldCore then
        -- This is a module registration call, not an entity initialization call
        -- Can store WorldCore reference if needed
        Crow.worldCore = worldCore
        return Crow
    end

    -- Copy all fields from Crow template to entity instance
    for k, v in pairs(Crow) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, worldCore)
end

return Crow
