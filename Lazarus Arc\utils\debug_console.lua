-- utils/debug_console.lua
-- Clean debug console for development information

local DebugConsole = {}

-- Initialize debug console
function DebugConsole.init()
    DebugConsole.enabled = false
    DebugConsole.lastUpdate = 0
    DebugConsole.updateInterval = 0.1 -- Update 10 times per second
    
    -- Console display settings
    DebugConsole.x = 10
    DebugConsole.y = love.graphics.getHeight() - 200
    DebugConsole.width = 400
    DebugConsole.height = 180
    DebugConsole.backgroundColor = {0, 0, 0, 0.8}
    DebugConsole.borderColor = {0.3, 0.3, 0.3, 1}
    DebugConsole.textColor = {1, 1, 1, 1}
    DebugConsole.headerColor = {0.8, 0.8, 0.8, 1}
    
    -- Debug data storage
    DebugConsole.data = {
        playerPosition = {x = 0, y = 0},
        playerMovement = {x = 0, y = 0},
        playerRegistered = false,
        worldName = "None",
        entityCount = 0,
        chunkCount = 0,
        fps = 0,
        frameTime = 0,
        memoryUsage = 0
    }
    
    -- Reduce terminal spam by tracking what we've already printed
    DebugConsole.lastPrintedData = {}
    DebugConsole.printThrottle = {}
end

-- Update debug data
function DebugConsole.update(dt)
    if not DebugConsole.enabled then return end
    
    DebugConsole.lastUpdate = DebugConsole.lastUpdate + dt
    
    if DebugConsole.lastUpdate >= DebugConsole.updateInterval then
        DebugConsole.lastUpdate = 0
        DebugConsole.collectDebugData()
    end
end

-- Collect current debug data
function DebugConsole.collectDebugData()
    -- Check if Engine is available
    local Engine = _G.Engine
    if not Engine then
        -- Engine not loaded yet, use default values
        DebugConsole.data.playerRegistered = false
        DebugConsole.data.worldName = "Engine Loading..."
        DebugConsole.data.entityCount = 0
        DebugConsole.data.chunkCount = 0
        DebugConsole.data.fps = love.timer.getFPS()
        DebugConsole.data.frameTime = love.timer.getDelta() * 1000
        DebugConsole.data.memoryUsage = collectgarbage("count") / 1024
        return
    end

    -- Player data
    if Engine.player and Engine.player.position then
        DebugConsole.data.playerPosition.x = Engine.player.position.x
        DebugConsole.data.playerPosition.y = Engine.player.position.y
        DebugConsole.data.playerRegistered = true
    else
        DebugConsole.data.playerRegistered = false
    end

    -- World data
    if Engine.currentWorld then
        DebugConsole.data.worldName = Engine.currentWorld.name or "Unknown"

        -- Entity count
        if Engine.currentWorld.entitySystem and Engine.currentWorld.entitySystem.entities then
            DebugConsole.data.entityCount = #Engine.currentWorld.entitySystem.entities
        end

        -- Chunk count
        if Engine.currentWorld.chunkSystem and Engine.currentWorld.chunkSystem.loadedChunks then
            DebugConsole.data.chunkCount = 0
            for _ in pairs(Engine.currentWorld.chunkSystem.loadedChunks) do
                DebugConsole.data.chunkCount = DebugConsole.data.chunkCount + 1
            end
        end
    else
        DebugConsole.data.worldName = "No World"
        DebugConsole.data.entityCount = 0
        DebugConsole.data.chunkCount = 0
    end

    -- Performance data
    DebugConsole.data.fps = love.timer.getFPS()
    DebugConsole.data.frameTime = love.timer.getDelta() * 1000 -- Convert to ms
    DebugConsole.data.memoryUsage = collectgarbage("count") / 1024 -- Convert to MB
end

-- Draw debug console
function DebugConsole.draw()
    if not DebugConsole.enabled then return end
    
    love.graphics.push()
    
    -- Draw background
    love.graphics.setColor(DebugConsole.backgroundColor)
    love.graphics.rectangle("fill", DebugConsole.x, DebugConsole.y, DebugConsole.width, DebugConsole.height)
    
    -- Draw border
    love.graphics.setColor(DebugConsole.borderColor)
    love.graphics.setLineWidth(1)
    love.graphics.rectangle("line", DebugConsole.x, DebugConsole.y, DebugConsole.width, DebugConsole.height)
    
    -- Draw header
    love.graphics.setColor(DebugConsole.headerColor)
    love.graphics.print("Debug Console", DebugConsole.x + 10, DebugConsole.y + 10)
    
    -- Draw debug information
    local lineHeight = 14
    local startY = DebugConsole.y + 30
    local line = 0
    
    love.graphics.setColor(DebugConsole.textColor)
    
    -- Player information
    love.graphics.print("Player:", DebugConsole.x + 10, startY + line * lineHeight)
    line = line + 1
    
    if DebugConsole.data.playerRegistered then
        love.graphics.print(string.format("  Position: %.2f, %.2f", 
            DebugConsole.data.playerPosition.x, DebugConsole.data.playerPosition.y), 
            DebugConsole.x + 10, startY + line * lineHeight)
        line = line + 1
        
        love.graphics.print("  Status: Registered", DebugConsole.x + 10, startY + line * lineHeight)
    else
        love.graphics.print("  Status: Not Registered", DebugConsole.x + 10, startY + line * lineHeight)
    end
    line = line + 1
    
    -- World information
    love.graphics.print("World:", DebugConsole.x + 10, startY + line * lineHeight)
    line = line + 1
    
    love.graphics.print("  Name: " .. DebugConsole.data.worldName, DebugConsole.x + 10, startY + line * lineHeight)
    line = line + 1
    
    love.graphics.print(string.format("  Entities: %d", DebugConsole.data.entityCount), 
        DebugConsole.x + 10, startY + line * lineHeight)
    line = line + 1
    
    love.graphics.print(string.format("  Chunks: %d", DebugConsole.data.chunkCount), 
        DebugConsole.x + 10, startY + line * lineHeight)
    line = line + 1
    
    -- Performance information
    love.graphics.print("Performance:", DebugConsole.x + 10, startY + line * lineHeight)
    line = line + 1
    
    love.graphics.print(string.format("  FPS: %d", DebugConsole.data.fps), 
        DebugConsole.x + 10, startY + line * lineHeight)
    line = line + 1
    
    love.graphics.print(string.format("  Frame: %.2fms", DebugConsole.data.frameTime), 
        DebugConsole.x + 10, startY + line * lineHeight)
    line = line + 1
    
    love.graphics.print(string.format("  Memory: %.1fMB", DebugConsole.data.memoryUsage), 
        DebugConsole.x + 10, startY + line * lineHeight)
    
    love.graphics.pop()
end

-- Toggle debug console
function DebugConsole.toggle()
    DebugConsole.enabled = not DebugConsole.enabled
    
    if DebugConsole.enabled then
        print("Debug Console enabled - reducing terminal spam")
    else
        print("Debug Console disabled")
    end
    
    return DebugConsole.enabled
end

-- Throttled print function to reduce spam
function DebugConsole.throttledPrint(key, message, interval)
    interval = interval or 1.0 -- Default to 1 second throttle
    
    local currentTime = love.timer.getTime()
    
    if not DebugConsole.printThrottle[key] or 
       (currentTime - DebugConsole.printThrottle[key]) >= interval then
        
        -- Only print if the message has changed
        if DebugConsole.lastPrintedData[key] ~= message then
            print(message)
            DebugConsole.lastPrintedData[key] = message
        end
        
        DebugConsole.printThrottle[key] = currentTime
    end
end

-- Log player position (throttled)
function DebugConsole.logPlayerPosition(x, y, context)
    -- Always allow logging regardless of enabled state for critical debug info
    local message = string.format("Player %s: x=%.2f, y=%.2f", context or "Position", x or 0, y or 0)
    DebugConsole.throttledPrint("player_position", message, 0.5)
end

-- Log player registration (throttled)
function DebugConsole.logPlayerRegistration(success, playerId)
    -- Always allow logging regardless of enabled state for critical debug info
    local message = string.format("Player Registration: %s (ID: %s)",
        success and "SUCCESS" or "FAILED", playerId or "unknown")
    DebugConsole.throttledPrint("player_registration", message, 2.0)
end

-- Log movement input (throttled)
function DebugConsole.logMovementInput(moveX, moveY)
    -- Always allow logging regardless of enabled state for critical debug info
    -- Only log if there's actual movement
    if (moveX and moveX ~= 0) or (moveY and moveY ~= 0) then
        local message = string.format("Movement Input: x=%.2f, y=%.2f", moveX or 0, moveY or 0)
        DebugConsole.throttledPrint("movement_input", message, 0.2)
    end
end

-- Set position
function DebugConsole.setPosition(x, y)
    DebugConsole.x = x
    DebugConsole.y = y
end

-- Set size
function DebugConsole.setSize(width, height)
    DebugConsole.width = width
    DebugConsole.height = height
end

-- Clear all throttled data (useful for testing)
function DebugConsole.clearThrottle()
    DebugConsole.printThrottle = {}
    DebugConsole.lastPrintedData = {}
end

return DebugConsole
