# Creature Sound System

The Lazarus Arc creature sound system provides a flexible, user-modifiable approach to creature audio using JSON configuration files and a powerful synthesizer orchestra.

## Architecture

The system is designed with clean separation of concerns:

- **Synth Orchestra** (`utils/synth_orchestra.lua`) - Pure sound generation utility with 15+ instruments
- **Sound Config** (`utils/sound_config.lua`) - Loads and parses JSON configurations and musical notation
- **Entity Files** (`entities/*.lua`) - Define default sound configurations for each creature
- **Sound System** (`utils/sound_system.lua`) - Orchestrates between components
- **User Overrides** (`sounds/creatures/*.json`) - User-customizable JSON files

## User Customization

Users can customize creature sounds by creating JSON files in the `sounds/creatures/` directory.

### Example: `sounds/creatures/rabbit.json`

```json
{
  "entityType": "rabbit",
  "sounds": {
    "hop": {
      "instrument": "xylophone",
      "notes": ["C4", "E4"],
      "durations": [0.1, 0.1],
      "volume": 0.25
    },
    "squeak": {
      "instrument": "kalimba",
      "notes": ["A4", "C5", "E5"],
      "durations": [0.15, 0.15, 0.2],
      "volume": 0.35,
      "vibrato": true,
      "vibratoRate": 7.0
    }
  }
}
```

## Sound Configuration Format

### Basic Properties
- `instrument` - Name of synthesizer instrument (see Available Instruments below)
- `notes` - Array of note names (e.g., "C4", "F#3", "Bb5") or single note string
- `duration` - Duration in seconds for single note
- `durations` - Array of durations for multiple notes
- `volume` - Volume level (0.0 to 1.0)

### Effects
- `vibrato` - Enable vibrato effect (true/false)
- `vibratoRate` - Vibrato speed in Hz (default: 5.0)
- `vibratoDepth` - Vibrato intensity (default: 0.1)

### Note Names
Standard musical notation is supported:
- **Notes**: C, D, E, F, G, A, B
- **Sharps**: C#, D#, F#, G#, A#
- **Flats**: Db, Eb, Gb, Ab, Bb (converted to sharps internally)
- **Octaves**: 0-8 (C4 = middle C, A4 = 440Hz)

Examples: `C4`, `F#3`, `Bb5`, `A4`

## Available Instruments

### Piano Family
- `grand_piano` - Rich, resonant concert piano
- `upright_piano` - Classic, warm home piano  
- `electric_piano` - Vintage electric piano (Rhodes style)

### Guitar Family
- `acoustic_guitar` - Warm, natural guitar
- `electric_guitar` - Bright, sustained electric guitar
- `bass_guitar` - Deep, punchy bass guitar
- `classical_guitar` - Nylon string, gentle

### Orchestral
- `trumpet` - Bright, brassy, heroic tones
- `saxophone` - Smooth, jazzy reed instrument
- `cello` - Rich, warm string instrument
- `clarinet` - Smooth woodwind

### Percussion & Mallet
- `xylophone` - Bright, percussive wooden mallet sounds
- `vibraphone` - Metallic, shimmering mallet instrument
- `marimba` - Warm, woody percussion
- `kalimba` - Thumb piano, metallic plucked tines

### Folk & World
- `harmonica` - Reedy, expressive mouth organ
- `banjo` - Twangy, plucked string instrument
- `accordion` - Reedy, folk instrument with vibrato
- `ocarina` - Ceramic wind instrument, mystical
- `didgeridoo` - Deep, droning wind instrument

## Priority System

The sound system checks for sounds in this order:

1. **User JSON Override** - `sounds/creatures/{entity}.json`
2. **Entity Definition** - Synth config in entity file
3. **Audio File Fallback** - Traditional audio files

This ensures backward compatibility while allowing full customization.

## For Developers

### Adding New Creatures

In your entity file (`entities/newcreature.lua`):

```lua
sounds = {
    call = {
        file = "newcreature_call", -- fallback audio file
        synth = {
            instrument = "trumpet",
            notes = ["G4", "C5"],
            durations = [0.3, 0.5],
            volume = 0.4,
            vibrato = true
        }
    }
}
```

### Adding New Instruments

Add to `SynthOrchestra.instruments` in `utils/synth_orchestra.lua`:

```lua
new_instrument = {
    attack = 0.1, decay = 0.2, sustain = 0.7, release = 0.3,
    harmonics = {{1.0, 1.0}, {2.0, 0.5}, {3.0, 0.25}},
    waveform = "sine"
}
```

## Benefits

- **User-Friendly**: JSON format is widely understood
- **Standard Notation**: Uses familiar musical note names
- **Modular**: Clean separation between sound generation and game logic
- **Extensible**: Easy to add new instruments and effects
- **Backward Compatible**: Existing audio files continue to work
- **No Code Required**: Users can customize sounds without touching Lua code

This system gives users the power of a "standard format" (JSON + musical notation) while maintaining the flexibility and power of the synthesizer orchestra.
