-- classes/fighter.lua
-- Fighter class definition for Lazarus Arc

local Fighter = {
    name = "Fighter",
    description = "A master of physical combat, capable of wielding a wide variety of weapons and adapting to any battle situation.",
    icon = "fighter_icon.png",

    -- 🎭 **Playstyle & Trade-offs**
    pros = {
        "Can use nearly any weapon effectively",
        "Balanced offense and defense",
        "Adaptable to different combat styles"
    },
    cons = {
        "Lacks magical abilities",
        "Doesn’t excel in any one specific area",
        "Requires strategic play rather than brute force"
    },

    -- 🏋 **Base Stats & Growth**
    base_stats = { 
        health = 120, 
        maxHealth = 120,
        stamina = 80, 
        maxStamina = 80,
        mana = 20, 
        maxMana = 20,
        pattack = 14, 
        pdefense = 8, 
        mattack = 4, 
        mdefense = 5, 
        speed = 6, 
        luck = 5 
    },

    stat_growth = {
        health = 10,
        stamina = 7,
        mana = 1,
        pattack = 1.3,
        pdefense = 0.9,
        mattack = 0.2,
        mdefense = 0.5,
        speed = 0.6,
        luck = 0.4
    },

    -- ⚔️ **Weapon Proficiency**
    proficiencies = { 
        sword = 2.0,  -- Most fighters rely on swords
        dagger = 1.5,
        greatsword = 1.8,  
        axe = 1.8,
        mace = 1.6,
        spear = 1.9,
        bow = 1.2,  
        crossbow = 1.3, 
        gun = 1.0,
        magic = 0.3  -- Very weak in magic
    },

    -- 🌿 **Elemental Affinities**
    element_affinities = {
        earth = 10,  -- Stability, resilience
        air = 5,     -- Quick footwork
        fire = -5    -- Not particularly attuned to aggressive fire-based combat
    },

    -- 💥 **Overdrive Abilities**
    overdrive = { 
        "blade_dance", -- Rapid slashes that strike multiple enemies
        "indomitable_will", -- Removes all debuffs and restores stamina
        "weapon_mastery", -- Temporarily increases damage based on weapon type
    },

    -- 🎯 **Starting Skills**
    starting_skills = {
        "precision_strike",
        "deflect",
        "stamina_surge"
    },

    -- 📈 **Skill Progression**
    skill_tree = {
        tier1 = {
            "flurry",
            "counter_attack",
            "iron_stance"
        },
        tier2 = {
            "weapon_specialist",
            "battlefield_tactics", 
            "unbreakable_guard",
            "disarm"
        },
        tier3 = {
            "relentless_assault",
            "last_resort",
            "perfect_parry"
        }
    },

    -- 🎒 **Inventory & Carrying Capacity**
    inventory_limit = 120, -- Higher than most classes

    -- ✨ **Special Abilities (Passive Perks)**
    special_abilities = {
        "Adaptive Combatant", -- Gains a small bonus based on the weapon equipped
        "Second Wind", -- Can recover a portion of stamina after defeating an enemy
        "Combat Readiness", -- Starts battle with increased defense for a few turns
        "Tactical Strike", -- Attacks deal extra damage when hitting a weak point
        "Battle Hardened", -- Reduced effect of enemy debuffs
    },

    -- 🎭 **Prestige Paths**
    prestige_paths = {
        weapon_master = {
            description = "A highly trained fighter capable of mastering multiple weapon types effortlessly.",
            bonuses = {
                "Can swap weapons instantly during combat",
                "Passive damage increase with all weapon types",
                "Access to unique combo attacks"
            },
            weaknesses = {
                "Requires knowledge of multiple weapons",
                "Not as durable as a tank"
            }
        },

        gladiator = {
            description = "A fighter who excels in one-on-one combat, using showmanship and skill to outmaneuver opponents.",
            bonuses = {
                "Higher critical hit chance",
                "Can dodge melee attacks more effectively",
                "Special finishing moves against weakened enemies"
            },
            weaknesses = {
                "Struggles in prolonged fights against multiple enemies",
                "Less effective in team battles"
            }
        },

        warlord = {
            description = "A battlefield commander who specializes in leading allies and overpowering enemies through tactics.",
            bonuses = {
                "Boosts stats of nearby allies",
                "Can command NPCs or party members for strategic actions",
                "Stronger in large-scale combat"
            },
            weaknesses = {
                "Weaker when fighting alone",
                "Requires careful strategy instead of brute force"
            }
        }
    },

    -- ⚔️ **Hybrid & Multiclass Options**
    hybrid_options = {
        battle_mage = {
            description = "A Fighter who incorporates magic into their combat techniques.",
            pros = {
                "Can use magic-infused weapon strikes",
                "Some resistance to magic damage"
            },
            cons = {
                "Limited mana pool",
                "Weaker against pure magic users"
            }
        },

        dread_knight = {
            description = "A fusion of raw martial power and dark abilities, using fear and intimidation to dominate the battlefield.",
            pros = {
                "Can sap enemy stamina and morale",
                "Heavy armor with speed boosts"
            },
            cons = {
                "Less resistance to light-based magic",
                "Reliant on maintaining aggression"
            }
        }
    }
}

return Fighter
