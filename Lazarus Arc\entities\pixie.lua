-- entities/pixie.lua
-- Pixie with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local Pixie = {
    id = "pixie",
    name = "<PERSON>xie",
    type = "pixie",
    shape = {
        {0, -0.3}, {0.2, -0.2}, {0.3, 0}, {0.2, 0.2},
        {0, 0.3}, {-0.2, 0.2}, {-0.3, 0}, {-0.2, -0.2}
    },
    size = 1.5,  -- Even smaller than fairies

    -- Entity categories
    categories = {"fae", "magical", "flying", "mischief", "tiny"},
    threatCategories = {"spider", "bird", "cat"},
    
    -- Base stats (tiny but mischievous)
    maxHealth = 8,
    health = 8,
    maxStamina = 50,
    stamina = 50,
    maxMana = 40,
    mana = 40,
    speed = 5.0,  -- Extremely fast
    attack = 3,   -- Very low physical attack
    defense = 1,  -- Minimal defense
    magicAttack = 15,
    magicDefense = 15,
    flight_altitude = 20,
    mischief_power = 25,
    illusion_mastery = 20,
    
    -- Behaviors
    behaviors = {"erratic_flight", "prank_magic", "swarm_tactics", "hide_and_seek"},
    behaviorConfigs = {
        erratic_flight = {
            flightSpeed = 5.5,
            randomMovement = 2.0,
            unpredictable = true,
            zigzagPattern = true
        },
        prank_magic = {
            prankFrequency = 0.6,
            illusionSpells = 2.0,
            confusionMagic = 1.8,
            harmlessTricks = true
        },
        swarm_tactics = {
            swarmRadius = 15,
            groupBonus = 2.0,
            coordinatedPranks = true,
            overwhelmingNumbers = 1.5
        },
        hide_and_seek = {
            hideChance = 0.8,
            invisibilityDuration = 8,
            surpriseAttack = 1.5,
            escapeArtist = 2.0
        }
    },
    
    -- Enhanced variant system for pixies
    variantChances = {
        normal = 0.75,          -- 75% forest pixie
        shiny = 0.18,           -- 18% trickster pixie (shiny)
        rare = 0.06,            -- 6% shadow pixie (rare)
        legendary = 0.01        -- 1% pixie lord (legendary)
    },
    
    variants = {
        normal = {
            name = "Forest Pixie",
            description = "A tiny mischievous sprite that loves playing pranks",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.8, 1.2, 0.9, 1.0}  -- Light green
            }
        },
        
        shiny = {
            name = "Trickster Pixie",
            description = "A master of illusions and elaborate pranks",
            statModifiers = {
                maxHealth = 1.5,    -- 12 health
                maxMana = 1.8,      -- 72 mana
                speed = 1.4,        -- 7.0 speed
                mischief_power = 2.0,
                illusion_mastery = 2.5,
                prank_mastery = 3.0
            },
            appearanceModifiers = {
                scale = 1.2,
                glow = true,
                colorTint = {1.2, 1.0, 1.4, 1.0},  -- Shimmering purple-green
                trickster_aura = true,
                illusion_shimmer = true,
                mischief_sparkles = true
            },
            soundModifiers = {
                pitch = 1.4,
                volume = 1.2,
                playful = true
            }
        },
        
        rare = {
            name = "Shadow Pixie",
            description = "A dark pixie that dwells in shadows and plays sinister tricks",
            statModifiers = {
                maxHealth = 1.8,    -- 14.4 health
                maxMana = 2.2,      -- 88 mana
                speed = 1.6,        -- 8.0 speed
                shadow_magic = 3.0,
                stealth_mastery = 4.0,
                dark_pranks = 2.5
            },
            appearanceModifiers = {
                scale = 1.1,
                colorTint = {0.4, 0.3, 0.6, 1.0},  -- Dark purple-black
                shadow_form = true,
                darkness_trail = true,
                sinister_glow = true,
                shadow_wings = true
            },
            soundModifiers = {
                pitch = 1.1,
                volume = 1.1,
                reverb = true,
                ominous = true
            }
        },
        
        legendary = {
            name = "Pixie Lord",
            description = "An ancient pixie ruler with reality-bending mischief powers",
            statModifiers = {
                maxHealth = 3.0,    -- 24 health
                maxMana = 3.5,      -- 140 mana
                speed = 2.0,        -- 10.0 speed
                mischief_power = 5.0,
                reality_pranks = 10.0,
                pixie_dominion = 5.0,
                chaos_mastery = 3.0
            },
            appearanceModifiers = {
                scale = 1.5,
                glow = true,
                colorTint = {1.6, 1.4, 1.8, 1.0},  -- Chaotic rainbow shimmer
                pixie_crown = true,
                chaos_aura = "legendary",
                reality_distortion = true,
                mischief_mastery = true
            },
            soundModifiers = {
                pitch = 1.2,
                volume = 1.5,
                reverb = true,
                echo = true,
                chaotic_chimes = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "pixie_dust", chance = 0.8, quantity = {1, 2}},
        {id = "tiny_wing_fragment", chance = 0.6, quantity = {1, 1}},
        {id = "mischief_essence", chance = 0.7, quantity = {1, 2}},
        {id = "illusion_crystal", chance = 0.4, quantity = {1, 1}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "trickster_dust", chance = 0.9, quantity = {1, 3}},
            {id = "illusion_mastery_core", chance = 0.8, quantity = {1, 1}},
            {id = "prank_essence", chance = 0.7, quantity = {1, 2}},
            {id = "shimmer_wing", chance = 0.6, quantity = {1, 1}}
        },
        rare = {
            {id = "shadow_pixie_dust", chance = 0.9, quantity = {1, 2}},
            {id = "dark_mischief_core", chance = 0.8, quantity = {1, 1}},
            {id = "shadow_wing_fragment", chance = 0.7, quantity = {1, 1}},
            {id = "sinister_essence", chance = 0.6, quantity = {1, 1}}
        },
        legendary = {
            {id = "pixie_lord_crown_shard", chance = 0.95, quantity = {1, 1}},
            {id = "chaos_mastery_essence", chance = 0.9, quantity = {1, 2}},
            {id = "reality_prank_crystal", chance = 0.8, quantity = {1, 1}},
            {id = "pixie_dominion_gem", chance = 0.7, quantity = {1, 1}},
            {id = "mischief_lordship_orb", chance = 0.6, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "pixie",
        scale = 0.3,
        animations = {
            "dart", "prank", "vanish", "swarm", "chaos_dance"
        },
        variants = {
            "forest_pixie", "trickster_pixie", "shadow_pixie", "pixie_lord"
        }
    },
    
    -- Sound effects with mischievous characteristics
    sounds = {
        pixie_giggle = {
            file = "pixie_giggle",
            synth = {
                instrument = "kalimba",
                notes = {"G5", "A5", "C6", "E6"},
                durations = {0.1, 0.1, 0.15, 0.2},
                volume = 0.4,
                mischievous = true
            }
        },
        wing_buzz = {
            file = "wing_buzz",
            synth = {
                instrument = "synthesizer",
                notes = {"A4", "C5"},
                durations = {0.05, 0.1},
                volume = 0.15,
                buzzing = true
            }
        },
        prank_chime = {
            file = "prank_chime",
            synth = {
                instrument = "xylophone",
                notes = {"C5", "E5", "G5"},
                durations = {0.1, 0.1, 0.2},
                volume = 0.3,
                playful = true
            }
        },
        chaos_tinkle = {
            file = "chaos_tinkle",
            synth = {
                instrument = "celesta",
                notes = {"F5", "A5", "C6", "F6"},
                durations = {0.15, 0.1, 0.15, 0.25},
                volume = 0.35,
                chaotic = true
            }
        }
    },
    
    -- Special pixie abilities
    abilities = {
        erratic_flight = {
            type = "passive",
            description = "Unpredictable movement makes pixie hard to target",
            effect = "evasion_mastery"
        },
        invisibility = {
            type = "active",
            description = "Becomes invisible for short periods",
            effect = "temporary_invisibility",
            manaCost = 15,
            duration = 5,
            cooldown = 12
        },
        confusion_prank = {
            type = "active",
            description = "Confuses enemies with illusion magic",
            effect = "confusion_spell",
            manaCost = 10,
            cooldown = 8
        },
        swarm_coordination = {
            type = "passive",
            description = "Gains bonuses when near other pixies",
            effect = "swarm_bonus"
        },
        mischief_magic = {
            type = "active",
            description = "Casts random harmless but annoying spells",
            effect = "random_prank",
            manaCost = 8,
            cooldown = 6
        },
        escape_artist = {
            type = "passive",
            description = "Extremely difficult to catch or pin down",
            effect = "escape_mastery"
        }
    }
}

-- Initialize the pixie entity using enhanced template
function Pixie.init(entity, world)
    -- Copy all fields from Pixie template to entity instance
    for k, v in pairs(Pixie) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return Pixie
