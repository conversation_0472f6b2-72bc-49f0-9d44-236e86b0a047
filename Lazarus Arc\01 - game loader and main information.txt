-- game-loader.lua (Pseudocode)

game engien is love2d

1. Load essential core systems (WorldCore, etc.)

2. Initialize basic Game state container 
   - Minimal settings
   - World reference
   - Runtime state tracking

3. Define initialization function
   - Set up randomization
   - Register event hooks for NFC connection/disconnection
   - Initialize core systems WITHOUT hardcoding content
   - Create world hub [generate random seed, then when a player joins load their hub seed from their card, transitioning from the random one]
   - Set up content loading functions that read from directories

4. Define dynamic content loading system
   - Function to scan directories for content
   - Load content based on file structure
   - Register content with appropriate systems

5. Define player management
   - Handle NFC card connection/disconnection
   - Add/remove players from the world [local cached players for now generate from character.lua]
   - Manage player viewports [viewport_management.lua]

6. Define world interaction
   - controller.lua
   - Process player input
   - Handle world updates
   - Manage game state (running, paused)


7. Define minimal UI for settings and player status
   - Keep UI separate from game logic
   - Support in-game settings access

8. Define clean shutdown process

9. Export Game object