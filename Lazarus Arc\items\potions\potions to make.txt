Potions = {
    -- Healing Potions

    minor_mana_potion = {name = "Minor Mana Potion", category = "healing", effect = "restore_mp", amount = 25},
    mana_potion = {name = "Mana Potion", category = "healing", effect = "restore_mp", amount = 50},
    greater_mana_potion = {name = "Greater Mana Potion", category = "healing", effect = "restore_mp", amount = 100},
    superior_mana_potion = {name = "Superior Mana Potion", category = "healing", effect = "restore_mp", amount = 250},
    ether_flask = {name = "Ether Flask", category = "healing", effect = "restore_mp", amount = 999},
    minor_stamina_potion = {name = "Minor Stamina Potion", category = "healing", effect = "restore_stamina", amount = 25},
    stamina_potion = {name = "Stamina Potion", category = "healing", effect = "restore_stamina", amount = 50},
    greater_stamina_potion = {name = "Greater Stamina Potion", category = "healing", effect = "restore_stamina", amount = 100},
    superior_stamina_potion = {name = "Superior Stamina Potion", category = "healing", effect = "restore_stamina", amount = 250},
    champions_brew = {name = "Champion's Brew", category = "healing", effect = "restore_stamina", amount = 999},

    -- Buff Potions
    potion_of_strength = {name = "Potion of Strength", category = "buff", effect = "increase_attack", percentage = 10, duration = 300},
    potion_of_fortitude = {name = "Potion of Fortitude", category = "buff", effect = "increase_defense", percentage = 10, duration = 300},
    potion_of_speed = {name = "Potion of Speed", category = "buff", effect = "increase_speed", percentage = 10, duration = 300},
    potion_of_precision = {name = "Potion of Precision", category = "buff", effect = "increase_crit_chance", percentage = 10, duration = 300},
    potion_of_luck = {name = "Potion of Luck", category = "buff", effect = "increase_loot_drop", percentage = 10, duration = 300},
    potion_of_fire_resistance = {name = "Potion of Fire Resistance", category = "buff", effect = "reduce_fire_damage", percentage = 50, duration = 600},
    potion_of_frost_resistance = {name = "Potion of Frost Resistance", category = "buff", effect = "reduce_ice_damage", percentage = 50, duration = 600},
    potion_of_shock_resistance = {name = "Potion of Shock Resistance", category = "buff", effect = "reduce_lightning_damage", percentage = 50, duration = 600},
    potion_of_magic_resistance = {name = "Potion of Magic Resistance", category = "buff", effect = "reduce_magic_damage", percentage = 25, duration = 600},
    potion_of_the_titan = {name = "Potion of the Titan", category = "buff", effect = "double_max_hp", duration = 120},

    -- Debuff Potions
    weakening_poison = {name = "Weakening Poison", category = "debuff", effect = "reduce_attack", percentage = 10, duration = 300},
    crippling_poison = {name = "Crippling Poison", category = "debuff", effect = "reduce_defense", percentage = 10, duration = 300},
    slowing_toxin = {name = "Slowing Toxin", category = "debuff", effect = "reduce_speed", percentage = 10, duration = 300},
    paralysis_poison = {name = "Paralysis Poison", category = "debuff", effect = "stun_enemy", duration = 5},
    cursed_elixir = {name = "Cursed Elixir", category = "debuff", effect = "increase_damage_taken", percentage = 20, duration = 300},

    -- Special Potions
    invisibility_potion = {name = "Invisibility Potion", category = "special", effect = "invisibility", duration = 30},
    potion_of_levitation = {name = "Potion of Levitation", category = "special", effect = "temporary_flight", duration = 30},
    potion_of_phasing = {name = "Potion of Phasing", category = "special", effect = "pass_through_walls", duration = 15},
    potion_of_regeneration = {name = "Potion of Regeneration", category = "special", effect = "heal_over_time", amount = 1, duration = 600},
    potion_of_energy_burst = {name = "Potion of Energy Burst", category = "special", effect = "restore_all_abilities", uses = 1},
    potion_of_minds_eye = {name = "Potion of Mind’s Eye", category = "special", effect = "reveal_hidden_objects", duration = 600},
    potion_of_resurrection = {name = "Potion of Resurrection", category = "special", effect = "revive_on_death", uses = 1},
    potion_of_wild_magic = {name = "Potion of Wild Magic", category = "special", effect = "random_effect", duration = 30},
    potion_of_transmutation = {name = "Potion of Transmutation", category = "special", effect = "upgrade_random_item", uses = 1},
    potion_of_time_dilation = {name = "Potion of Time Dilation", category = "special", effect = "slow_time", duration = 10}
}

return Potions
