local Songbird = {
    id = "songbird",
    name = "Songbird",
    type = "songbird",

    -- Entity categories
    categories = {"animal", "bird"},

    -- Threat and food categories
    threatCategories = {"predator"},
    foodCategories = {"insect", "seed", "berry"},

    -- Stats
    maxHealth = 8,
    health = 8,
    speed = 0.8,
    canFly = true,

    -- Behaviors
    behaviors = {"wander", "flee", "peck", "sing"},

    -- Behavior configurations
    behaviorConfigs = {
        wander = {
            moveSpeed = 1.2,
            changeDirectionChance = 0.15,
            idleChance = 0.2,
            idleDuration = {1, 3},
            wanderRadius = 10,
            flightChance = 0.6  -- Higher chance to fly while wandering
        },
        flee = {
            useCategories = true,
            moveSpeed = 2.5,
            detectionRadius = 7
        },
        peck = {
            foodTypes = {"insect", "seed", "berry"},
            peckRadius = 2,
            peckCooldown = 0.8
        },
        sing = {
            songCooldown = {5, 15}  -- Time between songs
        }
    },

    -- Appearance
    appearance = {
        sprite = "songbird",
        scale = 0.5,
        animations = {
            "idle", "walk", "fly", "peck", "sing"
        }
    },

    -- Sound effects with synth configuration
    sounds = {
        chirp = {
            file = "songbird_chirp",
            synth = {
                instrument = "classical_guitar",
                notes = {"A4", "C5"},
                durations = {0.2, 0.3},
                volume = 0.3,
                vibrato = true,
                vibratoRate = 5.0
            }
        },
        sing = {
            file = "songbird_song",
            synth = {
                instrument = "classical_guitar",
                notes = {"G4", "A4", "C5", "E5", "D5"},
                durations = {0.4, 0.3, 0.5, 0.4, 0.6},
                volume = 0.35,
                vibrato = true,
                vibratoRate = 4.0
            }
        },
        footstep = {
            synth = {
                instrument = "xylophone",
                notes = {"C4"},
                duration = 0.08,
                volume = 0.1
            }
        },
        fly = {
            synth = {
                instrument = "harmonica",
                notes = {"E4", "G4"},
                durations = {0.15, 0.15},
                volume = 0.2
            }
        },
        hurt = {
            synth = {
                instrument = "electric_guitar",
                notes = {"D#4"},
                duration = 0.25,
                volume = 0.4,
                vibrato = true,
                vibratoRate = 8.0
            }
        },
        death = {
            synth = {
                instrument = "cello",
                notes = {"F4", "D4", "C4"},
                durations = {0.3, 0.3, 0.8},
                volume = 0.4,
                vibrato = true,
                vibratoRate = 2.0
            }
        },
        alert = {
            synth = {
                instrument = "trumpet",
                notes = {"G5", "E5"},
                durations = {0.15, 0.2},
                volume = 0.4
            }
        }
    },

    -- Loot drops
    drops = {
        {id = "feather", chance = 0.8, quantity = {1, 2}}
    }
}

-- Initialize the songbird entity
function Songbird.init(entity, world)
    entity.x = entity.x or 0
    entity.y = entity.y or 0
    entity.rotation = entity.rotation or 0
    entity.width = 1
    entity.height = 1
    entity.type = "songbird"
    
    -- Apply behavior
    local behaviorName = "songbird_behavior"
    -- Check if world is not nil before trying to access it
    if world and world.modules and world.modules.behaviors then
        local behavior = world.modules.behaviors[behaviorName]
        if behavior then
            behavior.apply(entity)
        end
    end
    
    return entity
end

-- Update the songbird entity
function Songbird.update(entity, world, dt)
    -- Handle singing behavior
    entity.songTimer = entity.songTimer - dt
    if entity.songTimer <= 0 then
        -- Trigger singing animation and sound
        if entity.triggerAnimation then
            entity.triggerAnimation("sing")
        end
        if entity.playSound then
            entity.playSound("sing")
        end

        -- Reset song timer
        entity.songTimer = math.random(entity.behaviorConfigs.sing.songCooldown[1], entity.behaviorConfigs.sing.songCooldown[2])
    end
end

return Songbird