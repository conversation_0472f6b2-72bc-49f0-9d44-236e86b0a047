-- classes/warrior.lua
-- Warrior class definition for Lazarus Arc

local Warrior = {
    name = "Warrior",
    description = "A relentless combatant built for close-quarters engagements, excelling in brute force and durability.",
    icon = "warrior_icon.png",

    -- ⚔️ **Playstyle & Trade-offs**
    pros = {
        "High survivability and defense",
        "Versatile weapon use (melee & shields)",
        "Excellent stamina and endurance"
    },
    cons = {
        "Limited ranged combat capabilities",
        "Heavy armor slows movement",
        "Magic resistance is lower than other classes"
    },

    -- 🏋 **Base Stats & Growth**
    base_stats = { 
        health = 130, 
        maxHealth = 130,
        stamina = 90, 
        maxStamina = 90,
        mana = 20, 
        maxMana = 20,
        pattack = 15, 
        pdefense = 8, 
        mattack = 5, 
        mdefense = 5, 
        speed = 5, 
        luck = 4
    },

    stat_growth = {
        health = 12,
        stamina = 7,
        mana = 2,
        pattack = 1.5,
        pdefense = 0.8,
        mattack = 0.3,
        mdefense = 0.5,
        speed = 0.5,
        luck = 0.3
    },

    -- ⚔️ **Weapon Proficiency**
    proficiencies = { 
        sword = 2.0,  
        dagger = 0.8,
        greatsword = 2.2,  
        axe = 2.0,
        mace = 1.8,
        bow = 0.5,  
        magic = 0.3  
    },

    -- 🌿 **Elemental Affinities**
    element_affinities = {
        earth = 10,  -- Stability and resilience
        fire = 5,    -- Raw aggression
        air = -5     -- Slower response to airborne attacks
    },

    -- 💥 **Overdrive Abilities**
    overdrive = { 
        "berserker_rage", 
        "earthquake_strike", 
        "unbreakable_wall" 
    },

    -- 🎯 **Starting Skills**
    starting_skills = {
        "heavy_strike",
        "shield_block",
        "battle_roar"
    },

    -- 📈 **Skill Progression**
    skill_tree = {
        tier1 = {
            "sunder_armor",
            "defensive_stance",
            "power_swing"
        },
        tier2 = {
            "iron_guard",
            "adrenaline_rush",
            "war_cry"
        },
        tier3 = {
            "titan_slam",
            "last_stand",
            "indomitable_spirit"
        }
    },

    -- 🎭 **Prestige Paths**
    prestige_paths = {
        berserker = {
            description = "A fearless, unstoppable force who thrives on rage and sheer power.",
            bonuses = {
                "Increased attack power when below 50% HP",
                "Faster attack speed",
                "Stronger against multiple enemies"
            },
            weaknesses = {
                "Lowered defenses while raging",
                "Cannot use shields effectively"
            }
        },

        knight = {
            description = "A disciplined protector who blends offense and defense.",
            bonuses = {
                "Shield-based counterattacks",
                "Increased resistance to magic",
                "Passive health regeneration"
            },
            weaknesses = {
                "Slower attack speed",
                "Limited agility compared to Berserkers"
            }
        },

        gladiator = {
            description = "A showman of battle, skilled in crowd control and battlefield adaptability.",
            bonuses = {
                "Improved stun abilities",
                "Can use light armor for better speed",
                "Stronger counterattacks"
            },
            weaknesses = {
                "Loses raw damage output of Berserkers",
                "Limited effectiveness against magic users"
            }
        }
    }
}

return Warrior
