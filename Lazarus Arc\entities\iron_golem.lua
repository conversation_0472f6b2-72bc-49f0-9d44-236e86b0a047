-- entities/iron_golem.lua
-- Iron golem with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local IronGolem = {
    id = "iron_golem",
    name = "Iron Golem",
    type = "iron_golem",
    shape = {
        {0, -11}, {8, -8}, {11, 0}, {8, 8},
        {0, 11}, {-8, 8}, {-11, 0}, {-8, -8}
    },
    size = 14,

    -- Entity categories
    categories = {"golem", "construct", "metal", "guardian", "mechanical"},
    threatCategories = {"player", "intruder", "destroyer", "rust"},
    
    -- Base stats (metallic construct)
    maxHealth = 250,
    health = 250,
    maxStamina = 100,
    stamina = 100,
    maxMana = 40,
    mana = 40,
    speed = 2.0,
    attack = 40,
    defense = 50,  -- Very high defense
    magicDefense = 20,
    metalResistance = 60,
    rustVulnerability = 2.0,
    construct_durability = 35,
    metal_affinity = 30,
    
    -- Behaviors
    behaviors = {"mechanical_patrol", "metal_manipulation", "construct_maintenance", "iron_fist"},
    behaviorConfigs = {
        mechanical_patrol = {
            patrolRadius = 35,
            mechanicalPrecision = 2.0,
            systematicMovement = true,
            guardianProtocol = 2.2
        },
        metal_manipulation = {
            metalShaping = 2.5,
            magneticControl = 2.0,
            ironWill = 2.2,
            metallicResonance = 1.8
        },
        construct_maintenance = {
            selfMaintenance = 2.0,
            metalAbsorption = true,
            systemRepair = 2.5,
            mechanicalResilience = 3.0
        },
        iron_fist = {
            punchDamage = 3.0,
            metalImpact = true,
            armorPiercing = 2.0,
            mechanicalForce = 2.5
        }
    },
    
    -- Enhanced variant system for iron golems
    variantChances = {
        normal = 0.55,          -- 55% iron golem
        shiny = 0.30,           -- 30% steel golem (shiny)
        rare = 0.12,            -- 12% mithril golem (rare)
        legendary = 0.03        -- 3% adamantine colossus (legendary)
    },
    
    variants = {
        normal = {
            name = "Iron Golem",
            description = "A sturdy golem forged from solid iron",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.5, 0.5, 0.6, 1.0}  -- Metallic gray
            }
        },
        
        shiny = {
            name = "Steel Golem",
            description = "A refined golem crafted from tempered steel",
            statModifiers = {
                maxHealth = 1.3,    -- 325 health
                attack = 1.4,       -- 56 attack
                defense = 1.3,      -- 65 defense
                construct_durability = 1.6,
                steel_hardness = 2.0,
                tempered_strength = 1.8
            },
            appearanceModifiers = {
                scale = 1.1,
                glow = true,
                colorTint = {0.7, 0.7, 0.8, 1.0},  -- Polished steel
                steel_shine = true,
                refined_construction = true,
                precision_joints = true
            },
            soundModifiers = {
                pitch = 1.1,
                volume = 1.2,
                metallic = true
            }
        },
        
        rare = {
            name = "Mithril Golem",
            description = "A legendary golem forged from precious mithril",
            statModifiers = {
                maxHealth = 1.8,    -- 450 health
                attack = 1.6,       -- 64 attack
                defense = 1.8,      -- 90 defense
                speed = 1.4,        -- 2.8 speed (mithril is lighter)
                mithril_properties = 3.0,
                magical_conductivity = 2.5,
                ethereal_strength = 2.0
            },
            appearanceModifiers = {
                scale = 1.2,
                colorTint = {0.8, 0.9, 1.1, 1.0},  -- Silvery mithril
                mithril_glow = true,
                ethereal_shimmer = true,
                magical_runes = true,
                light_weight_grace = true
            },
            soundModifiers = {
                pitch = 1.2,
                volume = 1.3,
                reverb = true,
                ethereal = true
            }
        },
        
        legendary = {
            name = "Adamantine Colossus",
            description = "A colossal golem forged from indestructible adamantine",
            statModifiers = {
                maxHealth = 2.8,    -- 700 health
                attack = 2.5,       -- 100 attack
                defense = 2.5,      -- 125 defense
                construct_durability = 4.0,
                adamantine_invincibility = 10.0,
                colossus_might = 5.0,
                indestructible_form = 3.0
            },
            appearanceModifiers = {
                scale = 1.6,
                glow = true,
                colorTint = {0.3, 0.4, 0.5, 1.0},  -- Dark adamantine with power glow
                adamantine_aura = "legendary",
                indestructible_form = true,
                colossus_presence = true,
                reality_anchor = true
            },
            soundModifiers = {
                pitch = 0.6,
                volume = 1.8,
                reverb = true,
                echo = true,
                adamantine_resonance = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "iron_ingot", chance = 1.0, quantity = {2, 5}},
        {id = "golem_core", chance = 0.8, quantity = {1, 1}},
        {id = "metal_essence", chance = 0.7, quantity = {1, 3}},
        {id = "mechanical_component", chance = 0.6, quantity = {2, 4}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "steel_ingot", chance = 1.0, quantity = {2, 4}},
            {id = "tempered_golem_core", chance = 0.9, quantity = {1, 1}},
            {id = "steel_hardness_essence", chance = 0.8, quantity = {1, 2}},
            {id = "precision_gear", chance = 0.7, quantity = {1, 2}}
        },
        rare = {
            {id = "mithril_ingot", chance = 1.0, quantity = {1, 3}},
            {id = "ethereal_golem_core", chance = 0.9, quantity = {1, 1}},
            {id = "magical_conductivity_crystal", chance = 0.8, quantity = {1, 1}},
            {id = "mithril_rune", chance = 0.7, quantity = {1, 2}}
        },
        legendary = {
            {id = "adamantine_fragment", chance = 1.0, quantity = {1, 2}},
            {id = "colossus_core", chance = 0.95, quantity = {1, 1}},
            {id = "indestructible_essence", chance = 0.9, quantity = {1, 1}},
            {id = "adamantine_invincibility_crystal", chance = 0.8, quantity = {1, 1}},
            {id = "colossus_might_orb", chance = 0.7, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "iron_golem",
        scale = 1.4,
        animations = {
            "mechanical_walk", "iron_fist", "metal_manipulation", "guardian_stance", "colossus_roar"
        },
        variants = {
            "iron_golem", "steel_golem", "mithril_golem", "adamantine_colossus"
        }
    },
    
    -- Sound effects with metallic characteristics
    sounds = {
        metal_clank = {
            file = "iron_golem_clank",
            synth = {
                instrument = "percussion",
                notes = {"F1", "Bb1", "F2"},
                durations = {0.3, 0.2, 0.4},
                volume = 1.0,
                metallic = true
            }
        },
        iron_fist = {
            file = "iron_fist",
            synth = {
                instrument = "percussion",
                notes = {"C1", "F1", "C2"},
                durations = {0.2, 0.1, 0.3},
                volume = 1.2,
                crushing = true
            }
        },
        mechanical_hum = {
            file = "mechanical_hum",
            synth = {
                instrument = "synthesizer",
                notes = {"A2", "D3", "A3"},
                durations = {1.0, 0.8, 1.2},
                volume = 0.6,
                mechanical = true
            }
        },
        metal_resonance = {
            file = "metal_resonance",
            synth = {
                instrument = "brass",
                notes = {"G1", "C2", "G2", "C3"},
                durations = {0.8, 0.6, 0.8, 1.0},
                volume = 0.8,
                resonant = true
            }
        }
    },
    
    -- Special iron golem abilities
    abilities = {
        metal_armor = {
            type = "passive",
            description = "Natural metal armor provides exceptional defense",
            effect = "metal_resistance"
        },
        magnetic_control = {
            type = "active",
            description = "Controls metal objects and weapons in the area",
            effect = "metal_manipulation",
            manaCost = 25,
            cooldown = 12
        },
        iron_fist_strike = {
            type = "active",
            description = "Devastating punch that can shatter armor",
            effect = "armor_piercing_attack",
            cooldown = 15
        },
        mechanical_repair = {
            type = "active",
            description = "Absorbs metal to repair mechanical damage",
            effect = "metal_healing",
            cooldown = 20
        },
        guardian_protocol = {
            type = "passive",
            description = "Enhanced performance when protecting designated areas",
            effect = "guardian_bonus"
        },
        metal_resonance = {
            type = "active",
            description = "Creates harmful vibrations in metal equipment",
            effect = "metal_disruption",
            cooldown = 18
        }
    }
}

-- Initialize the iron golem entity using enhanced template
function IronGolem.init(entity, world)
    -- Copy all fields from IronGolem template to entity instance
    for k, v in pairs(IronGolem) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return IronGolem
