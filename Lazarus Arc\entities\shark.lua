-- entities/shark.lua
-- Underwater shark with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local Shark = {
    id = "shark",
    name = "Shark",
    type = "shark",
    shape = {
        {0, -1.2}, {1.0, -0.4}, {1.5, 0}, {1.0, 0.4},
        {0, 1.2}, {-0.8, 0.6}, {-1.2, 0}, {-0.8, -0.6}
    },
    size = 10,

    -- Entity categories
    categories = {"aquatic", "predator", "fish", "large"},
    threatCategories = {"player", "small_fish", "swimmer"},
    
    -- Base stats (fast aquatic predator)
    maxHealth = 120,
    health = 120,
    maxStamina = 80,
    stamina = 80,
    speed = 3.5,  -- Very fast in water
    attack = 25,
    defense = 8,
    waterBreathing = true,
    bloodSense = 15,  -- Can sense blood from distance
    
    -- Behaviors
    behaviors = {"hunt_aquatic", "patrol_waters", "blood_frenzy"},
    behaviorConfigs = {
        hunt_aquatic = {
            moveSpeed = 4.0,
            huntRadius = 25,
            ambushStyle = true,
            preferredDepth = {5, 50}
        },
        patrol_waters = {
            moveSpeed = 2.5,
            patrolRadius = 40,
            territorial = true,
            depth_variation = true
        },
        blood_frenzy = {
            triggerCondition = "blood_detected",
            speedBonus = 1.8,
            attackBonus = 1.5,
            duration = 15
        }
    },
    
    -- Enhanced variant system for sharks
    variantChances = {
        normal = 0.65,          -- 65% reef shark
        shiny = 0.25,           -- 25% great white (shiny)
        rare = 0.08,            -- 8% tiger shark (rare)
        legendary = 0.02        -- 2% megalodon (legendary)
    },
    
    variants = {
        normal = {
            name = "Reef Shark",
            description = "A common coastal shark hunting in shallow waters",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.7, 0.8, 0.9, 1.0}  -- Blue-gray
            }
        },
        
        shiny = {
            name = "Great White Shark",
            description = "A massive apex predator with incredible power",
            statModifiers = {
                maxHealth = 1.8,    -- 216 health
                speed = 1.3,        -- 4.55 speed
                attack = 1.6,       -- 40 attack
                defense = 1.4,      -- 11.2 defense
                bite_force = 2.0
            },
            appearanceModifiers = {
                scale = 1.4,
                glow = true,
                colorTint = {0.9, 0.9, 1.0, 1.0},  -- Pristine white with intimidating presence
                massive_jaws = true,
                apex_presence = true
            },
            soundModifiers = {
                pitch = 0.7,
                volume = 1.5,
                menacing = true
            }
        },
        
        rare = {
            name = "Tiger Shark",
            description = "A fierce striped shark known for its aggressive nature",
            statModifiers = {
                maxHealth = 1.6,    -- 192 health
                speed = 1.4,        -- 4.9 speed
                attack = 1.7,       -- 42.5 attack
                defense = 1.2,      -- 9.6 defense
                aggression = 2.0,
                versatile_diet = 1.5
            },
            appearanceModifiers = {
                scale = 1.3,
                colorTint = {0.8, 0.7, 0.6, 1.0},  -- Tiger stripes
                tiger_stripes = true,
                aggressive_posture = true,
                battle_scars = true
            },
            soundModifiers = {
                pitch = 0.8,
                volume = 1.4,
                reverb = true,
                aggressive_tone = true
            }
        },
        
        legendary = {
            name = "Megalodon",
            description = "An ancient giant shark of prehistoric proportions",
            statModifiers = {
                maxHealth = 3.0,    -- 360 health
                speed = 1.2,        -- 4.2 speed (slower but devastating)
                attack = 2.5,       -- 62.5 attack
                defense = 2.0,      -- 16 defense
                prehistoric_power = 5.0,
                ocean_dominance = 10.0
            },
            appearanceModifiers = {
                scale = 2.5,
                glow = true,
                colorTint = {0.5, 0.6, 0.8, 1.0},  -- Ancient deep blue
                prehistoric_features = true,
                massive_proportions = true,
                ancient_aura = "legendary",
                ocean_distortion = true
            },
            soundModifiers = {
                pitch = 0.4,
                volume = 2.0,
                reverb = true,
                echo = true,
                prehistoric_roar = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "shark_meat", chance = 0.9, quantity = {3, 5}},
        {id = "shark_fin", chance = 0.7, quantity = {1, 2}},
        {id = "shark_tooth", chance = 0.8, quantity = {2, 4}},
        {id = "shark_skin", chance = 0.6, quantity = {1, 2}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "great_white_jaw", chance = 0.9, quantity = {1, 1}},
            {id = "apex_predator_essence", chance = 0.8, quantity = {1, 1}},
            {id = "massive_shark_tooth", chance = 0.9, quantity = {3, 6}},
            {id = "intimidation_aura", chance = 0.6, quantity = {1, 1}}
        },
        rare = {
            {id = "tiger_shark_hide", chance = 0.9, quantity = {1, 1}},
            {id = "striped_fin", chance = 0.8, quantity = {1, 2}},
            {id = "aggressive_essence", chance = 0.7, quantity = {1, 1}},
            {id = "versatile_predator_gland", chance = 0.5, quantity = {1, 1}}
        },
        legendary = {
            {id = "megalodon_tooth", chance = 0.95, quantity = {1, 3}},
            {id = "prehistoric_shark_spine", chance = 0.9, quantity = {1, 1}},
            {id = "ancient_ocean_essence", chance = 0.8, quantity = {1, 2}},
            {id = "megalodon_heart", chance = 0.7, quantity = {1, 1}},
            {id = "ocean_dominance_crystal", chance = 0.6, quantity = {1, 1}},
            {id = "prehistoric_apex_core", chance = 0.5, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "shark",
        scale = 1.2,
        animations = {
            "swim", "hunt", "bite", "circle", "blood_frenzy"
        },
        variants = {
            "reef_shark", "great_white", "tiger_shark", "megalodon"
        }
    },
    
    -- Sound effects with aquatic characteristics
    sounds = {
        swim = {
            file = "shark_swim",
            synth = {
                instrument = "synthesizer",
                notes = {"C2", "E2"},
                durations = {0.8, 1.0},
                volume = 0.3,
                underwater = true
            }
        },
        bite = {
            file = "shark_bite",
            synth = {
                instrument = "percussion",
                notes = {"D2"},
                duration = 0.3,
                volume = 0.7,
                crushing = true
            }
        },
        blood_frenzy = {
            file = "shark_frenzy",
            synth = {
                instrument = "distorted_guitar",
                notes = {"E2", "G2", "E2"},
                durations = {0.2, 0.3, 0.4},
                volume = 0.8,
                frenzied = true
            }
        },
        prehistoric_roar = {
            file = "megalodon_roar",
            synth = {
                instrument = "organ",
                notes = {"C1", "E1", "G1"},
                durations = {1.0, 0.8, 1.2},
                volume = 1.0,
                ancient = true
            }
        }
    },
    
    -- Special aquatic abilities
    abilities = {
        blood_sense = {
            type = "passive",
            description = "Can detect injured creatures from great distances",
            effect = "enhanced_detection"
        },
        aquatic_speed = {
            type = "passive",
            description = "Moves much faster in water",
            effect = "water_speed_bonus"
        },
        crushing_bite = {
            type = "active",
            description = "Devastating bite attack with high critical chance",
            effect = "high_damage_bite",
            cooldown = 8
        },
        territorial_dominance = {
            type = "passive",
            description = "Other aquatic creatures flee from presence",
            effect = "intimidation_aura"
        }
    }
}

-- Initialize the shark entity using enhanced template
function Shark.init(entity, world)
    -- Copy all fields from Shark template to entity instance
    for k, v in pairs(Shark) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return Shark
