local Names = require("npc_names")
local Professions = require("npc_professions")

local NPCGenerator = {}

function NPCGenerator.randomName(gender)
    local list = Names[gender] or Names.male
    return list[math.random(#list)]
end

function NPCGenerator.createNPC(professionKey)
    local prof = Professions[professionKey]
    if not prof then return nil end

    local gender = math.random() < 0.5 and "male" or "female"
    local name = NPCGenerator.randomName(gender)

    local npc = {
        id = "npc_" .. tostring(math.random(10000, 99999)),
        name = name,
        gender = gender,
        profession = professionKey,
        appearance = prof.appearance,
        position = {x = 0, y = 0},
        velocity = {x = 0, y = 0},
        type = "npc",
        tags = prof.tags or {},
        behaviors = prof.behaviors
    }

    return npc
end

return NPCGenerator
