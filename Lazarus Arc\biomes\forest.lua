-- biomes/forest.lua
local ForestBiome = {
    id = "forest",
    name = "Forest",
    
    -- Core tile types used in this biome
    primaryTiles = {"grass", "dirt", "tree"},
    secondaryTiles = {"bush", "flower", "mushroom"},
    rareTiles = {"ancient_tree", "fairy_ring", "magic_spring"},
    
    -- Default proportions (adjustable through variants)
    tileDistribution = {
        grass = 0.35,
        dirt = 0.25,
        tree = 0.20,
        bush = 0.10,
        flower = 0.05,
        mushroom = 0.03,
        ancient_tree = 0.01,
        fairy_ring = 0.005,
        magic_spring = 0.005
    },
    
    -- Entities common to this biome
    commonEntities = {
        "deer", "rabbit", "squirrel", "bird", "fox"
    },
    
    uncommonEntities = {
        "wolf", "bear", "boar", "forest_spirit", "dryad"
    },
    
    rareEntities = {
        "unicorn", "treant", "forest_dragon", "fairy"
    },
    
    -- Biome variants for diverse generation
    variants = {
        temperate = {
            name = "Temperate Forest",
            elevationMultiplier = 1.0,
            temperatureOffset = 0.0,
            tileAdjustments = {
                grass = 0.4,
                tree = 0.25,
                flower = 0.1
            },
            entityAdjustments = {
                deer = 1.5,
                bird = 1.3,
                fox = 1.2
            },
            specialEffects = {
                "dappled_sunlight",
                "gentle_breeze",
                "falling_leaves"
            }
        },
        ancient = {
            name = "Ancient Forest",
            elevationMultiplier = 1.1,
            temperatureOffset = -2.0,
            tileAdjustments = {
                tree = 0.3,
                ancient_tree = 0.15,
                magic_spring = 0.1,
                grass = 0.25
            },
            entityAdjustments = {
                treant = 2.0,
                forest_spirit = 1.5,
                unicorn = 1.0
            },
            specialEffects = {
                "mystical_mist",
                "glowing_mushrooms",
                "ancient_whispers"
            }
        },
        enchanted = {
            name = "Enchanted Forest",
            elevationMultiplier = 1.0,
            temperatureOffset = 0.0,
            tileAdjustments = {
                grass = 0.3,
                fairy_ring = 0.1,
                magic_spring = 0.1,
                flower = 0.2
            },
            entityAdjustments = {
                fairy = 2.0,
                forest_spirit = 1.5,
                unicorn = 1.2
            },
            specialEffects = {
                "sparkling_air",
                "floating_lights",
                "magical_aurora"
            }
        },
        dense = {
            name = "Dense Forest",
            elevationMultiplier = 1.0,
            temperatureOffset = -1.0,
            tileAdjustments = {
                tree = 0.4,
                bush = 0.2,
                grass = 0.2
            },
            entityAdjustments = {
                wolf = 1.5,
                bear = 1.3,
                boar = 1.2
            },
            specialEffects = {
                "thick_fog",
                "dense_shadows",
                "creaking_branches"
            }
        }
    },
    
    -- Structures that can generate in this biome
    structures = {
        {
            name = "hunter_camp",
            chance = 0.15,
            entities = {"hunter", "tent", "campfire"}
        },
        {
            name = "fairy_grove",
            chance = 0.08,
            requiresVariant = "enchanted",
            entities = {"fairy", "magic_spring", "glowing_flowers"}
        },
        {
            name = "ancient_ruins",
            chance = 0.05,
            requiresVariant = "ancient",
            entities = {"stone_guardian", "ancient_artifact", "moss_covered_wall"}
        },
        {
            name = "herbalist_hut",
            chance = 0.12,
            entities = {"herbalist", "herb_garden", "potion_brewing_station"}
        },
        {
            name = "treant_grove",
            chance = 0.07,
            requiresVariant = "ancient",
            entities = {"treant", "ancient_tree", "magic_spring"}
        },
        {
            name = "wolf_den",
            chance = 0.1,
            requiresVariant = "dense",
            entities = {"wolf", "wolf_pup", "bone_pile"}
        }
    },
    
    -- Weather patterns
    weather = {
        clear = 0.3,
        cloudy = 0.3,
        rain = 0.2,
        foggy = 0.15,
        storm = 0.05
    }
}

return ForestBiome 