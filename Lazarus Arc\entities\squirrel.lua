-- entities/squirrel.lua
local Squirrel = {
    id = "squirrel",
    name = "Squirrel",
    type = "squirrel",

    -- Entity categories
    categories = {"animal", "prey", "mammal", "small"},

    -- Threat and food categories
    threatCategories = {"predator", "player"},
    foodCategories = {"nut", "seed", "berry", "fruit"},

    -- Stats
    maxHealth = 15,
    health = 15,
    maxStamina = 45,
    stamina = 45,
    speed = 2.0,

    -- Behaviors
    behaviors = {"flee", "wander", "forage", "climb", "cache"},

    -- Behavior configurations
    behaviorConfigs = {
        flee = {
            moveSpeed = 2.8,
            detectionRadius = 10,
            climbChance = 0.8,
            hideDuration = {2, 5}
        },
        wander = {
            moveSpeed = 1.5,
            changeDirectionChance = 0.12,
            idleChance = 0.25,
            idleDuration = {1, 3},
            wanderRadius = 8
        },
        forage = {
            moveSpeed = 1.2,
            searchRadius = 12,
            forageTime = {3, 6},
            foodTypes = {"nut", "seed", "berry", "fruit"},
            cacheChance = 0.6
        },
        climb = {
            maxHeight = 6,
            climbSpeed = 1.8,
            descentSpeed = 2.0,
            branchHopChance = 0.4,
            branchHopDistance = {2, 4}
        },
        cache = {
            maxCaches = 5,
            cacheRadius = 15,
            cacheTypes = {"nut", "seed"},
            retrieveChance = 0.3,
            retrieveInterval = {20, 40}
        }
    },

    -- Special abilities
    abilities = {
        quickClimb = {
            speedBoost = 1.8,
            duration = 1.5,
            cooldown = 4
        },
        chatter = {
            range = 6,
            duration = 1,
            cooldown = 3,
            effect = "alert"
        }
    },

    -- Appearance
    appearance = {
        sprite = "squirrel",
        scale = 0.7,
        animations = {
            "idle", "walk", "run", "climb", "forage", "chatter"
        },
        variants = {
            "red", "gray", "black", "albino"
        }
    },

    -- Sound effects
    sounds = {
        chatter = "squirrel_chatter",
        rustle = "squirrel_rustle",
        climb = "squirrel_climb"
    },

    -- Loot drops
    drops = {
        {id = "meat", chance = 0.7, quantity = {1, 1}},
        {id = "tail", chance = 0.5, quantity = {1, 1}},
        {id = "fur", chance = 0.6, quantity = {1, 2}},
        {id = "cached_nut", chance = 0.3, quantity = {1, 2}}
    }
}

-- Initialize the entity
function Squirrel.init(entity, world)
    -- Copy all fields from Squirrel template to entity instance
    for k, v in pairs(Squirrel) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random squirrel variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Squirrel.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end
end

return Squirrel