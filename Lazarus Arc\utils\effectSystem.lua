-- effectSystem.lua (LÖVE 2D Focused)
-- Manages visual effects like hit splashes, block shields, evasion puffs, and status indicators using LÖVE 2D objects.

local EffectSystem = {
    activeEffects = {}, -- Store active visual effects (particles, text, etc.)
    initialized = false,
    world = nil,
    defaultFont = nil -- Store a default font reference
}

-- Initialize the Effect System
function EffectSystem:init(world, defaultFont)
    if self.initialized then return end
    print("Initializing LÖVE 2D Effect System...")
    self.world = world -- Reference to the main game world if needed
    self.activeEffects = {}

    -- Store the default font if provided, otherwise try loading one
    if defaultFont then
        self.defaultFont = defaultFont
    else
        -- Attempt to load a default font (adjust path/size as needed)
        local success, font = pcall(love.graphics.newFont, 12)
        if success then
            self.defaultFont = font
            print("Loaded default font for effects.")
        else
            print("Warning: Could not load default font for EffectSystem.")
        end
    end

    self.initialized = true
    print("LÖVE 2D Effect System Initialized.")
    return self
end

-- Update active effects (particles, text lifetimes, animations)
function EffectSystem:update(dt)
    if not self.initialized then return end

    -- Iterate backwards to safely remove expired effects
    for i = #self.activeEffects, 1, -1 do
        local effect = self.activeEffects[i]
        effect.lifetime = effect.lifetime - dt

        local removeEffect = false
        if effect.lifetime <= 0 then
            removeEffect = true
        end

        -- Update particle system if it exists
        if effect.particleSystem then
            effect.particleSystem:update(dt)
            -- Optionally remove if lifetime expired AND particles are gone
            if removeEffect and effect.particleSystem:getCount() == 0 then
                 -- Particle system finished after its intended life
            elseif effect.particleSystem:isActive() == false and effect.particleSystem:getCount() == 0 then
                 -- Remove if system finishes early (e.g., non-looping emitter)
                 -- You might want different logic here depending on particle type
                 -- removeEffect = true
            end
        end

        -- Update text properties (e.g., fade out, move)
        if effect.effectType == "text" and effect.update then
             effect:update(dt)
        end

        -- Add updates for other effect types (animations, etc.) here

        -- Remove expired or finished effects
        if removeEffect then
            -- Stop particles if they are still running but lifetime is up
            if effect.particleSystem then
                effect.particleSystem:stop()
            end
            -- Call cleanup if defined
            if effect.cleanUp then
                effect:cleanUp()
            end
            table.remove(self.activeEffects, i)
        end
    end
end

-- Draw active effects
function EffectSystem:draw()
    if not self.initialized then return end

    for _, effect in ipairs(self.activeEffects) do
        -- Draw particle system
        if effect.particleSystem then
            love.graphics.draw(effect.particleSystem, effect.x, effect.y)
        end

        -- Draw text effect
        if effect.effectType == "text" then
            local font = effect.font or self.defaultFont
            if font then
                local r, g, b, a = love.graphics.getColor()
                love.graphics.setColor(effect.color or {1, 1, 1, 1})
                love.graphics.setFont(font)
                -- Example: Draw text that moves up and fades slightly
                local alpha = math.max(0, math.min(1, effect.lifetime / effect.initialLifetime)) -- Fade based on remaining life
                love.graphics.setColor(effect.color[1], effect.color[2], effect.color[3], (effect.color[4] or 1) * alpha)
                love.graphics.print(effect.text, effect.x, effect.y - (effect.initialLifetime - effect.lifetime) * (effect.riseSpeed or 30), effect.rotation or 0, effect.scaleX or 1, effect.scaleY or 1, effect.offsetX or 0, effect.offsetY or 0)
                love.graphics.setColor(r, g, b, a) -- Restore previous color
            end
        end

        -- Add drawing for other effect types here
    end
end

-- Create a new effect using LÖVE 2D objects
-- data might contain: { x, y, lifetime, effectType ("particle", "text", "animation", etc.),
--                      particleConfig (table for newParticleSystem), image (for particles),
--                      text, font, color, riseSpeed (for text),
--                      update = function(self, dt), draw = function(self), cleanUp = function(self) }
function EffectSystem:createEffect(data)
    if not self.initialized then return nil end

    local effect = {
        x = data.x or 0,
        y = data.y or 0,
        lifetime = data.lifetime or 1.0,
        initialLifetime = data.lifetime or 1.0,
        effectType = data.effectType or "generic",
        particleSystem = nil,
        text = data.text or nil,
        font = data.font or self.defaultFont,
        color = data.color or {1, 1, 1, 1},
        riseSpeed = data.riseSpeed, -- Specific to text popups
        rotation = data.rotation, -- For text or other drawables
        scaleX = data.scaleX,
        scaleY = data.scaleY,
        offsetX = data.offsetX, -- For text origin
        offsetY = data.offsetY,
        update = data.update,
        draw = data.draw,       -- Overrides default drawing if provided
        cleanUp = data.cleanUp
    }

    -- Create Particle System if config provided
    if effect.effectType == "particle" and data.particleConfig and data.image then
        local success, psystem = pcall(love.graphics.newParticleSystem, data.image, data.particleConfig)
        if success then
            effect.particleSystem = psystem
            effect.particleSystem:setPosition(effect.x, effect.y) -- Set position immediately
            effect.particleSystem:start()
            -- Adjust lifetime based on emitter if not explicitly set
            if not data.lifetime then
                local emitterLife = effect.particleSystem:getEmitterLifetime()
                effect.lifetime = (emitterLife == -1) and 1.0 or emitterLife -- Use emitter life if finite
                effect.initialLifetime = effect.lifetime
            end
        else
            print("Error creating particle system:", psystem) -- pcall returns error message on failure
        end
    end

    -- Add to active effects
    table.insert(self.activeEffects, effect)
    return effect
end

-- === Combat Specific Effect Creators ===
-- These now create specific types of LÖVE 2D effects

-- NOTE: You need to create these particle configurations!
local particleConfigs = {
    hit = { -- Example Hit Config (replace with actual values)
        emissionRate = 50,
        emitterLifetime = 0.1,
        particleLifetime = {0.2, 0.5},
        direction = math.pi, -- Example: Upwards
        spread = math.pi * 2,
        speed = {50, 100},
        gravity = {0, 300},
        colors = {{1, 0.2, 0, 1}, {1, 0.8, 0.2, 1}, {1, 1, 1, 0}},
        size = {2, 5, 1},
    },
    block = { -- Example Block Config
        emissionRate = 30,
        emitterLifetime = 0.2,
        particleLifetime = {0.3, 0.6},
        direction = 0, spread = math.pi * 2,
        speed = {20, 40},
        colors = {{0.5, 0.5, 1, 1}, {0.8, 0.8, 1, 1}, {1, 1, 1, 0}},
        size = {3, 6, 2},
    },
    evasion = { -- Example Evasion Config
        emissionRate = 20,
        emitterLifetime = 0.15,
        particleLifetime = {0.4, 0.7},
        direction = math.pi/2, spread = math.pi, -- Example: Sideways puff
        speed = {60, 120},
        colors = {{0.8, 0.8, 0.8, 0.8}, {1, 1, 1, 0}},
        size = {4, 8, 1},
        linearDamping = {0.5, 0.9}
    }
    -- Define more configs as needed (e.g., for status effects)
}

-- Load particle images (adjust paths)
local particleImages = {
    default = love.graphics.newImage("assets/particles/default_particle.png"), -- Replace with your image path
    hit = love.graphics.newImage("assets/particles/sparkle.png"),    -- Replace
    block = love.graphics.newImage("assets/particles/shield_bit.png"), -- Replace
    evasion = love.graphics.newImage("assets/particles/smoke_puff.png") -- Replace
}

function EffectSystem:createHitEffect(x, y, damageType, amount)
    print(string.format("Creating Hit Effect at (%.2f, %.2f), Amount: %s", x, y, amount or "N/A"))
    -- Create damage number popup
    if amount and amount > 0 then
        self:createEffect({
            x = x, y = y,
            effectType = "text",
            text = tostring(amount),
            color = {1, 0.8, 0.2, 1}, -- Reddish-orange
            lifetime = 0.8,
            riseSpeed = 40, -- Pixels per second upward movement
            font = self.defaultFont, -- Or a specific damage font
            offsetX = (self.defaultFont and self.defaultFont:getWidth(tostring(amount)) / 2) or 0 -- Center text
        })
    end
    -- Create particle burst
    if particleConfigs.hit and particleImages.hit then
        self:createEffect({
            x = x, y = y,
            effectType = "particle",
            image = particleImages.hit, -- Use specific image for hits
            particleConfig = particleConfigs.hit,
            lifetime = nil -- Let particle system duration define lifetime
        })
    end
end

function EffectSystem:createBlockEffect(x, y)
    print(string.format("Creating Block Effect at (%.2f, %.2f)", x, y))
    -- Create "Blocked!" text popup
    self:createEffect({
        x = x, y = y,
        effectType = "text",
        text = "Blocked!",
        color = {0.5, 0.5, 1, 1}, -- Blue
        lifetime = 0.7,
        riseSpeed = 30,
        font = self.defaultFont,
        offsetX = (self.defaultFont and self.defaultFont:getWidth("Blocked!") / 2) or 0
    })
    -- Create block particle effect
    if particleConfigs.block and particleImages.block then
        self:createEffect({
            x = x, y = y,
            effectType = "particle",
            image = particleImages.block,
            particleConfig = particleConfigs.block,
            lifetime = nil
        })
    end
end

function EffectSystem:createEvasionEffect(x, y)
    print(string.format("Creating Evasion Effect at (%.2f, %.2f)", x, y))
    -- Create "Evaded!" text popup
    self:createEffect({
        x = x, y = y,
        effectType = "text",
        text = "Evaded!",
        color = {0.8, 0.8, 0.8, 1}, -- White/Gray
        lifetime = 0.6,
        riseSpeed = 50, -- Faster rise for evasion?
        font = self.defaultFont,
        offsetX = (self.defaultFont and self.defaultFont:getWidth("Evaded!") / 2) or 0
    })
    -- Create evasion particle effect (e.g., smoke puff)
    if particleConfigs.evasion and particleImages.evasion then
        self:createEffect({
            x = x, y = y,
            effectType = "particle",
            image = particleImages.evasion,
            particleConfig = particleConfigs.evasion,
            lifetime = nil
        })
    end
end

function EffectSystem:createStatusEffect(x, y, effectType)
    print(string.format("Creating Status Effect Visual at (%.2f, %.2f), Type: %s", x, y, effectType))

    -- Fetch status effect details (color, icon, etc.) - Requires your Combat module
    local statusData = Combat and Combat.StatusEffectData and Combat.StatusEffectData[effectType] or {}

    -- Example: Show the status effect name briefly
    self:createEffect({
        x = x, y = y,
        effectType = "text",
        text = statusData.name or effectType,
        color = statusData.color or {1, 1, 0, 1}, -- Yellow default
        lifetime = 1.2,
        riseSpeed = 25,
        font = self.defaultFont,
        offsetX = (self.defaultFont and self.defaultFont:getWidth(statusData.name or effectType) / 2) or 0
    })
    -- Optionally, create a specific particle effect for the status application
    -- if particleConfigs[effectType] and particleImages[effectType] then ...
end


-- Shutdown the Effect System (optional cleanup)
function EffectSystem:shutdown()
    if not self.initialized then return end
    print("Shutting down LÖVE 2D Effect System...")
    self.activeEffects = {}
    self.initialized = false
    self.world = nil
    self.defaultFont = nil -- Release font reference
    print("LÖVE 2D Effect System Shutdown.")
end

return EffectSystem