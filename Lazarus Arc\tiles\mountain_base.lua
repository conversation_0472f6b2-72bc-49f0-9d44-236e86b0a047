-- tiles/mountain_base.lua
local MountainBaseTile = {
    id = "mountain_base",
    name = "Mountain Base",
    passable = true, -- Generally passable, but often uneven or sloped
    variants = 6, -- e.g., Gentle Foothills, Talus Slope, Rocky Outcrops, Sparse Pine Forest, Runoff Gully, High Meadow

    -- Entities found at the transition zone
    spawns = {"mountain_goat_lowland", "deer", "bear_ foraging", "foothills_herb", "fallen_log", "loose_boulder", "prospector_camp_small"},

    -- Movement properties
    movementSpeed = 0.85, -- Slower due to slope and uneven terrain (rocks, roots)

    -- Mountain Base specific properties
    slope = 0.3, -- Average slope gradient (0-1)
    elevation = 1, -- Relative elevation level (0=sea level, mountain=3+)
    hasTalus = false, -- Is it primarily loose rock debris? (Set by variant)
    runoffEffect = 0.4, -- How much it's affected by mountain runoff (0-1)
    isTrailhead = false, -- Does a path up the mountain start here?

    -- Interaction function
    interact = function(tile, entity)
        -- Look for trails or climbing routes
        if math.random() < 0.25 then
             if tile.isTrailhead then
                 return { success = true, message = "You find a faint path leading upwards towards the mountain peaks.", effects={{type="discover_path", path_id="mountain_trail_"..tile.x..tile.y}} }
             elseif entity.skills and entity.skills.climbing > 2 then
                 return { success = true, message = "You scan the mountain face above, looking for potential handholds to begin a climb.", effects={{type="assess_climbing_route", difficulty=(tile.baseClimbDifficulty or 7)}} }
             end
        end

        -- Gather resources
        if math.random() < 0.3 then
             local resources = {
                 {id="stone", chance=0.4},
                 {id="sturdy_branch", chance=0.3, condition_property="hasTrees"}, -- Changed condition=tile.hasTrees
                 {id="mountain_herb_common", chance=0.2},
                 {id="iron_deposit_trace", chance=0.1, condition_item="pickaxe"} -- Changed condition=entity.hasItem...
             }
             local possibleFinds = {} -- Filter based on conditions
             for _, res in ipairs(resources) do
                 local conditionMet = true
                 if res.condition_property then conditionMet = tile[res.condition_property] == true end
                 if res.condition_item then conditionMet = conditionMet and (entity.hasItem and entity:hasItem(res.condition_item)) end -- Check item condition

                 if conditionMet then table.insert(possibleFinds, res) end
             end
             
             if #possibleFinds > 0 then
                 for _, res in ipairs(possibleFinds) do
                     if math.random() < res.chance then
                         local qty = (res.id=="stone" or res.id=="sturdy_branch") and math.random(1,3) or 1
                         return { success = true, message = "Searching the area, you gather " .. qty .. " " .. res.id .. ".", effects={{type="add_item", item=res.id, quantity=qty}} }
                     end
                 end
             end
        end

        -- Examine the terrain
        local description = "The ground slopes upwards towards the towering mountain nearby. "
        if tile.hasTalus then description = description .. "Loose rock debris covers the slope."
        elseif tile.hasTrees then description = description .. "Sparse trees cling to the rocky soil."
        else description = description .. "Rocky outcrops and hardy grasses dominate the landscape." end
        
        return {
            success = false,
            message = description
        }
    end,

    -- Weather effects (influenced by mountain)
    weatherEffects = {
        rain = function(tile)
            tile.isWet = true
            tile.slipperiness = 0.3 -- Moderate slipperiness on slopes/rocks
            -- Increased runoff
            if math.random() < (tile.runoffEffect or 0.4) then
                 return "create_effect", "temporary_streamlet"
            end
            return "visual_effect", "wet_rocks_soil"
        end,
        
        snow = function(tile) -- Snow accumulates more easily here than lowlands
            tile.isSnowCovered = true
            tile.slipperiness = 0.5 -- Snow on uneven ground is tricky
            tile.movementSpeedModifier = 0.8
            return "visual_effect", "snow_covered_slopes"
        end,

        wind = function(tile) -- Can be windier due to mountain topography
             -- Assuming world context is available if needed, e.g., via tile.worldRef
             local currentWindStrength = (tile.worldRef and tile.worldRef.windStrength) or 0
             if currentWindStrength > 0.7 then
                 return "apply_effect_area", {type="wind_buffet", intensity=0.3}
             end
             return nil
        end,
        
         freeze = function(tile)
            if tile.isWet then
                 tile.isIcy = true
                 tile.slipperiness = 0.6
                 return "visual_effect", "icy_patches_slope"
            end
            return nil
        end,
        
        sun = function(tile) -- Drying / Melting
             tile.isWet = false
             tile.isIcy = false
             tile.slipperiness = 0
             tile.movementSpeedModifier = 1.0
             -- Assuming world context is available if needed, e.g., via tile.worldRef
             local currentTemp = (tile.worldRef and tile.worldRef.temperature) or 0
              if tile.isSnowCovered and currentTemp > 3 then
                  tile.isSnowCovered = false
                  return "remove_visual_effect", "snow_covered_slopes"
             end
             return "remove_visual_effect", {"wet_rocks_soil", "icy_patches_slope"}
        end
    },

     -- Time effects
    timeEffects = {
        morning = function(tile) -- Mountain might cast long morning shadows
            -- Assuming world context is available if needed, e.g., via tile.worldRef
            local sunPos = (tile.worldRef and tile.worldRef.sunPosition) or "none"
            if sunPos == "east" then tile.isInShadow = true; tile.temperatureOffset = -3 end
            tile.ambientSound = "mountain_base_morning" -- Birds, distant calls
            return nil
        end,
        evening = function(tile) -- Mountain might cast long evening shadows
             -- Assuming world context is available if needed, e.g., via tile.worldRef
             local sunPos = (tile.worldRef and tile.worldRef.sunPosition) or "none"
             if sunPos == "west" then tile.isInShadow = true; tile.temperatureOffset = -2 end
             tile.ambientSound = "mountain_base_evening" -- Crickets, settling sounds
            return nil
        end,
        night = function(tile)
             tile.isInShadow = true -- Always dark
             tile.temperatureOffset = -5
             tile.ambientSound = "mountain_base_night" -- Wind, nocturnal animals
            return nil
        end,
        midday = function(tile)
             tile.isInShadow = false; tile.temperatureOffset = 0
             tile.ambientSound = "mountain_base_day"
            return nil
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {0.5, 0.45, 0.35}, -- Earthy brown/grey mix
        height_map = true, -- Sloping terrain
        height_scale = 0.5, -- Moderate height variation
        variants = {
            {name = "Gentle Foothills", slope=0.2, texture="grassy_rocky_mix"},
            {name = "Talus Slope", slope=0.5, texture="loose_rock_debris", hasTalus=true, movementSpeed=0.75},
            {name = "Rocky Outcrops", overlay_objects={{name="large_boulder", chance=0.4}, {name="rock_ledge", chance=0.2}}},
            {name = "Sparse Pine Forest", overlay_objects={{name="pine_tree_small", chance=0.6}, {name="fallen_log", chance=0.3}}, hasTrees=true},
            {name = "Runoff Gully", texture="eroded_gully", isWet=true, runoffEffect=0.8}, -- Wetter variant
            {name = "High Meadow", slope=0.1, texture="alpine_meadow_grass", overlay_objects={{name="wildflower_alpine", chance=0.5}}}
        },
        decoration_objects = {
            {name = "medium_rock", chance = 0.4},
            {name = "scrub_bush", chance = 0.3},
            {name = "animal_trail_faint", chance = 0.2}
        },
         special_effects = {
             slope_shadows = {dynamic=true, intensity=0.5}, -- Shadows based on slope and sun
             dust_or_mist = {type="particle", density=0.1, when="isDryOrHumid"} -- Changed condition=tile.isDry or tile.isHumid
         },
         weather_overlays = {
             wet_shine = {shininess=0.3, when="isWet"},
             snow_drifts = {coverage=0.6, when="isSnowCovered"},
             icy_patches = {texture="ice_patch_thin", shininess=0.5, when="isIcy"}
         }
    },
    
    -- Audio properties
    ambientSound = "mountain_base_day", -- Wind, distant birds/animals
    footstepSound = "dirt_rock_step", -- Mix of dirt and rock sounds
     specialSounds = {
        {name = "loose_rock_clatter", trigger = "on_walk", chance = 0.1, condition_property="hasTalus"}, -- Changed condition=tile.hasTalus
        {name = "distant_rockfall_echo", trigger = "random", chance = 0.02},
        {name = "stream_trickle", trigger = "random", base_chance = 0.1, chance_modifier="runoffEffect", condition_property="isWet"} -- Changed chance = 0.1 * tile.runoffEffect, condition=tile.isWet
    },
    -- Audio Modifiers
    audioModifiers = {
         wind_occlusion = 0.3 -- Partially sheltered from wind compared to peak?
    },

    -- Connections to other tiles
    connects_to = {
        "mountain_base", -- Itself
        "mountain", -- The impassable slope above [cite: 266]
        "rocky_ground", -- Common transition [cite: 266]
        "stone", -- Can border solid stone areas
        "dirt", -- Transition to flatter dirt areas
        "grass", -- Transition to meadows/fields
        "forest", -- Edge of forests often meets foothills
        "mountain_path" -- Trails often start here
    },

    -- Pathfinding properties
     pathfinding = {
        travel_cost = 1.2, -- More costly than flat ground
        slope_penalty = 1.4, -- Penalty for moving directly uphill
        hazard_chance = 0.08 -- Chance of stumble on uneven ground/loose rock
    },

    -- Effects on entities that walk on this tile
    onWalk = function(tile, entity)
        local sound = tile.footstepSound or "dirt_rock_step"
        local fatigue = 0.1 * (1 + (tile.slope or 0.3)) -- Fatigue increases with slope
        local effects = {}

        if tile.hasTalus and math.random() < 0.1 then
             sound = "loose_rock_clatter"
             table.insert(effects, {type="apply_status", status="unsteady", duration=2}) -- Minor debuff
        elseif (tile.isWet or tile.isIcy) and math.random() < (tile.slipperiness or 0) * 0.4 then
             sound = "slip_scrape_slope"
             table.insert(effects, {type="apply_status", status="slipping_minor", duration=1})
        end

        table.insert(effects, {type="increase_fatigue", amount = fatigue})

        return {
            sound = sound,
            volume = 0.9,
            effects = effects
        }
    end,
    
    -- Effect when discovering mountain base
    onDiscover = function(tile, entity)
        if entity.journal and entity.journal.addDiscovery then
             entity.journal.addDiscovery({
                type = "terrain",
                name = "Mountain Base",
                location = {x = tile.x, y = tile.y},
                notes = "The sloping foothills leading up to the mountain proper. Terrain is uneven."
            })
        end
        return {
            message = "You begin to ascend the sloping base of the mountain.",
            effects = {
                {type = "reveal_map", radius = 3}
            }
        }
    end
}

function MountainBaseTile.init(world)
    print("Mountain Base tile module initialized")
    -- Register with geography, pathfinding systems
     if world and world.systems and world.systems.geography then
        world.systems.geography:registerTerrainZone("mountain_base", {elevation=1, base_slope=0.3})
    end
end

return MountainBaseTile
