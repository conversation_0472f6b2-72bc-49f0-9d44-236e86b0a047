local BronzeClub = {
    id = "bronze_club",
    name = "Bronze Club",
    type = "weapon",
    category = "mace",
    
    -- Properties
    properties = {
        damage = 24,
        attackSpeed = 1.0,
        range = 1.4,
        durability = 150,
        maxDurability = 150,
        staggerChance = 0.3,
        knockback = 0.5,
        bluntPower = 1.1,
        armorPenetration = 0.3,
        weight = 1.0,
        momentum = 0,
        maxMomentum = 3,
        momentumPower = 1.1
    },
    
    -- Appearance
    appearance = {
        sprite = "bronze_club",
        scale = 1.0,
        animations = {
            "idle",
            "swing",
            "heavy",
            "momentum"
        },
        variants = {
            "bronze",
            "copper",
            "iron",
            "steel"
        },
        blendMode = "normal",
        tint = {0.8, 0.6, 0.4},
        alpha = 1.0
    },
    
    -- Sound effects
    sounds = {
        swing = "club_swing",
        impact = "club_impact",
        stagger = "club_stagger",
        momentum = "club_momentum"
    },
    
    -- Effects
    effects = {
        stagger = {
            type = "status",
            duration = 0.35,
            effects = {
                stagger = true,
                knockback = 0.5
            }
        },
        momentum = {
            type = "status",
            duration = 0.25,
            effects = {
                damage = 1.1
            }
        }
    }
}

-- Initialize the weapon
function BronzeClub.init(weapon, world)
    -- Copy all fields from BronzeClub template to weapon instance
    for k, v in pairs(BronzeClub) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        momentum = 0,
        lastMomentumTime = 0
    }

    return weapon
end

-- Update the weapon
function BronzeClub.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Decay momentum
        if world.time - weapon.properties.weaponState.lastMomentumTime > 1.5 then
            weapon.properties.weaponState.momentum = 0
        end
    end
end

-- Handle attack
function BronzeClub.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Calculate attack direction
    local angle = math.atan2(
        target.position.y - attacker.position.y,
        target.position.x - attacker.position.x
    )
    
    -- Create swing effect
    if world.createEffect then
        world.createEffect({
            type = "swing",
            position = attacker.position,
            angle = angle,
            range = weapon.properties.range,
            duration = 0.35
        })
    end
    
    -- Check for stagger effect
    if math.random() < weapon.properties.staggerChance then
        -- Create stagger effect
        if world.createEffect then
            world.createEffect({
                type = "stagger",
                position = target.position,
                duration = 0.35
            })
        end
        
        -- Apply stagger effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.stagger)
        end
        
        -- Play stagger sound
        if world.playSound then
            world.playSound(weapon.sounds.stagger)
        end
    end
    
    -- Update momentum
    if world.time - weapon.properties.weaponState.lastMomentumTime <= 1.5 then
        weapon.properties.weaponState.momentum = math.min(weapon.properties.maxMomentum,
            weapon.properties.weaponState.momentum + 1)
    else
        weapon.properties.weaponState.momentum = 1
    end
    weapon.properties.weaponState.lastMomentumTime = world.time
    
    -- Apply momentum effect if active
    if weapon.properties.weaponState.momentum > 1 then
        if target.applyEffect then
            target.applyEffect(weapon.effects.momentum)
        end
        
        -- Play momentum sound
        if world.playSound then
            world.playSound(weapon.sounds.momentum)
        end
    end
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function BronzeClub.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough momentum
    if weapon.properties.weaponState.momentum < 2 then
        return false
    end
    
    -- Create heavy swing effect
    if world.createEffect then
        world.createEffect({
            type = "heavy_swing",
            position = attacker.position,
            angle = math.atan2(
                target.position.y - attacker.position.y,
                target.position.x - attacker.position.x
            ),
            range = weapon.properties.range * 1.2,
            duration = 0.5
        })
    end
    
    -- Apply stagger effect with increased power
    if target.applyEffect then
        target.applyEffect({
            type = "stagger",
            duration = 0.5,
            effects = {
                stagger = true,
                knockback = weapon.properties.knockback * 1.5
            }
        })
    end
    
    -- Reset momentum
    weapon.properties.weaponState.momentum = 0
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 2)
    
    return true
end

return BronzeClub 