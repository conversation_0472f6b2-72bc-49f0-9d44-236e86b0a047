-- utils/god_mode.lua
-- God mode functionality for debugging
--
-- ⚠️  SECURITY WARNING: This module provides cheat functionality that MUST be
-- disabled in production builds for MMO environments. God mode would break
-- game balance and economy if accessible to players.
--
-- TODO: Add conditional compilation or build flags to exclude this entirely
-- from release builds.

local GodMode = {}

-- Initialize god mode
function GodMode.init()
    GodMode.enabled = false
    GodMode.originalValues = {}
    GodMode.features = {
        invincibility = true,
        unlimitedResources = true,
        noFallDamage = true,
        unlimitedStamina = true,
        unlimitedMana = true,
        fastMovement = true,
        noClip = false -- Separate from no-clip mode
    }
    
    -- Movement speed multiplier when god mode is active
    GodMode.speedMultiplier = 2.0
    
    -- Resource amounts to maintain
    GodMode.maxResources = {
        health = 9999,
        mana = 9999,
        stamina = 9999,
        gold = 999999,
        experience = 0 -- Don't modify XP by default
    }
end

-- Toggle god mode
function GodMode.toggle()
    GodMode.enabled = not GodMode.enabled
    
    if GodMode.enabled then
        GodMode.activate()
        print("God Mode activated")
    else
        GodMode.deactivate()
        print("God Mode deactivated")
    end
    
    return GodMode.enabled
end

-- Activate god mode
function GodMode.activate()
    GodMode.enabled = true
    print("God Mode: Invincibility, unlimited resources, and enhanced abilities activated")
end

-- Deactivate god mode
function GodMode.deactivate()
    GodMode.enabled = false
    -- Restore original values if we stored them
    GodMode.restoreOriginalValues()
    print("God Mode: Normal gameplay restored")
end

-- Store original player values
function GodMode.storeOriginalValues(player)
    if not player then return end
    
    GodMode.originalValues = {
        health = player.health and player.health.current or player.hp,
        maxHealth = player.health and player.health.max or player.maxHP,
        mana = player.mana and player.mana.current or player.mp,
        maxMana = player.mana and player.mana.max or player.maxMP,
        stamina = player.stamina and player.stamina.current or player.stamina,
        maxStamina = player.stamina and player.stamina.max or player.maxStamina,
        speed = player.speed or player.moveSpeed,
        invulnerable = player.invulnerable or false
    }
end

-- Restore original player values
function GodMode.restoreOriginalValues()
    -- This would be called when god mode is disabled
    -- Implementation depends on how the player object is structured
end

-- Update player with god mode effects
function GodMode.updatePlayer(player, dt)
    if not GodMode.enabled or not player then return end
    
    -- Store original values on first activation
    if not GodMode.originalValues.health then
        GodMode.storeOriginalValues(player)
    end
    
    -- Apply invincibility
    if GodMode.features.invincibility then
        GodMode.applyInvincibility(player)
    end
    
    -- Apply unlimited resources
    if GodMode.features.unlimitedResources then
        GodMode.applyUnlimitedResources(player)
    end
    
    -- Apply movement enhancements
    if GodMode.features.fastMovement then
        GodMode.applyMovementEnhancements(player)
    end
end

-- Apply invincibility
function GodMode.applyInvincibility(player)
    -- Set invulnerable flag
    player.invulnerable = true
    
    -- Maintain full health
    if player.health then
        if type(player.health) == "table" then
            player.health.current = player.health.max or GodMode.maxResources.health
        else
            player.health = player.maxHealth or GodMode.maxResources.health
        end
    elseif player.hp then
        player.hp = player.maxHP or GodMode.maxResources.health
    end
end

-- Apply unlimited resources
function GodMode.applyUnlimitedResources(player)
    -- Unlimited mana
    if player.mana then
        if type(player.mana) == "table" then
            player.mana.current = player.mana.max or GodMode.maxResources.mana
        else
            player.mana = player.maxMana or GodMode.maxResources.mana
        end
    elseif player.mp then
        player.mp = player.maxMP or GodMode.maxResources.mana
    end
    
    -- Unlimited stamina
    if player.stamina then
        if type(player.stamina) == "table" then
            player.stamina.current = player.stamina.max or GodMode.maxResources.stamina
        else
            player.stamina = player.maxStamina or GodMode.maxResources.stamina
        end
    end
    
    -- Unlimited gold/currency
    if player.gold then
        player.gold = math.max(player.gold, GodMode.maxResources.gold)
    elseif player.currency then
        player.currency = math.max(player.currency, GodMode.maxResources.gold)
    end
    
    -- Unlimited inventory space (if applicable)
    if player.inventory and player.inventory.maxSlots then
        player.inventory.maxSlots = 999
    end
end

-- Apply movement enhancements
function GodMode.applyMovementEnhancements(player)
    -- Increase movement speed
    if player.speed then
        if not GodMode.originalValues.speed then
            GodMode.originalValues.speed = player.speed
        end
        player.speed = GodMode.originalValues.speed * GodMode.speedMultiplier
    elseif player.moveSpeed then
        if not GodMode.originalValues.speed then
            GodMode.originalValues.speed = player.moveSpeed
        end
        player.moveSpeed = GodMode.originalValues.speed * GodMode.speedMultiplier
    end
end

-- Check if damage should be prevented
function GodMode.shouldPreventDamage(damageType)
    if not GodMode.enabled then return false end
    
    if GodMode.features.invincibility then
        return true
    end
    
    if GodMode.features.noFallDamage and damageType == "fall" then
        return true
    end
    
    return false
end

-- Modify damage amount
function GodMode.modifyDamage(damage, damageType)
    if not GodMode.enabled then return damage end
    
    if GodMode.shouldPreventDamage(damageType) then
        return 0
    end
    
    return damage
end

-- Check if resource consumption should be prevented
function GodMode.shouldPreventResourceConsumption(resourceType)
    if not GodMode.enabled then return false end
    
    if GodMode.features.unlimitedResources then
        return true
    end
    
    if GodMode.features.unlimitedStamina and resourceType == "stamina" then
        return true
    end
    
    if GodMode.features.unlimitedMana and resourceType == "mana" then
        return true
    end
    
    return false
end

-- Get god mode status for UI display
function GodMode.getStatus()
    if not GodMode.enabled then
        return "Disabled"
    end
    
    local activeFeatures = {}
    for feature, enabled in pairs(GodMode.features) do
        if enabled then
            table.insert(activeFeatures, feature)
        end
    end
    
    return "Active: " .. table.concat(activeFeatures, ", ")
end

-- Toggle specific god mode feature
function GodMode.toggleFeature(feature)
    if GodMode.features[feature] ~= nil then
        GodMode.features[feature] = not GodMode.features[feature]
        print("God Mode " .. feature .. ": " .. (GodMode.features[feature] and "enabled" or "disabled"))
        return GodMode.features[feature]
    end
    return false
end

-- Set speed multiplier
function GodMode.setSpeedMultiplier(multiplier)
    GodMode.speedMultiplier = math.max(0.1, math.min(10.0, multiplier))
    print("God Mode speed multiplier set to: " .. GodMode.speedMultiplier)
end

-- Hook into player damage function (to be called by damage system)
function GodMode.onPlayerDamage(player, damage, damageType, source)
    if GodMode.shouldPreventDamage(damageType) then
        print("God Mode: Prevented " .. (damage or 0) .. " " .. (damageType or "unknown") .. " damage")
        return 0
    end
    return damage
end

-- Hook into resource consumption (to be called by resource systems)
function GodMode.onResourceConsumption(player, resourceType, amount)
    if GodMode.shouldPreventResourceConsumption(resourceType) then
        print("God Mode: Prevented " .. (amount or 0) .. " " .. resourceType .. " consumption")
        return 0
    end
    return amount
end

-- Draw god mode indicator
function GodMode.drawIndicator(x, y)
    if not GodMode.enabled then return end
    
    love.graphics.push()
    
    -- Draw background
    love.graphics.setColor(1, 1, 0, 0.3)
    love.graphics.rectangle("fill", x, y, 150, 20)
    
    -- Draw border
    love.graphics.setColor(1, 1, 0, 0.8)
    love.graphics.setLineWidth(1)
    love.graphics.rectangle("line", x, y, 150, 20)
    
    -- Draw text
    love.graphics.setColor(1, 1, 1, 1)
    love.graphics.print("GOD MODE ACTIVE", x + 5, y + 3)
    
    love.graphics.pop()
end

return GodMode
