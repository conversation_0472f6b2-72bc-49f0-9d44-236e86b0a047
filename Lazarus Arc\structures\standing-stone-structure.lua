-- structures/standing-stone-structure.lua
-- Ancient standing stone structure with mystical properties

local StandingStoneStructure = {
    id = "standing_stone_structure",
    name = "Standing Stone Structure",
    category = "ancient",
    
    -- Physical properties
    width = 8,    -- Tiles wide
    height = 8,   -- Tiles tall
    solid = true, -- Solid structure
    providesShelter = false,
    
    -- Structure properties
    hasInterior = false,
    hasUnderground = false,
    condition = 0.9, -- 0.0 = completely ruined, 1.0 = pristine
    age = 0, -- Will be set during init, in years
    decayRate = 0.000005, -- Very slow base decay rate per year
    mysticalPower = 0.8, -- Affected by component status and alignment
    alignmentLevel = 0.7, -- Affected by seasonal alignment and stone health
    lastAlignment = 0, -- When the stones were last aligned
    alignmentCycle = 100, -- Years between natural alignment cycles
    seasonalEffects = {
        spring = 1.3, -- Strong renewal season
        summer = 1.2, -- Peak power season
        autumn = 0.9, -- Waning power season
        winter = 0.8  -- Dormant season
    },
    cosmeticDamage = {
        stones = 0.0, -- 0.0 = pristine, 1.0 = heavily damaged
        runes = 0.0,
        ground = 0.0,
        energy = 0.0
    },
    
    -- Define structure components
    components = {
        {
            id = "central_stone",
            type = "SUPPORT",
            options = {
                material = "ancient_stone",
                critical = true,
                isLoadBearing = true,
                mystical = true
            }
        },
        {
            id = "outer_stones",
            type = "SUPPORT",
            options = {
                material = "ancient_stone",
                critical = true,
                isLoadBearing = true,
                mystical = true
            }
        },
        {
            id = "runes",
            type = "WALL",
            options = {
                material = "mystical_runes",
                critical = true,
                isExterior = true,
                mystical = true
            }
        },
        {
            id = "ground",
            type = "SUPPORT",
            options = {
                material = "sacred_ground",
                critical = true,
                isLoadBearing = false,
                mystical = true
            }
        },
        {
            id = "energy_field",
            type = "POWER_SOURCE",
            options = {
                material = "mystical_energy",
                critical = true,
                maxOutput = 1000,
                mystical = true
            }
        }
    },
    
    -- Spawn properties
    spawnChance = {
        plains = 0.3,
        forest = 0.4,
        mountain = 0.5,
        ruins = 0.6,
        sacred_ground = 0.8
    },
    
    -- Spawn requirements
    requirements = {
        minDistanceFromOtherStructures = 20,
        validTiles = {"grass", "forest_floor", "stone", "sacred_ground"},
        invalidTiles = {"water", "lava", "void"},
        needsFlatGround = true,
        preferredLocation = "sacred_ground"
    },
    
    -- NPCs and creatures that may inhabit this structure
    inhabitants = {
        common = {
            "druid",
            "shaman",
            "mystic",
            "wanderer"
        },
        uncommon = {
            "ancient_guardian",
            "stone_keeper",
            "energy_walker",
            "rune_master"
        },
        rare = {
            "ancient_one",
            "stone_sentinel",
            "mystical_elder",
            "energy_weaver"
        }
    },
    
    -- Items that may spawn inside
    loot = {
        common = {
            {id = "mystical_herbs", chance = 0.7, quantity = {1, 3}},
            {id = "ancient_scrolls", chance = 0.6, quantity = {1, 2}},
            {id = "energy_crystals", chance = 0.5, quantity = {1, 2}},
            {id = "sacred_stones", chance = 0.4, quantity = {1, 2}}
        },
        uncommon = {
            {id = "mystical_artifacts", chance = 0.3, quantity = {1, 1}},
            {id = "ancient_relics", chance = 0.25, quantity = {1, 1}},
            {id = "energy_cores", chance = 0.2, quantity = {1, 1}},
            {id = "sacred_texts", chance = 0.15, quantity = {1, 1}}
        },
        rare = {
            {id = "ancient_secrets", chance = 0.1, quantity = {1, 1}},
            {id = "mystical_essence", chance = 0.1, quantity = {1, 1}},
            {id = "energy_heart", chance = 0.15, quantity = {1, 1}},
            {id = "sacred_artifact", chance = 0.05, quantity = {1, 1}}
        }
    },
    
    -- Variant types for this structure
    variants = {
        {
            name = "solstice",
            description = "Solstice alignment structure",
            materials = {
                central_stone = "solstice_stone",
                outer_stones = "alignment_stones",
                runes = "solstice_runes",
                ground = "sacred_ground",
                energy_field = "solstice_energy"
            },
            features = {
                "solstice_markers",
                "alignment_points",
                "energy_channels",
                "mystical_ground"
            },
            mysticalPower = 0.9,
            alignmentLevel = 0.8,
            inhabitants = {
                "solstice_guardian",
                "alignment_keeper",
                "energy_walker"
            }
        },
        {
            name = "equinox",
            description = "Equinox alignment structure",
            materials = {
                central_stone = "equinox_stone",
                outer_stones = "balance_stones",
                runes = "equinox_runes",
                ground = "sacred_ground",
                energy_field = "equinox_energy"
            },
            features = {
                "equinox_markers",
                "balance_points",
                "energy_flows",
                "mystical_ground"
            },
            mysticalPower = 0.85,
            alignmentLevel = 0.85,
            inhabitants = {
                "equinox_guardian",
                "balance_keeper",
                "energy_walker"
            }
        },
        {
            name = "lunar",
            description = "Lunar alignment structure",
            materials = {
                central_stone = "lunar_stone",
                outer_stones = "moon_stones",
                runes = "lunar_runes",
                ground = "sacred_ground",
                energy_field = "lunar_energy"
            },
            features = {
                "lunar_markers",
                "moon_points",
                "energy_tides",
                "mystical_ground"
            },
            mysticalPower = 0.8,
            alignmentLevel = 0.9,
            inhabitants = {
                "lunar_guardian",
                "moon_keeper",
                "energy_walker"
            }
        }
    },
    
    -- Tile layout (exterior)
    tileLayout = {
        "wwwwwwww",
        "w......w",
        "w......w",
        "w......w",
        "w......w",
        "w......w",
        "w......w",
        "wwwwwwww"
    },
    
    -- Tile legend for exterior
    tileLegend = {
        w = "standing_stone",
        ["."] = "sacred_ground"
    },
    
    -- Tags for search/filtering
    tags = {"ancient", "mystical", "sacred", "alignment"},
}

-- Define functions OUTSIDE the table, after it's been created
-- Initialize the structure
function StandingStoneStructure.init(structure, world, x, y)
    -- Set structure position
    structure.x = x
    structure.y = y
    
    -- Set random variant if none specified
    structure.variant = structure.variant or math.random(#StandingStoneStructure.variants)
    
    -- Set random age between 100 and 1000 years
    structure.age = math.random(100, 1000)
    
    -- Initialize cosmetic damage based on age
    local ageFactor = structure.age / 1000
    structure.cosmeticDamage = {
        stones = math.min(0.8, ageFactor * 1.2),
        runes = math.min(0.9, ageFactor * 1.5),
        ground = math.min(0.7, ageFactor),
        energy = math.min(0.6, ageFactor * 0.8)
    }
    
    -- Set last alignment time to current age
    structure.lastAlignment = structure.age
    
    -- Apply variant-specific properties
    local variant = StandingStoneStructure.variants[structure.variant]
    
    -- Update component materials based on variant
    for componentId, material in pairs(variant.materials) do
        if structure.components[componentId] then
            structure.components[componentId].options.material = material
        end
    end
    
    -- Update tile legends based on variant
    structure.tileLegend.w = variant.name .. "_standing_stone"
    structure.tileLegend["."] = variant.name .. "_sacred_ground"
    
    -- Set mystical power and alignment levels based on variant
    structure.mysticalPower = variant.mysticalPower
    structure.alignmentLevel = variant.alignmentLevel
    
    -- Adjust levels based on age
    structure.mysticalPower = structure.mysticalPower * (1 - (structure.age * structure.decayRate))
    structure.alignmentLevel = structure.alignmentLevel * (1 - (structure.age * structure.decayRate))
    
    -- Initialize components
    if not structure.components then
        structure.components = {}
        for _, componentData in ipairs(StandingStoneStructure.components) do
            local component = StructureComponents.createComponent(componentData.type, componentData.options)
            structure.components[componentData.id] = component
        end
    end
    
    return structure
end

-- Update the structure
function StandingStoneStructure.update(structure, world, dt)
    -- Get current season
    local currentSeason = world.getCurrentSeason()
    local seasonMultiplier = structure.seasonalEffects[currentSeason]
    
    -- Update all components' seasonal state
    for componentId, component in pairs(structure.components) do
        StructureComponents.updateSeason(component, currentSeason)
    end
    
    -- Update mystical power based on component status and season
    local centralStone = structure.components["central_stone"]
    local outerStones = structure.components["outer_stones"]
    local runes = structure.components["runes"]
    local ground = structure.components["ground"]
    local energyField = structure.components["energy_field"]
    
    if centralStone and outerStones and runes and ground and energyField then
        -- Calculate mystical power based on component health
        local centralHealth = centralStone.health / centralStone.maxHealth
        local outerHealth = outerStones.health / outerStones.maxHealth
        local runeHealth = runes.health / runes.maxHealth
        local groundHealth = ground.health / ground.maxHealth
        local energyHealth = energyField.health / energyField.maxHealth
        
        structure.mysticalPower = math.min(centralHealth, outerHealth, runeHealth, groundHealth, energyHealth)
        
        -- Apply seasonal decay
        local yearsSinceLastAlignment = structure.age - structure.lastAlignment
        local decayFactor = (yearsSinceLastAlignment * structure.decayRate) * seasonMultiplier
        
        -- Natural alignment cycle
        if yearsSinceLastAlignment >= structure.alignmentCycle then
            -- Stones naturally align
            structure.lastAlignment = structure.age
            structure.mysticalPower = math.min(1.0, structure.mysticalPower + 0.2)
            
            -- Reduce cosmetic damage
            for component, damage in pairs(structure.cosmeticDamage) do
                structure.cosmeticDamage[component] = math.max(0.0, damage - 0.2)
            end
        else
            -- Apply decay
            structure.mysticalPower = math.max(0.0, structure.mysticalPower - decayFactor)
            
            -- Increase cosmetic damage
            for component, damage in pairs(structure.cosmeticDamage) do
                structure.cosmeticDamage[component] = math.min(1.0, damage + (decayFactor * 0.1))
            end
        end
    end
    
    -- Update alignment level based on mystical power and season
    structure.alignmentLevel = structure.mysticalPower * seasonMultiplier
    
    -- Update visual effects based on component status and cosmetic damage
    for componentId, component in pairs(structure.components) do
        local visualState = StructureComponents.getVisualState(component)
        
        -- Update tile legend based on visual state
        if component.type == "SUPPORT" then
            local stoneDamage = structure.cosmeticDamage.stones
            if component.status == "destroyed" then
                structure.tileLegend.w = "collapsed_" .. structure.variant.name .. "_stone"
            elseif component.status == "critical" then
                structure.tileLegend.w = "damaged_" .. structure.variant.name .. "_stone"
            elseif stoneDamage > 0.7 then
                structure.tileLegend.w = "weathered_" .. structure.variant.name .. "_stone"
            elseif stoneDamage > 0.3 then
                structure.tileLegend.w = "worn_" .. structure.variant.name .. "_stone"
            end
            
            -- Apply seasonal overlay
            if visualState.overlay then
                structure.tileLegend.w = structure.tileLegend.w .. "_" .. visualState.overlay.name
            end
        elseif component.type == "WALL" and component.status == "critical" then
            -- Add mystical effects
            structure.effects = {
                "fading_runes",
                "energy_drain",
                "ground_corruption"
            }
        end
    end
    
    -- Check structural integrity
    local centralStone = structure.components["central_stone"]
    if centralStone and centralStone.status == "destroyed" then
        -- Structure is unstable
        structure.condition = structure.condition * 0.5
    end
end

-- Repair the structure
function StandingStoneStructure.repair(structure, world, repairType)
    if repairType == "cosmetic" then
        -- Cosmetic repairs (can be done by mystics)
        for component, damage in pairs(structure.cosmeticDamage) do
            structure.cosmeticDamage[component] = math.max(0.0, damage - 0.2)
        end
    elseif repairType == "structural" then
        -- Structural repairs (requires more effort)
        structure.lastAlignment = structure.age
        structure.mysticalPower = math.min(1.0, structure.mysticalPower + 0.3)
        
        -- Repair components
        for componentId, component in pairs(structure.components) do
            if component.status ~= "destroyed" then
                component.health = math.min(component.maxHealth, component.health + (component.maxHealth * 0.3))
            end
        end
    end
end

-- Draw the structure
function StandingStoneStructure.draw(structure, world, x, y)
    -- Draw base structure
    for i = 1, #structure.tileLayout do
        for j = 1, #structure.tileLayout[i] do
            local tile = structure.tileLayout[i][j]
            if tile ~= "." then
                local tileName = structure.tileLegend[tile]
                if tileName then
                    -- Draw base tile
                    world.drawTile(tileName, x + j - 1, y + i - 1)
                    
                    -- Draw seasonal overlay if present
                    local component = structure.components["central_stone"]
                    if component then
                        local visualState = StructureComponents.getVisualState(component)
                        if visualState.overlay then
                            world.drawTileOverlay(
                                visualState.overlay.name,
                                x + j - 1,
                                y + i - 1,
                                visualState.overlay.modifiers
                            )
                        end
                    end
                    
                    -- Draw mystical effects if present
                    if structure.effects then
                        for _, effect in ipairs(structure.effects) do
                            world.drawEffect(effect, x + j - 1, y + i - 1)
                        end
                    end
                end
            end
        end
    end
end

return StandingStoneStructure 