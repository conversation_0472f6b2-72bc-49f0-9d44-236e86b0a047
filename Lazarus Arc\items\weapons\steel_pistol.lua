local SteelPistol = {
    id = "steel_pistol",
    name = "Steel Pistol",
    type = "weapon",
    category = "gun",
    
    -- Properties
    properties = {
        damage = 32,
        attackSpeed = 0.8,
        range = 8.0,
        durability = 200,
        maxDurability = 200,
        bulletSpeed = 20,
        bulletSpread = 0.12,
        reloadTime = 1.2,
        criticalChance = 0.22,
        criticalMultiplier = 1.6,
        armorPenetration = 0.45,
        weight = 1.4,
        momentum = 0,
        maxMomentum = 3,
        momentumPower = 1.3,
        ammoType = "steel_bullet",
        maxAmmo = 6,
        recoil = 0.15
    },
    
    -- Appearance
    appearance = {
        sprite = "steel_pistol",
        scale = 1.2,
        animations = {
            "idle",
            "shoot",
            "reload",
            "momentum"
        },
        variants = {
            "steel",
            "iron",
            "bronze",
            "copper"
        },
        blendMode = "normal",
        tint = {0.8, 0.8, 0.8},
        alpha = 1.0
    },
    
    -- Sound effects
    sounds = {
        shoot = "pistol_shoot",
        reload = "pistol_reload",
        empty = "pistol_empty",
        momentum = "pistol_momentum"
    },
    
    -- Effects
    effects = {
        bullet = {
            type = "projectile",
            speed = 20,
            lifetime = 0.6,
            effects = {
                damage = 32,
                pierce = true,
                bleed = 0.3,
                armorPenetration = 0.45
            }
        },
        critical = {
            type = "status",
            duration = 0.3,
            effects = {
                damage = 1.6
            }
        },
        momentum = {
            type = "status",
            duration = 0.4,
            effects = {
                damage = 1.3
            }
        }
    }
}

-- Initialize the weapon
function SteelPistol.init(weapon, world)
    -- Copy all fields from SteelPistol template to weapon instance
    for k, v in pairs(SteelPistol) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        momentum = 0,
        lastMomentumTime = 0,
        currentAmmo = weapon.properties.maxAmmo,
        isReloading = false,
        reloadStartTime = 0
    }

    return weapon
end

-- Update the weapon
function SteelPistol.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Decay momentum
        if world.time - weapon.properties.weaponState.lastMomentumTime > 1.5 then
            weapon.properties.weaponState.momentum = 0
        end
        
        -- Check if reload is complete
        if weapon.properties.weaponState.isReloading then
            if world.time - weapon.properties.weaponState.reloadStartTime >= weapon.properties.reloadTime then
                -- Finish reload
                weapon.properties.weaponState.currentAmmo = weapon.properties.maxAmmo
                weapon.properties.weaponState.isReloading = false
                
                -- Play reload sound
                if world.playSound then
                    world.playSound(weapon.sounds.reload)
                end
            end
        end
    end
end

-- Handle attack (shoot)
function SteelPistol.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Check ammo
    if weapon.properties.weaponState.currentAmmo <= 0 then
        -- Play empty sound
        if world.playSound then
            world.playSound(weapon.sounds.empty)
        end
        return false
    end
    
    -- Calculate shot direction
    local angle = math.atan2(
        target.position.y - attacker.position.y,
        target.position.x - attacker.position.x
    )
    
    -- Add spread and recoil to angle
    angle = angle + (math.random() - 0.5) * weapon.properties.bulletSpread
    angle = angle + (math.random() - 0.5) * weapon.properties.recoil
    
    -- Create shoot effect
    if world.createEffect then
        world.createEffect({
            type = "shoot",
            position = attacker.position,
            angle = angle,
            range = weapon.properties.range,
            duration = 0.3
        })
    end
    
    -- Create bullet projectile
    if world.createProjectile then
        world.createProjectile({
            type = "bullet",
            position = attacker.position,
            angle = angle,
            speed = weapon.properties.bulletSpeed,
            lifetime = weapon.effects.bullet.lifetime,
            effects = weapon.effects.bullet.effects
        })
    end
    
    -- Check for critical hit
    if math.random() < weapon.properties.criticalChance then
        -- Create critical effect
        if world.createEffect then
            world.createEffect({
                type = "critical",
                position = target.position,
                duration = 0.3
            })
        end
        
        -- Apply critical effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.critical)
        end
    end
    
    -- Update momentum
    if world.time - weapon.properties.weaponState.lastMomentumTime <= 1.5 then
        weapon.properties.weaponState.momentum = math.min(weapon.properties.maxMomentum,
            weapon.properties.weaponState.momentum + 1)
    else
        weapon.properties.weaponState.momentum = 1
    end
    weapon.properties.weaponState.lastMomentumTime = world.time
    
    -- Apply momentum effect if active
    if weapon.properties.weaponState.momentum > 1 then
        if target.applyEffect then
            target.applyEffect(weapon.effects.momentum)
        end
        
        -- Play momentum sound
        if world.playSound then
            world.playSound(weapon.sounds.momentum)
        end
    end
    
    -- Play shoot sound
    if world.playSound then
        world.playSound(weapon.sounds.shoot)
    end
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce ammo
    weapon.properties.weaponState.currentAmmo = weapon.properties.weaponState.currentAmmo - 1
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack (fan shot)
function SteelPistol.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough momentum
    if weapon.properties.weaponState.momentum < 2 then
        return false
    end
    
    -- Check ammo
    if weapon.properties.weaponState.currentAmmo < 3 then
        return false
    end
    
    -- Create fan shot effect
    if world.createEffect then
        world.createEffect({
            type = "fan_shot",
            position = attacker.position,
            angle = math.atan2(
                target.position.y - attacker.position.y,
                target.position.x - attacker.position.x
            ),
            range = weapon.properties.range * 1.2,
            duration = 0.6
        })
    end
    
    -- Fire multiple bullets in a fan pattern
    for i = -1, 1 do
        local angle = math.atan2(
            target.position.y - attacker.position.y,
            target.position.x - attacker.position.x
        ) + i * 0.2
        
        if world.createProjectile then
            world.createProjectile({
                type = "bullet",
                position = attacker.position,
                angle = angle,
                speed = weapon.properties.bulletSpeed * 1.1,
                lifetime = weapon.effects.bullet.lifetime,
                effects = {
                    damage = weapon.effects.bullet.effects.damage * 0.8,
                    pierce = true,
                    bleed = weapon.effects.bullet.effects.bleed * 0.8,
                    armorPenetration = weapon.effects.bullet.effects.armorPenetration * 0.8
                }
            })
        end
    end
    
    -- Reset momentum
    weapon.properties.weaponState.momentum = 0
    
    -- Reduce ammo
    weapon.properties.weaponState.currentAmmo = weapon.properties.weaponState.currentAmmo - 3
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 2)
    
    return true
end

-- Handle reload
function SteelPistol.reload(weapon, world)
    if weapon.properties.weaponState.isReloading then
        return false
    end
    
    -- Start reload
    weapon.properties.weaponState.isReloading = true
    weapon.properties.weaponState.reloadStartTime = world.time
    
    return true
end

return SteelPistol 