-- tiles/mud.lua
local MudTile = {
    id = "mud",
    name = "Mud",
    passable = true, -- Passable, but very difficult
    variants = 5, -- e.g., Shallow Mud, Deep Mire, Drying Mud, Soggy Earth, Peat Bog

    -- Entities that spawn here
    spawns = {"mud_crab", "leech", "mosquito_swarm", "frog", "reeds", "lost_boot", "stuck_cart_wheel"}, -- Creatures and items associated with mud

    -- Movement properties
    movementSpeed = 0.4, -- Very slow movement speed

    -- Mud-specific properties
    depth = 0.8, -- Average depth, affects stickiness/difficulty (0-1+)
    stickiness = 0.6, -- Chance to impede movement further (0-1)
    moistureLevel = 0.9, -- Mud is typically very wet (0-1)
    preservesTracks = true, -- Tracks are easily visible

    -- Interaction function
    interact = function(tile, entity)
        -- Search the mud
        if math.random() < 0.25 then
            local stuckItems = {
                {id="lost_coin", chance=0.3},
                {id="old_leather_boot", chance=0.25},
                {id="broken_tool_handle", chance=0.2},
                {id="waterlogged_pouch", chance=0.15}, -- Could contain minor random item
                {id="fossilized_leaf", chance=0.1}
            }
             for _, item in ipairs(stuckItems) do
                 if math.random() < item.chance then
                     return {
                         success = true,
                         message = "Sifting through the thick mud, you find a " .. item.id .. ".",
                         effects = {{type="add_item", item=item.id, quantity=1}}
                     }
                 end
             end
        end

        -- Examine tracks
        if tile.preservesTracks and entity.skills and entity.skills.tracking > 1 then
             -- Placeholder: In a real system, this would check for actual tracks left by NPCs/creatures
             local tracksFound = {"deer_tracks_recent", "humanoid_bootprint", "unknown_creature_drag_marks"}
             local foundTrack = tracksFound[math.random(#tracksFound)]
             return {
                 success = true,
                 message = "The mud clearly shows tracks: " .. foundTrack .. ".",
                 effects = {
                     {type="gain_tracking_clue", clue=foundTrack, location={tile.x, tile.y}}
                 }
             }
        end

        -- General examination
        local depthDesc = "The mud seems thick and deep."
        if tile.depth < 0.5 then depthDesc = "It looks like shallow, sticky mud." end
        return {
            success = false,
            message = "A patch of thick, sucking mud. " .. depthDesc .. " Difficult to traverse."
        }
    end,

    -- Weather effects
    weatherEffects = {
        rain = function(tile)
            tile.moistureLevel = 1.0
            tile.depth = math.min(1.5, tile.depth + 0.3) -- Rain deepens mud
            tile.stickiness = math.min(0.9, tile.stickiness + 0.2)
            tile.movementSpeed = math.max(0.2, tile.movementSpeed - 0.1) -- Even slower
            return "visual_effect", "mud_puddles"
        end,
        
        heat = function(tile) -- Drying effect
            tile.moistureLevel = math.max(0, tile.moistureLevel - 0.3)
            tile.depth = math.max(0.1, tile.depth - 0.1)
            tile.stickiness = math.max(0.1, tile.stickiness - 0.2)
            if tile.moistureLevel < 0.2 then
                 tile.movementSpeed = 0.7 -- Faster as it dries
                 if tile.moistureLevel == 0 then
                      -- Transform into dirt or cracked earth
                      return "transform", "cracked_earth" 
                 end
                 return "visual_effect", "drying_mud_cracks"
            end
            return nil
        end,

        sun = function(tile) -- Slower drying than direct heat
            tile.moistureLevel = math.max(0, tile.moistureLevel - 0.15)
            tile.depth = math.max(0.1, tile.depth - 0.05)
            tile.stickiness = math.max(0.1, tile.stickiness - 0.1)
             if tile.moistureLevel < 0.2 then
                 tile.movementSpeed = 0.7
                 if tile.moistureLevel == 0 then
                      return "transform", "dirt" -- More likely transforms to regular dirt
                 end
                 return "visual_effect", "drying_mud_cracks"
            end
             -- Remove puddle effect if it exists
             return "remove_visual_effect", "mud_puddles"
        end,

        freeze = function(tile) -- Freezing mud
             tile.isFrozen = true
             tile.movementSpeed = 0.8 -- Hard but uneven/slippery
             tile.stickiness = 0 -- Not sticky when frozen
             tile.footstepSound = "frozen_mud_crunch"
             return "visual_effect", "frozen_mud"
        end
    },

     -- Time effects
    timeEffects = {
        night = function(tile)
            tile.ambientSound = "mud_bubbles_insects"
            -- Increase spawn chance for nocturnal insects/amphibians
            return nil
        end,
        
        dawn = function(tile)
             tile.ambientSound = "mud_bubbles_gentle"
             -- Reset freeze effect if temp rises
             if tile.isFrozen and world.temperature > 0 then
                  tile.isFrozen = false
                  tile.movementSpeed = 0.4 -- Back to slow speed
                  tile.stickiness = 0.6 -- Reset stickiness
                  tile.footstepSound = "mud_squelch"
                  return "remove_visual_effect", "frozen_mud"
             end
            return nil
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {0.4, 0.3, 0.2}, -- Dark brown
        variants = {
            {name = "Shallow Mud", depth_mod=0.5, color_shift={0.1,0.05,0}},
            {name = "Deep Mire", depth_mod=1.5, color_shift={-0.1,-0.1,-0.1}, stickiness_mod=1.3},
            {name = "Drying Mud", moisture_level=0.3, texture="cracked_mud_light"},
            {name = "Soggy Earth", texture="muddy_grass", base_color={0.3, 0.35, 0.2}}, -- Mix with grass
            {name = "Peat Bog", base_color={0.2, 0.15, 0.1}, overlay_objects={{name="rotting_log", chance=0.3}}}
        },
        decoration_objects = {
            {name = "reeds", chance = 0.3},
            {name = "mud_bubble_effect", chance = 0.4}, -- Animated effect
            {name = "animal_track_decal", chance = 0.5, condition = function(tile) return tile.preservesTracks end},
            {name = "broken_branch", chance = 0.2}
        },
         special_effects = {
             wet_sheen = {intensity=0.6, when="moistureLevel > 0.5"},
             cracks = {intensity_scale = function(tile) return 1 - tile.moistureLevel end, when="moistureLevel < 0.4"}
         },
         weather_overlays = {
             puddles = {effect = "reflective_shallow", when = "has_puddles"}, -- Assuming rain sets has_puddles flag
             frozen_sheen = {shininess=0.5, when="isFrozen"}
         }
    },
    
    -- Audio properties
    ambientSound = "mud_bubbles_gentle",
    footstepSound = "mud_squelch", -- Very distinct sound
     specialSounds = {
        {name = "insect_buzz_heavy", trigger = "entity_nearby", chance = 0.2},
        {name = "mud_suck_deep", trigger = "on_walk", chance = function(tile) return 0.1 * tile.stickiness end}, -- Sound if potentially stuck
        {name = "frog_croak", trigger = "random", chance = 0.1}
    },

    -- Connections to other tiles
    connects_to = {
        "mud", -- Itself
        "dirt", -- Common source/result
        "grass", -- Can border grassy areas (Soggy Earth)
        "shallow_water", -- Common border
        "water", -- Riverbanks, lake shores
        "sand", -- Sandy mud
        "road", -- Especially dirt roads after rain [cite: 23]
        "swamp" -- Leads into swampy areas
    },

    -- Pathfinding properties
     pathfinding = {
        travel_cost = 2.5, -- Very high cost
        hazard_chance = function(tile) return 0.15 * tile.stickiness end, -- Chance to trigger 'stuck' effect
        reduces_stealth = true -- Hard to move quietly
    },

    -- Effects on entities that walk on this tile
    onWalk = function(tile, entity)
        local fatigue = 0.3 * (1 + tile.depth) -- High fatigue cost, scaled by depth
        local sound = tile.footstepSound or "mud_squelch"
        local effects = {{type="increase_fatigue", amount = fatigue}}

        -- Chance to get stuck
        if math.random() < 0.15 * tile.stickiness * tile.depth then
             sound = "mud_suck_deep"
             table.insert(effects, {type="apply_status", status="stuck_in_mud", duration=3, magnitude=tile.depth}) -- Stuck effect, magnitude relates to depth
             return {
                 sound = sound,
                 volume = 1.0,
                 message = "Your feet sink deep into the mud, holding you fast!",
                 effects = effects
             }
        end
        
        -- Apply 'muddy' status?
        table.insert(effects, {type="apply_status", status="muddy", duration=60})

        return {
            sound = sound,
            volume = 1.0,
            effects = effects
        }
    end,
    
    -- Effect when discovering mud
    onDiscover = function(tile, entity)
        if entity.journal and entity.journal.addDiscovery then
             entity.journal.addDiscovery({
                type = "terrain",
                name = "Mud",
                location = {x = tile.x, y = tile.y},
                notes = "Thick, difficult mud. Slows movement significantly."
            })
        end
        return {
            message = "You've encountered a patch of thick mud.",
            effects = {
                {type = "reveal_map", radius = 1} -- Usually small patches, reveal less
            }
        }
    end
}

function MudTile.init(world)
    print("Mud tile module initialized")
    -- Register with relevant systems
    if world.systems and world.systems.pathfinding then
        world.systems.pathfinding:registerTerrainHazard("mud", {cost=2.5, stuck_chance=0.1})
    end
     if world.systems and world.systems.environment then
        world.systems.environment:registerTrackableSurface("mud")
    end
end

return MudTile