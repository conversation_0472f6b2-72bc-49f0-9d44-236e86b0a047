local Cliff = {
    id = "cliff",
    name = "Cliff",
    type = "tile",
    
    -- Categories
    categories = {"terrain", "cliff", "mountain", "vertical"},
    
    -- Properties
    properties = {
        walkable = false,
        swimable = false,
        climbable = true,
        flyable = true,
        diggable = true,
        buildable = false,
        flammable = false,
        height = 2.0,
        steepness = 0.9,
        stability = 0.8,
        erosion = 0.1,
        rockType = "stone",
        mossChance = 0.3,
        mossGrowth = 0.1,
        mossColor = {0.3, 0.5, 0.2},
        ledgeChance = 0.2,
        ledgeWidth = 0.5,
        ledgeDepth = 0.3
    },
    
    -- Appearance
    appearance = {
        sprite = "cliff",
        scale = 1.0,
        animations = {
            "idle",
            "erosion",
            "moss"
        },
        variants = {
            "stone",
            "granite",
            "limestone",
            "sandstone"
        },
        blendMode = "normal",
        tint = {0.7, 0.7, 0.7},
        alpha = 1.0
    },
    
    -- Sound effects
    sounds = {
        climb = "cliff_climb",
        dig = "stone_dig",
        collapse = "cliff_collapse",
        wind = "cliff_wind"
    },
    
    -- Resources
    resources = {
        stone = {
            type = "mineral",
            amount = 5,
            harvestable = true,
            harvestAmount = 1,
            harvestTool = "pickaxe"
        },
        moss = {
            type = "plant",
            amount = 1,
            regrowthRate = 0.1,
            regrowthTime = 600,
            harvestable = true,
            harvestAmount = 1,
            harvestTool = "hand"
        }
    },
    
    -- Effects
    effects = {
        wind = {
            type = "environment",
            duration = 0,
            effects = {
                windStrength = 0.8,
                windDirection = {x = 0, y = 1}
            }
        },
        erosion = {
            type = "environment",
            duration = 0,
            effects = {
                erosionRate = 0.1,
                particleEffect = "dust"
            }
        }
    },
    
    -- Spawn rules
    spawnRules = {
        plants = {
            "cliff_grass",
            "cliff_flower",
            "cliff_moss",
            "cliff_vine"
        },
        structures = {
            "rock_formation",
            "cave_entrance",
            "bird_nest"
        }
    }
}

-- Initialize the tile
function Cliff.init(tile, world)
    -- Initialize cliff state first
    tile.properties = tile.properties or {}
    tile.properties.cliffState = {
        hasMoss = math.random() < (Cliff.properties.mossChance or 0.3),
        mossAmount = 0,
        hasLedge = math.random() < (Cliff.properties.ledgeChance or 0.2),
        erosionAmount = 0,
        stability = Cliff.properties.stability or 0.8
    }

    -- Copy all fields from Cliff template to tile instance
    for k, v in pairs(Cliff) do
        if type(v) ~= "function" and tile[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                tile[k] = {}
                for subk, subv in pairs(v) do
                    tile[k][subk] = subv
                end
            else
                tile[k] = v
            end
        end
    end

    -- Set random variant
    if tile.appearance and tile.appearance.variants then
        local variants = tile.appearance.variants
        tile.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize resources
    if tile.resources then
        for resourceType, resource in pairs(tile.resources) do
            resource.currentAmount = resource.amount
            resource.lastHarvest = 0
            resource.growthStage = "mature"
        end
    end

    return tile
end

-- Update the tile
function Cliff.update(tile, world, dt)
    -- Update cliff state
    if tile.properties.cliffState then
        -- Update moss growth
        if tile.properties.cliffState.hasMoss then
            tile.properties.cliffState.mossAmount = math.min(1.0, 
                tile.properties.cliffState.mossAmount + tile.properties.mossGrowth * dt)
        end
        
        -- Update erosion
        if world.wind and world.wind.strength and world.wind.strength > 0.5 then
            tile.properties.cliffState.erosionAmount = math.min(1.0,
                tile.properties.cliffState.erosionAmount + tile.properties.erosion * dt)
            
            -- Check for cliff collapse
            if tile.properties.cliffState.erosionAmount > 0.8 and 
               tile.properties.cliffState.stability < 0.3 then
                -- Create rockfall effect
                if world.createEffect then
                    world.createEffect({
                        type = "rockfall",
                        position = tile.position,
                        amount = 3
                    })
                end
                
                -- Play collapse sound
                if world.playSound then
                    world.playSound(tile.sounds.collapse)
                end
                
                -- Convert to rubble tile
                if world.setTile then
                    world.setTile(tile.position, "rubble")
                end
            end
        end
        
        -- Update stability based on erosion
        tile.properties.cliffState.stability = math.max(0,
            tile.properties.stability - tile.properties.cliffState.erosionAmount * 0.1)
    end
    
    -- Update resources
    if tile.resources then
        for resourceType, resource in pairs(tile.resources) do
            if resource.currentAmount < resource.amount then
                -- Regrow resources
                local regrowthAmount = resource.regrowthRate * dt
                resource.currentAmount = math.min(resource.amount, resource.currentAmount + regrowthAmount)
                
                -- Update growth stage
                if resource.currentAmount >= resource.amount then
                    resource.growthStage = "mature"
                elseif resource.currentAmount >= resource.amount * 0.5 then
                    resource.growthStage = "growing"
                else
                    resource.growthStage = "young"
                end
            end
        end
    end
end

-- Handle interaction
function Cliff.interact(tile, world, entity, action)
    if action == "climb" then
        -- Handle climbing
        if tile.properties.climbable then
            -- Check if entity has climbing ability
            if entity.canClimb then
                -- Move entity up
                if entity.move then
                    entity.move({x = 0, y = -tile.properties.height})
                end
                
                -- Play climb sound
                if world.playSound then
                    world.playSound(tile.sounds.climb)
                end
            end
        end
    elseif action == "dig" then
        -- Handle digging
        if tile.properties.diggable then
            -- Create stone item
            if world.createItem then
                world.createItem({
                    type = "stone",
                    amount = 1,
                    position = tile.position
                })
            end
            
            -- Play dig sound
            if world.playSound then
                world.playSound(tile.sounds.dig)
            end
            
            -- Convert to dirt tile
            if world.setTile then
                world.setTile(tile.position, "dirt")
            end
        end
    elseif action == "harvest" then
        -- Handle resource harvesting
        if tile.resources then
            for resourceType, resource in pairs(tile.resources) do
                if resource.harvestable and resource.currentAmount > 0 then
                    -- Create harvested item
                    if world.createItem then
                        world.createItem({
                            type = resourceType,
                            amount = resource.harvestAmount,
                            position = tile.position
                        })
                    end
                    
                    -- Update resource amount
                    resource.currentAmount = math.max(0, resource.currentAmount - resource.harvestAmount)
                    resource.lastHarvest = world.time
                    resource.growthStage = "young"
                end
            end
        end
    end
end

return Cliff 