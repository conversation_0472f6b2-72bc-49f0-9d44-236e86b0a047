-- test_weather_fixes.lua
-- Test script to verify weather system fixes

print("=== Testing Weather System Fixes ===")

-- Mock love framework for testing
love = love or {}
love.graphics = love.graphics or {}
love.graphics.newCanvas = function() return {} end
love.graphics.setCanvas = function() end
love.graphics.clear = function() end
love.graphics.setColor = function() end
love.graphics.rectangle = function() end
love.graphics.circle = function() end
love.graphics.newParticleSystem = function() return {
    setParticleLifetime = function() end,
    setEmissionRate = function() end,
    setSizeVariation = function() end,
    setLinearAcceleration = function() end,
    setColors = function() end,
    setBlendMode = function() end,
    update = function() end,
    start = function() end,
    stop = function() end
} end

-- Test 1: Load rain weather and check for cleanUp function
print("\n--- Test 1: Rain Weather cleanUp Function ---")
local success, WeatherRain = pcall(require, "weather.weather_rain")
if success then
    print("✓ Rain weather loaded successfully")
    
    -- Check if cleanUp function exists
    if WeatherRain.cleanUp then
        print("✓ cleanUp function exists (static)")
    else
        print("✗ cleanUp function missing (static)")
    end
    
    -- Test instance creation and cleanUp
    local rainInstance = WeatherRain.new()
    if rainInstance.cleanUp then
        print("✓ cleanUp function exists (instance)")
        
        -- Test calling cleanUp
        local testSuccess, result = pcall(rainInstance.cleanUp, rainInstance, {})
        if testSuccess then
            print("✓ cleanUp function callable")
        else
            print("✗ cleanUp function error: " .. tostring(result))
        end
    else
        print("✗ cleanUp function missing (instance)")
    end
    
    -- Check synth sounds
    if WeatherRain.synthSounds then
        print("✓ Synth sounds configuration exists")
        local soundCount = 0
        for name, config in pairs(WeatherRain.synthSounds) do
            soundCount = soundCount + 1
            print("  - " .. name .. " (" .. (config.instrument or "unknown") .. ")")
        end
        print("  Total synth sounds: " .. soundCount)
    else
        print("✗ Synth sounds configuration missing")
    end
else
    print("✗ Failed to load rain weather: " .. tostring(WeatherRain))
end

-- Test 2: Load volcanic weather and check calling convention
print("\n--- Test 2: Volcanic Weather Calling Convention ---")
local success, VolcanicWeather = pcall(require, "weather.weather_volcanic")
if success then
    print("✓ Volcanic weather loaded successfully")
    
    -- Test static function versions
    if VolcanicWeather.init and VolcanicWeather.update and VolcanicWeather.cleanUp then
        print("✓ Static function versions exist")
        
        -- Test calling init with world parameter
        local mockWorld = { lightLevel = 1.0 }
        local testSuccess, result = pcall(VolcanicWeather.init, mockWorld)
        if testSuccess then
            print("✓ Static init function callable")
        else
            print("✗ Static init function error: " .. tostring(result))
        end
    else
        print("✗ Static function versions missing")
    end
    
    -- Check synth sounds
    if VolcanicWeather.synthSounds then
        print("✓ Synth sounds configuration exists")
        local soundCount = 0
        for name, config in pairs(VolcanicWeather.synthSounds) do
            soundCount = soundCount + 1
            print("  - " .. name .. " (" .. (config.instrument or "unknown") .. ")")
        end
        print("  Total synth sounds: " .. soundCount)
    else
        print("✗ Synth sounds configuration missing")
    end
else
    print("✗ Failed to load volcanic weather: " .. tostring(VolcanicWeather))
end

-- Test 3: Load weather system and check synth orchestra integration
print("\n--- Test 3: Weather System Synth Orchestra Integration ---")
local success, WeatherSystem = pcall(require, "utils.weather_system")
if success then
    print("✓ Weather system loaded successfully")
    
    -- Check if playSound function exists
    if WeatherSystem.playSound then
        print("✓ playSound function exists")
        
        -- Mock synth orchestra
        package.loaded["utils.synth_orchestra"] = {
            initialized = true,
            generateFromConfig = function(config)
                if config and config.notes then
                    return {
                        setVolume = function() end,
                        setLooping = function() end,
                        play = function() end,
                        stop = function() end
                    }
                end
                return nil
            end
        }
        
        -- Mock current weather with synth sounds
        WeatherSystem.currentWeather = {
            synthSounds = {
                test_sound = {
                    notes = {440, 523, 659},
                    durations = {1.0, 1.0, 1.0},
                    instrument = "upright_piano",
                    volume = 0.5
                }
            }
        }
        
        -- Test playing a sound
        local testSuccess, result = pcall(WeatherSystem.playSound, "test_sound", { verbose = false })
        if testSuccess and result then
            print("✓ playSound with synth orchestra works")
        else
            print("✗ playSound failed: " .. tostring(result))
        end
    else
        print("✗ playSound function missing")
    end
else
    print("✗ Failed to load weather system: " .. tostring(WeatherSystem))
end

print("\n=== Weather System Fixes Test Complete ===")
