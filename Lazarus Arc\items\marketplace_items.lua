-- items/marketplace_items.lua
-- Marketplace-related items for trade, goods, and valuables

local MarketplaceItems = {
    -- Common Items
    coin = {
        id = "coin",
        name = "Coin",
        category = "currency",
        description = "A standard coin used for trade",
        value = 1,
        weight = 0.1,
        stackable = true,
        maxStack = 999,
        properties = {
            material = "gold",
            quality = 1.0,
            durability = 100
        },
        onUse = function(user, target)
            -- Can be used for trading
            return true
        end
    },
    
    goods = {
        id = "goods",
        name = "Trade Goods",
        category = "trade",
        description = "Various items for trade",
        value = 5,
        weight = 1.0,
        stackable = true,
        maxStack = 20,
        properties = {
            material = "various",
            quality = 1.0,
            durability = 100
        },
        onUse = function(user, target)
            -- Can be traded
            return true
        end
    },
    
    food = {
        id = "food",
        name = "Food",
        category = "food",
        description = "Fresh food for sale",
        value = 3,
        weight = 1.0,
        stackable = true,
        maxStack = 15,
        properties = {
            material = "food",
            quality = 1.0,
            durability = 50,
            effect = "satiate"
        },
        onUse = function(user, target)
            -- Restores some health
            return true
        end
    },
    
    clothing = {
        id = "clothing",
        name = "Clothing",
        category = "apparel",
        description = "Basic clothing for sale",
        value = 4,
        weight = 1.0,
        stackable = true,
        maxStack = 10,
        properties = {
            material = "fabric",
            quality = 1.0,
            durability = 100
        },
        onUse = function(user, target)
            -- Can be worn
            return true
        end
    },
    
    -- Uncommon Items
    rare_coin = {
        id = "rare_coin",
        name = "Rare Coin",
        category = "currency",
        description = "A valuable rare coin",
        value = 50,
        weight = 0.1,
        stackable = true,
        maxStack = 10,
        properties = {
            material = "gold",
            quality = 2.0,
            durability = 200
        },
        onUse = function(user, target)
            -- Can be used for high-value trading
            return true
        end
    },
    
    exotic_goods = {
        id = "exotic_goods",
        name = "Exotic Goods",
        category = "trade",
        description = "Valuable items from distant lands",
        value = 100,
        weight = 1.0,
        stackable = true,
        maxStack = 5,
        properties = {
            material = "various",
            quality = 2.0,
            durability = 150
        },
        onUse = function(user, target)
            -- Can be traded for high value
            return true
        end
    },
    
    fine_food = {
        id = "fine_food",
        name = "Fine Food",
        category = "food",
        description = "High-quality food for sale",
        value = 20,
        weight = 1.0,
        stackable = true,
        maxStack = 5,
        properties = {
            material = "food",
            quality = 2.0,
            durability = 75,
            effect = "satiate_enhanced"
        },
        onUse = function(user, target)
            -- Restores more health
            return true
        end
    },
    
    luxury_clothing = {
        id = "luxury_clothing",
        name = "Luxury Clothing",
        category = "apparel",
        description = "Expensive and fashionable clothing",
        value = 75,
        weight = 1.0,
        stackable = true,
        maxStack = 5,
        properties = {
            material = "fine_fabric",
            quality = 2.0,
            durability = 150
        },
        onUse = function(user, target)
            -- Can be worn with status effect
            return true
        end
    },
    
    -- Rare Items
    legendary_coin = {
        id = "legendary_coin",
        name = "Legendary Coin",
        category = "currency",
        description = "A legendary coin of immense value",
        value = 500,
        weight = 0.1,
        stackable = true,
        maxStack = 3,
        properties = {
            material = "legendary_gold",
            quality = 3.0,
            durability = 500,
            magical = true
        },
        onUse = function(user, target)
            -- Can be used for legendary trading
            return true
        end
    },
    
    ancient_goods = {
        id = "ancient_goods",
        name = "Ancient Goods",
        category = "trade",
        description = "Valuable items from ancient times",
        value = 300,
        weight = 1.0,
        stackable = true,
        maxStack = 3,
        properties = {
            material = "ancient",
            quality = 2.5,
            durability = 300
        },
        onUse = function(user, target)
            -- Can be traded for immense value
            return true
        end
    },
    
    royal_food = {
        id = "royal_food",
        name = "Royal Food",
        category = "food",
        description = "Food fit for royalty",
        value = 100,
        weight = 1.0,
        stackable = true,
        maxStack = 3,
        properties = {
            material = "food",
            quality = 3.0,
            durability = 100,
            effect = "satiate_legendary",
            magical = true
        },
        onUse = function(user, target)
            -- Restores significant health and provides buffs
            return true
        end
    },
    
    royal_clothing = {
        id = "royal_clothing",
        name = "Royal Clothing",
        category = "apparel",
        description = "Clothing fit for royalty",
        value = 200,
        weight = 1.0,
        stackable = true,
        maxStack = 3,
        properties = {
            material = "royal_fabric",
            quality = 3.0,
            durability = 300,
            magical = true
        },
        onUse = function(user, target)
            -- Can be worn with powerful status effects
            return true
        end
    }
}

return MarketplaceItems 