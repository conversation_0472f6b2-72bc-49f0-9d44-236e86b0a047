-- tiles/oasis.lua
local OasisTile = {
    id = "oasis",
    name = "Oasis",
    passable = true, -- Generally passable, though may contain water elements
    variants = 4, -- e.g., Small Spring, Palm Grove, Rocky Pool, Lush Expanse

    -- Entities that can spawn in an oasis
    spawns = {"desert_fox", "gazelle", "camel_wild", "oasis_bird", "date_palm_tree", "traveling_merchant", "nomad"}, -- Mix of wildlife and potential NPCs

    -- Movement properties
    movementSpeed = 0.95, -- Slightly slower due to vegetation and uneven ground, but better than sand

    -- Oasis-specific properties
    waterSourceQuality = 0.9, -- Quality of the water (0-1)
    vegetationDensity = 0.7, -- How lush the oasis is (0-1)
    temperatureModifier = -10, -- Degrees Celsius cooler than surrounding desert base temp
    shelterValue = 0.6, -- Provides some shelter from weather (0-1)

    -- Interaction function
    interact = function(tile, entity)
        -- Interact with water source (if present nearby or as part of the tile)
        if tile.hasWaterSource and math.random() < 0.8 then
            local waterQualityDesc = "The water looks clear and refreshing."
            if tile.waterSourceQuality < 0.5 then
                 waterQualityDesc = "The water looks a bit stagnant, but drinkable."
            end
            
            -- Option to drink or fill container [Inspired by water.txt: 195-206]
            if entity.needs and entity.needs.hydration < 80 then
                 return {
                    success = true,
                    message = waterQualityDesc .. " You drink deeply, replenishing your thirst.",
                    effects = {
                        {type = "restore_hydration", amount = 40 * tile.waterSourceQuality},
                        {type = "restore_stamina", amount = 10} -- Refreshing effect
                    }
                }
            elseif entity.hasItem and entity.hasItem("water_container_empty") then
                 return {
                     success = true,
                     message = waterQualityDesc .. " You fill your container with water.",
                     effects = {
                         {type = "fill_container", item = "water_container_empty", becomes = "water_container_filled", quality = tile.waterSourceQuality}
                     }
                 }
            else
                 return {
                    success = false,
                    message = waterQualityDesc
                 }
            end
        end

        -- Chance to find edible plants/fruit
        if tile.vegetationDensity > 0.5 and math.random() < 0.25 then
             local plants = {
                {id = "desert_dates", chance = 0.6},
                {id = "oasis_berries", chance = 0.3},
                {id = "medicinal_herb_soothing", chance = 0.1}
             }
             for _, plant in ipairs(plants) do
                 if math.random() < plant.chance then
                     return {
                         success = true,
                         message = "You find some edible " .. plant.id .. " growing here.",
                         effects = {
                             {type = "add_item", item = plant.id, quantity = math.random(1, 3)}
                         }
                     }
                 end
             end
        end

        -- Resting effect
        if entity.status and entity.status.isResting then
             return {
                success = true,
                message = "The relative cool and shade of the oasis make for a restful spot.",
                effects = {
                    {type = "increase_rest_effectiveness", multiplier = 1.5} -- Recover faster here
                }
             }
        end

        -- Standard examination
        local tempDesc = "It feels noticeably cooler here than the surrounding desert."
        local vegDesc = "Lush vegetation provides welcome shade."
        if tile.vegetationDensity < 0.3 then
            vegDesc = "Sparse vegetation clings to life around a water source."
        end
        return {
            success = false,
            message = "A pocket of life in the harsh desert. " .. vegDesc .. " " .. tempDesc
        }
    end,

    -- Weather effects
    weatherEffects = {
        heat = function(tile) -- Less affected by extreme heat
            tile.temperature = (world.temperature or 35) + tile.temperatureModifier + 5 -- Still warmer, but moderated
            tile.dehydrationRisk = 0.2 -- Lower risk than open desert
            return nil
        end,
        
        sun = function(tile) -- Base daytime temp
             tile.temperature = 35 + tile.temperatureModifier
             tile.dehydrationRisk = 0.1
             return nil
        end,

        wind = function(tile) -- Offers shelter from wind
             if world.weather == "sandstorm" then
                tile.visibility = 0.8 -- Better visibility than open desert
                tile.movementSpeed = 0.9 -- Less slowed
                return "provide_shelter", tile.shelterValue
             end
             return nil
        end,

        rain = function(tile) -- Rain benefits the oasis
            tile.waterSourceQuality = math.min(1.0, tile.waterSourceQuality + 0.2)
            tile.vegetationDensity = math.min(1.0, tile.vegetationDensity + 0.1)
            -- Briefly boosts positive effects
            tile.temporaryBoost = { type = "regeneration", value = 1, duration = 12 } -- e.g., minor health regen for 12 turns
            return nil
        end
    },

    -- Time effects
    timeEffects = {
        night = function(tile) -- Still relatively cool
            tile.temperature = 15 + tile.temperatureModifier -- Cool night
            tile.dehydrationRisk = 0.01
            tile.ambientSound = "oasis_night_sounds" -- Different sounds (insects, distant animals)
            -- Higher chance of finding resting animals/NPCs
            return nil
        end,
        
        dawn = function(tile)
             tile.temperature = 25 + tile.temperatureModifier
             tile.ambientSound = "oasis_morning_birds"
             tile.temporaryBoost = nil -- Remove rain boost if present
            return nil
        end
    },

    -- Visual properties
    visual = {
        base_color = {0.4, 0.6, 0.3}, -- Greenish-brown earth tones
        variants = {
            {name = "small_spring", water_feature = "spring", vegetation = "sparse_reeds", color_shift={0.1, 0, -0.1}},
            {name = "palm_grove", water_feature = "pool_small", vegetation = "dense_palms", base_color={0.3, 0.5, 0.2}},
            {name = "rocky_pool", water_feature = "pool_rocky", vegetation = "moderate_shrubs", overlay_objects={{name="medium_rock", chance=0.4}}},
            {name = "lush_expanse", water_feature = "stream", vegetation = "lush_mixed", size_modifier=1.5}
        },
        -- Water visuals could reference shallow_water/water tile properties
        water_visuals = { 
             base_color = {0.2, 0.6, 0.7, 0.8}, -- Clearer water than default shallow
             animation = {type="ripple_gentle", speed=0.5},
             reflective = true
        },
        decoration_objects = {
            {name = "palm_tree", chance = function(tile) return 0.5 * tile.vegetationDensity end},
            {name = "reeds", chance = 0.4},
            {name = "flowering_desert_plant", chance = 0.2},
            {name = "smooth_stones", chance = 0.3}
        },
         special_effects = {
             mist_morning = {type="particle", density=0.2, color={0.8, 0.8, 0.9}, when="dawn"},
             fireflies_night = {type="particle_emitter", particle="light_speck", color={0.8, 1.0, 0.2}, when="night", density = function(tile) return 0.1 * tile.vegetationDensity end}
        }
    },

    -- Audio properties
    ambientSound = "oasis_gentle_water", -- Default sound
    footstepSound = "dirt_step_damp", -- Sound for walking on damp earth/vegetation
     specialSounds = {
        {name = "bird_chirp", trigger = "daytime", chance = 0.15},
        {name = "frog_croak", trigger = "near_water", chance = 0.1},
        {name = "leaves_rustling", trigger = "windy", chance = 0.4}
    },

    -- Connections to other tiles
    connects_to = {
        "oasis", -- Connects to itself
        "desert_sand", -- Primary connection
        "shallow_water", -- If water feature extends
        "rocky_ground" -- Possible edge transition
    },

    -- Provides resources and safety
    resourceSource = {"water", "food_plant"},
    safeZoneRating = 0.7, -- Reduces chance of hostile encounters (0-1)

    -- Effects on entities that walk on this tile
    onWalk = function(tile, entity)
        -- Apply minor cooling/refreshing effect
        if entity.status and tile.temperature < 30 then
             entity.applyStatus("refreshed", 10, {magnitude=1}) -- Short duration minor buff
        end

        return {
            sound = "dirt_step_damp",
            volume = 0.7,
             effects = { -- Oasis slightly reduces fatigue gain compared to desert sand
                 {type="increase_fatigue", amount = 0.05} 
            }
        }
    end,
     
    -- Effect when discovered
    onDiscover = function(tile, entity)
        local message = "You stumble upon a life-giving oasis, a stark contrast to the barren desert!"
        if entity.needs and entity.needs.hydration < 20 then
            message = "Just as thirst threatened to overwhelm you, a saving oasis appears!"
        end
        
        if entity.journal and entity.journal.addDiscovery then
             entity.journal.addDiscovery({
                type = "location",
                name = "Oasis",
                location = {x = tile.x, y = tile.y},
                notes = "Provides water and shelter in the desert."
            })
        end
        return {
            message = message,
            effects = {
                {type = "restore_hydration", amount = 10}, -- Immediate small relief
                {type = "reveal_map", radius = 4} -- Reveal a slightly larger area
            }
        }
    end
}

function OasisTile.init(world)
    print("Oasis tile module initialized")
    
    -- Register with ecosystem as a vital resource point
    if world.systems and world.systems.ecosystem then
        world.systems.ecosystem:registerResourceHub("oasis", {"water", "food_plant"})
        world.systems.ecosystem:registerSafeZone("oasis", 0.7)
    end
     -- Register with navigation as a potential stop point
    if world.systems and world.systems.navigation then
        world.systems.navigation:registerPointOfInterest("oasis")
    end
end

return OasisTile