-- classes/red_mage.lua
-- Red Mage class definition for Lazarus Arc

local RedMage = {
    name = "Red Mage",
    description = "A versatile spellblade who enhances their melee combat with elemental magic, balancing offense and spellcasting.",
    icon = "red_mage_icon.png",

    -- 🎭 **Playstyle & Trade-offs**
    pros = {
        "Can switch between melee and magic easily",
        "Buffs weapons with elemental magic for extra damage",
        "Excels in adaptability and versatility"
    },
    cons = {
        "Jack of all trades, master of none",
        "Lower raw damage than pure melee or magic classes",
        "Mana-dependent for maximum effectiveness"
    },

    -- 🏋 **Base Stats & Growth**
    base_stats = { 
        health = 100, 
        maxHealth = 100,
        stamina = 70, 
        maxStamina = 70,
        mana = 70, 
        maxMana = 70,
        pattack = 10, 
        pdefense = 6, 
        mattack = 10, 
        mdefense = 7, 
        speed = 6, 
        luck = 5 
    },

    stat_growth = {
        health = 8,
        stamina = 6,
        mana = 7,
        pattack = 1.0,
        pdefense = 0.6,
        mattack = 1.0,
        mdefense = 0.7,
        speed = 0.5,
        luck = 0.4
    },

    -- ⚔️ **Weapon Proficiency**
    proficiencies = { 
        sword = 2.0,  
        dagger = 1.5,  
        staff = 1.5,  
        greatsword = 1.2,  
        bow = 1.0,  
        gun = 0.8,  
        magic = 1.8  -- Strong spellcasting potential
    },

    -- 🌿 **Elemental Affinities**
    element_affinities = {
        fire = 10,   -- High affinity for fire-based sword magic
        air = 10,    -- Lightning-fast reflexes
        water = 5,   -- Can infuse weapons with frost energy
        earth = 5,   -- Defensive magic reinforcement
    },

    -- 💥 **Overdrive Abilities**
    overdrive = { 
        "spellblade_barrage", -- Unleashes a flurry of elemental strikes
        "arcane_edge", -- Supercharges their weapon with raw magical energy
        "final_resonance", -- Releases all stored magic in a devastating blast
    },

    -- 🎯 **Starting Skills**
    starting_skills = {
        "flame_blade",
        "aero_strike",
        "focus"
    },

    -- 📈 **Skill Progression**
    skill_tree = {
        tier1 = {
            "frost_blade",
            "arcane_parry",
            "spell_dodge"
        },
        tier2 = {
            "lightning_surge",
            "mana_slash", 
            "reflect_spell",
            "storm_fury"
        },
        tier3 = {
            "blade_of_the_elements",
            "ether_resonance",
            "meteor_cut"
        }
    },

    -- 🎒 **Inventory & Carrying Capacity**
    inventory_limit = 100, -- Moderate carrying capacity

    -- ✨ **Special Abilities (Passive Perks)**
    special_abilities = {
        "Magic Infusion", -- Can enchant weapons with elemental energy
        "Mana Efficiency", -- Reduced mana cost when buffing weapons
        "Adaptive Combat", -- Can switch between melee and ranged magic easily
        "Spellblade Reflexes", -- Gains a temporary speed boost after casting a spell
        "Arcane Counter", -- Small chance to reflect magic attacks back to the caster
    },

    -- 🎭 **Prestige Paths**
    prestige_paths = {
        spellblade = {
            description = "A master of elemental swordplay, seamlessly blending melee and magic.",
            bonuses = {
                "Stronger weapon enchantments",
                "Can store magic in their weapon for delayed bursts",
                "Faster melee attacks when buffed"
            },
            weaknesses = {
                "Weaker long-range magic",
                "Struggles against pure tanks"
            }
        },

        arcane_duelist = {
            description = "A fast-moving spellblade, specializing in magical sword duels.",
            bonuses = {
                "Stronger counterattacks against enemy magic",
                "Increased attack speed with one-handed weapons",
                "Mana restores faster when in combat"
            },
            weaknesses = {
                "Weaker against multiple opponents",
                "Less defensive power compared to tanks"
            }
        },

        eldritch_knight = {
            description = "A heavily armored magic swordsman, fusing resilience with arcane might.",
            bonuses = {
                "Can cast spells even while wearing heavy armor",
                "Higher resistance to magic attacks",
                "Uses defensive barriers instead of dodging"
            },
            weaknesses = {
                "Less mobility",
                "Higher stamina drain when casting spells"
            }
        }
    },

    -- ⚔️ **Hybrid & Multiclass Options**
    hybrid_options = {
        battle_sorcerer = {
            description = "A Red Mage who fully embraces magical destruction while retaining their swordplay.",
            pros = {
                "Stronger long-range magic",
                "Higher mana pool"
            },
            cons = {
                "Melee damage slightly weaker",
                "More vulnerable to counter-magic"
            }
        },

        mystic_ranger = {
            description = "A Red Mage who blends ranged attacks with elemental magic.",
            pros = {
                "Can enchant arrows and ranged attacks",
                "More mobile than a pure spellcaster"
            },
            cons = {
                "Weaker at close-range combat",
                "Requires mana for maximum effectiveness"
            }
        }
    }
}

return RedMage
