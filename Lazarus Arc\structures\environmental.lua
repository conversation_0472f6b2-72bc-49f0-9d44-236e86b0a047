-- structures/environmental_structures.lua
-- Minimalist environment elements that add variety to the world

local EnvironmentalStructures = {}

-- 1. TALL GRASS
EnvironmentalStructures.TallGrass = {
    id = "tall_grass",
    name = "Tall Grass",
    category = "flora",
    width = 1,
    height = 1,
    solid = false,
    providesShelter = false,
    spawnChance = {
        plains = 0.25,
        forest_edge = 0.15,
        hills = 0.1
    },
    requirements = {
        validTiles = {"grass", "dirt"},
        invalidTiles = {"water", "lava", "void", "stone", "road", "mountain", "dense_forest"}
    },
    tileLayout = {"G"},
    tileLegend = {G = "tall_grass"},
    interactions = {
        {
            name = "examine",
            prompt = "Examine Grass",
            action = function(structure, entity, world)
                return {
                    success = true,
                    message = "Tall wild grass swaying gently in the breeze."
                }
            end
        },
        {
            name = "search",
            prompt = "Search Grass",
            action = function(structure, entity, world)
                -- Small chance to find something
                if math.random() < 0.15 then
                    local items = {
                        {name = "seed", chance = 0.6},
                        {name = "insect", chance = 0.3},
                        {name = "small_herb", chance = 0.1}
                    }
                    
                    for _, item in ipairs(items) do
                        if math.random() < item.chance then
                            return {
                                success = true,
                                message = "You search through the tall grass and find a " .. item.name .. ".",
                                effects = {
                                    {type = "add_item", item = item.name, quantity = 1}
                                }
                            }
                        end
                    end
                end
                
                return {
                    success = false,
                    message = "You search through the tall grass but find nothing of interest."
                }
            end
        }
    },
    init = function(structure, world, x, y)
        structure.x = x
        structure.y = y
        return structure
    end
}

-- 2. THORNY BUSH
EnvironmentalStructures.ThornyBush = {
    id = "thorny_bush",
    name = "Thorny Bush",
    category = "flora",
    width = 1,
    height = 1,
    solid = true,
    providesShelter = false,
    spawnChance = {
        plains = 0.1,
        forest_edge = 0.15,
        desert = 0.2
    },
    requirements = {
        validTiles = {"grass", "dirt", "sand"},
        invalidTiles = {"water", "lava", "void", "stone", "road", "dense_forest"}
    },
    tileLayout = {"T"},
    tileLegend = {T = "thorny_bush"},
    interactions = {
        {
            name = "examine",
            prompt = "Examine Bush",
            action = function(structure, entity, world)
                return {
                    success = true,
                    message = "A dense bush covered in sharp thorns. It would be painful to walk through."
                }
            end
        }
    },
    init = function(structure, world, x, y)
        structure.x = x
        structure.y = y
        return structure
    end
}

-- 3. SMALL ROCK FORMATION
EnvironmentalStructures.SmallRockFormation = {
    id = "small_rock_formation",
    name = "Small Rock Formation",
    category = "natural",
    width = 1,
    height = 1,
    solid = true,
    providesShelter = false,
    spawnChance = {
        mountains = 0.2,
        hills = 0.15,
        plains = 0.08
    },
    requirements = {
        validTiles = {"grass", "dirt", "stone", "mountain_base"},
        invalidTiles = {"water", "lava", "void", "road", "dense_forest"}
    },
    tileLayout = {"R"},
    tileLegend = {R = "rock_formation"},
    interactions = {
        {
            name = "examine",
            prompt = "Examine Rocks",
            action = function(structure, entity, world)
                return {
                    success = true,
                    message = "A small formation of weathered rocks jutting from the ground."
                }
            end
        }
    },
    init = function(structure, world, x, y)
        structure.x = x
        structure.y = y
        return structure
    end
}

-- 4. FALLEN LOG
EnvironmentalStructures.FallenLog = {
    id = "fallen_log",
    name = "Fallen Log",
    category = "natural",
    width = 1,
    height = 1,
    solid = false,
    providesShelter = false,
    spawnChance = {
        forest = 0.2,
        forest_edge = 0.15
    },
    requirements = {
        validTiles = {"grass", "dirt", "forest_floor"},
        invalidTiles = {"water", "lava", "void", "road", "stone", "mountain"}
    },
    tileLayout = {"L"},
    tileLegend = {L = "fallen_log"},
    interactions = {
        {
            name = "examine",
            prompt = "Examine Log",
            action = function(structure, entity, world)
                return {
                    success = true,
                    message = "A rotting log covered in moss and mushrooms."
                }
            end
        }
    },
    init = function(structure, world, x, y)
        structure.x = x
        structure.y = y
        return structure
    end
}

return EnvironmentalStructures