local Pike = {
    id = "pike",
    name = "Northern Pike",
    type = "fish",
    
    -- Entity categories
    categories = {"animal", "fish", "freshwater", "predator", "ambush"},
    
    -- Threat and food categories
    threatCategories = {"player", "predator"},
    foodCategories = {"fish", "frog", "bird", "small_prey"},
    
    -- Stats
    maxHealth = 45,
    health = 45,
    maxStamina = 70,
    stamina = 70,
    speed = 2.2,
    
    -- Water properties
    water = {
        swimSpeed = 2.2,
        maxDepth = 10,
        preferredDepth = 4,
        oxygenLevel = 1.0,
        temperature = "cold",
        currentSpeed = 1.2
    },
    
    -- Behaviors
    behaviors = {"hunt", "patrol", "ambush", "territorial"},
    
    -- Behavior configurations
    behaviorConfigs = {
        hunt = {
            moveSpeed = 2.8,
            searchRadius = 20,
            attackRange = 2.0,
            damage = 25,
            preferredPrey = {"fish", "frog", "bird"},
            successChance = 0.75,
            currentBonus = 0.2
        },
        patrol = {
            moveSpeed = 1.8,
            patrolRadius = 25,
            preferredDepth = 4,
            restInterval = {15, 20}
        },
        ambush = {
            moveSpeed = 0.3,
            waitDuration = {10, 25},
            attackSpeed = 3.5,
            successChance = 0.9,
            preferredTerrain = "vegetation",
            detectionRange = 15
        },
        territorial = {
            moveSpeed = 2.5,
            territoryRadius = 30,
            preferredDepth = 4,
            aggressionLevel = 0.8
        }
    },
    
    -- Special abilities
    abilities = {
        charge = {
            speed = 3.5,
            damage = 30,
            duration = 0.6,
            cooldown = 4,
            staminaCost = 20
        },
        tailWhip = {
            range = 3,
            damage = 15,
            duration = 0.3,
            cooldown = 2,
            effect = "stun"
        },
        camouflage = {
            duration = 8,
            cooldown = 10,
            successChance = 0.8,
            effect = "invisible"
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "pike",
        scale = 1.2,
        animations = {
            "idle", "swim", "charge", "ambush", "attack"
        },
        variants = {
            "green", "brown", "spotted", "dark"
        }
    },
    
    -- Sound effects
    sounds = {
        splash = "fish_splash",
        attack = "fish_attack",
        swim = "fish_swim",
        charge = "fish_charge"
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.8, quantity = {1, 2}},
        {id = "scale", chance = 0.7, quantity = {1, 3}},
        {id = "fin", chance = 0.4, quantity = {1, 1}},
        {id = "bone", chance = 0.5, quantity = {1, 1}}
    }
}

-- Initialize the entity
function Pike.init(entity, world)
    -- Copy all fields from Pike template to entity instance
    for k, v in pairs(Pike) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random pike variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Pike.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update water properties
    if entity.water then
        -- Adjust hunting success based on time of day
        if world and world.timeOfDay == "dawn" or world.timeOfDay == "dusk" then
            entity.behaviorConfigs.hunt.successChance = 0.85
        else
            entity.behaviorConfigs.hunt.successChance = 0.75
        end

        -- Adjust preferred depth based on time of day
        if world and world.timeOfDay == "night" then
            entity.water.preferredDepth = 2
        else
            entity.water.preferredDepth = 4
        end

        -- Adjust behavior based on water current
        if world and world.water and world.water.current then
            entity.behaviorConfigs.hunt.successChance = 
                entity.behaviorConfigs.hunt.successChance + 
                entity.behaviorConfigs.hunt.currentBonus
        end
    end
end

return Pike 