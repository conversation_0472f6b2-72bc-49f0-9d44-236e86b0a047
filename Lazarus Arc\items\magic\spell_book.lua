-- items/magic/spell_book.lua

local MagicItem = {
    id = "spell_book",
    name = "Spell Book",
    description = "A book containing magical knowledge and spells.",
    type = "book",
    category = "magic",
    stackable = false,

    stats = {
        magic_power = 10.0,
        mana_efficiency = 1.1, -- 10% more efficient mana usage
        weight = 2.0,
    },

    sprite = "res://Sprite/items/magic/spell_book.png",
    size = { width = 32, height = 32 },

    effects = {
        spell_boost = 0.05, -- 5% boost to spell effectiveness
        mana_regen = 0.02   -- 2% faster mana regeneration
    },

    meta = {
        rarity = "uncommon"
    },

    onUse = function(self, user)
        if user then
            print(user.name .. " studied the spell book.")
            
            -- In a real implementation, this would open a spell selection UI
            print("Available spells: Fireball, Ice Shard, Lightning Bolt")
            
            -- Temporary boost to magic power
            if user.stats and user.stats.magic_power then
                local oldPower = user.stats.magic_power
                user.stats.magic_power = oldPower * 1.1 -- 10% temporary boost
                print(user.name .. "'s magic power temporarily increased!")
                
                -- Schedule power to return to normal
                -- In a real implementation, this would use a timer
                print("The effect will wear off in 30 seconds.")
            end
            
            return true
        end
        return false
    end,
    
    -- When equipped, provides passive bonuses
    onEquip = function(self, user)
        if user and user.stats then
            if user.stats.mana_regen then
                user.stats.mana_regen = user.stats.mana_regen * (1 + self.effects.mana_regen)
            end
            
            if user.stats.spell_power then
                user.stats.spell_power = user.stats.spell_power * (1 + self.effects.spell_boost)
            end
            
            print(user.name .. " equipped the spell book, gaining magical bonuses.")
        end
    end,
    
    -- When unequipped, removes passive bonuses
    onUnequip = function(self, user)
        if user and user.stats then
            if user.stats.mana_regen then
                user.stats.mana_regen = user.stats.mana_regen / (1 + self.effects.mana_regen)
            end
            
            if user.stats.spell_power then
                user.stats.spell_power = user.stats.spell_power / (1 + self.effects.spell_boost)
            end
            
            print(user.name .. " unequipped the spell book, losing magical bonuses.")
        end
    end
}

return MagicItem
