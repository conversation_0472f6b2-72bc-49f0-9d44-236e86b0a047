-- weather/nanite_storm.lua
-- Nanite storm weather pattern - Hawken repair nanites with visual effects

local NaniteStormWeather = {
    id = "nanite_storm",
    name = "Nanite Storm",
    
    -- Visual properties
    visual = {
        skyColor = {r = 100, g = 200, b = 255}, -- Bright blue sky
        sunIntensity = 0.8,
        cloudCoverage = 0.3,
        cloudColor = {r = 150, g = 200, b = 255}, -- Light blue clouds
        ambientLightLevel = 0.7,
        naniteColor = {r = 0, g = 255, b = 255} -- Cyan nanites
    },
    
    -- Environment modifiers
    environment = {
        temperature = 0.9, -- Slightly cooler
        visibility = 0.8, -- Good visibility
        humidity = 0.5, -- Moderate humidity
        windStrength = 0.3, -- Light wind
        naniteDensity = 0.8, -- High nanite concentration
        repairRate = 0.7 -- High repair rate
    },
    
    -- Particle systems
    particles = {
        nanites = {
            enabled = true,
            intensity = 0.8,
            color = {r = 0, g = 255, b = 255},
            size = {min = 1, max = 3},
            speed = {min = 50, max = 150},
            image = "assets/particles/Complex/star/star_1.png",
            blendMode = "add",
            lifetime = {min = 0.5, max = 1.0},
            rotation = {min = 0, max = 360},
            alpha = {min = 0.6, max = 0.9}
        },
        repair = {
            enabled = true,
            intensity = 0.6,
            color = {r = 0, g = 255, b = 0},
            size = {min = 1, max = 2},
            speed = {min = 20, max = 40},
            image = "assets/particles/Complex/others/others_1.png",
            blendMode = "add",
            lifetime = {min = 0.3, max = 0.7},
            rotation = {min = 0, max = 360},
            alpha = {min = 0.4, max = 0.7}
        },
        glow = {
            enabled = true,
            intensity = 0.4,
            color = {r = 0, g = 200, b = 255},
            size = {min = 2, max = 4},
            speed = {min = 10, max = 20},
            image = "assets/particles/Complex/circle/circle_1.png",
            blendMode = "add",
            lifetime = {min = 1.0, max = 2.0},
            rotation = {min = 0, max = 360},
            alpha = {min = 0.2, max = 0.4}
        }
    },
    
    -- Sound effects
    sounds = {
        ambient = "nanite_ambient",
        repair = "nanite_repair",
        swarm = "nanite_swarm",
        volume = 0.3
    },
    
    -- Effects on game entities and tiles
    effects = {
        -- Movement effects
        movementSpeedMultiplier = 1.0, -- No speed change
        
        -- Vision effects
        visionRange = 0.9, -- Slightly reduced vision
        
        -- Temperature effects
        temperatureModifier = 0.9, -- Slightly cooler
        
        -- Energy effects
        energyDrainRate = 0.8, -- Reduced energy drain
        
        -- Shield effects
        shieldEffectiveness = 1.1, -- Increased shield effectiveness
        
        -- Sensor effects
        sensorRange = 0.9, -- Slightly reduced sensor range
        
        -- Special effects
        repairRate = 0.7, -- High repair rate
        naniteBoost = 0.2 -- Nanite performance boost
    },
    
    -- Transition probabilities to other weather (per game hour)
    transitions = {
        clear = 0.4,
        cloudy = 0.3,
        em_storm = 0.1,
        -- Implied: 0.2 chance to stay nanite storm
    },
    
    -- Day/night cycle modifiers
    timeModifiers = {
        dawn = {
            skyColor = {r = 120, g = 180, b = 255},
            ambientLightLevel = 0.5,
            naniteIntensity = 0.8 -- Lower nanite activity at dawn
        },
        day = {
            -- Uses default properties
        },
        dusk = {
            skyColor = {r = 90, g = 170, b = 255},
            ambientLightLevel = 0.4,
            naniteIntensity = 0.9 -- Higher nanite activity at dusk
        },
        night = {
            skyColor = {r = 70, g = 160, b = 255},
            ambientLightLevel = 0.6,
            naniteIntensity = 1.2 -- More visible nanites at night
        }
    }
}

-- Helper function to safely set particle system properties
local function setupParticleSystem(ps, config)
    if not ps or type(ps) ~= "table" then
        print("Warning: Invalid particle system")
        return false
    end
    
    local methods = {
        setParticleLifetime = function() return ps:setParticleLifetime(config.lifetime.min, config.lifetime.max) end,
        setEmissionRate = function() return ps:setEmissionRate(config.emissionRate or 0) end,
        setSizeVariation = function() return ps:setSizeVariation(config.sizeVariation or 0.3) end,
        setLinearAcceleration = function() return ps:setLinearAcceleration(config.acceleration.x1 or 0, config.acceleration.y1 or 0, config.acceleration.x2 or 0, config.acceleration.y2 or 0) end,
        setColors = function() return ps:setColors(config.colors.r1 or 255, config.colors.g1 or 255, config.colors.b1 or 255, config.colors.a1 or 255, config.colors.r2 or 255, config.colors.g2 or 255, config.colors.b2 or 255, config.colors.a2 or 0) end,
        setBlendMode = function() return ps:setBlendMode(config.blendMode or "normal") end
    }
    
    for methodName, method in pairs(methods) do
        if type(ps[methodName]) ~= "function" then
            print("Warning: Particle system missing method: " .. methodName)
            return false
        end
        
        local success, err = pcall(method)
        if not success then
            print("Warning: Failed to call " .. methodName .. ": " .. tostring(err))
            return false
        end
    end
    
    return true
end

-- Initialize the weather pattern
function NaniteStormWeather.init(world)
    if not world then
        print("Warning: world parameter is nil in NaniteStormWeather.init")
        return
    end
    
    print("Initializing nanite storm weather")
    
    -- Set global light level
    world.lightLevel = NaniteStormWeather.visual.ambientLightLevel
    
    -- Start ambient sounds if weather system exists
    if world.weatherSystem then
        if NaniteStormWeather.sounds.ambient then
            world.weatherSystem:playSound(NaniteStormWeather.sounds.ambient, {
                verbose = false,  -- Don't print warnings if sound is missing
                loop = true,      -- Ambient sounds should loop
                volume = 0.5      -- Lower volume for ambient weather
            })
        end
    end
    
    -- Create particle systems
    if NaniteStormWeather.particles.nanites.enabled then
        -- Create nanite particles
        print("Creating nanite particles with intensity: " .. NaniteStormWeather.particles.nanites.intensity)
        local success, image = pcall(love.graphics.newImage, NaniteStormWeather.particles.nanites.image)
        if success and image then
            local success2, ps = pcall(love.graphics.newParticleSystem, image)
            if success2 and ps then
                local config = {
                    lifetime = NaniteStormWeather.particles.nanites.lifetime,
                    emissionRate = 20,
                    sizeVariation = 0.3,
                    acceleration = {x1 = -10, y1 = -10, x2 = 10, y2 = 10},
                    colors = {r1 = 255, g1 = 255, b1 = 255, a1 = 255, r2 = 0, g2 = 255, b2 = 255, a2 = 0},
                    blendMode = "add"
                }
                
                if setupParticleSystem(ps, config) then
                    NaniteStormWeather.naniteSystem = ps
                else
                    print("Warning: Failed to setup nanite particle system")
                end
            else
                print("Warning: Failed to create nanite particle system")
            end
        else
            print("Warning: Could not load nanite particle image: " .. NaniteStormWeather.particles.nanites.image)
        end
    end
    
    if NaniteStormWeather.particles.repair.enabled then
        -- Create repair particles
        print("Creating repair particles with intensity: " .. NaniteStormWeather.particles.repair.intensity)
        local success, image = pcall(love.graphics.newImage, NaniteStormWeather.particles.repair.image)
        if success and image then
            local success2, ps = pcall(love.graphics.newParticleSystem, image)
            if success2 and ps then
                local config = {
                    lifetime = NaniteStormWeather.particles.repair.lifetime,
                    emissionRate = 0, -- Only emit on repair
                    sizeVariation = 0.2,
                    acceleration = {x1 = -5, y1 = -5, x2 = 5, y2 = 5},
                    colors = {r1 = 255, g1 = 255, b1 = 255, a1 = 255, r2 = 0, g2 = 255, b2 = 0, a2 = 0},
                    blendMode = "add"
                }
                
                if setupParticleSystem(ps, config) then
                    NaniteStormWeather.repairSystem = ps
                else
                    print("Warning: Failed to setup repair particle system")
                end
            else
                print("Warning: Failed to create repair particle system")
            end
        else
            print("Warning: Could not load repair particle image: " .. NaniteStormWeather.particles.repair.image)
        end
    end
    
    if NaniteStormWeather.particles.glow.enabled then
        -- Create glow particles
        print("Creating glow particles with intensity: " .. NaniteStormWeather.particles.glow.intensity)
        local success, image = pcall(love.graphics.newImage, NaniteStormWeather.particles.glow.image)
        if success and image then
            local success2, ps = pcall(love.graphics.newParticleSystem, image)
            if success2 and ps then
                local config = {
                    lifetime = NaniteStormWeather.particles.glow.lifetime,
                    emissionRate = 5,
                    sizeVariation = 0.3,
                    acceleration = {x1 = -2, y1 = -2, x2 = 2, y2 = 2},
                    colors = {r1 = 255, g1 = 255, b1 = 255, a1 = 255, r2 = 0, g2 = 200, b2 = 255, a2 = 0},
                    blendMode = "add"
                }
                
                if setupParticleSystem(ps, config) then
                    NaniteStormWeather.glowSystem = ps
                else
                    print("Warning: Failed to setup glow particle system")
                end
            else
                print("Warning: Failed to create glow particle system")
            end
        else
            print("Warning: Could not load glow particle image: " .. NaniteStormWeather.particles.glow.image)
        end
    end
    
    -- Display message to players if message system exists
    if world.messageSystem then
        world.messageSystem:broadcast("Nanite storm detected. Repair systems active.")
    else
        print("Nanite storm detected. Repair systems active.")
    end
end

-- Update function called every frame
function NaniteStormWeather.update(world, dt)
    if not world then
        print("Warning: world parameter is nil in NaniteStormWeather.update")
        return
    end
    
    -- Update day/night cycle based on world time
    local timeOfDay = world.timeOfDay or "day"
    local modifiers = NaniteStormWeather.timeModifiers[timeOfDay]
    
    if modifiers then
        -- Apply time-specific modifiers
        if modifiers.skyColor then
            -- Change sky color
        end
        
        if modifiers.ambientLightLevel then
            world.lightLevel = modifiers.ambientLightLevel
        end
        
        if modifiers.naniteIntensity then
            NaniteStormWeather.particles.nanites.intensity = modifiers.naniteIntensity
            if NaniteStormWeather.naniteSystem then
                NaniteStormWeather.naniteSystem:setEmissionRate(20 * modifiers.naniteIntensity)
            end
        end
    end
    
    -- Update particle systems
    if NaniteStormWeather.naniteSystem then
        NaniteStormWeather.naniteSystem:update(dt)
    end
    if NaniteStormWeather.repairSystem then
        NaniteStormWeather.repairSystem:update(dt)
    end
    if NaniteStormWeather.glowSystem then
        NaniteStormWeather.glowSystem:update(dt)
    end
    
    -- Apply effects to game world if entity system exists
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            -- Apply repair effects
            if entity.health and entity.maxHealth then
                entity.health = math.min(entity.maxHealth, entity.health + 
                    (NaniteStormWeather.effects.repairRate * dt))
                -- Trigger repair particle effect
                if NaniteStormWeather.repairSystem then
                    NaniteStormWeather.repairSystem:emit(5)
                end
            end
            
            -- Apply energy boost
            if entity.energy and entity.maxEnergy then
                entity.energy = math.min(entity.maxEnergy, entity.energy + 
                    (NaniteStormWeather.effects.naniteBoost * dt))
            end
            
            -- Apply shield boost
            if entity.shield and entity.maxShield then
                entity.shield = math.min(entity.maxShield, entity.shield + 
                    (NaniteStormWeather.effects.naniteBoost * dt))
            end
        end
    end
    
    -- Apply weather effects if weather system exists
    if world.weatherSystem then
        world.weatherSystem:applyEffects(NaniteStormWeather.environment)
    end
end

-- Clean up when weather changes
function NaniteStormWeather.cleanUp(world)
    if not world then
        print("Warning: world parameter is nil in NaniteStormWeather.cleanUp")
        return
    end
    
    -- Stop sounds if weather system exists
    if world.weatherSystem then
        if NaniteStormWeather.sounds.ambient then
            world.weatherSystem:stopSound(NaniteStormWeather.sounds.ambient)
        end
    end
    
    -- Clean up particle systems
    if NaniteStormWeather.naniteSystem then
        NaniteStormWeather.naniteSystem:stop()
    end
    if NaniteStormWeather.repairSystem then
        NaniteStormWeather.repairSystem:stop()
    end
    if NaniteStormWeather.glowSystem then
        NaniteStormWeather.glowSystem:stop()
    end
    
    -- Notify players if message system exists
    if world.messageSystem then
        world.messageSystem:broadcast("Nanite storm ending. Repair systems deactivating.")
    else
        print("Nanite storm ending. Repair systems deactivating.")
    end
    
    print("Nanite storm weather ending")
end

-- Table contains helper function
function table.contains(table, element)
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

return NaniteStormWeather 