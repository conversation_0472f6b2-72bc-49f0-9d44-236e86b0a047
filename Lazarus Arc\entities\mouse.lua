local Mouse = {
    id = "mouse",
    name = "Mouse",
    type = "mouse",
    -- PLACEHOLDER: Basic shape for visual representation
    shape = {
        {0, -1}, {0.8, -0.6}, {1, 0}, {0.8, 0.6},
        {0, 1}, {-0.8, 0.6}, {-1, 0}, {-0.8, -0.6}
    },
    size = 4,

    -- Entity categories
    categories = {"animal", "prey", "mammal", "small"},

    -- Threat and food categories
    threatCategories = {"predator", "player"},
    foodCategories = {"seed", "berry", "insect"},

    -- Stats
    maxHealth = 12,
    health = 12,
    maxStamina = 30,
    stamina = 30,
    speed = 1.8,

    -- Behaviors
    behaviors = {"flee", "wander", "forage", "burrow"},

    -- Behavior configurations
    behaviorConfigs = {
        flee = {
            moveSpeed = 2.5,
            detectionRadius = 8,
            hideChance = 0.7,
            hideDuration = {3, 8}
        },
        wander = {
            moveSpeed = 1.2,
            changeDirectionChance = 0.15,
            idleChance = 0.3,
            idleDuration = {1, 4},
            wanderRadius = 6
        },
        forage = {
            moveSpeed = 1.0,
            searchRadius = 5,
            forageTime = {2, 5},
            foodTypes = {"seed", "berry", "insect"},
            cacheChance = 0.4
        },
        burrow = {
            maxDepth = 2,
            digSpeed = 0.5,
            tunnelLength = {3, 6},
            exitChance = 0.3,
            exitInterval = {10, 20}
        }
    },

    -- Special abilities
    abilities = {
        quickHide = {
            speedBoost = 1.5,
            duration = 1,
            cooldown = 3
        },
        squeak = {
            range = 4,
            duration = 0.5,
            cooldown = 2,
            effect = "alert"
        }
    },

    -- Appearance
    appearance = {
        sprite = "mouse",
        scale = 0.6,
        animations = {
            "idle", "walk", "run", "burrow", "hide"
        },
        variants = {
            "brown", "gray", "white", "black"
        }
    },

    -- Sound effects
    sounds = {
        squeak = "mouse_squeak",
        rustle = "mouse_rustle",
        dig = "mouse_dig"
    },

    -- Loot drops
    drops = {
        {id = "meat", chance = 0.7, quantity = {1, 1}},
        {id = "tail", chance = 0.5, quantity = {1, 1}},
        {id = "fur", chance = 0.6, quantity = {1, 2}},
        {id = "whisker", chance = 0.4, quantity = {1, 3}},
        {id = "bone", chance = 0.3, quantity = {1, 1}}
    }
}

-- Initialize the entity
function Mouse.init(entity, world)
    -- Copy all fields from Mouse template to entity instance
    for k, v in pairs(Mouse) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random mouse variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

return Mouse