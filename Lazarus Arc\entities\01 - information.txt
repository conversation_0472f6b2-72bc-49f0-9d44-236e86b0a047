Load them through your module system like we set up earlier:

WorldCore.loadModule("entities", "rabbit")
WorldCore.loadModule("entities", "crow")
-- etc.

Spawn them in appropriate biomes

-- Example: Add rabbits to a grassy area
local x, y = 100, 100 -- Position to spawn
local rabbit = world.entitySystem:addEntity("rabbit", x, y)

------------------------------------------------------------------------------------------------------------------------------------

"Create a 32x32 pixel art [ENTITY] sprite for a video game that strikes a balance between simplicity and detail. Use a transparent background with clean, crisp outlines and a limited, retro-inspired color palette. Emphasize the [ENTITY]'s distinctive features with just enough pixel-level detail to enhance its character without drifting into realism. Ensure the design maintains a classic pixel art style—charming and animation-friendly—so it remains unmistakably a [ENTITY] and not over-stylized or disproportionate."

------------------------------------------------------------------------------------------------------------------------------------