local ForestEdge = {
    id = "forest_edge",
    name = "Forest Edge",
    type = "tile",
    
    -- Categories
    categories = {"ground", "forest", "transition", "biome"},
    
    -- Properties
    properties = {
        walkable = true,
        swimable = false,
        climbable = false,
        flyable = true,
        diggable = true,
        buildable = true,
        flammable = true,
        fertility = 0.7,
        moisture = 0.6,
        temperature = 0.5,
        grassDensity = 0.8,
        grassHeight = 0.6,
        grassColor = {0.4, 0.6, 0.2},
        grassVariants = 3,
        grassTypes = {"tall", "medium", "short"},
        grassDistribution = {
            tall = 0.4,
            medium = 0.4,
            short = 0.2
        },
        transitionRadius = 2,
        transitionStrength = 0.5,
        biomeBlend = true,
        biomeInfluence = {
            forest = 0.7,
            plains = 0.3
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "forest_edge",
        scale = 1.0,
        animations = {
            "idle",
            "wind",
            "transition"
        },
        variants = {
            "dense",
            "sparse",
            "mixed",
            "transition"
        },
        blendMode = "multiply",
        tint = {0.4, 0.6, 0.2},
        alpha = 1.0
    },
    
    -- Sound effects
    sounds = {
        walk = "grass_walk",
        dig = "grass_dig",
        wind = "forest_wind"
    },
    
    -- Resources
    resources = {
        grass = {
            type = "plant",
            amount = 4,
            regrowthRate = 0.3,
            regrowthTime = 240,
            harvestable = true,
            harvestAmount = 1,
            harvestTool = "scythe"
        },
        flowers = {
            type = "plant",
            amount = 2,
            regrowthRate = 0.2,
            regrowthTime = 360,
            harvestable = true,
            harvestAmount = 1,
            harvestTool = "scythe"
        },
        berries = {
            type = "plant",
            amount = 1,
            regrowthRate = 0.1,
            regrowthTime = 480,
            harvestable = true,
            harvestAmount = 1,
            harvestTool = "hand"
        }
    },
    
    -- Effects
    effects = {
        wind = {
            type = "environment",
            duration = 0,
            effects = {
                grassWave = true,
                windStrength = 0.5
            }
        },
        transition = {
            type = "environment",
            duration = 0,
            effects = {
                biomeBlend = true,
                transitionStrength = 0.5
            }
        }
    },
    
    -- Spawn rules
    spawnRules = {
        plants = {
            "forest_grass",
            "forest_flower",
            "forest_berry",
            "forest_sapling",
            "forest_fern"
        },
        animals = {
            "deer",
            "rabbit",
            "fox",
            "bird",
            "squirrel"
        },
        structures = {
            "fallen_log",
            "rock_formation",
            "flower_patch"
        }
    }
}

-- Initialize the tile
function ForestEdge.init(tile, world)
    -- Initialize properties first
    tile.properties = tile.properties or {}
    tile.properties.grassState = {
        type = ForestEdge.properties.grassTypes[math.random(#ForestEdge.properties.grassTypes)],
        height = ForestEdge.properties.grassHeight or 0.6,
        color = ForestEdge.properties.grassColor or {0.4, 0.6, 0.2},
        density = ForestEdge.properties.grassDensity or 0.8
    }

    -- Copy all fields from ForestEdge template to tile instance
    for k, v in pairs(ForestEdge) do
        if type(v) ~= "function" and tile[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                tile[k] = {}
                for subk, subv in pairs(v) do
                    tile[k][subk] = subv
                end
            else
                tile[k] = v
            end
        end
    end

    -- Set random variant
    if tile.appearance and tile.appearance.variants then
        local variants = tile.appearance.variants
        tile.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize resources
    if tile.resources then
        for resourceType, resource in pairs(tile.resources) do
            resource.currentAmount = resource.amount
            resource.lastHarvest = 0
            resource.growthStage = "mature"
        end
    end

    -- Initialize biome transition state
    tile.properties.transitionState = {
        forestInfluence = 0.7,
        plainsInfluence = 0.3,
        transitionProgress = 0.5
    }

    return tile
end

-- Update the tile
function ForestEdge.update(tile, world, dt)
    -- Update grass state based on environment
    if tile.properties.grassState then
        -- Check for wind effect
        if world.wind and world.wind.strength > 0.3 then
            tile.properties.grassState.windEffect = true
        else
            tile.properties.grassState.windEffect = false
        end
        
        -- Update grass color based on biome transition
        if tile.properties.transitionState then
            local forestColor = {0.3, 0.5, 0.1}
            local plainsColor = {0.4, 0.6, 0.2}
            local progress = tile.properties.transitionState.transitionProgress
            
            tile.properties.grassState.color = {
                forestColor[1] * progress + plainsColor[1] * (1 - progress),
                forestColor[2] * progress + plainsColor[2] * (1 - progress),
                forestColor[3] * progress + plainsColor[3] * (1 - progress)
            }
        end
    end
    
    -- Update resources
    if tile.resources then
        for resourceType, resource in pairs(tile.resources) do
            if resource.currentAmount < resource.amount then
                -- Regrow resources
                local regrowthAmount = resource.regrowthRate * dt
                resource.currentAmount = math.min(resource.amount, resource.currentAmount + regrowthAmount)
                
                -- Update growth stage
                if resource.currentAmount >= resource.amount then
                    resource.growthStage = "mature"
                elseif resource.currentAmount >= resource.amount * 0.5 then
                    resource.growthStage = "growing"
                else
                    resource.growthStage = "young"
                end
            end
        end
    end
    
    -- Update biome transition
    if tile.properties.transitionState then
        -- Check surrounding tiles for biome influence
        if world.getTile then
            local forestCount = 0
            local plainsCount = 0
            local totalCount = 0
            
            for x = -tile.properties.transitionRadius, tile.properties.transitionRadius do
                for y = -tile.properties.transitionRadius, tile.properties.transitionRadius do
                    local checkTile = world.getTile({
                        x = tile.position.x + x,
                        y = tile.position.y + y
                    })
                    
                    if checkTile then
                        totalCount = totalCount + 1
                        if table.contains(checkTile.categories, "forest") then
                            forestCount = forestCount + 1
                        elseif table.contains(checkTile.categories, "plains") then
                            plainsCount = plainsCount + 1
                        end
                    end
                end
            end
            
            if totalCount > 0 then
                -- Update transition progress based on surrounding biomes
                tile.properties.transitionState.transitionProgress = forestCount / totalCount
                
                -- Update biome influences
                tile.properties.transitionState.forestInfluence = forestCount / totalCount
                tile.properties.transitionState.plainsInfluence = plainsCount / totalCount
            end
        end
    end
end

-- Handle interaction
function ForestEdge.interact(tile, world, entity, action)
    if action == "dig" then
        -- Handle digging
        if tile.properties.diggable then
            -- Create dirt item
            if world.createItem then
                world.createItem({
                    type = "dirt",
                    amount = 1,
                    position = tile.position
                })
            end
            
            -- Play dig sound
            if world.playSound then
                world.playSound(tile.sounds.dig)
            end
            
            -- Convert to dirt tile
            if world.setTile then
                world.setTile(tile.position, "dirt")
            end
        end
    elseif action == "harvest" then
        -- Handle resource harvesting
        if tile.resources then
            for resourceType, resource in pairs(tile.resources) do
                if resource.harvestable and resource.currentAmount > 0 then
                    -- Create harvested item
                    if world.createItem then
                        world.createItem({
                            type = resourceType,
                            amount = resource.harvestAmount,
                            position = tile.position
                        })
                    end
                    
                    -- Update resource amount
                    resource.currentAmount = math.max(0, resource.currentAmount - resource.harvestAmount)
                    resource.lastHarvest = world.time
                    resource.growthStage = "young"
                end
            end
        end
    end
end

return ForestEdge 