import SpriteGenerator from './spriteGenerator.js';

class TileGenerator extends SpriteGenerator {
    constructor(canvas) {
        super(canvas);
        this.tileType = 'grass';
        this.tileStyle = 'basic';
        this.variation = 0;
        this.edgeStyle = 'none';
    }

    // Set tile type
    setTileType(type) {
        this.tileType = type;
        this.drawBaseShape();
    }

    // Set tile style
    setTileStyle(style) {
        this.tileStyle = style;
        this.drawBaseShape();
    }

    // Set variation
    setVariation(variation) {
        this.variation = variation;
        this.drawBaseShape();
    }

    // Set edge style
    setEdgeStyle(style) {
        this.edgeStyle = style;
        this.drawBaseShape();
    }

    // Draw base shape
    drawBaseShape() {
        this.ctx.clearRect(0, 0, this.size, this.size);
        
        // Draw base tile
        this.drawBaseTile();
        
        // Draw edge if applicable
        if (this.edgeStyle !== 'none') {
            this.drawEdge();
        }
        
        // Draw variation details
        this.drawVariation();
    }

    // Draw base tile
    drawBaseTile() {
        switch (this.tileType) {
            case 'grass':
                this.drawGrassTile();
                break;
            case 'stone':
                this.drawStoneTile();
                break;
            case 'water':
                this.drawWaterTile();
                break;
            case 'sand':
                this.drawSandTile();
                break;
            case 'dirt':
                this.drawDirtTile();
                break;
            case 'wood':
                this.drawWoodTile();
                break;
        }
    }

    // Draw grass tile
    drawGrassTile() {
        // Base color
        this.ctx.fillStyle = this.currentPalette[0];
        this.ctx.fillRect(0, 0, this.size, this.size);
        
        // Grass details
        this.ctx.fillStyle = this.currentPalette[1];
        for (let i = 0; i < 8; i++) {
            const x = Math.random() * this.size;
            const y = Math.random() * this.size;
            const width = 2 + Math.random() * 2;
            const height = 2 + Math.random() * 3;
            this.ctx.fillRect(x, y, width, height);
        }
    }

    // Draw stone tile
    drawStoneTile() {
        // Base color
        this.ctx.fillStyle = this.currentPalette[0];
        this.ctx.fillRect(0, 0, this.size, this.size);
        
        // Stone texture
        this.ctx.fillStyle = this.currentPalette[1];
        for (let i = 0; i < 12; i++) {
            const x = Math.random() * this.size;
            const y = Math.random() * this.size;
            const size = 2 + Math.random() * 3;
            this.ctx.beginPath();
            this.ctx.arc(x, y, size, 0, Math.PI * 2);
            this.ctx.fill();
        }
    }

    // Draw water tile
    drawWaterTile() {
        // Base color
        this.ctx.fillStyle = this.currentPalette[0];
        this.ctx.fillRect(0, 0, this.size, this.size);
        
        // Water ripples
        this.ctx.fillStyle = this.currentPalette[1];
        for (let i = 0; i < 6; i++) {
            const x = Math.random() * this.size;
            const y = Math.random() * this.size;
            const size = 3 + Math.random() * 4;
            this.ctx.beginPath();
            this.ctx.arc(x, y, size, 0, Math.PI * 2);
            this.ctx.fill();
        }
    }

    // Draw sand tile
    drawSandTile() {
        // Base color
        this.ctx.fillStyle = this.currentPalette[0];
        this.ctx.fillRect(0, 0, this.size, this.size);
        
        // Sand texture
        this.ctx.fillStyle = this.currentPalette[1];
        for (let i = 0; i < 20; i++) {
            const x = Math.random() * this.size;
            const y = Math.random() * this.size;
            const size = 1 + Math.random() * 2;
            this.ctx.beginPath();
            this.ctx.arc(x, y, size, 0, Math.PI * 2);
            this.ctx.fill();
        }
    }

    // Draw dirt tile
    drawDirtTile() {
        // Base color
        this.ctx.fillStyle = this.currentPalette[0];
        this.ctx.fillRect(0, 0, this.size, this.size);
        
        // Dirt texture
        this.ctx.fillStyle = this.currentPalette[1];
        for (let i = 0; i < 15; i++) {
            const x = Math.random() * this.size;
            const y = Math.random() * this.size;
            const size = 2 + Math.random() * 3;
            this.ctx.beginPath();
            this.ctx.arc(x, y, size, 0, Math.PI * 2);
            this.ctx.fill();
        }
    }

    // Draw wood tile
    drawWoodTile() {
        // Base color
        this.ctx.fillStyle = this.currentPalette[0];
        this.ctx.fillRect(0, 0, this.size, this.size);
        
        // Wood grain
        this.ctx.strokeStyle = this.currentPalette[1];
        this.ctx.lineWidth = 2;
        for (let i = 0; i < 4; i++) {
            const y = (this.size / 4) * i;
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.size, y);
            this.ctx.stroke();
        }
    }

    // Draw edge
    drawEdge() {
        this.ctx.strokeStyle = this.currentPalette[2];
        this.ctx.lineWidth = 2;
        
        switch (this.edgeStyle) {
            case 'raised':
                this.ctx.strokeRect(1, 1, this.size - 2, this.size - 2);
                break;
            case 'sunken':
                this.ctx.strokeRect(0, 0, this.size, this.size);
                break;
            case 'gradient':
                const gradient = this.ctx.createLinearGradient(0, 0, this.size, this.size);
                gradient.addColorStop(0, this.currentPalette[2]);
                gradient.addColorStop(1, this.currentPalette[3]);
                this.ctx.strokeStyle = gradient;
                this.ctx.strokeRect(0, 0, this.size, this.size);
                break;
        }
    }

    // Draw variation details
    drawVariation() {
        switch (this.variation) {
            case 1:
                // Add some random spots
                this.ctx.fillStyle = this.currentPalette[3];
                for (let i = 0; i < 3; i++) {
                    const x = Math.random() * this.size;
                    const y = Math.random() * this.size;
                    const size = 1 + Math.random() * 2;
                    this.ctx.beginPath();
                    this.ctx.arc(x, y, size, 0, Math.PI * 2);
                    this.ctx.fill();
                }
                break;
            case 2:
                // Add a pattern
                this.ctx.strokeStyle = this.currentPalette[3];
                this.ctx.lineWidth = 1;
                for (let i = 0; i < 4; i++) {
                    const angle = (Math.PI / 2) * i;
                    this.ctx.beginPath();
                    this.ctx.moveTo(
                        this.size / 2 + Math.cos(angle) * this.size / 4,
                        this.size / 2 + Math.sin(angle) * this.size / 4
                    );
                    this.ctx.lineTo(
                        this.size / 2 + Math.cos(angle) * this.size / 2,
                        this.size / 2 + Math.sin(angle) * this.size / 2
                    );
                    this.ctx.stroke();
                }
                break;
        }
    }
}

export default TileGenerator; 