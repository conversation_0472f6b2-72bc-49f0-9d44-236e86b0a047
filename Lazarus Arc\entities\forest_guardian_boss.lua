-- entities/forest_guardian_boss.lua
-- Forest Guardian Boss with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local ForestGuardianBoss = {
    id = "forest_guardian_boss",
    name = "Forest Guardian",
    type = "forest_guardian_boss",
    shape = {
        {0, -15}, {10, -10}, {15, 0}, {10, 10},
        {0, 15}, {-10, 10}, {-15, 0}, {-10, -10}
    },
    size = 20,  -- Massive boss size

    -- Entity categories
    categories = {"boss", "nature", "guardian", "ancient", "magical"},
    threatCategories = {"player", "undead", "corrupted", "destroyer"},
    
    -- Base stats (boss-level stats)
    maxHealth = 500,
    health = 500,
    maxStamina = 200,
    stamina = 200,
    maxMana = 300,
    mana = 300,
    speed = 2.0,  -- Slower but devastating
    attack = 40,
    defense = 25,
    magicAttack = 35,
    magicDefense = 30,
    natureConnection = 50,
    forestDominion = 25,
    
    -- Boss behaviors
    behaviors = {"boss_patrol", "nature_magic", "summon_minions", "phase_transition"},
    behaviorConfigs = {
        boss_patrol = {
            moveSpeed = 2.5,
            territoryRadius = 100,
            alertRadius = 50,
            neverRetreat = true
        },
        nature_magic = {
            spells = {"entangle", "thorn_volley", "healing_grove", "nature_wrath"},
            castChance = 0.4,
            manaCost = 50,
            areaEffect = true
        },
        summon_minions = {
            minionTypes = {"tree_ent", "vine_crawler", "forest_sprite"},
            maxMinions = 8,
            summonCooldown = 15,
            minionBonus = 1.5
        },
        phase_transition = {
            phases = {
                {healthThreshold = 0.75, abilities = {"root_slam", "thorn_barrier"}},
                {healthThreshold = 0.5, abilities = {"forest_rage", "nature_heal"}},
                {healthThreshold = 0.25, abilities = {"world_tree_avatar", "final_judgment"}}
            }
        }
    },
    
    -- Enhanced variant system for forest guardian boss
    variantChances = {
        normal = 0.60,          -- 60% ancient guardian
        shiny = 0.30,           -- 30% world tree guardian (shiny)
        rare = 0.08,            -- 8% corrupted guardian (rare)
        legendary = 0.02        -- 2% primordial guardian (legendary)
    },
    
    variants = {
        normal = {
            name = "Ancient Forest Guardian",
            description = "A massive tree-like being that protects the ancient forest",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.6, 0.8, 0.5, 1.0},  -- Natural green-brown
                ancient_bark = true
            }
        },
        
        shiny = {
            name = "World Tree Guardian",
            description = "A guardian connected to the cosmic world tree",
            statModifiers = {
                maxHealth = 1.5,    -- 750 health
                maxMana = 1.8,      -- 540 mana
                magicAttack = 1.6,  -- 56 magic attack
                defense = 1.4,      -- 35 defense
                cosmic_connection = 2.0
            },
            appearanceModifiers = {
                scale = 1.3,
                glow = true,
                colorTint = {0.8, 1.2, 0.8, 1.0},  -- Radiant green with golden light
                world_tree_connection = true,
                cosmic_branches = true,
                starlight_leaves = true
            },
            soundModifiers = {
                pitch = 0.8,
                volume = 1.4,
                cosmic_resonance = true
            }
        },
        
        rare = {
            name = "Corrupted Forest Guardian",
            description = "A guardian twisted by dark magic, now a threat to nature itself",
            statModifiers = {
                maxHealth = 1.8,    -- 900 health
                attack = 1.8,       -- 72 attack
                magicAttack = 1.7,  -- 59.5 magic attack
                corruption_power = 3.0,
                dark_magic = 2.0
            },
            appearanceModifiers = {
                scale = 1.2,
                colorTint = {0.6, 0.4, 0.7, 1.0},  -- Sickly purple-brown corruption
                corrupted_bark = true,
                withered_branches = true,
                dark_energy_veins = true,
                twisted_form = true
            },
            soundModifiers = {
                pitch = 0.6,
                volume = 1.5,
                reverb = true,
                corrupted_tone = true
            }
        },
        
        legendary = {
            name = "Primordial Forest Guardian",
            description = "The first guardian, created when the world was young",
            statModifiers = {
                maxHealth = 2.5,    -- 1250 health
                maxMana = 2.5,      -- 750 mana
                attack = 2.0,       -- 80 attack
                defense = 2.0,      -- 50 defense
                magicAttack = 2.2,  -- 77 magic attack
                primordial_power = 10.0,
                world_creation = 5.0
            },
            appearanceModifiers = {
                scale = 1.8,
                glow = true,
                colorTint = {1.0, 1.4, 1.0, 1.0},  -- Primordial green-gold energy
                primordial_aura = "legendary",
                creation_energy = true,
                reality_shaping = true,
                time_distortion = true
            },
            soundModifiers = {
                pitch = 0.4,
                volume = 2.0,
                reverb = true,
                echo = true,
                primordial_resonance = true
            }
        }
    },
    
    -- Boss drops (guaranteed valuable loot)
    baseDrops = {
        {id = "guardian_heart", chance = 1.0, quantity = {1, 1}},
        {id = "ancient_wood", chance = 1.0, quantity = {5, 10}},
        {id = "nature_essence", chance = 1.0, quantity = {3, 6}},
        {id = "forest_blessing", chance = 0.8, quantity = {1, 2}}
    },
    
    -- Variant-specific boss drops
    variantDrops = {
        shiny = {
            {id = "world_tree_branch", chance = 1.0, quantity = {1, 1}},
            {id = "cosmic_leaf", chance = 0.9, quantity = {2, 4}},
            {id = "starlight_essence", chance = 0.8, quantity = {1, 2}},
            {id = "world_connection_crystal", chance = 0.7, quantity = {1, 1}}
        },
        rare = {
            {id = "corrupted_guardian_core", chance = 1.0, quantity = {1, 1}},
            {id = "dark_nature_essence", chance = 0.9, quantity = {2, 3}},
            {id = "corruption_antidote", chance = 0.8, quantity = {1, 2}},
            {id = "twisted_wood", chance = 0.7, quantity = {3, 5}}
        },
        legendary = {
            {id = "primordial_guardian_seed", chance = 1.0, quantity = {1, 1}},
            {id = "creation_essence", chance = 1.0, quantity = {1, 2}},
            {id = "world_shaping_crystal", chance = 0.9, quantity = {1, 1}},
            {id = "primordial_wisdom_tome", chance = 0.8, quantity = {1, 1}},
            {id = "reality_anchor_root", chance = 0.7, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "forest_guardian_boss",
        scale = 1.5,
        animations = {
            "idle_sway", "root_slam", "nature_cast", "summon", "phase_change", "final_form"
        },
        variants = {
            "ancient_guardian", "world_tree_guardian", "corrupted_guardian", "primordial_guardian"
        }
    },
    
    -- Sound effects with boss characteristics
    sounds = {
        boss_roar = {
            file = "guardian_roar",
            synth = {
                instrument = "organ",
                notes = {"C1", "G1", "C2"},
                durations = {1.0, 0.8, 1.2},
                volume = 1.0,
                earth_shaking = true
            }
        },
        nature_magic = {
            file = "nature_magic",
            synth = {
                instrument = "harp",
                notes = {"F3", "A3", "C4", "F4", "A4"},
                durations = {0.4, 0.4, 0.4, 0.4, 0.8},
                volume = 0.8,
                magical = true
            }
        },
        root_slam = {
            file = "root_slam",
            synth = {
                instrument = "percussion",
                notes = {"C1", "C1", "C1"},
                durations = {0.3, 0.3, 0.6},
                volume = 1.2,
                earth_impact = true
            }
        },
        phase_transition = {
            file = "phase_transition",
            synth = {
                instrument = "choir",
                notes = {"D2", "F2", "A2", "D3", "F3"},
                durations = {0.8, 0.8, 0.8, 0.8, 1.2},
                volume = 0.9,
                transformation = true
            }
        }
    },
    
    -- Special boss abilities
    abilities = {
        nature_dominion = {
            type = "passive",
            description = "Controls all plant life in the area",
            effect = "environmental_control"
        },
        root_network = {
            type = "passive",
            description = "Can sense all movement through root network",
            effect = "area_awareness"
        },
        forest_regeneration = {
            type = "active",
            description = "Heals by drawing power from the forest",
            effect = "area_heal",
            cooldown = 30
        },
        nature_wrath = {
            type = "active",
            description = "Unleashes devastating nature magic",
            effect = "area_damage",
            cooldown = 20
        },
        guardian_resurrection = {
            type = "passive",
            description = "Can resurrect once per encounter if forest remains",
            effect = "second_life"
        }
    }
}

-- Initialize the forest guardian boss entity using enhanced template
function ForestGuardianBoss.init(entity, world)
    -- Copy all fields from ForestGuardianBoss template to entity instance
    for k, v in pairs(ForestGuardianBoss) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return ForestGuardianBoss
