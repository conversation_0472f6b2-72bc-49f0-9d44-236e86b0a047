local Flamingo = {
    id = "flamingo",
    name = "Flamingo",
    type = "bird",
    
    -- Entity categories
    categories = {"animal", "bird", "flying", "water", "wading", "social"},
    
    -- Threat and food categories
    threatCategories = {"player", "predator"},
    foodCategories = {"algae", "insect", "crustacean", "plant"},
    
    -- Stats
    maxHealth = 35,
    health = 35,
    maxStamina = 60,
    stamina = 60,
    speed = 1.5,
    
    -- Flight properties
    flight = {
        maxHeight = 10,
        minHeight = 1,
        ascentSpeed = 0.7,
        descentSpeed = 1.2,
        hoverHeight = 2,
        currentHeight = 2,
        wingFlapRate = 0.15,
        soarChance = 0.2
    },
    
    -- Water properties
    water = {
        wadeDepth = 1.5,
        swimSpeed = 1.0,
        filterRange = 3,
        filterSuccess = 0.6
    },
    
    -- Behaviors
    behaviors = {"filter", "flock", "roost", "display"},
    
    -- Behavior configurations
    behaviorConfigs = {
        filter = {
            moveSpeed = 0.8,
            searchRadius = 8,
            preferredFood = {"algae", "insect", "crustacean"},
            filterRate = 0.5,
            successChance = 0.6
        },
        flock = {
            moveSpeed = 1.5,
            followDistance = 2,
            separationDistance = 1,
            alignmentStrength = 0.3,
            cohesionStrength = 0.4,
            maxGroupSize = 20,
            preferredTime = "day"
        },
        roost = {
            startTime = "dusk",
            endTime = "dawn",
            roostHeight = {1, 2},
            healthRegen = 0.02,
            staminaRegen = 0.1
        },
        display = {
            moveSpeed = 1.2,
            duration = 3,
            staminaCost = 8,
            attractionRadius = 6,
            preferredTime = "dawn"
        }
    },
    
    -- Special abilities
    abilities = {
        filter = {
            duration = 2,
            cooldown = 1,
            staminaCost = 3,
            successChance = 0.6
        },
        preen = {
            duration = 3,
            cooldown = 5,
            healthRegen = 0.05
        },
        call = {
            range = 10,
            duration = 0.5,
            cooldown = 2,
            effect = "attract"
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "flamingo",
        scale = 0.9,
        animations = {
            "idle", "fly", "wade", "filter", "preen", "display"
        },
        variants = {
            "greater", "lesser", "chilean", "caribbean"
        }
    },
    
    -- Sound effects
    sounds = {
        call = "flamingo_call",
        wingFlap = "flamingo_wing_flap",
        filter = "flamingo_filter"
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.5, quantity = {1, 2}},
        {id = "feather", chance = 0.6, quantity = {1, 3}},
        {id = "beak", chance = 0.3, quantity = {1, 1}},
        {id = "algae", chance = 0.4, quantity = {1, 2}}
    }
}

-- Initialize the entity
function Flamingo.init(entity, world)
    -- Copy all fields from Flamingo template to entity instance
    for k, v in pairs(Flamingo) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random flamingo variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Flamingo.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update flight properties
    if entity.flight then
        -- Update wing flapping based on height and activity
        if entity.flight.currentHeight > entity.flight.minHeight then
            if entity.flight.soarChance > math.random() then
                entity.flight.wingFlapRate = 0.08
            else
                entity.flight.wingFlapRate = 0.15
            end
        else
            entity.flight.wingFlapRate = 0.25
        end
    end

    -- Update water properties
    if entity.water then
        -- Adjust filter success based on time of day
        if world and world.timeOfDay == "dawn" or world.timeOfDay == "dusk" then
            entity.water.filterSuccess = 0.7
        else
            entity.water.filterSuccess = 0.6
        end
    end
end

return Flamingo 