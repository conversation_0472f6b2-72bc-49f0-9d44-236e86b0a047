-- biomes/caves.lua
CavesBiome = {
    id = "caves",
    name = "Caves",
    description = "A network of underground tunnels and chambers with unique creatures and features.", -- [cite: 699]

    -- Environmental factors (Cool, damp, dark, still air)
    environment = {
        humidity = 0.8,
        temperature = 0.3, -- Represents cool, stable temperature
        sunlight = 0.0,    -- No sunlight
        windStrength = 0.05 -- Very little air movement usually
    },

    -- Core tile types used in this biome
    -- NOTE: 'cave_wall' is essential (impassable). 'underground_river', 'crystal_formations', 'bottomless_pit' need defining.
    primaryTiles = {"cave_floor", "cave_wall"}, -- Floor and the impassable walls defining the cave
    secondaryTiles = {"water", "shallow_water"}, -- Representing underground rivers/lakes
    rareTiles = {"crystal_formations", "ancient_artifacts_tile", "bottomless_pit"}, -- Artifacts could be tile or entity

    -- Default proportions (These represent passable areas primarily)
    tileDistribution = {
        cave_floor = 0.8, -- Floor area within passages
        water = 0.1, -- Underground rivers/lakes within passages
        shallow_water = 0.05,
        crystal_formations = 0.03,
        ancient_artifacts_tile = 0.01,
        bottomless_pit = 0.01
        -- cave_wall makes up the rest implicitly via generation algorithm
    },

    -- Entities common to this biome
    commonEntities = {
        "cave_bat",
        "spider",
        "giant_insect",
        "cave_rat" -- Added rat
    },
    uncommonEntities = {
        "troglodyte",
        "cave_fish", -- in water
        "giant_centipede",
        "slime" -- Added centipede/slime
    },
    rareEntities = {
        "cave_troll",
        "deep_dweller",
        "ancient_cave_guardian" -- Added troll/dweller/guardian
    },

    -- Cave-specific environmental properties (similar to Crystal Cavern)
    isUnderground = true,
    naturalLight = 0.0, -- Almost zero unless near entrance or from bioluminescence
    caveAmbience = true,
    echoEffect = true,
    hasBreathableAir = true, -- Usually
    ceilingHeight = {min = 2, max = 10},

    -- Biome variants for diverse generation
    variants = {
        limestone_caves = {
            name = "Limestone Caves",
            humidity = 0.9,
            tileAdjustments = {
                 cave_floor = 0.7,
                 water = 0.15, -- More dripping water/pools
                 stalactite_feature_chance = 0.4, -- Increase feature chance
                 stalagmite_feature_chance = 0.4
            },
            entityAdjustments = { cave_bat = 1.5, slime = 1.2 },
            specialEffects = {"constant_dripping", "echoing_sounds"}
        },
        crystal_caves = { -- Could potentially reuse Crystal Cavern definition?
            name = "Crystal Caves",
            naturalLight = 0.3, -- From glowing crystals
            tileAdjustments = {
                cave_floor = 0.5,
                crystal_formations = 0.4, -- Much more crystal
                water = 0.05
            },
            entityAdjustments = { crystal_golem = 1.5, mana_sprite = 1.3 },
            specialEffects = {"crystal_glow", "mana_resonance"}
        },
        underground_jungle = {
             name = "Underground Jungle",
             humidity = 0.95,
             temperature = 0.6, -- Warmer than other caves
             naturalLight = 0.1, -- Bioluminescent plants
             tileAdjustments = {
                  cave_floor = 0.4, -- More dirt/mud floor?
                  dense_vegetation_cave = 0.3, -- Need cave vegetation tile/entities
                  water = 0.15,
                  glowing_mushroom_chance = 0.5
             },
             entityAdjustments = { giant_insect = 1.5, spider = 1.3, cave_lizard_large = 1.0 },
             specialEffects = {"bioluminescence", "thick_vines", "humid_air"}
        },
        ice_caves = { -- Connects to Tundra
             name = "Ice Caves",
             temperature = 0.0, -- Freezing
             humidity = 0.5,
             tileAdjustments = {
                 ice_floor = 0.6, -- Need ice floor tile
                 ice = 0.2, -- Frozen water features
                 cave_floor = 0.15, -- Some non-ice floor
             },
             entityAdjustments = { ice_wraith = 1.5, cave_bat = 0.2, frozen_creature = 1.0 },
             specialEffects = {"frozen_waterfalls", "subzero_temps", "slippery_floors"}
        }
        -- Could also add Lava Tubes variant connecting to Volcanic
    },

    -- Structures that can generate in this biome
    structures = {
        {
            name = "goblin_camp",
            chance = 0.08,
            entities = {"goblin_scout", "goblin_shaman", "cave_rat_pet"}
        },
        {
            name = "dwarven_city", -- Likely a very large, rare structure spanning chunks
            chance = 0.01,
            unique = true, -- Unique per region
            entities = {"dwarf_guard", "dwarf_miner", "dwarf_artisan", "city_golem"}
        },
        {
            name = "abandoned_mine", -- Similar to other biomes but adapted
            chance = 0.1,
            entities = {"giant_spider_nest", "ghost_miner", "ore_vein_cave"}
        },
        {
            name = "hidden_temple", -- Temple to underground deities?
            chance = 0.04,
            entities = {"cultist", "temple_guardian_statue", "dark_altar"}
        }
    },

    -- No weather, use environmental effects
    environmentalEffects = {
        water_drips = 0.4, -- Chance of dripping water effect
        echoes = 0.8,      -- Sound propagation effect
        cave_ins = 0.03,   -- Chance of minor rockfall event
        still_air = 0.9    -- Modifier reducing wind effects
    },

    -- Unique features generation
    features = {
        {
            name = "underground_lake", -- Large area of water tiles
            chance = 0.1,
            size = {min=10, max=30}
        },
        {
            name = "hidden_city", -- Very large structure/feature, maybe tied to dwarven city?
            chance = 0.01,
            unique = true
        },
        {
            name = "glowing_mushrooms", -- Feature placing glowing mushroom entities/patches
            chance = 0.2,
            size = {min=3, max=8}
        },
        {
            name = "bottomless_pit", -- Places hazard tiles or impassable void
            chance = 0.05,
            danger = true
        },
        {
            name = "ancient_artifacts", -- Feature placing artifact entities/tiles
            chance = 0.06
        },
        -- Added stalactites/stalagmites as features placing decorations
        {
             name = "stalactite_cluster",
             chance = 0.25
             -- Places stalactite decorations on ceiling tiles
        },
        {
             name = "stalagmite_field",
             chance = 0.25
             -- Places stalagmite decorations on floor tiles
        }
    },

    -- Generation algorithm (Needs specific cave generation logic)
    generate = function(chunk, world, variant)
        print("Generating " .. (variant and CavesBiome.variants[variant].name or "Caves") .. " biome")
        local variantData = variant and CavesBiome.variants[variant] or nil

        -- Adjust tile distribution
        local tileDistribution = {}
        for tileType, chance in pairs(CavesBiome.tileDistribution) do
            tileDistribution[tileType] = chance
            if variantData and variantData.tileAdjustments and variantData.tileAdjustments[tileType] then
                tileDistribution[tileType] = variantData.tileAdjustments[tileType]
            end
        end

        -- Noise functions
        local seed = world.seed + (chunk.x * 389 + chunk.y * 751)
        math.randomseed(seed)
        local function pnoise2D(x, y, frequency, seed_offset) return love.math.noise(x * frequency, y * frequency, (world.seed + (seed_offset or 0)) * 0.01) end

        -- *** Cave Generation Logic Placeholder ***
        -- This needs a proper algorithm (e.g., Cellular Automata, Perlin Worms)
        -- to carve passages (cave_floor) out of solid rock (cave_wall).
        -- The simple noise check used in surface biomes won't create realistic caves.
        -- For now, we'll use noise to *crudely* designate floor vs wall.

        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                local worldX = chunk.x * world.CHUNK_SIZE + x
                local worldY = chunk.y * world.CHUNK_SIZE + y

                local caveShapeNoise = pnoise2D(worldX, worldY, 0.1, 1) -- Controls open space vs wall
                local floorTypeNoise = pnoise2D(worldX, worldY, 0.2, 2) -- Controls floor variations

                local tileType = "cave_wall" -- Default is solid wall
                local isPassage = false

                if caveShapeNoise > 0.1 then -- Threshold for open space
                    isPassage = true
                    tileType = "cave_floor" -- Default floor

                    -- Determine floor type based on distribution and noise
                    local tileRoll = math.random()
                    local cumulativeChance = 0
                    for tType, chance in pairs(tileDistribution) do
                         cumulativeChance = cumulativeChance + chance
                         if tileRoll <= cumulativeChance and tType ~= "cave_wall" then
                              -- Allow placement of water, crystal, pit etc. based on distribution
                              tileType = tType
                              break
                         end
                    end

                     -- Maybe refine based on floorTypeNoise
                     if tileType == "cave_floor" then
                          if floorTypeNoise < -0.4 then tileType = "water"
                          elseif floorTypeNoise > 0.6 then tileType = "crystal_formations" end
                     end
                end

                -- Create the tile
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = math.random(1, 3),
                    isCave = true,
                    isPassage = isPassage,
                    biome = "caves",
                    variant = variant
                }

                -- Add variant-specific properties
                if variantData then
                    tile.variantData = variantData
                end

                -- Add to chunk
                chunk.tiles[x][y] = tile
            end
        end

        return chunk
    end,

    -- Initialize the biome
    init = function(biome, world)
        -- Initialize biome properties
        biome.properties = biome.properties or {}
        
        -- Copy all fields from CavesBiome template to biome instance
        for k, v in pairs(CavesBiome) do
            if type(v) ~= "function" and biome[k] == nil then
                if type(v) == "table" then
                    -- Deep copy for tables
                    biome[k] = {}
                    for subk, subv in pairs(v) do
                        biome[k][subk] = subv
                    end
                else
                    biome[k] = v
                end
            end
        end

        return biome
    end
}

return CavesBiome