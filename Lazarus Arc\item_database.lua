-- item_database.lua
-- Dynamic item repository for Lazarus Arc

local ItemDatabase = {
    templates = {},     -- Static item definitions (non-instanced)
    loadedFiles = {},   -- Track loaded files to prevent re-require
    categories = {},    -- Maps categories to itemIds
}

local UUID = require("utils.uuid")
local lfs = love.filesystem
local json = require("lunajson") -- Make sure you have lunajson or similar

-- Load all item files from the items directory
function ItemDatabase.loadAll()
    print("Loading all items from /items/")

    local function recursiveLoad(path)
        if not lfs.getInfo(path, "directory") then return end
        local files = lfs.getDirectoryItems(path)

        for _, file in ipairs(files) do
            local fullPath = path .. "/" .. file
            local info = lfs.getInfo(fullPath)
            if info.type == "file" and file:match("%.lua$") then
                local itemId = file:gsub("%.lua$", "")
                local requirePath = fullPath:gsub("%.lua$", ""):gsub("/", ".")

                if not ItemDatabase.loadedFiles[requirePath] then
                    local success, item = pcall(require, requirePath)
                    if success and type(item) == "table" then
                        item.id = itemId
                        item.category = item.category or "misc"
                        ItemDatabase.templates[itemId] = item
                        ItemDatabase.categories[item.category] = ItemDatabase.categories[item.category] or {}
                        table.insert(ItemDatabase.categories[item.category], itemId)
                        ItemDatabase.loadedFiles[requirePath] = true
                        print("Loaded item:", itemId)
                    else
                        print("Failed to load item:", requirePath)
                    end
                end
            elseif info.type == "directory" then
                recursiveLoad(fullPath)
            end
        end
    end

    recursiveLoad("items")
    print("Item database loaded:", ItemDatabase.getItemCount(), "items.")
end

-- Get static item template
function ItemDatabase.getTemplate(itemId)
    return ItemDatabase.templates[itemId]
end

-- Create a new unique item from template
function ItemDatabase.createItemInstance(itemId)
    local base = ItemDatabase.getTemplate(itemId)
    if not base then return nil end

    return {
        uuid = UUID.generate(),
        baseId = itemId,
        name = base.name,
        category = base.category,
        stats = base.stats and table.deepcopy(base.stats) or {},
        createdAt = os.time(),
        stackable = base.stackable or false,
        value = base.value or 0,
        identified = base.identified ~= false,
        meta = base.meta or {}
    }
end

-- Count of items
function ItemDatabase.getItemCount()
    local count = 0
    for _ in pairs(ItemDatabase.templates) do count = count + 1 end
    return count
end

-- Save inventory items to file
function ItemDatabase.saveInventory(playerId, inventory)
    local filename = "saves/" .. playerId .. "_inventory.json"
    local data = json.encode(inventory)
    lfs.write(filename, data)
    print("Saved inventory for player:", playerId)
end

-- Load inventory items from file
function ItemDatabase.loadInventory(playerId)
    local filename = "saves/" .. playerId .. "_inventory.json"
    if lfs.getInfo(filename) then
        local data = lfs.read(filename)
        local items = json.decode(data)
        print("Loaded inventory for player:", playerId)
        return items
    end
    return {}
end

-- Optional: Get all items in a category
function ItemDatabase.getItemsByCategory(category)
    return ItemDatabase.categories[category] or {}
end

return ItemDatabase
