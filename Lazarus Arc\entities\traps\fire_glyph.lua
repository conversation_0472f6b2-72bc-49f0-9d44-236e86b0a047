local FireGlyph = {
    id = "fire_glyph",
    name = "Fire Glyph",
    type = "trap",
    
    -- Entity categories
    categories = {"trap", "dungeon", "fire", "magic"},
    
    -- Target categories
    targetCategories = {"player", "monster"},
    
    -- Stats
    maxHealth = 20,
    health = 20,
    duration = 15,
    cooldown = 30,
    
    -- Trap properties
    properties = {
        radius = 2.0,
        setupTime = 2,
        isArmed = false,
        isTriggered = false,
        destroyOnActivate = false,
        burnDamage = 8,
        burnDuration = 5,
        flameRadius = 1.5,
        flameDamage = 4
    },
    
    -- Appearance
    appearance = {
        sprite = "fire_glyph",
        scale = 1.0,
        animations = {
            "idle", "trigger", "burn"
        },
        variants = {
            "red", "blue", "purple", "gold"
        }
    },
    
    -- Sound effects
    sounds = {
        trigger = "fire_glyph_trigger",
        burn = "fire_glyph_burn",
        flame = "fire_glyph_flame"
    },
    
    -- Effects
    effects = {
        burn = {
            type = "status",
            duration = 5,
            effects = {
                damage = 8,
                interval = 1,
                spread = true
            }
        },
        flame = {
            type = "area",
            radius = 1.5,
            effects = {
                damage = 4,
                interval = 0.5,
                burn = {
                    type = "status",
                    duration = 3,
                    effects = {
                        damage = 2,
                        interval = 1
                    }
                }
            }
        }
    }
}

-- Initialize the trap
function FireGlyph.init(entity, world)
    -- Copy all fields from FireGlyph template to entity instance
    for k, v in pairs(FireGlyph) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position
    entity.position = entity.position or {x = 0, y = 0}

    -- Set random variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize trap state
    entity.properties.isArmed = false
    entity.properties.isTriggered = false
    entity.properties.triggerTime = 0
    entity.properties.flameActive = false

    return entity
end

-- Update the trap
function FireGlyph.update(entity, world, dt)
    -- Check if trap is armed and not triggered
    if entity.properties.isArmed and not entity.properties.isTriggered then
        -- Check for targets in radius
        if world and world.entities then
            for _, target in ipairs(world.entities) do
                if target.position and target.categories then
                    -- Check if target is in trap categories
                    local isTarget = false
                    for _, category in ipairs(entity.targetCategories) do
                        if table.contains(target.categories, category) then
                            isTarget = true
                            break
                        end
                    end
                    
                    if isTarget then
                        local distance = math.sqrt(
                            (target.position.x - entity.position.x)^2 + 
                            (target.position.y - entity.position.y)^2
                        )
                        
                        if distance <= entity.properties.radius then
                            -- Trigger trap
                            entity.properties.isTriggered = true
                            entity.properties.triggerTime = world.time
                            entity.properties.flameActive = true
                            
                            -- Apply burn effect to target
                            if target.applyEffect then
                                target.applyEffect(entity.effects.burn)
                            end
                            
                            -- Create flame effect
                            if world.createEffect then
                                world.createEffect({
                                    type = "flame",
                                    position = entity.position,
                                    radius = entity.properties.flameRadius,
                                    effects = entity.effects.flame.effects
                                })
                            end
                            
                            -- Play trigger sound
                            if world.playSound then
                                world.playSound(entity.sounds.trigger)
                            end
                            
                            break
                        end
                    end
                end
            end
        end
    end
    
    -- Handle flame effect
    if entity.properties.flameActive then
        if world and world.entities then
            for _, target in ipairs(world.entities) do
                if target.position then
                    local distance = math.sqrt(
                        (target.position.x - entity.position.x)^2 + 
                        (target.position.y - entity.position.y)^2
                    )
                    
                    if distance <= entity.properties.flameRadius then
                        -- Apply flame damage
                        if target.takeDamage then
                            target.takeDamage(entity.properties.flameDamage)
                        end
                        
                        -- Apply burn effect
                        if target.applyEffect then
                            target.applyEffect(entity.effects.flame.effects.burn)
                        end
                    end
                end
            end
        end
        
        -- Check if flame should dissipate
        if world.time - entity.properties.triggerTime >= entity.duration then
            entity.properties.flameActive = false
        end
    end
    
    -- Check if trap can be rearmed
    if not entity.properties.isArmed and world.time - entity.properties.triggerTime >= entity.cooldown then
        entity.properties.isArmed = true
        entity.properties.isTriggered = false
        entity.properties.flameActive = false
    end
end

-- Arm the trap
function FireGlyph.arm(entity, world)
    if not entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = true
        entity.properties.triggerTime = world.time
        entity.properties.flameActive = false
        return true
    end
    return false
end

-- Disarm the trap
function FireGlyph.disarm(entity, world)
    if entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = false
        entity.properties.triggerTime = world.time
        entity.properties.flameActive = false
        return true
    end
    return false
end

return FireGlyph 