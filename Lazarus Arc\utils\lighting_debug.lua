-- utils/lighting_debug.lua
-- Lighting debug visualization

local LightingDebug = {}

-- Initialize lighting debug
function LightingDebug.init()
    LightingDebug.enabled = false
    
    -- Display settings
    LightingDebug.showNormals = true
    LightingDebug.showLightSources = true
    LightingDebug.showShadows = true
    LightingDebug.showLightRays = true
    LightingDebug.showAmbientLight = true
    
    -- Visual settings
    LightingDebug.normalLength = 20
    LightingDebug.lightRadius = 100
    LightingDebug.rayCount = 16
    
    -- Colors
    LightingDebug.colors = {
        normal = {0, 1, 0, 0.8}, -- Green for normals
        lightSource = {1, 1, 0, 1}, -- Yellow for light sources
        lightRay = {1, 1, 1, 0.3}, -- White for light rays
        shadow = {0, 0, 1, 0.5}, -- Blue for shadows
        ambient = {0.5, 0.5, 1, 0.2} -- Light blue for ambient
    }
    
    -- Light sources (for debugging)
    LightingDebug.lightSources = {}
    LightingDebug.shadowCasters = {}
end

-- Toggle lighting debug
function LightingDebug.toggle()
    LightingDebug.enabled = not LightingDebug.enabled
    
    if LightingDebug.enabled then
        print("Lighting Debug enabled")
    else
        print("Lighting Debug disabled")
    end
    
    return LightingDebug.enabled
end

-- Update lighting debug (collect light sources and shadow casters)
function LightingDebug.update(world, entities)
    if not LightingDebug.enabled then return end
    
    -- Clear previous data
    LightingDebug.lightSources = {}
    LightingDebug.shadowCasters = {}
    
    -- Collect light sources from world
    LightingDebug.collectLightSources(world)
    
    -- Collect light sources from entities
    if entities then
        for _, entity in ipairs(entities) do
            LightingDebug.collectEntityLights(entity)
        end
    end
    
    -- Collect shadow casters
    LightingDebug.collectShadowCasters(world, entities)
end

-- Collect light sources from world
function LightingDebug.collectLightSources(world)
    -- Check for world lighting system
    if world.lightingSystem and world.lightingSystem.lights then
        for _, light in ipairs(world.lightingSystem.lights) do
            table.insert(LightingDebug.lightSources, {
                x = light.x or light.position.x,
                y = light.y or light.position.y,
                radius = light.radius or 100,
                intensity = light.intensity or 1,
                color = light.color or {1, 1, 1},
                type = light.type or "point"
            })
        end
    end
    
    -- Check for structures with lights
    if world.structures then
        for _, structure in ipairs(world.structures) do
            if structure.hasLight or structure.lightSource then
                table.insert(LightingDebug.lightSources, {
                    x = structure.position.x,
                    y = structure.position.y,
                    radius = structure.lightRadius or 50,
                    intensity = structure.lightIntensity or 0.8,
                    color = structure.lightColor or {1, 0.8, 0.6},
                    type = "structure"
                })
            end
        end
    end
    
    -- Add default sun light if no other lights
    if #LightingDebug.lightSources == 0 then
        table.insert(LightingDebug.lightSources, {
            x = 0,
            y = -1000, -- High up for sun
            radius = 2000,
            intensity = 0.8,
            color = {1, 1, 0.9},
            type = "sun"
        })
    end
end

-- Collect light sources from entities
function LightingDebug.collectEntityLights(entity)
    if entity.lightSource or entity.hasLight then
        table.insert(LightingDebug.lightSources, {
            x = entity.position.x,
            y = entity.position.y,
            radius = entity.lightRadius or 30,
            intensity = entity.lightIntensity or 0.6,
            color = entity.lightColor or {1, 0.9, 0.7},
            type = "entity"
        })
    end
    
    -- Check for torch or lantern items
    if entity.inventory then
        for _, item in ipairs(entity.inventory) do
            if item.type == "torch" or item.type == "lantern" then
                table.insert(LightingDebug.lightSources, {
                    x = entity.position.x,
                    y = entity.position.y,
                    radius = item.lightRadius or 40,
                    intensity = item.lightIntensity or 0.7,
                    color = item.lightColor or {1, 0.7, 0.3},
                    type = "item"
                })
            end
        end
    end
end

-- Collect shadow casters
function LightingDebug.collectShadowCasters(world, entities)
    -- Collect from structures
    if world.structures then
        for _, structure in ipairs(world.structures) do
            if structure.castsShadow ~= false then -- Default to casting shadows
                table.insert(LightingDebug.shadowCasters, {
                    x = structure.position.x,
                    y = structure.position.y,
                    width = structure.width or 32,
                    height = structure.height or 32,
                    type = "structure"
                })
            end
        end
    end
    
    -- Collect from entities
    if entities then
        for _, entity in ipairs(entities) do
            if entity.castsShadow ~= false and entity.position then
                table.insert(LightingDebug.shadowCasters, {
                    x = entity.position.x,
                    y = entity.position.y,
                    width = entity.width or 16,
                    height = entity.height or 16,
                    type = "entity"
                })
            end
        end
    end
    
    -- Collect from terrain (simplified)
    if world.tiles then
        for x, column in pairs(world.tiles) do
            for y, tile in pairs(column) do
                if tile.solid or tile.height and tile.height > 0 then
                    table.insert(LightingDebug.shadowCasters, {
                        x = x * 32, -- Assuming 32x32 tiles
                        y = y * 32,
                        width = 32,
                        height = 32,
                        type = "terrain"
                    })
                end
            end
        end
    end
end

-- Draw lighting debug information
function LightingDebug.draw(camera)
    if not LightingDebug.enabled then return end
    
    love.graphics.push()
    
    -- Apply camera transform if available
    if camera then
        love.graphics.translate(-camera.x, -camera.y)
    end
    
    -- Draw ambient light
    if LightingDebug.showAmbientLight then
        LightingDebug.drawAmbientLight()
    end
    
    -- Draw light sources
    if LightingDebug.showLightSources then
        LightingDebug.drawLightSources()
    end
    
    -- Draw light rays
    if LightingDebug.showLightRays then
        LightingDebug.drawLightRays()
    end
    
    -- Draw shadows
    if LightingDebug.showShadows then
        LightingDebug.drawShadows()
    end
    
    -- Draw normals
    if LightingDebug.showNormals then
        LightingDebug.drawNormals()
    end
    
    love.graphics.pop()
end

-- Draw ambient light overlay
function LightingDebug.drawAmbientLight()
    love.graphics.setColor(LightingDebug.colors.ambient)
    
    -- Get screen bounds
    local screenWidth = love.graphics.getWidth()
    local screenHeight = love.graphics.getHeight()
    
    -- Draw ambient light overlay
    love.graphics.rectangle("fill", -screenWidth, -screenHeight, screenWidth * 3, screenHeight * 3)
end

-- Draw light sources
function LightingDebug.drawLightSources()
    for _, light in ipairs(LightingDebug.lightSources) do
        -- Draw light radius
        love.graphics.setColor(light.color[1], light.color[2], light.color[3], 0.2)
        love.graphics.circle("fill", light.x, light.y, light.radius)
        
        -- Draw light source marker
        love.graphics.setColor(LightingDebug.colors.lightSource)
        love.graphics.circle("fill", light.x, light.y, 8)
        
        -- Draw light source info
        love.graphics.setColor(1, 1, 1, 1)
        love.graphics.print(light.type, light.x + 10, light.y - 10)
        love.graphics.print(string.format("I:%.1f R:%d", light.intensity, light.radius), light.x + 10, light.y + 5)
    end
end

-- Draw light rays
function LightingDebug.drawLightRays()
    love.graphics.setColor(LightingDebug.colors.lightRay)
    love.graphics.setLineWidth(1)
    
    for _, light in ipairs(LightingDebug.lightSources) do
        if light.type ~= "sun" then -- Don't draw rays for sun light
            -- Draw rays in multiple directions
            for i = 0, LightingDebug.rayCount - 1 do
                local angle = (i / LightingDebug.rayCount) * math.pi * 2
                local endX = light.x + math.cos(angle) * light.radius
                local endY = light.y + math.sin(angle) * light.radius
                
                -- Check for shadow caster intersection
                local hitX, hitY = LightingDebug.raycastToShadow(light.x, light.y, endX, endY)
                
                love.graphics.line(light.x, light.y, hitX or endX, hitY or endY)
            end
        end
    end
end

-- Draw shadows
function LightingDebug.drawShadows()
    love.graphics.setColor(LightingDebug.colors.shadow)
    
    for _, caster in ipairs(LightingDebug.shadowCasters) do
        -- Draw shadow caster outline
        love.graphics.rectangle("line", caster.x - caster.width/2, caster.y - caster.height/2, 
                               caster.width, caster.height)
        
        -- Calculate and draw shadows for each light source
        for _, light in ipairs(LightingDebug.lightSources) do
            if light.type ~= "sun" then
                local shadowPoints = LightingDebug.calculateShadow(caster, light)
                if shadowPoints and #shadowPoints >= 6 then
                    love.graphics.polygon("fill", shadowPoints)
                end
            end
        end
    end
end

-- Draw surface normals
function LightingDebug.drawNormals()
    love.graphics.setColor(LightingDebug.colors.normal)
    love.graphics.setLineWidth(2)
    
    for _, caster in ipairs(LightingDebug.shadowCasters) do
        -- Draw normals for each face of the shadow caster
        local faces = {
            {x1 = caster.x - caster.width/2, y1 = caster.y - caster.height/2, 
             x2 = caster.x + caster.width/2, y2 = caster.y - caster.height/2, normal = {0, -1}}, -- Top
            {x1 = caster.x + caster.width/2, y1 = caster.y - caster.height/2, 
             x2 = caster.x + caster.width/2, y2 = caster.y + caster.height/2, normal = {1, 0}}, -- Right
            {x1 = caster.x + caster.width/2, y1 = caster.y + caster.height/2, 
             x2 = caster.x - caster.width/2, y2 = caster.y + caster.height/2, normal = {0, 1}}, -- Bottom
            {x1 = caster.x - caster.width/2, y1 = caster.y + caster.height/2, 
             x2 = caster.x - caster.width/2, y2 = caster.y - caster.height/2, normal = {-1, 0}} -- Left
        }
        
        for _, face in ipairs(faces) do
            local centerX = (face.x1 + face.x2) / 2
            local centerY = (face.y1 + face.y2) / 2
            local normalEndX = centerX + face.normal[1] * LightingDebug.normalLength
            local normalEndY = centerY + face.normal[2] * LightingDebug.normalLength
            
            love.graphics.line(centerX, centerY, normalEndX, normalEndY)
            
            -- Draw arrow head
            local arrowSize = 5
            local arrowAngle = math.atan2(face.normal[2], face.normal[1])
            local arrow1X = normalEndX - arrowSize * math.cos(arrowAngle - 0.5)
            local arrow1Y = normalEndY - arrowSize * math.sin(arrowAngle - 0.5)
            local arrow2X = normalEndX - arrowSize * math.cos(arrowAngle + 0.5)
            local arrow2Y = normalEndY - arrowSize * math.sin(arrowAngle + 0.5)
            
            love.graphics.line(normalEndX, normalEndY, arrow1X, arrow1Y)
            love.graphics.line(normalEndX, normalEndY, arrow2X, arrow2Y)
        end
    end
end

-- Raycast to find shadow intersection
function LightingDebug.raycastToShadow(startX, startY, endX, endY)
    for _, caster in ipairs(LightingDebug.shadowCasters) do
        local hit = LightingDebug.lineRectIntersection(startX, startY, endX, endY, 
                                                      caster.x - caster.width/2, caster.y - caster.height/2,
                                                      caster.width, caster.height)
        if hit then
            return hit.x, hit.y
        end
    end
    return nil, nil
end

-- Line-rectangle intersection
function LightingDebug.lineRectIntersection(x1, y1, x2, y2, rectX, rectY, rectW, rectH)
    -- Simple AABB line intersection
    local minX, maxX = math.min(x1, x2), math.max(x1, x2)
    local minY, maxY = math.min(y1, y2), math.max(y1, y2)
    
    if maxX < rectX or minX > rectX + rectW or maxY < rectY or minY > rectY + rectH then
        return nil
    end
    
    -- Return approximate intersection point
    return {x = (x1 + x2) / 2, y = (y1 + y2) / 2}
end

-- Calculate shadow polygon for a caster and light
function LightingDebug.calculateShadow(caster, light)
    -- Simplified shadow calculation
    local dx = caster.x - light.x
    local dy = caster.y - light.y
    local distance = math.sqrt(dx * dx + dy * dy)
    
    if distance > light.radius then
        return nil -- Too far from light
    end
    
    -- Calculate shadow projection
    local shadowLength = 100
    local shadowEndX = caster.x + (dx / distance) * shadowLength
    local shadowEndY = caster.y + (dy / distance) * shadowLength
    
    -- Create shadow polygon (simplified)
    return {
        caster.x - caster.width/2, caster.y - caster.height/2,
        caster.x + caster.width/2, caster.y - caster.height/2,
        shadowEndX + caster.width/2, shadowEndY - caster.height/2,
        shadowEndX - caster.width/2, shadowEndY - caster.height/2
    }
end

-- Toggle display options
function LightingDebug.toggleNormals()
    LightingDebug.showNormals = not LightingDebug.showNormals
    return LightingDebug.showNormals
end

function LightingDebug.toggleLightSources()
    LightingDebug.showLightSources = not LightingDebug.showLightSources
    return LightingDebug.showLightSources
end

function LightingDebug.toggleShadows()
    LightingDebug.showShadows = not LightingDebug.showShadows
    return LightingDebug.showShadows
end

function LightingDebug.toggleLightRays()
    LightingDebug.showLightRays = not LightingDebug.showLightRays
    return LightingDebug.showLightRays
end

function LightingDebug.toggleAmbientLight()
    LightingDebug.showAmbientLight = not LightingDebug.showAmbientLight
    return LightingDebug.showAmbientLight
end

-- Add custom light source for testing
function LightingDebug.addTestLight(x, y, radius, intensity, color)
    table.insert(LightingDebug.lightSources, {
        x = x,
        y = y,
        radius = radius or 100,
        intensity = intensity or 1,
        color = color or {1, 1, 1},
        type = "test"
    })
end

return LightingDebug
