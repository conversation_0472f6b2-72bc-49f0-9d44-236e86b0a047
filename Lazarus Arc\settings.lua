-- settings.lua
-- Global game settings and configurations

local Settings = {
    -- Display Settings
    display = {
        screenWidth = 1280,
        screenHeight = 720,
        fullscreen = false,
        vsync = true,
        scale = 1.0,
        showFPS = true,
        pixelPerfect = true,
        maxFPS = 60
    },

    -- Debug Settings
    debug = {
        enabled = true,
        showGrid = true,
        showChunks = true,
        showBounds = true,
        showEntityInfo = true,
        showCollisionBoxes = true,
        showPathfinding = true,
        showPerformance = true,
        colors = {
            grid = {0.5, 0.5, 0.5, 0.3},
            chunks = {1, 0, 0, 0.2},
            bounds = {0, 1, 0, 0.2},
            collision = {1, 0, 0, 0.3},
            pathfinding = {0, 0, 1, 0.3},
            text = {1, 1, 1, 1}
        },
        -- Module toggles
        modules = {
            weather = true,      -- Weather system
            entities = true,     -- All entities except player
            player = true,       -- Player entity
            effects = true,      -- Visual effects
            particles = true,    -- Particle systems
            sound = true,        -- Sound system
            ui = true,          -- UI system
            ai = true,          -- AI systems
            combat = true,      -- Combat system
            inventory = true,    -- Inventory system
            quests = true,       -- Quest system
            npcs = true,        -- NPC system
            enemies = true,      -- Enemy system
            items = true,        -- Item system
            structures = true,   -- Structure system
            biomes = true,       -- Biome system
            chunks = true,       -- Chunk system
            save = true,        -- Save system
            network = true,      -- Network system
            world = true,        -- World system
            physics = true,      -- Physics system
            networking = true,   -- Networking system
            
            -- Placeholder fallback systems
            placeholder_entities = false, -- Fallback entity system
            placeholder_chunks = false,   -- Fallback chunk system
            placeholder_tiles = false,    -- Fallback tile types
            placeholder_biomes = false,   -- Fallback biome generation
            placeholder_physics = false,
            placeholder_lighting = false,
            placeholder_pathfinding = false,
            placeholder_dialogue = false
        }
    },

    -- UI Settings
    ui = {
        fontSizes = {
            small = 12,
            regular = 16,
            medium = 20,
            large = 24,
            title = 32
        },
        colors = {
            background = {0.1, 0.1, 0.12, 1},
            text = {0.9, 0.9, 0.9, 1},
            textHighlight = {1, 1, 1, 1},
            textDim = {0.7, 0.7, 0.7, 1},
            primary = {0.2, 0.6, 0.9, 1},
            secondary = {0.8, 0.3, 0.9, 1},
            success = {0.3, 0.8, 0.4, 1},
            warning = {0.9, 0.7, 0.2, 1},
            danger = {0.9, 0.3, 0.3, 1},
            health = {0.9, 0.2, 0.2, 1},
            mana = {0.2, 0.4, 0.9, 1},
            stamina = {0.2, 0.9, 0.4, 1}
        },
        barSettings = {
            healthBarWidth = 200,
            healthBarHeight = 20,
            manaBarWidth = 200,
            manaBarHeight = 20,
            staminaBarWidth = 200,
            staminaBarHeight = 20,
            barSpacing = 5
        }
    },

    -- Game Mechanics
    game = {
        maxInventoryWeight = 100,
        maxStackSize = 99,
        defaultMoveSpeed = 5,
        defaultAttackSpeed = 1.0,
        defaultJumpForce = 10,
        gravity = 0.5,
        maxHealth = 100,
        maxMana = 100,
        maxStamina = 100,
        healthRegenRate = 0.1,
        manaRegenRate = 0.1,
        staminaRegenRate = 0.2
    },

    -- World Settings
    world = {
        chunkSize = 16,
        renderDistance = 10,
        maxChunks = 100,
        tileSize = 32,
        maxEntitiesPerChunk = 50,
        spawnRate = 0.1,
        dayLength = 1200, -- seconds
        weatherChangeInterval = 300 -- seconds
    },

    -- Combat Settings
    combat = {
        baseDamageMultiplier = 1.0,
        criticalHitMultiplier = 1.5,
        criticalHitChance = 0.05,
        dodgeChance = 0.1,
        blockChance = 0.15,
        parryChance = 0.05,
        maxCombo = 5,
        comboWindow = 1.5, -- seconds
        invincibilityFrames = 0.5 -- seconds
    },

    -- Sound Settings
    sound = {
        enabled = true,
        musicVolume = 0.5,
        sfxVolume = 0.7,
        ambientVolume = 0.5,
        maxConcurrentSounds = 8,
        soundDistance = 30,
        musicFadeTime = 1.0
    },

    -- Save Settings
    save = {
        autoSaveInterval = 300, -- seconds
        maxSaveSlots = 5,
        saveDirectory = "saves/",
        backupDirectory = "saves/backups/"
    }
}

-- Helper function to get a setting value
function Settings:get(path)
    local current = self
    for key in path:gmatch("[^.]+") do
        current = current[key]
        if not current then return nil end
    end
    return current
end

-- Helper function to set a setting value
function Settings:set(path, value)
    local current = self
    local keys = {}
    for key in path:gmatch("[^.]+") do
        table.insert(keys, key)
    end
    
    for i = 1, #keys - 1 do
        if not current[keys[i]] then
            current[keys[i]] = {}
        end
        current = current[keys[i]]
    end
    
    current[keys[#keys]] = value
end

return Settings 