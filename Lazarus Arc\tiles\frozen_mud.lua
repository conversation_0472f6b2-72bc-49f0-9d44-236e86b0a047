-- tiles/frozen_mud.lua
local FrozenMudTile = {
    id = "frozen_mud",
    name = "Frozen Mud",
    passable = true,
    variants = 3, -- Different frozen mud patterns
    
    -- Properties
    properties = {
        thickness = 0.5,
        temperature = -10,
        mudContent = 0.8,
        iceContent = 0.2,
        hardness = 0.7,
        hasFossils = true,
        hasRoots = true,
        thawing = false
    },
    
    -- Visual properties
    visual = {
        base_color = {0.4, 0.3, 0.2}, -- Dark brown
        variants = {
            {name = "solid_frozen", type = "solid"},
            {name = "cracked_frozen", type = "cracked"},
            {name = "root_frozen", type = "root"}
        },
        ice_patterns = {
            {name = "ice_crystal", type = "crystal"},
            {name = "ice_vein", type = "vein"},
            {name = "ice_patch", type = "patch"}
        }
    },
    
    -- Sound properties
    sounds = {
        ambient = {
            "ice_creaking",
            "mud_crunching"
        },
        footsteps = {
            "frozen_mud_step",
            "ice_crunch"
        }
    },
    
    -- Resources
    resources = {
        fossils = {
            amount = 1,
            regenRate = 0,
            harvestAmount = 1
        },
        roots = {
            amount = 2,
            regenRate = 0,
            harvestAmount = 1
        }
    },
    
    -- Effects
    effects = {
        thawing = {
            active = false,
            progress = 0.0,
            rate = 0.1
        },
        cracking = {
            active = false,
            progress = 0.0,
            threshold = 0.8
        }
    },
    
    -- Spawn rules
    spawnRules = {
        fossils = {
            chance = 0.2,
            minDistance = 5,
            maxDensity = 0.1
        },
        roots = {
            chance = 0.4,
            minDistance = 2,
            maxDensity = 0.3
        }
    },
    
    -- Weather effects
    weatherEffects = {
        snow = function(tile)
            -- Snow can strengthen the frozen mud
            tile.properties.thickness = math.min(1.0, tile.properties.thickness + 0.1)
            tile.properties.hardness = math.min(1.0, tile.properties.hardness + 0.1)
            return "freeze_deep"
        end,
        rain = function(tile)
            -- Rain can start thawing
            tile.properties.thickness = math.max(0, tile.properties.thickness - 0.1)
            tile.properties.hardness = math.max(0, tile.properties.hardness - 0.1)
            if tile.properties.thickness < 0.3 then
                tile.properties.thawing = true
                return "start_thaw"
            end
            return "soften"
        end,
        heat = function(tile)
            -- Heat rapidly thaws the mud
            tile.properties.thickness = math.max(0, tile.properties.thickness - 0.3)
            tile.properties.hardness = math.max(0, tile.properties.hardness - 0.3)
            if tile.properties.thickness <= 0 then
                return "complete_thaw"
            end
            return "thaw"
        end
    },
    
    -- Time effects
    timeEffects = {
        day = function(tile)
            -- Daytime can start thawing
            if tile.properties.temperature > 0 then
                tile.properties.thickness = math.max(0, tile.properties.thickness - 0.1)
                tile.properties.hardness = math.max(0, tile.properties.hardness - 0.1)
                if tile.properties.thickness < 0.3 then
                    tile.properties.thawing = true
                    return "start_thaw"
                end
                return "soften"
            end
            return nil
        end,
        night = function(tile)
            -- Nighttime can refreeze
            if tile.properties.temperature < 0 then
                tile.properties.thickness = math.min(1.0, tile.properties.thickness + 0.1)
                tile.properties.hardness = math.min(1.0, tile.properties.hardness + 0.1)
                if tile.properties.thawing then
                    tile.properties.thawing = false
                    return "refreeze"
                end
                return "freeze"
            end
            return nil
        end
    }
}

-- Initialize the tile
function FrozenMudTile.init(tile, world)
    -- Initialize properties first
    tile.properties = tile.properties or {}
    
    -- Copy all fields from FrozenMudTile template to tile instance
    for k, v in pairs(FrozenMudTile) do
        if type(v) ~= "function" and tile[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                tile[k] = {}
                for subk, subv in pairs(v) do
                    tile[k][subk] = subv
                end
            else
                tile[k] = v
            end
        end
    end
    
    -- Set random variant
    if tile.visual and tile.visual.variants then
        local variants = tile.visual.variants
        tile.currentVariant = variants[math.random(#variants)]
    end
    
    -- Initialize resources
    if tile.resources then
        for resourceType, resource in pairs(tile.resources) do
            resource.currentAmount = resource.amount
            resource.lastHarvest = 0
            resource.growthStage = "mature"
        end
    end
    
    return tile
end

return FrozenMudTile 