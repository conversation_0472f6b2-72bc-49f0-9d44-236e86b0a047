-- utils/memory_monitor.lua
-- Memory usage monitoring and display

local MemoryMonitor = {}

-- Initialize memory monitor
function MemoryMonitor.init()
    MemoryMonitor.enabled = false
    MemoryMonitor.updateInterval = 0.5 -- Update every 0.5 seconds
    MemoryMonitor.lastUpdate = 0
    
    -- Display settings
    MemoryMonitor.x = 10
    MemoryMonitor.y = 300
    MemoryMonitor.width = 250
    MemoryMonitor.height = 200
    MemoryMonitor.backgroundColor = {0, 0, 0, 0.8}
    MemoryMonitor.textColor = {1, 1, 1, 1}
    MemoryMonitor.warningColor = {1, 1, 0, 1}
    MemoryMonitor.criticalColor = {1, 0, 0, 1}
    
    -- Memory thresholds (in MB)
    MemoryMonitor.thresholds = {
        lua = {
            warning = 50,
            critical = 100
        },
        texture = {
            warning = 100,
            critical = 200
        },
        total = {
            warning = 200,
            critical = 400
        }
    }
    
    -- Current memory stats
    MemoryMonitor.stats = {
        luaMemory = 0,
        textureMemory = 0,
        totalMemory = 0,
        gcCount = 0,
        lastGC = 0
    }
    
    -- Memory history for trend analysis
    MemoryMonitor.history = {
        lua = {},
        texture = {},
        total = {}
    }
    MemoryMonitor.maxHistorySize = 60 -- Keep 30 seconds of history at 0.5s intervals
end

-- Update memory statistics
function MemoryMonitor.update(dt)
    if not MemoryMonitor.enabled then return end
    
    MemoryMonitor.lastUpdate = MemoryMonitor.lastUpdate + dt
    
    if MemoryMonitor.lastUpdate >= MemoryMonitor.updateInterval then
        MemoryMonitor.lastUpdate = 0
        MemoryMonitor.collectStats()
    end
end

-- Collect current memory statistics
function MemoryMonitor.collectStats()
    -- Lua memory (in MB)
    MemoryMonitor.stats.luaMemory = collectgarbage("count") / 1024
    
    -- Texture memory (if available)
    local stats = love.graphics.getStats()
    if stats and stats.texturememory then
        MemoryMonitor.stats.textureMemory = stats.texturememory / (1024 * 1024)
    else
        MemoryMonitor.stats.textureMemory = 0
    end
    
    -- Total estimated memory
    MemoryMonitor.stats.totalMemory = MemoryMonitor.stats.luaMemory + MemoryMonitor.stats.textureMemory
    
    -- Garbage collection info
    MemoryMonitor.stats.gcCount = collectgarbage("count")
    
    -- Add to history
    MemoryMonitor.addToHistory("lua", MemoryMonitor.stats.luaMemory)
    MemoryMonitor.addToHistory("texture", MemoryMonitor.stats.textureMemory)
    MemoryMonitor.addToHistory("total", MemoryMonitor.stats.totalMemory)
end

-- Add value to history
function MemoryMonitor.addToHistory(type, value)
    if not MemoryMonitor.history[type] then
        MemoryMonitor.history[type] = {}
    end
    
    table.insert(MemoryMonitor.history[type], value)
    
    -- Remove old entries
    if #MemoryMonitor.history[type] > MemoryMonitor.maxHistorySize then
        table.remove(MemoryMonitor.history[type], 1)
    end
end

-- Get memory trend (increasing, decreasing, stable)
function MemoryMonitor.getTrend(type)
    local history = MemoryMonitor.history[type]
    if not history or #history < 5 then return "stable" end
    
    local recent = {}
    local older = {}
    
    -- Compare last 5 values with previous 5 values
    for i = math.max(1, #history - 4), #history do
        table.insert(recent, history[i])
    end
    
    for i = math.max(1, #history - 9), math.max(1, #history - 5) do
        table.insert(older, history[i])
    end
    
    local recentAvg = 0
    local olderAvg = 0
    
    for _, v in ipairs(recent) do recentAvg = recentAvg + v end
    for _, v in ipairs(older) do olderAvg = olderAvg + v end
    
    recentAvg = recentAvg / #recent
    olderAvg = olderAvg / #older
    
    local diff = recentAvg - olderAvg
    local threshold = olderAvg * 0.05 -- 5% threshold
    
    if diff > threshold then
        return "increasing"
    elseif diff < -threshold then
        return "decreasing"
    else
        return "stable"
    end
end

-- Get color based on memory level
function MemoryMonitor.getMemoryColor(value, thresholds)
    if value >= thresholds.critical then
        return MemoryMonitor.criticalColor
    elseif value >= thresholds.warning then
        return MemoryMonitor.warningColor
    else
        return MemoryMonitor.textColor
    end
end

-- Draw memory usage display
function MemoryMonitor.draw()
    if not MemoryMonitor.enabled then return end
    
    love.graphics.push()
    
    -- Draw background
    love.graphics.setColor(MemoryMonitor.backgroundColor)
    love.graphics.rectangle("fill", MemoryMonitor.x, MemoryMonitor.y, MemoryMonitor.width, MemoryMonitor.height)
    
    -- Draw border
    love.graphics.setColor(0.5, 0.5, 0.5, 1)
    love.graphics.setLineWidth(1)
    love.graphics.rectangle("line", MemoryMonitor.x, MemoryMonitor.y, MemoryMonitor.width, MemoryMonitor.height)
    
    -- Draw title
    love.graphics.setColor(MemoryMonitor.textColor)
    love.graphics.setFont(love.graphics.getFont())
    love.graphics.print("Memory Usage", MemoryMonitor.x + 10, MemoryMonitor.y + 10)
    
    local lineHeight = 16
    local startY = MemoryMonitor.y + 35
    
    -- Lua memory
    local luaColor = MemoryMonitor.getMemoryColor(MemoryMonitor.stats.luaMemory, MemoryMonitor.thresholds.lua)
    local luaTrend = MemoryMonitor.getTrend("lua")
    local luaTrendSymbol = luaTrend == "increasing" and "↑" or (luaTrend == "decreasing" and "↓" or "→")
    
    love.graphics.setColor(luaColor)
    love.graphics.print(string.format("Lua Memory: %.2f MB %s", MemoryMonitor.stats.luaMemory, luaTrendSymbol), 
                       MemoryMonitor.x + 10, startY)
    
    -- Texture memory
    local textureColor = MemoryMonitor.getMemoryColor(MemoryMonitor.stats.textureMemory, MemoryMonitor.thresholds.texture)
    local textureTrend = MemoryMonitor.getTrend("texture")
    local textureTrendSymbol = textureTrend == "increasing" and "↑" or (textureTrend == "decreasing" and "↓" or "→")
    
    love.graphics.setColor(textureColor)
    love.graphics.print(string.format("Texture Memory: %.2f MB %s", MemoryMonitor.stats.textureMemory, textureTrendSymbol), 
                       MemoryMonitor.x + 10, startY + lineHeight)
    
    -- Total memory
    local totalColor = MemoryMonitor.getMemoryColor(MemoryMonitor.stats.totalMemory, MemoryMonitor.thresholds.total)
    local totalTrend = MemoryMonitor.getTrend("total")
    local totalTrendSymbol = totalTrend == "increasing" and "↑" or (totalTrend == "decreasing" and "↓" or "→")
    
    love.graphics.setColor(totalColor)
    love.graphics.print(string.format("Total Memory: %.2f MB %s", MemoryMonitor.stats.totalMemory, totalTrendSymbol), 
                       MemoryMonitor.x + 10, startY + lineHeight * 2)
    
    -- Garbage collection info
    love.graphics.setColor(MemoryMonitor.textColor)
    love.graphics.print(string.format("GC Count: %.0f KB", MemoryMonitor.stats.gcCount), 
                       MemoryMonitor.x + 10, startY + lineHeight * 4)
    
    -- Memory usage bars
    MemoryMonitor.drawMemoryBar("Lua", MemoryMonitor.stats.luaMemory, MemoryMonitor.thresholds.lua.critical, 
                               MemoryMonitor.x + 10, startY + lineHeight * 6, 200, 10)
    
    MemoryMonitor.drawMemoryBar("Texture", MemoryMonitor.stats.textureMemory, MemoryMonitor.thresholds.texture.critical, 
                               MemoryMonitor.x + 10, startY + lineHeight * 7.5, 200, 10)
    
    -- Memory actions (only show in debug builds)
    love.graphics.setColor(0.7, 0.7, 0.7, 1)
    love.graphics.print("Click to force garbage collection", MemoryMonitor.x + 10, startY + lineHeight * 9)
    
    love.graphics.pop()
end

-- Draw a memory usage bar
function MemoryMonitor.drawMemoryBar(label, current, max, x, y, width, height)
    -- Background
    love.graphics.setColor(0.3, 0.3, 0.3, 1)
    love.graphics.rectangle("fill", x, y, width, height)
    
    -- Usage bar
    local usage = math.min(current / max, 1)
    local color = MemoryMonitor.getMemoryColor(current, {warning = max * 0.7, critical = max * 0.9})
    love.graphics.setColor(color)
    love.graphics.rectangle("fill", x, y, width * usage, height)
    
    -- Border
    love.graphics.setColor(0.6, 0.6, 0.6, 1)
    love.graphics.setLineWidth(1)
    love.graphics.rectangle("line", x, y, width, height)
end

-- Toggle memory monitor
function MemoryMonitor.toggle()
    MemoryMonitor.enabled = not MemoryMonitor.enabled
    if MemoryMonitor.enabled then
        MemoryMonitor.collectStats() -- Collect initial stats
        print("Memory monitor enabled")
    else
        print("Memory monitor disabled")
    end
    return MemoryMonitor.enabled
end

-- Force garbage collection
function MemoryMonitor.forceGC()
    local beforeGC = collectgarbage("count")
    collectgarbage("collect")
    local afterGC = collectgarbage("count")
    local freed = beforeGC - afterGC
    
    print(string.format("Garbage collection: freed %.2f KB", freed))
    MemoryMonitor.collectStats() -- Update stats immediately
    
    return freed
end

-- Handle mouse click (for GC button)
function MemoryMonitor.mousePressed(x, y, button)
    if not MemoryMonitor.enabled or button ~= 1 then return false end

    -- Check if click is on the memory monitor area
    if x >= MemoryMonitor.x and x <= MemoryMonitor.x + MemoryMonitor.width and
       y >= MemoryMonitor.y and y <= MemoryMonitor.y + MemoryMonitor.height then
        -- Force garbage collection when clicked
        MemoryMonitor.forceGC()
        return true
    end

    return false
end

-- Set position
function MemoryMonitor.setPosition(x, y)
    MemoryMonitor.x = x
    MemoryMonitor.y = y
end

return MemoryMonitor
