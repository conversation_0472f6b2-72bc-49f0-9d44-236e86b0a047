-- tiles/cave_floor.lua
local CaveFloorTile = {
    id = "cave_floor",
    name = "Cave Floor",
    passable = true,
    variants = 6, -- e.g., Smooth Stone, Uneven Rock, Packed Dirt, <PERSON>ose <PERSON>l, <PERSON>p Mud, Fungal Growth

    -- Entities that spawn in caves
    spawns = {"cave_bat_swarm", "giant_spider", "cave_lizard", "glowing_mushroom", "mineral_deposit_small", "guano_pile", "slime_ooze"}, -- Typical cave dwellers and features

    -- Movement properties (can vary significantly by variant)
    movementSpeed = 0.9, -- Default, slightly uneven/slower than ideal surface

    -- Cave-specific properties
    isDark = true, -- Requires light source for visibility
    echoLevel = 0.7, -- How much sound echoes (0-1)
    humidity = 0.6, -- Base humidity level (0-1)
    floorMaterial = "rock", -- Default material (rock, dirt, gravel) - affects sounds, interactions

    -- Interaction function
    interact = function(tile, entity)
        -- Examine the floor material
        local materialDesc = "The floor here is composed of " .. tile.floorMaterial .. "."
        local footingDesc = "It seems relatively stable."
        if tile.variant_name == "Uneven Rock" or tile.variant_name == "Loose Gravel" then
             footingDesc = "Watch your step; the footing is uneven."
        elseif tile.variant_name == "Damp Mud" then
             footingDesc = "It's damp and somewhat sticky underfoot."
        end

        -- Search for items/minerals
        if math.random() < 0.15 then
            local caveFinds = {
                {id="loose_gemstone_tiny", chance=0.15},
                {id="sharp_rock_fragment", chance=0.3},
                {id="bat_guano", chance=0.2, condition_property="hasGuano"}, -- Changed condition=tile.hasGuano
                {id="edible_cave_fungus", chance=0.25, condition_property="hasFungus"}, -- Changed condition=tile.hasFungus
                {id="discarded_miner_pick_head", chance=0.1} -- Hint of previous activity
            }
             local possibleFinds = {}
            for _, item in ipairs(caveFinds) do
                 -- Check if the condition property exists on the tile instance and is true, or if no condition property is specified
                 local conditionMet = true
                 if item.condition_property then
                     conditionMet = tile[item.condition_property] == true
                 end

                 if conditionMet then
                     table.insert(possibleFinds, item)
                 end
            end
             if #possibleFinds > 0 then
                 for _, item in ipairs(possibleFinds) do
                     if math.random() < item.chance then
                         return {
                             success = true,
                             message = "Searching the cave floor, you find a " .. item.id .. ".",
                             effects = {{type="add_item", item=item.id, quantity=1}}
                         }
                     end
                 end
             end
        end

        -- Check for water drips (might indicate path to water or surface)
        if tile.isDamp and math.random() < 0.2 then
             return { success = false, message = materialDesc .. " " .. footingDesc .. " Water drips steadily from the ceiling here." }
        end

        -- General examination
        return {
            success = false,
            message = materialDesc .. " " .. footingDesc
        }
    end,

    -- Environmental effects (Indirect weather)
    environmentalEffects = {
        -- No direct weather, but can react to conditions outside
        prolonged_rain_outside = function(tile)
             tile.humidity = math.min(1.0, tile.humidity + 0.2)
             if math.random() < 0.3 then tile.isDamp = true end -- Increased chance of dampness/drips
        end,
        prolonged_dry_outside = function(tile)
             tile.humidity = math.max(0.2, tile.humidity - 0.1)
             if tile.floorMaterial == "dirt" or tile.variant_name == "Damp Mud" then
                 -- Dirt/mud floors might dry and crack
                 if tile.humidity < 0.3 then return "visual_effect", "floor_cracks_dry" end
             end
             tile.isDamp = false
        end,
        deep_freeze_outside = function(tile)
            if tile.isDamp then
                 tile.isIcyPatch = true -- Form ice patches if damp
                 tile.slipperiness = 0.6
                 return "visual_effect", "cave_floor_ice_patch"
            end
        end
    },

     -- Time effects (Minimal, mostly related to creature activity)
    timeEffects = {
        night = function(tile)
            -- Bat activity increases? Other nocturnal creatures emerge?
            tile.spawnModifier = { type="cave_bat_swarm", chanceMultiplier=2.0 }
            tile.ambientSound = "cave_drips_distant_skittering"
            return nil
        end,
        
        day = function(tile)
             tile.spawnModifier = nil
             tile.ambientSound = "cave_drips_echoing"
            return nil
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {0.4, 0.4, 0.4}, -- Default dark grey stone
        lighting = { ambient_light_level = 0.1 }, -- Very low light
        variants = {
            {name = "Smooth Stone", floorMaterial="stone", texture="cave_stone_smooth", movementSpeed=1.0},
            {name = "Uneven Rock", floorMaterial="rock", texture="cave_stone_uneven", movementSpeed=0.85, hazard_chance=0.05},
            {name = "Packed Dirt", floorMaterial="dirt", base_color={0.5, 0.4, 0.3}, texture="cave_dirt_packed", movementSpeed=0.9},
            {name = "Loose Gravel", floorMaterial="gravel", texture="cave_gravel", movementSpeed=0.8, footstepSound="gravel_crunch_cave"},
            {name = "Damp Mud", floorMaterial="mud", base_color={0.4, 0.3, 0.25}, texture="cave_mud", movementSpeed=0.6, stickiness=0.4}, -- Less extreme than surface mud
            {name = "Fungal Growth", overlay_objects={{name="glowing_mushroom_small", chance=0.6}, {name="cave_moss_patch", chance=0.4}}, hasFungus=true}
        },
        decoration_objects = {
            {name = "loose_rock", chance = 0.3},
            {name = "stalagmite_small", chance = 0.1}, -- Small floor formations
            {name = "puddle_shallow", chance = 0.15, condition_property="isDamp"}, -- Changed condition=tile.isDamp
            {name = "guano_patch", chance = 0.1, condition_property="hasGuano"} -- Changed hasGuano=true to condition_property
        },
         special_effects = {
             dust_motes = {type="particle", density=0.1, when="light_source_nearby"}, -- Visible in light beams
             water_drip_splash = {type="particle_splash_small", base_chance=0.05, chance_modifier="humidity", when="isDamp"} -- Changed chance=0.05 * tile.humidity
         },
          weather_overlays = { -- Represents dampness/ice rather than weather
              damp_sheen = {shininess=0.3, when="isDamp"},
              ice_patch = {texture="ice_patch_thin", shininess=0.5, when="isIcyPatch"}
          }
    },
    
    -- Audio properties
    ambientSound = "cave_drips_echoing",
    footstepSound = "stone_step_cave", -- Default cave footstep, echoes
     specialSounds = {
        {name = "bat_flutter_close", trigger = "entity_nearby", chance = 0.1},
        {name = "rock_fall_small_distant", trigger = "random", chance = 0.03},
        {name = "water_drip_plink", trigger = "random", base_chance=0.2, chance_modifier="humidity"}, -- Changed chance = 0.2 * tile.humidity
        {name = "skittering_echo", trigger = "random", chance = 0.05} -- Unseen creature
    },
     -- Modifiers based on environment
     audioModifiers = {
         base_reverb=0.7, reverb_modifier="echoLevel", -- Changed reverb = 0.7 * tile.echoLevel
         muffling = 0.2 -- Base sound muffling in caves?
     },

    -- Connections to other tiles
    connects_to = {
        "cave_floor", -- Itself
        "cave_wall", -- Impassable boundary
        "cave_entrance", -- Exit/entry point
        "underground_stream_bank", -- If connects to water feature
        "crystal_cave_floor", -- Transition to different cave type
        "lava_tube_floor", -- Transition to different cave type
        "chasm_edge" -- Edge of a pitfall
    },

    -- Pathfinding properties
     pathfinding = {
        travel_cost = 1.1, -- Default cost (slightly higher due to unevenness/darkness)
        visibility_penalty = 0.8, -- Reduces effective sight range
        hazard_chance = 0.05 -- Base chance for minor hazard (stumble, etc.)
    },

    -- Effects on entities that walk on this tile
    onWalk = function(tile, entity)
        local sound = tile.footstepSound or "stone_step_cave"
        local fatigue = 0.08 -- Base fatigue
        local effects = {}
        
        -- Adjust sound/fatigue based on variant material
        if tile.floorMaterial == "gravel" then sound = "gravel_crunch_cave"; fatigue = 0.1;
        elseif tile.floorMaterial == "dirt" then sound = "dirt_step_cave"; fatigue = 0.09;
        elseif tile.floorMaterial == "mud" then sound = "mud_squelch_cave"; fatigue = 0.15; end
        
        -- Check for slipping on ice patches
        if tile.isIcyPatch and math.random() < tile.slipperiness * 0.6 then
             sound = "ice_slip_scrape_cave"
             table.insert(effects, {type="apply_status", status="slipping_minor", duration=1})
             -- Calculate volume based on tile's echoLevel
             local volume = 0.8 * (1 + (tile.echoLevel or 0))
             return { sound=sound, volume=volume, message="You slip on a patch of ice!", effects=effects }
        end

        -- Check for stumbling on uneven ground
        if tile.variant_name == "Uneven Rock" and math.random() < 0.05 then
            table.insert(effects, {type="apply_status", status="stumbled", duration=1})
             -- Calculate volume based on tile's echoLevel
             local volume = 0.9 * (1 + (tile.echoLevel or 0))
             return { sound=sound, volume=volume, message="You stumble on the uneven cave floor.", effects=effects }
        end

        table.insert(effects, {type="increase_fatigue", amount = fatigue})
        -- Potentially alert nearby creatures?
        if math.random() < 0.1 then table.insert(effects, {type="alert_nearby_creatures", radius=5, intensity=0.5}) end

        -- Calculate volume based on tile's echoLevel
        local volume = 0.9 * (1 + (tile.echoLevel or 0))
        return {
            sound = sound,
            volume = volume, -- Apply echo amplification
            effects = effects
        }
    end,
    
    -- Effect when discovering cave floor (usually means entering a cave)
    onDiscover = function(tile, entity)
        if entity.journal and entity.journal.addDiscovery then
             entity.journal.addDiscovery({
                type = "location",
                name = "Cave System", -- Generic name, could be specific
                location = {x = tile.x, y = tile.y},
                notes = "Entered a dark cave system. The floor is [" .. tile.floorMaterial .. "]."
            })
        end
        return {
            message = "You step onto the floor of a dark cave.",
            effects = {
                {type = "apply_environment_effect", effect="darkness", intensity=0.9}, -- Apply darkness effect
                {type = "reveal_map", radius = 1} -- Reveal only immediate surroundings due to darkness
            }
        }
    end
}

function CaveFloorTile.init(world)
    print("Cave Floor tile module initialized")
    -- Register with relevant systems
     if world and world.systems and world.systems.environment then
        world.systems.environment:registerCaveTerrain("cave_floor", {base_light=0.1, base_humidity=0.6})
    end
end

return CaveFloorTile
