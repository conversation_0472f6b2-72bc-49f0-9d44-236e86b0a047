-- tiles/forest_snowy.lua
local ForestSnowyTile = {
    id = "forest_snowy",
    name = "Snowy Forest",
    passable = true, -- Passable, but potentially difficult due to snow and trees
    variants = 6, -- e.g., Snowy Pine Forest, Deciduous Winter Woods, Deep Snow Thicket, Lightly Dusted Woods, Frozen Grove, Old Growth Snow Forest

    -- Entities found in snowy forests
    spawns = {"winter_wolf", "snow_deer", "winter_owl", "squirrel_tracks_snow", "snow_covered_log", "hibernating_bear_den", "ice_elemental_lesser"},

    -- Movement properties
    movementSpeed = 0.65, -- Base speed reduction due to snow and potential undergrowth/obstacles

    -- Snowy Forest-specific properties
    snowDepth = 0.6, -- Average depth, potentially less than open areas due to canopy
    treeDensity = 0.7, -- Density of trees (0-1)
    canopyCover = 0.6, -- How much the canopy blocks sky/light/snowfall (0-1)
    shelterValue = 0.4, -- Provides moderate shelter from wind/snowfall (0-1)
    snowOnBranches = 0.5, -- Amount of snow accumulated on tree branches (0-1)

    -- Interaction function
    interact = function(tile, entity)
        -- Shake snow off branches? (Could reveal items or cause snow to fall)
        if tile.snowOnBranches > 0.3 and math.random() < 0.3 then
             return {
                 success = true,
                 message = "You could try shaking the snow off a nearby tree branch.",
                 effects = {{type="suggest_action", action="shake_branch"}}
                 -- Shaking branch could trigger snow fall effect
             }
        end

        -- Look for tracks (may be clearer here than wind-swept open snow)
        if entity.skills and entity.skills.tracking > 1 and math.random() < 0.5 then
             local tracks = {"wolf_pack_tracks", "deer_trail_snow", "small_rodent_prints", "lost_trapper_tracks"}
             local foundTrack = tracks[math.random(#tracks)]
             return { success = true, message = "The snow here clearly shows tracks: " .. foundTrack .. ".", effects={{type="gain_tracking_clue", clue=foundTrack}} }
        end

        -- Gather resources (snow-covered wood, specific plants)
        if math.random() < 0.25 then
             local resources = {
                 {id="snow_covered_firewood", chance=0.5},
                 {id="winterberry_bush", chance=0.3}, -- Plant adapted to cold
                 {id="pine_cone_frozen", chance=0.2}
             }
             for _, res in ipairs(resources) do
                 if math.random() < res.chance then
                     return { success = true, message = "Searching around, you gather some " .. res.id .. ".", effects={{type="add_item", item=res.id, quantity=math.random(1,2)}} }
                 end
             end
        end
        
        -- Examine the environment
        local canopyDesc = "Snow rests heavily on the tree branches above."
        if tile.canopyCover < 0.3 then canopyDesc = "The trees offer little cover here." end
        local depthDesc = "Snow blankets the forest floor."
        if tile.snowDepth < 0.2 then depthDesc = "Only a light dusting of snow covers the ground."
        elseif tile.snowDepth > 1.0 then depthDesc = "Deep snowdrifts make movement difficult between the trees." end

        return {
            success = false,
            message = depthDesc .. " " .. canopyDesc
        }
    end,

    -- Weather effects (Trees modify effects)
    weatherEffects = {
        snow = function(tile)
            -- Less snow might reach the ground due to canopy
            tile.depth = (tile.depth or 0) + (0.2 * (1 - (tile.canopyCover or 0.6) * 0.7)) -- Canopy intercepts some snow
            tile.snowOnBranches = math.min(1.0, (tile.snowOnBranches or 0) + 0.3)
            tile.snowType = "powder"
            tile.movementSpeed = math.max(0.2, 0.65 - ((tile.depth or 0) * 0.25)) -- Speed affected by depth
            return "visual_effect", "heavy_snowfall_forest"
        end,
        
        heat = function(tile) -- Melting, dripping from trees
            -- Assuming world context is available if needed, e.g., via tile.worldRef
            local currentTemp = (tile.worldRef and tile.worldRef.temperature) or 0
            if currentTemp > 0 then
                 local meltRateGround = 0.25 * (currentTemp / 10)
                 local meltRateBranches = 0.4 * (currentTemp / 10)
                 tile.depth = math.max(0, (tile.depth or 0) - meltRateGround)
                 tile.snowOnBranches = math.max(0, (tile.snowOnBranches or 0) - meltRateBranches)
                 tile.snowType = "wet"
                 tile.movementSpeed = 0.45 -- Slushy forest floor is very slow
                 if tile.depth == 0 then
                      -- Reveal underlying forest floor tile
                      return "transform", tile.underlyingTile or "forest_floor_damp" 
                 end
                 return "visual_effect", "snow_melt_drip"
            end
            return nil
        end,

        sun = function(tile) -- Slower melting, more dripping
            -- Assuming world context is available if needed, e.g., via tile.worldRef
            local currentTemp = (tile.worldRef and tile.worldRef.temperature) or 0
            if currentTemp > 0 then
                 local meltRateGround = 0.1 * (1 - (tile.canopyCover or 0.6) * 0.5) -- Sun blocked by canopy
                 local meltRateBranches = 0.2
                 tile.depth = math.max(0, (tile.depth or 0) - meltRateGround)
                 tile.snowOnBranches = math.max(0, (tile.snowOnBranches or 0) - meltRateBranches)
                 tile.snowType = "wet"
                 tile.movementSpeed = 0.5
                 if tile.depth == 0 then
                      return "transform", tile.underlyingTile or "forest_floor" 
                 end
                 return "visual_effect", "snow_melt_drip_sunny"
            end
            return nil
        end,

        freeze = function(tile) -- Refreezing, ice on branches
             -- Assuming world context is available if needed, e.g., via tile.worldRef
             local currentTemp = (tile.worldRef and tile.worldRef.temperature) or 0
             if tile.snowType == "wet" and currentTemp < -2 then
                 tile.snowType = "crusty"
                 tile.movementSpeed = 0.55
                 tile.footstepSound = "snow_crunch_forest_crusty"
             end
             if (tile.snowOnBranches or 0) > 0.1 and currentTemp < -3 then
                  -- Icicles form? Branches become brittle?
                  return "visual_effect", "icy_branches"
             end
             return nil
        end,

        wind = function(tile) -- Trees provide shelter
             -- Assuming world context is available if needed, e.g., via tile.worldRef
             local currentWindStrength = (tile.worldRef and tile.worldRef.windStrength) or 0
             local effectiveWind = currentWindStrength * (1 - (tile.shelterValue or 0.4))
             if effectiveWind > 0.7 then
                 -- Still chance of blizzard/blowing snow, but less than open
                 if math.random() < 0.3 then return "trigger_event", "blizzard_forest" end
                 return "visual_effect", "blowing_snow_trees"
             elseif effectiveWind > 0.4 then
                  -- Snow falling from trees
                  if (tile.snowOnBranches or 0) > 0.5 and math.random() < 0.1 then
                       return "trigger_event", "branch_snow_fall"
                  end
             end
             return nil
        end
    },

     -- Time effects
    timeEffects = {
        night = function(tile)
            -- Assuming world context is available if needed, e.g., via tile.worldRef
            local currentTemp = (tile.worldRef and tile.worldRef.temperature) or -7
            tile.temperature = currentTemp - 2
            tile.ambientSound = "snowy_forest_night_wind" -- Wind, possibly distant howls
            tile.visibilityModifier = 0.4 -- Dark and obscured
            return nil
        end,
        
        day = function(tile)
             -- Assuming world context is available if needed, e.g., via tile.worldRef
             local currentTemp = (tile.worldRef and tile.worldRef.temperature) or -7
             tile.temperature = currentTemp
             tile.ambientSound = "snowy_forest_day_muffled" -- Quiet, muffled sounds
             tile.visibilityModifier = 0.8 -- Better visibility, but still obscured by trees
            return nil
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {0.9, 0.9, 0.95}, -- Snow color dominant
        variants = {
            {name = "Snowy Pine Forest", treeType="pine", canopyCover=0.7, texture="forest_floor_pine_snow"},
            {name = "Deciduous Winter Woods", treeType="deciduous_bare", canopyCover=0.3, texture="forest_floor_leafy_snow"},
            {name = "Deep Snow Thicket", treeDensity=0.9, snowDepth=1.2, movementSpeed=0.4},
            {name = "Lightly Dusted Woods", snowDepth=0.2, snowOnBranches=0.1},
            {name = "Frozen Grove", overlay_objects={{name="icicle_large", chance=0.5}}, temperature_mod=-5},
            {name = "Old Growth Snow Forest", treeType="large_ancient", canopyCover=0.8, treeDensity=0.5}
        },
        decoration_objects = {
            {name = "tree_snowy", base_chance = 0.8, chance_modifier="treeDensity"}, -- Changed chance = 0.8 * tile.treeDensity
            {name = "snow_covered_bush", chance = 0.4},
            {name = "animal_tracks_forest_decal", chance = 0.5},
            {name = "fallen_branch_snowy", chance = 0.2}
        },
         special_effects = {
             light_shafts_snow = {type="volumetric_light", intensity=0.3, when="sunny", condition_property="canopyCover", condition_op="lt", condition_value=0.8}, -- Changed condition=tile.canopyCover < 0.8
             snow_falling_from_branches = {type="particle_fall", particle="snow_clump", base_chance=0.05, chance_modifier="snowOnBranches"} -- Changed chance=0.05 * tile.snowOnBranches
         },
         underlying_tile_influence = 0.3, -- Underlying forest floor slightly influences visuals
         hides_underlying_tile = true -- Snow covers the ground mostly
    },
    
    -- Audio properties
    ambientSound = "snowy_forest_day_muffled", -- Quiet, snow-dampened sounds
    footstepSound = "snow_crunch_forest", -- Muffled crunch
     specialSounds = {
        {name = "snow_thump_branch", trigger = "random", base_chance = 0.05, chance_modifier="snowOnBranches"}, -- Changed chance = 0.05 * tile.snowOnBranches
        {name = "branch_snap_cold", trigger = "temperature_change_freeze", chance = 0.1},
        {name = "distant_wolf_howl_muffled", trigger = "night", chance = 0.04},
        {name = "owl_hoot_snowy", trigger = "night", chance = 0.05}
    },
    -- Audio Modifiers
     audioModifiers = {
         base_muffling = 0.6, muffling_modifier_depth=0.1, muffling_modifier_treeDensity=0.1, -- Changed muffling = 0.6 + tile.depth * 0.1 + tile.treeDensity * 0.1
         reverb = 0.2 -- Less echo than open areas? Or more due to trees? Let's say less.
     },

    -- Connections to other tiles
    connects_to = {
        "forest_snowy", -- Itself
        "snow", -- Edge of the forest
        "forest", -- Edge of the snowy area within a forest
        "frozen_river_bank", -- If forest borders a frozen river
        "mountain_base", -- Snowy forests often occur on foothills
        "clearing_snowy" -- Open area within the snowy forest
    },

    -- Pathfinding properties
     pathfinding = {
        base_travel_cost = 1.5, cost_modifier_depth=0.7, cost_modifier_treeDensity=0.3, -- Changed travel_cost = (1.5 + tile.depth * 0.7) * (1 + tile.treeDensity * 0.3)
        provides_shelter_property = "shelterValue", -- Changed provides_shelter = tile.shelterValue
        reduces_visibility = true -- Trees and snow reduce sight lines
    },

    -- Effects on entities that walk on this tile
    onWalk = function(tile, entity)
        local sound = tile.footstepSound or "snow_crunch_forest"
        -- Adjust sound based on snow type if needed (e.g., crusty, wet)
        
        local depth = tile.depth or 0.6
        local density = tile.treeDensity or 0.7
        local fatigue = 0.18 * (1 + depth * 1.2) * (1 + density * 0.2) -- Fatigue from snow depth and navigating trees
        local effects = {{type="increase_fatigue", amount = fatigue}}

        -- Apply cold effect
        local temp = tile.temperature or -7
        if temp < -3 then
             table.insert(effects, {type="apply_status", status="cold", duration=40, magnitude=math.abs(temp/8)})
        end
        
        -- Chance of snow falling from branches
        local snowOnBranches = tile.snowOnBranches or 0.5
        if snowOnBranches > 0.6 and math.random() < 0.08 then
             table.insert(effects, {type="apply_status", status="snow_dumped", duration=5}) -- Minor disorientation/cold burst?
             -- Play specific sound
             return { sound="snow_thump_branch_close", volume=1.0, message="A clump of snow falls on you from above!", effects=effects }
        end

        -- Leave tracks
        table.insert(effects, {type="leave_tracks", track_type=entity.trackType or "humanoid", duration=120}) -- Tracks last longer in sheltered forest?

        return {
            sound = sound,
            volume = 0.7, -- Muffled sound
            effects = effects
        }
    end,
    
    -- Effect when discovering snowy forest
    onDiscover = function(tile, entity)
        if entity.journal and entity.journal.addDiscovery then
             entity.journal.addDiscovery({
                type = "biome",
                name = "Snowy Forest",
                location = {x = tile.x, y = tile.y},
                notes = "A forest covered in snow. Quiet and cold."
            })
        end
        return {
            message = "You enter a quiet, snow-covered forest.",
            effects = {
                {type = "reveal_map", radius = 2} -- Visibility limited by trees
            }
        }
    end
}

function ForestSnowyTile.init(world)
    print("Snowy Forest tile module initialized")
    -- Register with environment/biome systems
     if world and world.systems and world.systems.environment then
        world.systems.environment:registerForestType("snowy", {base_temp=-7, provides_shelter=0.4})
    end
end

return ForestSnowyTile
