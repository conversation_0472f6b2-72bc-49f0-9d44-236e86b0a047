-- utils/sound_config.lua
-- Sound configuration loader for creature sounds
-- Supports JSON overrides and musical notation parsing

local lunajson = require("lunajson")

local SoundConfig = {
    initialized = false,
    configCache = {},
    userOverrideDir = "sounds/creatures/",
    
    -- Note name to frequency mapping (A4 = 440Hz)
    noteFrequencies = {}
}

-- Initialize the sound config system
function SoundConfig.init()
    if SoundConfig.initialized then return SoundConfig end
    
    print("Initializing Sound Configuration system...")
    
    -- Generate note frequencies for musical notation
    SoundConfig.generateNoteFrequencies()
    
    SoundConfig.initialized = true
    print("Sound Configuration system initialized")
    return SoundConfig
end

-- Generate note frequencies for standard musical notation
function SoundConfig.generateNoteFrequencies()
    local noteNames = {"C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"}
    local A4_frequency = 440.0
    local A4_midi = 69
    
    -- Generate frequencies for octaves 0-8
    for octave = 0, 8 do
        SoundConfig.noteFrequencies[octave] = {}
        for i, noteName in ipairs(noteNames) do
            local midiNote = octave * 12 + (i - 1)
            local frequency = A4_frequency * math.pow(2, (midiNote - A4_midi) / 12)
            SoundConfig.noteFrequencies[octave][noteName] = frequency
        end
    end
end

-- Parse note name to frequency (e.g., "C4", "F#3", "Bb5")
function SoundConfig.parseNoteToFrequency(noteName)
    if type(noteName) == "number" then
        return noteName -- Already a frequency
    end
    
    if type(noteName) ~= "string" then
        return 440.0 -- Default fallback
    end
    
    -- Handle flat notation (convert 'b' to '#' of previous note)
    noteName = noteName:gsub("Bb", "A#"):gsub("Db", "C#"):gsub("Eb", "D#")
                      :gsub("Gb", "F#"):gsub("Ab", "G#")
    
    -- Extract note and octave
    local note, octave = noteName:match("([A-G]#?)(%d)")
    if not note or not octave then
        print("Warning: Invalid note name '" .. noteName .. "', using A4")
        return 440.0
    end
    
    octave = tonumber(octave)
    if not octave or octave < 0 or octave > 8 then
        print("Warning: Invalid octave in '" .. noteName .. "', using octave 4")
        octave = 4
    end
    
    local frequency = SoundConfig.noteFrequencies[octave] and SoundConfig.noteFrequencies[octave][note]
    if not frequency then
        print("Warning: Unknown note '" .. note .. "' in octave " .. octave .. ", using A4")
        return 440.0
    end
    
    return frequency
end

-- Load user override JSON file for an entity
function SoundConfig.loadUserOverride(entityType)
    local filePath = SoundConfig.userOverrideDir .. entityType .. ".json"
    
    -- Check if file exists
    if not love.filesystem.getInfo(filePath) then
        return nil
    end
    
    -- Try to load and parse JSON
    local success, content = pcall(function()
        return love.filesystem.read(filePath)
    end)
    
    if not success then
        print("Warning: Could not read sound config file: " .. filePath)
        return nil
    end
    
    local success, config = pcall(function()
        return lunajson.decode(content)
    end)
    
    if not success then
        print("Warning: Invalid JSON in sound config file: " .. filePath)
        return nil
    end
    
    print("Loaded user sound override for: " .. entityType)
    return config
end

-- Get sound configuration for an entity and sound name
function SoundConfig.getSoundConfig(entityType, soundName, entitySoundDef)
    if not SoundConfig.initialized then SoundConfig.init() end
    
    local cacheKey = entityType .. "_" .. soundName
    
    -- Check cache first
    if SoundConfig.configCache[cacheKey] then
        return SoundConfig.configCache[cacheKey]
    end
    
    local config = nil
    
    -- 1. Check for user JSON override
    local userOverride = SoundConfig.loadUserOverride(entityType)
    if userOverride and userOverride.sounds and userOverride.sounds[soundName] then
        config = userOverride.sounds[soundName]
    end
    
    -- 2. Check entity definition
    if not config and entitySoundDef then
        if type(entitySoundDef) == "table" and entitySoundDef.synth then
            config = entitySoundDef.synth
        elseif type(entitySoundDef) == "table" and entitySoundDef.instrument then
            -- Direct synth config in entity
            config = entitySoundDef
        end
    end
    
    -- 3. No synth config found, return nil (will fall back to audio files)
    if not config then
        SoundConfig.configCache[cacheKey] = nil
        return nil
    end
    
    -- Process and validate the config
    local processedConfig = SoundConfig.processConfig(config)
    
    -- Cache the processed config
    SoundConfig.configCache[cacheKey] = processedConfig
    
    return processedConfig
end

-- Process and validate a sound configuration
function SoundConfig.processConfig(config)
    local processed = {
        instrument = config.instrument or "upright_piano",
        volume = config.volume or 0.3,
        notes = {},
        durations = {},
        vibrato = config.vibrato or false,
        vibratoRate = config.vibratoRate or 5.0,
        vibratoDepth = config.vibratoDepth or 0.1
    }
    
    -- Process notes
    if config.notes then
        if type(config.notes) == "string" then
            -- Single note
            table.insert(processed.notes, SoundConfig.parseNoteToFrequency(config.notes))
            table.insert(processed.durations, config.duration or 0.5)
        elseif type(config.notes) == "table" then
            -- Multiple notes
            for i, note in ipairs(config.notes) do
                table.insert(processed.notes, SoundConfig.parseNoteToFrequency(note))
                
                -- Handle durations
                if config.durations and config.durations[i] then
                    table.insert(processed.durations, config.durations[i])
                else
                    table.insert(processed.durations, config.duration or 0.5)
                end
            end
        end
    else
        -- Default single note
        table.insert(processed.notes, 440.0) -- A4
        table.insert(processed.durations, 0.5)
    end
    
    return processed
end

-- Clear the configuration cache (useful for reloading)
function SoundConfig.clearCache()
    SoundConfig.configCache = {}
    print("Sound configuration cache cleared")
end

-- Get cache statistics
function SoundConfig.getCacheStats()
    local count = 0
    for _ in pairs(SoundConfig.configCache) do
        count = count + 1
    end
    
    return {
        cachedConfigs = count,
        userOverrideDir = SoundConfig.userOverrideDir
    }
end

return SoundConfig
