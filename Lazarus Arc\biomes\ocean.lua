-- biomes/ocean.lua
local OceanBiome = {
    id = "ocean",
    name = "Ocean",
    description = "A vast expanse of water with diverse marine life and underwater features.",
    
    -- Environmental factors
    environment = {
        humidity = 1.0,    -- Maximum humidity
        temperature = 0.6, -- Moderate temperature
        sunlight = 0.7,    -- Good sunlight penetration
        windStrength = 0.8 -- Strong winds
    },
    
    -- Core tile types used in this biome
    primaryTiles = {"water", "sand"},
    secondaryTiles = {"coral", "seaweed"},
    rareTiles = {"deep_water", "abyss"},
    
    -- Default proportions
    tileDistribution = {
        water = 0.6,
        sand = 0.2,
        coral = 0.1,
        seaweed = 0.05,
        deep_water = 0.03,
        abyss = 0.02
    },
    
    -- Entities common to this biome
    commonEntities = {
        "fish", "crab", "seaweed", "coral"
    },
    
    uncommonEntities = {
        "dolphin", "shark", "jellyfish", "sea_turtle"
    },
    
    rareEntities = {
        "whale", "kraken", "mermaid", "sea_serpent"
    },
    
    -- Environmental properties specific to ocean biomes
    isUnderwater = true,
    waterDepth = {min = 1, max = 20},
    hasCurrents = true,
    hasTides = true,
    hasWaves = true,
    
    -- Biome variants for diverse generation
    variants = {
        tropical = {
            name = "Tropical Ocean",
            temperature = 0.8,
            tileAdjustments = {
                water = 0.5,
                coral = 0.3,
                sand = 0.15
            },
            entityAdjustments = {
                fish = 2.0,
                coral = 2.0
            },
            specialEffects = {
                "coral_glow",
                "tropical_fish_schools"
            }
        },
        arctic = {
            name = "Arctic Ocean",
            temperature = 0.2,
            tileAdjustments = {
                water = 0.7,
                ice = 0.2,
                deep_water = 0.05
            },
            entityAdjustments = {
                penguin = 2.0,
                polar_bear = 1.5
            },
            specialEffects = {
                "ice_floating",
                "aurora_borealis"
            }
        },
        deep = {
            name = "Deep Ocean",
            waterDepth = {min = 10, max = 30},
            tileAdjustments = {
                water = 0.4,
                deep_water = 0.4,
                abyss = 0.15
            },
            entityAdjustments = {
                whale = 2.0,
                kraken = 1.5
            },
            specialEffects = {
                "bioluminescence",
                "pressure_effects"
            }
        }
    },
    
    -- Structures that can generate in this biome
    structures = {
        {
            name = "shipwreck",
            chance = 0.1,
            entities = {"ghost", "treasure"}
        },
        {
            name = "coral_reef",
            chance = 0.15,
            requiresVariant = "tropical",
            entities = {"fish", "coral"}
        },
        {
            name = "underwater_city",
            chance = 0.05,
            entities = {"mermaid", "sea_elite"}
        },
        {
            name = "iceberg",
            chance = 0.1,
            requiresVariant = "arctic",
            entities = {"penguin", "polar_bear"}
        }
    },
    
    -- Environmental effects
    environmentalEffects = {
        waves = 0.8,
        currents = 0.6,
        tides = 0.4,
        storms = 0.3,
        whirlpools = 0.1
    },
    
    -- Unique features
    features = {
        {
            name = "whirlpool",
            chance = 0.05,
            danger = true
        },
        {
            name = "coral_reef",
            chance = 0.1,
            requiresVariant = "tropical"
        },
        {
            name = "underwater_cave",
            chance = 0.08
        },
        {
            name = "sunken_treasure",
            chance = 0.03
        },
        {
            name = "seaweed_forest",
            chance = 0.12
        }
    },
    
    -- Properties specific to water biomes
    maxDepth = 50, -- In arbitrary depth units
    swimSpeed = 0.7, -- Player moves slower underwater
    breathingRequired = true, -- Player needs oxygen/breathing apparatus
    oxygenDepletion = 0.5, -- Rate at which oxygen depletes
    visibility = 20, -- How far players can see underwater
    
    -- Initialize the biome module
    init = function(self, worldCore)
        print("Ocean biome module initialized")
        
        -- Store reference to WorldCore for later use
        self.worldCore = worldCore
        
        -- Don't try to register generators at init time
        -- Instead, we'll register them when a world is created
        
        -- Store the biome in the biomes registry
        -- This allows the module to be used later when worlds are created
        print("Ocean biome registered successfully")
    end,

    -- Add a new function to register generators with a specific world instance
    registerWithWorld = function(self, world)
        print("Registering ocean biome generators with world")
        
        if not world or not world.chunkSystem then
            print("Warning: Cannot register ocean biome - world or chunkSystem is nil")
            return false
        end
        
        -- Register the biome's generators with the world's chunk system
        world.chunkSystem:registerBiomeGenerators("ocean", {
            terrain = self.generateTerrain,
            features = self.generateFeatures,
            structures = self.generateStructures,
            entities = self.populateEntities
        })
        
        return true
    end,

    -- Generate base terrain using noise functions
    generateTerrain = function(self, chunk, world, variant)
        print("Generating Ocean terrain for chunk")
        local seed = world.seed + (chunk.x * 653 + chunk.y * 841)
        math.randomseed(seed)
        
        -- Create perlin noise-like function (simplified for example)
        local function noise2D(x, y, frequency)
            return math.abs(math.sin(x * frequency) * math.cos(y * frequency))
        end
        
        -- Initialize 2D tiles array if not exists
        chunk.tiles = chunk.tiles or {}
        for x = 0, world.CHUNK_SIZE - 1 do
            chunk.tiles[x] = chunk.tiles[x] or {}
        end
        
        -- Get biome properties
        local baseTemperature = self.baseTemperature or 15
        local temperatureVariation = self.temperatureVariation or 5
        local variantData = self.variants[variant] or {}
        local temperatureOffset = variantData.temperatureOffset or 0
        local tileDistribution = variantData.tileDistribution or self.tileDistribution

        -- Generate tiles
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                -- Calculate world coordinates
                local worldX = chunk.x * world.CHUNK_SIZE + x
                local worldY = chunk.y * world.CHUNK_SIZE + y
                
                -- Generate different noise layers
                local depthNoise = noise2D(worldX, worldY, 0.05)  -- General ocean depth
                local detailNoise = noise2D(worldX, worldY, 0.2) -- Small features
                local biomeNoise = noise2D(worldX, worldY, 0.01) -- Large biome features
                
                -- Determine depth - deeper as you go further from land
                -- We'll assume distance from center (0,0) indicates distance from land
                local distanceFromLand = math.sqrt((worldX/100)^2 + (worldY/100)^2)
                local baseDepth = math.min(distanceFromLand / 2, 1.0) -- Cap at 1.0
                local finalDepth = baseDepth * 50 + depthNoise * 30 -- Scale to max depth
                
                -- Determine tile type based on depth, noise, and distribution
                local tileType = "ocean_shallow" -- Default
                
                if finalDepth > 30 then
                    tileType = "ocean_deep"
                elseif finalDepth > 15 then
                    tileType = "ocean_shallow"
                end
                
                -- Apply probability distribution for special tiles
                local tileRoll = math.random()
                local cumulativeChance = 0
                
                for tType, chance in pairs(tileDistribution) do
                    if tType ~= "ocean_deep" and tType ~= "ocean_shallow" then -- Skip basic ocean types
                        cumulativeChance = cumulativeChance + chance
                        if tileRoll <= cumulativeChance then
                            -- Check depth requirements for certain tiles
                            if tType == "ocean_trench" and finalDepth < 35 then
                                -- Trench only in deep water
                                tileType = "ocean_deep"
                            elseif tType == "coral_reef" and finalDepth > 20 then
                                -- Coral only in shallow water
                                tileType = "ocean_shallow"
                            else
                                tileType = tType
                            end
                            break
                        end
                    end
                end
                
                -- Special case: trenches are very deep areas
                if biomeNoise < 0.1 and depthNoise > 0.8 then
                    tileType = "ocean_trench"
                    finalDepth = 50 + math.random(10, 20) -- Extra deep
                end
                
                -- Special case: thermal vents in deep areas or near biome boundaries
                if tileType == "ocean_deep" and biomeNoise > 0.85 and depthNoise > 0.7 then
                    if math.random() < 0.3 then
                        tileType = "thermal_vent"
                    end
                end
                
                -- Variant-specific modifications
                if variant == "coral_paradise" and tileType == "ocean_shallow" then
                    -- More coral in coral paradise
                    if detailNoise > 0.7 then
                        tileType = "coral_reef"
                    end
                elseif variant == "kelp_forest" and tileType == "ocean_shallow" then
                    -- More seaweed in kelp forest
                    if detailNoise > 0.6 then
                        tileType = "seaweed_forest"
                    end
                elseif variant == "abyssal" and tileType == "ocean_deep" then
                    -- More trenches in abyssal
                    if depthNoise > 0.75 and biomeNoise < 0.2 then
                        tileType = "ocean_trench"
                    end
                elseif variant == "haunted_depths" and depthNoise > 0.8 then
                    -- Ruins in haunted depths
                    if math.random() < 0.15 then
                        tileType = "ancient_ruin_submerged"
                    end
                end
                
                -- Create the tile
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = math.random(1, 3),
                    passable = (tileType ~= "deep_water" and tileType ~= "coral_reef"),
                    isUnderground = false,
                    depth = finalDepth,
                    temperature = baseTemperature + temperatureOffset
                }
                
                -- Add to chunk using 2D indexing
                chunk.tiles[x][y] = tile
            end
        end
        
        -- Place features
        self.generateFeatures(chunk, world, variant)
        
        -- Add entities
        self.populateEntities(chunk, world, variant)
        
        -- Place structures
        self.generateStructures(chunk, world, variant)
        
        -- Post-processing: add general ocean properties to all tiles
        -- Check if chunk.tiles is actually a 2D array before iterating
        if type(chunk.tiles) == "table" then
            for x = 0, world.CHUNK_SIZE - 1 do
                if type(chunk.tiles[x]) == "table" then
                    for y = 0, world.CHUNK_SIZE - 1 do
                        local tile = chunk.tiles[x][y]
                        if type(tile) == "table" then -- Ensure tile is a table
                            -- Swimming effects
                            tile.requiresSwimming = true
                            tile.swimSpeed = self.swimSpeed
                            
                            -- Oxygen depletion for non-aquatic creatures
                            tile.oxygenDepletion = self.oxygenDepletion
                            
                            -- Water pressure increases with depth
                            tile.waterPressure = tile.depth / 10
                            
                            -- Water currents (occasional)
                            if math.random() < 0.05 then
                                tile.hasCurrent = true
                                tile.currentDirection = math.random(0, 7) -- 8 possible directions
                                tile.currentStrength = math.random(10, 30) / 100 -- 0.1 to 0.3
                            end
                        end
                    end
                end
            end
        end
    end,

    -- Helper function to generate features
    generateFeatures = function(self, chunk, world, variant)
        print("Generating Ocean features for chunk")
        -- Iterate through each potential feature
        for _, feature in ipairs(self.features) do
            -- Check if feature should spawn based on chance
            if math.random() < feature.chance then
                -- Check variant requirements if any
                if feature.requiresVariant and feature.requiresVariant ~= variant then
                    goto continue
                end
                
                -- For unique features, only place one per chunk
                if feature.unique and chunk.uniqueFeatures and chunk.uniqueFeatures[feature.name] then
                    goto continue
                end
                
                -- Determine position for feature
                local featureX = math.random(2, world.CHUNK_SIZE - 3)
                local featureY = math.random(2, world.CHUNK_SIZE - 3)
                
                -- Place feature based on type
                if feature.name == "blue_hole" then
                    -- Create a deep underwater hole/chasm
                    local radius = math.random(5, 10)
                    local depth = math.random(feature.depth.min, feature.depth.max)
                    
                    for dx = -radius, radius do
                        for dy = -radius, radius do
                            local dist = math.sqrt(dx*dx + dy*dy)
                            if dist <= radius then
                                local tileX = featureX + dx
                                local tileY = featureY + dy
                                
                                if tileX >= 0 and tileX < world.CHUNK_SIZE and
                                   tileY >= 0 and tileY < world.CHUNK_SIZE then
                                    local tile = chunk.tiles[tileX][tileY]
                                    
                                    -- Set to ocean trench tile and increase depth
                                    tile.type = "ocean_trench"
                                    tile.depth = depth * (1 - dist/radius) -- Deeper in center
                                    
                                    -- Special properties
                                    tile.currentStrength = 0.3 -- Gentle current pulling toward center
                                    tile.mysterious = true
                                    
                                    -- Center of blue hole might have special entities
                                    if dist < 2 and math.random() < 0.5 then
                                        local worldX = chunk.x * world.CHUNK_SIZE + tileX
                                        local worldY = chunk.y * world.CHUNK_SIZE + tileY
                                        
                                        local deepEntities = {"deep_sea_creature", "ancient_relic", "bioluminescent_crystal"}
                                        local entityType = deepEntities[math.random(#deepEntities)]
                                        
                                        world.entitySystem:addEntity(entityType, worldX, worldY, {
                                            depth = depth,
                                            mysterious = true
                                        })
                                    end
                                end
                            end
                        end
                    end
                    
                elseif feature.name == "shipwreck" then
                    -- Create a shipwreck
                    local shipLength = math.random(8, 15)
                    local shipWidth = math.random(3, 5)
                    
                    -- Determine ship age and type
                    local ageRoll = math.random()
                    local shipAge, shipType
                    
                    if ageRoll < feature.age.ancient then
                        shipAge = "ancient"
                        shipType = "trireme"
                    elseif ageRoll < feature.age.ancient + feature.age.colonial then
                        shipAge = "colonial"
                        shipType = "galleon"
                    else
                        shipAge = "modern"
                        shipType = "freighter"
                    end
                    
                    -- Random rotation (0, 90, 180, 270 degrees)
                    local rotation = math.random(0, 3) * 90
                    local rotRad = rotation * math.pi / 180
                    
                    -- Create ship based on rotation
                    for i = -shipLength/2, shipLength/2 do
                        for j = -shipWidth/2, shipWidth/2 do
                            -- Rotate coordinates
                            local rx, ry
                            if rotation == 0 then
                                rx, ry = i, j
                            elseif rotation == 90 then
                                rx, ry = -j, i
                            elseif rotation == 180 then
                                rx, ry = -i, -j
                            else -- 270
                                rx, ry = j, -i
                            end
                            
                            local tileX = featureX + rx
                            local tileY = featureY + ry
                            
                            if tileX >= 0 and tileX < world.CHUNK_SIZE and
                               tileY >= 0 and tileY < world.CHUNK_SIZE then
                                local tile = chunk.tiles[tileX][tileY]
                                
                                -- Mark as shipwreck tile
                                tile.shipwreck = true
                                tile.shipAge = shipAge
                                tile.shipType = shipType
                                
                                -- Add ship part entity
                                local worldX = chunk.x * world.CHUNK_SIZE + tileX
                                local worldY = chunk.y * world.CHUNK_SIZE + tileY
                                
                                -- Different parts based on position
                                local partType
                                if math.abs(i) > shipLength/2 - 2 then
                                    -- Bow or stern
                                    partType = i > 0 and "ship_bow" or "ship_stern"
                                elseif math.abs(j) > shipWidth/2 - 2 then
                                    -- Hull sides
                                    partType = "ship_hull"
                                elseif math.abs(i) < 2 and math.abs(j) < 2 then
                                    -- Ship center - special feature
                                    partType = shipAge == "modern" and "ship_bridge" or "ship_deck"
                                    
                                    -- Add treasure or cargo
                                    if math.random() < 0.7 then
                                        world.entitySystem:addEntity("treasure_chest", worldX, worldY, {
                                            age = shipAge,
                                            quality = math.random(1, 5)
                                        })
                                    end
                                else
                                    -- Regular deck
                                    partType = "ship_deck"
                                end
                                
                                world.entitySystem:addEntity(partType, worldX, worldY, {
                                    age = shipAge,
                                    rotation = rotation
                                })
                            end
                        end
                    end
                    
                elseif feature.name == "underwater_cave_system" then
                    -- Create a system of underwater caves
                    local caveSize = math.random(feature.size.min, feature.size.max)
                    
                    -- Mark center tile as cave entrance
                    local entranceX = featureX
                    local entranceY = featureY
                    local entranceWorldX = chunk.x * world.CHUNK_SIZE + entranceX
                    local entranceWorldY = chunk.y * world.CHUNK_SIZE + entranceY
                    
                    local tile = chunk.tiles[entranceX][entranceY]
                    tile.type = "underwater_cave"
                    tile.isEntrance = true
                    
                    -- Add cave entrance entity
                    world.entitySystem:addEntity("cave_entrance", entranceWorldX, entranceWorldY, {
                        caveSize = caveSize,
                        caveDepth = math.random(1, 3),
                        hasTreasure = math.random() < 0.3,
                        hasAir = math.random() < 0.2 -- Some caves might have air pockets
                    })
                    
                elseif feature.name == "giant_coral_formation" then
                    -- Create a large coral structure
                    local coralRadius = math.random(5, 9)
                    
                    for dx = -coralRadius, coralRadius do
                        for dy = -coralRadius, coralRadius do
                            local dist = math.sqrt(dx*dx + dy*dy)
                            if dist <= coralRadius and math.random() < (1 - dist/coralRadius) then
                                local tileX = featureX + dx
                                local tileY = featureY + dy
                                
                                if tileX >= 0 and tileX < world.CHUNK_SIZE and
                                   tileY >= 0 and tileY < world.CHUNK_SIZE then
                                    local tile = chunk.tiles[tileX][tileY]
                                    
                                    -- Set to coral reef
                                    tile.type = "coral_reef"
                                    
                                    -- Add coral entities
                                    if math.random() < 0.4 then
                                        local worldX = chunk.x * world.CHUNK_SIZE + tileX
                                        local worldY = chunk.y * world.CHUNK_SIZE + tileY
                                        
                                        local coralTypes = {"brain_coral", "staghorn_coral", "table_coral", "soft_coral"}
                                        local coralType = coralTypes[math.random(#coralTypes)]
                                        
                                        world.entitySystem:addEntity(coralType, worldX, worldY, {
                                            size = math.random(10, 30) / 10,
                                            color = math.random(1, 5) -- Different coral colors
                                        })
                                    end
                                end
                            end
                        end
                    end
                    
                elseif feature.name == "thermal_vent_field" then
                    -- Create a field of thermal vents
                    local ventCount = math.random(5, 12)
                    local fieldRadius = math.random(6, 10)
                    
                    for i = 1, ventCount do
                        local angle = math.random() * math.pi * 2
                        local distance = fieldRadius * math.sqrt(math.random())
                        
                        local ventX = math.floor(featureX + math.cos(angle) * distance)
                        local ventY = math.floor(featureY + math.sin(angle) * distance)
                        
                        if ventX >= 0 and ventX < world.CHUNK_SIZE and
                           ventY >= 0 and ventY < world.CHUNK_SIZE then
                            local tile = chunk.tiles[ventX][ventY]
                            tile.type = "thermal_vent"
                            
                            -- Add thermal vent entity
                            local worldX = chunk.x * world.CHUNK_SIZE + ventX
                            local worldY = chunk.y * world.CHUNK_SIZE + ventY
                            
                            world.entitySystem:addEntity("thermal_vent", worldX, worldY, {
                                temperature = 150 + math.random(0, 100),
                                activity = math.random(1, 5), -- Different activity levels
                                hasMineral = math.random() < 0.3 -- Some vents have mineral deposits
                            })
                        end
                    end
                    
                elseif feature.name == "sunken_city" then
                    -- Create a sunken city
                    local cityRadius = math.random(10, 15)
                    
                    -- Create city grid layout
                    local streetWidth = 2
                    local blockSize = 5
                    
                    for dx = -cityRadius, cityRadius do
                        for dy = -cityRadius, cityRadius do
                            local dist = math.sqrt(dx*dx + dy*dy)
                            if dist <= cityRadius then
                                local tileX = featureX + dx
                                local tileY = featureY + dy
                                
                                if tileX >= 0 and tileX < world.CHUNK_SIZE and
                                   tileY >= 0 and tileY < world.CHUNK_SIZE then
                                    local tile = chunk.tiles[tileX][tileY]
                                    
                                    -- Grid-based city layout
                                    local isStreet = (dx % (blockSize + streetWidth) < streetWidth) or
                                                     (dy % (blockSize + streetWidth) < streetWidth)
                                    
                                    if isStreet then
                                        -- Streets
                                        tile.type = "ancient_ruin_submerged"
                                        tile.variant = 1 -- Street variant
                                    else
                                        -- Building blocks
                                        tile.type = "ancient_ruin_submerged"
                                        tile.variant = 2 -- Building variant
                                        
                                        -- Add building ruins
                                        if math.random() < 0.4 then
                                            local worldX = chunk.x * world.CHUNK_SIZE + tileX
                                            local worldY = chunk.y * world.CHUNK_SIZE + tileY
                                            
                                            local ruinTypes = {"collapsed_pillar", "sunken_statue", "building_remains"}
                                            local ruinType = ruinTypes[math.random(#ruinTypes)]
                                            
                                            world.entitySystem:addEntity(ruinType, worldX, worldY, {
                                                size = math.random(10, 30) / 10,
                                                age = "ancient"
                                            })
                                        end
                                    end
                                end
                            end
                        end
                    end
                end
                
                -- Mark unique features as placed
                if feature.unique then
                    if not chunk.uniqueFeatures then
                        chunk.uniqueFeatures = {}
                    end
                    chunk.uniqueFeatures[feature.name] = true
                end
            end
            
            ::continue::
        end
    end,

    -- Helper function to add entities
    populateEntities = function(self, chunk, world, variant)
        print("Generating Ocean entities for chunk")
        -- Get variant-specific entity adjustments
        local variantData = variant and self.variants[variant] or nil
        
        -- Helper function to spawn an entity with variant adjustments
        local function spawnEntity(entityType, count)
            -- Apply variant adjustment if available
            local multiplier = 1.0
            if variantData and variantData.entityAdjustments and variantData.entityAdjustments[entityType] then
                multiplier = variantData.entityAdjustments[entityType]
            end
            
            -- Adjust count based on variant
            local adjustedCount = math.floor(count * multiplier + 0.5)
            
            -- Spawn entities
            for i = 1, adjustedCount do
                -- Pick a random position in the chunk
                local x = math.random(0, world.CHUNK_SIZE - 1)
                local y = math.random(0, world.CHUNK_SIZE - 1)
                local tile = chunk.tiles[x][y]
                
                -- Check if suitable for this entity type
                local suitable = true
                if entityType == "coral_fragment" and tile.type ~= "coral_reef" then
                    suitable = false
                elseif entityType == "kelp" and tile.type ~= "seaweed_forest" then
                    suitable = false
                elseif (entityType == "angler_fish" or entityType == "deep_sea_creature") and tile.type ~= "ocean_deep" and tile.type ~= "ocean_trench" then
                    suitable = false
                elseif (entityType == "small_fish" or entityType == "tropical_fish") and tile.type == "ocean_trench" then
                    suitable = false
                end
                
                if suitable then
                    local worldX = chunk.x * world.CHUNK_SIZE + x
                    local worldY = chunk.y * world.CHUNK_SIZE + y
                    
                    -- Spawn the entity
                    world.entitySystem:addEntity(entityType, worldX, worldY)
                end
            end
        end
        
        -- Spawn common entities
        for _, entityType in ipairs(self.commonEntities) do
            local count = math.random(2, 5)
            spawnEntity(entityType, count)
        end
        
        -- Spawn uncommon entities
        for _, entityType in ipairs(self.uncommonEntities) do
            local count = math.random(1, 2)
            spawnEntity(entityType, count)
        end
        
        -- Spawn rare entities
        for _, entityType in ipairs(self.rareEntities) do
            if math.random() < 0.2 then -- 20% chance for each rare entity
                spawnEntity(entityType, 1)
            end
        end
        
        -- Add variant-specific entities
        if variant == "tropical" then
            spawnEntity("tropical_fish", math.random(5, 10))
            spawnEntity("sea_turtle", math.random(1, 3))
        elseif variant == "kelp_forest" then
            spawnEntity("kelp", math.random(10, 20))
            spawnEntity("sea_otter", math.random(1, 3))
        elseif variant == "abyssal" then
            spawnEntity("angler_fish", math.random(2, 5))
            spawnEntity("deep_sea_creature", math.random(1, 3))
        elseif variant == "coral_paradise" then
            spawnEntity("clownfish", math.random(3, 8))
            spawnEntity("coral_fragment", math.random(5, 15))
        elseif variant == "haunted_depths" then
            spawnEntity("ghost_ship", math.random(0, 1))
            spawnEntity("sea_spirit", math.random(1, 4))
        end
    end,

    -- Helper function to place structures
    generateStructures = function(self, chunk, world, variant)
        print("Generating Ocean structures for chunk")
        -- Go through each structure type
        for _, structure in ipairs(self.structures) do
            -- Check chance and variant requirements
            if math.random() < structure.chance and
               (not structure.requiresVariant or structure.requiresVariant == variant) then
                
                -- Find suitable location for structure
                local structX = math.random(3, world.CHUNK_SIZE - 4)
                local structY = math.random(3, world.CHUNK_SIZE - 4)
                local worldX = chunk.x * world.CHUNK_SIZE + structX
                local worldY = chunk.y * world.CHUNK_SIZE + structY
                
                -- Place structure
                world.chunkSystem:placeStructure(chunk, structure.name, worldX, worldY)
                
                -- Add associated entities
                if structure.entities then
                    for _, entityType in ipairs(structure.entities) do
                        -- Add entity near structure
                        local offsetX = math.random(-2, 2)
                        local offsetY = math.random(-2, 2)
                        world.entitySystem:addEntity(entityType, worldX + offsetX, worldY + offsetY)
                    end
                end
                
                -- Special handling for different structure types
                if structure.name == "sunken_ship" then
                    -- Create a ship hull entity
                    world.entitySystem:addEntity("ship_hull", worldX, worldY, {
                        size = math.random(10, 20),
                        age = math.random() < 0.3 and "ancient" or (math.random() < 0.7 and "colonial" or "modern"),
                        rotation = math.random(0, 3) * 90,
                        damaged = true
                    })
                    
                    -- Add some debris around
                    for i = 1, math.random(3, 7) do
                        local debrisX = worldX + math.random(-5, 5)
                        local debrisY = worldY + math.random(-5, 5)
                        
                        world.entitySystem:addEntity("ship_debris", debrisX, debrisY)
                    end
                    
                elseif structure.name == "underwater_cave" then
                    -- Mark the tile as a cave entrance
                    local tile = chunk.tiles[structX][structY]
                    tile.type = "underwater_cave"
                    tile.isEntrance = true
                    
                    -- Add cave entrance entity
                    world.entitySystem:addEntity("cave_entrance", worldX, worldY, {
                        caveDepth = math.random(1, 3),
                        hasTreasure = math.random() < 0.3
                    })
                    
                elseif structure.name == "coral_castle" then
                    -- Create a structure of living coral
                    local castleRadius = math.random(4, 7)
                    
                    for dx = -castleRadius, castleRadius do
                        for dy = -castleRadius, castleRadius do
                            local dist = math.max(math.abs(dx), math.abs(dy))
                            if dist <= castleRadius then
                                local tileX = structX + dx
                                local tileY = structY + dy
                                
                                if tileX >= 0 and tileX < world.CHUNK_SIZE and
                                   tileY >= 0 and tileY < world.CHUNK_SIZE then
                                    local castleTile = chunk.tiles[tileX][tileY]
                                    
                                    -- Set to coral reef
                                    castleTile.type = "coral_reef"
                                    
                                    -- Create castle walls
                                    if dist == castleRadius or 
                                       (dx == 0 and math.abs(dy) <= castleRadius) or
                                       (dy == 0 and math.abs(dx) <= castleRadius) then
                                        local wallX = chunk.x * world.CHUNK_SIZE + tileX
                                        local wallY = chunk.y * world.CHUNK_SIZE + tileY
                                        
                                        world.entitySystem:addEntity("coral_wall", wallX, wallY, {
                                            height = 3,
                                            color = math.random(1, 5)
                                        })
                                    end
                                end
                            end
                        end
                    end
                end
            end
        end
    end,

    -- Apply post-processing effects
    postProcess = function(self, chunk, world, variant)
        print("Post-processing Ocean chunk")
        local variantData = variant and self.variants[variant] or nil
        
        -- Apply variant-specific special effects
        if variantData and variantData.specialEffects then
            chunk.environmentalEffects = chunk.environmentalEffects or {}
            
            for _, effect in ipairs(variantData.specialEffects) do
                table.insert(chunk.environmentalEffects, effect)
            end
        end
        
        -- Add extra dangers for some variants
        if variantData and variantData.extraDangers then
            chunk.extraDangers = chunk.extraDangers or {}
            
            for _, danger in ipairs(variantData.extraDangers) do
                table.insert(chunk.extraDangers, danger)
            end
        end
        
        -- Process specific variant effects
        if variant == "abyssal" then
            -- Make abyssal depths darker and more dangerous
            for _, tile in pairs(chunk.tiles) do
                tile.lightLevel = tile.lightLevel or 1.0
                tile.lightLevel = tile.lightLevel * 0.5
                
                if tile.type == "ocean_trench" then
                    tile.dangerLevel = (tile.dangerLevel or 0) + 2
                end
            end
        elseif variant == "coral_paradise" then
            -- Add more vibrant colors to coral paradise
            for _, tile in pairs(chunk.tiles) do
                if tile.type == "coral_reef" then
                    tile.colorMultiplier = {1.2, 1.2, 1.1}
                    tile.glowIntensity = math.random(5, 15) / 10
                end
            end
        elseif variant == "haunted_depths" then
            -- Make haunted depths eerie
            for _, tile in pairs(chunk.tiles) do
                tile.fogDensity = (tile.fogDensity or 0) + 0.5
                
                if tile.type == "ancient_ruin_submerged" then
                    tile.hauntedLevel = math.random(1, 5)
                    tile.whisperingChance = 0.05
                end
            end
        end
    end
}

return OceanBiome