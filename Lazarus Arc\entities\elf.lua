-- entities/elf.lua
-- Elven entity with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local Elf = {
    id = "elf",
    name = "Elf",
    type = "elf",
    shape = {
        {0, -1}, {0.8, -0.6}, {1, 0}, {0.8, 0.6},
        {0, 1}, {-0.8, 0.6}, {-1, 0}, {-0.8, -0.6}
    },
    size = 7,
    description = "An elegant elven being with natural grace and magical affinity",

    -- Entity categories
    categories = {"humanoid", "elf", "magical", "intelligent"},
    threatCategories = {"player", "orc", "undead"},
    
    -- Base stats (agile and magical)
    maxHealth = 80,
    health = 80,
    maxStamina = 70,
    stamina = 70,
    maxMana = 100,
    mana = 100,
    speed = 2.5,
    attack = 12,
    defense = 6,
    magicAttack = 15,
    magicDefense = 12,
    
    -- Behaviors (elves are more sophisticated)
    behaviors = {"wander", "meditate", "cast_magic", "flee_from_threats"},
    behaviorConfigs = {
        wander = {
            moveSpeed = 2.0,
            changeDirectionChance = 0.03,
            wanderRadius = 8,
            preferNature = true
        },
        meditate = {
            duration = {10, 20},
            manaRegenBoost = 2.0,
            frequency = 0.2
        },
        cast_magic = {
            spells = {"nature_bolt", "heal", "barrier"},
            castChance = 0.15,
            manaCost = 20
        },
        flee_from_threats = {
            moveSpeed = 3.5,
            detectionRadius = 12,
            fleeDistance = 15
        }
    },
    
    -- Enhanced variant system for elves
    variantChances = {
        normal = 0.75,          -- 75% normal elf
        shiny = 0.15,           -- 15% high elf (shiny)
        rare = 0.08,            -- 8% ancient elf (rare)
        legendary = 0.02        -- 2% archelf (legendary)
    },
    
    variants = {
        normal = {
            name = "Wood Elf",
            description = "A common forest-dwelling elf",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.9, 1.1, 0.9, 1.0}  -- Slight green tint
            }
        },
        
        shiny = {
            name = "High Elf",
            description = "A noble elf with enhanced magical abilities",
            statModifiers = {
                maxHealth = 1.3,
                maxMana = 1.5,
                magicAttack = 1.4,
                magicDefense = 1.3,
                speed = 1.2
            },
            appearanceModifiers = {
                scale = 1.1,
                glow = true,
                colorTint = {1.2, 1.2, 1.4, 1.0},  -- Blue-white tint
                aura = "magical"
            },
            soundModifiers = {
                pitch = 1.3,
                volume = 1.1,
                magical_resonance = true
            }
        },
        
        rare = {
            name = "Ancient Elf",
            description = "An elf of great age and wisdom",
            statModifiers = {
                maxHealth = 1.6,
                maxMana = 2.0,
                magicAttack = 1.7,
                magicDefense = 1.6,
                speed = 1.1,
                wisdom = 2.0
            },
            appearanceModifiers = {
                scale = 1.15,
                colorTint = {1.3, 1.1, 1.3, 1.0},  -- Purple-silver tint
                ancient_markings = true
            },
            soundModifiers = {
                pitch = 0.9,
                volume = 1.3,
                reverb = true,
                ancient_echo = true
            }
        },
        
        legendary = {
            name = "Archelf",
            description = "A legendary elf lord with immense power",
            statModifiers = {
                maxHealth = 2.5,
                maxMana = 3.0,
                magicAttack = 2.2,
                magicDefense = 2.0,
                speed = 1.4,
                wisdom = 3.0
            },
            appearanceModifiers = {
                scale = 1.3,
                glow = true,
                colorTint = {1.5, 1.3, 1.8, 1.0},  -- Radiant purple-gold
                aura = "legendary",
                crown_of_stars = true
            },
            soundModifiers = {
                pitch = 0.8,
                volume = 1.6,
                reverb = true,
                echo = true,
                celestial_harmony = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "elven_cloth", chance = 0.7, quantity = {1, 2}},
        {id = "nature_essence", chance = 0.6, quantity = {1, 1}},
        {id = "silver_coin", chance = 0.8, quantity = {2, 5}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "high_elven_silk", chance = 0.8, quantity = {1, 2}},
            {id = "mana_crystal", chance = 0.6, quantity = {1, 1}},
            {id = "elven_jewelry", chance = 0.4, quantity = {1, 1}}
        },
        rare = {
            {id = "ancient_elven_tome", chance = 0.7, quantity = {1, 1}},
            {id = "wisdom_crystal", chance = 0.6, quantity = {1, 1}},
            {id = "ancient_elven_artifact", chance = 0.3, quantity = {1, 1}},
            {id = "moonstone", chance = 0.5, quantity = {1, 2}}
        },
        legendary = {
            {id = "archelf_crown_fragment", chance = 0.9, quantity = {1, 1}},
            {id = "star_essence", chance = 0.8, quantity = {1, 3}},
            {id = "legendary_elven_weapon", chance = 0.4, quantity = {1, 1}},
            {id = "celestial_orb", chance = 0.6, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "elf",
        scale = 1.0,
        animations = {
            "idle", "walk", "cast_spell", "meditate", "death"
        },
        variants = {
            "wood_elf", "high_elf", "ancient_elf", "archelf"
        }
    },
    
    -- Sound effects with elven characteristics
    sounds = {
        idle = {
            file = "elf_idle",
            synth = {
                instrument = "harp",
                notes = {"A4", "C5", "E5"},
                durations = {0.3, 0.4, 0.5},
                volume = 0.25
            }
        },
        cast_spell = {
            file = "elf_cast",
            synth = {
                instrument = "celesta",
                notes = {"C5", "E5", "G5", "C6"},
                durations = {0.2, 0.2, 0.2, 0.4},
                volume = 0.4
            }
        },
        hurt = {
            file = "elf_hurt",
            synth = {
                instrument = "harp",
                notes = {"F#4"},
                duration = 0.3,
                volume = 0.4,
                vibrato = true
            }
        },
        death = {
            file = "elf_death",
            synth = {
                instrument = "celesta",
                notes = {"A4", "F4", "D4", "A3"},
                durations = {0.5, 0.5, 0.5, 1.0},
                volume = 0.5,
                fade_out = true
            }
        },
        meditate = {
            file = "elf_meditate",
            synth = {
                instrument = "pad",
                notes = {"C4", "E4", "G4"},
                duration = 2.0,
                volume = 0.2,
                ambient = true
            }
        }
    },
    
    -- Special abilities
    abilities = {
        nature_magic = {
            type = "passive",
            description = "Natural affinity with nature magic",
            effect = "mana_regen_boost"
        },
        keen_senses = {
            type = "passive", 
            description = "Enhanced perception and awareness",
            effect = "detection_radius_boost"
        },
        elven_grace = {
            type = "passive",
            description = "Natural agility and grace",
            effect = "dodge_chance_boost"
        }
    }
}

-- Initialize the elf entity using enhanced template
function Elf.init(entity, world)
    -- Copy all fields from Elf template to entity instance
    for k, v in pairs(Elf) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return Elf
