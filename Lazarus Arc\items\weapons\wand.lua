-- items/weapons/wand.lua

local Weapon = {
    id = "wand",
    name = "Wand",
    description = "A slender magical implement that focuses arcane energies.",
    type = "wand",
    category = "weapon",
    stackable = false,

    stats = {
        attack_power = 3.0,
        attack_speed = 1.2,
        magic_power = 12.0,
        durability = 60.0,
        repair_cost = 25,
        weight = 1.0,
    },

    damage_type = "magical",
    material = "wood",

    sprite = "res://Sprite/items/weapons/wand.png",
    size = { width = 64, height = 64 },

    effects = {
        magic_boost = 0.08,
        spell_accuracy = 0.05,
        mana_efficiency = 0.05
    },

    meta = {
        rarity = "uncommon"
    },

    take_damage = function(self, amount)
        self.stats.durability = math.max(0, self.stats.durability - amount)
        if self.stats.durability == 0 then
            print(self.name .. " is broken! Penalties applied.")
            -- Potential penalties could be applied here
        end
    end,

    repair = function(self)
        local repair_amount = 60 - self.stats.durability
        self.stats.durability = 60
        print(self.name .. " repaired by " .. repair_amount .. " points.")
    end,
    
    onEquip = function(self, user)
        if user and user.stats then
            -- Apply magic boost
            if user.stats.magic_power then
                user.stats.magic_power = user.stats.magic_power * (1 + self.effects.magic_boost)
            end
            
            -- Apply spell accuracy
            if user.stats.spell_accuracy then
                user.stats.spell_accuracy = user.stats.spell_accuracy + self.effects.spell_accuracy
            else
                user.stats.spell_accuracy = self.effects.spell_accuracy
            end
            
            -- Apply mana efficiency
            if user.stats.mana_cost_modifier then
                user.stats.mana_cost_modifier = user.stats.mana_cost_modifier * (1 - self.effects.mana_efficiency)
            else
                user.stats.mana_cost_modifier = 1 - self.effects.mana_efficiency
            end
            
            print(user.name .. " equipped the " .. self.name .. ", gaining magical bonuses.")
        end
    end,
    
    onUnequip = function(self, user)
        if user and user.stats then
            -- Remove magic boost
            if user.stats.magic_power then
                user.stats.magic_power = user.stats.magic_power / (1 + self.effects.magic_boost)
            end
            
            -- Remove spell accuracy
            if user.stats.spell_accuracy then
                user.stats.spell_accuracy = user.stats.spell_accuracy - self.effects.spell_accuracy
            end
            
            -- Remove mana efficiency
            if user.stats.mana_cost_modifier then
                user.stats.mana_cost_modifier = user.stats.mana_cost_modifier / (1 - self.effects.mana_efficiency)
            end
            
            print(user.name .. " unequipped the " .. self.name .. ", losing magical bonuses.")
        end
    end
}

return Weapon
