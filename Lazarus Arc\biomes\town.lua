-- biomes/town.lua
local TownBiome = {
    id = "town",
    name = "Town",
    description = "A bustling settlement with buildings, roads, and various amenities.",
    
    -- Environmental factors
    environment = {
        humidity = 0.5,    -- Moderate humidity
        temperature = 0.6, -- Comfortable temperature
        sunlight = 0.7,    -- Good sunlight
        windStrength = 0.3 -- Light winds
    },
    
    -- Core tile types used in this biome
    primaryTiles = {"road", "building"},
    secondaryTiles = {"grass", "garden"},
    rareTiles = {"marketplace", "town_square"},
    
    -- Default proportions
    tileDistribution = {
        road = 0.3,
        building = 0.4,
        grass = 0.2,
        garden = 0.05,
        marketplace = 0.03,
        town_square = 0.02
    },
    
    -- Entities common to this biome
    commonEntities = {
        "villager", "merchant", "guard", "dog"
    },
    
    uncommonEntities = {
        "noble", "priest", "blacksmith", "farmer"
    },
    
    rareEntities = {
        "mayor", "wizard", "royal_guard", "traveling_merchant"
    },
    
    -- Environmental properties specific to town biomes
    hasBuildings = true,
    hasRoads = true,
    hasGardens = true,
    hasMarkets = true,
    
    -- Biome variants for diverse generation
    variants = {
        prosperous = {
            name = "Prosperous Town",
            temperature = 0.7,
            tileAdjustments = {
                building = 0.5,
                marketplace = 0.1,
                garden = 0.1
            },
            entityAdjustments = {
                merchant = 2.0,
                noble = 1.5
            },
            specialEffects = {
                "wealth_aura",
                "busy_streets"
            }
        },
        medieval = {
            name = "Medieval Town",
            temperature = 0.5,
            tileAdjustments = {
                building = 0.4,
                road = 0.3,
                grass = 0.2
            },
            entityAdjustments = {
                guard = 1.5,
                blacksmith = 1.2
            },
            specialEffects = {
                "castle_shadows",
                "medieval_ambience"
            }
        },
        magical = {
            name = "Magical Town",
            temperature = 0.6,
            tileAdjustments = {
                building = 0.4,
                garden = 0.2,
                marketplace = 0.1
            },
            entityAdjustments = {
                wizard = 2.0,
                magical_creature = 1.5
            },
            specialEffects = {
                "magic_glow",
                "floating_lights"
            }
        }
    },
    
    -- Structures that can generate in this biome
    structures = {
        {
            name = "town_hall",
            chance = 0.1,
            entities = {"mayor", "clerk"}
        },
        {
            name = "marketplace",
            chance = 0.15,
            entities = {"merchant", "customer"}
        },
        {
            name = "guard_tower",
            chance = 0.08,
            entities = {"guard", "watchman"}
        },
        {
            name = "wizard_tower",
            chance = 0.05,
            requiresVariant = "magical",
            entities = {"wizard", "apprentice"}
        }
    },
    
    -- Environmental effects
    environmentalEffects = {
        crowd_noise = 0.8,
        market_bustle = 0.6,
        church_bells = 0.4,
        street_music = 0.3,
        festival = 0.1
    },
    
    -- Unique features
    features = {
        {
            name = "town_square",
            chance = 0.1,
            unique = true
        },
        {
            name = "marketplace",
            chance = 0.08,
            requiresVariant = "prosperous"
        },
        {
            name = "wizard_tower",
            chance = 0.05,
            requiresVariant = "magical"
        },
        {
            name = "guard_post",
            chance = 0.12
        },
        {
            name = "garden",
            chance = 0.15
        }
    },
    
    -- Weather patterns (mostly normal but with town-specific effects)
    weather = {
        clear = 0.6,
        rain = 0.2,
        cloudy = 0.15,
        storm = 0.05
    },
    
    -- Generation algorithm for this biome
    generate = function(chunk, world, variant)
        print("Generating " .. (variant and TownBiome.variants[variant].name or "Town") .. " biome")
        
        -- Get variant-specific adjustments
        local variantData = variant and TownBiome.variants[variant] or nil
        local populationDensity = variantData and variantData.populationDensity or 0.7
        local wealth = variantData and variantData.wealth or 0.6
        
        -- Adjust tile distribution based on variant
        local tileDistribution = {}
        for tileType, chance in pairs(TownBiome.tileDistribution) do
            tileDistribution[tileType] = chance
            
            -- Apply variant adjustments if any
            if variantData and variantData.tileAdjustments and variantData.tileAdjustments[tileType] then
                tileDistribution[tileType] = variantData.tileAdjustments[tileType]
            end
        end
        
        -- Add variant-specific tiles if they don't exist in base distribution
        if variantData and variantData.tileAdjustments then
            for tileType, chance in pairs(variantData.tileAdjustments) do
                if not tileDistribution[tileType] then
                    tileDistribution[tileType] = chance
                end
            end
        end
        
        -- Generate base terrain using noise functions
        local seed = world.seed + (chunk.x * 743 + chunk.y * 391)
        math.randomseed(seed)
        
        -- Create perlin noise-like function (simplified for example)
        local function noise2D(x, y, frequency)
            return math.abs(math.sin(x * frequency) * math.cos(y * frequency))
        end
        
        -- Define roads and paths first
        -- Towns should have a more structured layout
        
        -- Create a grid-based road system
        local gridSize = 8 -- Size of town blocks
        local roadWidth = 2 -- Width of main roads
        local pathWidth = 1 -- Width of smaller paths
        
        -- Add some randomization to make it less perfectly grid-like
        local jitterAmount = 1 -- How much to offset the grid lines
        
        -- Generate road grid with center point
        local centerX = math.floor(world.CHUNK_SIZE / 2)
        local centerY = math.floor(world.CHUNK_SIZE / 2)
        
        -- Create town layout
        local townLayout = {}
        for x = 0, world.CHUNK_SIZE - 1 do
            townLayout[x] = {}
            for y = 0, world.CHUNK_SIZE - 1 do
                townLayout[x][y] = 0 -- 0 = not defined yet
            end
        end
        
        -- Place town square at or near center
        local squareSize = math.random(10, 15)
        local squareX = centerX + math.random(-5, 5)
        local squareY = centerY + math.random(-5, 5)
        
        -- Ensure square is within chunk bounds
        squareX = math.max(squareSize / 2, math.min(world.CHUNK_SIZE - squareSize / 2, squareX))
        squareY = math.max(squareSize / 2, math.min(world.CHUNK_SIZE - squareSize / 2, squareY))
        
        -- Place town square
        for x = squareX - squareSize / 2, squareX + squareSize / 2 do
            for y = squareY - squareSize / 2, squareY + squareSize / 2 do
                local tileX = math.floor(x)
                local tileY = math.floor(y)
                
                if tileX >= 0 and tileX < world.CHUNK_SIZE and 
                   tileY >= 0 and tileY < world.CHUNK_SIZE then
                    townLayout[tileX][tileY] = 1 -- 1 = town square
                end
            end
        end
        
        -- Create main roads extending from town square
        local roadDirections = {
            {dx = 1, dy = 0},  -- East
            {dx = -1, dy = 0}, -- West
            {dx = 0, dy = 1},  -- South
            {dx = 0, dy = -1}  -- North
        }
        
        -- Add some randomization to road directions based on town variant
        if variant == "fishing" then
            -- Fishing villages often have curved layouts following coastline
            -- Add diagonal roads
            table.insert(roadDirections, {dx = 1, dy = 1})   -- Southeast
            table.insert(roadDirections, {dx = -1, dy = 1})  -- Southwest
        elseif variant == "mining" then
            -- Mining towns often develop along a single main road
            -- Remove some directions for a more linear town
            if math.random() < 0.5 then
                roadDirections = {
                    {dx = 1, dy = 0},  -- East
                    {dx = -1, dy = 0}  -- West
                }
            else
                roadDirections = {
                    {dx = 0, dy = 1},  -- South
                    {dx = 0, dy = -1}  -- North
                }
            end
        elseif variant == "noble" then
            -- Noble quarters often have a more planned, geometric layout
            -- Add diagonal roads for a star pattern
            table.insert(roadDirections, {dx = 1, dy = 1})   -- Southeast
            table.insert(roadDirections, {dx = 1, dy = -1})  -- Northeast
            table.insert(roadDirections, {dx = -1, dy = 1})  -- Southwest
            table.insert(roadDirections, {dx = -1, dy = -1}) -- Northwest
        end
        
        -- Create main roads
        for _, dir in ipairs(roadDirections) do
            local roadLength = math.random(world.CHUNK_SIZE / 3, world.CHUNK_SIZE / 2)
            local curX = squareX
            local curY = squareY
            
            -- Add jitter to main road direction
            local jitterX = math.random(-jitterAmount, jitterAmount) / 10
            local jitterY = math.random(-jitterAmount, jitterAmount) / 10
            
            -- Create main road
            for i = 1, roadLength do
                curX = curX + dir.dx + jitterX
                curY = curY + dir.dy + jitterY
                
                -- Place road tiles in width
                for w = -roadWidth, roadWidth do
                    for h = -roadWidth, roadWidth do
                        local tileX = math.floor(curX + w)
                        local tileY = math.floor(curY + h)
                        
                        if tileX >= 0 and tileX < world.CHUNK_SIZE and 
                           tileY >= 0 and tileY < world.CHUNK_SIZE and
                           townLayout[tileX][tileY] == 0 then -- Don't overwrite town square
                            townLayout[tileX][tileY] = 2 -- 2 = main road
                        end
                    end
                end
                
                -- Occasionally create side paths/roads
                if math.random() < 0.15 and i > 5 then
                    -- Side road direction perpendicular to main road
                    local sideDir = {dx = -dir.dy, dy = dir.dx}
                    if math.random() < 0.5 then
                        sideDir.dx = -sideDir.dx
                        sideDir.dy = -sideDir.dy
                    end
                    
                    local sideLength = math.random(5, 15)
                    local sideX = curX
                    local sideY = curY
                    
                    -- Create side path
                    for j = 1, sideLength do
                        sideX = sideX + sideDir.dx
                        sideY = sideY + sideDir.dy
                        
                        -- Place path tiles
                        for w = -pathWidth, pathWidth do
                            for h = -pathWidth, pathWidth do
                                local tileX = math.floor(sideX + w)
                                local tileY = math.floor(sideY + h)
                                
                                if tileX >= 0 and tileX < world.CHUNK_SIZE and 
                                   tileY >= 0 and tileY < world.CHUNK_SIZE and
                                   townLayout[tileX][tileY] == 0 then
                                    townLayout[tileX][tileY] = 3 -- 3 = side path
                                end
                            end
                        end
                    end
                end
            end
        end
        
        -- Define building plots
        local buildingMargin = 1 -- Space between buildings
        local buildingPlots = {}
        
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                -- Skip if this is already a road, path, or town square
                if townLayout[x][y] > 0 then
                    goto continue
                end
                
                -- Check if this could be a building corner
                local isBuildingCorner = true
                
                -- Check surrounding tiles - we want some space around buildings
                for nx = -buildingMargin, buildingMargin do
                    for ny = -buildingMargin, buildingMargin do
                        local tileX = x + nx
                        local tileY = y + ny
                        
                        -- If out of bounds or already part of a road or building, not a valid corner
                        if tileX < 0 or tileX >= world.CHUNK_SIZE or
                           tileY < 0 or tileY >= world.CHUNK_SIZE or
                           townLayout[tileX][tileY] == 4 then -- 4 = building plot
                            isBuildingCorner = false
                            break
                        end
                    end
                    if not isBuildingCorner then break end
                end
                
                if isBuildingCorner then
                    -- Determine if we should place a building here
                    -- Buildings should be more likely near roads
                    local nearRoad = false
                    local searchRadius = 3
                    
                    for nx = -searchRadius, searchRadius do
                        for ny = -searchRadius, searchRadius do
                            local tileX = x + nx
                            local tileY = y + ny
                            
                            if tileX >= 0 and tileX < world.CHUNK_SIZE and
                               tileY >= 0 and tileY < world.CHUNK_SIZE and
                               (townLayout[tileX][tileY] == 2 or townLayout[tileX][tileY] == 3) then
                                nearRoad = true
                                break
                            end
                        end
                        if nearRoad then break end
                    end
                    
                    local buildingChance = nearRoad and 0.7 or 0.2
                    
                    -- Adjust based on population density
                    buildingChance = buildingChance * populationDensity
                    
                    if math.random() < buildingChance then
                        -- Determine building size based on variant
                        local minSize = 2
                        local maxSize = 4
                        
                        if variant == "noble" then
                            -- Larger buildings in noble quarters
                            minSize = 3
                            maxSize = 6
                        elseif variant == "farming" then
                            -- Smaller buildings in farming villages
                            minSize = 2
                            maxSize = 3
                        end
                        
                        local buildingWidth = math.random(minSize, maxSize)
                        local buildingHeight = math.random(minSize, maxSize)
                        
                        -- Check if building fits
                        local canFit = true
                        for bx = 0, buildingWidth - 1 do
                            for by = 0, buildingHeight - 1 do
                                local tileX = x + bx
                                local tileY = y + by
                                
                                if tileX >= world.CHUNK_SIZE or tileY >= world.CHUNK_SIZE or
                                   townLayout[tileX][tileY] > 0 then
                                    canFit = false
                                    break
                                end
                            end
                            if not canFit then break end
                        end
                        
                        if canFit then
                            -- Register building plot
                            local buildingType = "house_small" -- Default
                            
                            -- Determine building type based on variant and location
                            if nearRoad and math.random() < 0.3 then
                                -- Commercial buildings more likely near roads
                                local commercialBuildings = {"market_stall", "blacksmith", "tavern"}
                                buildingType = commercialBuildings[math.random(1, #commercialBuildings)]
                            elseif variant == "farming" and math.random() < 0.4 then
                                buildingType = "farm"
                            elseif variant == "fishing" and math.random() < 0.4 then
                                buildingType = "fishery"
                            elseif variant == "mining" and math.random() < 0.4 then
                                buildingType = "blacksmith"
                            elseif variant == "trading" and math.random() < 0.5 then
                                buildingType = "trading_post"
                            elseif variant == "noble" and math.random() < 0.4 then
                                buildingType = "manor_house"
                            elseif buildingWidth >= 4 and buildingHeight >= 4 then
                                buildingType = "house_medium"
                            end
                            
                            -- Mark building plot in layout
                            for bx = 0, buildingWidth - 1 do
                                for by = 0, buildingHeight - 1 do
                                    local tileX = x + bx
                                    local tileY = y + by
                                    
                                    townLayout[tileX][tileY] = 4 -- 4 = building plot
                                end
                            end
                            
                            -- Add to building plots list
                            table.insert(buildingPlots, {
                                x = x,
                                y = y,
                                width = buildingWidth,
                                height = buildingHeight,
                                type = buildingType
                            })
                        end
                    end
                end
                
                ::continue::
            end
        end
        
        -- Generate tiles
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                -- Calculate world coordinates
                local worldX = chunk.x * world.CHUNK_SIZE + x
                local worldY = chunk.y * world.CHUNK_SIZE + y
                
                -- Generate different noise layers for variation
                local baseNoise = noise2D(worldX, worldY, 0.1)
                local detailNoise = noise2D(worldX, worldY, 0.3)
                
                -- Determine tile type based on town layout
                local tileType
                
                if townLayout[x][y] == 1 then
                    -- Town square
                    tileType = "town_square"
                elseif townLayout[x][y] == 2 then
                    -- Main road
                    tileType = "cobblestone"
                elseif townLayout[x][y] == 3 then
                    -- Side path
                    if variant == "noble" then
                        tileType = "fine_cobblestone"
                    else
                        tileType = math.random() < 0.3 and "cobblestone" or "dirt_path"
                    end
                elseif townLayout[x][y] == 4 then
                    -- Building plot - will be handled separately
                    tileType = "building_floor"
                else
                    -- Other areas - use probability distribution
                    local tileRoll = math.random()
                    local cumulativeChance = 0
                    
                    for tType, chance in pairs(tileDistribution) do
                        cumulativeChance = cumulativeChance + chance
                        if tileRoll <= cumulativeChance then
                            tileType = tType
                            break
                        end
                    end
                    
                    -- Default if nothing was selected
                    if not tileType then
                        tileType = "town_grass"
                    end
                    
                    -- Variant-specific modifications
                    if variant == "farming" then
                        -- More farmland in farming villages
                        if baseNoise > 0.7 and math.random() < 0.7 then
                            tileType = "farmland"
                        end
                    elseif variant == "fishing" then
                        -- More beach areas near water in fishing villages
                        if baseNoise > 0.75 and detailNoise > 0.6 then
                            tileType = "beach_sand"
                        end
                    end
                end
                
                -- Create the tile
                local tile = {
                    type = tileType,
                    x = x,
                    y = y,
                    variant = math.random(1, 3), -- Random visual variant
                    townElement = townLayout[x][y], -- Store town layout data
                    wealth = wealth + (baseNoise * 0.2) -- Variation in wealth
                }
                
                -- Add tile to chunk
                local tileIndex = x + y * world.CHUNK_SIZE
                chunk.tiles[tileIndex] = tile
            end
        end
        
        -- Process building plots
        for _, plot in ipairs(buildingPlots) do
            local worldX = chunk.x * world.CHUNK_SIZE + plot.x + math.floor(plot.width / 2)
            local worldY = chunk.y * world.CHUNK_SIZE + plot.y + math.floor(plot.height / 2)
            
            -- Place structure
            world.chunkSystem:placeStructure(chunk, plot.type, worldX, worldY, {
                width = plot.width,
                height = plot.height
            })
        end
        
        -- Place town hall near town square if it doesn't exist yet
        local hasTownHall = false
        for _, plot in ipairs(buildingPlots) do
            if plot.type == "town_hall" then
                hasTownHall = true
                break
            end
        end
        
        if not hasTownHall then
            -- Find a spot near town square
            local townHallX = squareX + math.random(-5, 5)
            local townHallY = squareY + math.random(-5, 5)
            
            -- Make sure it's not too close to the square center
            if math.abs(townHallX - squareX) < 3 and math.abs(townHallY - squareY) < 3 then
                if math.random() < 0.5 then
                    townHallX = squareX + (math.random() < 0.5 and 4 or -4)
                else
                    townHallY = squareY + (math.random() < 0.5 and 4 or -4)
                end
            end
            
            local worldX = chunk.x * world.CHUNK_SIZE + townHallX
            local worldY = chunk.y * world.CHUNK_SIZE + townHallY
            
            world.chunkSystem:placeStructure(chunk, "town_hall", worldX, worldY, {
                width = 5,
                height = 5,
                important = true
            })
        end
        
        -- Place features
        TownBiome.generateFeatures(chunk, world, variant, squareX, squareY)
        
        -- Add entities
        TownBiome.populateEntities(chunk, world, variant)
    end,
    
    -- Helper function to generate features
    generateFeatures = function(chunk, world, variant, squareX, squareY)
        -- Iterate through each potential feature
        for _, feature in ipairs(TownBiome.features) do
            -- Check if feature should spawn based on chance
            if math.random() < feature.chance then
                -- For unique features, only place one per chunk
                if feature.unique and chunk.uniqueFeatures and chunk.uniqueFeatures[feature.name] then
                    goto continue
                end
                
                -- Determine position for feature
                local featureX, featureY
                
                if feature.name == "town_square" then
                    -- Town square is already placed at squareX, squareY
                    featureX = squareX
                    featureY = squareY
                else
                    -- Find a suitable position
                    local attempts = 0
                    local maxAttempts = 10
                    local validPosition = false
                    
                    while attempts < maxAttempts and not validPosition do
                        if feature.name == "fountain" or feature.name == "statue" then
                            -- These look good in the town square
                            featureX = squareX + math.random(-3, 3)
                            featureY = squareY + math.random(-3, 3)
                        else
                            -- Other features can be elsewhere
                            featureX = math.random(5, world.CHUNK_SIZE - 6)
                            featureY = math.random(5, world.CHUNK_SIZE - 6)
                        end
                        
                        -- Check if there's room for this feature
                        if feature.size then
                            local radius = math.max(feature.size.min, feature.size.max) / 2
                            validPosition = true
                            
                            -- Make sure all tiles in this area allow for the feature
                            for dx = -radius, radius do
                                for dy = -radius, radius do
                                    local tileX = math.floor(featureX + dx)
                                    local tileY = math.floor(featureY + dy)
                                    
                                    if tileX < 0 or tileX >= world.CHUNK_SIZE or
                                       tileY < 0 or tileY >= world.CHUNK_SIZE then
                                        validPosition = false
                                        break
                                    end
                                    
                                    local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                    local tile = chunk.tiles[tileIndex]
                                    
                                    -- Don't place features on buildings
                                    if tile.townElement == 4 then
                                        validPosition = false
                                        break
                                    end
                                end
                                if not validPosition then break end
                            end
                        else
                            -- Smaller features just need one open tile
                            local tileIndex = math.floor(featureX) + math.floor(featureY) * world.CHUNK_SIZE
                            local tile = chunk.tiles[tileIndex]
                            
                            validPosition = (tile.townElement ~= 4) -- Not a building
                        end
                        
                        attempts = attempts + 1
                    end
                    
                    if not validPosition then
                        goto continue -- Couldn't find a spot for this feature
                    end
                end
                
                -- Place feature based on type
                if feature.name == "fountain" then
                    -- Create a fountain
                    local fountainRadius = math.random(feature.size.min, feature.size.max)
                    
                    for dx = -fountainRadius, fountainRadius do
                        for dy = -fountainRadius, fountainRadius do
                            local dist = math.sqrt(dx*dx + dy*dy)
                            if dist <= fountainRadius then
                                local tileX = math.floor(featureX + dx)
                                local tileY = math.floor(featureY + dy)
                                
                                if tileX >= 0 and tileX < world.CHUNK_SIZE and
                                   tileY >= 0 and tileY < world.CHUNK_SIZE then
                                    local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                    
                                    if dist < fountainRadius * 0.5 then
                                        -- Center is water
                                        chunk.tiles[tileIndex].type = "fountain_water"
                                    else
                                        -- Edges are stone
                                        chunk.tiles[tileIndex].type = "fountain_base"
                                    end
                                end
                            end
                        end
                    end
                    
                    -- Add fountain centerpiece
                    local worldX = chunk.x * world.CHUNK_SIZE + featureX
                    local worldY = chunk.y * world.CHUNK_SIZE + featureY
                    
                    world.entitySystem:addEntity("fountain_statue", worldX, worldY, {
                        spraying = true,
                        size = fountainRadius * 0.6
                    })
                    
                elseif feature.name == "statue" then
                    -- Create a statue
                    local statueSubject = feature.subjects[math.random(#feature.subjects)]
                    local worldX = chunk.x * world.CHUNK_SIZE + featureX
                    local worldY = chunk.y * world.CHUNK_SIZE + featureY
                    
                    -- Set tile to statue base
                    local tileIndex = math.floor(featureX) + math.floor(featureY) * world.CHUNK_SIZE
                    chunk.tiles[tileIndex].type = "statue_base"
                    
                    -- Add statue entity
                    world.entitySystem:addEntity("statue", worldX, worldY, {
                        subject = statueSubject,
                        size = math.random(15, 25) / 10,
                        material = math.random() < 0.8 and "stone" or "bronze"
                    })
                    
                elseif feature.name == "marketplace" then
                    -- Create a marketplace area
                    local marketSize = math.random(feature.size.min, feature.size.max)
                    local radius = marketSize / 2
                    
                    for dx = -radius, radius do
                        for dy = -radius, radius do
                            local dist = math.sqrt(dx*dx + dy*dy)
                            if dist <= radius then
                                local tileX = math.floor(featureX + dx)
                                local tileY = math.floor(featureY + dy)
                                
                                if tileX >= 0 and tileX < world.CHUNK_SIZE and
                                   tileY >= 0 and tileY < world.CHUNK_SIZE then
                                    local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                    
                                    -- Set market tile
                                    chunk.tiles[tileIndex].type = "marketplace"
                                end
                            end
                        end
                    end
                    
                    -- Add market stalls
                    local stallCount = math.random(4, 10)
                    for i = 1, stallCount do
                        local angle = (i / stallCount) * 2 * math.pi
                        local distance = radius * 0.7 * math.random(5, 10) / 10
                        
                        local stallX = chunk.x * world.CHUNK_SIZE + featureX + math.cos(angle) * distance
                        local stallY = chunk.y * world.CHUNK_SIZE + featureY + math.sin(angle) * distance
                        
                        world.entitySystem:addEntity("market_stall", stallX, stallY, {
                            goods = ({"food", "clothing", "tools", "crafts", "spices"})[math.random(1, 5)],
                            quality = math.random(1, 5)
                        })
                    end
                    
                elseif feature.name == "garden" then
                    -- Create a garden area
                    local gardenSize = math.random(feature.size.min, feature.size.max)
                    local radius = gardenSize / 2
                    
                    for dx = -radius, radius do
                        for dy = -radius, radius do
                            local dist = math.sqrt(dx*dx + dy*dy)
                            if dist <= radius then
                                local tileX = math.floor(featureX + dx)
                                local tileY = math.floor(featureY + dy)
                                
                                if tileX >= 0 and tileX < world.CHUNK_SIZE and
                                   tileY >= 0 and tileY < world.CHUNK_SIZE then
                                    local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                    
                                    -- Set garden tile
                                    chunk.tiles[tileIndex].type = "garden"
                                    
                                    -- Add some plants
                                    if math.random() < 0.2 then
                                        local plantX = chunk.x * world.CHUNK_SIZE + tileX
                                        local plantY = chunk.y * world.CHUNK_SIZE + tileY
                                        
                                        local plantTypes = {"flower_bush", "ornamental_tree", "garden_statue", "hedge"}
                                        local plantType = plantTypes[math.random(#plantTypes)]
                                        
                                        world.entitySystem:addEntity(plantType, plantX, plantY, {
                                            size = math.random(10, 20) / 10
                                        })
                                    end
                                end
                            end
                        end
                    end
                    
                elseif feature.name == "well" then
                    -- Create a well
                    local worldX = chunk.x * world.CHUNK_SIZE + featureX
                    local worldY = chunk.y * world.CHUNK_SIZE + featureY
                    
                    -- Set tile to well base
                    local tileIndex = math.floor(featureX) + math.floor(featureY) * world.CHUNK_SIZE
                    chunk.tiles[tileIndex].type = "well_base"
                    
                    -- Add well entity
                    world.entitySystem:addEntity("well", worldX, worldY, {
                        hasBucket = true,
                        hasRoof = math.random() < 0.5
                    })
                    
                elseif feature.name == "graveyard" then
                    -- Create a graveyard
                    local graveyardSize = math.random(feature.size.min, feature.size.max)
                    local radius = graveyardSize / 2
                    
                    for dx = -radius, radius do
                        for dy = -radius, radius do
                            local dist = math.sqrt(dx*dx + dy*dy)
                            if dist <= radius then
                                local tileX = math.floor(featureX + dx)
                                local tileY = math.floor(featureY + dy)
                                
                                if tileX >= 0 and tileX < world.CHUNK_SIZE and
                                   tileY >= 0 and tileY < world.CHUNK_SIZE then
                                    local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                    
                                    -- Set graveyard tile
                                    chunk.tiles[tileIndex].type = "graveyard_soil"
                                    
                                    -- Add some graves
                                    if math.random() < 0.3 then
                                        local graveX = chunk.x * world.CHUNK_SIZE + tileX
                                        local graveY = chunk.y * world.CHUNK_SIZE + tileY
                                        
                                        world.entitySystem:addEntity("gravestone", graveX, graveY, {
                                            age = math.random(10, 100),
                                            style = math.random(1, 3)
                                        })
                                    end
                                end
                            end
                        end
                    end
                    
                    -- Add a small mausoleum or shrine
                    if math.random() < 0.7 then
                        local shrineX = chunk.x * world.CHUNK_SIZE + featureX
                        local shrineY = chunk.y * world.CHUNK_SIZE + featureY
                        
                        world.entitySystem:addEntity("small_mausoleum", shrineX, shrineY, {
                            style = math.random(1, 3)
                        })
                    end
                end
                
                -- Mark unique features as placed
                if feature.unique then
                    if not chunk.uniqueFeatures then
                        chunk.uniqueFeatures = {}
                    end
                    chunk.uniqueFeatures[feature.name] = true
                end
            end
            
            ::continue::
        end
    end,
    
    -- Helper function to add entities
    populateEntities = function(chunk, world, variant)
        -- Get variant-specific entity adjustments
        local variantData = variant and TownBiome.variants[variant] or nil
        local populationDensity = variantData and variantData.populationDensity or 0.7
        
        -- Towns have high entity density
        local commonCount = math.random(15, 30) * populationDensity
        local uncommonCount = math.random(5, 10) * populationDensity
        local rareCount = math.random(1, 3)
        
        -- Helper function to spawn an entity with variant adjustments
        local function spawnEntity(entityType, count)
            -- Apply variant adjustment if available
            local multiplier = 1.0
            if variantData and variantData.entityAdjustments and variantData.entityAdjustments[entityType] then
                multiplier = variantData.entityAdjustments[entityType]
            end
            
            -- Adjust count based on variant
            local adjustedCount = math.floor(count * multiplier + 0.5)
            
            -- Spawn entities
            for i = 1, adjustedCount do
                -- Pick a random position in the chunk
                local attempts = 0
                local maxAttempts = 5
                local placed = false
                
                while attempts < maxAttempts and not placed do
                    local x = math.random(0, world.CHUNK_SIZE - 1)
                    local y = math.random(0, world.CHUNK_SIZE - 1)
                    local tileIndex = x + y * world.CHUNK_SIZE
                    local tile = chunk.tiles[tileIndex]
                    
                    -- Check if tile is suitable for this entity
                    local suitable = false
                    
                    if entityType == "villager" or entityType == "merchant" or entityType == "bard" then
                        -- Civilians prefer roads, paths, and town square
                        suitable = (tile.townElement == 1 or tile.townElement == 2 or tile.townElement == 3)
                    elseif entityType == "guard" then
                        -- Guards patrol roads and important areas
                        suitable = (tile.townElement == 1 or tile.townElement == 2)
                    elseif entityType == "noble" then
                        -- Nobles in fancy areas or gardens
                        suitable = (tile.type == "garden" or tile.type == "town_square" or 
                                  tile.type == "fine_cobblestone")
                    elseif entityType == "cat" or entityType == "dog" then
                        -- Pets anywhere except water
                        suitable = (tile.type ~= "fountain_water")
                    elseif entityType == "chicken" then
                        -- Farm animals near farmland
                        suitable = (tile.type == "farmland" or tile.type == "dirt_path")
                    else
                        -- Default - any walkable area
                        suitable = (tile.townElement ~= 4) -- Not in buildings
                    end
                    
                    if suitable then
                        local worldX = chunk.x * world.CHUNK_SIZE + x
                        local worldY = chunk.y * world.CHUNK_SIZE + y
                        
                        -- Spawn the entity
                        world.entitySystem:addEntity(entityType, worldX, worldY, {
                            townResident = true
                        })
                        placed = true
                    end
                    
                    attempts = attempts + 1
                end
            end
        end
        
        -- Spawn common entities
        for _, entityType in ipairs(TownBiome.commonEntities) do
            local count = math.random(3, 6)
            spawnEntity(entityType, count)
        end
        
        -- Spawn uncommon entities
        for _, entityType in ipairs(TownBiome.uncommonEntities) do
            local count = math.random(1, 3)
            spawnEntity(entityType, count)
        end
        
        -- Spawn rare entities
        for _, entityType in ipairs(TownBiome.rareEntities) do
            if math.random() < 0.3 then -- 30% chance for each rare entity
                spawnEntity(entityType, 1)
            end
        end
        
        -- Add variant-specific entities
        if variant == "farming" then
            spawnEntity("farmer", math.random(4, 8))
            spawnEntity("farm_animal", math.random(5, 10))
        elseif variant == "fishing" then
            spawnEntity("fisherman", math.random(4, 8))
            spawnEntity("seagull", math.random(3, 7))
        elseif variant == "mining" then
            spawnEntity("miner", math.random(4, 8))
            spawnEntity("blacksmith", math.random(2, 4))
        elseif variant == "trading" then
            spawnEntity("merchant", math.random(5, 10))
            spawnEntity("traveler", math.random(3, 7))
        elseif variant == "noble" then
            spawnEntity("noble", math.random(3, 6))
            spawnEntity("guard", math.random(4, 8))
            spawnEntity("servant", math.random(4, 7))
        end
        
        -- Add ambient movement - people going about their business
        for i = 1, math.random(5, 15) do
            -- Pick random road tiles for movement endpoints
            local startX, startY, endX, endY
            local attempts = 0
            local maxAttempts = 10
            
            while attempts < maxAttempts do
                startX = math.random(0, world.CHUNK_SIZE - 1)
                startY = math.random(0, world.CHUNK_SIZE - 1)
                
                local startTileIndex = startX + startY * world.CHUNK_SIZE
                if chunk.tiles[startTileIndex].townElement == 2 or chunk.tiles[startTileIndex].townElement == 3 then
                    -- Found a valid start point on a road
                    break
                end
                
                attempts = attempts + 1
            end
            
            attempts = 0
            while attempts < maxAttempts do
                endX = math.random(0, world.CHUNK_SIZE - 1)
                endY = math.random(0, world.CHUNK_SIZE - 1)
                
                local endTileIndex = endX + endY * world.CHUNK_SIZE
                if chunk.tiles[endTileIndex].townElement == 2 or chunk.tiles[endTileIndex].townElement == 3 then
                    -- Found a valid end point on a road
                    break
                end
                
                attempts = attempts + 1
            end
            
            if attempts < maxAttempts then
                local startWorldX = chunk.x * world.CHUNK_SIZE + startX
                local startWorldY = chunk.y * world.CHUNK_SIZE + startY
                local endWorldX = chunk.x * world.CHUNK_SIZE + endX
                local endWorldY = chunk.y * world.CHUNK_SIZE + endY
                
                -- Create moving entity
                local movingEntityTypes = {"villager", "merchant", "traveler", "guard"}
                local entityType = movingEntityTypes[math.random(#movingEntityTypes)]
                
                world.entitySystem:addEntity(entityType, startWorldX, startWorldY, {
                    townResident = true,
                    moving = true,
                    targetX = endWorldX,
                    targetY = endWorldY,
                    speed = math.random(5, 15) / 10
                })
            end
        end
    end,
    
    -- Apply post-processing effects
    postProcess = function(chunk, world, variant)
        local variantData = variant and TownBiome.variants[variant] or nil
        
        -- Apply variant-specific special effects
        if variantData and variantData.specialEffects then
            chunk.environmentalEffects = chunk.environmentalEffects or {}
            
            for _, effect in ipairs(variantData.specialEffects) do
                table.insert(chunk.environmentalEffects, effect)
            end
        end
        
        -- Process town tiles
        for _, tile in pairs(chunk.tiles) do
            -- Set town-specific properties
            if tile.townElement == 1 then -- Town square
                tile.trafficked = true
                tile.maintenanceLevel = 0.9 -- Well maintained
            elseif tile.townElement == 2 then -- Main road
                tile.trafficked = true
                tile.maintenanceLevel = 0.8
            elseif tile.townElement == 3 then -- Side path
                tile.trafficked = true
                tile.maintenanceLevel = 0.6
            elseif tile.townElement == 4 then -- Building
                tile.occupied = true
            end
            
            -- Apply variant-specific tile properties
            if variant == "farming" and tile.type == "farmland" then
                tile.fertility = 0.8 + math.random() * 0.2
                tile.cropType = {"wheat", "corn", "potatoes", "vegetables"}[math.random(1, 4)]
            elseif variant == "fishing" and tile.type == "dock_planks" then
                tile.weathered = true
                tile.slippery = math.random() < 0.3
            elseif variant == "noble" then
                if tile.type == "fine_cobblestone" or tile.type == "garden" then
                    tile.maintenanceLevel = 0.9 -- Very well maintained
                    tile.decorated = math.random() < 0.3
                end
            end
        end
        
        -- Set town ambient sounds based on variant
        if variant == "farming" then
            chunk.ambientSounds = {"animal_sounds", "farm_work", "mild_chatter"}
        elseif variant == "fishing" then
            chunk.ambientSounds = {"seagulls", "waves", "harbor_sounds"}
        elseif variant == "mining" then
            chunk.ambientSounds = {"hammering", "forge_sounds", "mining_equipment"}
        elseif variant == "trading" then
            chunk.ambientSounds = {"market_bustle", "trading_calls", "crowd_noise"}
        elseif variant == "noble" then
            chunk.ambientSounds = {"soft_music", "fountain_splashing", "distant_conversation"}
        else
            chunk.ambientSounds = {"town_chatter", "occasional_animals", "daily_life"}
        end
        
        -- Set town lighting for night time
        chunk.lightSources = {}
        for x = 0, world.CHUNK_SIZE - 1, 5 do -- Place lights every 5 tiles
            for y = 0, world.CHUNK_SIZE - 1, 5 do
                local tileIndex = x + y * world.CHUNK_SIZE
                local tile = chunk.tiles[tileIndex]
                
                if tile.townElement == 1 or tile.townElement == 2 then
                    -- Add light source on main roads and town square
                    table.insert(chunk.lightSources, {
                        x = x,
                        y = y,
                        intensity = 0.8,
                        color = {1.0, 0.9, 0.7}, -- Warm light
                        height = 3 -- Lamp post height
                    })
                end
            end
        end
    end,
    
    -- Initialize the biome module
    init = function(worldCore)
        print("Town biome module initialized")
        
        -- Store reference to WorldCore for later use
        TownBiome.worldCore = worldCore
        
        -- Don't try to register generators at init time
        -- Instead, we'll register them when a world is created
        
        -- Store the biome in the biomes registry
        -- This allows the module to be used later when worlds are created
        print("Town biome registered successfully")
    end,
    
    -- Add a new function to register generators with a specific world instance
    registerWithWorld = function(world)
        print("Registering town biome generators with world")
        
        if not world or not world.chunkSystem then
            print("Warning: Cannot register town biome - world or chunkSystem is nil")
            return false
        end
        
        -- Register generation algorithms with the world
        world.chunkSystem:registerGenerator("town", TownBiome.generate)
        
        -- Register biome variants
        for variantId, variant in pairs(TownBiome.variants) do
            world.chunkSystem:registerGenerator("town_" .. variantId, function(chunk, world)
                TownBiome.generate(chunk, world, variantId)
                TownBiome.postProcess(chunk, world, variantId)
            end)
        end
        
        return true
    end
}

return TownBiome
                