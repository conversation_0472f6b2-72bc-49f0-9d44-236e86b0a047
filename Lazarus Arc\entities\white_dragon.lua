-- entities/white_dragon.lua
-- White dragon with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local WhiteDragon = {
    id = "white_dragon",
    name = "White Dragon",
    type = "white_dragon",
    shape = {
        {0, -20}, {15, -15}, {25, 0}, {15, 15},
        {0, 20}, {-15, 15}, {-25, 0}, {-15, -15}
    },
    size = 25,

    -- Entity categories
    categories = {"dragon", "flying", "ice", "arctic", "primal"},
    threatCategories = {"player", "knight", "treasure_hunter", "dragon_slayer"},
    
    -- Base stats (ice-breathing arctic dragon)
    maxHealth = 780,
    health = 780,
    maxStamina = 320,
    stamina = 320,
    maxMana = 350,
    mana = 350,
    speed = 3.2,
    attack = 58,
    defense = 42,
    magicAttack = 40,
    magicDefense = 45,
    iceResistance = 100,
    fireVulnerability = 1.8,
    flight_altitude = 45,
    arctic_mastery = 30,
    primal_fury = 20,
    
    -- Behaviors
    behaviors = {"arctic_hunt", "ice_breath", "blizzard_control", "primal_rage"},
    behaviorConfigs = {
        arctic_hunt = {
            coldTracking = 2.5,
            snowCamouflage = 1.8,
            glacialMovement = true,
            endlessEndurance = true
        },
        ice_breath = {
            breathRange = 35,
            freezeDamage = 2.8,
            frostbiteChance = 0.7,
            iceWall = 15,
            flashFreeze = true
        },
        blizzard_control = {
            blizzardSummoning = true,
            temperatureControl = 3.0,
            snowstormRadius = 50,
            glacialAdvance = 2.0
        },
        primal_rage = {
            rageTrigger = 0.5,
            berserkerMode = 2.5,
            primalRoar = true,
            unstoppableForce = 2.0
        }
    },
    
    -- Enhanced variant system for white dragons
    variantChances = {
        normal = 0.50,          -- 50% young white dragon
        shiny = 0.35,           -- 35% adult white dragon (shiny)
        rare = 0.13,            -- 13% ancient white dragon (rare)
        legendary = 0.02        -- 2% glacier lord (legendary)
    },
    
    variants = {
        normal = {
            name = "Young White Dragon",
            description = "A fierce young dragon from the frozen wastes",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {1.1, 1.1, 1.2, 1.0}  -- Pure white scales
            }
        },
        
        shiny = {
            name = "Adult White Dragon",
            description = "A mature arctic dragon with mastery over ice and cold",
            statModifiers = {
                maxHealth = 1.4,    -- 1092 health
                attack = 1.4,       -- 81.2 attack
                defense = 1.3,      -- 54.6 defense
                primal_fury = 1.8,  -- Enhanced fury
                ice_mastery = 2.0,
                arctic_dominion = 2.5
            },
            appearanceModifiers = {
                scale = 1.3,
                glow = true,
                colorTint = {1.2, 1.2, 1.4, 1.0},  -- Crystalline white with ice aura
                ice_crystals = true,
                frost_aura = true,
                glacial_presence = true
            },
            soundModifiers = {
                pitch = 0.8,
                volume = 1.3,
                crystalline = true
            }
        },
        
        rare = {
            name = "Ancient White Dragon",
            description = "An ancient wyrm that has witnessed the birth of glaciers",
            statModifiers = {
                maxHealth = 2.0,    -- 1560 health
                attack = 1.8,       -- 104.4 attack
                defense = 1.6,      -- 67.2 defense
                primal_fury = 2.5,  -- Primal master
                ice_mastery = 3.5,
                glacial_age = 4.0,
                ancient_cold = 5.0
            },
            appearanceModifiers = {
                scale = 1.6,
                colorTint = {1.1, 1.1, 1.3, 1.0},  -- Ancient ice-blue white
                permanent_blizzard = true,
                glacial_integration = true,
                ice_age_scars = true,
                ancient_frost = true
            },
            soundModifiers = {
                pitch = 0.6,
                volume = 1.5,
                reverb = true,
                glacial_echo = true
            }
        },
        
        legendary = {
            name = "Glacier Lord",
            description = "A legendary dragon that commands the eternal ice itself",
            statModifiers = {
                maxHealth = 2.8,    -- 2184 health
                attack = 2.2,       -- 127.6 attack
                defense = 2.0,      -- 84 defense
                primal_fury = 3.5,  -- Ultimate fury
                ice_lordship = 10.0,
                eternal_winter = 5.0,
                glacial_dominion = 3.0
            },
            appearanceModifiers = {
                scale = 2.0,
                glow = true,
                colorTint = {1.3, 1.3, 1.6, 1.0},  -- Eternal ice with cosmic frost
                glacier_embodiment = true,
                eternal_winter_aura = "legendary",
                ice_age_mastery = true,
                reality_freeze = true
            },
            soundModifiers = {
                pitch = 0.4,
                volume = 1.8,
                reverb = true,
                echo = true,
                glacier_lord_resonance = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "white_dragon_scale", chance = 1.0, quantity = {5, 10}},
        {id = "dragon_blood", chance = 0.9, quantity = {2, 4}},
        {id = "ice_fang", chance = 0.8, quantity = {1, 2}},
        {id = "frost_essence", chance = 1.0, quantity = {3, 6}},
        {id = "primal_heart", chance = 0.7, quantity = {1, 1}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "arctic_dragon_scale", chance = 1.0, quantity = {6, 12}},
            {id = "ice_mastery_core", chance = 0.9, quantity = {1, 1}},
            {id = "glacial_crystal", chance = 0.8, quantity = {1, 1}},
            {id = "arctic_dominion_essence", chance = 0.7, quantity = {1, 2}}
        },
        rare = {
            {id = "ancient_ice_scale", chance = 1.0, quantity = {8, 15}},
            {id = "glacial_age_crystal", chance = 0.9, quantity = {1, 2}},
            {id = "ancient_cold_core", chance = 0.8, quantity = {1, 1}},
            {id = "eternal_frost_essence", chance = 0.7, quantity = {2, 4}},
            {id = "ice_age_fragment", chance = 0.8, quantity = {1, 1}}
        },
        legendary = {
            {id = "glacier_lord_scale", chance = 1.0, quantity = {10, 20}},
            {id = "eternal_winter_crown", chance = 0.95, quantity = {1, 1}},
            {id = "ice_lordship_orb", chance = 0.9, quantity = {1, 1}},
            {id = "glacial_dominion_essence", chance = 0.8, quantity = {1, 2}},
            {id = "ice_mastery_codex", chance = 0.7, quantity = {1, 1}},
            {id = "reality_freeze_scepter", chance = 0.6, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "white_dragon",
        scale = 1.8,
        animations = {
            "arctic_soar", "ice_breath", "primal_roar", "blizzard_summon", "glacial_advance"
        },
        variants = {
            "young_white", "adult_white", "ancient_white", "glacier_lord"
        }
    },
    
    -- Sound effects with arctic characteristics
    sounds = {
        primal_roar = {
            file = "white_dragon_roar",
            synth = {
                instrument = "organ",
                notes = {"A0", "E1", "A1", "E2"},
                durations = {1.6, 1.3, 1.5, 1.8},
                volume = 1.2,
                primal = true
            }
        },
        ice_breath = {
            file = "ice_breath",
            synth = {
                instrument = "synthesizer",
                notes = {"C3", "E3", "G3", "C4"},
                durations = {0.8, 0.6, 0.9, 1.0},
                volume = 0.9,
                freezing = true
            }
        },
        blizzard_call = {
            file = "blizzard_call",
            synth = {
                instrument = "choir",
                notes = {"F2", "A2", "C3", "F3"},
                durations = {1.2, 1.0, 1.2, 1.5},
                volume = 0.8,
                arctic_wind = true
            }
        },
        glacial_crack = {
            file = "glacial_crack",
            synth = {
                instrument = "percussion",
                notes = {"C1", "G1", "C2"},
                durations = {0.5, 0.4, 0.8},
                volume = 1.0,
                ice_breaking = true
            }
        }
    },
    
    -- Special white dragon abilities
    abilities = {
        ice_breath = {
            type = "active",
            description = "Freezing breath that creates ice walls and frostbite",
            effect = "ice_breath_attack",
            cooldown = 12
        },
        primal_fury = {
            type = "active",
            description = "Enters berserker rage with increased damage and speed",
            effect = "berserker_mode",
            duration = 15,
            cooldown = 30
        },
        arctic_mastery = {
            type = "passive",
            description = "Moves freely through ice and snow, immune to cold",
            effect = "arctic_movement"
        },
        blizzard_summoning = {
            type = "active",
            description = "Summons devastating blizzards that obscure vision",
            effect = "weather_control",
            manaCost = 80,
            cooldown = 25
        },
        ice_immunity = {
            type = "passive",
            description = "Complete immunity to ice and cold damage",
            effect = "ice_immunity"
        },
        glacial_advance = {
            type = "active",
            description = "Creates advancing walls of ice that crush enemies",
            effect = "ice_wall_advance",
            cooldown = 20
        }
    }
}

-- Initialize the white dragon entity using enhanced template
function WhiteDragon.init(entity, world)
    -- Copy all fields from WhiteDragon template to entity instance
    for k, v in pairs(WhiteDragon) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return WhiteDragon
