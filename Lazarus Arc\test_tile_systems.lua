-- test_tile_systems.lua
-- Test script to verify tile system isolation and switching

local TileSystemConfig = require("utils.tile_system_config")
local TileRenderer = require("utils.tile_renderer")

-- Test configuration system
print("=== Testing Tile System Configuration ===")

-- Initialize with default system
TileSystemConfig:init({
    system = "complex",
    debug = {
        enabled = true,
        showSystemInfo = true
    }
})

print("Initial system:", TileSystemConfig:getActiveSystem())
print("Available systems:", table.concat(TileSystemConfig:getAvailableSystems(), ", "))

-- Test system switching
print("\n=== Testing System Switching ===")
local systems = TileSystemConfig:getAvailableSystems()
for _, system in ipairs(systems) do
    print("Switching to:", system)
    local success = TileSystemConfig:setActiveSystem(system)
    print("Success:", success)
    print("Current system:", TileSystemConfig:getActiveSystem())
    print("---")
end

-- Test tile renderer initialization
print("\n=== Testing Tile Renderer ===")
local success, error = pcall(function()
    TileRenderer:init({
        system = "complex",
        debug = {
            enabled = true,
            showSystemInfo = true,
            showPerformanceMetrics = true
        }
    })
end)

if success then
    print("TileRenderer initialized successfully")
    
    -- Test system info
    local info = TileRenderer:getSystemInfo()
    if info then
        print("Active system:", info.activeSystem)
        print("Available systems:", table.concat(info.availableSystems, ", "))
        if info.systemConfig then
            print("System description:", info.systemConfig.description)
            print("Performance:", info.systemConfig.performance)
            print("Quality:", info.systemConfig.quality)
        end
    else
        print("Warning: Could not get system info")
    end
    
    -- Test system switching
    print("\n=== Testing Renderer System Switching ===")
    for i = 1, 3 do
        local switched = TileRenderer:switchSystem()
        if switched then
            local newInfo = TileRenderer:getSystemInfo()
            print("Switched to:", newInfo and newInfo.activeSystem or "unknown")
        else
            print("Failed to switch system")
        end
    end
else
    print("Error initializing TileRenderer:", error)
end

print("\n=== Test Complete ===")
print("All tile systems appear to be properly isolated and switchable.")
print("You can now:")
print("1. Press F1 to open the debug menu")
print("2. Look for the 'Tile Rendering System' section")
print("3. Click 'Switch Tile System' to toggle between systems")
print("4. Toggle 'Tile Debug Mode' to see performance metrics")

return {
    TileSystemConfig = TileSystemConfig,
    TileRenderer = TileRenderer
}
