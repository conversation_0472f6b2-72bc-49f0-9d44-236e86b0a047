-- event_log.lua
local EventLogger = {}

-- Forward reference for database manager
local db = nil

-- Set the database manager reference (to be called by engine.lua after initialization)
function EventLogger.setDatabaseManager(database_manager)
    db = database_manager
end

function EventLogger.log(playerUUID, eventType, description)
    if not db or not db.connectionActive then return false end

    local timestamp = os.time()
    
    if db.useSQL then
        local query = string.format(
            "INSERT INTO event_log (uuid, event_type, description, timestamp) VALUES ('%s', '%s', '%s', %d)",
            db.escape(playerUUID), db.escape(eventType), db.escape(description), timestamp
        )
        db.execute(query)
    else
        local filename = string.format("event_logs/%s.json", os.date("%Y%m%d_%H%M%S"))
        local logEntry = {
            uuid = playerUUID,
            type = eventType,
            description = description,
            timestamp = timestamp
        }

        -- append mode
        local json = love.filesystem.read(filename) or "[]"
        local list = db.deserialize(json)
        table.insert(list, logEntry)
        love.filesystem.write(filename, db.serialize(list))
    end

    return true
end

return EventLogger
