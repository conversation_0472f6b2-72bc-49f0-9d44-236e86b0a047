-- entities/blue_dragon.lua
-- Blue dragon with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local BlueDragon = {
    id = "blue_dragon",
    name = "Blue Dragon",
    type = "blue_dragon",
    shape = {
        {0, -20}, {15, -15}, {25, 0}, {15, 15},
        {0, 20}, {-15, 15}, {-25, 0}, {-15, -15}
    },
    size = 25,

    -- Entity categories
    categories = {"dragon", "flying", "lightning", "ancient", "magical"},
    threatCategories = {"player", "knight", "treasure_hunter", "dragon_slayer"},
    
    -- Base stats (storm dragon)
    maxHealth = 750,
    health = 750,
    maxStamina = 350,
    stamina = 350,
    maxMana = 500,
    mana = 500,
    speed = 3.5,  -- Faster than red dragons
    attack = 55,
    defense = 35,
    magicAttack = 65,  -- Higher magic attack
    magicDefense = 50,
    lightningResistance = 100,
    waterVulnerability = 1.3,
    flight_altitude = 60,
    storm_control = 25,
    
    -- Behaviors
    behaviors = {"storm_flight", "lightning_breath", "weather_control", "electrical_mastery"},
    behaviorConfigs = {
        storm_flight = {
            stormRiding = true,
            lightningSpeed = 4.5,
            cloudWalking = true,
            weatherSense = 2.0
        },
        lightning_breath = {
            breathRange = 40,
            chainLightning = true,
            stunChance = 0.6,
            electricalDamage = 2.5
        },
        weather_control = {
            stormSummoning = true,
            rainControl = true,
            windMastery = 2.0,
            atmosphericDominion = 3.0
        },
        electrical_mastery = {
            electricalImmunity = true,
            powerAbsorption = 1.5,
            magneticField = 2.0
        }
    },
    
    -- Enhanced variant system for blue dragons
    variantChances = {
        normal = 0.50,          -- 50% young blue dragon
        shiny = 0.35,           -- 35% adult blue dragon (shiny)
        rare = 0.13,            -- 13% ancient blue dragon (rare)
        legendary = 0.02        -- 2% storm lord dragon (legendary)
    },
    
    variants = {
        normal = {
            name = "Young Blue Dragon",
            description = "A swift young dragon crackling with electrical energy",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.4, 0.6, 1.2, 1.0}  -- Electric blue scales
            }
        },
        
        shiny = {
            name = "Adult Blue Dragon",
            description = "A mature storm dragon with mastery over lightning",
            statModifiers = {
                maxHealth = 1.4,    -- 1050 health
                speed = 1.3,        -- 4.55 speed
                magicAttack = 1.5,  -- 97.5 magic attack
                defense = 1.3,      -- 45.5 defense
                storm_mastery = 2.0,
                lightning_power = 1.8
            },
            appearanceModifiers = {
                scale = 1.3,
                glow = true,
                colorTint = {0.3, 0.5, 1.4, 1.0},  -- Deep blue with lightning aura
                lightning_aura = true,
                storm_clouds = true,
                electrical_discharge = true
            },
            soundModifiers = {
                pitch = 0.9,
                volume = 1.3,
                electrical = true
            }
        },
        
        rare = {
            name = "Ancient Blue Dragon",
            description = "An ancient wyrm that commands the very storms",
            statModifiers = {
                maxHealth = 2.0,    -- 1500 health
                speed = 1.5,        -- 5.25 speed
                magicAttack = 2.0,  -- 130 magic attack
                defense = 1.6,      -- 56 defense
                storm_mastery = 4.0,
                weather_dominion = 3.0,
                ancient_wisdom = 5.0
            },
            appearanceModifiers = {
                scale = 1.6,
                colorTint = {0.2, 0.4, 1.3, 1.0},  -- Deep storm blue
                permanent_storm = true,
                lightning_crown = true,
                weather_distortion = true,
                ancient_power = true
            },
            soundModifiers = {
                pitch = 0.7,
                volume = 1.5,
                reverb = true,
                thunder_echo = true
            }
        },
        
        legendary = {
            name = "Storm Lord Dragon",
            description = "A legendary dragon that embodies the fury of all storms",
            statModifiers = {
                maxHealth = 2.8,    -- 2100 health
                speed = 1.8,        -- 6.3 speed
                magicAttack = 2.8,  -- 182 magic attack
                defense = 2.0,      -- 70 defense
                storm_lordship = 10.0,
                atmospheric_control = 5.0,
                weather_creation = 3.0
            },
            appearanceModifiers = {
                scale = 2.0,
                glow = true,
                colorTint = {0.1, 0.3, 1.5, 1.0},  -- Cosmic storm blue
                storm_embodiment = true,
                reality_storm = true,
                atmospheric_aura = "legendary",
                lightning_mastery = true
            },
            soundModifiers = {
                pitch = 0.5,
                volume = 1.8,
                reverb = true,
                echo = true,
                storm_lord_thunder = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "blue_dragon_scale", chance = 1.0, quantity = {5, 10}},
        {id = "dragon_blood", chance = 0.9, quantity = {2, 4}},
        {id = "lightning_fang", chance = 0.8, quantity = {1, 2}},
        {id = "storm_essence", chance = 1.0, quantity = {3, 6}},
        {id = "electrical_heart", chance = 0.7, quantity = {1, 1}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "storm_dragon_scale", chance = 1.0, quantity = {6, 12}},
            {id = "lightning_mastery_orb", chance = 0.9, quantity = {1, 1}},
            {id = "storm_control_crystal", chance = 0.8, quantity = {1, 1}},
            {id = "electrical_wing_membrane", chance = 0.7, quantity = {1, 2}}
        },
        rare = {
            {id = "ancient_storm_scale", chance = 1.0, quantity = {8, 15}},
            {id = "weather_dominion_crystal", chance = 0.9, quantity = {1, 2}},
            {id = "storm_magic_core", chance = 0.8, quantity = {1, 1}},
            {id = "lightning_crown_fragment", chance = 0.7, quantity = {1, 1}},
            {id = "ancient_storm_essence", chance = 0.8, quantity = {2, 4}}
        },
        legendary = {
            {id = "storm_lord_scale", chance = 1.0, quantity = {10, 20}},
            {id = "atmospheric_control_crown", chance = 0.95, quantity = {1, 1}},
            {id = "storm_lordship_orb", chance = 0.9, quantity = {1, 1}},
            {id = "weather_creation_essence", chance = 0.8, quantity = {1, 2}},
            {id = "lightning_mastery_codex", chance = 0.7, quantity = {1, 1}},
            {id = "storm_dominion_scepter", chance = 0.6, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "blue_dragon",
        scale = 1.8,
        animations = {
            "storm_soar", "lightning_breath", "thunder_roar", "weather_control", "electrical_mastery"
        },
        variants = {
            "young_blue", "adult_blue", "ancient_blue", "storm_lord"
        }
    },
    
    -- Sound effects with storm characteristics
    sounds = {
        thunder_roar = {
            file = "blue_dragon_roar",
            synth = {
                instrument = "organ",
                notes = {"D1", "A1", "D2", "A2"},
                durations = {1.2, 1.0, 1.5, 2.0},
                volume = 1.1,
                thunder_crash = true
            }
        },
        lightning_breath = {
            file = "lightning_breath",
            synth = {
                instrument = "synthesizer",
                notes = {"B2", "D3", "F#3", "B3"},
                durations = {0.3, 0.2, 0.4, 0.6},
                volume = 0.9,
                electrical = true
            }
        },
        storm_call = {
            file = "storm_call",
            synth = {
                instrument = "choir",
                notes = {"G2", "B2", "D3", "G3", "B3"},
                durations = {0.8, 0.6, 0.8, 0.6, 1.2},
                volume = 0.8,
                atmospheric = true
            }
        },
        electrical_discharge = {
            file = "electrical_discharge",
            synth = {
                instrument = "electric_guitar",
                notes = {"E3", "G3", "B3"},
                durations = {0.2, 0.1, 0.3},
                volume = 0.7,
                crackling = true
            }
        }
    },
    
    -- Special storm dragon abilities
    abilities = {
        lightning_breath = {
            type = "active",
            description = "Chain lightning that jumps between enemies",
            effect = "chain_lightning_attack",
            cooldown = 10
        },
        storm_mastery = {
            type = "passive",
            description = "Controls weather patterns and atmospheric conditions",
            effect = "weather_control"
        },
        electrical_immunity = {
            type = "passive",
            description = "Immune to electrical damage and can absorb it for power",
            effect = "electrical_immunity"
        },
        thunder_shock = {
            type = "active",
            description = "Stunning thunder clap that disorients enemies",
            effect = "area_stun",
            cooldown = 15
        },
        storm_flight = {
            type = "passive",
            description = "Moves faster during storms and can ride lightning",
            effect = "storm_speed_bonus"
        },
        weather_summoning = {
            type = "active",
            description = "Summons powerful storms to the battlefield",
            effect = "storm_creation",
            manaCost = 100,
            cooldown = 30
        }
    }
}

-- Initialize the blue dragon entity using enhanced template
function BlueDragon.init(entity, world)
    -- Copy all fields from BlueDragon template to entity instance
    for k, v in pairs(BlueDragon) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return BlueDragon
