-- tiles/ruins.lua
local RuinsTile = {
    id = "ruins",
    name = "Ancient Ruins",
    passable = true,
    variants = 6, -- Different types of ruined structures
    
    -- Entities that can spawn in ruins
    spawns = {"shade", "memory_fragment", "forgotten_guardian", "relic", "crystal_growth"},
    
    -- Movement properties
    movementSpeed = 0.8, -- Slower movement through rubble
    
    -- Ruin-specific properties
    age = nil, -- Set during init (in years)
    civilization = nil, -- Set during init
    ruinType = nil, -- Set during init (temple, fortress, etc.)
    magicalResonance = 0, -- Some ruins have magical properties
    stabilityRating = 0.5, -- How stable the ruins are (0-1)
    
    -- Interaction function
    interact = function(tile, entity)
        -- Chance to find artifacts or trigger memories
        if math.random() < 0.25 then
            local items = {
                {id = "ancient_coin", chance = 0.4},
                {id = "memory_shard", chance = 0.3},
                {id = "broken_artifact", chance = 0.2},
                {id = "ancient_scroll", chance = 0.08},
                {id = "power_relic", chance = 0.02}
            }
            
            for _, item in ipairs(items) do
                if math.random() < item.chance then
                    return {
                        success = true,
                        message = "You discover a " .. item.id .. " among the ancient stones.",
                        effects = {
                            {type = "add_item", item = item.id, quantity = 1}
                        }
                    }
                end
            end
        end
        
        -- Chance to trigger special magical effects in magical ruins
        if tile.magicalResonance > 0.7 and math.random() < 0.2 then
            local effects = {
                {message = "The ruins glow briefly, and you feel a strange power coursing through you.", effect = {type = "restore_mana", amount = 20}},
                {message = "Ancient symbols on the wall briefly illuminate, revealing forgotten knowledge.", effect = {type = "gain_knowledge", amount = 1}},
                {message = "A memory from the distant past brushes against your mind.", effect = {type = "memory_flash", memory = "ancient_past"}},
                {message = "The air shimmers around you, momentarily revealing visions of the past.", effect = {type = "reveal_history", civilization = tile.civilization}}
            }
            
            local chosenEffect = effects[math.random(#effects)]
            return {
                success = true,
                message = chosenEffect.message,
                effects = {chosenEffect.effect}
            }
        end
        
        -- Check for hidden rooms or passages
        if entity.skills and entity.skills.archaeology and entity.skills.archaeology > 2 and math.random() < 0.15 then
            return {
                success = true,
                message = "Your archaeological knowledge helps you recognize a hidden passage among the ruins.",
                effects = {
                    {type = "discover_secret", secret_type = "hidden_room"}
                }
            }
        end
        
        -- Standard examination
        local ruinDescriptions = {
            temple = "The crumbling walls suggest this was once a sacred temple.",
            fortress = "Broken battlements and defensive walls hint this was a mighty fortress.",
            palace = "The elegant, decayed columns indicate this was a luxurious palace.",
            academy = "Remnants of writing and scholarly artifacts suggest this was a place of learning.",
            residential = "The layout of small rooms suggests these were once homes."
        }
        
        local description = ruinDescriptions[tile.ruinType] or "Stone fragments of a forgotten civilization litter the ground."
        
        return {
            success = false,
            message = description
        }
    end,
    
    -- Weather effects
    weatherEffects = {
        rain = function(tile)
            -- Rain weakens already unstable ruins
            if tile.stabilityRating < 0.4 then
                tile.stabilityRating = tile.stabilityRating - 0.05
                
                -- Very unstable ruins might collapse
                if tile.stabilityRating <= 0 then
                    return "trigger_event", "ruin_collapse"
                end
            end
            return nil
        end,
        
        lightning = function(tile)
            -- Lightning can reveal hidden magical properties
            if tile.magicalResonance > 0 and math.random() < 0.3 then
                return "visual_effect", "magical_resonance"
            end
            return nil
        end
    },
    
    -- Time effects
    timeEffects = {
        night = function(tile)
            -- Ruins might emit magical energy at night
            if tile.magicalResonance > 0.5 and math.random() < 0.2 then
                -- Glow effect
                tile.glowing = true
                -- Might attract magical creatures
                return "emanate", "magical_energy"
            end
            
            -- Ruins can be spookier at night
            tile.ambientSound = "haunted_whispers"
            return nil
        end,
        
        dawn = function(tile)
            -- Reset any night effects
            tile.glowing = false
            tile.ambientSound = nil
            return nil
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {0.6, 0.6, 0.6}, -- Grey stone
        variants = {
            {name = "pillars", description = "Broken columns and foundations"},
            {name = "walls", description = "Crumbling stone walls"},
            {name = "temple", description = "Remains of a sacred site"},
            {name = "fortress", description = "Ruins of defensive structures"},
            {name = "residential", description = "Ancient living quarters"},
            {name = "overgrown", description = "Ruins reclaimed by nature"}
        },
        decoration_objects = {
            {name = "fallen_pillar", chance = 0.3},
            {name = "ancient_statue", chance = 0.1},
            {name = "stone_carvings", chance = 0.2},
            {name = "mossy_rubble", chance = 0.4}
        },
        magical_effects = {
            glow = {color = {0.2, 0.5, 0.8}, intensity = 0.5, pulsing = true},
            floating_fragments = {chance = 0.3, count = {1, 3}}
        }
    },
    
    -- Audio properties
    ambientSound = "stone_wind",
    specialSounds = {
        {name = "ancient_whisper", trigger = "magical", chance = 0.1},
        {name = "stone_settling", trigger = "weather_change", chance = 0.2}
    },
    
    -- Archaeology properties
    archaeology = {
        knowledge_chance = 0.3, -- Chance to gain archaeological knowledge
        artifact_chance = 0.2, -- Chance to find artifacts with archaeology skill
        civilization_type = nil, -- Set during init
        historical_period = nil, -- Set during init
        inscription_chance = 0.15 -- Chance to find readable inscriptions
    },
    
    -- Connections to other tiles
    connects_to = {
        "ruins",
        "ancient_technology",
        "crystal_formation",
        "grass",
        "dirt",
        "stone"
    },
    
    -- Effect when discovered (first time interaction)
    onDiscover = function(tile, entity)
        if entity.journal and entity.journal.addDiscovery then
            entity.journal.addDiscovery({
                type = "ruins",
                civilization = tile.civilization,
                age = tile.age,
                ruinType = tile.ruinType,
                location = {x = tile.x, y = tile.y}
            })
        end
        
        return {
            message = "You've discovered ruins of the " .. tile.civilization .. " civilization.",
            effects = {
                {type = "gain_experience", amount = 25},
                {type = "reveal_map", radius = 3}
            }
        }
    end
}

function RuinsTile.init(world)
    print("Ruins tile module initialized")
    
    -- Set up possible civilizations
    local civilizations = {
        "Ancients",
        "Forerunners",
        "Crystal Mages",
        "War-Forged",
        "Deep Dwellers",
        "Sky Lords",
        "Elementalists",
        "Time Keepers"
    }
    
    -- Historical periods
    local periods = {
        {name = "Early Age", years = {2000, 3000}},
        {name = "Golden Era", years = {1200, 1800}},
        {name = "Final Dynasty", years = {500, 1000}},
        {name = "Cataclysm", years = {200, 400}}
    }
    
    -- Ruin types and their chances
    local ruinTypes = {
        {id = "temple", chance = 0.25},
        {id = "fortress", chance = 0.2},
        {id = "palace", chance = 0.15},
        {id = "academy", chance = 0.2},
        {id = "residential", chance = 0.2}
    }
    
    -- Register as archaeological site
    if world.systems and world.systems.archaeology then
        world.systems.archaeology:registerSite("ruins")
    end
    
    -- Register with lore system
    if world.systems and world.systems.lore then
        world.systems.lore:registerLoreSource("ruins")
    end
    
    -- Register different ruins civilizations with naming system
    if world.systems and world.systems.naming then
        for _, civilization in ipairs(civilizations) do
            world.systems.naming:registerNamingScheme("ruins_" .. civilization:lower(), {
                prefix = true,
                suffix = true,
                root_words = true,
                theme = civilization:lower()
            })
        end
    end
end

return RuinsTile