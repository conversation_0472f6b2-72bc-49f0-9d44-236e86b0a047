local Shark = {
    id = "shark",
    name = "Great White Shark",
    type = "fish",
    
    -- Entity categories
    categories = {"animal", "fish", "saltwater", "predator", "apex"},
    
    -- Threat and food categories
    threatCategories = {"player", "whale"},
    foodCategories = {"fish", "seal", "dolphin", "turtle", "carrion"},
    
    -- Stats
    maxHealth = 80,
    health = 80,
    maxStamina = 100,
    stamina = 100,
    speed = 2.8,
    
    -- Water properties
    water = {
        swimSpeed = 2.8,
        maxDepth = 20,
        preferredDepth = 5,
        oxygenLevel = 1.0,
        temperature = "warm",
        currentSpeed = 1.8
    },
    
    -- Behaviors
    behaviors = {"hunt", "patrol", "territorial", "scavenge"},
    
    -- Behavior configurations
    behaviorConfigs = {
        hunt = {
            moveSpeed = 3.2,
            searchRadius = 30,
            attackRange = 3.0,
            damage = 45,
            preferredPrey = {"seal", "dolphin", "fish", "turtle"},
            successChance = 0.85,
            ambushBonus = 0.2
        },
        patrol = {
            moveSpeed = 2.0,
            patrolRadius = 40,
            preferredDepth = 5,
            restInterval = {30, 40}
        },
        territorial = {
            moveSpeed = 2.5,
            territoryRadius = 50,
            preferredDepth = 5,
            aggressionLevel = 0.9
        },
        scavenge = {
            moveSpeed = 1.5,
            searchRadius = 35,
            preferredFood = {"carrion", "fish"},
            successChance = 0.7,
            detectionRange = 25
        }
    },
    
    -- Special abilities
    abilities = {
        charge = {
            speed = 4.0,
            damage = 50,
            duration = 0.8,
            cooldown = 5,
            staminaCost = 25
        },
        bite = {
            range = 3,
            damage = 35,
            duration = 0.4,
            cooldown = 2,
            effect = "bleed"
        },
        sense = {
            range = 40,
            duration = 5,
            cooldown = 8,
            effect = "detect",
            staminaCost = 10
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "shark",
        scale = 1.5,
        animations = {
            "idle", "swim", "charge", "attack", "patrol"
        },
        variants = {
            "white", "gray", "dark", "striped"
        }
    },
    
    -- Sound effects
    sounds = {
        splash = "fish_splash",
        attack = "shark_attack",
        swim = "shark_swim",
        charge = "shark_charge"
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.9, quantity = {2, 3}},
        {id = "fin", chance = 0.6, quantity = {1, 2}},
        {id = "tooth", chance = 0.4, quantity = {1, 2}},
        {id = "bone", chance = 0.5, quantity = {1, 2}}
    }
}

-- Initialize the entity
function Shark.init(entity, world)
    -- Copy all fields from Shark template to entity instance
    for k, v in pairs(Shark) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random shark variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Shark.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update water properties
    if entity.water then
        -- Adjust hunting success based on time of day
        if world and world.timeOfDay == "dawn" or world.timeOfDay == "dusk" then
            entity.behaviorConfigs.hunt.successChance = 0.9
        else
            entity.behaviorConfigs.hunt.successChance = 0.85
        end

        -- Adjust preferred depth based on time of day
        if world and world.timeOfDay == "night" then
            entity.water.preferredDepth = 3
        else
            entity.water.preferredDepth = 5
        end

        -- Apply ambush bonus when near surface
        if entity.position.y < 2 then
            entity.behaviorConfigs.hunt.successChance = 
                entity.behaviorConfigs.hunt.successChance + 
                entity.behaviorConfigs.hunt.ambushBonus
        end

        -- Adjust scavenging based on time of day
        if world and world.timeOfDay == "night" then
            entity.behaviorConfigs.scavenge.successChance = 0.8
        else
            entity.behaviorConfigs.scavenge.successChance = 0.7
        end
    end
end

return Shark 