-- behaviors/hunt.lua
-- Hunting behavior for predator entities

-- Create a factory function to ensure proper closure
local function createHuntBehavior()
    local HuntBehavior = {
        id = "hunt",
        name = "Hunt Behavior",
        
        -- Configuration parameters
        defaultConfig = {
            moveSpeed = 1.5,         -- Base movement speed
            huntRadius = 10,         -- How far to detect prey
            chaseRadius = 15,        -- How far to chase prey before giving up
            attackRadius = 1.5,      -- How close to get before attacking
            attackCooldown = 2.0,    -- Seconds between attacks
            preferredTargets = {},   -- List of preferred prey types
            retreatHealth = 0.3,     -- Retreat when health below this fraction
            scent_tracking = false,  -- Can follow scent trails
            pack_hunting = false,    -- Coordinates with other hunters
            ambush = false,          -- Can set up ambushes
            persistence = 0.7        -- How persistent in following targets (0-1)
        }
    }
    
    -- Initialize the behavior with an entity
    function HuntBehavior.init(entity, customConfig)
        -- Handle the case where this is being called during module registration
        if entity == HuntBehavior then
            -- Being called as part of module registration
            -- Store worldCore if needed: HuntBehavior.worldCore = customConfig
            return
        end
        
        -- Create behavior state for this entity
        entity.huntState = entity.huntState or {
            currentTarget = nil,
            lastKnownPosition = {x = 0, y = 0},
            attackTimer = 0,
            searchTimer = 0,
            hasAcquiredTarget = false,
            isReturningHome = false,
            originX = entity.position and entity.position.x or 0,
            originY = entity.position and entity.position.y or 0,
            config = {}
        }
        
        -- Apply configuration (custom settings override defaults)
        local config = entity.huntState.config
        for k, v in pairs(HuntBehavior.defaultConfig) do
            config[k] = v
        end
        
        if customConfig then
            for k, v in pairs(customConfig) do
                config[k] = v
            end
        end
    end
    
    -- Update function called on every game tick
    function HuntBehavior.update(entity, world, dt)
        local state = entity.huntState
        if not state then
            HuntBehavior.init(entity)
            state = entity.huntState
        end
        
        local config = state.config
        
        -- Update timers
        if state.attackTimer > 0 then
            state.attackTimer = state.attackTimer - dt
        end
        
        -- Check if should retreat due to low health
        if entity.health and entity.health / entity.maxHealth < config.retreatHealth then
            HuntBehavior.retreat(entity, world)
            return
        end
        
        -- If we have a target, track and possibly attack it
        if state.currentTarget then
            local target = world.entitySystem.entities[state.currentTarget]
            
            -- Check if target still exists
            if not target then
                state.currentTarget = nil
                state.hasAcquiredTarget = false
                return
            end
            
            -- Calculate distance to target
            local dx = target.position.x - entity.position.x
            local dy = target.position.y - entity.position.y
            local distSq = dx*dx + dy*dy
            
            -- If target is too far, give up chase
            if distSq > config.chaseRadius * config.chaseRadius then
                if math.random() > config.persistence then
                    state.currentTarget = nil
                    state.hasAcquiredTarget = false
                    return
                end
            end
            
            -- If close enough, attack
            if distSq <= config.attackRadius * config.attackRadius and state.attackTimer <= 0 then
                HuntBehavior.attack(entity, target, world)
                return
            end
            
            -- Otherwise move toward target
            local dist = math.sqrt(distSq)
            entity.velocity.x = (dx / dist) * config.moveSpeed
            entity.velocity.y = (dy / dist) * config.moveSpeed
            
            -- Record last known position
            state.lastKnownPosition.x = target.position.x
            state.lastKnownPosition.y = target.position.y
        else
            -- No target, search for one
            local newTarget = HuntBehavior.findTarget(entity, world)
            
            if newTarget then
                state.currentTarget = newTarget.id
                state.hasAcquiredTarget = true
                state.lastKnownPosition.x = newTarget.position.x
                state.lastKnownPosition.y = newTarget.position.y
            else
                -- No targets found, either return home or wander
                if state.isReturningHome then
                    HuntBehavior.returnHome(entity)
                else
                    -- Random movement or idle
                    entity.velocity.x = (math.random() * 2 - 1) * config.moveSpeed * 0.5
                    entity.velocity.y = (math.random() * 2 - 1) * config.moveSpeed * 0.5
                    
                    -- Occasionally decide to return home
                    if math.random() < 0.01 then
                        state.isReturningHome = true
                    end
                end
            end
        end
    end
    
    -- Find a suitable target to hunt
    function HuntBehavior.findTarget(entity, world)
        local state = entity.huntState
        local config = state.config
        
        -- Find all entities within hunt radius
        local nearbyEntities = world.entitySystem:findNearbyEntities(
            entity.position.x, entity.position.y, config.huntRadius
        )
        
        -- Filter for valid prey
        local validTargets = {}
        
        for _, potentialTarget in ipairs(nearbyEntities) do
            -- Skip self or other hunters of same type
            if potentialTarget.id == entity.id or potentialTarget.type == entity.type then
                goto continue
            end
            
            -- Check if this is a preferred target type
            local isPreferred = #config.preferredTargets == 0 -- If no preferences, all valid
            
            for _, targetType in ipairs(config.preferredTargets) do
                if potentialTarget.type == targetType then
                    isPreferred = true
                    break
                end
            end
            
            if isPreferred then
                table.insert(validTargets, potentialTarget)
            end
            
            ::continue::
        end
        
        -- Return closest valid target, if any
        if #validTargets > 0 then
            table.sort(validTargets, function(a, b)
                local dxA = a.position.x - entity.position.x
                local dyA = a.position.y - entity.position.y
                local distA = dxA*dxA + dyA*dyA
                
                local dxB = b.position.x - entity.position.x
                local dyB = b.position.y - entity.position.y
                local distB = dxB*dxB + dyB*dyB
                
                return distA < distB
            end)
            
            return validTargets[1]
        end
        
        return nil
    end
    
    -- Attack the target
    function HuntBehavior.attack(entity, target, world)
        local state = entity.huntState
        local config = state.config
        
        -- Reset attack timer
        state.attackTimer = config.attackCooldown
        
        -- Calculate damage based on entity stats
        local damage = entity.attackPower or 10
        
        -- Apply damage to target
        if target.health then
            target.health = target.health - damage
            
            -- Check if target is defeated
            if target.health <= 0 then
                HuntBehavior.onKill(entity, target, world)
            end
        end
        
        -- Trigger attack animation/sound
        if entity.triggerAnimation then
            entity.triggerAnimation("attack")
        end
        
        -- Notify the world of the attack (for other systems)
        if world.eventSystem then
            world.eventSystem:trigger("entity_attacked", {
                attacker = entity.id,
                target = target.id,
                damage = damage
            })
        end
    end
    
    -- Handle a successful kill
    function HuntBehavior.onKill(entity, target, world)
        local state = entity.huntState
        
        -- Reset hunting state
        state.currentTarget = nil
        state.hasAcquiredTarget = false
        
        -- Gain health or other benefits from kill
        if entity.health and entity.maxHealth then
            entity.health = math.min(entity.maxHealth, entity.health + entity.maxHealth * 0.2)
        end
        
        -- Possibly pick up loot from kill
        if entity.inventory and target.dropLoot then
            local loot = target.dropLoot()
            if loot then
                entity.inventory:addItem(loot.id, loot.quantity)
            end
        end
        
        -- Trigger feeding animation/sound
        if entity.triggerAnimation then
            entity.triggerAnimation("feed")
        end
        
        -- Consider whether to return home after kill
        if math.random() < 0.5 then
            state.isReturningHome = true
        end
    end
    
    -- Retreat when injured
    function HuntBehavior.retreat(entity, world)
        local state = entity.huntState
        
        -- Clear target
        state.currentTarget = nil
        state.isReturningHome = true
        
        -- Move away from any potential threats
        local nearbyThreats = world.entitySystem:findNearbyEntities(
            entity.position.x, entity.position.y, state.config.huntRadius
        )
        
        if #nearbyThreats > 0 then
            -- Find average position of threats
            local avgX, avgY = 0, 0
            local threatCount = 0
            
            for _, threat in ipairs(nearbyThreats) do
                -- Skip self
                if threat.id == entity.id then
                    goto continue
                end
                
                avgX = avgX + threat.position.x
                avgY = avgY + threat.position.y
                threatCount = threatCount + 1
                
                ::continue::
            end
            
            if threatCount > 0 then
                avgX = avgX / threatCount
                avgY = avgY / threatCount
                
                -- Move away from threats
                local dx = entity.position.x - avgX
                local dy = entity.position.y - avgY
                local dist = math.sqrt(dx*dx + dy*dy)
                
                if dist > 0 then
                    entity.velocity.x = (dx / dist) * state.config.moveSpeed * 1.5
                    entity.velocity.y = (dy / dist) * state.config.moveSpeed * 1.5
                end
            end
        else
            -- No threats, return home
            HuntBehavior.returnHome(entity)
        end
    end
    
    -- Return to original location
    function HuntBehavior.returnHome(entity)
        local state = entity.huntState
        
        -- Calculate direction to home
        local dx = state.originX - entity.position.x
        local dy = state.originY - entity.position.y
        local distSq = dx*dx + dy*dy
        
        -- If reached home, stop returning
        if distSq < 4 then
            state.isReturningHome = false
            entity.velocity.x = 0
            entity.velocity.y = 0
            return
        end
        
        -- Move toward home
        local dist = math.sqrt(distSq)
        entity.velocity.x = (dx / dist) * state.config.moveSpeed
        entity.velocity.y = (dy / dist) * state.config.moveSpeed
    end
    
    return HuntBehavior
end

-- Create and return the behavior module
return createHuntBehavior()
