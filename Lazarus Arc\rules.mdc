--[[
# 📖 Lazarus Arc - Cursor AI Default Ruleset

This guide outlines rules for Cursor AI to follow when generating entities, items, and components for Lazarus Arc (LOVE2D).

⚠️ Root Project Directory:
`C:\Users\<USER>\Downloads\lazarus arc\Lazarus Arc\`

🗃️ Important Project Folders:
- `entities/` - Game entities (players, NPCs, enemies)
- `items/` - Game items (weapons, consumables, equipment)
- `abilities/`, `assets/`, `behaviors/`, `biomes/`, `classes/`
- `overdrive/`, `raids/`, `skills/`, `structures/`, `tiles/`
- `utils/`, `weather/`
]]

-- ## 📌 Entity & Item Definition Rules
-- ✅ Defined as Lua tables in `entities/` and `items/`
-- ✅ Include `new()` returning deep copy
-- ✅ No global state; use passed/shared modules from `utils/`

-- ## 🎮 Multiplayer & Serialization
-- ✅ All entities/items MUST have UUID from `uuid.lua`
-- ✅ Implement `serialize()` and `deserialize(data)`
-- ✅ Separate transient state (animations/effects) from persistent state (inventory/stats)

-- ## ⚙️ Lifecycle Methods
-- ✅ Define lifecycle methods: `init(entity)`, `update(dt)`, `destroy(entity)`
-- ✅ Runtime state validation required

-- ## 🧩 Component System
-- ✅ Modular components (position, health, AI) in `behaviors/` or `utils/`
-- ✅ Naming convention: e.g., `position_component.lua`

-- ## ⚔️ Combat & Gameplay
-- ✅ Entities define: `onHit(damage, source)`, `onDeath()`, `onUpdate(dt)`
-- ✅ Items define: `onUse(user, target)`, `onEquip(entity)`, `onDrop(entity)`, `onPickup(entity)`
-- ✅ Deterministic logic; server authoritative

-- ## 🎮 Controls & Input
-- ✅ Player controls/input mapping EXCLUSIVELY in `controller.lua`
-- ✅ ALL input handling MUST be in `controller.lua` - DO NOT add elsewhere
-- ✅ `controller.lua` located at root (`Lazarus Arc/controller.lua`)
-- ✅ Any input logic outside `controller.lua` will result in disassembly

-- ## 🌍 World & Chunk Loading
-- ✅ Tiles defined in `tiles/` with IDs, images, optional hooks
-- ✅ Async chunk loading via `chunk_system.lua` (use `love.thread`/coroutines)
-- ✅ World tile access MUST use `world:getTile(x, y)` NOT `world:getTileAt(x, y)`
-- ✅ World object MUST be passed explicitly to entities needing tiles
-- ✅ Check if world exists before accessing tiles: `if world and world.getTile then`

-- ## 🔀 Parallel & Async
-- ✅ Use coroutine queues or LOVE2D threads for AI/world gen
-- ✅ Async/cached asset loading (`assets/`)

-- ## 🎨 Rendering
-- ✅ Entities must have `drawOrder` or `zIndex`
-- ✅ Items define UI rendering via `getIcon()`, `getDescription()`

-- ## 🎹 Sound & Effects
-- ✅ Use `sound_system.lua` and assets (`sounds/`)
-- ✅ Effects via `effectSystem.lua`

-- ## 📜 Database & Persistence
-- ✅ Persistent state management: `database_manager.lua`
-- ✅ Item/entity definitions via `item_database.lua`

-- ## 🌦️ Weather & Environment
-- ✅ Dynamic weather via `weather_system.lua` and `weather/`
-- ✅ Entities/items dynamically adapt to weather conditions

-- ## 🚩 UUID & Event Logging
-- ✅ Centralized UUID (`uuid.lua`)
-- ✅ Events logged via `event_log.lua`

-- ## 📝 Documentation & Testing
-- ✅ Templates require inline comments
-- ✅ Include `__test()` demonstrating usage
-- ✅ Validate construction/runtime states, raise descriptive errors

-- ## 📌 Build & Loader
-- ✅ Entry point: `game-loader.lua`
-- ✅ Core logic: `engine.lua`
-- ✅ Build via `001 - build.bat`

-- ## 📘 Implementation & Story
-- 📄 Refer to:
-- `01 - game pitch.txt`, `01 - story.txt`, `01 - character overview.txt`, `01 - implementation-guide.txt`

-- ## 🚫 Legacy Files
-- ⚠️ Ignore legacy files (`*.old`, `.bak`)
-- ✅ Reference current `.lua` files ONLY