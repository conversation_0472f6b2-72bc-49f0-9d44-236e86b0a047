-- entities/shadow_lord_boss.lua
-- Shadow Lord Boss with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local ShadowLordBoss = {
    id = "shadow_lord_boss",
    name = "Shadow Lord",
    type = "shadow_lord_boss",
    shape = {
        {0, -12}, {8, -8}, {12, 0}, {8, 8},
        {0, 12}, {-8, 8}, {-12, 0}, {-8, -8}
    },
    size = 18,

    -- Entity categories
    categories = {"boss", "shadow", "undead", "magical", "evil"},
    threatCategories = {"player", "living", "holy", "light"},
    
    -- Base stats (boss-level dark magic user)
    maxHealth = 400,
    health = 400,
    maxStamina = 150,
    stamina = 150,
    maxMana = 400,
    mana = 400,
    speed = 2.8,  -- Fast and elusive
    attack = 30,
    defense = 15,
    magicAttack = 45,
    magicDefense = 35,
    shadowMastery = 50,
    darkMagic = 40,
    
    -- Boss behaviors
    behaviors = {"shadow_teleport", "dark_magic", "summon_shadows", "phase_shift"},
    behaviorConfigs = {
        shadow_teleport = {
            teleportRange = 30,
            teleportCooldown = 5,
            shadowTrail = true,
            ambushBonus = 2.0
        },
        dark_magic = {
            spells = {"shadow_bolt", "darkness", "fear", "life_drain", "shadow_storm"},
            castChance = 0.5,
            manaCost = 40,
            areaEffect = true
        },
        summon_shadows = {
            minionTypes = {"shadow_wraith", "dark_spirit", "void_spawn"},
            maxMinions = 6,
            summonCooldown = 12,
            shadowBonus = 2.0
        },
        phase_shift = {
            phases = {
                {healthThreshold = 0.8, abilities = {"shadow_clones", "darkness_aura"}},
                {healthThreshold = 0.5, abilities = {"void_rift", "mass_fear"}},
                {healthThreshold = 0.2, abilities = {"shadow_apocalypse", "final_darkness"}}
            }
        }
    },
    
    -- Enhanced variant system for shadow lord boss
    variantChances = {
        normal = 0.55,          -- 55% shadow lord
        shiny = 0.35,           -- 35% void lord (shiny)
        rare = 0.08,            -- 8% nightmare lord (rare)
        legendary = 0.02        -- 2% darkness incarnate (legendary)
    },
    
    variants = {
        normal = {
            name = "Shadow Lord",
            description = "A powerful undead sorcerer who commands the shadows",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.3, 0.3, 0.4, 1.0},  -- Dark shadowy
                shadow_aura = true
            }
        },
        
        shiny = {
            name = "Void Lord",
            description = "A master of the void who can tear holes in reality",
            statModifiers = {
                maxHealth = 1.4,    -- 560 health
                maxMana = 1.6,      -- 640 mana
                magicAttack = 1.5,  -- 67.5 magic attack
                speed = 1.3,        -- 3.64 speed
                void_mastery = 3.0
            },
            appearanceModifiers = {
                scale = 1.2,
                glow = true,
                colorTint = {0.2, 0.2, 0.6, 1.0},  -- Deep void purple
                void_rifts = true,
                reality_distortion = true,
                cosmic_darkness = true
            },
            soundModifiers = {
                pitch = 0.7,
                volume = 1.4,
                void_resonance = true
            }
        },
        
        rare = {
            name = "Nightmare Lord",
            description = "A being of pure terror that feeds on fear and despair",
            statModifiers = {
                maxHealth = 1.6,    -- 640 health
                maxMana = 1.8,      -- 720 mana
                magicAttack = 1.7,  -- 76.5 magic attack
                speed = 1.4,        -- 3.92 speed
                fear_mastery = 4.0,
                terror_aura = 3.0
            },
            appearanceModifiers = {
                scale = 1.3,
                colorTint = {0.4, 0.2, 0.2, 1.0},  -- Blood-red nightmare
                nightmare_form = true,
                terror_manifestation = true,
                fear_distortion = true,
                screaming_faces = true
            },
            soundModifiers = {
                pitch = 0.6,
                volume = 1.5,
                reverb = true,
                nightmare_whispers = true
            }
        },
        
        legendary = {
            name = "Darkness Incarnate",
            description = "The physical manifestation of primordial darkness itself",
            statModifiers = {
                maxHealth = 2.2,    -- 880 health
                maxMana = 2.5,      -- 1000 mana
                magicAttack = 2.0,  -- 90 magic attack
                speed = 1.6,        -- 4.48 speed
                darkness_embodiment = 10.0,
                reality_negation = 5.0
            },
            appearanceModifiers = {
                scale = 1.5,
                glow = true,
                colorTint = {0.1, 0.1, 0.1, 1.0},  -- Pure darkness with anti-light
                darkness_incarnate = true,
                light_absorption = true,
                reality_void = true,
                primordial_darkness = "legendary"
            },
            soundModifiers = {
                pitch = 0.3,
                volume = 1.8,
                reverb = true,
                echo = true,
                darkness_absolute = true
            }
        }
    },
    
    -- Boss drops
    baseDrops = {
        {id = "shadow_essence", chance = 1.0, quantity = {3, 6}},
        {id = "dark_magic_crystal", chance = 1.0, quantity = {2, 4}},
        {id = "shadow_lord_robe", chance = 0.9, quantity = {1, 1}},
        {id = "darkness_orb", chance = 0.8, quantity = {1, 2}}
    },
    
    -- Variant-specific boss drops
    variantDrops = {
        shiny = {
            {id = "void_lord_crown", chance = 1.0, quantity = {1, 1}},
            {id = "reality_tear_fragment", chance = 0.9, quantity = {1, 2}},
            {id = "void_mastery_tome", chance = 0.8, quantity = {1, 1}},
            {id = "cosmic_darkness_shard", chance = 0.7, quantity = {1, 1}}
        },
        rare = {
            {id = "nightmare_lord_mask", chance = 1.0, quantity = {1, 1}},
            {id = "terror_essence", chance = 0.9, quantity = {2, 3}},
            {id = "fear_incarnation_crystal", chance = 0.8, quantity = {1, 1}},
            {id = "nightmare_realm_key", chance = 0.7, quantity = {1, 1}}
        },
        legendary = {
            {id = "darkness_incarnate_core", chance = 1.0, quantity = {1, 1}},
            {id = "primordial_darkness_essence", chance = 1.0, quantity = {1, 2}},
            {id = "reality_negation_orb", chance = 0.9, quantity = {1, 1}},
            {id = "absolute_darkness_codex", chance = 0.8, quantity = {1, 1}},
            {id = "light_extinction_crystal", chance = 0.7, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "shadow_lord_boss",
        scale = 1.3,
        animations = {
            "float_menacing", "cast_dark_magic", "shadow_teleport", "summon_darkness", "phase_shift", "final_form"
        },
        variants = {
            "shadow_lord", "void_lord", "nightmare_lord", "darkness_incarnate"
        }
    },
    
    -- Sound effects with dark boss characteristics
    sounds = {
        dark_laughter = {
            file = "shadow_lord_laugh",
            synth = {
                instrument = "organ",
                notes = {"D2", "F#2", "A2", "D3"},
                durations = {0.5, 0.4, 0.6, 0.8},
                volume = 0.9,
                sinister = true
            }
        },
        shadow_magic = {
            file = "shadow_magic",
            synth = {
                instrument = "synthesizer",
                notes = {"C2", "Eb2", "G2", "Bb2"},
                durations = {0.6, 0.5, 0.7, 0.8},
                volume = 0.8,
                dark_energy = true
            }
        },
        void_tear = {
            file = "void_tear",
            synth = {
                instrument = "distorted_guitar",
                notes = {"E1", "G1", "B1"},
                durations = {0.8, 0.6, 1.0},
                volume = 1.0,
                reality_rip = true
            }
        },
        nightmare_scream = {
            file = "nightmare_scream",
            synth = {
                instrument = "choir",
                notes = {"F2", "Ab2", "C3", "F3"},
                durations = {1.0, 0.8, 1.2, 1.5},
                volume = 1.1,
                terrifying = true
            }
        }
    },
    
    -- Special boss abilities
    abilities = {
        shadow_mastery = {
            type = "passive",
            description = "Complete control over shadows and darkness",
            effect = "shadow_manipulation"
        },
        void_teleportation = {
            type = "active",
            description = "Instantly teleport through shadow realm",
            effect = "instant_movement",
            cooldown = 8
        },
        darkness_aura = {
            type = "passive",
            description = "Reduces visibility and instills fear in nearby enemies",
            effect = "debuff_aura"
        },
        life_drain = {
            type = "active",
            description = "Drains life force from all nearby living beings",
            effect = "area_life_steal",
            cooldown = 15
        },
        shadow_resurrection = {
            type = "passive",
            description = "Can resurrect once by merging with shadows",
            effect = "shadow_rebirth"
        }
    }
}

-- Initialize the shadow lord boss entity using enhanced template
function ShadowLordBoss.init(entity, world)
    -- Copy all fields from ShadowLordBoss template to entity instance
    for k, v in pairs(ShadowLordBoss) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return ShadowLordBoss
