local Seagull = {
    id = "seagull",
    name = "Seagull",
    type = "bird",
    
    -- Entity categories
    categories = {"animal", "bird", "flying", "coastal"},
    
    -- Threat and food categories
    threatCategories = {"predator", "player"},
    foodCategories = {"fish", "insect", "food", "carrion"},
    
    -- Stats
    maxHealth = 20,
    health = 20,
    maxStamina = 50,
    stamina = 50,
    speed = 2.0,
    
    -- Flight properties
    flight = {
        maxHeight = 10,
        minHeight = 1,
        ascentSpeed = 1.0,
        descentSpeed = 1.5,
        hoverHeight = 3,
        currentHeight = 3,
        wingFlapRate = 0.15
    },
    
    -- Behaviors
    behaviors = {"flock", "forage", "patrol", "scavenge"},
    
    -- Behavior configurations
    behaviorConfigs = {
        flock = {
            moveSpeed = 2.5,
            followDistance = 4,
            separationDistance = 2,
            alignmentStrength = 0.3,
            cohesionStrength = 0.4,
            maxFlockSize = 15
        },
        forage = {
            moveSpeed = 1.8,
            searchRadius = 12,
            forageTime = {3, 8},
            foodTypes = {"fish", "insect", "food"},
            diveChance = 0.6
        },
        patrol = {
            moveSpeed = 2.2,
            patrolRadius = 25,
            coastLineFollow = true,
            restInterval = {5, 10}
        },
        scavenge = {
            moveSpeed = 2.0,
            searchRadius = 15,
            scavengeTime = {2, 5},
            foodTypes = {"carrion", "food"},
            alertRadius = 8
        }
    },
    
    -- Special abilities
    abilities = {
        dive = {
            speedBoost = 2.5,
            duration = 1,
            cooldown = 3,
            staminaCost = 10
        },
        screech = {
            range = 10,
            duration = 0.5,
            cooldown = 2,
            effect = "alert"
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "seagull",
        scale = 0.7,
        animations = {
            "idle", "fly", "dive", "land", "screech"
        },
        variants = {
            "herring", "laughing", "ring-billed", "great_black"
        }
    },
    
    -- Sound effects
    sounds = {
        screech = "seagull_screech",
        wingFlap = "seagull_wing_flap",
        call = "seagull_call"
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.5, quantity = {1, 1}},
        {id = "feather", chance = 0.7, quantity = {1, 3}},
        {id = "beak", chance = 0.3, quantity = {1, 1}}
    }
}

-- Initialize the entity
function Seagull.init(entity, world)
    -- Copy all fields from Seagull template to entity instance
    for k, v in pairs(Seagull) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random seagull variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Seagull.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update flight properties
    if entity.flight then
        -- Update wing flapping
        if entity.flight.currentHeight > entity.flight.minHeight then
            entity.flight.wingFlapRate = 0.15
        else
            entity.flight.wingFlapRate = 0.3
        end
    end
end

return Seagull