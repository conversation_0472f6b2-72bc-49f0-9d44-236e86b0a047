-- weather/solar_flare.lua
-- Solar flare weather pattern - intense radiation and electronic interference

local SolarFlareWeather = {}
SolarFlareWeather.__index = SolarFlareWeather

-- Basic properties
SolarFlareWeather.id = "solar_flare"
SolarFlareWeather.name = "Solar Flare"

-- Visual properties
SolarFlareWeather.visual = {
    skyColor = {r = 255, g = 200, b = 100}, -- Bright yellow-orange
    sunIntensity = 1.5,
    cloudCoverage = 0.2,
    cloudColor = {r = 255, g = 180, b = 80}, -- Bright clouds
    ambientLightLevel = 1.2,
    flareColor = {r = 255, g = 150, b = 50} -- Solar flare color
}

-- Environment modifiers
SolarFlareWeather.environment = {
    temperature = 1.3, -- Increased temperature
    visibility = 0.8, -- Good visibility
    humidity = 0.3, -- Very dry
    windStrength = 0.4, -- Light wind
    radiationLevel = 0.9, -- High radiation
    electromagneticInterference = 0.8 -- High EM interference
}

-- Particle systems
SolarFlareWeather.particles = {
    solarRadiation = {
        enabled = true,
        intensity = 0.9,
        color = {r = 255, g = 200, b = 100},
        size = {min = 1, max = 3},
        speed = {min = 100, max = 200}
    },
    energyWaves = {
        enabled = true,
        intensity = 0.7,
        color = {r = 255, g = 150, b = 50},
        size = {min = 2, max = 4},
        speed = {min = 50, max = 100}
    },
    staticDischarge = {
        enabled = true,
        intensity = 0.5,
        color = {r = 200, g = 200, b = 255},
        size = {min = 1, max = 2},
        speed = {min = 200, max = 300}
    }
}

-- Sound effects
SolarFlareWeather.sounds = {
    ambient = "solar_ambient",
    static = "radio_static",
    discharge = "energy_discharge",
    warning = "radiation_warning",
    volume = 0.8
}

-- Effects on game entities and tiles
SolarFlareWeather.effects = {
    -- Damage to exposed entities
    exposureDamage = 5, -- Moderate damage per second
    
    -- Equipment damage
    equipmentDamageRate = 2.0,
    
    -- Movement effects
    movementSpeedMultiplier = 0.9,
    
    -- Vision effects
    visionRange = 0.8,
    
    -- Radiation effects
    radiationDamage = 3,
    
    -- Electronic interference
    electronicMalfunctionChance = 0.3,
    
    -- Shield effects
    shieldEffectiveness = 0.7,
    
    -- Energy effects
    energyDrainRate = 1.5
}

-- Transition probabilities to other weather (per game hour)
SolarFlareWeather.transitions = {
    clear = 0.4,
    cloudy = 0.2,
    rain = 0.1,
    -- Implied: 0.3 chance to stay solar flare
}

-- Day/night cycle modifiers
SolarFlareWeather.timeModifiers = {
    dawn = {
        skyColor = {r = 255, g = 180, b = 80},
        ambientLightLevel = 1.1
    },
    day = {
        -- Uses default properties
    },
    dusk = {
        skyColor = {r = 255, g = 150, b = 50},
        ambientLightLevel = 1.0
    },
    night = {
        skyColor = {r = 200, g = 100, b = 30},
        ambientLightLevel = 0.8,
        radiationIntensity = 1.2 -- More intense radiation at night
    }
}

-- Helper function to deep copy a table
local function deepCopy(orig)
    local copy
    if type(orig) == 'table' then
        copy = {}
        for orig_key, orig_value in next, orig, nil do
            copy[deepCopy(orig_key)] = deepCopy(orig_value)
        end
        setmetatable(copy, deepCopy(getmetatable(orig)))
    else
        copy = orig
    end
    return copy
end

-- Create a new instance of the weather pattern
function SolarFlareWeather.new()
    local instance = setmetatable({}, SolarFlareWeather)
    -- Copy all properties
    for k, v in pairs(SolarFlareWeather) do
        if k ~= "__index" and k ~= "new" and type(v) ~= "function" then
            instance[k] = deepCopy(v)
        end
    end
    return instance
end

-- Initialize the weather pattern
function SolarFlareWeather:init(world)
    if not world then return self end
    
    print("Initializing solar flare weather")
    
    -- Set global light level
    if self.visual and self.visual.ambientLightLevel then
        world.lightLevel = self.visual.ambientLightLevel
    end
    
    -- Start ambient sounds
    if self.sounds and self.sounds.ambient then
        -- Play ambient solar sound
        print("Playing sound: " .. self.sounds.ambient)
    end
    
    -- Create particle systems
    if self.particles and self.particles.solarRadiation and self.particles.solarRadiation.enabled then
        -- Create solar radiation particles
        print("Creating solar radiation particles with intensity: " .. self.particles.solarRadiation.intensity)
    end
    
    if self.particles and self.particles.energyWaves and self.particles.energyWaves.enabled then
        -- Create energy wave particles
        print("Creating energy wave particles with intensity: " .. self.particles.energyWaves.intensity)
    end
    
    if self.particles and self.particles.staticDischarge and self.particles.staticDischarge.enabled then
        -- Create static discharge particles
        print("Creating static discharge particles with intensity: " .. self.particles.staticDischarge.intensity)
    end
    
    -- Display warning message to players
    if world.messageSystem then
        world.messageSystem:broadcast("WARNING: Solar flare detected. High radiation levels and electronic interference expected.")
    end
    
    return self
end

-- Update function called every frame
function SolarFlareWeather:update(world, dt)
    if not world then return end
    
    -- Update day/night cycle based on world time
    local timeOfDay = world.timeOfDay or "day"
    local modifiers = self.timeModifiers and self.timeModifiers[timeOfDay]
    
    if modifiers then
        -- Apply time-specific modifiers
        if modifiers.skyColor then
            -- Change sky color
        end
        
        if modifiers.ambientLightLevel then
            world.lightLevel = modifiers.ambientLightLevel
        end
        
        if modifiers.radiationIntensity and self.particles and self.particles.solarRadiation then
            self.particles.solarRadiation.intensity = modifiers.radiationIntensity
        end
    end
    
    -- Apply effects to game world
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            -- Check if entity is exposed to solar flare
            if entity.isExposed then
                -- Apply combined damage
                if entity.health and self.effects then
                    entity.health = entity.health - 
                        ((self.effects.exposureDamage + 
                          self.effects.radiationDamage) * dt)
                end
                
                -- Apply equipment damage
                if entity.equipment and self.effects then
                    for _, item in pairs(entity.equipment) do
                        if item.durability then
                            item.durability = item.durability - 
                                (self.effects.equipmentDamageRate * dt)
                        end
                    end
                end
                
                -- Apply movement speed reduction
                if entity.speed and self.effects then
                    entity.speed = entity.speed * self.effects.movementSpeedMultiplier
                end
                
                -- Apply energy drain
                if entity.energy and self.effects then
                    entity.energy = entity.energy - 
                        (self.effects.energyDrainRate * dt)
                end
            end
            
            -- Special effects for different entity types
            if entity.categories and self.effects then
                -- Effect on electronic devices
                if table.contains(entity.categories, "electronic") then
                    if math.random() < self.effects.electronicMalfunctionChance * dt then
                        if entity.onMalfunction then
                            entity:onMalfunction("em_interference")
                        end
                    end
                end
                
                -- Effect on shields
                if table.contains(entity.categories, "shield") then
                    if entity.effectiveness then
                        entity.effectiveness = entity.effectiveness * 
                            self.effects.shieldEffectiveness
                    end
                end
                
                -- Effect on sensors and vision devices
                if table.contains(entity.categories, "sensor") then
                    if entity.range then
                        entity.range = entity.range * self.effects.visionRange
                    end
                end
            end
        end
    end
    
    -- Random static discharge effects
    if math.random() < 0.1 and self.sounds then
        -- Play static sound
        print("Playing sound: " .. self.sounds.static)
    end
    
    -- Random energy discharge sounds
    if math.random() < 0.15 and self.sounds then
        -- Play discharge sound
        print("Playing sound: " .. self.sounds.discharge)
    end
end

-- Clean up when weather changes
function SolarFlareWeather:cleanUp(world)
    if not world then return end
    
    -- Stop ongoing sounds and particle effects
    print("Solar flare weather ending")
    
    -- Notify players
    if world.messageSystem then
        world.messageSystem:broadcast("Solar flare passing. Radiation levels decreasing.")
    end
    
    -- Reset entity states
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            -- Reset movement speed
            if entity.speed and entity.defaultSpeed then
                entity.speed = entity.defaultSpeed
            end
            
            -- Reset shield effectiveness
            if entity.categories and table.contains(entity.categories, "shield") then
                if entity.effectiveness and entity.defaultEffectiveness then
                    entity.effectiveness = entity.defaultEffectiveness
                end
            end
            
            -- Reset sensor ranges
            if entity.categories and table.contains(entity.categories, "sensor") then
                if entity.range and entity.defaultRange then
                    entity.range = entity.defaultRange
                end
            end
        end
    end
end

-- Table contains helper function
function table.contains(table, element)
    if not table then return false end
    
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

return SolarFlareWeather 