local BattleAxe = {
    id = "battle_axe",
    name = "Battle Axe",
    type = "weapon",
    category = "axe",
    
    -- Properties
    properties = {
        damage = 32,
        attackSpeed = 0.9,
        range = 1.8,
        durability = 180,
        maxDurability = 180,
        chopPower = 1.5,
        chopRadius = 1.2,
        chopAngle = 1.2,
        criticalChance = 0.2,
        criticalMultiplier = 2.2,
        armorPenetration = 0.6,
        staggerChance = 0.3,
        staggerDuration = 0.5,
        cleaveChance = 0.25,
        cleaveCount = 2,
        cleaveDamage = 0.7,
        weight = 1.5,
        momentum = 1.2,
        woodcutting = 1.0
    },
    
    -- Appearance
    appearance = {
        sprite = "battle_axe",
        scale = 1.0,
        animations = {
            "idle",
            "chop",
            "cleave",
            "heavy"
        },
        variants = {
            "steel",
            "iron",
            "bronze",
            "stone"
        },
        blendMode = "normal",
        tint = {0.7, 0.7, 0.7},
        alpha = 1.0
    },
    
    -- Sound effects
    sounds = {
        chop = "battle_axe_chop",
        cleave = "battle_axe_cleave",
        critical = "battle_axe_critical",
        woodcut = "battle_axe_woodcut"
    },
    
    -- Effects
    effects = {
        stagger = {
            type = "status",
            duration = 0.5,
            effects = {
                slow = 0.5
            }
        },
        cleave = {
            type = "status",
            duration = 0.5,
            effects = {
                damage = 0.7,
                count = 2
            }
        }
    }
}

-- Initialize the weapon
function BattleAxe.init(weapon, world)
    -- Copy all fields from BattleAxe template to weapon instance
    for k, v in pairs(BattleAxe) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        momentum = 0,
        lastChopTime = 0
    }

    return weapon
end

-- Update the weapon
function BattleAxe.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Update momentum
        weapon.properties.weaponState.momentum = math.max(0,
            weapon.properties.weaponState.momentum - dt * 2)
    end
end

-- Handle attack
function BattleAxe.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Calculate damage
    local damage = weapon.properties.damage
    
    -- Apply momentum bonus
    if weapon.properties.weaponState.momentum > 0 then
        damage = damage * (1 + weapon.properties.weaponState.momentum * 0.2)
    end
    
    -- Check for critical hit
    if math.random() < weapon.properties.criticalChance then
        damage = damage * weapon.properties.criticalMultiplier
        
        -- Play critical sound
        if world.playSound then
            world.playSound(weapon.sounds.critical)
        end
    end
    
    -- Apply damage
    if target.takeDamage then
        target.takeDamage(damage)
    end
    
    -- Check for stagger effect
    if math.random() < weapon.properties.staggerChance then
        -- Create stagger effect
        if world.createEffect then
            world.createEffect({
                type = "stagger",
                position = target.position,
                radius = weapon.properties.chopRadius,
                duration = weapon.properties.staggerDuration
            })
        end
        
        -- Apply stagger effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.stagger)
        end
    end
    
    -- Check for cleave effect
    if math.random() < weapon.properties.cleaveChance then
        -- Create cleave effect
        if world.createEffect then
            world.createEffect({
                type = "cleave",
                position = target.position,
                radius = weapon.properties.chopRadius * 1.5,
                duration = 0.5
            })
        end
        
        -- Apply cleave effect to nearby entities
        if world.entities then
            for _, entity in ipairs(world.entities) do
                if entity.position and entity ~= target then
                    local distance = math.sqrt(
                        (entity.position.x - target.position.x)^2 + 
                        (entity.position.y - target.position.y)^2
                    )
                    
                    if distance <= weapon.properties.chopRadius * 1.5 then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.cleave)
                        end
                        
                        if entity.takeDamage then
                            entity.takeDamage(damage * weapon.properties.cleaveDamage)
                        end
                    end
                end
            end
        end
        
        -- Play cleave sound
        if world.playSound then
            world.playSound(weapon.sounds.cleave)
        end
    end
    
    -- Update momentum
    weapon.properties.weaponState.momentum = math.min(1,
        weapon.properties.weaponState.momentum + 0.2)
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function BattleAxe.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough momentum
    if weapon.properties.weaponState.momentum < 0.5 then
        return false
    end
    
    -- Calculate heavy chop damage
    local damage = weapon.properties.damage * 1.5
    
    -- Apply momentum bonus
    damage = damage * (1 + weapon.properties.weaponState.momentum * 0.3)
    
    -- Apply damage
    if target.takeDamage then
        target.takeDamage(damage)
    end
    
    -- Create heavy chop effect
    if world.createEffect then
        world.createEffect({
            type = "heavy_chop",
            position = target.position,
            radius = weapon.properties.chopRadius * 2,
            duration = 0.5
        })
    end
    
    -- Apply effects to nearby entities
    if world.entities then
        for _, entity in ipairs(world.entities) do
            if entity.position then
                local distance = math.sqrt(
                    (entity.position.x - target.position.x)^2 + 
                    (entity.position.y - target.position.y)^2
                )
                
                if distance <= weapon.properties.chopRadius * 2 then
                    -- Apply stagger effect
                    if entity.applyEffect then
                        entity.applyEffect(weapon.effects.stagger)
                    end
                    
                    -- Apply damage
                    if entity.takeDamage then
                        entity.takeDamage(damage * 0.5)
                    end
                end
            end
        end
    end
    
    -- Reset momentum
    weapon.properties.weaponState.momentum = 0
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 3)
    
    return true
end

return BattleAxe 