-- utils/debug_overlays.lua
-- Enhanced debug overlays for existing features

local DebugOverlays = {}

-- Initialize debug overlays
function DebugOverlays.init()
    DebugOverlays.chunkBoundaries = {
        enabled = false,
        color = {0, 1, 0, 0.5},
        lineWidth = 2,
        showCoordinates = true,
        showLoadStatus = true
    }
    
    DebugOverlays.collisionBoxes = {
        enabled = false,
        entityColor = {1, 0, 0, 0.7},
        structureColor = {0, 0, 1, 0.7},
        tileColor = {1, 1, 0, 0.5},
        lineWidth = 1,
        showDetails = true
    }
    
    DebugOverlays.wireframe = {
        enabled = false,
        color = {1, 1, 1, 0.3},
        lineWidth = 1,
        showVertices = true,
        showNormals = false
    }
    
    DebugOverlays.deadzone = {
        enabled = false,
        gridColor = {0.5, 0.5, 0.5, 0.3},
        deadzoneColor = {1, 0, 0, 0.2},
        lineWidth = 1,
        gridSize = 32,
        showPlayerDeadzone = true
    }
    
    DebugOverlays.fps = {
        enabled = false,
        position = {x = 10, y = 10},
        backgroundColor = {0, 0, 0, 0.7},
        textColor = {1, 1, 1, 1},
        showDetailed = true
    }
end

-- Draw chunk boundaries
function DebugOverlays.drawChunkBoundaries(world, camera)
    if not DebugOverlays.chunkBoundaries.enabled then return end
    if not world or not world.chunkSystem then return end
    
    love.graphics.push()
    love.graphics.setColor(DebugOverlays.chunkBoundaries.color)
    love.graphics.setLineWidth(DebugOverlays.chunkBoundaries.lineWidth)
    
    local chunkSize = world.chunkSystem.chunkSize or 16
    local tileSize = 32 -- Assuming 32x32 tiles
    local chunkPixelSize = chunkSize * tileSize
    
    -- Get visible area
    local startX, startY, endX, endY = DebugOverlays.getVisibleBounds(camera, chunkPixelSize)
    
    -- Draw chunk grid
    for x = startX, endX, chunkPixelSize do
        love.graphics.line(x, startY, x, endY)
    end
    
    for y = startY, endY, chunkPixelSize do
        love.graphics.line(startX, y, endX, y)
    end
    
    -- Draw chunk information
    if DebugOverlays.chunkBoundaries.showCoordinates then
        love.graphics.setColor(1, 1, 1, 1)
        for x = startX, endX, chunkPixelSize do
            for y = startY, endY, chunkPixelSize do
                local chunkX = math.floor(x / chunkPixelSize)
                local chunkY = math.floor(y / chunkPixelSize)
                
                -- Draw chunk coordinates
                love.graphics.print(string.format("(%d,%d)", chunkX, chunkY), x + 5, y + 5)
                
                -- Show load status if available
                if DebugOverlays.chunkBoundaries.showLoadStatus and world.chunkSystem.isLoaded then
                    local loaded = world.chunkSystem:isLoaded(chunkX, chunkY)
                    local status = loaded and "LOADED" or "UNLOADED"
                    local color = loaded and {0, 1, 0, 1} or {1, 0, 0, 1}
                    love.graphics.setColor(color)
                    love.graphics.print(status, x + 5, y + 20)
                    love.graphics.setColor(1, 1, 1, 1)
                end
            end
        end
    end
    
    love.graphics.pop()
end

-- Draw collision boxes
function DebugOverlays.drawCollisionBoxes(world, entities)
    if not DebugOverlays.collisionBoxes.enabled then return end
    
    love.graphics.push()
    love.graphics.setLineWidth(DebugOverlays.collisionBoxes.lineWidth)
    
    -- Draw entity collision boxes
    if entities then
        love.graphics.setColor(DebugOverlays.collisionBoxes.entityColor)
        for _, entity in ipairs(entities) do
            if entity.position and entity.collisionBox then
                DebugOverlays.drawEntityCollisionBox(entity)
            end
        end
    end
    
    -- Draw structure collision boxes
    if world and world.structures then
        love.graphics.setColor(DebugOverlays.collisionBoxes.structureColor)
        for _, structure in ipairs(world.structures) do
            if structure.position and structure.collisionBox then
                DebugOverlays.drawStructureCollisionBox(structure)
            end
        end
    end
    
    -- Draw tile collision boxes
    if world and world.tiles then
        love.graphics.setColor(DebugOverlays.collisionBoxes.tileColor)
        DebugOverlays.drawTileCollisionBoxes(world)
    end
    
    love.graphics.pop()
end

-- Draw entity collision box
function DebugOverlays.drawEntityCollisionBox(entity)
    local x = entity.position.x
    local y = entity.position.y
    local box = entity.collisionBox
    
    -- Draw collision rectangle
    love.graphics.rectangle("line", x + box.x, y + box.y, box.width, box.height)
    
    -- Draw center point
    love.graphics.circle("fill", x, y, 2)
    
    -- Show details if enabled
    if DebugOverlays.collisionBoxes.showDetails then
        love.graphics.setColor(1, 1, 1, 1)
        love.graphics.print(entity.type or "entity", x + box.x, y + box.y - 15)
        love.graphics.print(string.format("%.1f,%.1f", x, y), x + box.x, y + box.y + box.height + 2)
    end
end

-- Draw structure collision box
function DebugOverlays.drawStructureCollisionBox(structure)
    local x = structure.position.x
    local y = structure.position.y
    local box = structure.collisionBox
    
    love.graphics.rectangle("line", x + box.x, y + box.y, box.width, box.height)
    
    if DebugOverlays.collisionBoxes.showDetails then
        love.graphics.setColor(1, 1, 1, 1)
        love.graphics.print(structure.type or "structure", x + box.x, y + box.y - 15)
    end
end

-- Draw tile collision boxes
function DebugOverlays.drawTileCollisionBoxes(world)
    local tileSize = 32
    
    for x, column in pairs(world.tiles) do
        for y, tile in pairs(column) do
            if tile.solid or tile.collision then
                local screenX = x * tileSize
                local screenY = y * tileSize
                love.graphics.rectangle("line", screenX, screenY, tileSize, tileSize)
                
                if DebugOverlays.collisionBoxes.showDetails then
                    love.graphics.setColor(1, 1, 1, 1)
                    love.graphics.print("S", screenX + 2, screenY + 2)
                end
            end
        end
    end
end

-- Draw wireframe mode
function DebugOverlays.drawWireframe(world, entities)
    if not DebugOverlays.wireframe.enabled then return end
    
    love.graphics.push()
    love.graphics.setColor(DebugOverlays.wireframe.color)
    love.graphics.setLineWidth(DebugOverlays.wireframe.lineWidth)
    
    -- Draw entity wireframes
    if entities then
        for _, entity in ipairs(entities) do
            DebugOverlays.drawEntityWireframe(entity)
        end
    end
    
    -- Draw structure wireframes
    if world and world.structures then
        for _, structure in ipairs(world.structures) do
            DebugOverlays.drawStructureWireframe(structure)
        end
    end
    
    love.graphics.pop()
end

-- Draw entity wireframe
function DebugOverlays.drawEntityWireframe(entity)
    if not entity.position then return end
    
    local x = entity.position.x
    local y = entity.position.y
    local width = entity.width or 16
    local height = entity.height or 16
    
    -- Draw entity outline
    love.graphics.rectangle("line", x - width/2, y - height/2, width, height)
    
    -- Draw center cross
    love.graphics.line(x - 5, y, x + 5, y)
    love.graphics.line(x, y - 5, x, y + 5)
    
    -- Draw vertices if enabled
    if DebugOverlays.wireframe.showVertices then
        love.graphics.circle("fill", x - width/2, y - height/2, 2)
        love.graphics.circle("fill", x + width/2, y - height/2, 2)
        love.graphics.circle("fill", x + width/2, y + height/2, 2)
        love.graphics.circle("fill", x - width/2, y + height/2, 2)
    end
end

-- Draw structure wireframe
function DebugOverlays.drawStructureWireframe(structure)
    if not structure.position then return end
    
    local x = structure.position.x
    local y = structure.position.y
    local width = structure.width or 32
    local height = structure.height or 32
    
    love.graphics.rectangle("line", x - width/2, y - height/2, width, height)
    
    if DebugOverlays.wireframe.showVertices then
        love.graphics.circle("fill", x - width/2, y - height/2, 2)
        love.graphics.circle("fill", x + width/2, y - height/2, 2)
        love.graphics.circle("fill", x + width/2, y + height/2, 2)
        love.graphics.circle("fill", x - width/2, y + height/2, 2)
    end
end

-- Draw deadzone/grid
function DebugOverlays.drawDeadzone(player, camera)
    if not DebugOverlays.deadzone.enabled then return end
    
    love.graphics.push()
    
    -- Draw grid
    love.graphics.setColor(DebugOverlays.deadzone.gridColor)
    love.graphics.setLineWidth(DebugOverlays.deadzone.lineWidth)
    
    local gridSize = DebugOverlays.deadzone.gridSize
    local startX, startY, endX, endY = DebugOverlays.getVisibleBounds(camera, gridSize)
    
    -- Draw grid lines
    for x = startX, endX, gridSize do
        love.graphics.line(x, startY, x, endY)
    end
    
    for y = startY, endY, gridSize do
        love.graphics.line(startX, y, endX, y)
    end
    
    -- Draw player deadzone if enabled
    if DebugOverlays.deadzone.showPlayerDeadzone and player and player.position then
        love.graphics.setColor(DebugOverlays.deadzone.deadzoneColor)
        local deadzoneSize = 64 -- Configurable deadzone size
        love.graphics.rectangle("fill", 
            player.position.x - deadzoneSize/2, 
            player.position.y - deadzoneSize/2, 
            deadzoneSize, deadzoneSize)
    end
    
    love.graphics.pop()
end

-- Draw FPS counter
function DebugOverlays.drawFPS()
    if not DebugOverlays.fps.enabled then return end
    
    love.graphics.push()
    
    local fps = love.timer.getFPS()
    local x = DebugOverlays.fps.position.x
    local y = DebugOverlays.fps.position.y
    
    -- Draw background
    love.graphics.setColor(DebugOverlays.fps.backgroundColor)
    local width = DebugOverlays.fps.showDetailed and 200 or 80
    local height = DebugOverlays.fps.showDetailed and 80 or 25
    love.graphics.rectangle("fill", x, y, width, height)
    
    -- Draw text
    love.graphics.setColor(DebugOverlays.fps.textColor)
    love.graphics.print("FPS: " .. fps, x + 5, y + 5)
    
    if DebugOverlays.fps.showDetailed then
        local dt = love.timer.getDelta()
        local memory = collectgarbage("count") / 1024
        
        love.graphics.print(string.format("Frame Time: %.2fms", dt * 1000), x + 5, y + 20)
        love.graphics.print(string.format("Memory: %.1fMB", memory), x + 5, y + 35)
        
        -- Performance indicator
        local color = fps >= 60 and {0, 1, 0, 1} or (fps >= 30 and {1, 1, 0, 1} or {1, 0, 0, 1})
        love.graphics.setColor(color)
        love.graphics.print("●", x + 5, y + 50)
    end
    
    love.graphics.pop()
end

-- Get visible bounds for drawing optimizations
function DebugOverlays.getVisibleBounds(camera, gridSize)
    local screenWidth = love.graphics.getWidth()
    local screenHeight = love.graphics.getHeight()
    
    local startX, startY, endX, endY
    
    if camera then
        startX = camera.x - screenWidth/2
        startY = camera.y - screenHeight/2
        endX = camera.x + screenWidth/2
        endY = camera.y + screenHeight/2
    else
        startX, startY = 0, 0
        endX, endY = screenWidth, screenHeight
    end
    
    -- Snap to grid
    if gridSize then
        startX = math.floor(startX / gridSize) * gridSize
        startY = math.floor(startY / gridSize) * gridSize
        endX = math.ceil(endX / gridSize) * gridSize
        endY = math.ceil(endY / gridSize) * gridSize
    end
    
    return startX, startY, endX, endY
end

-- Toggle functions
function DebugOverlays.toggleChunkBoundaries()
    DebugOverlays.chunkBoundaries.enabled = not DebugOverlays.chunkBoundaries.enabled
    return DebugOverlays.chunkBoundaries.enabled
end

function DebugOverlays.toggleCollisionBoxes()
    DebugOverlays.collisionBoxes.enabled = not DebugOverlays.collisionBoxes.enabled
    return DebugOverlays.collisionBoxes.enabled
end

function DebugOverlays.toggleWireframe()
    DebugOverlays.wireframe.enabled = not DebugOverlays.wireframe.enabled
    return DebugOverlays.wireframe.enabled
end

function DebugOverlays.toggleDeadzone()
    DebugOverlays.deadzone.enabled = not DebugOverlays.deadzone.enabled
    return DebugOverlays.deadzone.enabled
end

function DebugOverlays.toggleFPS()
    DebugOverlays.fps.enabled = not DebugOverlays.fps.enabled
    return DebugOverlays.fps.enabled
end

return DebugOverlays
