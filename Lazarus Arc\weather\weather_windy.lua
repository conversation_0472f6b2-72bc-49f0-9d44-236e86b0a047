-- Weather type: Windy
-- Adds strong wind effects to the game

local WindyWeather = {
    id = "weather_windy",
    name = "Windy",
    description = "Strong winds blow across the landscape",

    -- Visual settings
    visual = {
        ambientLightLevel = 0.9,  -- Slightly dimmer than clear weather
        cloudMovementSpeed = 0.3, -- Faster cloud movement
        windEffectStrength = 0.8  -- Strong wind visual effects
    },

    -- Sound effects
    sounds = {
        ambient = "wind_ambient",
        gust = "wind_gust"
    },

    -- Synth orchestra sound configurations
    synthSounds = {
        wind_ambient = {
            notes = {330, 247, 185, 138}, -- E4, B3, F#3, C#3 - flowing wind harmony
            durations = {3.0, 3.0, 3.0, 3.0},
            instrument = "flute",
            volume = 0.25,
            vibrato = true,
            vibratoRate = 0.3,
            vibratoDepth = 0.2
        },
        wind_gust = {
            notes = {440, 330, 220, 165}, -- A4, E4, A3, E3 - sudden gust
            durations = {0.8, 0.6, 0.4, 0.3},
            instrument = "accordion",
            volume = 0.4,
            vibrato = true,
            vibratoRate = 1.5,
            vibratoDepth = 0.3
        }
    },

    -- Particle effects
    particles = {
        wind = {
            intensity = 1.0,
            color = {0.9, 0.9, 0.9, 0.5},
            size = {min = 1, max = 3},
            speed = {min = 100, max = 200}
        },
        debris = {
            intensity = 0.7,
            color = {0.8, 0.8, 0.7, 0.6},
            size = {min = 2, max = 5},
            speed = {min = 80, max = 150}
        }
    },

    -- Environment effects
    environment = {
        windStrength = 0.9,       -- Strong wind
        visibility = 0.9,         -- Slightly reduced visibility
        temperature = 0.0,        -- No temperature effect
        humidity = 0.3,           -- Low humidity
        precipitation = 0.0,      -- No precipitation
        cloudCover = 0.4          -- Moderate cloud cover
    },

    -- Time of day modifiers
    timeModifiers = {
        day = {
            skyColor = {0.6, 0.7, 0.9},
            ambientLightLevel = 0.9,
            windIntensity = 1.0
        },
        night = {
            skyColor = {0.1, 0.15, 0.3},
            ambientLightLevel = 0.4,
            windIntensity = 0.8
        },
        dawn = {
            skyColor = {0.8, 0.7, 0.6},
            ambientLightLevel = 0.7,
            windIntensity = 0.9
        },
        dusk = {
            skyColor = {0.7, 0.5, 0.6},
            ambientLightLevel = 0.6,
            windIntensity = 0.9
        }
    },

    -- Weather transitions
    transitions = {
        -- Weather types that can naturally follow this one
        nextWeather = {"clear", "cloudy", "light_rain", "overcast"},
        -- Probabilities for each transition (must sum to 1.0)
        probabilities = {0.4, 0.3, 0.2, 0.1},
        -- Weather types that can naturally precede this one
        prevWeather = {"clear", "cloudy", "light_rain", "overcast"},
        -- Duration range in seconds
        duration = {min = 180, max = 600}
    }
}

-- Initialize the weather pattern
function WindyWeather.init(world)
    if not world then
        print("Warning: world parameter is nil in WindyWeather.init")
        return
    end

    print("Initializing windy weather")

    -- Set global light level
    world.lightLevel = WindyWeather.visual.ambientLightLevel

    -- Start ambient sounds if weather system exists
    if world.weatherSystem then
        if WindyWeather.sounds.ambient then
            world.weatherSystem:playSound(WindyWeather.sounds.ambient, {
                verbose = false,  -- Don't print warnings if sound is missing
                loop = true,      -- Ambient sounds should loop
                volume = 0.5      -- Medium volume for wind
            })
        end
    end

    -- Create wind particle image programmatically
    local windParticleCanvas = love.graphics.newCanvas(8, 2)
    love.graphics.setCanvas(windParticleCanvas)
    love.graphics.clear(0, 0, 0, 0)
    love.graphics.setColor(1, 1, 1, 0.7)
    love.graphics.rectangle("fill", 0, 0, 8, 2)
    love.graphics.setCanvas()

    -- Initialize wind particle system with the canvas
    WindyWeather.windSystem = love.graphics.newParticleSystem(
        windParticleCanvas,
        1000
    )

    -- Configure wind particle system
    if WindyWeather.windSystem then
        WindyWeather.windSystem:setParticleLifetime(1, 3)
        WindyWeather.windSystem:setEmissionRate(100)
        WindyWeather.windSystem:setSizeVariation(0.5)
        WindyWeather.windSystem:setLinearAcceleration(100, -10, 200, 10)
        WindyWeather.windSystem:setColors(
            0.9, 0.9, 0.9, 0.1,
            0.9, 0.9, 0.9, 0.3,
            0.9, 0.9, 0.9, 0.1
        )
    end

    -- Create debris particle image programmatically
    local debrisParticleCanvas = love.graphics.newCanvas(6, 6)
    love.graphics.setCanvas(debrisParticleCanvas)
    love.graphics.clear(0, 0, 0, 0)
    love.graphics.setColor(0.8, 0.7, 0.6, 0.8)
    love.graphics.rectangle("fill", 1, 1, 4, 4)
    love.graphics.setCanvas()

    -- Initialize debris particle system with the canvas
    WindyWeather.debrisSystem = love.graphics.newParticleSystem(
        debrisParticleCanvas,
        500
    )

    -- Configure debris particle system
    if WindyWeather.debrisSystem then
        WindyWeather.debrisSystem:setParticleLifetime(2, 5)
        WindyWeather.debrisSystem:setEmissionRate(30)
        WindyWeather.debrisSystem:setSizeVariation(0.7)
        WindyWeather.debrisSystem:setLinearAcceleration(80, -20, 150, 20)
        WindyWeather.debrisSystem:setColors(
            0.8, 0.8, 0.7, 0.2,
            0.8, 0.8, 0.7, 0.4,
            0.8, 0.8, 0.7, 0.1
        )
    end
end

-- Update function called every frame
function WindyWeather.update(world, dt)
    if not world then
        print("Warning: world parameter is nil in WindyWeather.update")
        return
    end

    -- Update day/night cycle based on world time
    local timeOfDay = world.timeOfDay or "day"
    local modifiers = WindyWeather.timeModifiers[timeOfDay]

    if modifiers then
        -- Apply time-specific modifiers
        if modifiers.skyColor then
            -- Change sky color
        end

        if modifiers.ambientLightLevel then
            world.lightLevel = modifiers.ambientLightLevel
        end

        if modifiers.windIntensity then
            WindyWeather.particles.wind.intensity = modifiers.windIntensity
            if WindyWeather.windSystem then
                WindyWeather.windSystem:setEmissionRate(100 * modifiers.windIntensity)
            end
            if WindyWeather.debrisSystem then
                WindyWeather.debrisSystem:setEmissionRate(30 * modifiers.windIntensity)
            end
        end
    end

    -- Update particle systems
    if WindyWeather.windSystem then
        WindyWeather.windSystem:update(dt)
    end

    if WindyWeather.debrisSystem then
        WindyWeather.debrisSystem:update(dt)
    end

    -- Random wind gust effects
    if math.random() < 0.02 then
        -- Increase wind strength temporarily
        WindyWeather.environment.windStrength = math.min(1.0, WindyWeather.environment.windStrength + 0.2)

        -- Play gust sound
        if world.weatherSystem and WindyWeather.sounds.gust then
            world.weatherSystem:playSound(WindyWeather.sounds.gust, {
                verbose = false,
                volume = 0.6 + math.random() * 0.3
            })
        end

        -- Gradually return to normal wind strength
        WindyWeather.environment.windStrength = math.max(0.7,
            WindyWeather.environment.windStrength - 0.05)
    end

    -- Apply weather effects if weather system exists and has applyEffects method
    if world.weatherSystem then
        if type(world.weatherSystem.applyEffects) == "function" then
            world.weatherSystem:applyEffects(WindyWeather.environment)
        elseif type(Engine) == "table" and Engine.systems and Engine.systems.weatherSystem and
               type(Engine.systems.weatherSystem.applyEffects) == "function" then
            -- Use global weather system as fallback
            Engine.systems.weatherSystem.applyEffects(WindyWeather.environment)
        end
    end
end

-- Draw function for rendering weather effects
function WindyWeather.draw()
    -- Draw wind particles
    if WindyWeather.windSystem then
        love.graphics.setColor(1, 1, 1, 0.5)
        love.graphics.draw(WindyWeather.windSystem)
    end

    -- Draw debris particles
    if WindyWeather.debrisSystem then
        love.graphics.setColor(1, 1, 1, 0.7)
        love.graphics.draw(WindyWeather.debrisSystem)
    end

    -- Reset color
    love.graphics.setColor(1, 1, 1, 1)
end

-- Clean up function when weather changes
function WindyWeather.cleanUp(world)
    -- Stop ambient sounds
    if world.weatherSystem then
        if WindyWeather.sounds.ambient then
            world.weatherSystem:stopSound(WindyWeather.sounds.ambient)
        end
    end

    -- Clean up particle systems
    if WindyWeather.windSystem then
        WindyWeather.windSystem:reset()
    end

    if WindyWeather.debrisSystem then
        WindyWeather.debrisSystem:reset()
    end
end

return WindyWeather
