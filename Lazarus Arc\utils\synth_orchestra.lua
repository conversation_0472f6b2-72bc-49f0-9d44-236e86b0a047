-- utils/synth_orchestra.lua
-- Multi-instrument synthesizer system for diverse entity sounds
-- Provides piano, drums, violin, brass, woodwinds, and more for rich audio variety

local SynthOrchestra = {
    initialized = false,
    sampleRate = 22050,
    baseFrequency = 440, -- A4
    
    -- Instrument definitions with unique characteristics
    instruments = {
        -- GRAND_PIANO - Rich, resonant concert piano
        grand_piano = {
            attack = 0.03, decay = 0.4, sustain = 0.7, release = 0.6,
            harmonics = {{1.0, 1.0}, {2.0, 0.6}, {3.0, 0.35}, {4.0, 0.2}, {5.0, 0.1}},
            waveform = "sine",
            resonant = true
        },

        -- UPRIGHT_PIANO - Classic, warm home piano
        upright_piano = {
            attack = 0.05, decay = 0.3, sustain = 0.6, release = 0.4,
            harmonics = {{1.0, 1.0}, {2.0, 0.5}, {3.0, 0.25}, {4.0, 0.125}},
            waveform = "sine"
        },

        -- ELECTRIC_PIANO - Vintage electric piano (Rhodes/Wu<PERSON>itzer style)
        electric_piano = {
            attack = 0.02, decay = 0.25, sustain = 0.5, release = 0.3,
            harmonics = {{1.0, 1.0}, {2.0, 0.4}, {3.0, 0.6}, {4.0, 0.2}},
            waveform = "sine",
            electric = true
        },

        -- ACOUSTIC_GUITAR - Warm, natural guitar
        acoustic_guitar = {
            attack = 0.01, decay = 0.3, sustain = 0.4, release = 0.5,
            harmonics = {{1.0, 1.0}, {2.0, 0.7}, {3.0, 0.5}, {4.0, 0.3}, {5.0, 0.2}},
            waveform = "triangle",
            stringResonance = true
        },

        -- ELECTRIC_GUITAR - Bright, sustained electric guitar
        electric_guitar = {
            attack = 0.02, decay = 0.2, sustain = 0.8, release = 0.4,
            harmonics = {{1.0, 1.0}, {2.0, 0.8}, {3.0, 0.6}, {4.0, 0.4}, {5.0, 0.3}},
            waveform = "sawtooth",
            electric = true,
            distortion = true
        },

        -- BASS_GUITAR - Deep, punchy bass guitar
        bass_guitar = {
            attack = 0.03, decay = 0.25, sustain = 0.7, release = 0.3,
            harmonics = {{1.0, 1.0}, {2.0, 0.4}, {3.0, 0.2}, {4.0, 0.1}},
            waveform = "square",
            electric = true
        },

        -- CLASSICAL_GUITAR - Nylon string, gentle
        classical_guitar = {
            attack = 0.01, decay = 0.35, sustain = 0.3, release = 0.6,
            harmonics = {{1.0, 1.0}, {2.0, 0.5}, {3.0, 0.3}, {4.0, 0.15}},
            waveform = "sine",
            nylonStrings = true
        },

        -- XYLOPHONE - Bright, percussive wooden mallet sounds
        xylophone = {
            attack = 0.01, decay = 0.2, sustain = 0.1, release = 0.3,
            harmonics = {{1.0, 1.0}, {3.0, 0.6}, {5.0, 0.4}, {7.0, 0.2}},
            waveform = "sine",
            metallic = true
        },

        -- TRUMPET - Bright, brassy, heroic tones
        trumpet = {
            attack = 0.08, decay = 0.15, sustain = 0.85, release = 0.25,
            harmonics = {{1.0, 1.0}, {2.0, 0.9}, {3.0, 0.7}, {4.0, 0.5}, {5.0, 0.3}},
            waveform = "square",
            naturalVibrato = true
        },

        -- CELLO - Deep, rich string tones
        cello = {
            attack = 0.15, decay = 0.1, sustain = 0.9, release = 0.4,
            harmonics = {{1.0, 1.0}, {2.0, 0.7}, {3.0, 0.5}, {4.0, 0.3}},
            waveform = "sawtooth",
            naturalVibrato = true
        },

        -- CLARINET - Smooth, woody wind instrument
        clarinet = {
            attack = 0.12, decay = 0.08, sustain = 0.8, release = 0.3,
            harmonics = {{1.0, 1.0}, {3.0, 0.4}, {5.0, 0.2}}, -- Odd harmonics
            waveform = "square",
            breathNoise = true
        },

        -- MARIMBA - Warm, wooden percussion
        marimba = {
            attack = 0.02, decay = 0.3, sustain = 0.2, release = 0.5,
            harmonics = {{1.0, 1.0}, {2.4, 0.5}, {4.2, 0.3}, {6.8, 0.15}},
            waveform = "sine"
        },

        -- SAXOPHONE - Smooth, jazzy reed instrument
        saxophone = {
            attack = 0.1, decay = 0.12, sustain = 0.8, release = 0.35,
            harmonics = {{1.0, 1.0}, {2.0, 0.6}, {3.0, 0.8}, {4.0, 0.4}},
            waveform = "sawtooth",
            breathNoise = true
        },

        -- BANJO - Twangy, plucked string instrument
        banjo = {
            attack = 0.005, decay = 0.25, sustain = 0.15, release = 0.4,
            harmonics = {{1.0, 1.0}, {2.0, 0.7}, {3.0, 0.5}, {4.0, 0.3}},
            waveform = "triangle",
            twang = true
        },

        -- ACCORDION - Reedy, folk instrument with vibrato
        accordion = {
            attack = 0.2, decay = 0.1, sustain = 0.9, release = 0.3,
            harmonics = {{1.0, 1.0}, {2.0, 0.5}, {3.0, 0.7}, {4.0, 0.3}},
            waveform = "square",
            naturalVibrato = true,
            breathNoise = true
        },

        -- VIBRAPHONE - Metallic, mellow mallet percussion
        vibraphone = {
            attack = 0.02, decay = 0.4, sustain = 0.6, release = 1.0,
            harmonics = {{1.0, 1.0}, {2.4, 0.4}, {3.8, 0.3}, {5.2, 0.2}},
            waveform = "sine",
            naturalVibrato = true,
            metallic = true
        },

        -- HARMONICA - Small, breathy reed instrument
        harmonica = {
            attack = 0.05, decay = 0.05, sustain = 0.8, release = 0.2,
            harmonics = {{1.0, 1.0}, {2.0, 0.4}, {3.0, 0.6}, {4.0, 0.2}},
            waveform = "square",
            breathNoise = true
        },

        -- DULCIMER - Plucked/hammered string instrument
        dulcimer = {
            attack = 0.01, decay = 0.35, sustain = 0.3, release = 0.6,
            harmonics = {{1.0, 1.0}, {2.0, 0.6}, {3.0, 0.4}, {4.0, 0.25}},
            waveform = "triangle"
        },

        -- OCARINA - Ceramic wind instrument, mystical
        ocarina = {
            attack = 0.08, decay = 0.06, sustain = 0.75, release = 0.25,
            harmonics = {{1.0, 1.0}, {2.0, 0.3}, {3.0, 0.15}},
            waveform = "sine",
            breathNoise = true
        },

        -- KALIMBA - Thumb piano, metallic plucked tines
        kalimba = {
            attack = 0.005, decay = 0.4, sustain = 0.1, release = 0.8,
            harmonics = {{1.0, 1.0}, {2.7, 0.5}, {4.3, 0.3}, {6.1, 0.2}},
            waveform = "sine",
            metallic = true
        },

        -- DIDGERIDOO - Deep, droning wind instrument
        didgeridoo = {
            attack = 0.3, decay = 0.1, sustain = 0.95, release = 0.4,
            harmonics = {{1.0, 1.0}, {2.0, 0.2}, {3.0, 0.1}},
            waveform = "square",
            breathNoise = true
        },

        -- STEEL_DRUM - Caribbean percussion, metallic
        steel_drum = {
            attack = 0.01, decay = 0.3, sustain = 0.4, release = 0.6,
            harmonics = {{1.0, 1.0}, {2.3, 0.7}, {3.7, 0.5}, {5.1, 0.3}},
            waveform = "sine",
            metallic = true
        }
    },
    
    -- Entity mappings removed - now handled by individual entity files and sound_config.lua

        -- Predators - powerful, deep instruments
        wolf = "bass_guitar",
        fox = "electric_guitar",
        bear = "bass_guitar",
        raccoon = "electric_piano",

        -- Aquatic - flowing, resonant instruments
        fish = "dulcimer",
        bass = "bass_guitar",
        trout = "classical_guitar",
        crab = "steel_drum",
        turtle = "upright_piano",
        frog = "kalimba",
        dolphin = "electric_piano",
        whale = "bass_guitar",
        shark = "electric_guitar",

        -- Magical/Boss - mystical instruments
        ancient_treant = "grand_piano",
        forest_golem = "bass_guitar",
        mystic_deer = "vibraphone",
        mimic = "electric_piano",

        -- Default fallbacks

    
    -- Note frequencies cache
    noteFrequencies = {},
    
    -- Sound cache
    soundCache = {},
    
    -- Octave ranges
    octaveRanges = {
        tiny = 3,      -- Very small creatures
        small = 2,     -- Small creatures  
        medium = 1,    -- Medium creatures
        large = 0,     -- Large creatures
        huge = -1,     -- Huge creatures
        bass = -2      -- Massive creatures
    }
}

-- Initialize the synth orchestra system
function SynthOrchestra.init()
    if SynthOrchestra.initialized then return SynthOrchestra end
    
    print("Initializing Synth Orchestra system...")
    
    -- Generate note frequencies
    SynthOrchestra.generateNoteFrequencies()
    
    SynthOrchestra.initialized = true
    print("Synth Orchestra system initialized with " .. 
          SynthOrchestra.getInstrumentCount() .. " instruments")
    return SynthOrchestra
end

-- Generate note frequencies for 12-tone equal temperament
function SynthOrchestra.generateNoteFrequencies()
    local notes = {"C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"}
    
    for octave = -3, 4 do
        SynthOrchestra.noteFrequencies[octave] = {}
        for i, note in ipairs(notes) do
            local semitonesFromA4 = (octave * 12) + (i - 10)
            local frequency = SynthOrchestra.baseFrequency * math.pow(2, semitonesFromA4 / 12)
            SynthOrchestra.noteFrequencies[octave][note] = frequency
        end
    end
end

-- Get instrument count
function SynthOrchestra.getInstrumentCount()
    local count = 0
    for _ in pairs(SynthOrchestra.instruments) do
        count = count + 1
    end
    return count
end

-- Determine octave based on entity size (utility function)
function SynthOrchestra.getOctaveForSize(entitySize)
    if entitySize <= 3 then
        return SynthOrchestra.octaveRanges.tiny
    elseif entitySize <= 6 then
        return SynthOrchestra.octaveRanges.small
    elseif entitySize <= 10 then
        return SynthOrchestra.octaveRanges.medium
    elseif entitySize <= 15 then
        return SynthOrchestra.octaveRanges.large
    elseif entitySize <= 20 then
        return SynthOrchestra.octaveRanges.huge
    else
        return SynthOrchestra.octaveRanges.bass
    end
end

-- Generate different waveforms
function SynthOrchestra.generateWaveform(waveform, frequency, t, options)
    options = options or {}

    if waveform == "sine" then
        return math.sin(2 * math.pi * frequency * t)
    elseif waveform == "square" then
        return math.sin(2 * math.pi * frequency * t) > 0 and 1 or -1
    elseif waveform == "sawtooth" then
        return 2 * (frequency * t - math.floor(frequency * t + 0.5))
    elseif waveform == "triangle" then
        local phase = frequency * t - math.floor(frequency * t)
        return phase < 0.5 and (4 * phase - 1) or (3 - 4 * phase)
    elseif waveform == "noise" then
        return (math.random() * 2 - 1) * 0.3 -- Scaled noise
    else
        return math.sin(2 * math.pi * frequency * t) -- Default to sine
    end
end

-- Generate a synthesized tone with specified instrument
function SynthOrchestra.generateTone(frequency, duration, instrumentName, options)
    if not SynthOrchestra.initialized then SynthOrchestra.init() end

    options = options or {}
    local instrument = SynthOrchestra.instruments[instrumentName] or SynthOrchestra.instruments.upright_piano

    -- Override instrument settings with options
    local attack = options.attack or instrument.attack
    local decay = options.decay or instrument.decay
    local sustain = options.sustain or instrument.sustain
    local release = options.release or instrument.release
    local volume = options.volume or 0.3
    local vibrato = options.vibrato or instrument.naturalVibrato or false
    local vibratoRate = options.vibratoRate or 5.0
    local vibratoDepth = options.vibratoDepth or 0.02

    -- Calculate sample count
    local sampleCount = math.floor(duration * SynthOrchestra.sampleRate)
    local samples = {}

    -- Generate samples
    for i = 0, sampleCount - 1 do
        local t = i / SynthOrchestra.sampleRate
        local sample = 0

        -- Apply vibrato if enabled
        local vibratoMod = 1.0
        if vibrato then
            vibratoMod = 1.0 + vibratoDepth * math.sin(2 * math.pi * vibratoRate * t)
        end

        local modFreq = frequency * vibratoMod

        -- Generate harmonics with instrument-specific waveform
        for _, harmonic in ipairs(instrument.harmonics) do
            local harmFreq = modFreq * harmonic[1]
            local harmAmp = harmonic[2]

            local waveValue = SynthOrchestra.generateWaveform(instrument.waveform, harmFreq, t, options)
            sample = sample + harmAmp * waveValue
        end

        -- Add special effects for certain instruments
        if instrument.breathNoise then
            sample = sample + (math.random() * 2 - 1) * 0.05 -- Subtle breath noise
        end

        if instrument.useNoise then
            sample = sample * 0.7 + (math.random() * 2 - 1) * 0.3 -- Mix with noise
        end

        if instrument.metallic then
            -- Add metallic shimmer with high frequency content
            sample = sample + 0.1 * math.sin(2 * math.pi * modFreq * 8 * t) * envelope
        end

        if instrument.twang then
            -- Add banjo-like twang effect
            local twangDecay = math.exp(-t * 15)
            sample = sample + 0.2 * math.sin(2 * math.pi * modFreq * 2.1 * t) * twangDecay
        end

        if instrument.electric then
            -- Add electric instrument characteristics
            sample = sample * (1 + 0.1 * math.sin(2 * math.pi * modFreq * 0.5 * t)) -- Slight modulation
        end

        if instrument.distortion then
            -- Add guitar distortion effect
            sample = math.tanh(sample * 2) * 0.7 -- Soft clipping distortion
        end

        if instrument.stringResonance then
            -- Add acoustic guitar string resonance
            sample = sample + 0.05 * math.sin(2 * math.pi * modFreq * 1.5 * t) * envelope
        end

        if instrument.nylonStrings then
            -- Add classical guitar nylon string warmth
            sample = sample * (1 - 0.1 * math.sin(2 * math.pi * modFreq * 3 * t) * envelope)
        end

        if instrument.resonant then
            -- Add grand piano resonance
            sample = sample + 0.08 * math.sin(2 * math.pi * modFreq * 0.25 * t) * envelope
        end

        -- Apply ADSR envelope
        local envelope = 1.0
        if t < attack then
            envelope = t / attack
        elseif t < attack + decay then
            local decayProgress = (t - attack) / decay
            envelope = 1.0 - (1.0 - sustain) * decayProgress
        elseif t < duration - release then
            envelope = sustain
        else
            local releaseProgress = (t - (duration - release)) / release
            envelope = sustain * (1.0 - releaseProgress)
        end

        -- Apply envelope and volume
        sample = sample * envelope * volume

        -- Clamp to prevent clipping
        sample = math.max(-1.0, math.min(1.0, sample))
        samples[i + 1] = sample
    end

    return samples
end

-- Create a LÖVE audio source from samples
function SynthOrchestra.createAudioSource(samples)
    if not love.audio or not love.sound then return nil end

    local soundData = love.sound.newSoundData(#samples, SynthOrchestra.sampleRate, 16, 1)

    for i = 1, #samples do
        soundData:setSample(i - 1, samples[i])
    end

    return love.audio.newSource(soundData)
end

-- Generate a sequence of notes (for complex creature sounds)
function SynthOrchestra.generateNoteSequence(frequencies, durations, instrumentName, options)
    if not SynthOrchestra.initialized then SynthOrchestra.init() end

    options = options or {}

    if #frequencies == 0 then
        return nil
    end

    -- For single note, use existing generateTone function
    if #frequencies == 1 then
        local samples = SynthOrchestra.generateTone(frequencies[1], durations[1] or 0.5, instrumentName, options)
        return SynthOrchestra.createAudioSource(samples)
    end

    -- For multiple notes, concatenate them
    local allSamples = {}
    local totalDuration = 0

    for i, frequency in ipairs(frequencies) do
        local duration = durations[i] or 0.5
        local samples = SynthOrchestra.generateTone(frequency, duration, instrumentName, options)

        -- Add samples to the sequence
        for _, sample in ipairs(samples) do
            table.insert(allSamples, sample)
        end

        totalDuration = totalDuration + duration
    end

    return SynthOrchestra.createAudioSource(allSamples)
end

-- Generate sound from configuration (used by sound system)
function SynthOrchestra.generateFromConfig(config)
    if not config or not config.notes or #config.notes == 0 then
        return nil
    end

    return SynthOrchestra.generateNoteSequence(
        config.notes,
        config.durations,
        config.instrument or "upright_piano",
        {
            volume = config.volume or 0.3,
            vibrato = config.vibrato,
            vibratoRate = config.vibratoRate,
            vibratoDepth = config.vibratoDepth
        }
    )
end

-- Cache management for generated sounds
function SynthOrchestra.getCacheKey(instrument, frequencies, durations, options)
    options = options or {}
    local freqStr = table.concat(frequencies, ",")
    local durStr = table.concat(durations, ",")
    return string.format("%s_%s_%s_%.1f",
        instrument or "upright_piano",
        freqStr,
        durStr,
        options.volume or 0.3
    )
end

function SynthOrchestra.clearCache()
    SynthOrchestra.soundCache = {}
    print("Synth Orchestra cache cleared")
end

function SynthOrchestra.getCacheStats()
    local count = 0
    for _ in pairs(SynthOrchestra.soundCache) do
        count = count + 1
    end
    return {
        cachedSounds = count,
        instruments = SynthOrchestra.getInstrumentCount(),
        memoryEstimate = count * 0.7 .. " KB"
    }
end

-- Get list of all available instruments
function SynthOrchestra.getInstrumentList()
    local instruments = {}
    for name, _ in pairs(SynthOrchestra.instruments) do
        table.insert(instruments, name)
    end
    table.sort(instruments)
    return instruments
end

-- Get instrument information
function SynthOrchestra.getInstrumentInfo(instrumentName)
    return SynthOrchestra.instruments[instrumentName]
end

return SynthOrchestra
