local Angelfish = {
    id = "angelfish",
    name = "Angelfish",
    type = "angelfish",

    -- Entity categories
    categories = { "animal", "fish", "medium", "decorative" },

    -- Threat categories
    threatCategories = { "predator", "large" },
    foodCategories = { "insect", "small_fish", "crustacean" },

    -- Stats
    maxHealth = 15,
    health = 15,
    speed = 3.0,

    -- Behaviors
    behaviors = { "flee", "Foraging" }, -- No schooling

    -- Behavior configurations
    behaviorConfigs = {
        flee = {
            useCategories = true,
            moveSpeed = 5.0,
            detectionRadius = 7
        },
        觅食 = {
            foodTypes = { "insect", "small_fish", "crustacean" },
            foodRadius = 5
        }
    },

    -- Appearance
    appearance = {
        sprite = "angelfish", -- Replace with your angelfish sprite
        scale = 1.0,
        animations = {
            "swim",
            "flee",
            "eat"
        },
        -- Variety of colors and patterns
        variants = {
            { color = "silver", pattern = "striped" },
            { color = "black", pattern = "marble" },
            { color = "gold", pattern = "veil" }
        }
    },

    -- Sound effects
    sounds = {
        -- May not have distinct sounds
    }
}

-- Initialize the angelfish entity
function Angelfish.init(entity, world)
    entity.position = entity.position or { x = 0, y = 0 }
    entity.velocity = entity.velocity or { x = 0, y = 0 }

    -- Initialize behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.init then
            behavior.init(entity, entity.behaviorConfigs[behaviorName])
        end
    end

    return entity
end

return Angelfish