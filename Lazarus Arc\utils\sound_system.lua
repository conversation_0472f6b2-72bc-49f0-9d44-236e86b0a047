-- sound_system.lua
-- Sound system with procedural generation for Lazarus Arc

local SynthOrchestra = require("utils.synth_orchestra")
local SoundConfig = require("utils.sound_config")

local SoundSystem = {
    initialized = false,
    enabled = true,
    musicVolume = 0.7,
    sfxVolume = 0.8,
    currentMusic = nil,
    sounds = {}, -- Loaded sound effects
    activeSources = {}, -- Currently active sound sources
    soundBaseDir = "sounds/", -- Base directory for sound files
    musicBaseDir = "music/", -- Base directory for music files
    maxConcurrentSounds = 16, -- Maximum number of sounds playing at once
    currentSoundCount = 0,
    pitchVariationSeeds = {}, -- Persistent seeds for procedural generation
    useSynthOrchestra = true -- Use synth orchestra for entity sounds
}

-- Initialize the sound system
function SoundSystem.init(settings)
    print("Initializing sound system...")

    -- Apply settings if provided
    if settings then
        if settings.musicVolume ~= nil then SoundSystem.musicVolume = settings.musicVolume end
        if settings.sfxVolume ~= nil then SoundSystem.sfxVolume = settings.sfxVolume end
        if settings.enabled ~= nil then SoundSystem.enabled = settings.enabled end
        if settings.useSynthOrchestra ~= nil then SoundSystem.useSynthOrchestra = settings.useSynthOrchestra end
    end

    -- Check if LÖVE audio is available
    if not love.audio then
        print("Warning: LÖVE audio module not available. Sound disabled.")
        SoundSystem.enabled = false
        SoundSystem.initialized = true
        return SoundSystem
    end

    -- Initialize synth orchestra and sound config if enabled
    if SoundSystem.useSynthOrchestra then
        SynthOrchestra.init()
        SoundConfig.init()
        print("Synth Orchestra and Sound Configuration enabled")
    end

    -- Preload common sound effects
    SoundSystem.preloadCommonSounds()

    SoundSystem.initialized = true
    print("Sound system initialized")

    return SoundSystem
end

-- Preload common sound effects
function SoundSystem.preloadCommonSounds()
    -- List of common sounds to preload
    local commonSounds = {
        "ui_click",
        "ui_hover",
        "ui_error",
        "ui_success",
        "player_footstep",
        "player_attack",
        "player_hit",
        "pickup_item"
    }
    
    -- Try to load each sound
    for _, soundName in ipairs(commonSounds) do
        SoundSystem.loadSound(soundName)
    end
end

-- Play entity sound using synth orchestra or file
function SoundSystem.playEntitySound(entity, soundName, options)
    if not SoundSystem.initialized or not SoundSystem.enabled then
        return false
    end

    options = options or {}

    -- Use synth orchestra if enabled
    if SoundSystem.useSynthOrchestra then
        -- Get entity sound definition (from entity file)
        local entitySoundDef = nil
        if entity.sounds and entity.sounds[soundName] then
            entitySoundDef = entity.sounds[soundName]
        end

        -- Get sound configuration (checks JSON overrides, then entity definition)
        local soundConfig = SoundConfig.getSoundConfig(entity.type, soundName, entitySoundDef)

        if soundConfig then
            -- Generate synth sound from configuration
            local synthSource = SynthOrchestra.generateFromConfig(soundConfig)
            if synthSource then
                -- Add variation to synth sounds
                options.pitchVariation = options.pitchVariation or 0.1
                options.volumeVariation = options.volumeVariation or 0.1
                options.entityType = entity.type

                return SoundSystem.playSource(synthSource, options)
            end
        end
    end

    -- Fall back to file-based sound (check entity definition first)
    local soundFileName = soundName
    if entity.sounds and entity.sounds[soundName] then
        local entitySound = entity.sounds[soundName]
        if type(entitySound) == "string" then
            soundFileName = entitySound
        elseif type(entitySound) == "table" and entitySound.file then
            soundFileName = entitySound.file
        end
    end

    return SoundSystem.playSound(soundFileName, options)
end

-- Load a sound from file
function SoundSystem.loadSound(soundName)
    if not SoundSystem.initialized or not SoundSystem.enabled then
        return nil
    end

    -- Check if already loaded
    if SoundSystem.sounds[soundName] then
        return SoundSystem.sounds[soundName]
    end

    -- Try different file extensions
    local extensions = {".wav", ".ogg", ".mp3"}
    local source = nil

    for _, ext in ipairs(extensions) do
        local filePath = SoundSystem.soundBaseDir .. soundName .. ext
        if love.filesystem.getInfo(filePath) then
            -- Try to load the file
            local success, result = pcall(function()
                return love.audio.newSource(filePath, "static")
            end)

            if success and result then
                source = result
                break
            end
        end
    end
    
    if source then
        SoundSystem.sounds[soundName] = source
        return source
    else
        print("Warning: Could not load sound '" .. soundName .. "'")
        return nil
    end
end

-- Update sound system (to be called each frame)
function SoundSystem.update(dt)
    if not SoundSystem.initialized or not SoundSystem.enabled then
        return
    end
    
    -- Count active sounds
    SoundSystem.currentSoundCount = 0
    for _, sound in pairs(SoundSystem.sounds) do
        if sound:isPlaying() then
            SoundSystem.currentSoundCount = SoundSystem.currentSoundCount + 1
        end
    end
    
    -- Update music system if needed
end

-- Play a raw sound source
function SoundSystem.playSource(source, options)
    if not SoundSystem.initialized or not SoundSystem.enabled or not source then
        return false
    end
    
    options = options or {}
    
    -- If we're at max concurrent sounds, don't play non-important ones
    if SoundSystem.currentSoundCount >= SoundSystem.maxConcurrentSounds and not options.important then
        return false
    end
    
    -- Clone source so multiple instances can play simultaneously
    local playingSource = source:clone()
    
    -- Apply options
    local volume = (options.volume or 1.0) * SoundSystem.sfxVolume
    playingSource:setVolume(volume)
    
    if options.pitch then
        playingSource:setPitch(options.pitch)
    end
    
    if options.loop ~= nil then
        playingSource:setLooping(options.loop)
    end
    
    -- Apply position if given
    if options.position then
        playingSource:setPosition(
            options.position.x or 0,
            options.position.y or 0,
            options.position.z or 0
        )
    end
    
    -- Play the sound
    playingSource:play()
    
    -- Return the source for potential manipulation
    return playingSource
end

-- Play a sound with procedural variations
function SoundSystem.playSound(soundName, options)
    if not SoundSystem.initialized or not SoundSystem.enabled then
        return false
    end
    
    -- Ensure options is a table
    if type(options) ~= "table" then
        local oldOptions = options
        options = {}
        if type(oldOptions) == "string" then
            options.legacy = oldOptions -- Store the original value just in case
        end
    end
    
    -- Apply default options
    options = options or {}
    options.volume = options.volume or SoundSystem.sfxVolume
    options.pitch = options.pitch or 1.0
    options.pitchVariation = options.pitchVariation or 0
    options.volumeVariation = options.volumeVariation or 0
    options.loop = options.loop or false
    options.maxDistance = options.maxDistance or 1000
    options.referenceDistance = options.referenceDistance or 300
    options.position = options.position or nil
    options.important = options.important or false
    options.group = options.group or nil
    
    -- Load or get the sound source
    local source = SoundSystem.loadSound(soundName)
    if not source then
        return false
    end
    
    -- Generate a seed for this sound if it doesn't have one
    if not SoundSystem.pitchVariationSeeds[soundName] then
        SoundSystem.pitchVariationSeeds[soundName] = math.random()
    end
    
    -- Get base options
    local seedValue = SoundSystem.pitchVariationSeeds[soundName]
    local entityType = options.entityType or "default"
    
    -- Calculate procedural variations
    local pitch = 1.0
    local volume = 1.0
    
    -- Apply pitch variation based on entity type
    if entityType == "small_creature" then
        pitch = 1.3 + seedValue * 0.2 -- Higher pitch for small creatures
        volume = 0.6 + seedValue * 0.2
    elseif entityType == "large_creature" then
        pitch = 0.7 - seedValue * 0.2 -- Lower pitch for large creatures
        volume = 0.8 + seedValue * 0.2
    elseif entityType == "player" then
        pitch = 1.0 + (seedValue - 0.5) * 0.2 -- Small variation around 1.0
        volume = 0.8 + seedValue * 0.1
    end
    
    -- Apply modifiers from options
    if options.pitch then
        pitch = pitch * options.pitch
    end
    
    if options.pitchVariation then
        -- Add controlled randomness
        pitch = pitch * (1 + (math.random() - 0.5) * options.pitchVariation)
    end
    
    if options.volume then
        volume = volume * options.volume
    end
    
    if options.volumeVariation then
        -- Add controlled randomness
        volume = volume * (1 + (math.random() - 0.5) * options.volumeVariation)
    end
    
    -- Apply material variations
    if options.material then
        if options.material == "metal" then
            pitch = pitch * 1.2
        elseif options.material == "wood" then
            pitch = pitch * 0.9
        elseif options.material == "stone" then
            pitch = pitch * 0.8
            volume = volume * 1.2
        end
    end
    
    -- Apply environment variations
    if options.environment then
        if options.environment == "cave" then
            -- Cave echo effect would be applied here
            volume = volume * 1.1
        elseif options.environment == "forest" then
            -- Forest dampening
            volume = volume * 0.9
        end
    end
    
    -- Ensure values are in reasonable ranges
    pitch = math.max(0.5, math.min(2.0, pitch))
    volume = math.max(0.1, math.min(1.0, volume))
    
    -- Safely play the sound with variations
    local success, playingSource = pcall(function()
        return SoundSystem.playSource(source, {
            pitch = pitch,
            volume = volume,
            loop = options.loop,
            important = options.important,
            position = options.position
        })
    end)
    
    if not success then
        print("Warning: Failed to play sound '" .. soundName .. "': " .. tostring(playingSource))
        return false
    end
    
    -- Track this source if successful
    if playingSource then
        table.insert(SoundSystem.activeSources, {
            name = soundName,
            source = playingSource
        })
    end
    
    return playingSource
end

-- Play a footstep sound with surface awareness
function SoundSystem.playFootstep(entityType, surfaceType, options)
    if not SoundSystem.initialized or not SoundSystem.enabled then
        return false
    end
    
    options = options or {}
    options.entityType = entityType
    
    -- Determine the specific sound to play
    local soundName = "footstep_" .. surfaceType
    if not SoundSystem.sounds[soundName] and not SoundSystem.loadSound(soundName) then
        -- Fall back to generic footstep
        soundName = "footstep"
    end
    
    -- Add some randomness to footsteps
    options.pitchVariation = options.pitchVariation or 0.1
    
    -- Play with the footstep specifics
    return SoundSystem.playSound(soundName, options)
end

-- Play a combat sound (attack, hit, block, etc)
function SoundSystem.playCombatSound(soundType, weaponType, options)
    if not SoundSystem.initialized or not SoundSystem.enabled then
        return false
    end
    
    options = options or {}
    
    -- Set combat sound characteristics
    local soundName
    if soundType == "attack" then
        soundName = weaponType .. "_attack"
    elseif soundType == "hit" then
        -- Determine hit sound based on target material
        local material = options.material or "flesh"
        soundName = "hit_" .. material
    elseif soundType == "block" then
        soundName = "block"
    else
        soundName = soundType -- Use directly if it's a special type
    end
    
    -- For weapon attacks, apply weapon-specific modifications
    if soundType == "attack" then
        if weaponType == "sword" then
            options.pitch = 1.0
            options.volume = 0.8
        elseif weaponType == "axe" then
            options.pitch = 0.9
            options.volume = 0.9
        elseif weaponType == "mace" or weaponType == "hammer" then
            options.pitch = 0.8
            options.volume = 1.0
        elseif weaponType == "dagger" then
            options.pitch = 1.2
            options.volume = 0.7
        elseif weaponType == "bow" then
            options.pitch = 1.1
            options.volume = 0.6
        elseif weaponType == "staff" then
            options.pitch = 1.0
            options.volume = 0.7
        end
    end
    
    -- Add variation for combat sounds
    options.pitchVariation = options.pitchVariation or 0.15
    options.volumeVariation = options.volumeVariation or 0.1
    
    -- Combat sounds should be important
    options.important = true
    
    return SoundSystem.playSound(soundName, options)
end

-- Play a magical sound (spell casting, impact, etc)
function SoundSystem.playMagicSound(spellType, elementType, options)
    if not SoundSystem.initialized or not SoundSystem.enabled then
        return false
    end
    
    options = options or {}
    
    local soundName
    if spellType == "cast" then
        soundName = "magic_cast_" .. elementType
    elseif spellType == "impact" then
        soundName = "magic_impact_" .. elementType
    elseif spellType == "channel" then
        soundName = "magic_channel_" .. elementType
    else
        soundName = "magic_" .. spellType
    end
    
    -- Element-specific variations
    if elementType == "fire" then
        options.pitch = 1.0
        options.volume = 0.9
    elseif elementType == "ice" or elementType == "water" then
        options.pitch = 1.1
        options.volume = 0.8
    elseif elementType == "lightning" then
        options.pitch = 1.2
        options.volume = 1.0
    elseif elementType == "earth" then
        options.pitch = 0.8
        options.volume = 0.9
    else
        -- Generic magic
        options.pitch = 1.0
        options.volume = 0.8
    end
    
    -- Add variation
    options.pitchVariation = options.pitchVariation or 0.2
    options.volumeVariation = options.volumeVariation or 0.15
    
    -- Magic sounds should be important
    options.important = true
    
    return SoundSystem.playSound(soundName, options)
end

-- Play a UI sound
function SoundSystem.playUISound(uiAction, options)
    if not SoundSystem.initialized or not SoundSystem.enabled then
        return false
    end
    
    options = options or {}
    
    -- UI sounds are always important and non-positional
    options.important = true
    
    -- Set default UI sound properties
    local soundName = "ui_" .. uiAction
    
    if uiAction == "click" then
        options.pitch = 1.0
        options.volume = 0.7
    elseif uiAction == "hover" then
        options.pitch = 1.1
        options.volume = 0.5
    elseif uiAction == "error" then
        options.pitch = 0.9
        options.volume = 0.8
    elseif uiAction == "success" then
        options.pitch = 1.0
        options.volume = 0.8
    else
        options.pitch = 1.0
        options.volume = 0.7
    end
    
    -- Less variation for UI sounds
    options.pitchVariation = options.pitchVariation or 0.05
    options.volumeVariation = options.volumeVariation or 0.05
    
    return SoundSystem.playSound(soundName, options)
end

-- Convenience functions for common UI sounds
function SoundSystem.playClick(options)
    return SoundSystem.playUISound("click", options)
end

function SoundSystem.playHover(options)
    return SoundSystem.playUISound("hover", options)
end

function SoundSystem.playError(options)
    return SoundSystem.playUISound("error", options)
end

function SoundSystem.playSuccess(options)
    return SoundSystem.playUISound("success", options)
end

-- Play music with fade
function SoundSystem.playMusic(musicName, loop, fadeDuration)
    if not SoundSystem.initialized or not SoundSystem.enabled then
        return false
    end
    
    -- Stop current music first
    if SoundSystem.currentMusic then
        SoundSystem.stopMusic(fadeDuration)
    end
    
    -- Try different file extensions
    local extensions = {".ogg", ".mp3", ".wav"}
    local source = nil
    
    for _, ext in ipairs(extensions) do
        local filePath = SoundSystem.musicBaseDir .. musicName .. ext
        if love.filesystem.getInfo(filePath) then
            -- Try to load the file
            local success, result = pcall(function()
                return love.audio.newSource(filePath, "stream")
            end)
            
            if success and result then
                source = result
                break
            end
        end
    end
    
    if not source then
        print("Warning: Could not load music '" .. musicName .. "'")
        return false
    end
    
    -- Set up music properties
    source:setVolume(SoundSystem.musicVolume)
    source:setLooping(loop or true)
    
    -- Store as current music
    SoundSystem.currentMusic = {
        source = source,
        name = musicName,
        fadingIn = fadeDuration and fadeDuration > 0,
        fadeDuration = fadeDuration or 0,
        fadeTimer = 0,
        targetVolume = SoundSystem.musicVolume
    }
    
    -- Start playing
    source:play()
    
    return true
end

-- Stop music with optional fade
function SoundSystem.stopMusic(fadeDuration)
    if not SoundSystem.currentMusic then
        return false
    end
    
    if not fadeDuration or fadeDuration <= 0 then
        -- Stop immediately
        SoundSystem.currentMusic.source:stop()
        SoundSystem.currentMusic = nil
    else
        -- Start fade out
        SoundSystem.currentMusic.fadingIn = false
        SoundSystem.currentMusic.fadeDuration = fadeDuration
        SoundSystem.currentMusic.fadeTimer = 0
    end
    
    return true
end

-- Set volume levels
function SoundSystem.setVolumes(musicVol, sfxVol)
    if musicVol ~= nil then
        SoundSystem.musicVolume = musicVol
        
        -- Update current music volume
        if SoundSystem.currentMusic and not SoundSystem.currentMusic.fadingIn then
            SoundSystem.currentMusic.source:setVolume(musicVol)
            SoundSystem.currentMusic.targetVolume = musicVol
        end
    end
    
    if sfxVol ~= nil then
        SoundSystem.sfxVolume = sfxVol
    end
end

-- Enable or disable sound system
function SoundSystem.setEnabled(enabled)
    SoundSystem.enabled = enabled
    
    -- If disabling, stop all sounds
    if not enabled then
        if SoundSystem.currentMusic then
            SoundSystem.currentMusic.source:stop()
        end
        
        love.audio.stop()
    end
end

-- Shutdown sound system
function SoundSystem.shutdown()
    -- Stop all sounds
    if SoundSystem.currentMusic then
        SoundSystem.currentMusic.source:stop()
        SoundSystem.currentMusic = nil
    end
    
    love.audio.stop()
    
    SoundSystem.sounds = {}
    SoundSystem.initialized = false
end

-- Stop a specific sound by name
function SoundSystem.stopSound(soundName)
    if not SoundSystem.initialized or not SoundSystem.enabled then
        return false
    end
    
    -- Find sound sources with this name
    for i, source in ipairs(SoundSystem.activeSources or {}) do
        if source.name == soundName then
            source.source:stop()
            table.remove(SoundSystem.activeSources, i)
            return true
        end
    end
    
    return false
end

-- Generate entity-specific sound ID
function SoundSystem.generateEntitySoundID(entityType, entitySize, entityVariant)
    -- Create a consistent ID for an entity type to maintain sound consistency
    local sizeValue = 1.0
    if entitySize == "small" then
        sizeValue = 0.7
    elseif entitySize == "large" then
        sizeValue = 1.3
    end
    
    local variantValue = entityVariant or 1
    
    -- Return a table with sound properties
    return {
        type = entityType,
        size = sizeValue,
        variant = variantValue,
        -- Create a seed based on these values for consistent randomization
        seed = tonumber(string.format("%.4f", (sizeValue * 10 + variantValue) / 13))
    }
end

return SoundSystem