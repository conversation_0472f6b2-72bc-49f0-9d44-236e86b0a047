-- biomes/river.lua
-- River biome for Lazarus Arc

local RiverBiome = {
    id = "river",
    name = "River",
    
    -- Core tile types
    primaryTiles = {"river_water", "riverbank"},
    secondaryTiles = {"shallow_water", "sand"},
    rareTiles = {"pebbles", "gravel"},
    
    -- Default proportions
    tileDistribution = {
        river_water = 0.5,
        riverbank = 0.25,
        shallow_water = 0.15,
        sand = 0.07,
        pebbles = 0.02,
        gravel = 0.01
    },
    
    -- Common passive entities
    commonEntities = {
        "fish", "frog", "duck", "water_insect"
    },
    
    uncommonEntities = {
        "beaver", "heron", "turtle", "crayfish"
    },
    
    rareEntities = {
        "river_spirit", "giant_catfish", "water_snake"
    },
    
    -- Weather patterns
    weather = {
        clear = 0.5,
        light_rain = 0.3,
        rain = 0.2
    },
    
    -- Unique features
    features = {
        {
            name = "rapids",
            chance = 0.3,
            size = {min = 3, max = 6}
        },
        {
            name = "fishing_spot",
            chance = 0.4,
            size = {min = 2, max = 3}
        },
        {
            name = "waterfall",
            chance = 0.1,
            size = {min = 4, max = 6},
            unique = true
        },
        {
            name = "flooded_trees",
            chance = 0.2,
            size = {min = 3, max = 5}
        }
    },
    
    -- Initialize the biome module
    init = function(worldCore)
        print("River biome module initialized")
        return true
    end,
    
    -- Add a new function to register generators with a specific world instance
    registerWithWorld = function(self, world)
        print("Registering river biome generators with world")
        
        if not world or not world.chunkSystem then
            print("Warning: Cannot register river biome - world or chunkSystem is nil")
            return false
        end
        
        -- Register the biome's generators with the world's chunk system
        world.chunkSystem:registerBiomeGenerators("river", {
            terrain = self.generateTerrain,
            features = self.generateFeatures,
            structures = self.generateStructures,
            entities = self.populateEntities
        })
        
        return true
    end,
    
    -- Generation algorithm for this biome
    generateChunk = function(chunk, world, variant)
        -- Initialize 2D tiles array if not exists
        chunk.tiles = chunk.tiles or {}
        for x = 0, world.CHUNK_SIZE - 1 do
            chunk.tiles[x] = chunk.tiles[x] or {}
        end
        
        -- Basic terrain generation
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                -- Calculate noise values for terrain
                local nx = (chunk.x * world.CHUNK_SIZE + x) / 100
                local ny = (chunk.y * world.CHUNK_SIZE + y) / 100
                
                -- Determine flow direction of the river
                local flowNoise = love.math.noise(nx * 0.5, ny * 0.5)
                local flowDirection = (flowNoise > 0.5) and "horizontal" or "vertical"
                
                -- River shape noise
                local riverShape = love.math.noise(nx * 2, ny * 2)
                
                -- Determine tile type based on river shape
                local tileType
                if flowDirection == "horizontal" then
                    -- Horizontal river flow
                    if math.abs(y - world.CHUNK_SIZE/2) < world.CHUNK_SIZE/4 * riverShape + world.CHUNK_SIZE/8 then
                        tileType = "river_water"
                    elseif math.abs(y - world.CHUNK_SIZE/2) < world.CHUNK_SIZE/4 * riverShape + world.CHUNK_SIZE/6 then
                        tileType = "riverbank"
                    else
                        tileType = "sand"
                    end
                else
                    -- Vertical river flow
                    if math.abs(x - world.CHUNK_SIZE/2) < world.CHUNK_SIZE/4 * riverShape + world.CHUNK_SIZE/8 then
                        tileType = "river_water"
                    elseif math.abs(x - world.CHUNK_SIZE/2) < world.CHUNK_SIZE/4 * riverShape + world.CHUNK_SIZE/6 then
                        tileType = "riverbank"
                    else
                        tileType = "sand"
                    end
                end
                
                -- Create the tile
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = love.math.random(1, 3),
                    passable = (tileType ~= "river_water"),
                    isUnderground = false
                }
                
                -- Add to chunk using 2D indexing
                chunk.tiles[x][y] = tile
            end
        end
        
        return chunk
    end,
    
    -- Helper function to generate features
    generateFeatures = function(chunk, world, variant)
        -- Generate river-specific features here
    end,
    
    -- Helper function to add entities
    populateEntities = function(chunk, world)
        local spawnEntity = function(entityType, count)
            for i = 1, count do
                local x = love.math.random(0, world.CHUNK_SIZE - 1)
                local y = love.math.random(0, world.CHUNK_SIZE - 1)
                
                if chunk.tiles[x][y].passable then
                    local worldX = chunk.x * world.CHUNK_SIZE + x
                    local worldY = chunk.y * world.CHUNK_SIZE + y
                    world.entitySystem:addEntity(entityType, worldX, worldY)
                end
            end
        end
        
        -- Spawn common entities
        for _, entityType in ipairs(RiverBiome.commonEntities) do
            local count = love.math.random(2, 5)
            spawnEntity(entityType, count)
        end
        
        -- Spawn uncommon entities
        for _, entityType in ipairs(RiverBiome.uncommonEntities) do
            local count = love.math.random(1, 2)
            spawnEntity(entityType, count)
        end
        
        -- Spawn rare entities
        for _, entityType in ipairs(RiverBiome.rareEntities) do
            if love.math.random() < 0.2 then -- 20% chance for each rare entity
                spawnEntity(entityType, 1)
            end
        end
    end,
    
    -- Helper function to place structures
    generateStructures = function(chunk, world, variant)
        -- Generate river-specific structures here
    end,
    
    -- Apply post-processing effects
    postProcess = function(chunk, world, variant)
        -- Apply river-specific post-processing here
    end
}

-- The generate function that the chunk system will call
function RiverBiome.generate(chunk, world, variant)
    RiverBiome.generateChunk(chunk, world, variant)
    RiverBiome.populateEntities(chunk, world)
    return chunk
end

return RiverBiome 