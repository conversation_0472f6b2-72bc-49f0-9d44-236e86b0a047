-- threat_curve.lua
-- Calculates scaled threat level based on inputs like time, region, player level, or events

local ThreatCurve = {}

-- Example region data (could be loaded from world or region system)
local regionThreatBase = {
    forest = 2,
    swamp = 4,
    mountains = 6,
    ruins = 5,
    hell = 9
}

-- Example modifier weights
ThreatCurve.modifiers = {
    timeOfDay = {
        day = 1.0,
        night = 1.5
    },
    playerLevel = function(level)
        return 0.8 + (level * 0.05)
    end,
    chaosLevel = function(globalChaos)
        return 1.0 + (globalChaos / 100)
    end
}

-- Main threat calculation
function ThreatCurve.calculate(region, context)
    local base = regionThreatBase[region] or 1
    local timeFactor = ThreatCurve.modifiers.timeOfDay[context.timeOfDay] or 1.0
    local playerFactor = ThreatCurve.modifiers.playerLevel(context.playerLevel or 1)
    local chaosFactor = ThreatCurve.modifiers.chaosLevel(context.chaos or 0)

    local threatScore = base * timeFactor * playerFactor * chaosFactor

    return math.floor(threatScore + 0.5) -- rounded threat level
end

return ThreatCurve