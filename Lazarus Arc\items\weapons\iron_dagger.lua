local IronDagger = {
    id = "iron_dagger",
    name = "Iron Dagger",
    type = "weapon",
    category = "dagger",
    
    -- Properties
    properties = {
        damage = 18,
        attackSpeed = 1.6,
        range = 1.1,
        durability = 150,
        maxDurability = 150,
        criticalChance = 0.25,
        criticalMultiplier = 1.7,
        armorPenetration = 0.35,
        weight = 0.6,
        stabPower = 1.3,
        combo = 0,
        maxCombo = 4,
        comboDamage = 1.15
    },
    
    -- Appearance
    appearance = {
        sprite = "iron_dagger",
        scale = 0.8,
        animations = {
            "idle",
            "stab",
            "slash",
            "combo"
        },
        variants = {
            "iron",
            "steel",
            "bronze",
            "copper"
        },
        blendMode = "normal",
        tint = {0.7, 0.7, 0.7},
        alpha = 1.0
    },
    
    -- Sound effects
    sounds = {
        stab = "dagger_stab",
        slash = "dagger_slash",
        critical = "dagger_critical",
        combo = "dagger_combo"
    },
    
    -- Effects
    effects = {
        critical = {
            type = "status",
            duration = 0.3,
            effects = {
                damage = 1.7
            }
        },
        combo = {
            type = "status",
            duration = 0.3,
            effects = {
                damage = 1.15
            }
        }
    }
}

-- Initialize the weapon
function IronDagger.init(weapon, world)
    -- Copy all fields from IronDagger template to weapon instance
    for k, v in pairs(IronDagger) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        combo = 0,
        lastComboTime = 0
    }

    return weapon
end

-- Update the weapon
function IronDagger.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Decay combo
        if world.time - weapon.properties.weaponState.lastComboTime > 1.0 then
            weapon.properties.weaponState.combo = 0
        end
    end
end

-- Handle attack
function IronDagger.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Calculate attack direction
    local angle = math.atan2(
        target.position.y - attacker.position.y,
        target.position.x - attacker.position.x
    )
    
    -- Create stab effect
    if world.createEffect then
        world.createEffect({
            type = "stab",
            position = attacker.position,
            angle = angle,
            range = weapon.properties.range,
            duration = 0.3
        })
    end
    
    -- Check for critical hit
    if math.random() < weapon.properties.criticalChance then
        -- Create critical effect
        if world.createEffect then
            world.createEffect({
                type = "critical",
                position = target.position,
                duration = 0.3
            })
        end
        
        -- Apply critical effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.critical)
        end
        
        -- Play critical sound
        if world.playSound then
            world.playSound(weapon.sounds.critical)
        end
    end
    
    -- Update combo
    if world.time - weapon.properties.weaponState.lastComboTime <= 1.0 then
        weapon.properties.weaponState.combo = math.min(weapon.properties.maxCombo,
            weapon.properties.weaponState.combo + 1)
    else
        weapon.properties.weaponState.combo = 1
    end
    weapon.properties.weaponState.lastComboTime = world.time
    
    -- Apply combo effect if active
    if weapon.properties.weaponState.combo > 1 then
        if target.applyEffect then
            target.applyEffect(weapon.effects.combo)
        end
        
        -- Play combo sound
        if world.playSound then
            world.playSound(weapon.sounds.combo)
        end
    end
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function IronDagger.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough combo
    if weapon.properties.weaponState.combo < 3 then
        return false
    end
    
    -- Create combo stab effect
    if world.createEffect then
        world.createEffect({
            type = "combo_stab",
            position = attacker.position,
            angle = math.atan2(
                target.position.y - attacker.position.y,
                target.position.x - attacker.position.x
            ),
            range = weapon.properties.range * 1.2,
            duration = 0.5
        })
    end
    
    -- Apply stab effect with increased power
    if target.applyEffect then
        target.applyEffect({
            type = "stab",
            duration = 0.4,
            effects = {
                damage = weapon.properties.stabPower
            }
        })
    end
    
    -- Reset combo
    weapon.properties.weaponState.combo = 0
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 2)
    
    return true
end

return IronDagger 