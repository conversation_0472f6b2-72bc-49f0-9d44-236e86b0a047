-- tiles/cliff_ocean.lua
local CliffOceanTile = {
    id = "cliff_ocean",
    name = "Ocean Cliff",
    passable = false, -- Impassable terrain, requires climbing or specific approach
    variants = 6, -- e.g., Sheer Granite, Eroded Limestone, Basalt Columns, Overhanging Cliff, Sea Arch, Cave-Riddled

    -- Entities that spawn here
    spawns = {"seagull", "puffin", "cliff_nest", "tough_coastal_plant", "exposed_mineral_vein"}, -- Primarily birds and hardy plants/minerals

    -- Cliff-specific properties (Inspired by mountain.txt [cite: 229])
    height = 4, -- Height in tiles/units from water level
    steepness = 0.95, -- Very steep
    rockType = "granite", -- Default, can be changed by variant
    stability = 0.7, -- How stable the cliff face is (0-1), affected by weather/erosion
    canClimb = true, -- Can be climbed, but likely difficult

    -- Interaction function
    interact = function(tile, entity)
        -- If entity is above the cliff
        if entity.z > tile.z then -- Assuming z-coordinate represents height
            -- Check for climbing down
            if entity.skills and entity.skills.climbing and entity.skills.climbing > 4 then
                return { success = true, message = "The cliff face is steep and challenging, but your skill might allow you to climb down.", effects = {{type = "suggest_action", action = "climb_down"}} }
            elseif entity.hasItem and entity.hasItem("climbing_gear") then
                 return { success = true, message = "With proper climbing gear, descending this cliff might be possible.", effects = {{type = "suggest_action", action = "climb_down"}} }
            end
             -- Check for diving (very dangerous)
            if entity.hasAbility and entity.hasAbility("controlled_dive") then -- Special ability needed?
                 return { success = true, message = "It's a long drop to the water below. A controlled dive might be survivable... or fatal.", effects = {{type="suggest_action", action="dive_from_cliff"}} }
            end
            -- Fishing from the cliff edge
             if entity.hasItem and entity.hasItem("reinforced_fishing_rod") then
                  return { success = true, message = "You could try fishing in the deep water from the cliff edge.", effects = {{type="suggest_action", action="fish_from_cliff"}} }
             end
             -- Observe the ocean
              return { success = false, message = "You stand at the edge of a sheer cliff overlooking the deep ocean. The waves crash far below." }

        -- If entity is at the base (in water or on a tiny ledge/boat)
        else
             -- Check for climbing up
            if entity.skills and entity.skills.climbing and entity.skills.climbing > 5 then -- Maybe harder to climb up?
                return { success = true, message = "Scaling this imposing cliff face would require immense skill and endurance.", effects = {{type = "suggest_action", action = "climb_up"}} }
            elseif entity.hasItem and entity.hasItem("climbing_gear") then
                 return { success = true, message = "Your climbing gear might give you a purchase on the cliff face, but it looks difficult.", effects = {{type = "suggest_action", action = "climb_up"}} }
            end
             -- Look for sea caves (if variant allows)
             if tile.variant_name == "Cave-Riddled" or tile.hasCave then
                 return { success = true, message = "You spot dark openings at the base of the cliff - potentially sea caves.", effects = {{type="discover_feature", feature="sea_cave_entrance", location={tile.x, tile.y}}} }
             end
             -- Examine the base
             return { success = false, message = "Waves relentlessly batter the base of the towering cliff." }
        end
    end,

    -- Weather effects
    weatherEffects = {
        storm = function(tile) -- Storms are significant
            tile.waveImpact = 3.0 -- Arbitrary scale of wave force
            tile.stability = tile.stability - 0.05 -- Storms erode cliffs
            -- Chance of rockfall during severe storms
            if tile.stability < 0.4 and math.random() < 0.15 then
                 return "trigger_event", "cliff_rockfall"
            end
            -- Climbing becomes extremely hazardous or impossible
            tile.climbingDifficultyModifier = 2.0
            return "visual_effect", "crashing_waves_large"
        end,
        
        rain = function(tile)
            tile.isWet = true
            tile.stability = tile.stability - 0.01 -- Minor erosion
            -- Makes climbing slippery
            tile.climbingDifficultyModifier = 1.3
             return "visual_effect", "wet_rock_face"
        end,

        freeze = function(tile) -- Ice formation
             -- Assuming world context is available if needed, e.g., via tile.worldRef
             -- local world = tile.worldRef
             -- if world and world.temperature < -5 then
             -- Simplified check for example:
             if math.random() < 0.1 then -- Placeholder for actual temperature check
                 tile.isIcy = true
                 tile.climbingDifficultyModifier = 1.8 -- Ice makes climbing very hard
                 return "visual_effect", "icy_cliff_face"
             end
             return nil
        end,

        sun = function(tile) -- Drying effect
             tile.isWet = false
             tile.isIcy = false
             tile.climbingDifficultyModifier = 1.0 -- Reset modifier
             return "remove_visual_effect", {"wet_rock_face", "icy_cliff_face"}
        end
    },

     -- Time effects
    timeEffects = {
        high_tide = function(tile) -- Affects base accessibility/appearance
             tile.waterLevel = (tile.baseWaterLevel or 0) + 1.0 -- Water higher up the cliff face
             -- May hide caves or make climbing base harder
             return "visual_effect", "high_tide_mark"
        end,
        low_tide = function(tile)
             tile.waterLevel = (tile.baseWaterLevel or 0) - 1.0
             -- May reveal tide pools, caves, or climbing handholds at the base
             return "visual_effect", "low_tide_exposed_base"
        end,
        night = function(tile)
             tile.ambientSound = "ocean_waves_night"
             -- Bird sounds cease?
            return nil
        end,
        day = function(tile)
             tile.ambientSound = "ocean_waves_seabirds"
            return nil
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {0.55, 0.55, 0.5}, -- Default grey rock
        height_map = true, -- Represents verticality
        height_scale = 2.0, -- Significantly tall
        variants = {
            {name = "Sheer Granite", rockType="granite", color_shift={0.1,0.1,0.1}, texture="granite_cliff"},
            {name = "Eroded Limestone", rockType="limestone", color_shift={0.2,0.2,0.1}, texture="limestone_cliff", stability_mod=-0.1},
            {name = "Basalt Columns", rockType="basalt", color_shift={-0.2,-0.2,-0.2}, texture="basalt_columns"},
            {name = "Overhanging Cliff", shape="overhang", stability_mod=-0.2},
            {name = "Sea Arch", shape="arch"}, -- Might be a special structure tile pair
            {name = "Cave-Riddled", overlay_objects={{name="cave_mouth", chance=0.4}}}
        },
        decoration_objects = {
            {name = "seabird_nest", chance = 0.3},
            {name = "guano_stain", chance = 0.5},
            {name = "crack_large", base_chance = 0.1, chance_modifier="inverse_stability"}, -- Changed chance = 0.4 * (1-tile.stability)
            {name = "water_line_stain", chance=0.6} -- Shows typical high tide mark
        },
        special_effects = {
             wave_splash = {type="particle_emitter", particle="water_spray", base_intensity=1.0, intensity_modifier="waveImpact", height=0.2}, -- Changed intensity_scale=tile.waveImpact or 1.0
             water_runoff = {type="decal_flow", texture="wet_streak", when="isWet"} -- Streaks when wet
        },
         casts_shadow = true,
         shadow_length = 3.0 -- Casts long shadows [cite: 259]
    },
    
    -- Audio properties
    ambientSound = "ocean_waves_seabirds", -- Combination sound
     specialSounds = {
        {name = "wave_crash_heavy", trigger = "weather_storm", chance = 0.8},
        {name = "seabird_swarm_cry", trigger = "entity_nearby", chance = 0.1},
        {name = "rock_groan", trigger = "low_stability_check", base_chance = 0.01, chance_modifier="inverse_stability"}, -- Changed chance = 0.05 * (1-tile.stability)
        {name = "rockfall_splash", trigger = "event_rockfall", chance = 1.0}
    },

    -- Climbing properties (Inspired by mountain.txt)
     climbing = {
        difficulty = 8, -- Base difficulty (harder than standard mountain)
        skillRequired = 4, -- Higher skill needed
        equipmentBonus = 4,
        modifier = 1.0, -- Affected by weather (tile.climbingDifficultyModifier)
        hazards = {
            {name = "fall_to_rocks_or_water", risk = 0.3, damage = {10, 30}}, -- Falling is very dangerous
            {name = "sudden_rock_slip", risk = 0.1, damage = {5, 10}},
            {name = "nesting_bird_attack", risk = 0.05, damage = {2, 5}}
        },
        energy_cost = 20 -- High stamina cost
    },

    -- Connections to other tiles
    connects_to = {
        "cliff_ocean", -- Itself (along the coastline)
        "deep_water", -- Base connects to deep water
        "water", -- Base might connect to normal water in some cases
        -- Tiles above:
        "grass",
        "rocky_ground",
        "sand", -- If cliff rises from a beach area
        "stone_path", -- Path running along cliff edge
        -- Special connections:
        "sea_cave" -- If variant includes caves
    },
    
    -- Effects on entities that try to move into this tile (Impassable barrier)
    onMoveAttempt = function(tile, entity, from_tile)
        -- Allow movement if climbing successfully? (Handled by climb action?)
        -- Allow movement if falling/diving? (Handled by action?)
        
        -- Generally fail movement
        return false, "A sheer cliff face blocks the way."
    end,

     -- Effect when discovering
    onDiscover = function(tile, entity)
        if entity.journal and entity.journal.addDiscovery then
             entity.journal.addDiscovery({
                type = "geography",
                name = "Ocean Cliffs", -- Or specific named cliffs
                location = {x = tile.x, y = tile.y},
                notes = "Dramatic cliffs dropping into the deep ocean. Dangerous but potentially offers views or resources."
            })
        end
        return {
            message = "You reach the edge of towering cliffs plunging into the ocean below.",
            effects = {
                {type = "reveal_map", radius = 4, require_los=true} -- Good view reveals map
            }
        }
    end
}

function CliffOceanTile.init(world)
    print("Ocean Cliff tile module initialized")
    -- Register as impassable terrain
    if world and world.systems and world.systems.pathfinding then
        world.systems.pathfinding:registerBlockingTerrain("cliff_ocean", {allow_climbing=true})
    end
    -- Register with geography/weather systems
    if world and world.systems and world.systems.geography then
        world.systems.geography:registerCoastalFeature("cliff_ocean", {height=4, erosion_factor=0.6})
    end
end

return CliffOceanTile
