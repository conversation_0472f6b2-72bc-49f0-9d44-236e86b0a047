-- factions/kingdom_eldoria.lua
-- Definition for the Kingdom of Eldoria

local KingdomEldoria = {
    id = "kingdom_eldoria",
    name = "Kingdom of Eldoria",
    description = "A large, established kingdom known for its fertile plains, sprawling forests, and strong knightly orders. It values tradition and stability.",

    -- 👑 **Governance & Leadership**
    governmentType = "Hereditary Monarchy",
    ruler = {
        name = "King Theron IV",
        title = "King of Eldoria",
        -- dynasty = "House Valerius" -- Optional dynasty info
    },
    capitalCity = "city_eldor", -- ID of the capital city tile/area
    majorCities = {"city_riverwatch", "town_northgate", "port_silvercrest"}, -- IDs of other significant settlements

    -- 🗺️ **Geography & Territory**
    primaryBiomes = {"plains", "forest"}, -- Biomes predominantly controlled
    secondaryBiomes = {"hills", "riverlands", "coastline"}, -- Other significant biomes present
    keyLandmarks = {"mount_gryphon", "kingswood_forest", "silver_river"}, -- Important geographical features

    -- 👥 **Demographics & Culture**
    populationEstimate = 5000000, -- Rough population number
    primaryRace = "Human", -- Dominant race (if applicable)
    minorityRaces = {"Elf", "Dwarf"}, -- Other common races
    dominantReligion = "Church of the Sacred Flame", -- Main religion (if any)
    culturalValues = {"Tradition", "Honor", "Stability", "Agriculture"},

    -- ⚔️ **Military**
    militaryStrength = "Strong", -- Overall assessment (Weak, Moderate, Strong, Formidable)
    militaryComposition = {
        heavy_infantry = 0.4,
        archers = 0.2,
        knights_mounted = 0.25, -- Known for its knights
        mages_battle = 0.05,
        support_logistics = 0.1
    },
    keyMilitaryOrders = {"Order of the Silver Gryphon", "Royal Guard"},

    -- 💰 **Economy**
    economicFocus = {"Agriculture", "Timber", "Trade"},
    majorExports = {"Grain", "Lumber", "Livestock", "Textiles"},
    majorImports = {"Metals", "Luxury Goods", "Magic Reagents"},
    currency = "Gold Sovereigns",

    -- 🤝 **Diplomacy & Relations**
    -- Example relations with other hypothetical factions
    factionRelations = {
        ironhold_dwarves = "Neutral (Trade Partner)",
        whisperwood_elves = "Cautious (Border Disputes)",
        shadowfen_marches = "Hostile",
        free_cities_alliance = "Ally"
    },

    -- 📜 **History & Lore**
    foundingDate = "-345 AE", -- Example year relative to game epoch
    briefHistory = "Founded after the Great Migration, Eldoria consolidated the fertile riverlands and surrounding forests under House Valerius. Known for repelling the Shadowfen incursions centuries ago, it has enjoyed relative peace but faces increasing internal and external pressures.",
    keyHistoricalEvents = {"Founding Charter (-345 AE)", "Shadowfen Wars (-120 to -105 AE)", "Coronation of King Theron IV (25 AE)"},

    -- 🛡️ **Symbols**
    coatOfArms = "A silver gryphon rampant on a field of green and gold.",
    kingdomColors = {"Green", "Gold", "Silver"},

    -- Initialization function (Optional: could load related quests, NPCs, etc.)
    -- init = function(world) ... end,

    -- Update function (Optional: could manage kingdom-level events, AI strategy)
    -- update = function(world, dt) ... end
}

return KingdomEldoria