-- controller.lua
-- Handles input from keyboard, gamepads, and other input devices for Lazarus Arc

local Controller = {
    initialized = false,
    players = {}, -- Stores controller mappings for each player
    defaultKeyboardMapping = {
        move = {
            up = "w",
            down = "s",
            left = "a",
            right = "d"
        },
        action = "space",
        togglePause = "escape",
        toggleMap = "m",
        inventory = "i",
        character = "c",
        menu = "tab"
    },
    defaultGamepadMapping = {
        move = {
            -- Analog stick (handled differently)
            stick = "left",
            -- D-pad
            up = "dpup",
            down = "dpdown",
            left = "dpleft",
            right = "dpright"
        },
        action = "a", -- Xbox A button / PlayStation Cross button
        togglePause = "start", -- Start button
        toggleMap = "back", -- Back/Select button
        inventory = "y", -- Xbox Y button / PlayStation Triangle button
        character = "x", -- Xbox X button / PlayStation Square button
        menu = "b" -- Xbox B button / PlayStation Circle button
    },
    activeGamepads = {}, -- Tracks connected gamepads
    deadzone = 0.25, -- Analog stick deadzone
    inputBuffer = {}, -- For tracking input state changes
    mobileControls = false, -- Whether touch controls are active
    maxPlayers = 4,
    lastGamepadScan = 0,
    gamepadScanInterval = 1 -- How often to scan for gamepad changes (seconds)
}

-- Initialize the controller system
function Controller.init()
    print("Initializing controller system...")

    -- Set up default controller for player 1 (keyboard)
    Controller.players[1] = {
        id = 1,
        type = "keyboard",
        mapping = Controller.copyTable(Controller.defaultKeyboardMapping),
        active = true
    }

    -- Scan for connected gamepads
    Controller.scanForGamepads()

    -- Set up a periodic scan for gamepad changes
    Controller.lastGamepadScan = love.timer.getTime()

    Controller.initialized = true
    print("Controller system initialized")

    return true
end

-- Scan for connected gamepads
function Controller.scanForGamepads()
    local joysticks = love.joystick.getJoysticks()
    local foundGamepads = {}

    for i, joystick in ipairs(joysticks) do
        local joystickId = joystick:getID()
        foundGamepads[joystickId] = true

        -- Check if this is a new gamepad
        if not Controller.activeGamepads[joystickId] then
            print("New gamepad connected: " .. joystick:getName() .. " (ID: " .. joystickId .. ")")

            Controller.activeGamepads[joystickId] = {
                id = joystickId,
                joystick = joystick,
                name = joystick:getName(),
                isGamepad = joystick:isGamepad()
            }

            -- Try to assign to a player if it's a proper gamepad
            if joystick:isGamepad() then
                Controller.assignGamepadToPlayer(joystickId)
            end
        end
    end

    -- Check for disconnected gamepads
    for id, gamepad in pairs(Controller.activeGamepads) do
        if not foundGamepads[id] then
            print("Gamepad disconnected: " .. gamepad.name .. " (ID: " .. id .. ")")

            -- Remove the gamepad assignment from any player using it
            for playerId, player in pairs(Controller.players) do
                if player.type == "gamepad" and player.gamepadId == id then
                    print("Player " .. playerId .. " lost gamepad connection")

                    -- Fallback to keyboard if it's player 1
                    if playerId == 1 then
                        Controller.players[playerId].type = "keyboard"
                        Controller.players[playerId].mapping = Controller.copyTable(Controller.defaultKeyboardMapping)
                        Controller.players[playerId].gamepadId = nil
                        print("Player 1 switched to keyboard controls")
                    else
                        -- Mark as inactive if other player
                        Controller.players[playerId].active = false
                    end
                end
            end

            -- Remove from active gamepads
            Controller.activeGamepads[id] = nil
        end
    end
end

-- Try to assign a gamepad to a player without one
function Controller.assignGamepadToPlayer(gamepadId)
    local gamepad = Controller.activeGamepads[gamepadId]
    if not gamepad then return false end

    -- First, check if any existing player is using this gamepad
    for playerId, player in pairs(Controller.players) do
        if player.type == "gamepad" and player.gamepadId == gamepadId then
            return true -- Already assigned
        end
    end

    -- Try to assign to player 1 if they're using keyboard
    if Controller.players[1] and Controller.players[1].type == "keyboard" then
        Controller.players[1].type = "gamepad"
        Controller.players[1].gamepadId = gamepadId
        Controller.players[1].mapping = Controller.copyTable(Controller.defaultGamepadMapping)
        print("Assigned gamepad to Player 1")
        return true
    end

    -- Find the first player slot without an active controller
    for i = 1, Controller.maxPlayers do
        if not Controller.players[i] or not Controller.players[i].active then
            Controller.players[i] = {
                id = i,
                type = "gamepad",
                gamepadId = gamepadId,
                mapping = Controller.copyTable(Controller.defaultGamepadMapping),
                active = true
            }
            print("Assigned gamepad to Player " .. i)
            return true
        end
    end

    -- No available player slots
    print("No available player slots for gamepad assignment")
    return false
end

-- Update controller states
function Controller.update(dt)
    if not Controller.initialized then return end

    -- Periodically scan for gamepad changes
    local currentTime = love.timer.getTime()
    if currentTime - Controller.lastGamepadScan >= Controller.gamepadScanInterval then
        Controller.scanForGamepads()
        Controller.lastGamepadScan = currentTime
    end

    -- Process and update input buffer
    Controller.updateInputBuffer()
end

-- Update input buffer with current state
function Controller.updateInputBuffer()
    -- Clear the buffer for this frame
    Controller.inputBuffer = {}

    -- Process keyboard input (primarily for player 1)
    local keyboardState = {}

    -- Get all pressed keys using the correct LÖVE keyboard functions
    local keys = {"w", "a", "s", "d", "space", "i", "c", "escape", "up", "down", "left", "right"}
    for _, key in ipairs(keys) do
        keyboardState[key] = love.keyboard.isDown(key)
    end

    Controller.inputBuffer.keyboard = keyboardState

    -- Process gamepad input
    local gamepadState = {}

    for id, gamepad in pairs(Controller.activeGamepads) do
        gamepadState[id] = {
            buttons = {},
            axes = {}
        }

        -- Get gamepad button states
        if gamepad.joystick:isGamepad() then
            -- Get standard gamepad button states
            for _, button in ipairs({"a", "b", "x", "y", "back", "start", "leftshoulder", "rightshoulder"}) do
                gamepadState[id].buttons[button] = gamepad.joystick:isGamepadDown(button)
            end

            -- Get D-pad states
            for _, dir in ipairs({"dpup", "dpdown", "dpleft", "dpright"}) do
                gamepadState[id].buttons[dir] = gamepad.joystick:isGamepadDown(dir)
            end

            -- Get trigger states (as buttons)
            gamepadState[id].buttons.lefttrigger = gamepad.joystick:getGamepadAxis("triggerleft") > 0.5
            gamepadState[id].buttons.righttrigger = gamepad.joystick:getGamepadAxis("triggerright") > 0.5

            -- Get stick axes
            gamepadState[id].axes.leftx = gamepad.joystick:getGamepadAxis("leftx")
            gamepadState[id].axes.lefty = gamepad.joystick:getGamepadAxis("lefty")
            gamepadState[id].axes.rightx = gamepad.joystick:getGamepadAxis("rightx")
            gamepadState[id].axes.righty = gamepad.joystick:getGamepadAxis("righty")

            -- Apply deadzone to sticks
            for axis, value in pairs(gamepadState[id].axes) do
                if math.abs(value) < Controller.deadzone then
                    gamepadState[id].axes[axis] = 0
                end
            end
        else
            -- For non-gamepad joysticks, use a more generic approach
            -- Get all buttons
            for button = 1, gamepad.joystick:getButtonCount() do
                gamepadState[id].buttons[button] = gamepad.joystick:isDown(button)
            end

            -- Get all axes
            for axis = 1, gamepad.joystick:getAxisCount() do
                gamepadState[id].axes[axis] = gamepad.joystick:getAxis(axis)

                -- Apply deadzone
                if math.abs(gamepadState[id].axes[axis]) < Controller.deadzone then
                    gamepadState[id].axes[axis] = 0
                end
            end
        end
    end

    Controller.inputBuffer.gamepad = gamepadState

    -- Process touch input (if supported)
    if love.touch then
        local touchState = {}
        local touches = love.touch.getTouches()

        for i, id in ipairs(touches) do
            local x, y = love.touch.getPosition(id)
            touchState[id] = {x = x, y = y}
        end

        Controller.inputBuffer.touch = touchState
    end
end

-- Handle global key presses (not player-specific)
function Controller.handleGlobalKeyPress(key)
    local Engine = require("engine")

    -- Handle menu toggle with Escape key
    if key == "escape" then
        -- Show or hide inventory/character menu via UI system
        if Engine.systems.uiSystem then
            if not Engine.playerMenuVisible then
                Engine.playerMenuVisible = true
                Engine.gamePaused = true
                Engine.systems.uiSystem:openCharacterScreen(Engine.player)
                print("Opening player inventory/character menu")
            else
                Engine.playerMenuVisible = false
                Engine.gamePaused = false
                Engine.systems.uiSystem:closeCharacterScreen()
                print("Closing player inventory/character menu")
            end
            return true -- Key was handled
        end
    elseif key == "f1" then
        -- Toggle debug menu
        if Engine.systems.uiSystem and Engine.systems.uiSystem.toggleDebugMenu then
            -- Use the UI system's toggle method
            Engine.systems.uiSystem:toggleDebugMenu()
            return true -- Key was handled
        end
    elseif key == "f5" then
        -- Toggle debug mode
        Engine.debug = not Engine.debug
        print("Debug mode: " .. (Engine.debug and "ON" or "OFF"))
        return true -- Key was handled
    end

    return false -- Key was not handled
end

-- Get input state for a specific player
function Controller.getPlayerInput(playerId)
    if not Controller.initialized then Controller.init() end

    local player = Controller.players[playerId]
    if not player or not player.active then
        return {
            moveUp = false,
            moveDown = false,
            moveLeft = false,
            moveRight = false,
            moveX = 0,
            moveY = 0,
            action = false,
            menu = false,
            inventory = false,
            character = false,
            map = false
        }
    end

    local input = {
        moveUp = false,
        moveDown = false,
        moveLeft = false,
        moveRight = false,
        moveX = 0,  -- Normalized X movement (-1 to 1)
        moveY = 0,  -- Normalized Y movement (-1 to 1)
        action = false,
        menu = false,
        inventory = false,
        character = false,
        map = false
    }

    -- Process input based on controller type
    if player.type == "keyboard" then
        -- Movement
        input.moveUp = love.keyboard.isDown(player.mapping.move.up)
        input.moveDown = love.keyboard.isDown(player.mapping.move.down)
        input.moveLeft = love.keyboard.isDown(player.mapping.move.left)
        input.moveRight = love.keyboard.isDown(player.mapping.move.right)

        -- Convert binary movement to analog-like values
        input.moveX = (input.moveRight and 1 or 0) - (input.moveLeft and 1 or 0)
        input.moveY = (input.moveDown and 1 or 0) - (input.moveUp and 1 or 0)

        -- Actions
        input.action = love.keyboard.isDown(player.mapping.action)
        input.menu = love.keyboard.isDown(player.mapping.menu)
        input.inventory = love.keyboard.isDown(player.mapping.inventory)
        input.character = love.keyboard.isDown(player.mapping.character)
        input.map = love.keyboard.isDown(player.mapping.toggleMap)

    elseif player.type == "gamepad" and Controller.activeGamepads[player.gamepadId] then
        local gamepad = Controller.activeGamepads[player.gamepadId].joystick

        -- Get stick input first (priority over D-pad)
        if gamepad:isGamepad() then
            input.moveX = gamepad:getGamepadAxis("leftx")
            input.moveY = gamepad:getGamepadAxis("lefty")

            -- Apply deadzone
            if math.abs(input.moveX) < Controller.deadzone then input.moveX = 0 end
            if math.abs(input.moveY) < Controller.deadzone then input.moveY = 0 end
        end

        -- D-pad movement (overrides stick if no stick input)
        local dpadUsed = false
        if gamepad:isGamepadDown(player.mapping.move.up) then
            input.moveUp = true
            input.moveY = -1
            dpadUsed = true
        end
        if gamepad:isGamepadDown(player.mapping.move.down) then
            input.moveDown = true
            input.moveY = 1
            dpadUsed = true
        end
        if gamepad:isGamepadDown(player.mapping.move.left) then
            input.moveLeft = true
            input.moveX = -1
            dpadUsed = true
        end
        if gamepad:isGamepadDown(player.mapping.move.right) then
            input.moveRight = true
            input.moveX = 1
            dpadUsed = true
        end

        -- Actions
        input.action = gamepad:isGamepadDown(player.mapping.action)
        input.menu = gamepad:isGamepadDown(player.mapping.menu)
        input.inventory = gamepad:isGamepadDown(player.mapping.inventory)
        input.character = gamepad:isGamepadDown(player.mapping.character)
        input.map = gamepad:isGamepadDown(player.mapping.toggleMap)
    end

    return input
end

-- Legacy function - redirects to the main implementation
function Controller.applyPlayerMovement(player, dt, speed, playerId)
    -- This is a legacy function that now redirects to the main implementation
    -- at the bottom of this file to avoid duplicate code

    -- Call the main implementation with the player ID if provided
    -- The main implementation doesn't use the speed parameter directly,
    -- but calculates it based on isometric mode
    return Controller.applyPlayerMovementImpl(player, dt, playerId)
end

-- The actual implementation is now in Controller.applyPlayerMovementImpl
-- which is defined at the end of this file

-- Check if a button was just pressed this frame (not held)
function Controller.isButtonJustPressed(playerId, button)
    -- This requires tracking previous frame state, which would need to be implemented
    -- For now, just return the current state
    local input = Controller.getPlayerInput(playerId)
    return input[button] or false
end

-- Set up touch controls (for mobile)
function Controller.setupTouchControls(enabled)
    Controller.mobileControls = enabled

    if enabled then
        print("Touch controls enabled")
        -- Would set up on-screen controls here
    else
        print("Touch controls disabled")
    end
end

-- Custom key mapping for a player
function Controller.setPlayerKeyMapping(playerId, mappingTable)
    local player = Controller.players[playerId]
    if not player then return false end

    player.mapping = Controller.copyTable(mappingTable)
    return true
end

-- Reset a player's controls to default
function Controller.resetPlayerControls(playerId)
    local player = Controller.players[playerId]
    if not player then return false end

    if player.type == "keyboard" then
        player.mapping = Controller.copyTable(Controller.defaultKeyboardMapping)
    elseif player.type == "gamepad" then
        player.mapping = Controller.copyTable(Controller.defaultGamepadMapping)
    end

    return true
end

-- Add a player with a specific control type
function Controller.addPlayer(controlType, gamepadId)
    -- Find the first available player slot
    local newPlayerId = nil
    for i = 1, Controller.maxPlayers do
        if not Controller.players[i] then
            newPlayerId = i
            break
        end
    end

    if not newPlayerId then
        print("Cannot add player: maximum player count reached")
        return nil
    end

    -- Create new player
    if controlType == "keyboard" then
        Controller.players[newPlayerId] = {
            id = newPlayerId,
            type = "keyboard",
            mapping = Controller.copyTable(Controller.defaultKeyboardMapping),
            active = true
        }
    elseif controlType == "gamepad" and gamepadId then
        if not Controller.activeGamepads[gamepadId] then
            print("Cannot add player: gamepad not connected")
            return nil
        end

        Controller.players[newPlayerId] = {
            id = newPlayerId,
            type = "gamepad",
            gamepadId = gamepadId,
            mapping = Controller.copyTable(Controller.defaultGamepadMapping),
            active = true
        }
    elseif controlType == "touch" then
        Controller.players[newPlayerId] = {
            id = newPlayerId,
            type = "touch",
            mapping = {}, -- Touch controls don't use mapping the same way
            active = true
        }
    else
        print("Invalid control type: " .. tostring(controlType))
        return nil
    end

    print("Added player " .. newPlayerId .. " with " .. controlType .. " controls")
    return newPlayerId
end

-- Remove a player
function Controller.removePlayer(playerId)
    if playerId == 1 then
        print("Cannot remove player 1")
        return false
    end

    if Controller.players[playerId] then
        Controller.players[playerId] = nil
        print("Removed player " .. playerId)
        return true
    end

    return false
end

-- Set gamepad vibration (rumble)
function Controller.setVibration(playerId, leftMotor, rightMotor, duration)
    local player = Controller.players[playerId]
    if not player or player.type ~= "gamepad" then return false end

    local gamepad = Controller.activeGamepads[player.gamepadId]
    if not gamepad or not gamepad.joystick then return false end

    -- Check if vibration is supported
    if not gamepad.joystick.setVibration then return false end

    -- Set vibration
    gamepad.joystick:setVibration(leftMotor, rightMotor, duration)
    return true
end

-- Get a list of connected controllers
function Controller.getControllerList()
    local controllers = {}

    -- Add keyboard
    table.insert(controllers, {
        id = "keyboard",
        name = "Keyboard",
        type = "keyboard"
    })

    -- Add gamepads
    for id, gamepad in pairs(Controller.activeGamepads) do
        table.insert(controllers, {
            id = id,
            name = gamepad.name,
            type = "gamepad",
            isGamepad = gamepad.isGamepad
        })
    end

    return controllers
end

-- Get current player controller assignments
function Controller.getPlayerAssignments()
    local assignments = {}

    for id, player in pairs(Controller.players) do
        if player.active then
            assignments[id] = {
                type = player.type,
                gamepadId = player.gamepadId,
                active = player.active
            }
        end
    end

    return assignments
end

-- Export current control mappings (for saving)
function Controller.exportMappings()
    local mappings = {}

    for id, player in pairs(Controller.players) do
        mappings[id] = {
            type = player.type,
            mapping = Controller.copyTable(player.mapping)
        }
    end

    return mappings
end

-- Import control mappings (for loading)
function Controller.importMappings(mappings)
    for id, mapping in pairs(mappings) do
        if Controller.players[id] then
            Controller.players[id].mapping = Controller.copyTable(mapping.mapping)
        end
    end
end

-- Clean up and shut down the controller system
function Controller.shutdown()
    if not Controller.initialized then return end

    -- Stop any vibration
    for id, player in pairs(Controller.players) do
        if player.type == "gamepad" then
            Controller.setVibration(id, 0, 0, 0)
        end
    end

    Controller.initialized = false
    Controller.players = {}
    Controller.activeGamepads = {}
    Controller.inputBuffer = {}

    print("Controller system shutdown complete")
end

-- Apply player movement based on input (main implementation)
--
-- NOTE: MODIFICATION HISTORY
-- [2023-07-15] Attempted to fix player movement by changing direction format from strings to vectors
-- and adding additional safety checks. These changes DID NOT resolve the movement issue.
-- DO NOT attempt similar changes without first identifying the root cause through debugging.
-- The issue may be elsewhere in the entity system or tile rendering.
--
-- [2023-07-15] Second attempt: Added debug output to help diagnose player movement issues.
-- Added detailed logging for input state and player movement.
-- Added safety checks for player position tracking.
-- These changes DID NOT resolve the movement issue.
--
-- [2023-07-15] Third attempt: Added more robust debug output and position tracking.
-- The issue appears to be more complex than just a direction format mismatch.
-- The problem might be related to how input is processed, how the entity system registers
-- and updates entities, or how the world handles player movement and collision.
-- These changes also DID NOT resolve the movement issue.
--
-- [2023-07-15] IMPORTANT DISCOVERY FROM CONSOLE OUTPUT:
-- Console shows that key presses are being captured correctly:
--   "Engine: Key pressed: w/a/s/d"
-- But they're being forwarded to the UI system as text input:
--   "Engine.textInput called with: w/a/s/d"
--   "Forwarding to UI system directly"
-- This suggests the movement keys are being intercepted by the text input system
-- instead of being processed as movement commands. This would explain why the
-- player character isn't moving - the keys are being treated as text input rather
-- than control commands.
--
-- [2023-07-15] FINAL NOTE: Despite identifying the potential issue with text input
-- capturing movement keys, the character still isn't moving. This suggests either:
-- 1. Our diagnosis about text input is incorrect or incomplete
-- 2. There are multiple issues affecting player movement
-- 3. The issue is more fundamental to the engine architecture
-- Further debugging would require a deeper investigation of the engine's input
-- handling system and how it interacts with the UI and controller systems.
--
-- [2023-07-15] FIX IMPLEMENTED:
-- Modified Engine.textInput function in engine.lua to ignore movement keys (w, a, s, d)
-- during gameplay state. This prevents movement keys from being captured by the text
-- input system and allows them to be processed as movement commands.
--
-- [2023-07-15] UPDATE: The fix successfully prevents movement keys from being intercepted
-- by the text input system (confirmed in console output), but the player still doesn't move.
-- This suggests there might be another issue in the movement system or entity update logic.
-- The keys are correctly detected but something else is preventing the actual movement.
--
-- [2023-07-15] ADDITIONAL OBSERVATIONS:
-- 1. The player's direction is being updated (saved as "up" in character data)
-- 2. The player's position is being saved, but it's unclear if it's changing during gameplay
-- 3. Mouse clicks are being registered, but don't appear to affect player movement
-- 4. The issue might be related to how the player's position is updated in the world
--    or how the camera/viewport follows the player
--
-- [2023-07-15] FINAL CONCLUSION:
-- The text input fix was successful in preventing movement keys from being intercepted,
-- but the player still doesn't move. This suggests a deeper issue in the movement system.
-- Possible causes include:
-- 1. The player entity might not be properly registered in the entity system
-- 2. The movement vector might not be properly applied to the player's position
-- 3. The camera/viewport might not be following the player's position
-- 4. There might be collision detection preventing movement
-- 5. The world rendering might not be updating to reflect the player's new position
-- Further debugging would require more extensive modifications to add detailed logging
-- throughout the movement and rendering systems.
--
-- [2023-07-15] ROOT CAUSE IDENTIFIED AND FIXED:
-- The issue was in the viewport management system. During gameplay state, the camera was
-- intentionally configured to NOT follow the player. This was causing the player to appear
-- stationary even though their position was being updated correctly.
--
-- Fix 1: Modified viewport_management.lua to allow the camera to follow the player during
-- gameplay state by removing the early return statement that was preventing camera updates.
--
-- Fix 2: Modified renderer.lua to use the player's actual position instead of the viewport
-- position when calculating the camera transform. This ensures the camera follows the player
-- immediately without any lag.
--
-- Fix 3: Fixed a bug in weather_system.lua where it was attempting to perform arithmetic
-- on a table value instead of a number. Added type checking to handle this case.
--
--[[
    Character Movement System - Detailed Implementation
    --------------------------------------------------

    This is the core function that handles character movement in the game. It processes
    input from keyboard/gamepad and translates it into character movement in the game world.

    The character movement system has several key components:

    1. Input Handling:
       - Captures keyboard input (WASD, arrow keys) and gamepad input (sticks, d-pad)
       - Translates these inputs into movement vectors (dx, dy)
       - Normalizes diagonal movement to prevent faster diagonal speed

    2. Player Entity:
       - The player entity must have a unique ID (generated in Player.new)
       - The player entity must have a position property (x, y coordinates)
       - The player entity must have a direction property (up, down, left, right)

    3. World Registration:
       - The player entity must be registered with the world's entity system
       - This is done using Engine.currentWorld.entitySystem:register(player)
       - The player must also be set in the world using Engine.currentWorld:setPlayer(player)

    4. Viewport Management:
       - The camera needs to follow the player correctly
       - This is done by updating the player's position in the viewport manager
       - If the player isn't in the viewport manager, it's added

    For character movement to work properly:
    - The player entity must be properly created with a unique ID
    - The player entity must be registered with the world's entity system
    - The player entity must be set in the world using setPlayer
    - The player entity must be tracked by the viewport manager
    - The viewport manager must be configured to follow the player during gameplay

    The movement is frame-rate independent (using delta time) to ensure consistent
    movement speed regardless of frame rate.

    [2023-07-16] REIMPLEMENTATION:
    Completely reimplemented the player-controller connection to fix issues with player movement.
    The new implementation:
    1. Allows specifying which player ID to get input for (instead of hardcoding player 1)
    2. Properly connects the player entity to its controller
    3. Adds support for multiple players with different controllers
    4. Improves error handling and debugging
]]
function Controller.applyPlayerMovementImpl(player, dt, playerId)
    if not player then
        print("ERROR: No player provided to applyPlayerMovementImpl")
        return
    end

    -- Ensure player has a position
    if not player.position then
        player.position = {x = 0, y = 0}
        print("WARNING: Player had no position, initialized to (0,0)")
    end

    -- Get player input for the specified player ID (default to player 1 if not specified)
    local playerIdToUse = playerId or 1

    -- Check if the player ID is valid
    if not Controller.players[playerIdToUse] or not Controller.players[playerIdToUse].active then
        print("WARNING: Player ID " .. tostring(playerIdToUse) .. " is not valid or not active")
        -- Fall back to player 1 if available
        if Controller.players[1] and Controller.players[1].active then
            playerIdToUse = 1
            print("Falling back to player 1")
        else
            print("ERROR: No valid player controller available")
            return
        end
    end

    -- Get input for the specified player
    local input = Controller.debug and Controller.getPlayerInputWithDebug(playerIdToUse) or Controller.getPlayerInput(playerIdToUse)

    -- Log movement attempt
    if Controller.debug and player and player.position then
        Controller.logDebug("Movement attempt - Player " .. playerIdToUse .. " position: " ..
            tostring(player.position.x) .. ", " .. tostring(player.position.y) ..
            " - Input: moveX=" .. tostring(input.moveX) .. ", moveY=" .. tostring(input.moveY))
    end

    -- Set a reasonable movement speed
    local moveSpeed = 150  -- Units per second

    -- Apply movement directly based on input keys
    local moved = false

    -- Debug output for input state
    if Controller.debug then
        print("Input state for player " .. playerIdToUse .. ": moveLeft=" .. tostring(input.moveLeft) ..
              ", moveRight=" .. tostring(input.moveRight) ..
              ", moveUp=" .. tostring(input.moveUp) ..
              ", moveDown=" .. tostring(input.moveDown))
    end

    -- Store last position for collision detection
    if not player.lastPosition then
        player.lastPosition = {x = player.position.x, y = player.position.y}
    else
        player.lastPosition.x = player.position.x
        player.lastPosition.y = player.position.y
    end

    -- Store original position for debugging
    local originalX, originalY = player.position.x, player.position.y

    -- Use debug console for clean logging
    local DebugConsole = require("utils.debug_console")
    DebugConsole.logPlayerPosition(player.position.x, player.position.y, "Player " .. playerIdToUse .. " Before Movement")

    -- Apply movement based on input
    if input.moveLeft then
        player.position.x = player.position.x - moveSpeed * dt
        player.direction = "left"
        moved = true
        DebugConsole.logMovementInput(-1, 0)
    end

    if input.moveRight then
        player.position.x = player.position.x + moveSpeed * dt
        player.direction = "right"
        moved = true
        DebugConsole.logMovementInput(1, 0)
    end

    if input.moveUp then
        player.position.y = player.position.y - moveSpeed * dt
        player.direction = "up"
        moved = true
        DebugConsole.logMovementInput(0, -1)
    end

    if input.moveDown then
        player.position.y = player.position.y + moveSpeed * dt
        player.direction = "down"
        moved = true
        DebugConsole.logMovementInput(0, 1)
    end

    -- Set player's isMoving flag
    player.isMoving = moved

    -- Store movement vector for direction indicator
    if not player.moveVector then player.moveVector = {} end
    player.moveVector.x = (input.moveRight and 1 or 0) - (input.moveLeft and 1 or 0)
    player.moveVector.y = (input.moveDown and 1 or 0) - (input.moveUp and 1 or 0)

    -- Log final position after movement
    if moved then
        DebugConsole.logPlayerPosition(player.position.x, player.position.y, "Player " .. playerIdToUse .. " After Movement")
    end

    -- Get Engine reference
    local Engine = require("engine")

    -- Force update the entity in the world
    if Engine and Engine.currentWorld and Engine.currentWorld.entitySystem then
        -- Make sure the player entity is properly registered and updated
        local success = Engine.currentWorld.entitySystem:register(player)
        print("PLAYER " .. playerIdToUse .. " REGISTRATION: " .. (success and "SUCCESS" or "FAILED"))
        print("PLAYER " .. playerIdToUse .. " POSITION AFTER MOVEMENT: x=" .. tostring(player.position.x) .. ", y=" .. tostring(player.position.y))

        -- Ensure the player is set in the world
        if Engine.currentWorld.setPlayer then
            Engine.currentWorld:setPlayer(player)
            print("Player set in world")
        end

        -- Update the player in the viewport manager
        if Engine.systems and Engine.systems.viewportManager then
            -- Check if player is already in viewport manager
            local playerFound = false
            for i, p in ipairs(Engine.systems.viewportManager.players) do
                if p.id == player.id then
                    -- Update player position in viewport manager
                    p.position = player.position
                    playerFound = true
                    print("Updated player position in viewport manager")
                    break
                end
            end

            -- If player not found in viewport manager, add them
            if not playerFound then
                Engine.systems.viewportManager:addPlayer({
                    id = player.id,
                    position = player.position,
                    character = player.character
                })
                print("Added player to viewport manager")
            end
        end
    else
        print("WARNING: Cannot register player - Engine, currentWorld, or entitySystem not available")
    end

    -- If the player has a handleInput method, call it with the input
    if player.handleInput then
        -- Create a simple input manager that provides the getInputState method
        local inputManager = {
            getInputState = function()
                return {
                    move = {
                        x = input.moveX,
                        y = input.moveY
                    },
                    action = {
                        pressed = input.action
                    },
                    inventory = {
                        pressed = input.inventory
                    },
                    character = {
                        pressed = input.character
                    },
                    togglePause = {
                        pressed = input.menu
                    }
                }
            end
        }

        -- Call the player's handleInput method
        player:handleInput(inputManager, Engine and Engine.currentWorld, dt)
    end

    return input
end

-- Debug toggle for controller
Controller.debug = false
Controller.debugLog = {}
Controller.maxLogEntries = 100
Controller.lastInputState = {}
Controller.inputChanged = false

-- Toggle debug output for controller
function Controller.toggleDebug()
    Controller.debug = not Controller.debug
    print("Controller debug mode: " .. (Controller.debug and "ON" or "OFF"))

    -- If debug is enabled, also enable input tracing
    if Controller.debug then
        -- Force enable debug mode in the engine
        local Engine = require("engine")
        if Engine then
            Engine.debug = true
            print("Engine debug mode enabled")
        end

        -- Hook into love.keypressed if not already hooked
        if not Controller.originalKeypressed then
            Controller.originalKeypressed = love.keypressed
            love.keypressed = function(key, scancode, isrepeat)
                Controller.logDebug("KEYPRESSED: " .. key .. " (scancode: " .. scancode .. ", repeat: " .. tostring(isrepeat) .. ")")

                -- Call original handler
                if Controller.originalKeypressed then
                    Controller.originalKeypressed(key, scancode, isrepeat)
                end
            end
            print("Hooked into love.keypressed")
        end

        -- Hook into love.textinput if not already hooked
        if not Controller.originalTextinput then
            Controller.originalTextinput = love.textinput
            love.textinput = function(text)
                Controller.logDebug("TEXTINPUT: " .. text)

                -- Call original handler
                if Controller.originalTextinput then
                    Controller.originalTextinput(text)
                end
            end
            print("Hooked into love.textinput")
        end
    else
        -- Restore original handlers if debug is disabled
        if Controller.originalKeypressed then
            love.keypressed = Controller.originalKeypressed
            Controller.originalKeypressed = nil
            print("Restored original love.keypressed")
        end

        if Controller.originalTextinput then
            love.textinput = Controller.originalTextinput
            Controller.originalTextinput = nil
            print("Restored original love.textinput")
        end
    end

    return Controller.debug
end

-- Add a debug log entry
function Controller.logDebug(message)
    if not Controller.debug then return end

    -- Add timestamp
    local timestamp = os.date("%H:%M:%S")
    local entry = timestamp .. " - " .. message

    -- Add to log
    table.insert(Controller.debugLog, entry)

    -- Keep log size manageable
    if #Controller.debugLog > Controller.maxLogEntries then
        table.remove(Controller.debugLog, 1)
    end

    -- Print to console
    print("[CONTROLLER DEBUG] " .. entry)
end

-- Draw debug information on screen
function Controller.drawDebug()
    if not Controller.debug then return end

    local Engine = require("engine")
    if not Engine then return end

    -- Save current graphics state
    love.graphics.push()
    love.graphics.origin()

    -- Set color for debug text
    love.graphics.setColor(1, 1, 0, 0.9)

    -- Draw title
    love.graphics.print("CONTROLLER DEBUG", 10, 10)

    -- Draw player info
    if Engine.player then
        love.graphics.print("Player Position: " ..
            tostring(Engine.player.position.x) .. ", " ..
            tostring(Engine.player.position.y), 10, 30)
        love.graphics.print("Player Direction: " ..
            tostring(type(Engine.player.direction) == "table" and
                "x=" .. tostring(Engine.player.direction.x) .. ", y=" .. tostring(Engine.player.direction.y)
                or tostring(Engine.player.direction)), 10, 50)
        love.graphics.print("Player isMoving: " .. tostring(Engine.player.isMoving), 10, 70)
    else
        love.graphics.print("Player not found", 10, 30)
    end

    -- Draw input state
    love.graphics.print("Input State:", 10, 100)
    local y = 120
    for key, value in pairs(Controller.lastInputState) do
        if type(value) ~= "table" then
            love.graphics.print(key .. ": " .. tostring(value), 20, y)
            y = y + 20
        end
    end

    -- Draw recent log entries (last 10)
    love.graphics.print("Recent Events:", 10, y + 20)
    y = y + 40
    local startIdx = math.max(1, #Controller.debugLog - 10)
    for i = startIdx, #Controller.debugLog do
        love.graphics.print(Controller.debugLog[i], 20, y)
        y = y + 20
    end

    -- Restore graphics state
    love.graphics.pop()
end

-- Enhanced version of getPlayerInput that logs changes
function Controller.getPlayerInputWithDebug(playerId)
    local input = Controller.getPlayerInput(playerId)

    -- Check if input state has changed
    if Controller.debug then
        local changed = false

        -- Compare with last input state
        if not Controller.lastInputState.moveUp and input.moveUp then
            Controller.logDebug("Input changed: moveUp = true")
            changed = true
        elseif Controller.lastInputState.moveUp and not input.moveUp then
            Controller.logDebug("Input changed: moveUp = false")
            changed = true
        end

        if not Controller.lastInputState.moveDown and input.moveDown then
            Controller.logDebug("Input changed: moveDown = true")
            changed = true
        elseif Controller.lastInputState.moveDown and not input.moveDown then
            Controller.logDebug("Input changed: moveDown = false")
            changed = true
        end

        if not Controller.lastInputState.moveLeft and input.moveLeft then
            Controller.logDebug("Input changed: moveLeft = true")
            changed = true
        elseif Controller.lastInputState.moveLeft and not input.moveLeft then
            Controller.logDebug("Input changed: moveLeft = false")
            changed = true
        end

        if not Controller.lastInputState.moveRight and input.moveRight then
            Controller.logDebug("Input changed: moveRight = true")
            changed = true
        elseif Controller.lastInputState.moveRight and not input.moveRight then
            Controller.logDebug("Input changed: moveRight = false")
            changed = true
        end

        -- Store current input state for next comparison
        Controller.lastInputState = {
            moveUp = input.moveUp,
            moveDown = input.moveDown,
            moveLeft = input.moveLeft,
            moveRight = input.moveRight,
            moveX = input.moveX,
            moveY = input.moveY,
            action = input.action,
            menu = input.menu,
            inventory = input.inventory,
            character = input.character,
            map = input.map
        }

        Controller.inputChanged = changed
    end

    return input
end

-- Utility function to deep copy a table
function Controller.copyTable(orig)
    local orig_type = type(orig)
    local copy
    if orig_type == 'table' then
        copy = {}
        for orig_key, orig_value in pairs(orig) do
            copy[orig_key] = Controller.copyTable(orig_value)
        end
    else
        copy = orig
    end
    return copy
end

-- Love2D callbacks for controller system

-- Handle joystick connection
function love.joystickadded(joystick)
    if Controller.initialized then
        print("Joystick connected: " .. joystick:getName())
        Controller.scanForGamepads()
    end
end

-- Handle joystick disconnection
function love.joystickremoved(joystick)
    if Controller.initialized then
        print("Joystick disconnected: " .. joystick:getName())
        Controller.scanForGamepads()
    end
end

return Controller
