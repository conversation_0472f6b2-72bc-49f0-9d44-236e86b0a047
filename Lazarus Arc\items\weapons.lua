-- items/weapons.lua
-- Weapons and ammunition items

local WeaponItems = {
    -- Standard Weapons
    weapon_security_standard = {
        id = "weapon_security_standard",
        name = "Standard Security Weapon",
        description = "A standard-issue security weapon, reliable but basic.",
        type = "weapon",
        rarity = 2,
        value = 40,
        weight = 3,
        stackable = false,
        tags = {"weapon", "security", "standard"}
    },

    -- Ammunition
    ammunition_standard = {
        id = "ammunition_standard",
        name = "Standard Ammunition",
        description = "Standard ammunition rounds for security weapons.",
        type = "ammunition",
        rarity = 2,
        value = 5,
        weight = 0.5,
        stackable = true,
        maxStack = 50,
        tags = {"ammunition", "standard"}
    },

    -- Repair Kits
    repair_kit = {
        id = "repair_kit",
        name = "Weapon Repair Kit",
        description = "A kit containing tools and parts for weapon maintenance.",
        type = "tool",
        rarity = 2,
        value = 30,
        weight = 2,
        stackable = true,
        maxStack = 5,
        tags = {"weapon", "repair", "tool"}
    }
}

return WeaponItems 