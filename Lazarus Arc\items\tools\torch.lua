-- items/tools/torch.lua

local Tool = {
    id = "torch",
    name = "Torch",
    description = "A simple torch that provides light in dark areas.",
    type = "tool",
    category = "tool",
    stackable = true,
    maxStack = 10,

    stats = {
        light_radius = 8.0,
        burn_time = 300.0, -- seconds
        weight = 1.0,
    },

    sprite = "res://Sprite/items/tools/torch.png",
    size = { width = 32, height = 32 },

    effects = {
        light_source = true,
        fire_damage = 2.0
    },

    meta = {
        rarity = "common"
    },

    onUse = function(self, user)
        -- Toggle torch on/off
        if not self.isLit then
            self.isLit = true
            print(user.name .. " lit a torch.")
            -- Add light source to user
            if user.addLightSource then
                user:addLightSource(self.stats.light_radius)
            end
        else
            self.isLit = false
            print(user.name .. " extinguished a torch.")
            -- Remove light source from user
            if user.removeLightSource then
                user:removeLightSource()
            end
        end
        return true
    end,
    
    update = function(self, dt)
        if self.isLit then
            self.stats.burn_time = math.max(0, self.stats.burn_time - dt)
            if self.stats.burn_time <= 0 then
                self.isLit = false
                print("The torch burned out.")
                -- Remove light source from user
                if self.user and self.user.removeLightSource then
                    self.user:removeLightSource()
                end
            end
        end
    end
}

return Tool
