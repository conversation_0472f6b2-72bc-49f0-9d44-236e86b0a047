-- entities/orc.lua
-- Orcish entity with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local Orc = {
    id = "orc",
    name = "Orc",
    type = "orc",
    shape = {
        {0, -1.2}, {0.9, -0.8}, {1.2, 0}, {0.9, 0.8},
        {0, 1.2}, {-0.9, 0.8}, {-1.2, 0}, {-0.9, -0.8}
    },
    size = 8,  -- Larger than humans
    description = "A fierce orcish warrior with brutal strength and savage instincts",

    -- Entity categories
    categories = {"humanoid", "orc", "aggressive", "tribal"},
    threatCategories = {"player", "elf", "dwarf", "human"},
    
    -- Base stats (high attack, moderate defense)
    maxHealth = 140,
    health = 140,
    maxStamina = 100,
    stamina = 100,
    maxMana = 20,
    mana = 20,
    speed = 2.2,
    attack = 22,  -- Very high attack
    defense = 10,
    magicAttack = 4,
    magicDefense = 6,
    rage = 0,     -- Special rage meter
    
    -- Behaviors (orcs are aggressive)
    behaviors = {"hunt", "rage", "intimidate", "tribal_gather"},
    behaviorConfigs = {
        hunt = {
            moveSpeed = 2.5,
            huntRadius = 15,
            aggressionLevel = 0.8,
            packHunting = true
        },
        rage = {
            triggerHealth = 0.5,  -- Rage when below 50% health
            attackBonus = 1.5,
            speedBonus = 1.3,
            duration = 10
        },
        intimidate = {
            radius = 8,
            fearEffect = 0.7,
            frequency = 0.2
        },
        tribal_gather = {
            groupRadius = 20,
            leadershipBonus = 1.2,
            frequency = 0.1
        }
    },
    
    -- Enhanced variant system for orcs
    variantChances = {
        normal = 0.65,          -- 65% tribal orc
        shiny = 0.20,           -- 20% war chief (shiny)
        rare = 0.12,            -- 12% berserker (rare)
        legendary = 0.03        -- 3% orc warlord (legendary)
    },
    
    variants = {
        normal = {
            name = "Tribal Orc",
            description = "A common orc warrior from the tribal lands",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.8, 1.0, 0.7, 1.0}  -- Green skin
            }
        },
        
        shiny = {
            name = "War Chief",
            description = "A battle-scarred orc leader with enhanced combat prowess",
            statModifiers = {
                maxHealth = 1.5,
                attack = 1.4,
                defense = 1.3,
                speed = 1.2,
                leadership = 1.8
            },
            appearanceModifiers = {
                scale = 1.2,
                glow = true,
                colorTint = {0.7, 1.1, 0.6, 1.0},  -- Darker war paint
                war_paint = true,
                battle_scars = true
            },
            soundModifiers = {
                pitch = 0.8,
                volume = 1.4,
                intimidating = true
            }
        },
        
        rare = {
            name = "Berserker",
            description = "A frenzied orc consumed by battle rage",
            statModifiers = {
                maxHealth = 1.8,
                attack = 1.8,
                speed = 1.4,
                rage_generation = 2.0,
                defense = 0.8  -- Lower defense due to reckless fighting
            },
            appearanceModifiers = {
                scale = 1.25,
                colorTint = {1.0, 0.8, 0.6, 1.0},  -- Reddish from rage
                berserker_markings = true,
                blood_stains = true,
                wild_eyes = true
            },
            soundModifiers = {
                pitch = 0.7,
                volume = 1.6,
                reverb = true,
                primal_roar = true
            }
        },
        
        legendary = {
            name = "Orc Warlord",
            description = "A legendary orcish commander who unites the tribes",
            statModifiers = {
                maxHealth = 2.8,
                attack = 2.2,
                defense = 1.8,
                speed = 1.3,
                leadership = 3.0,
                tactical_genius = 2.0
            },
            appearanceModifiers = {
                scale = 1.5,
                glow = true,
                colorTint = {0.6, 1.2, 0.5, 1.0},  -- Dark emerald
                warlord_armor = true,
                command_aura = "legendary",
                tribal_banners = true
            },
            soundModifiers = {
                pitch = 0.6,
                volume = 2.0,
                reverb = true,
                echo = true,
                commanding_presence = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "orc_hide", chance = 0.8, quantity = {1, 2}},
        {id = "crude_weapon", chance = 0.6, quantity = {1, 1}},
        {id = "bone_fragment", chance = 0.7, quantity = {2, 4}},
        {id = "tribal_trinket", chance = 0.4, quantity = {1, 1}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "war_chief_banner", chance = 0.8, quantity = {1, 1}},
            {id = "battle_axe", chance = 0.7, quantity = {1, 1}},
            {id = "leadership_totem", chance = 0.5, quantity = {1, 1}},
            {id = "war_paint_recipe", chance = 0.4, quantity = {1, 1}}
        },
        rare = {
            {id = "berserker_rage_potion", chance = 0.7, quantity = {1, 2}},
            {id = "blood_soaked_weapon", chance = 0.6, quantity = {1, 1}},
            {id = "primal_essence", chance = 0.5, quantity = {1, 1}},
            {id = "berserker_mark", chance = 0.4, quantity = {1, 1}}
        },
        legendary = {
            {id = "warlord_crown", chance = 0.9, quantity = {1, 1}},
            {id = "legendary_orc_weapon", chance = 0.8, quantity = {1, 1}},
            {id = "tribal_unification_scroll", chance = 0.6, quantity = {1, 1}},
            {id = "command_horn", chance = 0.7, quantity = {1, 1}},
            {id = "warlord_essence", chance = 0.5, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "orc",
        scale = 1.1,  -- Larger than default
        animations = {
            "idle", "walk", "attack", "rage", "roar", "death"
        },
        variants = {
            "tribal_orc", "war_chief", "berserker", "orc_warlord"
        }
    },
    
    -- Sound effects with orcish characteristics
    sounds = {
        idle = {
            file = "orc_idle",
            synth = {
                instrument = "bass_guitar",
                notes = {"E2", "A2"},
                durations = {0.5, 0.7},
                volume = 0.4,
                growl = true
            }
        },
        roar = {
            file = "orc_roar",
            synth = {
                instrument = "brass",
                notes = {"C2", "G2"},
                durations = {0.8, 1.2},
                volume = 0.8,
                intimidating = true
            }
        },
        attack = {
            file = "orc_attack",
            synth = {
                instrument = "percussion",
                notes = {"D2"},
                duration = 0.3,
                volume = 0.6,
                aggressive = true
            }
        },
        hurt = {
            file = "orc_hurt",
            synth = {
                instrument = "bass_guitar",
                notes = {"F#2"},
                duration = 0.4,
                volume = 0.6,
                angry = true
            }
        },
        death = {
            file = "orc_death",
            synth = {
                instrument = "bass_guitar",
                notes = {"E2", "C2", "G1", "E1"},
                durations = {0.5, 0.5, 0.8, 1.5},
                volume = 0.7,
                final_roar = true
            }
        },
        rage = {
            file = "orc_rage",
            synth = {
                instrument = "distorted_guitar",
                notes = {"E2", "G2", "E2"},
                durations = {0.3, 0.4, 0.5},
                volume = 0.9,
                berserker = true
            }
        }
    },
    
    -- Special abilities
    abilities = {
        berserker_rage = {
            type = "active",
            description = "Enter a rage state for increased damage and speed",
            effect = "temporary_stat_boost",
            cooldown = 30
        },
        intimidating_presence = {
            type = "passive",
            description = "Causes fear in nearby enemies",
            effect = "fear_aura"
        },
        tribal_leadership = {
            type = "passive",
            description = "Boosts nearby orc allies",
            effect = "ally_buff"
        },
        thick_hide = {
            type = "passive",
            description = "Natural armor from tough skin",
            effect = "damage_reduction"
        }
    }
}

-- Initialize the orc entity using enhanced template
function Orc.init(entity, world)
    -- Copy all fields from Orc template to entity instance
    for k, v in pairs(Orc) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return Orc
