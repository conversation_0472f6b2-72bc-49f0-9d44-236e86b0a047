-- behaviors/intimidation.lua
-- Territorial/Intimidation behavior for entities that defend specific areas and intimidate intruders

local TerritorialBehavior = {}  -- create an empty table

TerritorialBehavior.id = "territorial"
TerritorialBehavior.name = "Territorial Behavior"

-- Configuration parameters
TerritorialBehavior.defaultConfig = {
    territoryRadius = 10,         -- Radius of defended area
    detectionRadius = 12,         -- Radius to detect intruders
    warningDistance = 8,          -- Distance to start issuing warnings
    attackDistance = 4,           -- Distance to start attacking
    chaseSpeed = 2.5,             -- Speed when chasing intruders
    warningDuration = {2, 5},     -- Duration spent warning before attack
    warnIntruders = true,         -- Issue warning before attacking
    returnToTerritory = true,     -- Return to territory center after chase
    intimidationChance = 0.6,     -- Chance to intimidate rather than attack
    intimidationDuration = {2, 4},-- Duration spent intimidating
    avoidOverextension = true,    -- Prevent chasing intruders too far
    maxChaseDistance = 20         -- Maximum distance to chase intruder
}

-- Initialize the behavior
function TerritorialBehavior.init(entity, customConfig)
    -- Ensure entity has a valid position
    local pos = entity.position or { x = 0, y = 0 }
    entity.position = pos

    entity.territorialState = entity.territorialState or {
        centerPosition = { x = pos.x, y = pos.y },
        targetIntruder = nil,
        isWarning = false,
        isAttacking = false,
        config = {}
    }

    local config = entity.territorialState.config
    for k, v in pairs(TerritorialBehavior.defaultConfig) do
        config[k] = v
    end

    if customConfig then
        for k, v in pairs(customConfig) do
            config[k] = v
        end
    end
end

-- Update function called every game tick
function TerritorialBehavior.update(entity, world, dt)
    local state = entity.territorialState
    local config = state.config

    -- Detect intruders
    if not state.targetIntruder then
        state.targetIntruder = TerritorialBehavior.detectIntruder(entity, world)
        if not state.targetIntruder then return end
    end

    local intruder = state.targetIntruder
    local dx = intruder.position.x - entity.position.x
    local dy = intruder.position.y - entity.position.y
    local distance = math.sqrt(dx * dx + dy * dy)

    -- Decide whether to warn or attack
    if distance < config.attackDistance then
        if math.random() < config.intimidationChance then
            TerritorialBehavior.intimidate(entity, intruder)
        else
            TerritorialBehavior.attack(entity, intruder)
        end
    elseif distance < config.warningDistance then
        TerritorialBehavior.issueWarning(entity, intruder)
    else
        state.targetIntruder = nil
    end

    -- Return to territory center if needed
    if config.returnToTerritory and state.targetIntruder == nil then
        TerritorialBehavior.returnToTerritory(entity, state)
    end
end

-- Detect intruders entering territory
function TerritorialBehavior.detectIntruder(entity, world)
    -- Implementation details...
end

-- Issue a warning
function TerritorialBehavior.issueWarning(entity, intruder)
    -- Implementation details...
end

-- Intimidate an intruder
function TerritorialBehavior.intimidate(entity, intruder)
    -- Implementation details...
end

-- Attack intruder
function TerritorialBehavior.attack(entity, intruder)
    -- Implementation details...
end

-- Return to territory center
function TerritorialBehavior.returnToTerritory(entity, state)
    -- Implementation details...
end

return TerritorialBehavior
