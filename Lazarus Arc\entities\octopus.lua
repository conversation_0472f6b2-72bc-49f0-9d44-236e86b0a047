-- entities/octopus.lua
-- Underwater octopus with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local Octopus = {
    id = "octopus",
    name = "Octopus",
    type = "octopus",
    shape = {
        {0, -0.8}, {0.8, -0.6}, {1.0, 0}, {0.8, 0.6},
        {0, 0.8}, {-0.8, 0.6}, {-1.0, 0}, {-0.8, -0.6}
    },
    size = 7,

    -- Entity categories
    categories = {"aquatic", "intelligent", "cephalopod", "camouflage"},
    threatCategories = {"player", "small_fish", "crab"},
    
    -- Base stats (intelligent and agile)
    maxHealth = 60,
    health = 60,
    maxStamina = 90,
    stamina = 90,
    speed = 2.8,
    attack = 12,
    defense = 6,
    intelligence = 18,  -- Very high intelligence
    camouflage = 15,
    waterBreathing = true,
    
    -- Behaviors
    behaviors = {"intelligent_hunt", "camouflage", "problem_solve", "escape_artist"},
    behaviorConfigs = {
        intelligent_hunt = {
            moveSpeed = 3.0,
            huntRadius = 12,
            ambushTactics = true,
            toolUse = true
        },
        camouflage = {
            duration = {8, 15},
            effectiveness = 0.9,
            colorMatching = true,
            textureMatching = true
        },
        problem_solve = {
            puzzleSolving = true,
            memoryRetention = 20,
            learningRate = 2.0
        },
        escape_artist = {
            escapeChance = 0.7,
            inkCloud = true,
            creviceHiding = true
        }
    },
    
    -- Enhanced variant system for octopus
    variantChances = {
        normal = 0.70,          -- 70% common octopus
        shiny = 0.20,           -- 20% giant pacific octopus (shiny)
        rare = 0.08,            -- 8% mimic octopus (rare)
        legendary = 0.02        -- 2% kraken spawn (legendary)
    },
    
    variants = {
        normal = {
            name = "Common Octopus",
            description = "An intelligent cephalopod with eight flexible arms",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.8, 0.6, 0.5, 1.0}  -- Reddish-brown
            }
        },
        
        shiny = {
            name = "Giant Pacific Octopus",
            description = "A massive octopus with incredible reach and strength",
            statModifiers = {
                maxHealth = 1.8,    -- 108 health
                speed = 1.2,        -- 3.36 speed
                attack = 1.6,       -- 19.2 attack
                defense = 1.4,      -- 8.4 defense
                reach = 2.0,        -- Extended tentacle reach
                grappling = 1.8
            },
            appearanceModifiers = {
                scale = 1.5,
                glow = true,
                colorTint = {0.9, 0.7, 0.6, 1.0},  -- Larger with impressive presence
                massive_tentacles = true,
                intimidating_size = true
            },
            soundModifiers = {
                pitch = 0.8,
                volume = 1.3,
                deep_water = true
            }
        },
        
        rare = {
            name = "Mimic Octopus",
            description = "A master of disguise that can imitate other sea creatures",
            statModifiers = {
                maxHealth = 1.4,    -- 84 health
                speed = 1.5,        -- 4.2 speed
                intelligence = 2.0, -- 36 intelligence
                camouflage = 2.5,   -- 37.5 camouflage
                mimicry = 3.0
            },
            appearanceModifiers = {
                scale = 1.1,
                colorTint = {0.7, 0.8, 0.9, 1.0},  -- Adaptive coloring
                shape_shifting = true,
                perfect_mimicry = true,
                adaptive_texture = true
            },
            soundModifiers = {
                pitch = 1.1,
                volume = 1.2,
                reverb = true,
                adaptive_sounds = true
            }
        },
        
        legendary = {
            name = "Kraken Spawn",
            description = "A young offspring of the legendary kraken with immense potential",
            statModifiers = {
                maxHealth = 2.5,    -- 150 health
                speed = 1.4,        -- 3.92 speed
                attack = 2.2,       -- 26.4 attack
                defense = 1.8,      -- 10.8 defense
                intelligence = 3.0, -- 54 intelligence
                tentacle_power = 5.0,
                oceanic_magic = 2.0
            },
            appearanceModifiers = {
                scale = 1.8,
                glow = true,
                colorTint = {0.4, 0.5, 0.9, 1.0},  -- Deep ocean blue with mystical energy
                kraken_features = true,
                oceanic_aura = "legendary",
                tentacle_crown = true,
                water_manipulation = true
            },
            soundModifiers = {
                pitch = 0.6,
                volume = 1.6,
                reverb = true,
                echo = true,
                oceanic_power = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "octopus_meat", chance = 0.8, quantity = {2, 3}},
        {id = "tentacle", chance = 0.9, quantity = {2, 4}},
        {id = "ink_sac", chance = 0.7, quantity = {1, 2}},
        {id = "intelligent_brain", chance = 0.4, quantity = {1, 1}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "giant_octopus_beak", chance = 0.8, quantity = {1, 1}},
            {id = "massive_tentacle", chance = 0.9, quantity = {1, 2}},
            {id = "deep_sea_essence", chance = 0.7, quantity = {1, 1}},
            {id = "grappling_sucker", chance = 0.6, quantity = {3, 6}}
        },
        rare = {
            {id = "mimic_skin", chance = 0.9, quantity = {1, 1}},
            {id = "adaptive_chromatophore", chance = 0.8, quantity = {2, 4}},
            {id = "perfect_camouflage_gland", chance = 0.7, quantity = {1, 1}},
            {id = "shape_shifting_essence", chance = 0.6, quantity = {1, 1}},
            {id = "mimicry_neural_cluster", chance = 0.5, quantity = {1, 1}}
        },
        legendary = {
            {id = "kraken_spawn_tentacle", chance = 0.95, quantity = {1, 2}},
            {id = "oceanic_magic_core", chance = 0.9, quantity = {1, 1}},
            {id = "deep_sea_dominance_essence", chance = 0.8, quantity = {1, 1}},
            {id = "kraken_bloodline_crystal", chance = 0.7, quantity = {1, 1}},
            {id = "tentacle_crown_fragment", chance = 0.6, quantity = {1, 1}},
            {id = "oceanic_mastery_orb", chance = 0.5, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "octopus",
        scale = 1.0,
        animations = {
            "swim", "camouflage", "grab", "ink_cloud", "problem_solve", "tentacle_dance"
        },
        variants = {
            "common_octopus", "giant_pacific", "mimic_octopus", "kraken_spawn"
        }
    },
    
    -- Sound effects with intelligent aquatic characteristics
    sounds = {
        swim = {
            file = "octopus_swim",
            synth = {
                instrument = "synthesizer",
                notes = {"A3", "C4", "E4"},
                durations = {0.4, 0.3, 0.5},
                volume = 0.25,
                fluid = true
            }
        },
        ink_release = {
            file = "ink_release",
            synth = {
                instrument = "synthesizer",
                notes = {"D3", "F3"},
                durations = {0.3, 0.7},
                volume = 0.4,
                cloudy = true
            }
        },
        tentacle_grab = {
            file = "tentacle_grab",
            synth = {
                instrument = "bass_guitar",
                notes = {"E2"},
                duration = 0.4,
                volume = 0.5,
                grasping = true
            }
        },
        intelligent_communication = {
            file = "octopus_communication",
            synth = {
                instrument = "kalimba",
                notes = {"G4", "A4", "C5", "D5"},
                durations = {0.2, 0.2, 0.3, 0.3},
                volume = 0.3,
                complex = true
            }
        }
    },
    
    -- Special intelligent aquatic abilities
    abilities = {
        perfect_camouflage = {
            type = "active",
            description = "Become nearly invisible by matching surroundings",
            effect = "stealth_boost",
            cooldown = 15
        },
        ink_cloud = {
            type = "active",
            description = "Release ink to obscure vision and escape",
            effect = "vision_obstruction",
            cooldown = 12
        },
        problem_solving = {
            type = "passive",
            description = "Can solve complex puzzles and remember solutions",
            effect = "intelligence_bonus"
        },
        tentacle_grapple = {
            type = "active",
            description = "Grab and restrain enemies with powerful tentacles",
            effect = "immobilize",
            cooldown = 10
        },
        adaptive_learning = {
            type = "passive",
            description = "Learns from encounters and adapts strategies",
            effect = "experience_bonus"
        }
    }
}

-- Initialize the octopus entity using enhanced template
function Octopus.init(entity, world)
    -- Copy all fields from Octopus template to entity instance
    for k, v in pairs(Octopus) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return Octopus
