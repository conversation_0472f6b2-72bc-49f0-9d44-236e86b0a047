-- classes/monk.lua
-- Monk class definition for Lazarus Arc

local Monk = {
    name = "Monk",
    description = "A disciplined martial artist who harnesses speed, agility, and spiritual energy to overwhelm opponents.",
    icon = "monk_icon.png",

    -- 🎭 **Playstyle & Trade-offs**
    pros = {
        "Fast and precise strikes with high attack speed",
        "Excels in dodging and counter-attacks",
        "Uses chi energy instead of weapons"
    },
    cons = {
        "Low armor and physical durability",
        "Struggles against ranged and magic users",
        "Requires strong positioning to maximize damage"
    },

    -- 🏋 **Base Stats & Growth**
    base_stats = { 
        health = 100, 
        maxHealth = 100,
        stamina = 90, 
        maxStamina = 90,
        mana = 50,  -- Used for chi-based abilities
        maxMana = 50,
        pattack = 12, 
        pdefense = 6, 
        mattack = 8, 
        mdefense = 7, 
        speed = 10, 
        luck = 6 
    },

    stat_growth = {
        health = 9,
        stamina = 8,
        mana = 4,
        pattack = 1.4,
        pdefense = 0.6,
        mattack = 0.8,
        mdefense = 0.7,
        speed = 1.2,
        luck = 0.5
    },

    -- ⚔️ **Weapon Proficiency (Not Weapon-Reliant)**
    proficiencies = { 
        fists = 2.5,  -- Primary combat method
        staff = 1.8,  -- Secondary weapon
        sword = 1.0,  
        dagger = 1.2, 
        greatsword = 0.3,  
        bow = 0.5, 
        gun = 0.4,  
        magic = 1.0  -- Uses chi instead of traditional magic
    },

    -- 🌿 **Elemental Affinities**
    element_affinities = {
        air = 10,   -- Enhanced reflexes and speed
        earth = 5,  -- Stable stance and endurance
        water = 5,  -- Fluid movement in combat
        fire = -5   -- Struggles in highly aggressive environments
    },

    -- 💥 **Overdrive Abilities**
    overdrive = { 
        "flurry_of_blows", -- Rapid consecutive strikes that ignore armor
        "iron_body", -- Reduces all damage for a short time
        "chi_explosion", -- Releases stored chi energy for a powerful AOE attack
    },

    -- 🎯 **Starting Skills**
    starting_skills = {
        "quick_strike",
        "dodge",
        "inner_focus"
    },

    -- 📈 **Skill Progression**
    skill_tree = {
        tier1 = {
            "spinning_kick",
            "pressure_point",
            "swift_counter"
        },
        tier2 = {
            "dragon_punch",
            "serene_mind", 
            "shadow_step",
            "evasive_dance"
        },
        tier3 = {
            "hundred_fists",
            "chi_mastery",
            "unbreakable_spirit"
        }
    },

    -- 🎒 **Inventory & Carrying Capacity**
    inventory_limit = 80, -- Lower than most classes due to light equipment

    -- ✨ **Special Abilities (Passive Perks)**
    special_abilities = {
        "Inner Strength", -- Gains a power boost when below 50% HP
        "Unshakable Focus", -- Reduces duration of stuns and fear effects
        "Chi Flow", -- Recovers stamina faster than other classes
        "Counter Mastery", -- Increased damage when counter-attacking
        "Evasive Instincts", -- Slightly increases evasion against melee attacks
    },

    -- 🎭 **Prestige Paths**
    prestige_paths = {
        grandmaster = {
            description = "A perfected martial artist who moves with unmatched speed and precision.",
            bonuses = {
                "Increased attack speed and evasion",
                "More chi-based special attacks",
                "Can deflect projectiles with well-timed counters"
            },
            weaknesses = {
                "Still lacks strong defenses",
                "Takes higher damage from magic"
            }
        },

        dragon_fist = {
            description = "A chi-powered warrior who strikes with explosive force, channeling raw energy into attacks.",
            bonuses = {
                "Attacks deal bonus elemental damage",
                "Increased knockback and armor penetration",
                "Can unleash devastating finishing moves"
            },
            weaknesses = {
                "Consumes chi at a rapid rate",
                "Weaker at sustained combat"
            }
        },

        way_of_the_wind = {
            description = "A mobile fighter who prioritizes movement and agility, never staying in one place for long.",
            bonuses = {
                "Can reposition instantly after every attack",
                "Higher dodge chance",
                "Regenerates stamina quickly"
            },
            weaknesses = {
                "Lower overall damage",
                "Can be overwhelmed if cornered"
            }
        }
    },

    -- ⚔️ **Hybrid & Multiclass Options**
    hybrid_options = {
        battle_monk = {
            description = "A Monk who embraces heavier weapons while still relying on chi energy.",
            pros = {
                "Can wield traditional melee weapons effectively",
                "Still retains fast movement speed"
            },
            cons = {
                "Loses some evasion and mobility",
                "Chi abilities are weaker compared to pure Monks"
            }
        },

        chi_sorcerer = {
            description = "A Monk who has fully integrated magic with martial arts, turning chi into arcane power.",
            pros = {
                "Can use magic-based attacks without wands or staves",
                "Increased mana regeneration"
            },
            cons = {
                "Loses some physical attack strength",
                "More vulnerable to anti-magic effects"
            }
        }
    }
}

return Monk
