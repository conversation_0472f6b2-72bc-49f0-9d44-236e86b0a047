-- weather/acid-rain.lua
-- Acid rain weather pattern - dangerous precipitation that damages entities and structures

local AcidRainWeather = {
    id = "acid_rain",
    name = "Acid Rain",
    
    -- Visual properties
    visual = {
        skyColor = {r = 150, g = 170, b = 120}, -- Sickly green tint
        sunIntensity = 0.6,
        cloudCoverage = 0.9,
        cloudColor = {r = 180, g = 190, b = 140}, -- Greenish clouds
        ambientLightLevel = 0.7,
        rainColor = {r = 200, g = 255, b = 150} -- Glowing green rain
    },
    
    -- Environment modifiers
    environment = {
        temperature = 0.9, -- Slightly cooler
        visibility = 0.7, -- Reduced visibility
        humidity = 1.2, -- High humidity
        windStrength = 0.4, -- Moderate wind
        acidLevel = 0.8 -- High acidity
    },
    
    -- Particle systems
    particles = {
        acidRain = {
            enabled = true,
            intensity = 0.8,
            color = {r = 200, g = 255, b = 150},
            size = {min = 2, max = 4},
            speed = {min = 200, max = 300}
        },
        acidMist = {
            enabled = true,
            intensity = 0.4,
            color = {r = 180, g = 220, b = 140},
            size = {min = 1, max = 2},
            speed = {min = 10, max = 20}
        }
    },
    
    -- Sound effects
    sounds = {
        ambient = "acid_rain_ambient",
        impact = "acid_sizzle",
        wind = "toxic_wind",
        volume = 0.7
    },
    
    -- Effects on game entities and tiles
    effects = {
        -- Damage to exposed entities
        exposureDamage = 5, -- Damage per second
        
        -- Corrosion effect on structures
        structureDamage = 2, -- Damage per second
        
        -- Plants take damage
        plantDamageMultiplier = 1.5,
        
        -- Reduced visibility
        visibilityMultiplier = 0.7,
        
        -- Puddles become acidic
        puddleAcidity = 0.8,
        
        -- Equipment degradation rate
        equipmentDegradation = 1.2
    },
    
    -- Transition probabilities to other weather (per game hour)
    transitions = {
        clear = 0.2,
        rain = 0.3,
        cloudy = 0.2,
        foggy = 0.1,
        -- Implied: 0.2 chance to stay acid rain
    },
    
    -- Day/night cycle modifiers
    timeModifiers = {
        dawn = {
            skyColor = {r = 170, g = 190, b = 140},
            ambientLightLevel = 0.6
        },
        day = {
            -- Uses default properties
        },
        dusk = {
            skyColor = {r = 160, g = 180, b = 130},
            ambientLightLevel = 0.5
        },
        night = {
            skyColor = {r = 40, g = 50, b = 35},
            ambientLightLevel = 0.3
        }
    }
}

-- Initialize the weather pattern
function AcidRainWeather.init(world)
    print("Initializing acid rain weather")
    
    -- Set global light level
    world.lightLevel = AcidRainWeather.visual.ambientLightLevel
    
    -- Start ambient sounds
    if AcidRainWeather.sounds.ambient then
        -- Play ambient acid rain sound
        print("Playing sound: " .. AcidRainWeather.sounds.ambient)
    end
    
    -- Create particle systems
    if AcidRainWeather.particles.acidRain.enabled then
        -- Create acid rain particles
        print("Creating acid rain particles with intensity: " .. AcidRainWeather.particles.acidRain.intensity)
    end
    
    if AcidRainWeather.particles.acidMist.enabled then
        -- Create acid mist particles
        print("Creating acid mist particles with intensity: " .. AcidRainWeather.particles.acidMist.intensity)
    end
    
    -- Display warning message to players if messaging system exists
    if world.messageSystem then
        world.messageSystem:broadcast("WARNING: Acid rain detected. Seek shelter immediately.")
    else
        print("WARNING: Acid rain detected. Seek shelter immediately.")
    end
end

-- Update function called every frame
function AcidRainWeather.update(world, dt)
    -- Update day/night cycle based on world time
    local timeOfDay = world.timeOfDay or "day"
    local modifiers = AcidRainWeather.timeModifiers[timeOfDay]
    
    if modifiers then
        -- Apply time-specific modifiers
        if modifiers.skyColor then
            -- Change sky color
        end
        
        if modifiers.ambientLightLevel then
            world.lightLevel = modifiers.ambientLightLevel
        end
    end
    
    -- Apply effects to game world
    for id, entity in pairs(world.entitySystem.entities) do
        -- Check if entity is exposed to acid rain
        if entity.isExposed and not entity.hasAcidProtection then
            -- Apply acid damage
            if entity.health then
                entity.health = entity.health - (AcidRainWeather.effects.exposureDamage * dt)
            end
            
            -- Apply equipment degradation
            if entity.equipment then
                for _, item in pairs(entity.equipment) do
                    if item.durability then
                        item.durability = item.durability - (AcidRainWeather.effects.equipmentDegradation * dt)
                    end
                end
            end
        end
        
        -- Special effects for different entity types
        if entity.categories then
            -- Damage to plants
            if table.contains(entity.categories, "plant") then
                if entity.health then
                    entity.health = entity.health - 
                        (AcidRainWeather.effects.exposureDamage * 
                         AcidRainWeather.effects.plantDamageMultiplier * dt)
                end
            end
            
            -- Damage to structures
            if table.contains(entity.categories, "structure") then
                if entity.health and not entity.hasAcidProtection then
                    entity.health = entity.health - (AcidRainWeather.effects.structureDamage * dt)
                end
            end
            
            -- Effect on robots
            if table.contains(entity.categories, "robot") then
                -- Robots take less damage but still degrade
                if entity.health then
                    entity.health = entity.health - (AcidRainWeather.effects.exposureDamage * 0.5 * dt)
                end
            end
        end
    end
    
    -- Update puddles
    for _, puddle in pairs(world.getPuddles()) do
        puddle.acidity = AcidRainWeather.effects.puddleAcidity
    end
    
    -- Random acid sizzle sounds
    if math.random() < 0.1 then
        -- Play acid impact sound at random location
        print("Playing sound: " .. AcidRainWeather.sounds.impact)
    end
end

-- Clean up when weather changes
function AcidRainWeather.cleanUp(world)
    -- Stop ongoing sounds and particle effects
    print("Acid rain weather ending")
    
    -- Notify players if messaging system exists
    if world.messageSystem then
        world.messageSystem:broadcast("Acid rain clearing. Air quality returning to normal levels.")
    else
        print("Acid rain clearing. Air quality returning to normal levels.")
    end
    
    -- Reset puddle acidity
    if world.getPuddles then
        for _, puddle in pairs(world.getPuddles()) do
            puddle.acidity = 0
        end
    end
end

-- Table contains helper function
function table.contains(table, element)
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

return AcidRainWeather 