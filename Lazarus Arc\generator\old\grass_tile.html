<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Isometric Grass Tile Generator</title>
  <style>
    body { background: #222; display: flex; justify-content: center; align-items: center; height: 100vh; }
    canvas { image-rendering: pixelated; border: 2px solid #555; }
  </style>
</head>
<body>
<canvas id="isoCanvas" width="256" height="128"></canvas>
<script>
const canvas = document.getElementById('isoCanvas');
const ctx = canvas.getContext('2d');
const pixelSize = 4; // Scaled for visibility
const isoWidth = 64;
const isoHeight = 32;

// Grass palette
const grassColors = ['#6AAA64', '#7BB661', '#82C55F', '#5D8C3E'];

// Generate procedural grass texture
function generateGrassTexture(width, height) {
  const texture = [];
  for(let y = 0; y < height; y++) {
    texture[y] = [];
    for(let x = 0; x < width; x++) {
      texture[y][x] = grassColors[Math.floor(Math.random() * grassColors.length)];
    }
  }
  return texture;
}

// Render isometric tile
function renderIsoTile(texture) {
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  const offsetX = canvas.width / 2;
  const offsetY = 0;
  
  for(let y = 0; y < isoHeight; y++) {
    for(let x = 0; x < isoWidth; x++) {
      const screenX = (x - y) * (pixelSize / 2) + offsetX;
      const screenY = (x + y) * (pixelSize / 4) + offsetY;

      ctx.fillStyle = texture[y][x];
      ctx.fillRect(screenX, screenY, pixelSize / 2, pixelSize / 2);
    }
  }
}

// Main function
generateAndRender();

// Generate and render new grass tile
function generateAndRender() {
  const grassTexture = generateGrassTexture(isoWidth, isoHeight);
  renderIsoTile(grassTexture);
}

// Regenerate tile on click
canvas.onclick = generateAndRender;
</script>
</body>
</html>
