local WarRaid = {
    id = "war_raid",
    name = "War Raid", -- Or "Revolt Raid" depending on the scenario
    location = {x = 0, y = 0},
    currentStage = 0,
    stages = {
        -- Stage 1: Skirmish
        {
            name = "Skirmish",
            duration = 60,
            objectives = {
                "Secure the forward positions",
            },
            enemies = {
                {type = "soldier_a", count = 15}, -- Faction A soldier
                {type = "soldier_b", count = 10}, -- Faction B soldier
            },
        },
        -- Stage 2: Frontline Battle
        {
            name = "Frontline Battle",
            duration = 90,
            objectives = {
                "Push the enemy back",
                "Capture the supply depot",
            },
            enemies = {
                {type = "soldier_a", count = 20},
                {type = "soldier_b", count = 20},
                {type = "elite_soldier_a", count = 5}, -- Stronger variant
                {type = "elite_soldier_b", count = 5},
            },
        },
        -- Stage 3: The Commander
        {
            name = "The Commander",
            duration = 120,
            objectives = {
                "Defeat the enemy commander",
            },
            enemies = {
                {type = "elite_soldier_a", count = 10},
                {type = "elite_soldier_b", count = 10},
                {type = "war_commander", count = 1}, -- Boss
            },
        },
    },
    timer = 0,
    isActive = false,
    -- Raid-specific data
    factionALeading = true, -- Or false, to track which side is winning
}

function WarRaid.init(config)
    for k, v in pairs(config or {}) do
        WarRaid[k] = v
    end
    WarRaid.timer = 0
    WarRaid.currentStage = 0
    WarRaid.isActive = true
    WarRaid.factionALeading = true -- Or initialize based on the scenario
end

function WarRaid.update(world, dt)
    if not WarRaid.isActive then
        return
    end

    WarRaid.timer = WarRaid.timer + dt
    local currentStage = WarRaid.stages[WarRaid.currentStage + 1]

    if currentStage then
        WarRaid.spawnEnemies(world, currentStage.enemies)

        -- Stage progression logic
        if WarRaid.timer >= currentStage.duration then
            WarRaid.currentStage = WarRaid.currentStage + 1
            WarRaid.timer = 0
            print("Stage " .. WarRaid.currentStage .. " complete!")
        end

        -- Stage-specific logic
        if WarRaid.currentStage == 2 then
            WarRaid.updateFrontline()
        end
        if WarRaid.currentStage == 3 then
            WarRaid.updateCommander()
        end

        WarRaid.updateFactionAdvantage()
    else
        WarRaid.isActive = false
        print("War Raid complete!")
    end
end

function WarRaid.spawnEnemies(world, enemies)
    for _, enemyGroup in ipairs(enemies) do
        for i = 1, enemyGroup.count do
            local entity = world.entityFactory:create(enemyGroup.type)
            if entity then
                entity.position = {
                    x = WarRaid.location.x + math.random(-5, 5),
                    y = WarRaid.location.y + math.random(-5, 5),
                }
                world.entitySystem:addEntity(entity)
            end
        end
    end
end

function WarRaid.updateFrontline()
    -- Logic for frontline mechanics (e.g., capturing points, pushing the line)
    print("The battle rages on the frontline!")
end

function WarRaid.updateCommander()
    -- Logic for the commander's abilities or behavior
    print("The enemy commander rallies their troops!")
end

function WarRaid.updateFactionAdvantage()
    -- Example: Change which faction is leading based on kills or objectives
    -- This is a placeholder; you'll need more complex logic
    if math.random(1, 100) < 5 then -- 5% chance to change
        WarRaid.factionALeading = not WarRaid.factionALeading
        local leadingFaction = WarRaid.factionALeading and "Faction A" or "Faction B"
        print(leadingFaction .. " is gaining ground!")
    end
end

return WarRaid