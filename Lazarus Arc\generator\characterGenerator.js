import SpriteGenerator from './spriteGenerator.js';

class CharacterGenerator extends SpriteGenerator {
    constructor(canvas) {
        super(canvas);
        this.currentPart = 'head';
        this.bodyStyle = 'basic';
        this.armorType = 'none';
        this.faceStyle = 'normal';
    }

    // Set current body part
    setCurrentPart(part) {
        this.currentPart = part;
        this.drawBaseShape();
    }

    // Set body style
    setBodyStyle(style) {
        this.bodyStyle = style;
        this.drawBaseShape();
    }

    // Set armor type
    setArmorType(type) {
        this.armorType = type;
        this.drawBaseShape();
    }

    // Set face style
    setFaceStyle(style) {
        this.faceStyle = style;
        this.drawBaseShape();
    }

    // Draw base shape
    drawBaseShape() {
        this.ctx.clearRect(0, 0, this.size, this.size);
        
        // Draw character based on current part
        switch (this.currentPart) {
            case 'head':
                this.drawHead();
                break;
            case 'body':
                this.drawBody();
                break;
            case 'arms':
                this.drawArms();
                break;
            case 'legs':
                this.drawLegs();
                break;
        }
    }

    // Draw head
    drawHead() {
        // Head shape
        this.ctx.fillStyle = this.currentPalette[0];
        this.ctx.beginPath();
        this.ctx.arc(this.size * 0.5, this.size * 0.4, this.size * 0.2, 0, Math.PI * 2);
        this.ctx.fill();
        
        // Face
        this.ctx.fillStyle = this.currentPalette[1];
        this.drawFace();
    }

    // Draw face
    drawFace() {
        switch (this.faceStyle) {
            case 'normal':
                // Eyes
                this.ctx.fillStyle = this.currentPalette[2];
                this.ctx.beginPath();
                this.ctx.arc(this.size * 0.4, this.size * 0.35, this.size * 0.03, 0, Math.PI * 2);
                this.ctx.arc(this.size * 0.6, this.size * 0.35, this.size * 0.03, 0, Math.PI * 2);
                this.ctx.fill();
                
                // Mouth
                this.ctx.beginPath();
                this.ctx.arc(this.size * 0.5, this.size * 0.45, this.size * 0.05, 0, Math.PI);
                this.ctx.fill();
                break;
        }
    }

    // Draw body
    drawBody() {
        // Body shape
        this.ctx.fillStyle = this.currentPalette[0];
        this.ctx.beginPath();
        this.ctx.moveTo(this.size * 0.3, this.size * 0.4);
        this.ctx.lineTo(this.size * 0.7, this.size * 0.4);
        this.ctx.lineTo(this.size * 0.7, this.size * 0.8);
        this.ctx.lineTo(this.size * 0.3, this.size * 0.8);
        this.ctx.closePath();
        this.ctx.fill();
        
        // Armor if applicable
        if (this.armorType !== 'none') {
            this.drawArmor();
        }
    }

    // Draw armor
    drawArmor() {
        this.ctx.fillStyle = this.currentPalette[1];
        this.ctx.strokeStyle = this.currentPalette[2];
        this.ctx.lineWidth = 2;
        
        // Draw armor details
        this.ctx.beginPath();
        this.ctx.moveTo(this.size * 0.35, this.size * 0.45);
        this.ctx.lineTo(this.size * 0.65, this.size * 0.45);
        this.ctx.moveTo(this.size * 0.35, this.size * 0.55);
        this.ctx.lineTo(this.size * 0.65, this.size * 0.55);
        this.ctx.stroke();
    }

    // Draw arms
    drawArms() {
        // Left arm
        this.ctx.fillStyle = this.currentPalette[0];
        this.ctx.fillRect(this.size * 0.2, this.size * 0.4, this.size * 0.15, this.size * 0.4);
        
        // Right arm
        this.ctx.fillRect(this.size * 0.65, this.size * 0.4, this.size * 0.15, this.size * 0.4);
    }

    // Draw legs
    drawLegs() {
        // Left leg
        this.ctx.fillStyle = this.currentPalette[0];
        this.ctx.fillRect(this.size * 0.35, this.size * 0.8, this.size * 0.15, this.size * 0.2);
        
        // Right leg
        this.ctx.fillRect(this.size * 0.5, this.size * 0.8, this.size * 0.15, this.size * 0.2);
    }
}

export default CharacterGenerator; 