-- entities/green_dragon.lua
-- Green dragon with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local GreenDragon = {
    id = "green_dragon",
    name = "Green Dragon",
    type = "green_dragon",
    shape = {
        {0, -20}, {15, -15}, {25, 0}, {15, 15},
        {0, 20}, {-15, 15}, {-25, 0}, {-15, -15}
    },
    size = 25,

    -- Entity categories
    categories = {"dragon", "flying", "poison", "forest", "cunning"},
    threatCategories = {"player", "knight", "treasure_hunter", "dragon_slayer"},
    
    -- Base stats (cunning forest dragon)
    maxHealth = 700,
    health = 700,
    maxStamina = 280,
    stamina = 280,
    maxMana = 450,
    mana = 450,
    speed = 2.8,
    attack = 50,
    defense = 45,  -- Higher defense than other dragons
    magicAttack = 55,
    magicDefense = 55,
    poisonResistance = 100,
    fireVulnerability = 1.2,
    flight_altitude = 35,
    forest_mastery = 30,
    cunning = 25,
    
    -- Behaviors
    behaviors = {"forest_ambush", "poison_breath", "nature_magic", "cunning_tactics"},
    behaviorConfigs = {
        forest_ambush = {
            camouflage = 2.5,
            ambushBonus = 3.0,
            forestStealth = true,
            patientHunter = true
        },
        poison_breath = {
            breathRange = 25,
            poisonDamage = 2.0,
            poisonDuration = 15,
            acidicProperties = true,
            lingering_cloud = 10
        },
        nature_magic = {
            plantControl = 3.0,
            forestCommunion = true,
            naturalHealing = 2.0,
            druidicMagic = 2.5
        },
        cunning_tactics = {
            strategicThinking = 3.0,
            trapSetting = true,
            psychologicalWarfare = 2.0,
            deception = 2.5
        }
    },
    
    -- Enhanced variant system for green dragons
    variantChances = {
        normal = 0.50,          -- 50% young green dragon
        shiny = 0.35,           -- 35% adult green dragon (shiny)
        rare = 0.13,            -- 13% ancient green dragon (rare)
        legendary = 0.02        -- 2% forest lord dragon (legendary)
    },
    
    variants = {
        normal = {
            name = "Young Green Dragon",
            description = "A cunning young dragon that lurks in forest depths",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.4, 1.0, 0.5, 1.0}  -- Forest green scales
            }
        },
        
        shiny = {
            name = "Adult Green Dragon",
            description = "A mature forest dragon with mastery over poison and nature",
            statModifiers = {
                maxHealth = 1.4,    -- 980 health
                defense = 1.4,      -- 63 defense
                magicAttack = 1.5,  -- 82.5 magic attack
                cunning = 1.8,      -- Enhanced cunning
                poison_mastery = 2.0,
                forest_dominion = 2.5
            },
            appearanceModifiers = {
                scale = 1.3,
                glow = true,
                colorTint = {0.3, 1.2, 0.4, 1.0},  -- Deep emerald with nature aura
                poison_aura = true,
                forest_camouflage = true,
                vine_growth = true
            },
            soundModifiers = {
                pitch = 0.8,
                volume = 1.2,
                natural = true
            }
        },
        
        rare = {
            name = "Ancient Green Dragon",
            description = "An ancient wyrm that has become one with the forest itself",
            statModifiers = {
                maxHealth = 2.0,    -- 1400 health
                defense = 1.8,      -- 81 defense
                magicAttack = 2.0,  -- 110 magic attack
                cunning = 2.5,      -- Master tactician
                poison_mastery = 3.5,
                nature_unity = 4.0,
                ancient_wisdom = 5.0
            },
            appearanceModifiers = {
                scale = 1.6,
                colorTint = {0.2, 1.1, 0.3, 1.0},  -- Ancient forest green
                living_forest = true,
                moss_covered = true,
                tree_integration = true,
                ancient_nature = true
            },
            soundModifiers = {
                pitch = 0.6,
                volume = 1.4,
                reverb = true,
                forest_whispers = true
            }
        },
        
        legendary = {
            name = "Forest Lord Dragon",
            description = "A legendary dragon that embodies the spirit of all forests",
            statModifiers = {
                maxHealth = 2.8,    -- 1960 health
                defense = 2.2,      -- 99 defense
                magicAttack = 2.5,  -- 137.5 magic attack
                cunning = 3.0,      -- Ultimate strategist
                poison_mastery = 5.0,
                forest_lordship = 10.0,
                nature_embodiment = 5.0
            },
            appearanceModifiers = {
                scale = 2.0,
                glow = true,
                colorTint = {0.1, 1.3, 0.2, 1.0},  -- Primordial forest green
                world_tree_connection = true,
                forest_crown = true,
                nature_aura = "legendary",
                ecosystem_integration = true
            },
            soundModifiers = {
                pitch = 0.4,
                volume = 1.6,
                reverb = true,
                echo = true,
                forest_lord_harmony = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "green_dragon_scale", chance = 1.0, quantity = {5, 10}},
        {id = "dragon_blood", chance = 0.9, quantity = {2, 4}},
        {id = "poison_fang", chance = 0.8, quantity = {1, 2}},
        {id = "nature_essence", chance = 1.0, quantity = {3, 6}},
        {id = "cunning_heart", chance = 0.7, quantity = {1, 1}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "forest_dragon_scale", chance = 1.0, quantity = {6, 12}},
            {id = "poison_mastery_gland", chance = 0.9, quantity = {1, 1}},
            {id = "forest_dominion_crystal", chance = 0.8, quantity = {1, 1}},
            {id = "cunning_essence", chance = 0.7, quantity = {1, 2}}
        },
        rare = {
            {id = "ancient_forest_scale", chance = 1.0, quantity = {8, 15}},
            {id = "nature_unity_crystal", chance = 0.9, quantity = {1, 2}},
            {id = "forest_magic_core", chance = 0.8, quantity = {1, 1}},
            {id = "living_wood_fragment", chance = 0.7, quantity = {2, 4}},
            {id = "ancient_poison_essence", chance = 0.8, quantity = {2, 4}}
        },
        legendary = {
            {id = "forest_lord_scale", chance = 1.0, quantity = {10, 20}},
            {id = "nature_embodiment_crown", chance = 0.95, quantity = {1, 1}},
            {id = "forest_lordship_orb", chance = 0.9, quantity = {1, 1}},
            {id = "world_tree_connection_essence", chance = 0.8, quantity = {1, 2}},
            {id = "nature_mastery_codex", chance = 0.7, quantity = {1, 1}},
            {id = "ecosystem_control_scepter", chance = 0.6, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "green_dragon",
        scale = 1.8,
        animations = {
            "forest_glide", "poison_breath", "nature_roar", "camouflage", "forest_magic"
        },
        variants = {
            "young_green", "adult_green", "ancient_green", "forest_lord"
        }
    },
    
    -- Sound effects with forest characteristics
    sounds = {
        nature_roar = {
            file = "green_dragon_roar",
            synth = {
                instrument = "organ",
                notes = {"F1", "C2", "F2", "C3"},
                durations = {1.3, 1.1, 1.4, 1.7},
                volume = 1.0,
                forest_echo = true
            }
        },
        poison_breath = {
            file = "poison_breath",
            synth = {
                instrument = "synthesizer",
                notes = {"G2", "Bb2", "D3", "G3"},
                durations = {0.6, 0.5, 0.7, 0.8},
                volume = 0.8,
                toxic = true
            }
        },
        forest_whisper = {
            file = "forest_whisper",
            synth = {
                instrument = "flute",
                notes = {"A3", "C4", "E4", "A4"},
                durations = {0.8, 0.6, 0.8, 1.0},
                volume = 0.6,
                mystical = true
            }
        },
        cunning_laugh = {
            file = "cunning_laugh",
            synth = {
                instrument = "xylophone",
                notes = {"D4", "F4", "A4", "D5"},
                durations = {0.3, 0.2, 0.3, 0.4},
                volume = 0.7,
                sinister = true
            }
        }
    },
    
    -- Special forest dragon abilities
    abilities = {
        poison_breath = {
            type = "active",
            description = "Toxic breath that creates lingering poison clouds",
            effect = "poison_breath_attack",
            cooldown = 12
        },
        forest_camouflage = {
            type = "active",
            description = "Becomes nearly invisible in forest environments",
            effect = "forest_stealth",
            duration = 10,
            cooldown = 20
        },
        nature_magic = {
            type = "active",
            description = "Controls plants and forest creatures",
            effect = "nature_control",
            manaCost = 60,
            cooldown = 15
        },
        cunning_tactics = {
            type = "passive",
            description = "Uses superior intelligence to outmaneuver enemies",
            effect = "tactical_advantage"
        },
        poison_immunity = {
            type = "passive",
            description = "Complete immunity to all toxins and diseases",
            effect = "poison_immunity"
        },
        forest_communion = {
            type = "passive",
            description = "Can communicate with and command forest life",
            effect = "nature_communication"
        }
    }
}

-- Initialize the green dragon entity using enhanced template
function GreenDragon.init(entity, world)
    -- Copy all fields from GreenDragon template to entity instance
    for k, v in pairs(GreenDragon) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return GreenDragon
