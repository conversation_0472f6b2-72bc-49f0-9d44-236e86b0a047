-- classes/paladin.lua
-- Paladin class definition for Lazarus Arc

local Paladin = {
    name = "<PERSON>lad<PERSON>",
    description = "A holy warrior combining martial prowess with divine magic for healing and protection.",
    icon = "paladin_icon.png",

    -- 🎭 **Playstyle & Trade-offs**
    pros = {
        "Can self-heal and heal allies",
        "Strong resistance to curses and debuffs",
        "Balanced offense and defense"
    },
    cons = {
        "Not as durable as a pure tank",
        "Heavily reliant on mana for skills",
        "Struggles against fast opponents"
    },

    -- 🏋 **Base Stats & Growth**
    base_stats = { 
        health = 110, 
        maxHealth = 110,
        stamina = 60, 
        maxStamina = 60,
        mana = 60, 
        maxMana = 60,
        pattack = 10, 
        pdefense = 10, 
        mattack = 10, 
        mdefense = 10, 
        speed = 4, 
        luck = 3 
    },

    stat_growth = {
        health = 11,
        stamina = 5,
        mana = 5,
        pattack = 0.8,
        pdefense = 1.0,
        mattack = 0.8,
        mdefense = 1.0,
        speed = 0.3,
        luck = 0.2
    },

    -- ⚔️ **Weapon Proficiency**
    proficiencies = { 
        sword = 1.2, 
        mace = 1.3,
        greatsword = 1.0, 
        hammer = 1.1,
        bow = 0.7, 
        gun = 0.6, 
        magic = 1.5 
    },

    -- 🌿 **Elemental Affinities**
    element_affinities = {
        light = 15, -- Natural resistance to darkness
        fire = 5,   -- Righteous flames
        dark = -10  -- Weakened by dark magic
    },

    -- 💥 **Overdrive Abilities**
    overdrive = { 
        "divine_barrier", -- Grants full immunity for a short duration
        "holy_wrath", -- AOE light damage and stun
        "heaven's_blessing" -- Fully restores mana and heals allies
    },

    -- 🎯 **Starting Skills**
    starting_skills = {
        "holy_strike",
        "divine_protection",
        "healing_light"
    },

    -- 📈 **Skill Progression**
    skill_tree = {
        tier1 = {
            "smite",
            "lay_on_hands",
            "consecration"
        },
        tier2 = {
            "divine_hammer",
            "holy_shield", 
            "purify",
            "rebuke"
        },
        tier3 = {
            "divine_intervention",
            "holy_nova",
            "resurrection"
        }
    },

    -- 🎒 **Inventory & Carrying Capacity**
    inventory_limit = 110, -- Moderate carrying capacity

    -- ✨ **Special Abilities (Passive Perks)**
    special_abilities = {
        "Divine Favor", -- Spells have a small chance to cost no mana
        "Holy Resilience", -- Reduced damage taken when below 30% HP
        "Aura of Light", -- Slightly increases party members' stats
        "Judgment", -- Attacks deal additional damage to undead/demonic enemies
        "Faith Shield", -- Auto-casts a small barrier when critically hit
    },

    -- 🎭 **Prestige Paths**
    prestige_paths = {
        crusader = {
            description = "A battle-hardened holy knight, wielding both blade and faith.",
            bonuses = {
                "Bonus damage against undead",
                "Can reflect damage back to attackers",
                "Stronger in group combat"
            },
            weaknesses = {
                "Less effective against magic-resistant enemies",
                "Slower attack speed"
            }
        },

        templar = {
            description = "A divine protector specializing in shielding magic.",
            bonuses = {
                "Can create barriers to block attacks",
                "Takes reduced damage from spells",
                "Can cleanse negative effects"
            },
            weaknesses = {
                "Lower raw damage output",
                "Limited ranged abilities"
            }
        },

        avenger = {
            description = "A righteous warrior who forsakes healing for divine retribution.",
            bonuses = {
                "Massive damage increase against evil-aligned enemies",
                "Light-infused attacks bypass armor",
                "Can sacrifice health to deal extra damage"
            },
            weaknesses = {
                "No access to healing skills",
                "More vulnerable to debuffs"
            }
        }
    },

    -- ⚔️ **Hybrid & Multiclass Options**
    hybrid_options = {
        battle_cleric = {
            description = "A Paladin who deepens their connection to divine magic, sacrificing melee strength for healing mastery.",
            pros = {
                "Access to high-level healing spells",
                "Can restore allies even in battle"
            },
            cons = {
                "Lower physical damage output",
                "Mana-dependent for effectiveness"
            }
        },

        radiant_warrior = {
            description = "A fusion of martial prowess and magical energy, enhancing melee attacks with divine magic.",
            pros = {
                "Can imbue weapons with light-based damage",
                "Balanced attack and spellcasting"
            },
            cons = {
                "Jack of all trades, master of none",
                "Requires high mana management"
            }
        }
    }
}

return Paladin
