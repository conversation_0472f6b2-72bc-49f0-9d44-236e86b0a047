-- tiles/thin_ice_patch.lua
local ThinIcePatchTile = {
    id = "thin_ice_patch",
    name = "Thin Ice Patch",
    passable = true,
    variants = 4, -- Different patch patterns
    
    -- Properties
    properties = {
        thickness = 0.2, -- Even thinner than thin_ice
        temperature = -3,
        cracking = false,
        hasFish = true,
        hasCracks = true, -- Always starts with cracks
        weightLimit = 30, -- Lower weight limit than thin_ice
        lastCrack = 0,
        patchSize = 1, -- Size of the patch
        isBreaking = false
    },
    
    -- Visual properties
    visual = {
        base_color = {0.7, 0.8, 0.9}, -- Lighter blue than thin_ice
        variants = {
            {name = "small_patch", type = "small"},
            {name = "medium_patch", type = "medium"},
            {name = "large_patch", type = "large"},
            {name = "breaking_patch", type = "breaking"}
        },
        crack_patterns = {
            {name = "spider_crack", type = "major"},
            {name = "fractured", type = "critical"},
            {name = "breaking", type = "breaking"}
        }
    },
    
    -- Sound properties
    sounds = {
        ambient = {
            "ice_creaking",
            "ice_cracking",
            "water_splashing"
        },
        footsteps = {
            "ice_crack",
            "ice_break"
        }
    },
    
    -- Resources
    resources = {
        fish = {
            amount = 1,
            regenRate = 0.05,
            harvestAmount = 1
        }
    },
    
    -- Effects
    effects = {
        cracking = {
            active = true,
            progress = 0.5, -- Starts halfway to breaking
            threshold = 0.8
        },
        melting = {
            active = false,
            rate = 0.2 -- Faster melting than thin_ice
        }
    },
    
    -- Spawn rules
    spawnRules = {
        fish = {
            chance = 0.3,
            minDistance = 3,
            maxDensity = 0.2
        }
    },
    
    -- Weather effects
    weatherEffects = {
        snow = function(tile)
            -- Snow can temporarily strengthen the patch
            tile.properties.thickness = math.min(0.4, tile.properties.thickness + 0.05)
            return "ice_thicken"
        end,
        rain = function(tile)
            -- Rain quickly weakens the patch
            tile.properties.thickness = math.max(0, tile.properties.thickness - 0.3)
            if tile.properties.thickness < 0.1 then
                tile.properties.isBreaking = true
                return "ice_break"
            end
            return "ice_weaken"
        end,
        heat = function(tile)
            -- Heat rapidly melts the patch
            tile.properties.thickness = math.max(0, tile.properties.thickness - 0.4)
            if tile.properties.thickness <= 0 then
                return "ice_melt"
            end
            return "ice_thin"
        end
    },
    
    -- Time effects
    timeEffects = {
        day = function(tile)
            -- Daytime rapidly weakens the patch
            if tile.properties.temperature > 0 then
                tile.properties.thickness = math.max(0, tile.properties.thickness - 0.2)
                if tile.properties.thickness < 0.1 then
                    tile.properties.isBreaking = true
                    return "ice_break"
                end
                return "ice_thin"
            end
            return nil
        end,
        night = function(tile)
            -- Nighttime can slightly strengthen the patch
            if tile.properties.temperature < 0 then
                tile.properties.thickness = math.min(0.4, tile.properties.thickness + 0.05)
                return "ice_thicken"
            end
            return nil
        end
    }
}

-- Initialize the tile
function ThinIcePatchTile.init(tile, world)
    -- Initialize properties first
    tile.properties = tile.properties or {}
    
    -- Copy all fields from ThinIcePatchTile template to tile instance
    for k, v in pairs(ThinIcePatchTile) do
        if type(v) ~= "function" and tile[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                tile[k] = {}
                for subk, subv in pairs(v) do
                    tile[k][subk] = subv
                end
            else
                tile[k] = v
            end
        end
    end
    
    -- Set random variant
    if tile.visual and tile.visual.variants then
        local variants = tile.visual.variants
        tile.currentVariant = variants[math.random(#variants)]
    end
    
    -- Initialize resources
    if tile.resources then
        for resourceType, resource in pairs(tile.resources) do
            resource.currentAmount = resource.amount
            resource.lastHarvest = 0
            resource.growthStage = "mature"
        end
    end
    
    return tile
end

return ThinIcePatchTile 