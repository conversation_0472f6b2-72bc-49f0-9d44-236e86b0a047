local Owl = {
    id = "owl",
    name = "Owl",
    type = "owl",

    -- Entity categories
    categories = {"animal", "predator", "bird", "flying"},

    -- Threat and food categories
    threatCategories = {"monster", "player"},
    foodCategories = {"small_prey", "rodent"},

    -- Stats
    maxHealth = 40,
    health = 40,
    maxStamina = 60,
    stamina = 60,
    speed = 2.5,

    -- Flight properties
    flight = {
        maxHeight = 10,
        minHeight = 2,
        ascentSpeed = 1.0,
        descentSpeed = 1.5,
        hoverHeight = 5,
        currentHeight = 5
    },

    -- Behaviors
    behaviors = {"hunt", "perch", "roost", "patrol"},

    -- Behavior configurations
    behaviorConfigs = {
        hunt = {
            moveSpeed = 3.0,
            huntRadius = 15,
            diveSpeed = 4.0,
            attackRange = 1.0,
            attackDamage = 12,
            attackCooldown = 1.0,
            preferredTargets = {"mouse", "rabbit", "field_mouse"},
            nightVision = true
        },
        perch = {
            moveSpeed = 1.0,
            perchDuration = {30, 60},
            perchHeight = {3, 8},
            alertRadius = 10
        },
        roost = {
            startTime = "dusk",
            endTime = "dawn",
            roostHeight = {5, 12},
            healthRegen = 0.05,
            staminaRegen = 0.1
        },
        patrol = {
            moveSpeed = 2.0,
            patrolRadius = 20,
            patrolHeight = {4, 8},
            changeDirectionChance = 0.1
        }
    },

    -- Special abilities
    abilities = {
        silentFlight = {
            detectionReduction = 0.7,
            duration = 5,
            cooldown = 8
        },
        talonGrip = {
            damageMultiplier = 1.5,
            range = 1.5,
            cooldown = 4
        },
        hoot = {
            range = 15,
            duration = 2,
            cooldown = 10,
            effect = "alert"
        }
    },

    -- Appearance
    appearance = {
        sprite = "owl",
        scale = 1.0,
        animations = {
            "idle", "fly", "dive", "perch", "roost", "hoot"
        },
        variants = {
            "barn", "great_horned", "snowy", "brown"
        }
    },

    -- Sound effects with synth configuration
    sounds = {
        hoot = {
            file = "owl_hoot",
            synth = {
                instrument = "cello",
                notes = {"C3", "G3", "C3"},
                durations = {0.4, 0.3, 0.5},
                volume = 0.4,
                vibrato = true,
                vibratoRate = 3.0
            }
        },
        screech = {
            file = "owl_screech",
            synth = {
                instrument = "electric_guitar",
                notes = {"F4", "D4", "A3"},
                durations = {0.3, 0.3, 0.4},
                volume = 0.5,
                vibrato = true,
                vibratoRate = 8.0
            }
        },
        wingFlap = {
            file = "owl_wing_flap",
            synth = {
                instrument = "marimba",
                notes = {"F3", "G3", "F3"},
                durations = {0.15, 0.1, 0.15},
                volume = 0.25
            }
        },
        footstep = {
            synth = {
                instrument = "kalimba",
                notes = {"E3"},
                duration = 0.06,
                volume = 0.12
            }
        },
        fly = {
            synth = {
                instrument = "harmonica",
                notes = {"G3", "C4"},
                durations = {0.25, 0.25},
                volume = 0.3
            }
        },
        hurt = {
            synth = {
                instrument = "cello",
                notes = {"D#3"},
                duration = 0.4,
                volume = 0.5,
                vibrato = true,
                vibratoRate = 6.0
            }
        },
        death = {
            synth = {
                instrument = "bass_guitar",
                notes = {"C3", "A2", "F2", "C2"},
                durations = {0.5, 0.5, 0.5, 1.0},
                volume = 0.5,
                vibrato = true,
                vibratoRate = 1.5
            }
        },
        hunt = {
            synth = {
                instrument = "cello",
                notes = {"G3", "E3"},
                durations = {0.2, 0.3},
                volume = 0.35
            }
        }
    },

    -- Loot drops
    drops = {
        {id = "meat", chance = 0.8, quantity = {1, 2}},
        {id = "feather", chance = 0.7, quantity = {2, 4}},
        {id = "talon", chance = 0.4, quantity = {1, 2}},
        {id = "owl_pellet", chance = 0.6, quantity = {1, 1}}
    }
}

-- Initialize the entity
function Owl.init(entity, world)
    -- Copy all fields from Owl template to entity instance
    for k, v in pairs(Owl) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random owl variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

return Owl