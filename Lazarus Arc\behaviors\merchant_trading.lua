-- behaviors/merchant_trading.lua
-- Merchant behavior for NPCs specifically involved in regular trading and shopkeeping

local MerchantBehavior = {}
MerchantBehavior.id = "merchant"
MerchantBehavior.name = "Merchant/Trader Behavior"

-- Configuration parameters
MerchantBehavior.defaultConfig = {
    tradingRadius = 8,              -- Interaction radius with customers
    inventory = {},                 -- Items available for sale
    dynamicPricing = true,          -- Adjust prices based on supply and demand
    priceFluctuation = 0.2,         -- Maximum percentage price fluctuation
    negotiationSkill = 0.6,         -- Negotiation skill level
    restockInterval = {300, 600},   -- Time interval between restocking (seconds)
    restockQuantity = {1, 4},       -- Number of items restocked each interval
    customerSatisfactionImpact = true, -- Customer satisfaction affects prices
    negotiationChance = 0.7,        -- Chance entity will negotiate prices
    refuseOfferChance = 0.15,       -- Chance to refuse an offer outright
    idleBehavior = true,            -- Perform idle actions when not trading
    idleDuration = {2, 5}           -- Idle duration between interactions
}

-- Initialize the behavior
function MerchantBehavior.init(entity, customConfig)
    entity.merchantState = entity.tradingState or {
        currentCustomer = nil,
        isNegotiating = false,
        config = {}
    }

    local config = entity.merchantState.config
    -- Merge default configuration values
    for k, v in pairs(MerchantBehavior.defaultConfig) do
        config[k] = v
    end

    -- Override with custom configuration if provided
    if customConfig then
        for k, v in pairs(customConfig) do
            config[k] = v
        end
    end

    -- Now set the inventory refresh timer using the merged configuration
    entity.merchantState.inventoryRefreshTimer = math.random(config.restockInterval[1], config.restockInterval[2])
end

-- Update merchant behavior
function MerchantBehavior.update(entity, world, dt)
    local state = entity.merchantState
    local config = state.config

    -- Restock inventory periodically
    state.inventoryRestockTimer = state.inventoryRestockTimer - dt
    if state.inventoryRestockTimer <= 0 then
        MerchantBehavior.restockInventory(entity)
        state.inventoryRestockTimer = math.random(config.restockInterval[1], config.restockInterval[2])
    end

    -- Handle trading interactions
    local customer = MerchantBehavior.findCustomer(entity, world)
    if customer then
        if math.random() < config.negotiationChance then
            MerchantBehavior.negotiate(entity, customer)
        elseif math.random() > config.refuseOfferChance then
            MerchantBehavior.completeTrade(entity, customer)
        end
    elseif config.idleBehavior then
        MerchantBehavior.performIdle(entity)
    end
end

-- Restock inventory
function MerchantBehavior.restockInventory(entity)
    -- Implementation details...
end

-- Find trading partner (alias for findCustomer)
function MerchantBehavior.findCustomer(entity, world)
    -- Implementation details...
end

-- Negotiate prices
function MerchantBehavior.negotiate(entity, customer)
    -- Implementation details...
end

-- Complete trade
function MerchantBehavior.completeTrade(entity, customer)
    -- Implementation details...
end

-- Idle behavior when not trading
function MerchantBehavior.performIdle(entity)
    -- Implementation details...
end

return MerchantBehavior
