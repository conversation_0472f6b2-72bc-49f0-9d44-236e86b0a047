-- tiles/mountain_peak.lua
local MountainPeakTile = {
    id = "mountain_peak",
    name = "Mountain Peak",
    passable = true,
    variants = 4, -- Different peak patterns
    
    -- Properties
    properties = {
        height = 1.0,
        temperature = -15,
        windStrength = 0.9,
        snowCover = 0.9,
        hasClouds = true,
        hasIce = true,
        hasRocks = true,
        hasEagles = true
    },
    
    -- Visual properties
    visual = {
        base_color = {0.8, 0.8, 0.8}, -- Light grey
        variants = {
            {name = "snowy_peak", type = "snow"},
            {name = "rocky_peak", type = "rock"},
            {name = "cloudy_peak", type = "cloud"},
            {name = "ice_peak", type = "ice"}
        },
        peak_features = {
            {name = "snow_cap", type = "snow"},
            {name = "rock_outcrop", type = "rock"},
            {name = "ice_formation", type = "ice"},
            {name = "cloud_ring", type = "cloud"}
        }
    },
    
    -- Sound properties
    sounds = {
        ambient = {
            "wind_howling",
            "eagle_cry",
            "rock_slide"
        },
        footsteps = {
            "snow_step",
            "rock_step"
        }
    },
    
    -- Resources
    resources = {
        snow = {
            amount = 10,
            regenRate = 0.3,
            harvestAmount = 2
        },
        ice = {
            amount = 5,
            regenRate = 0.1,
            harvestAmount = 1
        },
        rocks = {
            amount = 8,
            regenRate = 0,
            harvestAmount = 1
        }
    },
    
    -- Effects
    effects = {
        wind = {
            active = true,
            strength = 0.9,
            direction = "random",
            chill = true
        },
        cold = {
            active = true,
            intensity = 0.9,
            frostbite = true
        }
    },
    
    -- Spawn rules
    spawnRules = {
        eagles = {
            chance = 0.4,
            minDistance = 5,
            maxDensity = 0.2
        },
        rocks = {
            chance = 0.7,
            minDistance = 1,
            maxDensity = 0.6
        },
        ice_formations = {
            chance = 0.5,
            minDistance = 2,
            maxDensity = 0.4
        }
    },
    
    -- Weather effects
    weatherEffects = {
        snow = function(tile)
            -- Snow increases cover
            tile.properties.snowCover = math.min(1.0, tile.properties.snowCover + 0.1)
            return "snow_accumulate"
        end,
        wind = function(tile)
            -- Wind can cause avalanches
            if tile.properties.snowCover > 0.7 then
                tile.properties.snowCover = math.max(0, tile.properties.snowCover - 0.2)
                return "avalanche"
            end
            return nil
        end,
        heat = function(tile)
            -- Heat melts snow
            tile.properties.snowCover = math.max(0, tile.properties.snowCover - 0.2)
            if tile.properties.snowCover < 0.3 then
                return "snow_melt"
            end
            return "snow_reduce"
        end
    },
    
    -- Time effects
    timeEffects = {
        day = function(tile)
            -- Daytime can melt snow
            if tile.properties.temperature > -5 then
                tile.properties.snowCover = math.max(0, tile.properties.snowCover - 0.1)
                return "snow_melt"
            end
            return nil
        end,
        night = function(tile)
            -- Nighttime can freeze and accumulate snow
            if tile.properties.temperature < -10 then
                tile.properties.snowCover = math.min(1.0, tile.properties.snowCover + 0.1)
                return "snow_accumulate"
            end
            return nil
        end
    }
}

-- Initialize the tile
function MountainPeakTile.init(tile, world)
    -- Initialize properties first
    tile.properties = tile.properties or {}
    
    -- Copy all fields from MountainPeakTile template to tile instance
    for k, v in pairs(MountainPeakTile) do
        if type(v) ~= "function" and tile[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                tile[k] = {}
                for subk, subv in pairs(v) do
                    tile[k][subk] = subv
                end
            else
                tile[k] = v
            end
        end
    end
    
    -- Set random variant
    if tile.visual and tile.visual.variants then
        local variants = tile.visual.variants
        tile.currentVariant = variants[math.random(#variants)]
    end
    
    -- Initialize resources
    if tile.resources then
        for resourceType, resource in pairs(tile.resources) do
            resource.currentAmount = resource.amount
            resource.lastHarvest = 0
            resource.growthStage = "mature"
        end
    end
    
    return tile
end

return MountainPeakTile 