-- items/materials.lua
-- Defines material tiers and properties for equipment in Lazarus Arc

local Materials = {
    -- Material tier definitions with level requirements and base properties
    tiers = {
        -- Tier 1: Beginner (Levels 1-10)
        tier1 = {
            name = "Beginner",
            level_range = {min = 1, max = 10},
            materials = {
                wood = {
                    name = "Wood",
                    level_req = 1,
                    durability_mult = 0.7,
                    weight_mult = 0.6,
                    value_mult = 0.5,
                    properties = {
                        weapon = {
                            attack_power_mult = 0.6,
                            attack_speed_mult = 1.1
                        },
                        armor = {
                            defense_mult = 0.5,
                            magic_resist_mult = 0.7
                        },
                        shield = {
                            block_chance_mult = 0.7,
                            defense_mult = 0.5
                        }
                    },
                    description = "Common wooden materials, lightweight but not very durable."
                },
                stone = {
                    name = "Stone",
                    level_req = 1,
                    durability_mult = 0.8,
                    weight_mult = 1.2,
                    value_mult = 0.6,
                    properties = {
                        weapon = {
                            attack_power_mult = 0.8,
                            attack_speed_mult = 0.8
                        },
                        armor = {
                            defense_mult = 0.7,
                            magic_resist_mult = 0.5
                        },
                        shield = {
                            block_chance_mult = 0.8,
                            defense_mult = 0.7
                        }
                    },
                    description = "Basic stone materials, heavy but somewhat durable."
                },
                cloth = {
                    name = "Cloth",
                    level_req = 1,
                    durability_mult = 0.5,
                    weight_mult = 0.3,
                    value_mult = 0.4,
                    properties = {
                        weapon = {
                            attack_power_mult = 0.3,
                            attack_speed_mult = 1.3
                        },
                        armor = {
                            defense_mult = 0.3,
                            magic_resist_mult = 0.9
                        },
                        shield = {
                            block_chance_mult = 0.3,
                            defense_mult = 0.3
                        }
                    },
                    description = "Simple cloth materials, very lightweight with minimal protection."
                },
                copper = {
                    name = "Copper",
                    level_req = 5,
                    durability_mult = 0.9,
                    weight_mult = 0.9,
                    value_mult = 0.8,
                    properties = {
                        weapon = {
                            attack_power_mult = 0.9,
                            attack_speed_mult = 0.9
                        },
                        armor = {
                            defense_mult = 0.8,
                            magic_resist_mult = 0.6
                        },
                        shield = {
                            block_chance_mult = 0.8,
                            defense_mult = 0.8
                        }
                    },
                    description = "Basic metal with decent durability, a step up from wood and stone."
                },
                leather = {
                    name = "Leather",
                    level_req = 3,
                    durability_mult = 0.8,
                    weight_mult = 0.5,
                    value_mult = 0.7,
                    properties = {
                        weapon = {
                            attack_power_mult = 0.5,
                            attack_speed_mult = 1.2
                        },
                        armor = {
                            defense_mult = 0.6,
                            magic_resist_mult = 0.8
                        },
                        shield = {
                            block_chance_mult = 0.7,
                            defense_mult = 0.6
                        }
                    },
                    description = "Treated animal hide, offering flexibility and moderate protection."
                }
            }
        },
        
        -- Tier 2: Intermediate (Levels 10-20)
        tier2 = {
            name = "Intermediate",
            level_range = {min = 10, max = 20},
            materials = {
                bronze = {
                    name = "Bronze",
                    level_req = 10,
                    durability_mult = 1.1,
                    weight_mult = 1.0,
                    value_mult = 1.2,
                    properties = {
                        weapon = {
                            attack_power_mult = 1.1,
                            attack_speed_mult = 1.0
                        },
                        armor = {
                            defense_mult = 1.1,
                            magic_resist_mult = 0.8
                        },
                        shield = {
                            block_chance_mult = 1.0,
                            defense_mult = 1.1
                        }
                    },
                    description = "Copper-tin alloy, stronger than pure copper with good durability."
                },
                iron = {
                    name = "Iron",
                    level_req = 15,
                    durability_mult = 1.3,
                    weight_mult = 1.2,
                    value_mult = 1.5,
                    properties = {
                        weapon = {
                            attack_power_mult = 1.3,
                            attack_speed_mult = 0.9
                        },
                        armor = {
                            defense_mult = 1.3,
                            magic_resist_mult = 0.7
                        },
                        shield = {
                            block_chance_mult = 1.2,
                            defense_mult = 1.3
                        }
                    },
                    description = "Common but sturdy metal, providing good protection and weapon effectiveness."
                },
                hardwood = {
                    name = "Hardwood",
                    level_req = 12,
                    durability_mult = 1.0,
                    weight_mult = 0.8,
                    value_mult = 1.1,
                    properties = {
                        weapon = {
                            attack_power_mult = 1.0,
                            attack_speed_mult = 1.1
                        },
                        armor = {
                            defense_mult = 0.9,
                            magic_resist_mult = 1.1
                        },
                        shield = {
                            block_chance_mult = 1.0,
                            defense_mult = 0.9
                        }
                    },
                    description = "Dense, treated wood with enhanced durability and magical properties."
                },
                reinforced_leather = {
                    name = "Reinforced Leather",
                    level_req = 13,
                    durability_mult = 1.1,
                    weight_mult = 0.7,
                    value_mult = 1.3,
                    properties = {
                        weapon = {
                            attack_power_mult = 0.8,
                            attack_speed_mult = 1.2
                        },
                        armor = {
                            defense_mult = 1.0,
                            magic_resist_mult = 1.2
                        },
                        shield = {
                            block_chance_mult = 1.1,
                            defense_mult = 1.0
                        }
                    },
                    description = "Leather reinforced with metal studs or plates, balancing protection and flexibility."
                }
            }
        },
        
        -- Tier 3: Advanced (Levels 20-30)
        tier3 = {
            name = "Advanced",
            level_range = {min = 20, max = 30},
            materials = {
                steel = {
                    name = "Steel",
                    level_req = 20,
                    durability_mult = 1.5,
                    weight_mult = 1.1,
                    value_mult = 2.0,
                    properties = {
                        weapon = {
                            attack_power_mult = 1.5,
                            attack_speed_mult = 1.0
                        },
                        armor = {
                            defense_mult = 1.5,
                            magic_resist_mult = 0.9
                        },
                        shield = {
                            block_chance_mult = 1.3,
                            defense_mult = 1.5
                        }
                    },
                    description = "Iron-carbon alloy, stronger and more durable than iron."
                },
                silver = {
                    name = "Silver",
                    level_req = 25,
                    durability_mult = 1.2,
                    weight_mult = 1.0,
                    value_mult = 2.5,
                    properties = {
                        weapon = {
                            attack_power_mult = 1.3,
                            attack_speed_mult = 1.1,
                            undead_damage_mult = 1.5
                        },
                        armor = {
                            defense_mult = 1.2,
                            magic_resist_mult = 1.3
                        },
                        shield = {
                            block_chance_mult = 1.2,
                            defense_mult = 1.2,
                            magic_reflect_chance = 0.05
                        }
                    },
                    description = "Precious metal with innate magical properties, especially effective against undead."
                },
                obsidian = {
                    name = "Obsidian",
                    level_req = 22,
                    durability_mult = 1.3,
                    weight_mult = 1.3,
                    value_mult = 2.2,
                    properties = {
                        weapon = {
                            attack_power_mult = 1.6,
                            attack_speed_mult = 0.8,
                            bleeding_chance = 0.1
                        },
                        armor = {
                            defense_mult = 1.4,
                            magic_resist_mult = 1.0,
                            damage_reflection = 0.05
                        },
                        shield = {
                            block_chance_mult = 1.1,
                            defense_mult = 1.4,
                            counter_damage = 5
                        }
                    },
                    description = "Volcanic glass, extremely sharp but somewhat brittle. Can cause bleeding."
                },
                dragonhide = {
                    name = "Dragonhide",
                    level_req = 28,
                    durability_mult = 1.7,
                    weight_mult = 0.8,
                    value_mult = 2.8,
                    properties = {
                        weapon = {
                            attack_power_mult = 1.4,
                            attack_speed_mult = 1.2
                        },
                        armor = {
                            defense_mult = 1.6,
                            magic_resist_mult = 1.5,
                            fire_resist = 0.3
                        },
                        shield = {
                            block_chance_mult = 1.4,
                            defense_mult = 1.6,
                            fire_resist = 0.3
                        }
                    },
                    description = "Scales from dragons, offering excellent protection especially against fire."
                }
            }
        },
        
        -- Tier 4: Expert (Levels 30-50)
        tier4 = {
            name = "Expert",
            level_range = {min = 30, max = 50},
            materials = {
                mithril = {
                    name = "Mithril",
                    level_req = 30,
                    durability_mult = 2.0,
                    weight_mult = 0.7,
                    value_mult = 3.5,
                    properties = {
                        weapon = {
                            attack_power_mult = 1.8,
                            attack_speed_mult = 1.3
                        },
                        armor = {
                            defense_mult = 1.8,
                            magic_resist_mult = 1.5
                        },
                        shield = {
                            block_chance_mult = 1.6,
                            defense_mult = 1.8
                        }
                    },
                    description = "Legendary lightweight metal with exceptional durability and magical conductivity."
                },
                adamantite = {
                    name = "Adamantite",
                    level_req = 40,
                    durability_mult = 2.5,
                    weight_mult = 1.5,
                    value_mult = 4.0,
                    properties = {
                        weapon = {
                            attack_power_mult = 2.2,
                            attack_speed_mult = 0.9,
                            armor_penetration = 0.15
                        },
                        armor = {
                            defense_mult = 2.2,
                            magic_resist_mult = 1.2,
                            damage_reduction = 0.1
                        },
                        shield = {
                            block_chance_mult = 1.8,
                            defense_mult = 2.2,
                            damage_reduction = 0.1
                        }
                    },
                    description = "Incredibly hard metal that can penetrate almost any armor."
                },
                enchanted_gold = {
                    name = "Enchanted Gold",
                    level_req = 35,
                    durability_mult = 1.8,
                    weight_mult = 1.2,
                    value_mult = 4.5,
                    properties = {
                        weapon = {
                            attack_power_mult = 1.7,
                            attack_speed_mult = 1.1,
                            magic_damage_bonus = 10
                        },
                        armor = {
                            defense_mult = 1.6,
                            magic_resist_mult = 2.0,
                            mana_regen_bonus = 0.1
                        },
                        shield = {
                            block_chance_mult = 1.5,
                            defense_mult = 1.6,
                            spell_reflect_chance = 0.1
                        }
                    },
                    description = "Gold infused with powerful enchantments, excellent for magical equipment."
                },
                shadow_steel = {
                    name = "Shadow Steel",
                    level_req = 45,
                    durability_mult = 2.2,
                    weight_mult = 0.9,
                    value_mult = 4.2,
                    properties = {
                        weapon = {
                            attack_power_mult = 2.0,
                            attack_speed_mult = 1.2,
                            stealth_bonus = 10,
                            critical_chance_bonus = 0.05
                        },
                        armor = {
                            defense_mult = 1.9,
                            magic_resist_mult = 1.7,
                            stealth_bonus = 15,
                            shadow_resist = 0.3
                        },
                        shield = {
                            block_chance_mult = 1.7,
                            defense_mult = 1.9,
                            shadow_resist = 0.3
                        }
                    },
                    description = "Metal forged in shadow realms, granting stealth and critical strike bonuses."
                }
            }
        },
        
        -- Tier 5: Master (Levels 50+)
        tier5 = {
            name = "Master",
            level_range = {min = 50, max = 100},
            materials = {
                crystal = {
                    name = "Crystal",
                    level_req = 50,
                    durability_mult = 2.0,
                    weight_mult = 0.8,
                    value_mult = 5.0,
                    properties = {
                        weapon = {
                            attack_power_mult = 2.3,
                            attack_speed_mult = 1.2,
                            magic_damage_bonus = 20
                        },
                        armor = {
                            defense_mult = 2.0,
                            magic_resist_mult = 2.5,
                            mana_cost_reduction = 0.15
                        },
                        shield = {
                            block_chance_mult = 1.9,
                            defense_mult = 2.0,
                            spell_absorb_chance = 0.15
                        }
                    },
                    description = "Pure magical crystal that amplifies arcane energies and provides exceptional protection."
                },
                dragonbone = {
                    name = "Dragonbone",
                    level_req = 60,
                    durability_mult = 2.7,
                    weight_mult = 1.0,
                    value_mult = 5.5,
                    properties = {
                        weapon = {
                            attack_power_mult = 2.5,
                            attack_speed_mult = 1.1,
                            elemental_damage_bonus = 15
                        },
                        armor = {
                            defense_mult = 2.4,
                            magic_resist_mult = 2.0,
                            elemental_resist = 0.2
                        },
                        shield = {
                            block_chance_mult = 2.0,
                            defense_mult = 2.4,
                            elemental_resist = 0.2
                        }
                    },
                    description = "Bones from ancient dragons, containing immense power and elemental affinities."
                },
                stardust = {
                    name = "Stardust",
                    level_req = 70,
                    durability_mult = 2.5,
                    weight_mult = 0.5,
                    value_mult = 6.0,
                    properties = {
                        weapon = {
                            attack_power_mult = 2.7,
                            attack_speed_mult = 1.3,
                            cosmic_damage = 25
                        },
                        armor = {
                            defense_mult = 2.3,
                            magic_resist_mult = 2.7,
                            cosmic_resist = 0.3,
                            stat_bonus = 0.1
                        },
                        shield = {
                            block_chance_mult = 2.2,
                            defense_mult = 2.3,
                            cosmic_resist = 0.3,
                            stat_bonus = 0.1
                        }
                    },
                    description = "Material from fallen stars, containing cosmic energy that transcends normal magic."
                },
                primordial = {
                    name = "Primordial",
                    level_req = 85,
                    durability_mult = 3.0,
                    weight_mult = 1.0,
                    value_mult = 8.0,
                    properties = {
                        weapon = {
                            attack_power_mult = 3.0,
                            attack_speed_mult = 1.2,
                            all_damage_bonus = 0.2,
                            special_ability_chance = 0.2
                        },
                        armor = {
                            defense_mult = 2.8,
                            magic_resist_mult = 2.8,
                            all_resist = 0.2,
                            special_ability_chance = 0.2
                        },
                        shield = {
                            block_chance_mult = 2.5,
                            defense_mult = 2.8,
                            all_resist = 0.2,
                            special_ability_chance = 0.2
                        }
                    },
                    description = "Material from the dawn of creation, containing raw power of the universe itself."
                }
            }
        }
    },
    
    -- Helper functions
    
    -- Get material data by name
    getMaterial = function(self, materialName)
        for _, tier in pairs(self.tiers) do
            for id, material in pairs(tier.materials) do
                if id == materialName or material.name:lower() == materialName:lower() then
                    return material
                end
            end
        end
        return nil
    end,
    
    -- Get tier for a given level
    getTierForLevel = function(self, level)
        for tierId, tier in pairs(self.tiers) do
            if level >= tier.level_range.min and level <= tier.level_range.max then
                return tierId, tier
            end
        end
        -- If level is higher than any tier, return the highest tier
        return "tier5", self.tiers.tier5
    end,
    
    -- Get available materials for a given level
    getAvailableMaterials = function(self, level)
        local available = {}
        for _, tier in pairs(self.tiers) do
            for materialId, material in pairs(tier.materials) do
                if level >= material.level_req then
                    available[materialId] = material
                end
            end
        end
        return available
    end,
    
    -- Apply material properties to an item
    applyMaterialToItem = function(self, item, materialName)
        local material = self:getMaterial(materialName)
        if not material then return item end
        
        -- Apply basic properties
        item.material = materialName
        item.material_name = material.name
        
        -- Apply durability and weight modifiers
        if item.stats then
            if item.stats.durability then
                item.stats.durability = item.stats.durability * material.durability_mult
            end
            
            if item.stats.weight then
                item.stats.weight = item.stats.weight * material.weight_mult
            end
        end
        
        -- Apply type-specific properties
        local itemType = item.type or "weapon"
        if material.properties[itemType] then
            local props = material.properties[itemType]
            
            -- Apply attack modifiers for weapons
            if itemType == "weapon" and item.stats then
                if item.stats.attack_power and props.attack_power_mult then
                    item.stats.attack_power = item.stats.attack_power * props.attack_power_mult
                end
                
                if item.stats.attack_speed and props.attack_speed_mult then
                    item.stats.attack_speed = item.stats.attack_speed * props.attack_speed_mult
                end
                
                -- Apply special weapon properties
                for prop, value in pairs(props) do
                    if prop ~= "attack_power_mult" and prop ~= "attack_speed_mult" then
                        if not item.effects then item.effects = {} end
                        item.effects[prop] = value
                    end
                end
            end
            
            -- Apply defense modifiers for armor
            if (itemType == "armor" or itemType == "shield") and item.stats then
                if item.stats.defense and props.defense_mult then
                    item.stats.defense = item.stats.defense * props.defense_mult
                end
                
                -- Apply special armor/shield properties
                for prop, value in pairs(props) do
                    if prop ~= "defense_mult" then
                        if not item.effects then item.effects = {} end
                        item.effects[prop] = value
                    end
                end
            end
        end
        
        -- Update item description to include material information
        if item.description then
            item.description = material.name .. " " .. item.description
        end
        
        -- Update item name to include material
        if item.name then
            item.name = material.name .. " " .. item.name
        end
        
        return item
    end
}

return Materials
