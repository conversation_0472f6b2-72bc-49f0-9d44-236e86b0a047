local IronLongSpear = {
    id = "iron_long_spear",
    name = "Iron Long Spear",
    type = "weapon",
    category = "spear",
    
    -- Properties
    properties = {
        damage = 32,
        attackSpeed = 0.9,
        range = 2.2,
        durability = 190,
        maxDurability = 190,
        thrustPower = 1.6,
        piercePower = 1.5,
        armorPenetration = 0.4,
        weight = 1.3,
        momentum = 0,
        maxMomentum = 3,
        momentumPower = 1.3
    },
    
    -- Appearance
    appearance = {
        sprite = "iron_long_spear",
        scale = 1.3,
        animations = {
            "idle",
            "thrust",
            "sweep",
            "momentum"
        },
        variants = {
            "iron",
            "steel",
            "bronze",
            "copper"
        },
        blendMode = "normal",
        tint = {0.7, 0.7, 0.7},
        alpha = 1.0
    },
    
    -- Sound effects
    sounds = {
        thrust = "spear_thrust",
        sweep = "spear_sweep",
        pierce = "spear_pierce",
        momentum = "spear_momentum"
    },
    
    -- Effects
    effects = {
        pierce = {
            type = "status",
            duration = 0.4,
            effects = {
                damage = 32,
                pierce = true,
                bleed = 0.2
            }
        },
        momentum = {
            type = "status",
            duration = 0.4,
            effects = {
                damage = 1.3
            }
        }
    }
}

-- Initialize the weapon
function IronLongSpear.init(weapon, world)
    -- Copy all fields from Iron<PERSON><PERSON>Spear template to weapon instance
    for k, v in pairs(IronLongSpear) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        momentum = 0,
        lastMomentumTime = 0
    }

    return weapon
end

-- Update the weapon
function IronLongSpear.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Decay momentum
        if world.time - weapon.properties.weaponState.lastMomentumTime > 1.5 then
            weapon.properties.weaponState.momentum = 0
        end
    end
end

-- Handle attack
function IronLongSpear.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Calculate thrust direction
    local angle = math.atan2(
        target.position.y - attacker.position.y,
        target.position.x - attacker.position.x
    )
    
    -- Create thrust effect
    if world.createEffect then
        world.createEffect({
            type = "thrust",
            position = attacker.position,
            angle = angle,
            range = weapon.properties.range,
            duration = 0.4
        })
    end
    
    -- Check for pierce effect
    if math.random() < weapon.properties.thrustPower / weapon.properties.damage then
        -- Create pierce effect
        if world.createEffect then
            world.createEffect({
                type = "pierce",
                position = target.position,
                duration = 0.4
            })
        end
        
        -- Apply pierce effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.pierce)
        end
        
        -- Play pierce sound
        if world.playSound then
            world.playSound(weapon.sounds.pierce)
        end
    end
    
    -- Update momentum
    if world.time - weapon.properties.weaponState.lastMomentumTime <= 1.5 then
        weapon.properties.weaponState.momentum = math.min(weapon.properties.maxMomentum,
            weapon.properties.weaponState.momentum + 1)
    else
        weapon.properties.weaponState.momentum = 1
    end
    weapon.properties.weaponState.lastMomentumTime = world.time
    
    -- Apply momentum effect if active
    if weapon.properties.weaponState.momentum > 1 then
        if target.applyEffect then
            target.applyEffect(weapon.effects.momentum)
        end
        
        -- Play momentum sound
        if world.playSound then
            world.playSound(weapon.sounds.momentum)
        end
    end
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function IronLongSpear.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough momentum
    if weapon.properties.weaponState.momentum < 2 then
        return false
    end
    
    -- Create sweep effect
    if world.createEffect then
        world.createEffect({
            type = "sweep",
            position = attacker.position,
            angle = math.atan2(
                target.position.y - attacker.position.y,
                target.position.x - attacker.position.x
            ),
            range = weapon.properties.range * 1.2,
            duration = 0.6
        })
    end
    
    -- Apply pierce effect with momentum
    if target.applyEffect then
        target.applyEffect(weapon.effects.pierce)
        target.applyEffect(weapon.effects.momentum)
    end
    
    -- Reset momentum
    weapon.properties.weaponState.momentum = 0
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 2)
    
    return true
end

return IronLongSpear 