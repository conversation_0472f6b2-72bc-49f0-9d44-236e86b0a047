local Cloud = {
    id = "cloud",
    name = "Cloud",
    type = "tile",
    
    -- Categories
    categories = {"terrain", "cloud", "floating", "special"},
    
    -- Properties
    properties = {
        walkable = true,
        swimable = false,
        climbable = false,
        flyable = true,
        diggable = false,
        buildable = false,
        flammable = false,
        density = 0.3,
        moisture = 0.8,
        temperature = 0.2,
        windInfluence = 0.9,
        floatHeight = 0.5,
        floatSpeed = 0.2,
        floatOffset = 0,
        driftSpeed = 0.1,
        driftDirection = {x = 0, y = 0},
        condensationRate = 0.1,
        evaporationRate = 0.05,
        lightningChance = 0.01,
        rainChance = 0.3,
        fogDensity = 0.5,
        fogRadius = 3
    },
    
    -- Appearance
    appearance = {
        sprite = "cloud",
        scale = 1.0,
        animations = {
            "idle",
            "float",
            "drift",
            "rain",
            "lightning"
        },
        variants = {
            "white",
            "gray",
            "dark",
            "storm"
        },
        blendMode = "add",
        tint = {1.0, 1.0, 1.0},
        alpha = 0.8
    },
    
    -- Sound effects
    sounds = {
        wind = "cloud_wind",
        rain = "cloud_rain",
        thunder = "cloud_thunder",
        drift = "cloud_drift"
    },
    
    -- Effects
    effects = {
        fog = {
            type = "environment",
            duration = 0,
            effects = {
                visibility = 0.5,
                radius = 3,
                color = {0.8, 0.8, 0.8}
            }
        },
        rain = {
            type = "weather",
            duration = 0,
            effects = {
                rainRate = 0.5,
                rainRadius = 2
            }
        },
        lightning = {
            type = "weather",
            duration = 0.1,
            effects = {
                damage = 30,
                radius = 4,
                lightColor = {1.0, 1.0, 1.0},
                lightIntensity = 1.0
            }
        }
    },
    
    -- Spawn rules
    spawnRules = {
        structures = {
            "cloud_formation",
            "floating_island",
            "sky_crystal"
        }
    }
}

-- Initialize the tile
function Cloud.init(tile, world)
    -- Copy all fields from Cloud template to tile instance
    for k, v in pairs(Cloud) do
        if type(v) ~= "function" and tile[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                tile[k] = {}
                for subk, subv in pairs(v) do
                    tile[k][subk] = subv
                end
            else
                tile[k] = v
            end
        end
    end

    -- Set random variant
    if tile.appearance and tile.appearance.variants then
        local variants = tile.appearance.variants
        tile.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize cloud state
    tile.properties.cloudState = {
        floatOffset = math.random() * math.pi * 2,
        driftOffset = {x = math.random() * 2 - 1, y = math.random() * 2 - 1},
        moisture = tile.properties.moisture,
        isRaining = false,
        lightningTimer = 0,
        rainTimer = 0
    }

    return tile
end

-- Update the tile
function Cloud.update(tile, world, dt)
    -- Update cloud state
    if tile.properties.cloudState then
        -- Update floating motion
        tile.properties.cloudState.floatOffset = tile.properties.cloudState.floatOffset + dt * tile.properties.floatSpeed
        tile.properties.floatOffset = math.sin(tile.properties.cloudState.floatOffset) * tile.properties.floatHeight
        
        -- Update drift motion
        if world.wind then
            local windStrength = world.wind.strength * tile.properties.windInfluence
            tile.properties.cloudState.driftOffset.x = tile.properties.cloudState.driftOffset.x + 
                world.wind.direction.x * windStrength * tile.properties.driftSpeed * dt
            tile.properties.cloudState.driftOffset.y = tile.properties.cloudState.driftOffset.y + 
                world.wind.direction.y * windStrength * tile.properties.driftSpeed * dt
        end
        
        -- Update moisture and weather effects
        if tile.properties.cloudState.moisture > 0.8 then
            -- Check for rain
            if math.random() < tile.properties.rainChance then
                tile.properties.cloudState.isRaining = true
                tile.properties.cloudState.rainTimer = tile.properties.cloudState.rainTimer + dt
                
                -- Create rain effect
                if world.createEffect then
                    world.createEffect({
                        type = "rain",
                        position = tile.position,
                        radius = tile.properties.effects.rain.effects.rainRadius
                    })
                end
                
                -- Play rain sound
                if world.playSound then
                    world.playSound(tile.sounds.rain)
                end
                
                -- Reduce moisture
                tile.properties.cloudState.moisture = math.max(0,
                    tile.properties.cloudState.moisture - tile.properties.evaporationRate * dt)
            end
        end
        
        -- Check for lightning
        if tile.properties.cloudState.moisture > 0.9 and math.random() < tile.properties.lightningChance then
            -- Create lightning effect
            if world.createEffect then
                world.createEffect({
                    type = "lightning",
                    position = tile.position,
                    radius = tile.properties.effects.lightning.effects.radius
                })
            end
            
            -- Play thunder sound
            if world.playSound then
                world.playSound(tile.sounds.thunder)
            end
            
            -- Apply lightning damage to entities
            if world.entities then
                for _, entity in ipairs(world.entities) do
                    if entity.position then
                        local distance = math.sqrt(
                            (entity.position.x - tile.position.x)^2 + 
                            (entity.position.y - tile.position.y)^2
                        )
                        
                        if distance <= tile.properties.effects.lightning.effects.radius then
                            if entity.takeDamage then
                                entity.takeDamage(tile.properties.effects.lightning.effects.damage)
                            end
                        end
                    end
                end
            end
        end
        
        -- Update moisture based on environment
        if world.temperature and world.moisture then
            local moistureDiff = world.moisture - tile.properties.cloudState.moisture
            tile.properties.cloudState.moisture = math.max(0, math.min(1,
                tile.properties.cloudState.moisture + moistureDiff * tile.properties.condensationRate * dt))
        end
    end
end

-- Handle interaction
function Cloud.interact(tile, world, entity, action)
    if action == "walk" then
        -- Apply floating effect to walking entities
        if entity.move then
            entity.move({
                x = tile.properties.cloudState.driftOffset.x,
                y = tile.properties.floatOffset
            })
        end
    elseif action == "touch" then
        -- Apply fog effect to touching entities
        if entity.applyEffect then
            entity.applyEffect(tile.effects.fog)
        end
    end
end

return Cloud 