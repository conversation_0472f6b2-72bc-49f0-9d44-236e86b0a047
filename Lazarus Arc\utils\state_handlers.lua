-- state_handlers.lua
-- Game state transition handlers for Lazarus Arc


-- The game follows a modular architecture where:
-- main.lua is minimal and just initializes the engine
-- engine.lua sets up the LÖVE callbacks and delegates to appropriate systems
-- controller.lua handles input mapping and processing
-- state_handlers.lua manages game states and their specific behaviors
-- ui_system.lua handles UI rendering and interaction

-- Helper function to count table elements (including non-numeric keys)
if not table.count then
    table.count = function(t)
        local count = 0
        for _ in pairs(t) do
            count = count + 1
        end
        return count
    end
end

local StateHandlers = {
    currentState = "startup",
    nextState = nil,
    handlers = {}
}

-- Register a new state handler
function StateHandlers:registerHandler(stateName, handlers)
    self.handlers[stateName] = handlers
end

-- Register all state handlers
function StateHandlers:registerAllHandlers()
    local Engine = require("engine") -- For accessing engine systems

    -- Startup state
    self:registerHandler("startup", {
        enter = function()
            print("Entering startup state")
        end,
        update = function(dt)
            -- Minimal update during startup
            -- Typically just shows a splash screen and transitions to menu
            self:changeState("menu")
        end,
        draw = function()
            -- Draw splash screen
            love.graphics.clear(0, 0, 0, 1)
            love.graphics.setColor(1, 1, 1, 1)
            love.graphics.print("Lazarus Arc - Starting up...", 100, 100)
        end,
        exit = function()
            print("Exiting startup state")
        end
    })

    -- Menu state
    self:registerHandler("menu", {
        enter = function()
            print("Entering menu state")
            if Engine.systems.soundSystem then
                Engine.systems.soundSystem.playMusic("menu_theme", true)
            end

            if Engine.systems.uiSystem then
                Engine.systems.uiSystem.mainMenuActive = true
            end

            -- Create a hub world for the menu background if it doesn't exist
            if not Engine.currentWorld then
                print("Creating hub world for menu background")
                Engine.currentWorld = Engine.systems.worldCore.createWorld({
                    name = "Hub World",
                    seed = 12345, -- Use a fixed seed for consistent menu background
                    isHubWorld = true,
                    entitySystem = Engine.systems.entityManager,
                    chunkSystem = Engine.systems.chunkSystem,
                    weatherSystem = Engine.systems.weatherSystem
                })

                -- Initialize the hub area with proper biomes
                if Engine.currentWorld and Engine.currentWorld.chunkSystem then
                    -- Force creation of hub chunk and surrounding chunks
                    for x = -2, 2 do
                        for y = -2, 2 do
                            local chunk = Engine.currentWorld.chunkSystem:getChunkAt(x, y)
                            if chunk and (x == 0 and y == 0) then
                                -- Mark center chunk as hub
                                chunk.isHub = true

                                -- Set hub biome for center chunk
                                local chunkSize = Engine.currentWorld.chunkSystem.chunkSize
                                local chunkX = x * chunkSize
                                local chunkY = y * chunkSize
                                Engine.currentWorld.chunkSystem:setBiomeAt(chunkX, chunkY, "hub")
                            else
                                -- For surrounding chunks, set biome based on distance and position
                                local distance = math.sqrt(x*x + y*y)
                                local biomeName = "forest"  -- Default to forest for surrounding chunks

                                -- Vary biomes in surrounding chunks for visual interest
                                if distance > 0.8 then
                                    if (x + y) % 2 == 0 then
                                        biomeName = "plains"
                                    else
                                        biomeName = "forest"
                                    end
                                end

                                -- Set surrounding chunks to different biomes
                                local chunkSize = Engine.currentWorld.chunkSystem.chunkSize
                                local chunkX = x * chunkSize
                                local chunkY = y * chunkSize
                                Engine.currentWorld.chunkSystem:setBiomeAt(chunkX, chunkY, biomeName)
                            end
                        end
                    end
                    print("Hub world initialized for menu background")
                end
            end

            -- If we're coming from gameplay, remove the player entity but keep the world
            if Engine.player then
                print("Removing player entity for menu state")

                -- Unregister from entity system
                if Engine.currentWorld and Engine.currentWorld.entitySystem then
                    Engine.currentWorld.entitySystem:unregister(Engine.player.id)
                end

                -- Remove viewport tracking
                if Engine.systems.viewportManager then
                    Engine.systems.viewportManager:removePlayer(Engine.player)
                end

                -- Save player data
                if Engine.systems.database then
                    print("Saving player character: " .. (Engine.player.name or "Unknown"))
                    Engine.systems.database.saveCharacter(Engine.player)
                end

                -- Store player data for later but remove active entity
                Engine.savedPlayerData = Engine.player
                Engine.player = nil
            end

            -- Set up camera for menu view (centered on hub)
            if Engine.systems.viewportManager then
                Engine.systems.viewportManager:setMenuView({x = 0, y = 0})
            end
        end,

        -- Handle text input (for character name editing)
        textInput = function(text)
            if Engine.systems.uiSystem then
                Engine.systems.uiSystem:textInput(text)
            end
        end,

        -- Handle key presses
        keyPressed = function(key, scancode, isrepeat)
            if Engine.systems.uiSystem then
                Engine.systems.uiSystem:keyPressed(key)
                print("Menu state forwarded key: " .. key .. " to UI system")
            else
                -- Fallback key handling if UI system is not available
                if key == "1" or key == "return" then
                    self:changeState("gameplay")
                elseif key == "2" then
                    self:changeState("options")
                elseif key == "3" or key == "escape" then
                    love.event.quit()
                end
            end
        end,

        -- Keep the old function for backward compatibility
        keypressed = function(key)
            self.keyPressed(key)
        end,

        update = function(dt)
            -- Update the current world for background visualization
            if Engine.currentWorld then
                -- Update chunk system
                if Engine.currentWorld.chunkSystem then
                    Engine.currentWorld.chunkSystem:update(dt)
                end

                -- Update entity system
                if Engine.currentWorld.entitySystem then
                    Engine.currentWorld.entitySystem:update(dt)
                end

                -- Update weather and time of day
                if Engine.currentWorld.weatherSystem then
                    if type(Engine.currentWorld.weatherSystem.update) == "function" then
                        Engine.currentWorld.weatherSystem.update(dt)  -- Fixed: use dot syntax, not colon
                    else
                        -- Use the global weather system if the world one doesn't have an update method
                        if Engine.systems.weatherSystem then
                            Engine.systems.weatherSystem.update(dt)
                        end
                    end
                end

                -- Slowly rotate camera around hub for visual interest
                if Engine.systems.viewportManager then
                    Engine.systems.viewportManager:updateMenuView(dt)
                end
            end

            -- Update UI animations and effects
            if Engine.systems.uiSystem and Engine.systems.uiSystem.update then
                Engine.systems.uiSystem:update(dt)
            end

            -- Update UI messages
            if Engine.systems.uiSystem and Engine.systems.uiSystem.messages then
                for i = #Engine.systems.uiSystem.messages, 1, -1 do
                    local message = Engine.systems.uiSystem.messages[i]
                    message.current = message.current + dt

                    if message.current >= message.duration then
                        table.remove(Engine.systems.uiSystem.messages, i)
                    end
                end
            end
        end,
        draw = function()
            -- Clear screen
            love.graphics.clear(0.1, 0.1, 0.2, 1)

            -- Always draw the live game world in the background (no caching)
            love.graphics.clear(0.1, 0.1, 0.2, 1)

            -- Use renderer for efficient drawing of the live world
            if Engine.systems.renderer and Engine.currentWorld then
                love.graphics.push()
                -- Make sure performance mode is applied consistently
                if Engine.settings.performanceMode then
                    Engine.systems.renderer.enableShaders = false
                    Engine.systems.renderer.enableBlur = false
                    Engine.systems.renderer.simplifiedTiles = true
                    Engine.systems.renderer.simplifiedEntities = true
                else
                    -- Ensure high quality rendering for menu view
                    Engine.systems.renderer.enableShaders = true
                    Engine.systems.renderer.enableBlur = true
                    Engine.systems.renderer.simplifiedTiles = false
                    Engine.systems.renderer.simplifiedEntities = false
                end

                -- Ensure chunk system has good view distance for menu
                if Engine.currentWorld and Engine.currentWorld.chunkSystem then
                    Engine.currentWorld.chunkSystem.viewDistance = 4  -- Increased from default 2
                    Engine.currentWorld.chunkSystem.maxLoadedChunks = 100  -- Increased from default 25
                end

                -- Ensure the world has a proper time of day and weather for rendering
                if Engine.currentWorld and not Engine.currentWorld.chunkSystem then
                    -- Create a simple time of day system if none exists
                    Engine.currentWorld.chunkSystem = Engine.currentWorld.chunkSystem or {}
                    Engine.currentWorld.chunkSystem.timeOfDay = Engine.currentWorld.chunkSystem.timeOfDay or {
                        hour = math.random(0, 23),
                        minute = math.random(0, 59),
                        dayPhase = "day"
                    }
                end

                -- Draw the world with our LOD system
                -- Pass a dummy player object with position to ensure horizon line is rendered
                -- but don't include a character in the main menu
                local dummyPlayer = {
                    position = {
                        x = 0,
                        y = 0
                    },
                    noCharacter = true  -- Flag to indicate no character should be rendered
                }
                Engine.systems.renderer:drawWorld(Engine.currentWorld, dummyPlayer, Engine.systems.viewportManager)
                love.graphics.pop()

                -- Ensure isometric mode is enabled for horizon line
                if Engine.systems.colorUtils then
                    Engine.systems.colorUtils.isometricDebug = true
                end

                -- Draw horizon line explicitly for menu state
                if Engine.systems.renderer and Engine.systems.renderer.drawHorizonLine then
                    Engine.systems.renderer:drawHorizonLine()
                end
            end

            -- Draw effect system effects if available
            if Engine.systems.effectSystem and Engine.systems.effectSystem.draw then
                Engine.systems.effectSystem:draw()
            end

            -- Draw UI elements
            if Engine.systems.uiSystem and Engine.systems.uiSystem.draw then
                Engine.systems.uiSystem:draw()
            else
                -- Fallback menu if UI system not available
                love.graphics.setColor(0, 0, 0, 0.5)
                love.graphics.rectangle("fill", 0, 0, Engine.settings.screenWidth, Engine.settings.screenHeight)
                love.graphics.setColor(1, 1, 1, 1)
                love.graphics.print("Lazarus Arc", 100, 100, 0, 3, 3)
                love.graphics.print("1. Start Game", 100, 200)
                love.graphics.print("2. Options", 100, 250)
                love.graphics.print("3. Quit", 100, 300)
            end
        end,
        exit = function()
            print("Exiting menu state")
            if Engine.systems.uiSystem then
                Engine.systems.uiSystem.mainMenuActive = false
            end
        end,
        keyPressed = function(key)
            if Engine.systems.uiSystem and Engine.systems.uiSystem.keyPressed then
                Engine.systems.uiSystem:keyPressed(key)
            else
                -- Fallback if UI system not available
            if key == "1" or key == "return" then
                self:changeState("gameplay")
            elseif key == "2" then
                self:changeState("options")
            elseif key == "3" or key == "escape" then
                love.event.quit()
                end
            end
        end,
        mousePressed = function(x, y, button)
            if Engine.systems.uiSystem and Engine.systems.uiSystem.handleMainMenuClick then
                Engine.systems.uiSystem:handleMainMenuClick(x, y)
            end
        end,
        mouseMoved = function(x, y)
            if Engine.systems.uiSystem and Engine.systems.uiSystem.mouseMove then
                Engine.systems.uiSystem:mouseMove(x, y)
            end
        end
    })

    -- Gameplay state
    self:registerHandler("gameplay", {
        enter = function()
            print("Entering gameplay state")
            if Engine.systems.soundSystem then
                Engine.systems.soundSystem.playMusic("gameplay_theme", true)
            end

            -- Make sure UI system is not in menu mode
            if Engine.systems.uiSystem then
                Engine.systems.uiSystem.mainMenuActive = false
            end

            -- Set the same zoom level as the menu for visual consistency
            if Engine.systems.viewportManager then
                -- Store the menu zoom level to use in gameplay
                Engine.systems.viewportManager.gameplayZoom = Engine.systems.viewportManager.menuView.zoom
                Engine.systems.viewportManager:setZoom(Engine.systems.viewportManager.menuView.zoom)
            end

            -- Use current world if it exists, or create a new one
            if not Engine.currentWorld then
                print("Creating new world for gameplay")
                Engine.currentWorld = Engine.systems.worldCore.createWorld({
                    name = "Player World",
                    seed = os.time(),
                    isHubWorld = true,  -- Start in a hub world
                    entitySystem = Engine.systems.entityManager,
                    chunkSystem = Engine.systems.chunkSystem,
                    weatherSystem = Engine.systems.weatherSystem
                })

                -- Initialize the hub area with proper biomes
                if Engine.currentWorld and Engine.currentWorld.chunkSystem then
                    -- Force creation of hub chunk and surrounding chunks
                    for x = -2, 2 do
                        for y = -2, 2 do
                            local chunk = Engine.currentWorld.chunkSystem:getChunkAt(x, y)
                            if chunk and (x == 0 and y == 0) then
                                -- Mark center chunk as hub
                                chunk.isHub = true

                                -- Set hub biome for center chunk
                                local chunkSize = Engine.currentWorld.chunkSystem.chunkSize
                                local chunkX = x * chunkSize
                                local chunkY = y * chunkSize
                                Engine.currentWorld.chunkSystem:setBiomeAt(chunkX, chunkY, "hub")
                            else
                                -- For surrounding chunks, set biome based on distance and position
                                local distance = math.sqrt(x*x + y*y)
                                local biomeName = "forest"  -- Default to forest for surrounding chunks

                                -- Vary biomes in surrounding chunks for visual interest
                                if distance > 0.8 then
                                    if (x + y) % 2 == 0 then
                                        biomeName = "plains"
                                    else
                                        biomeName = "forest"
                                    end
                                end

                                -- Set surrounding chunks to different biomes
                                local chunkSize = Engine.currentWorld.chunkSystem.chunkSize
                                local chunkX = x * chunkSize
                                local chunkY = y * chunkSize
                                Engine.currentWorld.chunkSystem:setBiomeAt(chunkX, chunkY, biomeName)
                            end
                        end
                    end
                    print("Hub world initialized for gameplay")
                end
            else
                print("Using existing world from menu for gameplay")
            end

            -- Increase chunk system view distance to match menu view
            if Engine.currentWorld and Engine.currentWorld.chunkSystem then
                -- Increase view distance and max loaded chunks for better visibility
                Engine.currentWorld.chunkSystem.viewDistance = 4  -- Increased from default 2
                Engine.currentWorld.chunkSystem.maxLoadedChunks = 100  -- Increased from default 25
            end

            -- Create a player or restore saved player
            if Engine.savedPlayerData then
                print("Restoring saved player data")
                Engine.player = Engine.savedPlayerData
                Engine.savedPlayerData = nil
            elseif not Engine.player then
                print("Creating new player")
                local Character = require("character")
                local Player = require("entities/player")

                -- Create a basic player
                Engine.player = Player.new("Player", "Warrior")
                Engine.player.type = "player"

                -- Position player at the same location as the menu view for consistency
                if Engine.systems.viewportManager and Engine.systems.viewportManager.lastMenuPosition then
                    Engine.player.position = {
                        x = Engine.systems.viewportManager.lastMenuPosition.x,
                        y = Engine.systems.viewportManager.lastMenuPosition.y
                    }
                else
                    Engine.player.position = { x = 0, y = 0 }
                end
            end

            -- Register player with world entity system
            if Engine.currentWorld.entitySystem then
                -- Register player with world entity system
                local success = Engine.currentWorld.entitySystem:register(Engine.player)

                -- Ensure player has a unique ID
                if not Engine.player.id then
                    Engine.player.id = "player1"
                end

                -- Ensure player has a type
                if not Engine.player.type then
                    Engine.player.type = "player"
                end

                -- Store the player in the world for easy access
                Engine.currentWorld.player = Engine.player

                -- Ensure player has a position
                if not Engine.player.position then
                    Engine.player.position = {x = 0, y = 0}
                end
            end

            -- Set up viewport to follow player
            if Engine.systems.viewportManager then
                -- Add player to viewport manager
                Engine.systems.viewportManager:addPlayer(Engine.player)

                -- Store the real player for movement, but don't use it for rendering
                Engine.systems.viewportManager.realPlayer = Engine.player
            end
        end,
        update = function(dt)
            -- Skip updates if game is paused (menu is open)
            if Engine.gamePaused or (Engine.systems.uiSystem and (Engine.systems.uiSystem.mainMenuActive or (Engine.systems.uiSystem.inGameMenu and Engine.systems.uiSystem.inGameMenu.active))) then
                -- Still update UI animations if needed
                if Engine.systems.uiSystem then
                    Engine.systems.uiSystem:update(dt)
                end
                return
            end

            -- Update the current world
            if Engine.currentWorld then
                -- Update chunk system
                if Engine.currentWorld.chunkSystem then
                    Engine.currentWorld.chunkSystem:update(dt)
                end

                -- Update entity system
                if Engine.currentWorld.entitySystem then
                    Engine.currentWorld.entitySystem:update(dt)
                end

                -- Update weather system
                if Engine.currentWorld.weatherSystem then
                    if type(Engine.currentWorld.weatherSystem.update) == "function" then
                        Engine.currentWorld.weatherSystem.update(dt)  -- Fixed: use dot syntax, not colon
                    else
                        -- Use the global weather system if the world one doesn't have an update method
                        if Engine.systems.weatherSystem then
                            Engine.systems.weatherSystem.update(dt)
                        end
                    end
                end
            end

            -- Update player movement
            --
            -- NOTE: MODIFICATION HISTORY
            -- [2023-07-15] Attempted to fix player movement by adding property checks and explicit update calls.
            -- These changes DID NOT resolve the movement issue.
            -- DO NOT attempt similar changes without first identifying the root cause through debugging.
            -- The issue may be elsewhere in the entity system or tile rendering.
            --
            -- [2023-07-15] Multiple additional attempts to fix player movement in controller.lua and player.lua
            -- have been unsuccessful. The issue appears to be more complex than initially thought.
            -- The problem might be related to how the entity system registers and updates entities,
            -- how the world handles player movement, or issues with the game's main update loop.
            -- Further investigation would require more extensive debugging of the entire game engine architecture.
            --
            -- [2023-07-15] IMPORTANT DISCOVERY FROM CONSOLE OUTPUT:
            -- Console shows that key presses are being captured correctly:
            --   "Engine: Key pressed: w/a/s/d"
            -- But they're being forwarded to the UI system as text input:
            --   "Engine.textInput called with: w/a/s/d"
            --   "Forwarding to UI system directly"
            -- This suggests the movement keys are being intercepted by the text input system
            -- instead of being processed as movement commands. This would explain why the
            -- player character isn't moving - the keys are being treated as text input rather
            -- than control commands. The issue is likely in the input handling system, not in
            -- the player movement code itself.
            --
            -- [2023-07-15] FIX IMPLEMENTED:
            -- Modified Engine.textInput function in engine.lua to ignore movement keys (w, a, s, d)
            -- during gameplay state. This prevents movement keys from being captured by the text
            -- input system and allows them to be processed as movement commands.
            --
            -- [2023-07-15] UPDATE: The fix successfully prevents movement keys from being intercepted
            -- by the text input system (confirmed in console output), but the player still doesn't move.
            -- This suggests there might be another issue in the movement system or entity update logic.
            -- The keys are correctly detected but something else is preventing the actual movement.
            --
            -- [2023-07-15] ADDITIONAL OBSERVATIONS:
            -- 1. The player's direction is being updated (saved as "up" in character data)
            -- 2. The player's position is being saved, but it's unclear if it's changing during gameplay
            -- 3. Mouse clicks are being registered, but don't appear to affect player movement
            -- 4. The issue might be related to how the player's position is updated in the world
            --    or how the camera/viewport follows the player
            --
            -- [2023-07-15] FINAL CONCLUSION:
            -- The text input fix was successful in preventing movement keys from being intercepted,
            -- but the player still doesn't move. This suggests a deeper issue in the movement system.
            -- Possible causes include:
            -- 1. The player entity might not be properly registered in the entity system
            -- 2. The movement vector might not be properly applied to the player's position
            -- 3. The camera/viewport might not be following the player's position
            -- 4. There might be collision detection preventing movement
            -- 5. The world rendering might not be updating to reflect the player's new position
            -- Further debugging would require more extensive modifications to add detailed logging
            -- throughout the movement and rendering systems.
            --
            -- [2023-07-15] ROOT CAUSE IDENTIFIED AND FIXED:
            -- The issue was in the viewport management system. During gameplay state, the camera was
            -- intentionally configured to NOT follow the player. This was causing the player to appear
            -- stationary even though their position was being updated correctly.
            --
            -- Fix 1: Modified viewport_management.lua to allow the camera to follow the player during
            -- gameplay state by removing the early return statement that was preventing camera updates.
            --
            -- Fix 2: Modified renderer.lua to use the player's actual position instead of the viewport
            -- position when calculating the camera transform. This ensures the camera follows the player
            -- immediately without any lag.
            --
            -- Fix 3: Fixed a bug in weather_system.lua where it was attempting to perform arithmetic
            -- on a table value instead of a number. Added type checking to handle this case.
            --
            if Engine.player and Engine.systems.controller then
                -- Apply player movement with player ID 1 (default player)
                -- This ensures the player gets input from the correct controller
                Engine.systems.controller.applyPlayerMovement(Engine.player, dt, nil, 1)

                -- Update player entity in the world
                if Engine.currentWorld and Engine.currentWorld.entitySystem then
                    -- Make sure the player entity is properly registered
                    Engine.currentWorld.entitySystem:register(Engine.player)
                    -- Update all entities
                    Engine.currentWorld.entitySystem:update(dt)
                end
            end

            -- Update viewport to follow player
            if Engine.systems.viewportManager and Engine.player and Engine.player.position then
                -- Use debug console for clean logging
                local DebugConsole = require("utils.debug_console")
                DebugConsole.logPlayerPosition(Engine.player.position.x, Engine.player.position.y, "Before Viewport Update")

                -- FIXED: Allow the camera to follow the player
                -- Original comment: "Update the viewport manager but don't change the camera position"
                -- This comment suggests the camera was intentionally not following the player
                Engine.systems.viewportManager:update(dt)

                DebugConsole.logPlayerPosition(Engine.player.position.x, Engine.player.position.y, "After Viewport Update")
            end

            -- Update UI messages
            if Engine.systems.uiSystem and Engine.systems.uiSystem.messages then
                for i = #Engine.systems.uiSystem.messages, 1, -1 do
                    local message = Engine.systems.uiSystem.messages[i]
                    message.current = message.current + dt

                    if message.current >= message.duration then
                        table.remove(Engine.systems.uiSystem.messages, i)
                    end
                end
            end
        end,
        draw = function()
            -- Clear screen
            love.graphics.clear(0.1, 0.1, 0.2, 1)

            -- Always draw the live game world in the background (no caching)
            love.graphics.clear(0.1, 0.1, 0.2, 1)

            -- Use renderer for efficient drawing of the live world
            if Engine.systems.renderer and Engine.currentWorld then
                love.graphics.push()
                -- Make sure performance mode is applied consistently
                if Engine.settings.performanceMode then
                    Engine.systems.renderer.enableShaders = false
                    Engine.systems.renderer.enableBlur = false
                    Engine.systems.renderer.simplifiedTiles = true
                    Engine.systems.renderer.simplifiedEntities = true
                else
                    -- Ensure high quality rendering for menu view
                    Engine.systems.renderer.enableShaders = true
                    Engine.systems.renderer.enableBlur = true
                    Engine.systems.renderer.simplifiedTiles = false
                    Engine.systems.renderer.simplifiedEntities = false
                end

                -- Ensure chunk system has good view distance for menu
                if Engine.currentWorld and Engine.currentWorld.chunkSystem then
                    Engine.currentWorld.chunkSystem.viewDistance = 4  -- Increased from default 2
                    Engine.currentWorld.chunkSystem.maxLoadedChunks = 100  -- Increased from default 25
                end

                -- Ensure the world has a proper time of day and weather for rendering
                if Engine.currentWorld and not Engine.currentWorld.chunkSystem then
                    -- Create a simple time of day system if none exists
                    Engine.currentWorld.chunkSystem = Engine.currentWorld.chunkSystem or {}
                    Engine.currentWorld.chunkSystem.timeOfDay = Engine.currentWorld.chunkSystem.timeOfDay or {
                        hour = math.random(0, 23),
                        minute = math.random(0, 59),
                        dayPhase = "day"
                    }
                end

                -- Draw the world with our LOD system
                -- Pass a dummy player object with fixed position (same as menu)
                local dummyPlayer = {
                    position = {
                        x = 0,
                        y = 0
                    }
                }

                -- Store the real player position for entity rendering
                if Engine.player and Engine.player.position then
                    -- Update entity position in the world for rendering
                    if Engine.currentWorld and Engine.currentWorld.entitySystem then
                        -- Make sure the player entity is properly registered
                        Engine.currentWorld.entitySystem:register(Engine.player)

                        -- Store the player in the world for easy access
                        Engine.currentWorld.player = Engine.player
                    end
                end

                -- Draw the world with fixed camera position
                Engine.systems.renderer:drawWorld(Engine.currentWorld, dummyPlayer, Engine.systems.viewportManager)

                -- Draw the player entity separately if in gameplay state
                if Engine.stateManager:getCurrentState() == "gameplay" and Engine.player then
                    -- Save current transform
                    love.graphics.push()

                    -- Apply the same camera transform used for the world
                    Engine.systems.renderer:applyCameraTransform(dummyPlayer, Engine.systems.viewportManager)

                    --[[
                        Player Triangle Visualization System
                        ----------------------------------

                        This code draws the player entity as a triangle in the game world.
                        It's part of the character movement system visualization, showing
                        the player's position and direction.

                        IMPORTANT NOTE: THIS IS NOT THE ACTUAL ON-SCREEN CHARACTER THAT PLAYERS SEE DURING GAMEPLAY.
                        THIS IS A SECONDARY REPRESENTATION USED FOR DEBUGGING OR MENU PURPOSES.
                        THE ACTUAL PLAYER VISUALS ARE HANDLED IN renderer.lua AND entity_visuals.lua.

                        The triangle visualization:
                        1. Shows the player's current position in the world
                        2. Points in the direction the player is facing
                        3. Displays health and mana bars above the player's head

                        This visualization is affected by the player's movement and direction,
                        providing visual feedback on the character movement system.
                    ]]

                    love.graphics.setColor(1, 1, 1, 1)

                    -- Check if Engine.player exists and has a position
                    -- This safety check prevents errors when the player entity doesn't exist
                    if Engine.player and Engine.player.position then
                        -- Draw a triangle instead of a circle to show player direction
                        local playerX, playerY = Engine.player.position.x, Engine.player.position.y
                        local triangleSize = 12

                        -- Calculate triangle vertices based on player direction
                        local direction = Engine.player.direction

                        -- Initialize vertex variables with default values (pointing down)
                        local vx1, vy1 = playerX, playerY + triangleSize
                        local vx2, vy2 = playerX - triangleSize, playerY - triangleSize
                        local vx3, vy3 = playerX + triangleSize, playerY - triangleSize

                        -- Handle different direction formats
                        if type(direction) == "string" then
                            -- Direction is a string, use it directly
                            if direction == "up" then
                                -- Triangle pointing up
                                vx1, vy1 = playerX, playerY - triangleSize
                                vx2, vy2 = playerX - triangleSize, playerY + triangleSize
                                vx3, vy3 = playerX + triangleSize, playerY + triangleSize
                            elseif direction == "left" then
                                -- Triangle pointing left
                                vx1, vy1 = playerX - triangleSize, playerY
                                vx2, vy2 = playerX + triangleSize, playerY - triangleSize
                                vx3, vy3 = playerX + triangleSize, playerY + triangleSize
                            elseif direction == "right" then
                                -- Triangle pointing right
                                vx1, vy1 = playerX + triangleSize, playerY
                                vx2, vy2 = playerX - triangleSize, playerY - triangleSize
                                vx3, vy3 = playerX - triangleSize, playerY + triangleSize
                            end
                            -- If direction is "down" or any other string, use the default (down) values
                        elseif type(direction) == "table" then
                            -- Direction is a vector, convert to string direction
                            local dx = direction.x or 0
                            local dy = direction.y or 0

                            if math.abs(dx) > math.abs(dy) then
                                -- Horizontal movement is dominant
                                if dx > 0 then
                                    -- Right
                                    vx1, vy1 = playerX + triangleSize, playerY
                                    vx2, vy2 = playerX - triangleSize, playerY - triangleSize
                                    vx3, vy3 = playerX - triangleSize, playerY + triangleSize
                                else
                                    -- Left
                                    vx1, vy1 = playerX - triangleSize, playerY
                                    vx2, vy2 = playerX + triangleSize, playerY - triangleSize
                                    vx3, vy3 = playerX + triangleSize, playerY + triangleSize
                                end
                            else
                                -- Vertical movement is dominant
                                if dy > 0 then
                                    -- Down (already set as default)
                                else
                                    -- Up
                                    vx1, vy1 = playerX, playerY - triangleSize
                                    vx2, vy2 = playerX - triangleSize, playerY + triangleSize
                                    vx3, vy3 = playerX + triangleSize, playerY + triangleSize
                                end
                            end
                        end
                        -- If direction is nil or any other type, use the default (down) values

                        -- Draw the triangle
                        love.graphics.polygon("fill", vx1, vy1, vx2, vy2, vx3, vy3)

                        -- Draw health and mana bars above the player
                        local barWidth = 20
                        local barHeight = 3
                        local barSpacing = 2
                        local barY = playerY - triangleSize - barHeight - barSpacing

                        -- Get player stats
                        local healthPercent = 1.0
                        local manaPercent = 1.0

                        -- If player has character with stats, use them
                        if Engine.player.character then
                            if Engine.player.character.stats then
                                -- Calculate health percentage
                                if Engine.player.character.stats.health and Engine.player.character.stats.maxHealth then
                                    healthPercent = math.max(0, math.min(1, Engine.player.character.stats.health / Engine.player.character.stats.maxHealth))
                                end

                                -- Calculate mana percentage
                                if Engine.player.character.stats.mana and Engine.player.character.stats.maxMana then
                                    manaPercent = math.max(0, math.min(1, Engine.player.character.stats.mana / Engine.player.character.stats.maxMana))
                                end
                            end
                        end

                        -- Draw health bar background
                        love.graphics.setColor(0.2, 0.2, 0.2, 0.8)
                        love.graphics.rectangle("fill", playerX - barWidth/2, barY, barWidth, barHeight)

                        -- Draw health bar fill
                        love.graphics.setColor(0.8, 0.2, 0.2, 1.0)
                        love.graphics.rectangle("fill", playerX - barWidth/2, barY, barWidth * healthPercent, barHeight)

                        -- Draw mana bar background
                        love.graphics.setColor(0.2, 0.2, 0.2, 0.8)
                        love.graphics.rectangle("fill", playerX - barWidth/2, barY + barHeight + barSpacing, barWidth, barHeight)

                        -- Draw mana bar fill
                        love.graphics.setColor(0.2, 0.2, 0.8, 1.0)
                        love.graphics.rectangle("fill", playerX - barWidth/2, barY + barHeight + barSpacing, barWidth * manaPercent, barHeight)

                    end -- End of if Engine.player and Engine.player.position

                    -- Restore transform
                    love.graphics.pop()
                end
                love.graphics.pop()

                -- Ensure isometric mode is enabled for horizon line
                if Engine.systems.colorUtils then
                    Engine.systems.colorUtils.isometricDebug = true
                end

                -- Draw horizon line explicitly for gameplay state
                if Engine.systems.renderer and Engine.systems.renderer.drawHorizonLine then
                    Engine.systems.renderer:drawHorizonLine()
                end
            else
                -- Fallback clear screen if renderer isn't available
                love.graphics.clear(0.1, 0.1, 0.2, 1)
                love.graphics.setColor(1,1,1)
                love.graphics.print("Renderer system not found!", 10, 10)
            end

            -- Draw UI elements
            if Engine.systems.uiSystem and type(Engine.systems.uiSystem.draw) == "function" then
                Engine.systems.uiSystem:draw()
            end

            -- Draw version info
            love.graphics.origin()
            love.graphics.setColor(1, 1, 1, 0.7)
            love.graphics.print("Version: Alpha 0.1", 10, Engine.settings.screenHeight - 30)

            -- Draw controller debug info if enabled
            if Engine.systems.controller and Engine.systems.controller.debug then
                Engine.systems.controller.drawDebug()
            end
        end,
        exit = function()
            print("Exiting gameplay state")

            -- When exiting gameplay, we don't remove the player entity completely
            -- Instead, we'll save it for potential reuse and let the menu state handle it
            if Engine.player then
                -- Save character data
                if Engine.systems.database then
                    print("Saving player character: " .. (Engine.player.name or "Unknown"))
                    Engine.systems.database.saveCharacter(Engine.player)
                else
                    print("WARNING: Could not save character - database system not available")
                end

                -- Note: We don't unregister the player or remove viewport tracking here
                -- That's handled by the menu state's enter function
            end

            -- Save world state
            if Engine.currentWorld and Engine.currentWorld.chunkSystem then
                print("Saving world state")
                Engine.currentWorld.chunkSystem:saveAllChunks()
            end

            -- Check if there are any active players
            local activePlayers = 0
            if Engine.currentWorld and Engine.currentWorld.entitySystem then
                -- Count active player entities
                for _, entity in pairs(Engine.currentWorld.entitySystem.entities) do
                    if entity.type == "player" then
                        activePlayers = activePlayers + 1
                    end
                end
            end

            -- If no active players, revert to hub or randomized hub chunk
            if activePlayers == 0 and Engine.currentWorld then
                print("No active players, reverting to hub view")

                -- Option to randomize hub appearance when returning to menu
                if Engine.settings.randomizeHubOnReturn then
                    -- Generate a new seed for the hub
                    local newSeed = os.time() + math.random(1, 1000)
                    print("Randomizing hub with new seed: " .. newSeed)

                    -- Update hub chunk with new appearance
                    if Engine.currentWorld.chunkSystem then
                        local hubChunk = Engine.currentWorld.chunkSystem:getChunkAt(0, 0)
                        if hubChunk then
                            hubChunk.seed = newSeed
                            -- Regenerate hub appearance
                            if Engine.systems.worldCore and Engine.systems.worldCore.regenerateHubChunk then
                                Engine.systems.worldCore.regenerateHubChunk(hubChunk, newSeed)
                            end
                        end
                    end
                end
            end
        end,
        keyPressed = function(key)
            -- State-specific key handling (if any)
            -- Note: Global key handling is now in controller.lua
            -- This comment is a reminder: DONT ADD BUTTONS HERE. USE CONTROLLER.LUA
        end,
        mousePressed = function(x, y, button)
            -- Forward clicks to in-game menu (includes Debug tab)
            if Engine.systems.uiSystem and Engine.systems.uiSystem.inGameMenu and Engine.systems.uiSystem.inGameMenu.active then
                local handled = Engine.systems.uiSystem:handleInGameMenuClick(x, y)
                    if handled then
                        return -- Click was handled by UI system
                    end
                end
            -- Normal gameplay click handling continues here
        end
    })

    -- Options state
    self:registerHandler("options", {
        enter = function()
            print("Entering options state")
        end,
        update = function(dt)
            -- Options menu update logic
        end,
        draw = function()
            -- Draw options menu
            love.graphics.clear(0.1, 0.1, 0.2, 1)
            love.graphics.setColor(1, 1, 1, 1)
            love.graphics.print("Options", 100, 100, 0, 2, 2)

            -- Draw options
            love.graphics.print("Sound: " .. (Engine.settings.soundEnabled and "ON" or "OFF"), 100, 150)
            love.graphics.print("Music Volume: " .. Engine.settings.musicVolume * 100 .. "%", 100, 180)
            love.graphics.print("SFX Volume: " .. Engine.settings.sfxVolume * 100 .. "%", 100, 210)
            love.graphics.print("Fullscreen: " .. (Engine.settings.fullscreen and "ON" or "OFF"), 100, 240)

            -- Back button
            love.graphics.setColor(0.8, 0.2, 0.2)
            love.graphics.rectangle("fill", 100, 300, 200, 40)
            love.graphics.setColor(1, 1, 1)
            love.graphics.print("Back to Menu", 130, 310)
        end,
        exit = function()
            print("Exiting options state")
            Engine.saveSettings()
        end,
        keyPressed = function(key)
            if key == "escape" then
                self:changeState("menu")
            end
        end,
        mousePressed = function(x, y, button)
            -- Check if back button was clicked
            if x >= 100 and x <= 300 and y >= 300 and y <= 340 then
                self:changeState("menu")
            end
        end
    })
end

-- Perform state change
function StateHandlers:processStateChange()
    if self.nextState then
        -- Call exit function of current state
        if self.handlers[self.currentState] and self.handlers[self.currentState].exit then
            self.handlers[self.currentState].exit()
        end

        -- Update current state
        self.currentState = self.nextState
        self.nextState = nil

        -- Call enter function of new state
        if self.handlers[self.currentState] and self.handlers[self.currentState].enter then
            self.handlers[self.currentState].enter()
        end
    end
end

-- Change game state
function StateHandlers:changeState(newState)
    if self.handlers[newState] then
        local Engine = require("engine")
        local oldState = self.currentState

        -- Handle session start/end when transitioning to/from gameplay
        if newState == "gameplay" and oldState ~= "gameplay" then
            -- Starting a gameplay session
            print("Transitioning to gameplay state, starting session")
            Engine.startSession()
        elseif oldState == "gameplay" and newState ~= "gameplay" then
            -- Ending a gameplay session
            print("Transitioning from gameplay state, ending session")
            Engine.endSession()
        end

        self.nextState = newState
    else
        print("Warning: Tried to change to unknown state '" .. tostring(newState) .. "'")
    end
end

-- Get current state
function StateHandlers:getCurrentState()
    return self.currentState
end

-- Initialize the state system
function StateHandlers:init()
    self:registerAllHandlers()
    return self
end

-- Handle key press event
function StateHandlers:keyPressed(key, scancode, isrepeat)
    if self.handlers[self.currentState] and self.handlers[self.currentState].keyPressed then
        self.handlers[self.currentState].keyPressed(key, scancode, isrepeat)
    end
end

-- Handle key release event
function StateHandlers:keyReleased(key, scancode)
    if self.handlers[self.currentState] and self.handlers[self.currentState].keyReleased then
        self.handlers[self.currentState].keyReleased(key, scancode)
    end
end

-- Handle mouse press event
function StateHandlers:mousePressed(x, y, button, istouch, presses)
    if self.handlers[self.currentState] and self.handlers[self.currentState].mousePressed then
        self.handlers[self.currentState].mousePressed(x, y, button, istouch, presses)
    end
end

-- Handle mouse release event
function StateHandlers:mouseReleased(x, y, button, istouch, presses)
    if self.handlers[self.currentState] and self.handlers[self.currentState].mouseReleased then
        self.handlers[self.currentState].mouseReleased(x, y, button, istouch, presses)
    end
end

-- Handle mouse movement
function StateHandlers:mouseMoved(x, y, dx, dy, istouch)
    if self.handlers[self.currentState] and self.handlers[self.currentState].mouseMoved then
        self.handlers[self.currentState].mouseMoved(x, y, dx, dy, istouch)
    end
end

-- Update the current state
function StateHandlers:update(dt)
    -- Handle state transitions
    if self.nextState and self.nextState ~= self.currentState then
        local oldState = self.currentState
        self.currentState = self.nextState
        self.nextState = nil

        print("State changed from '" .. oldState .. "' to '" .. self.currentState .. "'")

        -- Call exit handler for old state
        if self.handlers[oldState] and self.handlers[oldState].exit then
            self.handlers[oldState].exit()
        end

        -- Call enter handler for new state
        if self.handlers[self.currentState] and self.handlers[self.currentState].enter then
            self.handlers[self.currentState].enter()
        end
    end

    -- Update current state
    if self.handlers[self.currentState] and self.handlers[self.currentState].update then
        self.handlers[self.currentState].update(dt)
    end
end

-- Draw the current state
function StateHandlers:draw()
    if self.handlers[self.currentState] and self.handlers[self.currentState].draw then
        self.handlers[self.currentState].draw()
    end
end

return StateHandlers
