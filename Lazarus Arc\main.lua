-- main.lua
-- Main entry point for the game using LÖVE framework

-- Load the engine
local Engine = require("engine")

-- Initialize love modules
function love.load()
    -- Set random seed
    math.randomseed(os.time())
    print("🎮 Starting Lazarus Arc - " .. os.date("%Y-%m-%d %H:%M:%S"))
    
    -- Initialize the engine
    Engine.init()
end

-- The engine will set up all other LÖVE callbacks through Engine.setupLoveCallbacks()
-- This keeps main.lua clean and delegates all logic to the engine and its subsystems
