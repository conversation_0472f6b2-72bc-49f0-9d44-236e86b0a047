-- classes/hunter.lua
-- Hunter class definition for Lazarus Arc

local Hunter = {
    name = "Hunter",
    description = "A skilled tracker and expert marksman who excels at ranged combat and survives in the wild.",
    icon = "hunter_icon.png",

    -- 🎭 **Playstyle & Trade-offs**
    pros = {
        "Expert with ranged weapons",
        "Survival skills in the wilderness",
        "Superior mobility and stealth",
        "Can tame and command animal companions"
    },
    cons = {
        "Limited in close-quarters combat",
        "Lower defense against direct attacks",
        "Requires preparation and awareness",
        "Dependent on ammunition for maximum effectiveness"
    },

    -- 🏋 **Base Stats & Growth**
    base_stats = { 
        health = 100, 
        maxHealth = 100,
        stamina = 110, 
        maxStamina = 110,
        mana = 30, 
        maxMana = 30,
        pattack = 12, 
        pdefense = 6, 
        mattack = 6, 
        mdefense = 6, 
        speed = 9, 
        luck = 7
    },

    stat_growth = {
        health = 8,
        stamina = 9,
        mana = 2,
        pattack = 1.2,
        pdefense = 0.6,
        mattack = 0.4,
        mdefense = 0.5,
        speed = 0.9,
        luck = 0.7
    },

    -- ⚔️ **Weapon Proficiency**
    proficiencies = { 
        bow = 2.2,       -- Primary weapon
        crossbow = 2.0,
        gun = 1.8,
        dagger = 1.5,    -- For close combat
        spear = 1.3,
        sword = 1.0,
        axe = 1.0,
        magic = 0.6      -- Basic nature magic
    },

    -- 🌿 **Elemental Affinities**
    element_affinities = {
        nature = 15,   -- Strong connection to nature
        air = 10,      -- Wind affects arrows less
        earth = 5,     -- Terrain knowledge
        fire = -5      -- Vulnerable to fire
    },

    -- 💥 **Overdrive Abilities**
    overdrive = { 
        "rapid_volley",        -- Fire multiple arrows in quick succession
        "beast_communion",     -- Summon animals to assist in battle
        "phantom_arrows"       -- Arrows that bypass physical defenses
    },

    -- 🎯 **Starting Skills**
    starting_skills = {
        "precise_shot",
        "animal_tracking",
        "camouflage"
    },

    -- 📈 **Skill Progression**
    skill_tree = {
        tier1 = {
            "multi_shot",
            "trap_mastery",
            "swift_step"
        },
        tier2 = {
            "animal_companion",
            "poison_arrows",
            "eagle_eye",
            "survival_instinct"
        },
        tier3 = {
            "piercing_shot",
            "nature's_blessing",
            "perfect_ambush"
        }
    },

    -- 🎒 **Inventory & Carrying Capacity**
    inventory_limit = 100,

    -- ✨ **Special Abilities (Passive Perks)**
    special_abilities = {
        "Beast Whisperer",     -- Can communicate with animals
        "Pathfinder",          -- Enhanced movement speed in natural environments
        "Precise Aim",         -- Increased critical hit chance with ranged weapons
        "Survivalist",         -- Can find food and resources in the wild
        "Night Vision",        -- Better visibility in low light conditions
    },

    -- 🎭 **Prestige Paths**
    prestige_paths = {
        ranger = {
            description = "A specialized hunter who has mastered survival in specific terrains and environments.",
            bonuses = {
                "Enhanced movement and camouflage in natural environments",
                "Can track any creature, even those that leave no physical trace",
                "Command multiple animal companions at once"
            },
            weaknesses = {
                "Less effective in urban environments",
                "Relies on knowledge of terrain"
            }
        },

        sniper = {
            description = "A hunter who has perfected the art of long-range combat and precision strikes.",
            bonuses = {
                "Extreme accuracy at long distances",
                "Higher damage on critical hits",
                "Chance to instantly defeat lesser enemies with headshots"
            },
            weaknesses = {
                "Vulnerable when detected",
                "Less effective at close range"
            }
        },

        beastmaster = {
            description = "A hunter who specializes in taming and fighting alongside powerful animal companions.",
            bonuses = {
                "Can tame more powerful and exotic creatures",
                "Enhanced combat abilities when fighting with companions",
                "Animal companions gain unique abilities"
            },
            weaknesses = {
                "Less effective when separated from companions",
                "Must divide attention between self and companions"
            }
        }
    },

    -- ⚔️ **Hybrid & Multiclass Options**
    hybrid_options = {
        druid_hunter = {
            description = "A Hunter who has embraced nature magic to enhance their tracking and combat abilities.",
            pros = {
                "Can use nature magic to control battlefield",
                "Enhanced bond with animal companions",
                "Better healing and sustainability"
            },
            cons = {
                "Divided focus between weapons and spells",
                "Less raw damage output than pure hunters"
            }
        },

        shadow_stalker = {
            description = "A hunter who has learned the arts of stealth and assassination.",
            pros = {
                "Enhanced stealth capabilities",
                "Deadly first-strike abilities",
                "Poisons and traps deal more damage"
            },
            cons = {
                "Less effective in prolonged combat",
                "Lower stamina reserves than pure hunters"
            }
        }
    },
    
    -- Starting equipment recommendations
    starting_equipment = {
        primary = "huntsman_bow",
        secondary = "skinning_knife",
        armor = "leather_garb",
        accessories = {"hawk_feather_charm", "tracker's_compass"}
    },
    
    -- Starting abilities
    starting_abilities = {
        "track_prey",
        "beast_empathy",
        "quick_shot"
    }
}

return Hunter 