local Raccoon = {
    id = "raccoon",
    name = "Racco<PERSON>",
    type = "raccoon",

    -- Entity categories
    categories = {"animal", "omnivore", "scavenger", "nocturnal"},

    -- Threat categories
    threatCategories = {"predator", "large"},

    -- Stats
    maxHealth = 20,
    health = 20,
    speed = 2.2,
    visionRange = 15, -- Good night vision

    -- Behaviors
    behaviors = {"wander", "flee", "scavenge", "climb"},

    -- Behavior configurations
    behaviorConfigs = {
        wander = {
            moveSpeed = 1.8,
            changeDirectionChance = 0.15,
            idleChance = 0.3,
            idleDuration = {2, 5},
            wanderRadius = 20,
            -- More active at night
            nightWanderRadius = 30,
            nightMoveSpeed = 2.5
        },
        flee = {
            useCategories = true,
            moveSpeed = 4.0,
            detectionRadius = 8,
            useHiding = true
        },
        scavenge = {
            foodTypes = {"carrion", "fruit", "nuts", "trash"}, -- Wide range of food
            foodValue = {
                carrion = 10,
                fruit = 5,
                nuts = 3,
                trash = 2 -- Even eats trash!
            },
            searchRadius = 25, -- Larger search area
            fleeWhenThreatened = true
        },
        climb = {
            canClimb = true,
            climbSpeed = 1.0,
            maxClimbHeight = 5,
            -- Prefers to climb trees at night
            nightClimbChance = 0.8
        }
    },

    -- Appearance
    appearance = {
        sprite = "raccoon", -- Replace with your raccoon sprite
        scale = 0.9,
        animations = {
            "idle",
            "walk",
            "climb",
            "eat",
            "sniff",
            "wash" -- Add a washing animation
        }
    },

    -- Sound effects
    sounds = {
        chitter = "raccoon_chitter",
        growl = "raccoon_growl"
    },

    -- Loot drops
    drops = {
        {id = "raccoon_fur", chance = 0.6, quantity = {1, 2}},
        {id = "meat", chance = 0.7, quantity = {1, 2}},
        {id = "hide", chance = 0.5, quantity = {1, 1}},
        {id = "claw", chance = 0.4, quantity = {2, 4}},
        {id = "tail", chance = 0.3, quantity = {1, 1}}
    }
}

-- Initialize the raccoon entity
function Raccoon.init(entity, world)
    entity.x = entity.x or 0
    entity.y = entity.y or 0
    entity.rotation = entity.rotation or 0
    entity.width = 1
    entity.height = 1
    entity.type = "raccoon"
    
    -- Apply behavior
    local behaviorName = "raccoon_behavior"
    -- Check if world is not nil before trying to access it
    if world and world.modules and world.modules.behaviors then
        local behavior = world.modules.behaviors[behaviorName]
        if behavior then
            behavior.apply(entity)
        end
    end
    
    return entity
end

return Raccoon