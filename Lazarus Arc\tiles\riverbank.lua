-- tiles/riverbank.lua
local RiverbankTile = {
    id = "riverbank",
    name = "Riverbank",
    passable = true, -- Passable, but footing can vary
    variants = 6, -- e.g., Muddy Bank, Sandy Shore, Grassy Verge, Rocky Ledge, Eroded Cutbank, Wooded Bank

    -- Entities found near rivers
    spawns = {"river_otter", "beaver_lodge_small", "kingfisher", "river_reeds", "willow_tree_sapling", "driftwood_pile", "fishing_spot_marker"},

    -- Movement properties
    movementSpeed = 0.95, -- Default, slightly uneven ground near water

    -- Riverbank-specific properties
    adjacentWaterType = "river", -- Assumes a 'river' water tile type exists
    erosionLevel = 0.3, -- How much the bank is eroded by the current (0-1)
    groundType = "dirt", -- Default material (dirt, mud, sand, grass, rock)
    bankSlope = 0.2, -- Gentle slope towards water usually (0-1)
    providesWaterAccess = true, -- Key function

    -- Interaction function
    interact = function(tile, entity)
        -- Fishing
        if entity.hasItem and entity.hasItem("fishing_rod") then
             local fishingSkill = (entity.skills and entity.skills.fishing) or 0
             local catchChance = 0.3 + (fishingSkill * 0.06) -- Rivers might have decent fishing
             if math.random() < catchChance then
                 local riverFish = { -- Different fish than deep water/shallow ponds
                     {id="river_trout", name="River Trout", chance=0.5},
                     {id="catfish", name="Catfish", chance=0.3},
                     {id="freshwater_pearl_oyster", name="Freshwater Pearl Oyster", chance=0.1}, -- Rare find
                     {id="snapping_turtle", name="Snapping Turtle", chance=0.1} -- Hazard/resource?
                 }
                 -- Simplified roll [Inspired by water.txt: 210-214]
                 local cumulativeChance, roll = 0, math.random()
                 local caughtItem = riverFish[1] 
                 for _, fishType in ipairs(riverFish) do
                     cumulativeChance = cumulativeChance + fishType.chance
                     if roll <= cumulativeChance then caughtItem = fishType; break end
                 end
                 if caughtItem.id == "snapping_turtle" then
                      return { success=true, message="You snag something heavy... a feisty Snapping Turtle!", effects={{type="spawn_hostile", id="snapping_turtle", count=1}} }
                 else
                      return { success = true, message = "You cast into the river and catch a " .. caughtItem.name .. "!", effects={{type="add_item", item=caughtItem.id, quantity=1}} }
                 end
             else
                 return { success = false, message = "The current is strong, but the fish aren't biting." }
             end
        end

        -- Drink/Gather Water (Assumes adjacent 'river' tile is source)
        if entity.canGatherWater then -- Simplified check
             return { success=true, message="You kneel by the flowing river and drink/gather fresh water.", effects={{type="gather_water", quality=0.8}} } -- River water quality might vary
        end

        -- Look for washed-up items or tracks
        if math.random() < 0.2 then
             local bankFinds = {
                 {id="smooth_river_stone", chance=0.4},
                 {id="waterlogged_branch", chance=0.3},
                 {id="otter_tracks", chance=0.2, isTrack=true},
                 {id="message_in_bottle_river", chance=0.1}
             }
             for _, item in ipairs(bankFinds) do
                 if math.random() < item.chance then
                     if item.isTrack then
                          return { success=true, message="You spot " .. item.id .. " in the soft earth near the water.", effects={{type="gain_tracking_clue", clue=item.id}} }
                     else
                          return { success = true, message = "Something washed up on the bank catches your eye: a " .. item.id .. ".", effects={{type="add_item", item=item.id, quantity=1}} }
                     end
                 end
             end
        end

        -- General examination
        local erosionDesc = ""
        if tile.erosionLevel > 0.6 then erosionDesc = " The bank looks heavily eroded and unstable here." end
        return {
            success = false,
            message = "The bank slopes down towards the flowing river. The ground is composed of " .. tile.groundType .. "." .. erosionDesc
        }
    end,

    -- Weather effects
    weatherEffects = {
        rain = function(tile)
            tile.isWet = true
            if tile.groundType == "dirt" or tile.groundType == "mud" then
                 tile.groundType = "mud" -- Becomes muddy
                 tile.slipperiness = 0.6
                 tile.movementSpeed = 0.7
            else
                 tile.slipperiness = 0.3
            end
            tile.erosionLevel = tile.erosionLevel + 0.05 -- Rain increases erosion
            -- River level might rise, potentially flooding bank
             if world.riverLevel > tile.floodThreshold then -- Assumes world tracks river level
                 return "transform", "shallow_water" -- Temporarily flooded
             end
            return "visual_effect", "wet_riverbank"
        end,
        
        freeze = function(tile) -- Ground freezes, river edge might get icy
             tile.isFrozen = true
             tile.slipperiness = 0.5
             tile.movementSpeed = 0.9 -- Frozen ground is hard but maybe uneven
             if world.temperature < -5 then -- River edge freezes
                 return "create_overlay", "river_ice_edge"
             end
             return "visual_effect", "frozen_riverbank"
        end,
        
        sun = function(tile) -- Drying effect
             tile.isWet = false
             tile.isFrozen = false
             tile.slipperiness = 0
             tile.movementSpeed = 0.95 -- Reset speed
             if tile.groundType == "mud" then tile.groundType = "dirt" end -- Mud dries to dirt
             return "remove_visual_effect", {"wet_riverbank", "frozen_riverbank", "river_ice_edge"}
        end
        -- Wind has less direct effect unless it's extreme or affects river waves
    },

     -- Time effects
    timeEffects = {
        night = function(tile)
            tile.ambientSound = "river_flow_night" -- Quieter flow, maybe nocturnal animals
            -- Mist might form over river
             if math.random() < 0.3 then return "visual_effect", "river_mist_low" end
            return nil
        end,
        
        day = function(tile)
             tile.ambientSound = "river_flow_day" -- Flowing water, birds
             return "remove_visual_effect", "river_mist_low"
        end
        -- River flow speed/level might change based on upstream events/time?
    },
    
    -- Visual properties
    visual = {
        base_color = {0.5, 0.45, 0.3}, -- Default earthy bank color
        variants = {
            {name = "Muddy Bank", groundType="mud", texture="bank_mud", movementSpeed=0.7, slipperiness=0.6},
            {name = "Sandy Shore", groundType="sand", texture="bank_sand", base_color={0.8, 0.7, 0.55}, movementSpeed=0.9},
            {name = "Grassy Verge", groundType="grass", texture="bank_grass", base_color={0.4, 0.55, 0.3}},
            {name = "Rocky Ledge", groundType="rock", texture="bank_rock", movementSpeed=0.85},
            {name = "Eroded Cutbank", bankSlope=0.7, texture="bank_eroded", erosionLevel=0.8}, -- Steeper, unstable
            {name = "Wooded Bank", groundType="dirt", overlay_objects={{name="willow_tree", chance=0.6}, {name="exposed_roots", chance=0.4}}}
        },
        decoration_objects = {
            {name = "river_reed_clump", chance = 0.4},
            {name = "smooth_river_rock", chance = 0.3},
            {name = "driftwood_small", chance = 0.2},
            {name = "erosion_gully", chance = function(tile) return 0.1 * tile.erosionLevel end}
        },
         special_effects = {
             water_lapping_edge = {type="decal_animated", texture="water_edge_ripple", condition = function(tile) return tile.adjacentToWater end},
             mist_low = {type="particle_volume", density=0.3, when="has_mist"}
         },
         adjacent_water_visuals = true -- Visuals change based on adjacent water tile
    },
    
    -- Audio properties
    ambientSound = "river_flow_day", -- Sound of flowing water dominant
    footstepSound = "dirt_step", -- Default, changes with groundType variant
     specialSounds = {
        {name = "water_lap_gentle", trigger = "entity_near_edge", chance = 0.5},
        {name = "fish_jump_river", trigger = "random", chance = 0.05},
        {name = "otter_chitter", trigger = "random", chance = 0.03}
    },

    -- Connections to other tiles
    connects_to = {
        "riverbank", -- Itself (along the river)
        "water", -- Specifically a 'river' type water tile
        "shallow_water", -- Where river might be shallow near edge
        "mud", -- Can transition to muddy areas
        "sand", -- Sandy parts of the bank or nearby areas
        "grass", -- Common adjacent land type
        "dirt", -- Common adjacent land type
        "forest", -- Forest bordering the river
        "swamp", -- Slow-moving rivers might border swamps
        "bridge_support" -- Where bridges meet the land
    },

    -- Pathfinding properties
     pathfinding = {
        travel_cost = 1.1, -- Slightly higher cost than flat grass/dirt
        provides_water_access = true -- Important flag for AI/player needs
    },

    -- Effects on entities that walk on this tile
    onWalk = function(tile, entity)
        local sound = tile.footstepSound or "dirt_step"
        if tile.groundType == "mud" then sound = "mud_squelch"
        elseif tile.groundType == "sand" then sound = "sand_step"
        elseif tile.groundType == "grass" then sound = "grass_step"
        elseif tile.groundType == "rock" then sound = "stone_step_solid" end
        if tile.isFrozen then sound = "frozen_ground_crunch" end

        local fatigue = 0.08 * (1 + tile.bankSlope) -- Base fatigue slightly higher due to unevenness/slope
        local effects = {{type="increase_fatigue", amount = fatigue}}

        -- Check for slipping
        if tile.slipperiness and tile.slipperiness > 0 and math.random() < tile.slipperiness * 0.4 then
            table.insert(effects, {type="apply_status", status="slipping_minor", duration=1})
             -- Maybe play slip sound?
             return { sound="slip_scrape", volume=0.8, message="You slip on the "..(tile.isFrozen and "frozen" or tile.isWet and "wet" or "").. " riverbank!", effects=effects }
        end
        
        -- Apply wet if very close to water? (Maybe handled by adjacent tile interaction)

        return {
            sound = sound,
            volume = 0.9,
            effects = effects
        }
    end,
    
    -- Effect when discovering riverbank
    onDiscover = function(tile, entity)
        if entity.journal and entity.journal.addDiscovery then
             entity.journal.addDiscovery({
                type = "geography",
                name = "Riverbank", -- Could be named river
                location = {x = tile.x, y = tile.y},
                notes = "The edge of a flowing river. Provides water access."
            })
        end
        return {
            message = "You arrive at the bank of a river.",
            effects = {
                {type = "reveal_map", radius = 3} -- Reveal river and opposite bank potentially
            }
        }
    end
}

function RiverbankTile.init(world)
    print("Riverbank tile module initialized")
    -- Register with geography, navigation systems
     if world.systems and world.systems.geography then
        world.systems.geography:registerWaterEdge("riverbank", {adjacent_water="river"})
    end
end

return RiverbankTile