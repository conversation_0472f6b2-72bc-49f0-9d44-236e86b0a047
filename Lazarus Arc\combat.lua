-- improved_combat_v2.lua
-- Enhanced combat system for Lazarus Arc, integrating features from multiple modules.
-- Based on combat.txt, enhanced with elements from improved_combat.lua and other systems.

-- Dependencies (to be set via init or passed to functions)
local OverdriveLoader
local effectSystem
local soundSystem
local itemDatabase
local eventLog
local weatherSystem -- Added dependency
local skillsSystem -- Added dependency
local classesSystem -- Added dependency

-- Utility for deep copying
local function deepcopy(original)
    local copy
    if type(original) == "table" then
        copy = {}
        for k, v in pairs(original) do
            copy[k] = deepcopy(v)
        end
    else
        copy = original
    end
    return copy
end

local Combat = {}

-- Constants (Consolidated from both combat.txt and improved_combat.lua)
Combat.DAMAGE_TYPES = {
    PHYSICAL = "physical",
    MAGICAL = "magical",
    FIRE = "fire",
    ICE = "ice",
    LIGHTNING = "lightning",
    POISON = "poison",
    VOID = "void",
    HEALING = "healing" -- Added for clarity
}

Combat.STATUS_EFFECTS = {
    BURNING = "burning",
    FROZEN = "frozen",
    POISONED = "poisoned",
    STUNNED = "stunned",
    WEAKENED = "weakened",
    BLEEDING = "bleeding",
    STRENGTHENED = "strengthened",
    HASTED = "hasted",
    PROTECTED = "protected",
    CURSED = "cursed",
    BLESSED = "blessed",
    EVASIVE = "evasive", -- Example: Increases evasion
    ACCURATE = "accurate" -- Example: Increases accuracy
}

-- Status effect definitions (Example structure, assumes full definitions exist elsewhere or are loaded)
-- Example based on improved_combat.txt structure
Combat.StatusEffectData = {
    [Combat.STATUS_EFFECTS.BURNING] = { icon = "burning_icon", color = {1, 0.3, 0}, damagePerTick = 5, duration = 10, damageType = Combat.DAMAGE_TYPES.FIRE, visualEffect = "fire_particles" },
    [Combat.STATUS_EFFECTS.FROZEN] = { icon = "frozen_icon", color = {0.5, 0.8, 1}, speedModifier = 0.5, duration = 8, visualEffect = "ice_particles" },
    [Combat.STATUS_EFFECTS.POISONED] = { icon = "poison_icon", color = {0.2, 0.8, 0.1}, damagePerTick = 3, duration = 15, affectsStat = "pdefense", statModifier = -0.1, visualEffect = "poison_particles"},
    [Combat.STATUS_EFFECTS.STUNNED] = { icon = "stun_icon", color = {1, 1, 0}, duration = 3, preventsAction = true, visualEffect = "stun_effect" },
    [Combat.STATUS_EFFECTS.WEAKENED] = { icon = "weakened_icon", color = {0.5, 0.5, 0.5}, damageModifier = 0.75, duration = 12 },
    [Combat.STATUS_EFFECTS.BLEEDING] = { icon = "bleeding_icon", color = {0.8, 0, 0}, damagePerTick = 4, duration = 10, extraDamageOnMove = 2, visualEffect = "blood_particles" },
    -- Add other effects here...
}


-- Initialize the Combat system with necessary dependencies
function Combat.init(world)
    print("Initializing Combat System v2...")
    if not world then
        print("Combat Init Error: World object is required.")
        return nil
    end
    OverdriveLoader = world.OverdriveLoader or require("overdrive_loader") -- Use world reference or require
    effectSystem = world.effectSystem
    soundSystem = world.soundSystem
    itemDatabase = world.itemDatabase or require("item_database")
    eventLog = world.eventLog
    weatherSystem = world.weatherSystem -- Get weather system from world
    skillsSystem = world.skillsSystem or require("skills") -- Get skills system
    classesSystem = world.classesSystem or require("classes") -- Get classes system

    if not effectSystem then print("Combat Warning: effectSystem not found in world.") end
    if not soundSystem then print("Combat Warning: soundSystem not found in world.") end
    if not itemDatabase then print("Combat Warning: itemDatabase not found in world.") end
    if not eventLog then print("Combat Warning: eventLog not found in world.") end
    if not weatherSystem then print("Combat Warning: weatherSystem not found in world.") end
    if not skillsSystem then print("Combat Warning: skillsSystem not found in world.") end
    if not classesSystem then print("Combat Warning: classesSystem not found in world.") end

    Combat.initialized = true
    print("Combat System v2 Initialized.")
    return Combat
end

-- Main attack function (Refined version based on combat.txt)
-- attacker, target: character objects (as defined in character.txt)
-- options: table with attack details (e.g., skillId, weaponId, baseDamage, damageType)
function Combat.attack(attacker, target, options)
    options = options or {}
    local result = {
        attackerId = attacker.id,
        targetId = target.id,
        damage = 0,
        crit = false,
        miss = false,
        evaded = false,
        blocked = false,
        parried = false, -- Added parry
        countered = false, -- Added counter
        resisted = false,
        vulnerable = false,
        effectsApplied = {},
        effectsResisted = {},
        log = {} -- Combat log messages specific to this attack
    }

    -- 0. Check if attack is possible (stunned, etc.)
    if attacker.hasStatusEffect and attacker.hasStatusEffect(Combat.STATUS_EFFECTS.STUNNED) then
        table.insert(result.log, attacker.name .. " is stunned and cannot attack.")
        return result
    end
    if target.hasStatusEffect and target.hasStatusEffect("invulnerable") then -- Example status
         table.insert(result.log, target.name .. " is invulnerable.")
         return result
    end

    -- 1. Determine Base Damage and Type
    local baseDamage = options.baseDamage or attacker.stats.pattack -- Default to physical attack
    local damageType = options.damageType or Combat.DAMAGE_TYPES.PHYSICAL
    local accuracyBonus = 0
    local critChanceBonus = 0
    local critDamageBonus = 1.5 -- Default crit multiplier
    local statusEffectChanceBonus = 0
    local statusEffectsToApply = {} -- { effect = STATUS_EFFECT, chance = 0-1, duration = X, strength = Y }

    -- Consider weapon being used (requires Equipment system integration)
    local weaponId = options.weaponId or (attacker.equipment and attacker.equipment.getSlot("mainHand"))
    local weaponData
    if weaponId and itemDatabase then
        weaponData = itemDatabase.getItem(weaponId)
        if weaponData then
            baseDamage = weaponData.damage or baseDamage
            damageType = weaponData.damageType or damageType
            accuracyBonus = accuracyBonus + (weaponData.accuracyBonus or 0)
            critChanceBonus = critChanceBonus + (weaponData.critChanceBonus or 0)
            critDamageBonus = weaponData.critDamageMultiplier or critDamageBonus
            if weaponData.effects then -- Effects granted by weapon
                for effect, data in pairs(weaponData.effects) do
                    table.insert(statusEffectsToApply, {
                        effect = effect,
                        chance = data.chance or 0.1, -- Default 10% chance from weapon
                        duration = data.duration or 10,
                        strength = data.strength or 1
                    })
                end
            end
        end
    end

    -- Consider skill being used
    local skillId = options.skillId
    local skillData
    if skillId and skillsSystem then
        skillData = skillsSystem.load(skillId) -- Assumes skillsSystem can load skill data
        if skillData then
            baseDamage = skillData.damage or baseDamage
            damageType = skillData.damageType or damageType
            accuracyBonus = accuracyBonus + (skillData.accuracyBonus or 0)
            critChanceBonus = critChanceBonus + (skillData.critChanceBonus or 0)
            critDamageBonus = skillData.critDamageMultiplier or critDamageBonus
            -- Add effects from skill
             if skillData.effects then
                for effect, data in pairs(skillData.effects) do
                     table.insert(statusEffectsToApply, {
                        effect = effect,
                        chance = data.chance or 0.5, -- Default 50% chance from skill
                        duration = data.duration or skillData.duration or 10,
                        strength = data.strength or skillData.strength or 1
                    })
                end
            end
             -- Apply skill-specific modifiers if defined in skillData.execute() or similar
             -- e.g., modify baseDamage, damageType, add AoE targets etc.
             if skillData.onHit then
                 -- Custom logic for the skill's hit effect
                 -- This could modify the 'result' table directly or add more complex effects
                 -- skillData.onHit(attacker, target, result, baseDamage, damageType)
             end
        end
    end

    -- 2. Calculate Attacker's Effective Stats (Apply buffs/debuffs, class/skill passives)
    local attackerStats = deepcopy(attacker.stats) -- Work on a copy
    -- Apply temporary effects (strengthened, weakened, etc.)
    if attacker.activeStatusEffects then
        for effect, data in pairs(attacker.activeStatusEffects) do
             if effect == Combat.STATUS_EFFECTS.STRENGTHENED then attackerStats.pattack = attackerStats.pattack * (1 + (data.strength or 0.2)) end
             if effect == Combat.STATUS_EFFECTS.WEAKENED then attackerStats.pattack = attackerStats.pattack * (1 - (data.strength or 0.25)) end
             if effect == Combat.STATUS_EFFECTS.HASTED then attackerStats.speed = attackerStats.speed * (1 + (data.strength or 0.3)) end
             if effect == Combat.STATUS_EFFECTS.ACCURATE then accuracyBonus = accuracyBonus + (data.strength or 0.1) end
             -- Apply other stat modifications from effects
        end
    end
     -- Apply class bonuses (requires Classes system integration)
    if classesSystem and attacker.class then
        local classData = classesSystem.loadedClasses[attacker.class]
        if classData and classData.stat_bonuses then
            -- Apply bonuses based on level, e.g., classData.stat_bonuses.pattack_per_level * attacker.level
        end
        if classData.passive_effects then
           -- Apply passive effects, e.g., increase crit chance for Archer
           if attacker.class == "archer" then critChanceBonus = critChanceBonus + 0.05 end -- Example
        end
    end
     -- Apply skill passives (requires Skills system integration)
    if skillsSystem and attacker.skills then
         for learnedSkillId, skillInfo in pairs(attacker.skills) do
            local learnedSkillData = skillsSystem.load(learnedSkillId)
            if learnedSkillData and learnedSkillData.passive_effect then
                -- learnedSkillData.passive_effect(attackerStats) -- Modify stats directly
            end
         end
    end


    -- 3. Calculate Target's Effective Stats (Apply buffs/debuffs)
    local targetStats = deepcopy(target.stats)
    local evasionBonus = 0
    local blockChanceBonus = 0
    local parryChanceBonus = 0
     -- Apply temporary effects
    if target.activeStatusEffects then
        for effect, data in pairs(target.activeStatusEffects) do
             if effect == Combat.STATUS_EFFECTS.PROTECTED then
                targetStats.pdefense = targetStats.pdefense * (1 + (data.strength or 0.25))
                targetStats.mdefense = targetStats.mdefense * (1 + (data.strength or 0.25))
             end
             if effect == Combat.STATUS_EFFECTS.FROZEN then targetStats.speed = targetStats.speed * (1 - (data.strength or 0.5)) end
             if effect == Combat.STATUS_EFFECTS.EVASIVE then evasionBonus = evasionBonus + (data.strength or 0.15) end
             -- Apply other stat modifications from effects
        end
    end
     -- Apply class bonuses (similar to attacker)
     -- Apply skill passives (similar to attacker)

    -- 4. Accuracy Check vs Evasion Check (Incorporate Speed, Luck, Weather)
    local baseAccuracy = 0.95 -- Base chance to hit
    local accuracy = baseAccuracy + accuracyBonus + (attackerStats.luck * 0.005) - (targetStats.luck * 0.002) + ((attackerStats.speed - targetStats.speed) * 0.01)

    -- Apply weather effects (Example: Fog reduces accuracy)
    local weatherStatus
    if weatherSystem then
        weatherStatus = weatherSystem.getCurrentStatus()
        if weatherStatus and weatherStatus.name == "foggy" then
            accuracy = accuracy * 0.8 -- 20% accuracy reduction in fog
            table.insert(result.log, "Fog reduces accuracy!")
        end
         if weatherStatus and weatherStatus.environment and weatherStatus.environment.visibility_modifier then
             accuracy = accuracy * weatherStatus.environment.visibility_modifier
         end
    end

    accuracy = math.max(0.1, math.min(1.0, accuracy)) -- Clamp accuracy between 10% and 100%

    local evasionChance = (targetStats.speed * 0.02) + (targetStats.luck * 0.01) + evasionBonus
    evasionChance = math.max(0, math.min(0.8, evasionChance)) -- Clamp evasion between 0% and 80%

    local hitRoll = math.random()
    if hitRoll > accuracy then
        result.miss = true
        table.insert(result.log, attacker.name .. " missed " .. target.name .. ".")
        -- Play miss sound/effect
        if effectSystem then effectSystem.createMissEffect(target.position.x, target.position.y) end
        if soundSystem then soundSystem.playSound("miss") end
        return result -- Attack ends on a miss
    end

    local evadeRoll = math.random()
    if evadeRoll < evasionChance then
        result.evaded = true
        table.insert(result.log, target.name .. " evaded the attack!")
        -- Play evade sound/effect
        if effectSystem then effectSystem.createEvasionEffect(target.position.x, target.position.y) end
        if soundSystem then soundSystem.playSound("dodge") end -- Use 'dodge' sound
        target.gainExperience(1) -- XP for evading
        -- Check for counter attack possibility on evade
        -- local counterChance = (targetStats.luck * 0.01) ...
        -- if math.random() < counterChance then result.countered = true ... end
        return result -- Attack ends on evasion
    end

    -- 5. Parry/Block Check (Incorporate Equipment, Skills)
    local blockChance = (targetStats.pdefense * 0.01) + blockChanceBonus -- Base block on defense
    local parryChance = (targetStats.speed * 0.005) + (targetStats.luck * 0.005) + parryChanceBonus -- Base parry on speed/luck

    -- Check if target has a shield equipped
    local shieldId = target.equipment and target.equipment.getSlot("offHand")
    local shieldData
    if shieldId and itemDatabase then
        shieldData = itemDatabase.getItem(shieldId)
        if shieldData and shieldData.itemType == "shield" then
            blockChance = blockChance + (shieldData.blockChance or 0.15) -- Add shield's block chance
            parryChance = parryChance + (shieldData.parryChance or 0.05) -- Some shields might allow parrying
        end
    end

    -- Modify based on skills (e.g., 'Shield Wall' skill increases block chance)

    blockChance = math.max(0, math.min(0.75, blockChance)) -- Clamp block chance
    parryChance = math.max(0, math.min(0.50, parryChance)) -- Clamp parry chance

    local defenseRoll = math.random()
    if defenseRoll < parryChance then
        result.parried = true
        table.insert(result.log, target.name .. " parried the attack!")
        if effectSystem then effectSystem.createParryEffect(target.position.x, target.position.y) end
        if soundSystem then soundSystem.playSound("parry") end -- Needs specific parry sound
        target.gainExperience(2) -- XP for parrying
        -- Check for counter attack on parry
        -- local counterChance = (targetStats.pattack * 0.01) ...
        -- if math.random() < counterChance then result.countered = true ... end
        return result -- Attack ends on parry
    elseif defenseRoll < (parryChance + blockChance) then
        result.blocked = true
        local blockReduction = 0.5 + (targetStats.pdefense * 0.01) -- Base 50% reduction + defense bonus
        if shieldData and shieldData.blockAmount then
             blockReduction = math.min(0.9, blockReduction + shieldData.blockAmount) -- Add shield amount, cap at 90%
        end
        baseDamage = baseDamage * (1 - blockReduction)
        table.insert(result.log, target.name .. " blocked the attack, reducing damage by " .. string.format("%.0f%%", blockReduction * 100) .. ".")
        if effectSystem then effectSystem.createBlockEffect(target.position.x, target.position.y) end
        if soundSystem then soundSystem.playSound("block") end
        target.gainExperience(1) -- XP for blocking
        -- Status effects generally don't apply on block, but could be a feature
        statusEffectsToApply = {} -- Clear effects on block
    end


    -- 6. Critical Hit Check
    local critChance = (attackerStats.luck * 0.01) + critChanceBonus
    -- Modify based on skills (e.g., Archer's 'Precise Shot')
    critChance = math.max(0.01, math.min(0.5, critChance)) -- Clamp crit chance
    if math.random() < critChance then
        result.crit = true
        baseDamage = baseDamage * critDamageBonus
        table.insert(result.log, "Critical Hit!")
        if soundSystem then soundSystem.playSound("critical_hit") end
        -- Could add critical hit visual effect
    end

    -- 7. Damage Calculation (Resistance/Vulnerability)
    local defenseStat = (damageType == Combat.DAMAGE_TYPES.PHYSICAL) and targetStats.pdefense or targetStats.mdefense
    local resistance = target.getResistance and target:getResistance(damageType) or 0 -- Assumes a getResistance function on character
    local vulnerability = target.getVulnerability and target:getVulnerability(damageType) or 0 -- Assumes a getVulnerability function

    -- Apply resistance/vulnerability multipliers
    local damageMultiplier = (1 - resistance) * (1 + vulnerability)

    if resistance > 0 then result.resisted = true end
    if vulnerability > 0 then result.vulnerable = true end

    -- Final damage formula (example)
    local calculatedDamage = baseDamage * damageMultiplier - defenseStat * (1 - resistance) -- Defense is less effective vs vulnerable types
    calculatedDamage = math.max(0, calculatedDamage) -- Damage cannot be negative

    -- Apply weather effects on damage (Example: Rain weakens Fire, boosts Lightning)
    if weatherStatus then
        if weatherStatus.name == "rainy" then
            if damageType == Combat.DAMAGE_TYPES.FIRE then
                 calculatedDamage = calculatedDamage * 0.75
                 table.insert(result.log, "Rain dampens fire damage.")
            elseif damageType == Combat.DAMAGE_TYPES.LIGHTNING then
                 calculatedDamage = calculatedDamage * 1.15
                 table.insert(result.log, "Rain enhances lightning damage!")
            end
        end
        -- Check for environment damage modifiers
         if weatherStatus.environment and weatherStatus.environment.damage_modifiers then
             local modifier = weatherStatus.environment.damage_modifiers[damageType] or 1
             calculatedDamage = calculatedDamage * modifier
         end
    end


    -- Round damage
    result.damage = math.floor(calculatedDamage + 0.5)


    -- 8. Apply Damage to Target
    if target.takeDamage then
        target:takeDamage(result.damage, damageType, attacker.id) -- Assumes takeDamage function exists
    else
        -- Fallback if no specific function
        target.stats.health = target.stats.health - result.damage
    end
    table.insert(result.log, attacker.name .. " hits " .. target.name .. " for " .. result.damage .. " " .. damageType .. " damage.")

    -- Play hit sound
    if not result.blocked and not result.parried and result.damage > 0 and soundSystem then
        soundSystem.playSound("hit") -- Generic hit sound
        -- Could play type-specific sounds: soundSystem.playSound("hit_"..damageType)
    elseif result.damage <= 0 and not result.blocked and not result.parried and soundSystem then
         soundSystem.playSound("hit_weak") -- Sound for no damage
    end
    -- Create damage number effect
    if effectSystem then
        effectSystem.createDamageNumber(target.position.x, target.position.y, result.damage, result.crit, damageType)
    end


    -- 9. Status Effect Application
    if not result.blocked and not result.parried then -- Don't apply effects if blocked/parried
        for _, effectData in ipairs(statusEffectsToApply) do
            local applyChance = effectData.chance + statusEffectChanceBonus + (attackerStats.luck * 0.002) - (targetStats.luck * 0.001)
            local resistChance = target.getStatusResistance and target:getStatusResistance(effectData.effect) or 0 -- Assumes function exists

            applyChance = applyChance * (1 - resistChance)
            applyChance = math.max(0, math.min(1, applyChance))

            if math.random() < applyChance then
                if target.addStatusEffect then -- Assumes function exists
                    local success, message = target:addStatusEffect(effectData.effect, effectData.duration, effectData.strength)
                    if success then
                        table.insert(result.effectsApplied, effectData.effect)
                        table.insert(result.log, target.name .. " is now " .. effectData.effect .. "!")
                        -- Create status effect visual on target
                        if effectSystem and Combat.StatusEffectData[effectData.effect] and Combat.StatusEffectData[effectData.effect].visualEffect then
                             effectSystem.createStatusEffect(target.position.x, target.position.y, Combat.StatusEffectData[effectData.effect].visualEffect)
                        end
                    else
                         table.insert(result.effectsResisted, effectData.effect)
                         table.insert(result.log, target.name .. " resisted " .. effectData.effect .. ". (" .. message .. ")")
                    end
                end
            else
                 table.insert(result.log, target.name .. " avoided the " .. effectData.effect .. " effect.")
            end
        end
    end

    -- 10. Grant Experience
    if attacker.gainExperience then
        local xpGained = math.floor(result.damage * 0.5 + 5) -- Base XP on damage dealt
        if result.crit then xpGained = xpGained * 1.5 end
        if target.stats.health <= 0 then xpGained = xpGained + (target.level * 10) end -- Bonus for kill
        attacker:gainExperience(xpGained)
        table.insert(result.log, attacker.name .. " gained " .. xpGained .. " XP.")
    end

    -- 11. Update Overdrive Meter (if applicable)
    if attacker.updateOverdrive then
        local meterGain = result.damage * 0.1 -- Gain based on damage
        if result.crit then meterGain = meterGain * 2 end
        attacker:updateOverdrive(meterGain)
    end
    if target.updateOverdrive then
         local meterGain = result.damage * 0.05 -- Target gains less
         target:updateOverdrive(meterGain)
    end

    -- 12. Log Event
    if eventLog then
        eventLog.addEntry({
            type = "combat_attack",
            attacker = attacker.id,
            target = target.id,
            result = result -- Store the detailed result
        })
    end

    -- 13. Check for Target Death
    if target.stats.health <= 0 then
         table.insert(result.log, target.name .. " has been defeated!")
         if target.onDeath then
             target:onDeath(attacker.id) -- Call death handler
         end
         -- Trigger death effects/sounds
         if soundSystem then soundSystem.playSound("defeat_" .. (target.type or "default")) end -- Type-specific defeat sound
    end

    return result
end


-- Wrapper function to easily perform an attack from game logic
function Combat.performAttack(world, attackerId, targetId, options)
    if not Combat.initialized then
        print("Combat Error: System not initialized. Call Combat.init(world) first.")
        return nil
    end

    local entityManager = world.entityManager -- Assuming world has an entity manager
    if not entityManager then
        print("Combat Error: entityManager not found in world.")
        return nil
    end

    local attacker = entityManager.getEntity(attackerId)
    local target = entityManager.getEntity(targetId)

    if not attacker then
        print("Combat Error: Attacker ("..attackerId..") not found.")
        return nil
    end
    if not target then
        print("Combat Error: Target ("..targetId..") not found.")
        return nil
    end

    -- Ensure dependencies are current (optional, could rely on init)
    -- effectSystem = world.effectSystem
    -- soundSystem = world.soundSystem
    -- ... etc.

    local result = Combat.attack(attacker, target, options)

    -- Print combat log for debugging
    if result and result.log then
        print("--- Combat Log ---")
        for _, msg in ipairs(result.log) do
            print(msg)
        end
        print("------------------")
    end

    return result
end


return Combat