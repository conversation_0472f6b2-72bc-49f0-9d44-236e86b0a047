-- weather/meteoroid.lua
-- Meteoroid weather pattern - meteor showers with impact effects

local MeteoroidWeather = {
    id = "meteoroid",
    name = "Meteoroid Shower",
    
    -- Visual properties
    visual = {
        skyColor = {r = 40, g = 40, b = 60}, -- Dark blue-black sky
        sunIntensity = 0.1,
        cloudCoverage = 0.2,
        cloudColor = {r = 60, g = 60, b = 80}, -- Dark gray clouds
        ambientLightLevel = 0.2,
        meteorColor = {r = 255, g = 200, b = 100} -- Orange-yellow meteors
    },
    
    -- Environment modifiers
    environment = {
        temperature = 0.8, -- Cooler temperature
        visibility = 0.6, -- Moderate visibility
        humidity = 0.3, -- Low humidity
        windStrength = 0.1, -- Very light wind
        meteorIntensity = 0.7, -- High meteor activity
        dustLevel = 0.5, -- Moderate dust
        impactRisk = 0.6 -- High impact risk
    },
    
    -- Particle systems
    particles = {
        meteor = {
            enabled = true,
            intensity = 0.7,
            color = {r = 255, g = 200, b = 100},
            size = {min = 2, max = 4},
            speed = {min = 300, max = 500},
            image = "assets/particles/Complex/flare/flare_1.png",
            blendMode = "add",
            lifetime = {min = 0.5, max = 1.0},
            rotation = {min = 0, max = 360},
            alpha = {min = 0.6, max = 0.9}
        },
        debris = {
            enabled = true,
            intensity = 0.5,
            color = {r = 200, g = 150, b = 50},
            size = {min = 1, max = 3},
            speed = {min = 100, max = 200},
            image = "assets/particles/Complex/impacts/impact_1.png",
            blendMode = "add",
            lifetime = {min = 0.3, max = 0.7},
            rotation = {min = 0, max = 360},
            alpha = {min = 0.4, max = 0.7}
        },
        dust = {
            enabled = true,
            intensity = 0.3,
            color = {r = 150, g = 100, b = 50},
            size = {min = 1, max = 2},
            speed = {min = 10, max = 30},
            image = "assets/particles/Complex/smoke/smoke_1.png",
            blendMode = "alpha",
            lifetime = {min = 1.0, max = 2.0},
            rotation = {min = 0, max = 360},
            alpha = {min = 0.2, max = 0.4}
        }
    },
    
    -- Sound effects
    sounds = {
        ambient = "meteoroid_ambient",
        impact = "meteoroid_impact",
        whoosh = "meteoroid_whoosh",
        volume = 0.4
    },
    
    -- Effects on game entities and tiles
    effects = {
        -- Movement effects
        movementSpeedMultiplier = 0.9, -- Slightly reduced speed
        
        -- Vision effects
        visionRange = 0.6, -- Reduced vision
        
        -- Temperature effects
        temperatureModifier = 0.8, -- Cooler temperature
        
        -- Energy effects
        energyDrainRate = 1.1, -- Slightly increased energy drain
        
        -- Shield effects
        shieldEffectiveness = 0.8, -- Reduced shield effectiveness
        
        -- Sensor effects
        sensorRange = 0.7, -- Reduced sensor range
        
        -- Special effects
        impactDamage = 0.2, -- High impact damage
        heatDamage = 0.1 -- Moderate heat damage
    },
    
    -- Transition probabilities to other weather (per game hour)
    transitions = {
        clear = 0.3,
        cloudy = 0.2,
        em_storm = 0.1,
        -- Implied: 0.4 chance to stay meteoroid
    },
    
    -- Day/night cycle modifiers
    timeModifiers = {
        dawn = {
            skyColor = {r = 50, g = 50, b = 70},
            ambientLightLevel = 0.15,
            meteorIntensity = 0.8 -- Higher meteor activity at dawn
        },
        day = {
            -- Uses default properties
        },
        dusk = {
            skyColor = {r = 30, g = 30, b = 50},
            ambientLightLevel = 0.1,
            meteorIntensity = 0.9 -- Higher meteor activity at dusk
        },
        night = {
            skyColor = {r = 20, g = 20, b = 40},
            ambientLightLevel = 0.05,
            meteorIntensity = 1.0 -- Highest meteor activity at night
        }
    }
}

-- Helper function to safely set particle system properties
local function setupParticleSystem(ps, config)
    if not ps or type(ps) ~= "table" then
        print("Warning: Invalid particle system")
        return false
    end
    
    local methods = {
        setParticleLifetime = function() return ps:setParticleLifetime(config.lifetime.min, config.lifetime.max) end,
        setEmissionRate = function() return ps:setEmissionRate(config.emissionRate or 0) end,
        setSizeVariation = function() return ps:setSizeVariation(config.sizeVariation or 0.3) end,
        setLinearAcceleration = function() return ps:setLinearAcceleration(config.acceleration.x1 or 0, config.acceleration.y1 or 0, config.acceleration.x2 or 0, config.acceleration.y2 or 0) end,
        setColors = function() return ps:setColors(config.colors.r1 or 255, config.colors.g1 or 255, config.colors.b1 or 255, config.colors.a1 or 255, config.colors.r2 or 255, config.colors.g2 or 255, config.colors.b2 or 255, config.colors.a2 or 0) end,
        setBlendMode = function() return ps:setBlendMode(config.blendMode or "normal") end
    }
    
    for methodName, method in pairs(methods) do
        if type(ps[methodName]) ~= "function" then
            print("Warning: Particle system missing method: " .. methodName)
            return false
        end
        
        local success, err = pcall(method)
        if not success then
            print("Warning: Failed to call " .. methodName .. ": " .. tostring(err))
            return false
        end
    end
    
    return true
end

-- Initialize the weather pattern
function MeteoroidWeather.init(world)
    if not world then
        print("Warning: world parameter is nil in MeteoroidWeather.init")
        return
    end
    
    print("Initializing meteoroid weather")
    
    -- Set global light level
    world.lightLevel = MeteoroidWeather.visual.ambientLightLevel
    
    -- Start ambient sounds if weather system exists
    if world.weatherSystem then
        if MeteoroidWeather.sounds.ambient then
            world.weatherSystem:playSound(MeteoroidWeather.sounds.ambient, {
                verbose = false,  -- Don't print warnings if sound is missing
                loop = true,      -- Ambient sounds should loop
                volume = 0.5      -- Lower volume for ambient weather
            })
        end
    end
    
    -- Create particle systems
    if MeteoroidWeather.particles.meteor.enabled then
        -- Create meteor particles
        print("Creating meteor particles with intensity: " .. MeteoroidWeather.particles.meteor.intensity)
        local success, image = pcall(love.graphics.newImage, MeteoroidWeather.particles.meteor.image)
        if success and image then
            local success2, ps = pcall(love.graphics.newParticleSystem, image)
            if success2 and ps then
                local config = {
                    lifetime = MeteoroidWeather.particles.meteor.lifetime,
                    emissionRate = 5,
                    sizeVariation = 0.3,
                    acceleration = {x1 = 0, y1 = 100, x2 = 0, y2 = 100},
                    colors = {r1 = 255, g1 = 255, b1 = 255, a1 = 255, r2 = 255, g2 = 200, b2 = 100, a2 = 0},
                    blendMode = "add"
                }
                
                if setupParticleSystem(ps, config) then
                    MeteoroidWeather.meteorSystem = ps
                else
                    print("Warning: Failed to setup meteor particle system")
                end
            else
                print("Warning: Failed to create meteor particle system")
            end
        else
            print("Warning: Could not load meteor particle image: " .. MeteoroidWeather.particles.meteor.image)
        end
    end
    
    if MeteoroidWeather.particles.debris.enabled then
        -- Create debris particles
        print("Creating debris particles with intensity: " .. MeteoroidWeather.particles.debris.intensity)
        local success, image = pcall(love.graphics.newImage, MeteoroidWeather.particles.debris.image)
        if success and image then
            local success2, ps = pcall(love.graphics.newParticleSystem, image)
            if success2 and ps then
                local config = {
                    lifetime = MeteoroidWeather.particles.debris.lifetime,
                    emissionRate = 0, -- Only emit on impact
                    sizeVariation = 0.2,
                    acceleration = {x1 = -50, y1 = -50, x2 = 50, y2 = 50},
                    colors = {r1 = 255, g1 = 255, b1 = 255, a1 = 255, r2 = 200, g2 = 150, b2 = 50, a2 = 0},
                    blendMode = "add"
                }
                
                if setupParticleSystem(ps, config) then
                    MeteoroidWeather.debrisSystem = ps
                else
                    print("Warning: Failed to setup debris particle system")
                end
            else
                print("Warning: Failed to create debris particle system")
            end
        else
            print("Warning: Could not load debris particle image: " .. MeteoroidWeather.particles.debris.image)
        end
    end
    
    if MeteoroidWeather.particles.dust.enabled then
        -- Create dust particles
        print("Creating dust particles with intensity: " .. MeteoroidWeather.particles.dust.intensity)
        local success, image = pcall(love.graphics.newImage, MeteoroidWeather.particles.dust.image)
        if success and image then
            local success2, ps = pcall(love.graphics.newParticleSystem, image)
            if success2 and ps then
                local config = {
                    lifetime = MeteoroidWeather.particles.dust.lifetime,
                    emissionRate = 10,
                    sizeVariation = 0.3,
                    acceleration = {x1 = -5, y1 = -5, x2 = 5, y2 = 5},
                    colors = {r1 = 255, g1 = 255, b1 = 255, a1 = 255, r2 = 150, g2 = 100, b2 = 50, a2 = 0},
                    blendMode = "alpha"
                }
                
                if setupParticleSystem(ps, config) then
                    MeteoroidWeather.dustSystem = ps
                else
                    print("Warning: Failed to setup dust particle system")
                end
            else
                print("Warning: Failed to create dust particle system")
            end
        else
            print("Warning: Could not load dust particle image: " .. MeteoroidWeather.particles.dust.image)
        end
    end
    
    -- Display warning message to players if message system exists
    if world.messageSystem then
        world.messageSystem:broadcast("WARNING: Meteoroid shower detected. High impact risk.")
    else
        print("WARNING: Meteoroid shower detected. High impact risk.")
    end
end

-- Update function called every frame
function MeteoroidWeather.update(world, dt)
    if not world then
        print("Warning: world parameter is nil in MeteoroidWeather.update")
        return
    end
    
    -- Update day/night cycle based on world time
    local timeOfDay = world.timeOfDay or "day"
    local modifiers = MeteoroidWeather.timeModifiers[timeOfDay]
    
    if modifiers then
        -- Apply time-specific modifiers
        if modifiers.skyColor then
            -- Change sky color
        end
        
        if modifiers.ambientLightLevel then
            world.lightLevel = modifiers.ambientLightLevel
        end
        
        if modifiers.meteorIntensity then
            MeteoroidWeather.particles.meteor.intensity = modifiers.meteorIntensity
            if MeteoroidWeather.meteorSystem then
                MeteoroidWeather.meteorSystem:setEmissionRate(5 * modifiers.meteorIntensity)
            end
        end
    end
    
    -- Update particle systems
    if MeteoroidWeather.meteorSystem then
        MeteoroidWeather.meteorSystem:update(dt)
    end
    if MeteoroidWeather.debrisSystem then
        MeteoroidWeather.debrisSystem:update(dt)
    end
    if MeteoroidWeather.dustSystem then
        MeteoroidWeather.dustSystem:update(dt)
    end
    
    -- Apply effects to game world if entity system exists
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            -- Apply movement speed reduction
            if entity.speed then
                entity.speed = entity.speed * MeteoroidWeather.effects.movementSpeedMultiplier
            end
            
            -- Apply energy drain
            if entity.energy then
                entity.energy = entity.energy - 
                    (MeteoroidWeather.effects.energyDrainRate * dt)
            end
            
            -- Random impact damage
            if math.random() < 0.01 then
                if entity.health then
                    entity.health = entity.health - 
                        (MeteoroidWeather.effects.impactDamage * dt)
                end
                -- Trigger debris particle effect
                if MeteoroidWeather.debrisSystem then
                    MeteoroidWeather.debrisSystem:emit(20)
                end
                -- Play impact sound
                if world.weatherSystem then
                    world.weatherSystem:playSound(MeteoroidWeather.sounds.impact, {
                        verbose = false,  -- Don't print warnings if sound is missing
                        volume = 0.8,     -- Higher volume for impact sounds
                        pitch = 1.0 + math.random() * 0.2 - 0.1  -- Random pitch variation
                    })
                end
            end
            
            -- Apply heat damage
            if entity.health then
                entity.health = entity.health - 
                    (MeteoroidWeather.effects.heatDamage * dt)
            end
        end
    end
    
    -- Apply weather effects if weather system exists
    if world.weatherSystem then
        world.weatherSystem:applyEffects(MeteoroidWeather.environment)
    end
end

-- Clean up when weather changes
function MeteoroidWeather.cleanUp(world)
    if not world then
        print("Warning: world parameter is nil in MeteoroidWeather.cleanUp")
        return
    end
    
    -- Stop sounds if weather system exists
    if world.weatherSystem then
        if MeteoroidWeather.sounds.ambient then
            world.weatherSystem:stopSound(MeteoroidWeather.sounds.ambient)
        end
    end
    
    -- Clean up particle systems
    if MeteoroidWeather.meteorSystem then
        MeteoroidWeather.meteorSystem:stop()
    end
    if MeteoroidWeather.debrisSystem then
        MeteoroidWeather.debrisSystem:stop()
    end
    if MeteoroidWeather.dustSystem then
        MeteoroidWeather.dustSystem:stop()
    end
    
    -- Notify players if message system exists
    if world.messageSystem then
        world.messageSystem:broadcast("Meteoroid shower ending. Impact risk decreasing.")
    else
        print("Meteoroid shower ending. Impact risk decreasing.")
    end
    
    print("Meteoroid weather ending")
end

-- Table contains helper function
function table.contains(table, element)
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

return MeteoroidWeather 