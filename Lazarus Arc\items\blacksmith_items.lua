-- items/blacksmith_items.lua
-- Blacksmith-related items for crafting and metalworking

local BlacksmithItems = {
    -- Common Items
    iron_ingot = {
        id = "iron_ingot",
        name = "Iron Ingot",
        category = "material",
        description = "A basic metal ingot used for crafting",
        value = 10,
        weight = 2.0,
        stackable = true,
        maxStack = 50,
        properties = {
            material = "iron",
            quality = 1.0,
            durability = 100
        },
        onUse = function(user, target)
            -- Can be used for crafting or sold
            return true
        end
    },
    
    steel_ingot = {
        id = "steel_ingot",
        name = "Steel Ingot",
        category = "material",
        description = "A refined metal ingot used for crafting",
        value = 20,
        weight = 2.0,
        stackable = true,
        maxStack = 50,
        properties = {
            material = "steel",
            quality = 1.2,
            durability = 120
        },
        onUse = function(user, target)
            -- Can be used for crafting or sold
            return true
        end
    },
    
    hammer = {
        id = "hammer",
        name = "Blacksmith's Hammer",
        category = "tool",
        description = "A basic hammer used for metalworking",
        value = 15,
        weight = 1.5,
        stackable = false,
        properties = {
            material = "iron",
            quality = 1.0,
            durability = 100,
            damage = 5
        },
        onUse = function(user, target)
            -- Can be used for crafting or as a weapon
            return true
        end
    },
    
    tongs = {
        id = "tongs",
        name = "Blacksmith's Tongs",
        category = "tool",
        description = "Basic tongs used for handling hot metal",
        value = 10,
        weight = 1.0,
        stackable = false,
        properties = {
            material = "iron",
            quality = 1.0,
            durability = 100
        },
        onUse = function(user, target)
            -- Can be used for crafting
            return true
        end
    },
    
    -- Uncommon Items
    mithril_ingot = {
        id = "mithril_ingot",
        name = "Mithril Ingot",
        category = "material",
        description = "A rare and valuable metal ingot",
        value = 100,
        weight = 1.0,
        stackable = true,
        maxStack = 20,
        properties = {
            material = "mithril",
            quality = 2.0,
            durability = 200,
            magical = true
        },
        onUse = function(user, target)
            -- Can be used for crafting or sold
            return true
        end
    },
    
    master_hammer = {
        id = "master_hammer",
        name = "Master Blacksmith's Hammer",
        category = "tool",
        description = "A finely crafted hammer for expert metalworking",
        value = 50,
        weight = 1.5,
        stackable = false,
        properties = {
            material = "steel",
            quality = 1.5,
            durability = 150,
            damage = 8
        },
        onUse = function(user, target)
            -- Can be used for crafting or as a weapon
            return true
        end
    },
    
    enchanted_tongs = {
        id = "enchanted_tongs",
        name = "Enchanted Tongs",
        category = "tool",
        description = "Magically enhanced tongs for handling hot metal",
        value = 40,
        weight = 1.0,
        stackable = false,
        properties = {
            material = "steel",
            quality = 1.5,
            durability = 150,
            magical = true
        },
        onUse = function(user, target)
            -- Can be used for crafting
            return true
        end
    },
    
    forge_manual = {
        id = "forge_manual",
        name = "Blacksmith's Manual",
        category = "book",
        description = "A guide to basic blacksmithing techniques",
        value = 30,
        weight = 1.0,
        stackable = false,
        properties = {
            material = "paper",
            quality = 1.0,
            durability = 50,
            knowledge = "basic_blacksmithing"
        },
        onUse = function(user, target)
            -- Can be used to learn blacksmithing skills
            return true
        end
    },
    
    -- Rare Items
    adamantine_ingot = {
        id = "adamantine_ingot",
        name = "Adamantine Ingot",
        category = "material",
        description = "A legendary metal ingot of immense power",
        value = 500,
        weight = 2.0,
        stackable = true,
        maxStack = 10,
        properties = {
            material = "adamantine",
            quality = 3.0,
            durability = 300,
            magical = true
        },
        onUse = function(user, target)
            -- Can be used for crafting or sold
            return true
        end
    },
    
    legendary_hammer = {
        id = "legendary_hammer",
        name = "Legendary Blacksmith's Hammer",
        category = "tool",
        description = "A legendary hammer with immense power",
        value = 200,
        weight = 2.0,
        stackable = false,
        properties = {
            material = "adamantine",
            quality = 2.5,
            durability = 250,
            damage = 15,
            magical = true
        },
        onUse = function(user, target)
            -- Can be used for crafting or as a powerful weapon
            return true
        end
    },
    
    ancient_tongs = {
        id = "ancient_tongs",
        name = "Ancient Blacksmith's Tongs",
        category = "tool",
        description = "Ancient tongs with mysterious powers",
        value = 150,
        weight = 1.0,
        stackable = false,
        properties = {
            material = "ancient_metal",
            quality = 2.5,
            durability = 250,
            magical = true
        },
        onUse = function(user, target)
            -- Can be used for crafting
            return true
        end
    },
    
    master_forge_manual = {
        id = "master_forge_manual",
        name = "Master Blacksmith's Manual",
        category = "book",
        description = "A comprehensive guide to advanced blacksmithing",
        value = 100,
        weight = 1.0,
        stackable = false,
        properties = {
            material = "ancient_paper",
            quality = 2.0,
            durability = 100,
            knowledge = "advanced_blacksmithing"
        },
        onUse = function(user, target)
            -- Can be used to learn advanced blacksmithing skills
            return true
        end
    }
}

return BlacksmithItems 