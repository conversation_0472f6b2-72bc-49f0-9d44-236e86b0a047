-- tiles/tile_definitions.lua
-- Loads and manages definitions for different tile types.

local TileDefinitions = {
    definitions = {} -- Registry for loaded tile definitions
}

-- Load all tile definitions from the 'tiles' directory
function TileDefinitions:loadAll()
    print("Loading tile definitions...")
    local dir = "tiles/"
    local files = love.filesystem.getDirectoryItems(dir)
    local loadedCount = 0

    for _, file in ipairs(files) do
        local fullPath = dir .. file
        -- Ensure it's a lua file and not this file itself or a directory
        local info = love.filesystem.getInfo(fullPath)
        if info and info.type == "file" and file:match("%.lua$") and file ~= "tile_definitions.lua" then
            local tileName = file:gsub("%.lua$", "") -- Use filename as tile type name
            -- Use require with the relative path from the project root
            local requirePath = "tiles." .. tileName
            -- print("  Attempting to load:", requirePath) -- Debug print
            local success, definition = pcall(require, requirePath)
            if success and type(definition) == "table" then
                print("  Loaded tile:", tileName)
                self.definitions[tileName] = definition
                loadedCount = loadedCount + 1
                -- Set default draw function if not provided
                if not definition.draw then
                    definition.draw = function(renderer, x, y, w, h, tileData)
                        -- Default drawing: simple colored rectangle/polygon
                        local color = definition.color or {0.5, 0.5, 0.5} -- Default gray
                        love.graphics.setColor(color)
                        if renderer.colorUtils and renderer.colorUtils.isometricDebug then
                             -- Basic isometric diamond shape using world coordinates (x, y are tile coords)
                             local isoX, isoY = renderer.colorUtils.toIsometric(x + 0.5, y + 0.5) -- Center of tile in world coords
                             local halfIsoW = w / 2 -- Visual width in iso projection
                             local halfIsoH = h / 4 -- Visual height in iso projection
                             love.graphics.polygon("fill",
                                 isoX, isoY - halfIsoH,        -- Top
                                 isoX + halfIsoW, isoY,        -- Right
                                 isoX, isoY + halfIsoH,        -- Bottom
                                 isoX - halfIsoW, isoY         -- Left
                             )
                        else
                            -- Top-down: x, y are world tile coordinates, w, h are pixel dimensions
                            love.graphics.rectangle("fill", x * w, y * h, w - 1, h - 1)
                        end
                    end
                end
            else
                print("  Warning: Failed to load or invalid definition for tile:", tileName, "Error:", definition) -- Print error on failure
            end
        end
    end
    print("Tile definitions loaded:", loadedCount)
    -- Add a default definition for safety
    if not self.definitions["default"] then
        self.definitions["default"] = { color = {1,0,1}, draw = function(renderer, x, y, w, h, tileData) love.graphics.setColor(1,0,1); love.graphics.rectangle("fill", x*w, y*h, w, h) end }
        print("  Added fallback default tile definition.")
    end
    return self
end

-- Get the definition for a specific tile type
function TileDefinitions:get(tileType)
    return self.definitions[tileType] or self.definitions["default"] -- Return default if type not found
end

-- Call the draw function for a specific tile type
-- x, y are world tile coordinates
-- w, h are tile pixel dimensions
function TileDefinitions:drawTile(renderer, tileType, x, y, w, h, tileData)
    local definition = self:get(tileType) -- Ensures we always get a valid definition (or default)
    if definition and definition.draw then
        definition.draw(renderer, x, y, w, h, tileData or {})
    else
        -- This fallback should ideally not be reached due to self:get() returning default
        love.graphics.setColor(1, 0, 1, 0.5) -- Bright pink semi-transparent for error
        love.graphics.rectangle("fill", x * w, y * h, w, h)
        print("Error: Could not find draw function for tile type:", tileType)
    end
end


return TileDefinitions
