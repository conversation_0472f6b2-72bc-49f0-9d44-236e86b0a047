-- main.lua
-- Main entry point for the game using LÖVE framework

-- Load the engine
local Engine = require("engine")

-- Initialize love modules
function love.load()
    -- Set random seed
    math.randomseed(os.time())
    print("🎮 Starting Lazarus Arc - " .. os.date("%Y-%m-%d %H:%M:%S"))
    
    -- Initialize the engine
    Engine.init()
end

-- The engine will set up all other LÖVE callbacks through Engine.setupLoveCallbacks()
-- This keeps main.lua clean and delegates all logic to the engine and its subsystems


  -- Initialize LÖVE settings
    love.graphics.setDefaultFilter("nearest", "nearest") -- For pixel art clarity
    love.window.setMode(Settings.display.screenWidth, Settings.display.screenHeight, {
        fullscreen = Settings.display.fullscreen,
        vsync = Settings.display.vsync,
        resizable = true
    })
    love.window.setTitle("Lazarus Arc")
    
    -- Initialize Game.time
    Game.time = 0
    
    -- Set up game timers
    Game.timer = {
        callbacks = {},
        nextId = 1,
        
        -- Add a new timer that executes once after the specified delay
        after = function(self, delay, callback)
            local id = self.nextId
            self.nextId = self.nextId + 1
            
            self.callbacks[id] = {
                delay = delay,
                remaining = delay,
                callback = callback,
                repeat_interval = nil
            }
            
            return id
        end,
        
        -- Add a new timer that executes repeatedly with the specified interval
        every = function(self, interval, callback)
            local id = self.nextId
            self.nextId = self.nextId + 1
            
            self.callbacks[id] = {
                delay = interval,
                remaining = interval,
                callback = callback,
                repeat_interval = interval
            }
            
            return id
        end,
        
        -- Cancel a timer
        cancel = function(self, id)
            self.callbacks[id] = nil
        end,
        
        -- Update all timers (call in love.update)
        update = function(self, dt)
            for id, timer in pairs(self.callbacks) do
                timer.remaining = timer.remaining - dt
                
                if timer.remaining <= 0 then
                    timer.callback(dt)
                    
                    if timer.repeat_interval then
                        -- Reset for repeating timer
                        timer.remaining = timer.repeat_interval
                    else
                        -- Remove one-time timer
                        self.callbacks[id] = nil
                    end
                end
            end
        end
    }
    
    -- Initialize input system
    Game.inputManager = {
        keyboard = {
            pressed = {},
            down = {}
        },
        mouse = {
            x = 0,
            y = 0,
            buttons = {false, false, false},
            buttonsPressed = {false, false, false},
            wheel = 0
        },
        controllers = {},
        
        -- Check if a key is currently down
        isKeyDown = function(self, key)
            return love.keyboard.isDown(key)
        end,
        
        -- Check if a key was just pressed
        isKeyPressed = function(self, key)
            return self.keyboard.pressed[key] == true
        end,
        
        -- Get a connected controller by index
        getController = function(self, index)
            local joysticks = love.joystick.getJoysticks()
            return joysticks[index]
        end,
        
        -- Get the current input state
        getInputState = function(self)
            return Controller.getPlayerInput(1)  -- Get input for player 1
        end,
        
        -- Update input state (call in love.update)
        update = function(self)
            Controller.updateInputBuffer()
        end
    }
    
    -- Initialize sound system
    Game.soundSystem = {
        sounds = {},
        music = nil,
        musicVolume = Settings.sound.musicVolume,
        sfxVolume = Settings.sound.sfxVolume,
        
        -- Load a sound
        loadSound = function(self, name, filename)
            self.sounds[name] = love.audio.newSource(filename, "static")
        end,
        
        -- Play a sound
        playSound = function(self, name, x, y, volume)
            if not Settings.sound.enabled or not self.sounds[name] then return end
            
            -- Clone the source for overlapping sounds
            local source = self.sounds[name]:clone()
            source:setVolume((volume or 1.0) * self.sfxVolume)
            
            -- Apply position if provided (for positional audio)
            if x and y and Game.viewportManager and Game.viewportManager.activeViewports[1] then
                local listener = Game.viewportManager.activeViewports[1]
                local lx, ly = listener.x, listener.y
                local dx, dy = x - lx, y - ly
                local distance = math.sqrt(dx*dx + dy*dy)
                
                -- Simple distance attenuation
                if distance > 0 then
                    local attenuation = math.max(0, 1 - distance / 30)
                    source:setVolume(source:getVolume() * attenuation)
                    
                    -- Simple panning
                    if dx ~= 0 then
                        local pan = math.max(-1, math.min(1, dx / 15))
                        -- LÖVE doesn't have direct panning, so we'd need a custom solution
                        -- This is just a placeholder
                    end
                end
            end
            
            source:play()
        end,
        
        -- Play background music
        playMusic = function(self, filename, loop)
            if self.music then
                self.music:stop()
            end
            
            self.music = love.audio.newSource(filename, "stream")
            self.music:setVolume(self.musicVolume)
            self.music:setLooping(loop or true)
            self.music:play()
        end,
        
        -- Stop currently playing music
        stopMusic = function(self)
            if self.music then
                self.music:stop()
                self.music = nil
            end
        end,
        
        -- Set volumes
        setVolumes = function(self, musicVol, sfxVol)
            self.musicVolume = musicVol
            self.sfxVolume = sfxVol
            
            if self.music then
                self.music:setVolume(musicVol)
            end
        end
    }
    
    -- Initialize basic effect system
    Game.effectSystem = {
        effects = {},
        nextId = 1,
        
        -- Create a hit effect
        createHitEffect = function(self, x, y, damage, damageType)
            local id = self.nextId
            self.nextId = self.nextId + 1
            
            self.effects[id] = {
                type = "hit",
                x = x,
                y = y,
                damage = damage,
                damageType = damageType,
                lifetime = 0.5,
                current = 0
            }
            
            return id
        end,
        
        -- Create a critical hit effect
        createCriticalHitEffect = function(self, x, y, damage)
            local id = self.nextId
            self.nextId = self.nextId + 1
            
            self.effects[id] = {
                type = "critical",
                x = x,
                y = y,
                damage = damage,
                lifetime = 0.8,
                current = 0,
                scale = 1.5
            }
            
            return id
        end,
        
        -- Create a status effect visual
        createStatusEffect = function(self, x, y, statusType)
            local id = self.nextId
            self.nextId = self.nextId + 1
            
            self.effects[id] = {
                type = "status",
                x = x,
                y = y,
                statusType = statusType,
                lifetime = 1.0,
                current = 0
            }
            
            return id
        end,
        
        -- Create other effect types (heal, block, etc.)
        createHealEffect = function(self, x, y, amount)
            local id = self.nextId
            self.nextId = self.nextId + 1
            
            self.effects[id] = {
                type = "heal",
                x = x,
                y = y,
                amount = amount,
                lifetime = 0.8,
                current = 0
            }
            
            return id
        end,
        
        -- Update all effects (call in love.update)
        update = function(self, dt)
            for id, effect in pairs(self.effects) do
                effect.current = effect.current + dt
                
                if effect.current >= effect.lifetime then
                    self.effects[id] = nil
                end
            end
        end,
        
        -- Draw all effects (call in love.draw)
        draw = function(self)
            for _, effect in pairs(self.effects) do
                local alpha = 1 - (effect.current / effect.lifetime)
                
                if effect.type == "hit" then
                    -- Draw hit effect
                    love.graphics.setColor(1, 0.3, 0.3, alpha)
                    love.graphics.print("-" .. effect.damage, effect.x, effect.y - 20 * (effect.current / effect.lifetime))
                    
                elseif effect.type == "critical" then
                    -- Draw critical hit effect
                    love.graphics.setColor(1, 0, 0, alpha)
                    love.graphics.print("CRIT! -" .. effect.damage, effect.x, effect.y - 30 * (effect.current / effect.lifetime), 0, 1.5, 1.5)
                    
                elseif effect.type == "heal" then
                    -- Draw heal effect
                    love.graphics.setColor(0, 1, 0.3, alpha)
                    love.graphics.print("+" .. (effect.amount or ""), effect.x, effect.y - 20 * (effect.current / effect.lifetime))
                    
                elseif effect.type == "status" then
                    -- Draw status effect based on type
                    local color = {1, 1, 1, alpha}
                    
                    if effect.statusType == "burning" then
                        color = {1, 0.3, 0, alpha}
                    elseif effect.statusType == "frozen" then
                        color = {0.5, 0.8, 1, alpha}
                    elseif effect.statusType == "poisoned" then
                        color = {0.5, 1, 0.5, alpha}
                    end
                    
                    love.graphics.setColor(color)
                    love.graphics.print(effect.statusType, effect.x, effect.y - 25 * (effect.current / effect.lifetime))
                end
            end
            
            -- Reset color
            love.graphics.setColor(1, 1, 1, 1)
        end
    }
    
    -- Initialize simple UI system
    Game.uiSystem = {
        elements = {},
        messages = {},
        messageTimeout = 3.0,
        activeScreen = nil,
        font = love.graphics.newFont(14),
        largeFont = love.graphics.newFont(20),
        debugMenuVisible = true,
        mainMenuActive = true, -- Start with main menu active
        editingName = false,   -- Flag for name editing
        tempName = "",         -- Temporary storage for name being edited
        
        -- Show a message on screen
        showMessage = function(self, text, duration)
            table.insert(self.messages, {
                text = text,
                duration = duration or self.messageTimeout,
                current = 0
            })
        end,
        
        -- Toggle debug menu
        toggleDebugMenu = function(self)
            self.debugMenuVisible = not self.debugMenuVisible
        end,
        
        -- Draw debug menu
        drawDebugMenu = function(self)
            if not self.debugMenuVisible then return end
            
            -- Semi-transparent background
            love.graphics.setColor(0, 0, 0, 0.7)
            love.graphics.rectangle("fill", 0, 0, Settings.display.screenWidth, Settings.display.screenHeight)
            
            -- Title
            love.graphics.setColor(1, 1, 1)
            love.graphics.setFont(self.largeFont)
            love.graphics.print("Debug Menu", 50, 50)
            love.graphics.setFont(self.font)
            
            -- Current biome info
            local biomeInfo = "Current biome: Unknown"
            if Game and Game.currentWorld and Game.currentWorld.chunkSystem and player then
                local chunkX = math.floor(player.position.x / (Game.chunkSize * 16))
                local chunkY = math.floor(player.position.y / (Game.chunkSize * 16))
                local chunk = Game.currentWorld.chunkSystem:getChunkAt(chunkX, chunkY)
                if chunk then
                    biomeInfo = "Current biome: " .. (chunk.biome or "Unknown") .. 
                                " | Chunk: (" .. chunkX .. "," .. chunkY .. ")" ..
                                " | isHub: " .. tostring(chunk.isHub or false)
                end
            end
            love.graphics.print(biomeInfo, 50, 75)
            
            -- Draw chunk system visualization
            if Game and Game.currentWorld and Game.currentWorld.chunkSystem then
                love.graphics.setColor(1, 1, 1)
                love.graphics.print("Chunk System Visualization:", 500, 75)
                
                -- Draw a small grid representing nearby chunks
                local gridSize = 11  -- Show 11x11 chunks centered on player
                local cellSize = 20
                local offsetX = 500
                local offsetY = 100
                local centerOffset = math.floor(gridSize / 2)
                
                -- Get player's current chunk
                local playerChunkX = 0
                local playerChunkY = 0
                
                if player and player.position then
                    playerChunkX = math.floor(player.position.x / (Game.chunkSize * 16))
                    playerChunkY = math.floor(player.position.y / (Game.chunkSize * 16))
                end
                
                -- Draw chunk grid
                for x = -centerOffset, centerOffset do
                    for y = -centerOffset, centerOffset do
                        local worldChunkX = playerChunkX + x
                        local worldChunkY = playerChunkY + y
                        
                        -- Try to get chunk
                        local chunk = Game.currentWorld.chunkSystem:getChunkAt(worldChunkX, worldChunkY)
                        local isHubChunk = false
                        local color = {0.2, 0.2, 0.2}  -- Default gray for unloaded chunks
                        
                        if chunk then
                            if chunk.isHub then
                                isHubChunk = true
                                color = {0.9, 0.9, 0.9}  -- White for hub chunks
                            elseif chunk.biome then
                                -- Color based on biome
                                if chunk.biome == "forest" then
                                    color = {0.2, 0.6, 0.2}
                                elseif chunk.biome == "plains" then
                                    color = {0.7, 0.8, 0.2}
                                elseif chunk.biome == "desert" then
                                    color = {0.8, 0.7, 0.2}
                                elseif chunk.biome == "ocean" then
                                    color = {0.2, 0.4, 0.8}
                                elseif chunk.biome == "crystal_cavern" then
                                    color = {0.7, 0.3, 0.7}
                                elseif chunk.biome == "hub" then
                                    color = {0.8, 0.8, 0.8}
                                end
                            end
                        end
                        
                        -- Draw the chunk cell
                        love.graphics.setColor(color)
                        love.graphics.rectangle("fill", 
                            offsetX + (x + centerOffset) * cellSize, 
                            offsetY + (y + centerOffset) * cellSize, 
                            cellSize - 1, 
                            cellSize - 1)
                        
                        -- Mark player's current chunk
                        if x == 0 and y == 0 then
                            love.graphics.setColor(1, 0, 0)
                            love.graphics.rectangle("line", 
                                offsetX + (x + centerOffset) * cellSize, 
                                offsetY + (y + centerOffset) * cellSize, 
                                cellSize - 1, 
                                cellSize - 1)
                            
                            -- Draw player indicator
                            love.graphics.setColor(1, 0, 0)
                            love.graphics.circle("fill", 
                                offsetX + (x + centerOffset) * cellSize + cellSize/2, 
                                offsetY + (y + centerOffset) * cellSize + cellSize/2, 
                                3)
                        end
                        
                        -- Add border for hub chunks
                        if isHubChunk then
                            love.graphics.setColor(1, 1, 0)
                            love.graphics.rectangle("line", 
                                offsetX + (x + centerOffset) * cellSize, 
                                offsetY + (y + centerOffset) * cellSize, 
                                cellSize - 1, 
                                cellSize - 1)
                        end
                    end
                end
                
                -- Add legend
                local legendY = offsetY + gridSize * cellSize + 20
                love.graphics.setColor(1, 1, 1)
                love.graphics.print("Legend:", offsetX, legendY)
                
                legendY = legendY + 20
                love.graphics.setColor(0.9, 0.9, 0.9)
                love.graphics.rectangle("fill", offsetX, legendY, 15, 15)
                love.graphics.setColor(1, 1, 1)
                love.graphics.print("Hub", offsetX + 20, legendY)
                
                legendY = legendY + 20
                love.graphics.setColor(0.2, 0.6, 0.2)
                love.graphics.rectangle("fill", offsetX, legendY, 15, 15)
                love.graphics.setColor(1, 1, 1)
                love.graphics.print("Forest", offsetX + 20, legendY)
                
                legendY = legendY + 20
                love.graphics.setColor(0.7, 0.8, 0.2)
                love.graphics.rectangle("fill", offsetX, legendY, 15, 15)
                love.graphics.setColor(1, 1, 1)
                love.graphics.print("Plains", offsetX + 20, legendY)
                
                legendY = legendY + 20
                love.graphics.setColor(0.8, 0.7, 0.2)
                love.graphics.rectangle("fill", offsetX, legendY, 15, 15)
                love.graphics.setColor(1, 1, 1)
                love.graphics.print("Desert", offsetX + 20, legendY)
                
                legendY = legendY + 20
                love.graphics.setColor(0.2, 0.4, 0.8)
                love.graphics.rectangle("fill", offsetX, legendY, 15, 15)
                love.graphics.setColor(1, 1, 1)
                love.graphics.print("Ocean", offsetX + 20, legendY)
            end
            
            -- Draw in two columns
            local column1X = 50
            local column2X = 350
            local startY = 115
            local spacing = 25
            
            -- Main systems section (left column)
            love.graphics.setColor(1, 1, 1)
            love.graphics.print("Game Systems:", column1X, startY)
            local y = startY + 25
            
            for module, enabled in pairs(Settings.debug.modules) do
                -- Only show main systems here (not placeholder systems)
                if not module:find("placeholder") then
                    love.graphics.setColor(0.8, 0.8, 0.8)
                    -- Draw checkbox
                    love.graphics.rectangle(enabled and "fill" or "line", column1X + 5, y + 2, 16, 16)
                    
                    -- Draw label
                    love.graphics.setColor(1, 1, 1)
                    love.graphics.print(module, column1X + 30, y)
                    y = y + spacing
                    
                    -- Wrap to next "mini-column" if getting too long
                    if y > Settings.display.screenHeight - 100 then
                        column1X = column1X + 150
                        y = startY + 25
                    end
                end
            end
            
            -- Placeholder systems section (right column)
            love.graphics.setColor(1, 1, 1)
            love.graphics.print("Placeholder Systems:", column2X, startY)
            y = startY + 25
            
            for module, enabled in pairs(Settings.debug.modules) do
                -- Only show placeholder systems here
                if module:find("placeholder") then
                    love.graphics.setColor(0.8, 0.8, 0.8)
                    -- Draw checkbox
                    love.graphics.rectangle(enabled and "fill" or "line", column2X + 5, y + 2, 16, 16)
                    
                    -- Draw label
                    love.graphics.setColor(1, 1, 1)
                    local displayName = module:gsub("placeholder_", "")
                    love.graphics.print(displayName, column2X + 30, y)
                    y = y + spacing
                end
            end
            
            -- Instructions
            y = Settings.display.screenHeight - 60
            love.graphics.setColor(0.7, 0.7, 0.7)
            love.graphics.print("Toggle modules by clicking checkboxes", 50, y)
            love.graphics.print("Press F1 to hide/show this menu", 50, y + 20)
        end,
        
        -- Handle debug menu clicks
        handleDebugMenuClick = function(self, x, y)
            if not self.debugMenuVisible then return end
            
            local column1X = 50
            local column2X = 350
            local startY = 115
            local spacing = 25
            
            -- Process main game system clicks (left column area)
            local currentY = startY + 25
            local currentX = column1X
            
            for module, enabled in pairs(Settings.debug.modules) do
                if not module:find("placeholder") then
                    -- Check if click is on this checkbox
                    if x >= currentX + 5 and x <= currentX + 21 and
                       y >= currentY + 2 and y <= currentY + 18 then
                        -- Toggle the module
                        Settings.debug.modules[module] = not enabled
                        self:showMessage(module .. " " .. (not enabled and "enabled" or "disabled"))
                        return
                    end
                    
                    currentY = currentY + spacing
                    
                    -- Wrap to next "mini-column" if getting too long
                    if currentY > Settings.display.screenHeight - 100 then
                        currentX = currentX + 150
                        currentY = startY + 25
                    end
                end
            end
            
            -- Process placeholder system clicks (right column area)
            currentY = startY + 25
            
            for module, enabled in pairs(Settings.debug.modules) do
                if module:find("placeholder") then
                    -- Check if click is on this checkbox
                    if x >= column2X + 5 and x <= column2X + 21 and
                       y >= currentY + 2 and y <= currentY + 18 then
                        -- Toggle the module
                        Settings.debug.modules[module] = not enabled
                        local displayName = module:gsub("placeholder_", "")
                        self:showMessage("Placeholder " .. displayName .. " " .. (not enabled and "enabled" or "disabled"))
                        return
                    end
                    
                    currentY = currentY + spacing
                end
            end
        end,
        
        -- Open various UI screens
        openInventory = function(self, player)
            self.activeScreen = {
                type = "inventory",
                player = player
            }
        end,
        
        openCharacterScreen = function(self, player)
            self.activeScreen = {
                type = "character",
                player = player
            }
        end,
        
        openMap = function(self, player)
            self.activeScreen = {
                type = "map",
                player = player
            }
        end,
        
        closeScreen = function(self)
            self.activeScreen = nil
        end,
        
        -- Main menu functions
        drawMainMenu = function(self)
            -- Semi-transparent background with world visible behind
            love.graphics.setColor(0, 0, 0, 0.5) -- More transparent to see the simulation
            love.graphics.rectangle("fill", 0, 0, Settings.display.screenWidth, Settings.display.screenHeight)
            
            -- Title at the top
            love.graphics.setColor(1, 1, 1)
            love.graphics.setFont(love.graphics.newFont(40))
            love.graphics.print("Lazarus Arc", Settings.display.screenWidth / 2 - 150, 50)
            
            if not self.creatingCharacter then
                -- MAIN MENU BUTTONS - MOVED TO BOTTOM
                love.graphics.setFont(self.largeFont)
                
                -- Button dimensions
                local buttonWidth = 200
                local buttonHeight = 40
                local spacing = 20
                local totalWidth = buttonWidth * 2 + spacing
                local startX = Settings.display.screenWidth / 2 - totalWidth / 2
                local buttonY = Settings.display.screenHeight - 100
                
                -- New Character Button
                love.graphics.setColor(0.2, 0.4, 0.8)
                love.graphics.rectangle("fill", startX, buttonY, buttonWidth, buttonHeight)
                love.graphics.setColor(1, 1, 1)
                love.graphics.print("New Character", startX + 30, buttonY + 10)
                
                -- Load Character Button 
                love.graphics.setColor(0.2, 0.4, 0.8)
                love.graphics.rectangle("fill", startX + buttonWidth + spacing, buttonY, buttonWidth, buttonHeight)
                love.graphics.setColor(1, 1, 1)
                love.graphics.print("Load Character", startX + buttonWidth + spacing + 30, buttonY + 10)
                
                -- Exit Button
                love.graphics.setColor(0.8, 0.2, 0.2)
                love.graphics.rectangle("fill", Settings.display.screenWidth / 2 - buttonWidth / 2, buttonY + buttonHeight + spacing, buttonWidth, buttonHeight) 
                love.graphics.setColor(1, 1, 1)
                love.graphics.print("Exit Game", Settings.display.screenWidth / 2 - buttonWidth / 2 + 60, buttonY + buttonHeight + spacing + 10)
                
                -- Version info is now displayed globally in the main love.draw function
            else
                -- CHARACTER CREATION SCREEN
                
                love.graphics.setFont(self.largeFont)
                love.graphics.print("Character Creation", Settings.display.screenWidth / 2 - 100, 150)
                
                -- Draw character creation UI
                local formX = Settings.display.screenWidth / 2 - 200
                local formY = 200
                local buttonWidth = 100
                local buttonHeight = 30
                
                -- Name field
                love.graphics.setColor(1, 1, 1)
                love.graphics.print("Name:", formX, formY)
                
                -- Name input box
                if self.editingName then
                    love.graphics.setColor(0.4, 0.4, 0.6)  -- Highlight color when editing
                    love.graphics.rectangle("fill", formX + 100, formY - 5, 200, 30)
                    love.graphics.setColor(1, 1, 1)
                    -- Show the name being edited with a cursor blink effect
                    local displayName = self.tempName
                    if math.floor(love.timer.getTime() * 2) % 2 == 0 then
                        displayName = displayName .. "|"
                    end
                    love.graphics.print(displayName, formX + 110, formY)
                else
                    love.graphics.setColor(0.3, 0.3, 0.3)
                    love.graphics.rectangle("fill", formX + 100, formY - 5, 200, 30)
                    love.graphics.setColor(1, 1, 1)
                    love.graphics.print(self.characterName or "Player1", formX + 110, formY)
                end
                
                -- Random name button (dice icon)
                love.graphics.setColor(0.5, 0.5, 0.8)
                love.graphics.rectangle("fill", formX + 310, formY - 5, 30, 30)
                love.graphics.setColor(1, 1, 1)
                love.graphics.print("🎲", formX + 316, formY)
                
                -- "Edit Name" button
                love.graphics.setColor(0.4, 0.4, 0.6)
                love.graphics.rectangle("fill", formX + 350, formY - 5, 80, 30)
                love.graphics.setColor(1, 1, 1)
                love.graphics.print("Edit", formX + 370, formY)
                
                -- Class selection
                formY = formY + 50
                love.graphics.setColor(1, 1, 1)
                love.graphics.print("Class:", formX, formY)
                
                -- Class buttons - use grid layout to show all classes
                local classes = CharacterCreator.getAvailableClasses()
                local selectedClass = self.characterClass or 1
                local classButtonsPerRow = 4
                local classButtonWidth = 100
                local classButtonHeight = 25
                local classButtonSpacing = 10
                
                for i, class in ipairs(classes) do
                    local row = math.floor((i-1) / classButtonsPerRow)
                    local col = (i-1) % classButtonsPerRow
                    
                    local btnX = formX + 100 + col * (classButtonWidth + classButtonSpacing)
                    local btnY = formY + row * (classButtonHeight + 5)
                    
                    -- Highlight selected class
                    if i == selectedClass then
                        love.graphics.setColor(0.4, 0.6, 1.0)
                    else
                        love.graphics.setColor(0.2, 0.4, 0.8)
                    end
                    
                    love.graphics.rectangle("fill", btnX, btnY, classButtonWidth, classButtonHeight)
                    
                    love.graphics.setColor(1, 1, 1)
                    love.graphics.print(class, btnX + 10, btnY + 5)
                end
                
                -- Class description - add 5 rows to accommodate all classes
                formY = formY + 5 * (classButtonHeight + 5) + 10
                love.graphics.setColor(1, 1, 1)
                love.graphics.print("Class Description:", formX, formY)
                
                -- Get selected class info
                local classInfo = "No information available"
                if classes[selectedClass] then
                    local classData = Classes.loadedClasses[string.lower(classes[selectedClass])]
                    if classData and classData.description then
                        classInfo = classData.description
                    else
                        classInfo = "A brave " .. classes[selectedClass] .. " ready for adventure."
                    end
                end
                
                love.graphics.setColor(0.8, 0.8, 0.8)
                love.graphics.printf(classInfo, formX + 100, formY, 300, "left")
                
                -- Starting kit selection
                formY = formY + 60
                love.graphics.setColor(1, 1, 1)
                love.graphics.print("Starting Kit:", formX, formY)
                
                -- Kit buttons
                local kits = {}
                for kitName, _ in pairs(CharacterCreator.startingKits) do
                    table.insert(kits, kitName)
                end
                local selectedKit = self.characterKit or 1
                
                for i, kit in ipairs(kits) do
                    local btnX = formX + 100 + (i-1) * (buttonWidth + 10)
                    
                    -- Highlight selected kit
                    if i == selectedKit then
                        love.graphics.setColor(0.4, 0.6, 1.0)
                    else
                        love.graphics.setColor(0.2, 0.4, 0.8)
                    end
                    
                    love.graphics.rectangle("fill", btnX, formY - 5, buttonWidth, buttonHeight)
                    
                    love.graphics.setColor(1, 1, 1)
                    love.graphics.print(kit, btnX + 10, formY)
                end
                
                -- Kit description
                formY = formY + 40
                local kitDescription = "No description available"
                if kits[selectedKit] and CharacterCreator.startingKits[kits[selectedKit]] then
                    kitDescription = CharacterCreator.startingKits[kits[selectedKit]].description
                end
                
                love.graphics.setColor(0.8, 0.8, 0.8)
                love.graphics.print(kitDescription, formX + 100, formY)
                
                -- Create Character button
                formY = formY + 70
                love.graphics.setColor(0.3, 0.7, 0.3)
                local createBtnX = Settings.display.screenWidth / 2 - 100
                love.graphics.rectangle("fill", createBtnX, formY, 200, 40)
                
                love.graphics.setColor(1, 1, 1)
                love.graphics.print("Create Character", createBtnX + 30, formY + 10)
                
                -- Back button
                formY = formY + 60
                love.graphics.setColor(0.7, 0.3, 0.3)
                love.graphics.rectangle("fill", createBtnX, formY, 200, 40)
                
                love.graphics.setColor(1, 1, 1)
                love.graphics.print("Back to Main Menu", createBtnX + 30, formY + 10)
            end
            
            -- Reset color
            love.graphics.setColor(1, 1, 1, 1)
        end,
        
        handleMainMenuClick = function(self, x, y)
            if not self.creatingCharacter then
                -- Handle main menu buttons - UPDATED FOR NEW POSITIONS
                local buttonWidth = 200
                local buttonHeight = 40
                local spacing = 20
                local totalWidth = buttonWidth * 2 + spacing
                local startX = Settings.display.screenWidth / 2 - totalWidth / 2
                local buttonY = Settings.display.screenHeight - 100
                
                -- Check if New Character button was clicked
                if x >= startX and x <= startX + buttonWidth and
                   y >= buttonY and y <= buttonY + buttonHeight then
                    self:showMessage("Character creation...")
                    self.creatingCharacter = true
                    self.characterName = "Player1"
                    self.characterClass = 1
                    self.characterKit = 1
                end
                
                -- Check if Load Character button was clicked
                if x >= startX + buttonWidth + spacing and x <= startX + buttonWidth * 2 + spacing and
                   y >= buttonY and y <= buttonY + buttonHeight then
                    self:showMessage("Loading character...")
                    self:loadCharacter()
                end
                
                -- Check if Exit button was clicked
                if x >= Settings.display.screenWidth / 2 - buttonWidth / 2 and 
                   x <= Settings.display.screenWidth / 2 + buttonWidth / 2 and
                   y >= buttonY + buttonHeight + spacing and 
                   y <= buttonY + buttonHeight * 2 + spacing then
                    love.event.quit()
                end
            else
                -- Handle character creation buttons
                local formX = Settings.display.screenWidth / 2 - 200
                local formY = 200
                local buttonWidth = 100
                local buttonHeight = 30
                
                -- Name field click - handle name input
                if x >= formX + 100 and x <= formX + 300 and
                   y >= formY - 5 and y <= formY + 25 then
                    -- Show text input dialog
                    self.editingName = true
                    love.keyboard.setTextInput(true)
                    self.tempName = self.characterName or ""
                    self:showMessage("Enter character name")
                end
                
                -- Random name button (dice)
                if x >= formX + 310 and x <= formX + 340 and
                   y >= formY - 5 and y <= formY + 25 then
                    -- Generate random name
                    self.characterName = CharacterCreator.getRandomName()
                    self:showMessage("Generated random name: " .. self.characterName)
                end
                
                -- Edit name button
                if x >= formX + 350 and x <= formX + 430 and
                   y >= formY - 5 and y <= formY + 25 then
                    -- Show text input dialog
                    self.editingName = true
                    love.keyboard.setTextInput(true)
                    self.tempName = self.characterName or ""
                    self:showMessage("Enter character name")
                end
                
                -- Class buttons - use grid layout
                local classes = CharacterCreator.getAvailableClasses()
                local classButtonsPerRow = 4
                local classButtonWidth = 100
                local classButtonHeight = 25
                local classButtonSpacing = 10
                
                formY = formY + 50
                
                for i = 1, #classes do
                    local row = math.floor((i-1) / classButtonsPerRow)
                    local col = (i-1) % classButtonsPerRow
                    
                    local btnX = formX + 100 + col * (classButtonWidth + classButtonSpacing)
                    local btnY = formY + row * (classButtonHeight + 5)
                    
                    if x >= btnX and x <= btnX + classButtonWidth and
                       y >= btnY and y <= btnY + classButtonHeight then
                        self.characterClass = i
                        self:showMessage("Selected class: " .. classes[i])
                        break
                    end
                end
                
                -- Skip past all class buttons and description
                formY = formY + 5 * (classButtonHeight + 5) + 10 + 60
                
                -- Kit buttons
                local kits = {}
                for kitName, _ in pairs(CharacterCreator.startingKits) do
                    table.insert(kits, kitName)
                end
                
                for i = 1, #kits do
                    local btnX = formX + 100 + (i-1) * (buttonWidth + 10)
                    
                    if x >= btnX and x <= btnX + buttonWidth and
                       y >= formY - 5 and y <= formY + buttonHeight - 5 then
                        self.characterKit = i
                        self:showMessage("Selected starting kit: " .. kits[i])
                        break
                    end
                end
                
                -- Create Character button
                formY = formY + 40 + 70  -- Skip kit description
                local createBtnX = Settings.display.screenWidth / 2 - 100
                
                if x >= createBtnX and x <= createBtnX + 200 and
                   y >= formY and y <= formY + 40 then
                    local characterName = self.characterName or "Player1"
                    local classes = CharacterCreator.getAvailableClasses()
                    local kits = {}
                    for kitName, _ in pairs(CharacterCreator.startingKits) do
                        table.insert(kits, kitName)
                    end
                    
                    self:createNewCharacter(characterName, classes[self.characterClass], kits[self.characterKit])
                end
                
                -- Back button
                formY = formY + 60
                
                if x >= createBtnX and x <= createBtnX + 200 and
                   y >= formY and y <= formY + 40 then
                    self.creatingCharacter = false
                    self.editingName = false
                end
            end
        end,
        
        createNewCharacter = function(self, playerName, playerClass, startingKit)
            -- Create a new character using the CharacterCreator
            player = CharacterCreator.createCharacter(playerName, playerClass, startingKit)
            
            -- Set player position to center of hub chunk
            if Game.currentWorld and Game.currentWorld.chunkSize then
                player.position.x = Game.currentWorld.chunkSize / 2
                player.position.y = Game.currentWorld.chunkSize / 2
            else
                player.position.x = 0
                player.position.y = 0
            end
            
            -- Register player with the world
            if Game.currentWorld and Game.currentWorld.entitySystem then
                Game.currentWorld.entitySystem:register(player)
            end
            
            -- Store player in Game object
            Game.player = player
            
            -- Add player to viewport
            if Game.viewportManager then
                Game.viewportManager:addPlayer({
                    id = 1,
                    x = player.position.x,
                    y = player.position.y,
                    character = player.character
                })
            end
            
            -- Save character to JSON file
            self:saveCharacter(player)
            
            -- Exit main menu
            self.mainMenuActive = false
            self.creatingCharacter = false
            self:showMessage("Welcome to your personal Hub World, " .. playerName .. "!")
        end,
        
        loadCharacter = function(self)
            -- In a real game, you'd show character selection from saved files
            -- For this demo, we'll look for any save files and load the first one
            local lunajson = require("lunajson")
            local saveDir = "saves/"
            local saveFiles = love.filesystem.getDirectoryItems(saveDir)
            
            if #saveFiles > 0 then
                -- Load the first save file
                local filename = saveDir .. saveFiles[1]
                local fileContents, size = love.filesystem.read(filename)
                
                if fileContents then
                    local characterData = lunajson.decode(fileContents)
                    
                    if characterData then
                        self:showMessage("Loading character: " .. (characterData.name or "Unknown"))
                        self:createNewCharacter(characterData.name, characterData.class, "Basic")
                        
                        -- Apply saved data to the newly created character
                        if Game.player and Game.player.character then
                            -- Apply stats
                            if characterData.stats then
                                for stat, value in pairs(characterData.stats) do
                                    Game.player.character.stats[stat] = value
                                end
                            end
                            
                            -- Apply inventory if it exists
                            if characterData.inventory and characterData.inventory.items then
                                Game.player.character.inventory.items = characterData.inventory.items
                            end
                            
                            -- Apply position if it exists
                            if characterData.position then
                                Game.player.position.x = characterData.position.x
                                Game.player.position.y = characterData.position.y
                            end
                            
                            -- Apply level
                            if characterData.level then
                                Game.player.character.level = characterData.level
                            end
                        end
                        
                        return
                    end
                end
            end
            
            -- If no saves found or loading failed, create a default character
            local defaultName = "Wanderer"
            local defaultClass = "Warrior"
            local defaultKit = "Basic"
            
            self:createNewCharacter(defaultName, defaultClass, defaultKit)
            self:showMessage("No saved characters found. Created default character.")
        end,
        
        -- Update UI (call in love.update)
        update = function(self, dt)
            -- Update messages
            for i, message in ipairs(self.messages) do
                message.current = message.current + dt
                
                if message.current >= message.duration then
                    table.remove(self.messages, i)
                end
            end
        end,
        
        -- Draw UI (call in love.draw)
        draw = function(self)
            -- Set font
            love.graphics.setFont(self.font)
            
            -- Draw messages
            for i, message in ipairs(self.messages) do
                local alpha = 1
                
                -- Fade in/out
                if message.current < 0.5 then
                    alpha = message.current / 0.5
                elseif message.current > message.duration - 0.5 then
                    alpha = (message.duration - message.current) / 0.5
                end
                
                love.graphics.setColor(1, 1, 1, alpha)
                love.graphics.printf(
                    message.text,
                    20,
                    20 + (i-1) * 25,
                    Settings.display.screenWidth - 40,
                    "left"
                )
            end
            
            -- Draw main menu if active
            if self.mainMenuActive then
                self:drawMainMenu()
            else
                -- Draw active screen if any
                if self.activeScreen then
                    self:drawScreen(self.activeScreen)
                end
                
                -- Draw debug menu only if visible
                if self.debugMenuVisible then
                    self:drawDebugMenu()
                end
            end
            
            -- Reset color
            love.graphics.setColor(1, 1, 1, 1)
        end,
        
        -- Draw a specific screen
        drawScreen = function(self, screen)
            -- Semi-transparent background
            love.graphics.setColor(0, 0, 0, 0.7)
            love.graphics.rectangle("fill", 0, 0, Settings.display.screenWidth, Settings.display.screenHeight)
            love.graphics.setColor(1, 1, 1, 1)
            
            if screen.type == "inventory" then
                self:drawInventoryScreen(screen.player)
            elseif screen.type == "character" then
                self:drawCharacterScreen(screen.player)
            elseif screen.type == "map" then
                self:drawMapScreen(screen.player)
            end
        end,
        
        -- Draw inventory screen
        drawInventoryScreen = function(self, player)
            love.graphics.setFont(self.largeFont)
            love.graphics.print("Inventory", 50, 50)
            love.graphics.setFont(self.font)
            
            if not player.character then return end
            
            -- Basic inventory display
            local y = 100
            for itemId, itemData in pairs(player.character.inventory.items or {}) do
                love.graphics.print(
                    itemData.quantity .. "x " .. (itemData.name or itemId),
                    50,
                    y
                )
                y = y + 25
            end
            
            -- Equipment display
            love.graphics.print("Equipment:", 400, 100)
            local equipY = 130
            for slot, itemId in pairs(player.equipment or {}) do
                if itemId then
                    love.graphics.print(
                        slot .. ": " .. itemId,
                        400,
                        equipY
                    )
                    equipY = equipY + 25
                end
            end
            
            -- Instructions
            love.graphics.print("Press [ESC] to close", 50, Settings.display.screenHeight - 50)
        end,
        
        -- Draw character screen
        drawCharacterScreen = function(self, player)
            love.graphics.setFont(self.largeFont)
            love.graphics.print("Character: " .. (player.name or "Unknown"), 50, 50)
            love.graphics.setFont(self.font)
            
            if not player.character then 
                love.graphics.print("No character data available", 50, 100)
                return 
            end
            
            -- Basic stats display
            local y = 100
            love.graphics.print("Level: " .. (player.character.level or 1), 50, y)
            y = y + 25
            
            -- Fix for table.concat error - check if class exists and is a table
            if player.character.class and type(player.character.class) == "table" then
                love.graphics.print("Class: " .. table.concat(player.character.class, ", "), 50, y)
            else
                love.graphics.print("Class: " .. (player.character.class or "Unknown"), 50, y)
            end
            y = y + 25
            
            love.graphics.print("Stats:", 50, y)
            y = y + 25
            
            -- Check if stats exists
            if player.character.stats and type(player.character.stats) == "table" then
                for stat, value in pairs(player.character.stats) do
                    love.graphics.print(
                        stat .. ": " .. math.floor(value * 10) / 10,
                        70,
                        y
                    )
                    y = y + 20
                end
            else
                love.graphics.print("No stats available", 70, y)
            end
            
            -- Status effects
            if player.status_effects and next(player.status_effects) then
                love.graphics.print("Status Effects:", 400, 100)
                local effectY = 130
                
                for effect, data in pairs(player.status_effects) do
                    love.graphics.print(
                        effect .. " (" .. math.floor(data.duration) .. "s)",
                        400,
                        effectY
                    )
                    effectY = effectY + 25
                end
            end
            
            -- Add Exit Game button
            local exitBtnX = Settings.display.screenWidth / 2 - 100
            local exitBtnY = Settings.display.screenHeight - 100
            
            love.graphics.setColor(0.8, 0.2, 0.2)
            love.graphics.rectangle("fill", exitBtnX, exitBtnY, 200, 40)
            love.graphics.setColor(1, 1, 1)
            love.graphics.print("Exit Game", exitBtnX + 70, exitBtnY + 10)
            
            -- Instructions
            love.graphics.print("Press [ESC] to close", 50, Settings.display.screenHeight - 50)
        end,
        
        -- Draw map screen
        drawMapScreen = function(self, player)
            love.graphics.setFont(self.largeFont)
            love.graphics.print("World Map", 50, 50)
            love.graphics.setFont(self.font)
            
            -- Get player's current chunk
            local chunkX = math.floor(player.position.x / 16)
            local chunkY = math.floor(player.position.y / 16)
            
            -- Draw a simple mini-map of surrounding chunks
            local mapX = Settings.display.screenWidth / 2 - 150
            local mapY = Settings.display.screenHeight / 2 - 150
            local chunkSize = 30
            
            for dx = -5, 5 do
                for dy = -5, 5 do
                    local cx = chunkX + dx
                    local cy = chunkY + dy
                    
                    -- Draw chunk
                    local chunk = Game.currentWorld and Game.currentWorld.chunks[cx .. "," .. cy]
                    local color = {0.2, 0.2, 0.2}
                    
                    if chunk then
                        if chunk.biome == "forest" then
                            color = {0.2, 0.6, 0.2}
                        elseif chunk.biome == "plains" then
                            color = {0.7, 0.8, 0.2}
                        elseif chunk.biome == "desert" then
                            color = {0.8, 0.7, 0.2}
                        elseif chunk.biome == "ocean" then
                            color = {0.2, 0.4, 0.8}
                        elseif chunk.biome == "crystal_cavern" then
                            color = {0.7, 0.3, 0.7}
                        elseif chunk.biome == "hub" then
                            color = {0.8, 0.8, 0.8}
                        end
                    end
                    
                    love.graphics.setColor(color)
                    love.graphics.rectangle(
                        "fill",
                        mapX + (dx + 5) * chunkSize,
                        mapY + (dy + 5) * chunkSize,
                        chunkSize - 2,
                        chunkSize - 2
                    )
                    
                    -- Mark player's current chunk
                    if dx == 0 and dy == 0 then
                        love.graphics.setColor(1, 0, 0)
                        love.graphics.rectangle(
                            "line",
                            mapX + (dx + 5) * chunkSize,
                            mapY + (dy + 5) * chunkSize,
                            chunkSize - 2,
                            chunkSize - 2
                        )
                    end
                end
            end
            
            -- Reset color
            love.graphics.setColor(1, 1, 1, 1)
            
            -- Instructions
            love.graphics.print("Press [ESC] to close", 50, Settings.display.screenHeight - 50)
        end,
        
        -- Add a method to handle character screen clicks
        handleScreenClick = function(self, screen, x, y)
            if screen.type == "character" then
                -- Check if exit button was clicked
                local exitBtnX = Settings.display.screenWidth / 2 - 100
                local exitBtnY = Settings.display.screenHeight - 100
                
                if x >= exitBtnX and x <= exitBtnX + 200 and
                   y >= exitBtnY and y <= exitBtnY + 40 then
                    love.event.quit()
                end
            end
        end,
        
        -- Add save/load character functionality using JSON
        saveCharacter = function(self, playerData)
            -- Helper function to sanitize data for JSON
            local function sanitizeForJson(data, depth)
                depth = depth or 0
                if depth > 10 then return nil end -- Prevent infinite recursion
                
                local dataType = type(data)
                
                -- Handle basic types
                if dataType == "string" or dataType == "number" or dataType == "boolean" or data == nil then
                    return data
                -- Handle tables by recursively sanitizing each entry
                elseif dataType == "table" then
                    local result = {}
                    for k, v in pairs(data) do
                        -- Only include string or number keys
                        if type(k) == "string" or type(k) == "number" then
                            -- Recursively sanitize value
                            result[k] = sanitizeForJson(v, depth + 1)
                        end
                    end
                    return result
                else
                    -- Functions, userdata, threads, etc. can't be serialized to JSON
                    return nil
                end
            end
            
            -- Create a character data structure for saving
            local characterData = {
                name = playerData.name,
                class = playerData.character.class,
                level = playerData.character.level or 1,
                stats = sanitizeForJson(playerData.character.stats) or {},
                inventory = sanitizeForJson(playerData.character.inventory) or {},
                equipment = sanitizeForJson(playerData.character.equipment) or {},
                position = sanitizeForJson(playerData.position) or {x = 0, y = 0},
                created = os.time(),
                lastPlayed = os.time()
            }
            
            -- Convert to JSON string using lunajson
            local lunajson = require("lunajson")
            local jsonString = lunajson.encode(characterData)
            
            -- Save to file
            local saveDir = "saves/"
            love.filesystem.createDirectory(saveDir)
            local filename = saveDir .. string.gsub(playerData.name, "%s+", "_") .. ".json"
            
            local success, message = love.filesystem.write(filename, jsonString)
            if success then
                self:showMessage("Character saved successfully!")
            else
                self:showMessage("Error saving character: " .. (message or "unknown error"))
            end
            
            return success
        end
    }
    
    -- Initialize the viewport manager
    Game.viewportManager = ViewportManager.new(Settings.display.screenWidth, Settings.display.screenHeight)
    
    -- Initialize the game
    if not Game.init() then
        error("Failed to initialize game")
    end
    
    -- Start a new game without a player - provide empty tables for player names and classes
    Game.newGame({}, {})
    
    -- Initialize world only (player will be created through main menu)
    print("Game initialized successfully! Showing main menu.")
    Game.uiSystem:showMessage("Welcome to Lazarus Arc!")
    
    -- Don't create player here anymore - it will be created when user selects from main menu
    -- We'll still load sounds
    Game.soundSystem:loadSound("attack", "sounds/attack.wav")
    Game.soundSystem:loadSound("block", "sounds/block.wav")
    Game.soundSystem:loadSound("hit", "sounds/hit.wav")
end

-- Main update function
function love.update(dt)
    -- Update game time
    if Game then
        Game.time = (Game.time or 0) + dt
        
        -- Update input manager
        if Game.inputManager then
            Game.inputManager:update()
        end
        
        -- Update chunk system if enabled
        if Settings.debug and Settings.debug.modules and Settings.debug.modules.chunks and 
           Game.world and Game.world.chunkSystem then
            Game.world.chunkSystem:update(dt)
        end
        
        -- Update player if enabled
        if Settings.debug and Settings.debug.modules and Settings.debug.modules.player and Game.player then
            -- Use Controller.applyPlayerMovement to handle player movement
            Controller.applyPlayerMovement(Game.player, dt)
            
            -- Update viewport to follow player
            if Game.viewportManager and Game.viewportManager.activeViewports and 
               Game.viewportManager.activeViewports[1] and Game.player.position then
                -- Update player position in viewport manager's players table
                if Game.viewportManager.players and Game.viewportManager.players[1] then
                    Game.viewportManager.players[1].x = Game.player.position.x
                    Game.viewportManager.players[1].y = Game.player.position.y
                    -- Call the update method which handles camera movement
                    Game.viewportManager:update(dt)
                end
            end
        end
        
        -- Update weather if enabled
        if Settings.debug and Settings.debug.modules and Settings.debug.modules.weather and Game.weatherSystem then
            Game.weatherSystem:update(dt)
        end
        
        -- Update entities if enabled
        if Settings.debug and Settings.debug.modules and Settings.debug.modules.entities and Game.world and Game.world.entityManager then
            Game.world.entityManager:update(dt)
        end
        
        -- Update UI messages
        if Game.uiSystem and Game.uiSystem.messages then
            for i = #Game.uiSystem.messages, 1, -1 do
                local message = Game.uiSystem.messages[i]
                message.current = message.current + dt
                
                if message.current >= message.duration then
                    table.remove(Game.uiSystem.messages, i)
                end
            end
        end
    end
end

-- Main draw function
function love.draw()
    -- Clear screen
    love.graphics.clear(0.1, 0.1, 0.2, 1)  -- Dark blue background
    
    -- Apply camera transform based on game state
    love.graphics.push()
    
    if Game.uiSystem.mainMenuActive then
        -- Center on hub area (0,0) for main menu background
        local screenCenterX = Settings.display.screenWidth / 2
        local screenCenterY = Settings.display.screenHeight / 2
        
        -- Apply camera transform - center on hub world (0,0)
        love.graphics.translate(screenCenterX, screenCenterY)
        
        -- Draw the hub area and surrounding chunks
        if Game.world and Game.world.chunkSystem then
            local tileSize = 16  -- Smaller tiles for background effect
            local chunkSize = Game.chunkSize or 32
            
            -- Draw chunks in a 3x3 grid centered on (0,0)
            for chunkX = -1, 1 do
                for chunkY = -1, 1 do
                    local chunk = Game.world.chunkSystem:getChunkAt(chunkX, chunkY)
                    
                    if chunk then
                        -- Calculate chunk screen position
                        local chunkScreenX = chunkX * chunkSize * tileSize
                        local chunkScreenY = chunkY * chunkSize * tileSize
                        local chunkPixelWidth = chunkSize * tileSize
                        local chunkPixelHeight = chunkSize * tileSize
                        
                        -- Draw chunk boundary
                        love.graphics.setColor(0.1, 0.1, 0.1, 0.3)
                        love.graphics.rectangle("fill", 
                            chunkScreenX, chunkScreenY, 
                            chunkPixelWidth, chunkPixelHeight)
                        
                        -- Draw chunk border
                        love.graphics.setColor(0.5, 0.5, 0.5, 0.5)
                        love.graphics.rectangle("line", 
                            chunkScreenX, chunkScreenY, 
                            chunkPixelWidth, chunkPixelHeight)
                        
                        -- Draw tiles if they exist
                        if chunk.tiles then
                            -- Loop through all tiles based on chunk size
                            for x = 0, chunkSize - 1 do
                                for y = 0, chunkSize - 1 do
                                    local tileIndex = y * chunkSize + x + 1  -- +1 for Lua's 1-based indexing
                                    local tile = chunk.tiles[tileIndex]
                                    
                                    if tile then
                                        -- Calculate screen position
                                        local tileScreenX = chunkScreenX + (x * tileSize)
                                        local tileScreenY = chunkScreenY + (y * tileSize)
                                        
                                        -- Draw tile
                                        local color = getTileColor(tile.type)
                                        love.graphics.setColor(color[1], color[2], color[3], 0.7) -- More transparent for menu background
                                        love.graphics.rectangle("fill", 
                                            tileScreenX, tileScreenY, 
                                            tileSize - 1, tileSize - 1)
                                    end
                                end
                            end
                        end
                        
                        -- Highlight hub chunk
                        if chunkX == 0 and chunkY == 0 and chunk.isHub then
                            love.graphics.setColor(0.7, 0.7, 0.7, 0.2)
                            love.graphics.rectangle("fill", 
                                chunkScreenX, chunkScreenY, 
                                chunkPixelWidth, chunkPixelHeight)
                            
                            love.graphics.setColor(0.9, 0.9, 0.2, 0.3)
                            love.graphics.rectangle("line", 
                                chunkScreenX, chunkScreenY, 
                                chunkPixelWidth, chunkPixelHeight)
                        end
                        
                        -- Draw biome name for hub
                        if chunkX == 0 and chunkY == 0 then
                            love.graphics.setColor(1, 1, 1, 0.5)
                            love.graphics.print("Hub World", 
                                chunkScreenX + chunkPixelWidth/2 - 30, 
                                chunkScreenY + chunkPixelHeight/2)
                        end
                    end
                end
            end
            
            -- Draw grid to give a sense of space
            love.graphics.setColor(0.3, 0.3, 0.3, 0.1)
            local gridSize = tileSize * 10  -- Grid every 10 tiles
            local gridDrawDistance = 10
            
            -- Draw vertical lines
            for x = -gridDrawDistance, gridDrawDistance do
                local gridX = x * gridSize
                love.graphics.line(
                    gridX, -gridDrawDistance * gridSize,
                    gridX, gridDrawDistance * gridSize
                )
            end
            
            -- Draw horizontal lines
            for y = -gridDrawDistance, gridDrawDistance do
                local gridY = y * gridSize
                love.graphics.line(
                    -gridDrawDistance * gridSize, gridY,
                    gridDrawDistance * gridSize, gridY
                )
            end
            
            -- Draw origin marker
            love.graphics.setColor(1, 1, 1, 0.4)
            love.graphics.circle("fill", 0, 0, 8)
            love.graphics.setColor(0, 0, 0, 0.4)
            love.graphics.circle("line", 0, 0, 8)
        end
    else
        -- During gameplay, center on the player
        if Game.player and Game.player.position then
            local playerX = Game.player.position.x
            local playerY = Game.player.position.y
            
            -- Calculate screen center
            local screenCenterX = Settings.display.screenWidth / 2
            local screenCenterY = Settings.display.screenHeight / 2
            
            -- Apply camera transform - center on player
            love.graphics.translate(screenCenterX - playerX, screenCenterY - playerY)
            
            -- Draw world if enabled
            if Settings and Settings.debug and Settings.debug.modules and 
               Settings.debug.modules.world and Game and Game.world then
                
                -- Create placeholder draw function for the world
                if Game.world.chunkSystem then
                    -- Determine visible area based on screen size
                    local tileSize = 32  -- Make tiles larger for better visibility
                    local visibleWidth = Settings.display.screenWidth / tileSize
                    local visibleHeight = Settings.display.screenHeight / tileSize
                    
                    -- Calculate chunk range to draw (add padding for smooth scrolling)
                    local chunkSize = Game.chunkSize or 32
                    local playerChunkX = math.floor(playerX / (chunkSize * tileSize))
                    local playerChunkY = math.floor(playerY / (chunkSize * tileSize))
                    
                    -- Determine chunks to render
                    local chunkRenderDistance = 2
                    local minChunkX = playerChunkX - chunkRenderDistance
                    local maxChunkX = playerChunkX + chunkRenderDistance
                    local minChunkY = playerChunkY - chunkRenderDistance
                    local maxChunkY = playerChunkY + chunkRenderDistance
                    
                    -- Manually render visible chunks
                    for chunkX = minChunkX, maxChunkX do
                        for chunkY = minChunkY, maxChunkY do
                            local chunk = Game.world.chunkSystem:getChunkAt(chunkX, chunkY)
                            
                            if chunk then
                                -- Draw chunk boundary
                                local chunkScreenX = chunkX * chunkSize * tileSize
                                local chunkScreenY = chunkY * chunkSize * tileSize
                                local chunkPixelWidth = chunkSize * tileSize
                                local chunkPixelHeight = chunkSize * tileSize
                                
                                -- Draw chunk background
                                love.graphics.setColor(0.1, 0.1, 0.1, 0.3)
                                love.graphics.rectangle("fill", 
                                    chunkScreenX, chunkScreenY, 
                                    chunkPixelWidth, chunkPixelHeight)
                                
                                -- Draw chunk border
                                love.graphics.setColor(0.5, 0.5, 0.5, 0.5)
                                love.graphics.rectangle("line", 
                                    chunkScreenX, chunkScreenY, 
                                    chunkPixelWidth, chunkPixelHeight)
                                
                                -- Draw chunk info
                                love.graphics.setColor(1, 1, 1, 0.8)
                                love.graphics.print("Chunk: " .. chunkX .. "," .. chunkY, 
                                    chunkScreenX + 10, chunkScreenY + 10)
                                
                                if chunk.biome then
                                    love.graphics.print("Biome: " .. chunk.biome, 
                                        chunkScreenX + 10, chunkScreenY + 30)
                                end
                                
                                if chunk.isHub then
                                    love.graphics.print("HUB CHUNK", 
                                        chunkScreenX + 10, chunkScreenY + 50)
                                end
                                
                                -- Draw tiles if they exist
                                if chunk.tiles then
                                    -- Loop through all tiles based on chunk size
                                    for x = 0, chunkSize - 1 do
                                        for y = 0, chunkSize - 1 do
                                            local tileIndex = y * chunkSize + x + 1  -- +1 for Lua's 1-based indexing
                                            local tile = chunk.tiles[tileIndex]
                                            
                                            if tile then
                                                -- Calculate screen position
                                                local tileScreenX = chunkScreenX + (x * tileSize)
                                                local tileScreenY = chunkScreenY + (y * tileSize)
                                                
                                                -- Draw tile
                                                local color = getTileColor(tile.type)
                                                love.graphics.setColor(color[1], color[2], color[3], 0.8)
                                                love.graphics.rectangle("fill", 
                                                    tileScreenX, tileScreenY, 
                                                    tileSize - 1, tileSize - 1)
                                                
                                                -- Mark safe tiles
                                                if tile.isSafe then
                                                    love.graphics.setColor(1, 1, 0, 0.4)
                                                    love.graphics.rectangle("line", 
                                                        tileScreenX, tileScreenY, 
                                                        tileSize - 1, tileSize - 1)
                                                end
                                                
                                                -- Small debug text showing tile type (when zoomed in enough)
                                                if tileSize > 20 then
                                                    love.graphics.setColor(0, 0, 0, 0.6)
                                                    love.graphics.print(tile.type:sub(1,4), 
                                                        tileScreenX + 2, tileScreenY + 2, 0, 0.5)
                                                end
                                            end
                                        end
                                    end
                                end
                            end
                        end
                    end
                    
                    -- Draw grid lines for reference
                    love.graphics.setColor(0.3, 0.3, 0.3, 0.2)
                    local gridSize = tileSize * 10  -- Show grid lines every 10 tiles
                    local gridDrawDistance = 20
                    
                    -- Draw vertical lines
                    for x = -gridDrawDistance, gridDrawDistance do
                        local gridX = x * gridSize
                        love.graphics.line(
                            gridX, -gridDrawDistance * gridSize,
                            gridX, gridDrawDistance * gridSize
                        )
                    end
                    
                    -- Draw horizontal lines
                    for y = -gridDrawDistance, gridDrawDistance do
                        local gridY = y * gridSize
                        love.graphics.line(
                            -gridDrawDistance * gridSize, gridY,
                            gridDrawDistance * gridSize, gridY
                        )
                    end
                    
                    -- Draw world origin marker
                    love.graphics.setColor(1, 1, 1, 0.7)
                    love.graphics.circle("fill", 0, 0, 10)
                    love.graphics.setColor(0, 0, 0, 0.7)
                    love.graphics.circle("line", 0, 0, 10)
                    love.graphics.setColor(1, 1, 1, 0.7)
                    love.graphics.print("Origin (0,0)", 15, -5)
                end
            end
            
            -- Draw entities if enabled
            if Settings and Settings.debug and Settings.debug.modules and 
            Settings.debug.modules.entities and Game and Game.world and 
            Game.world.entityManager then
                -- Draw entities (non-player)
                if Game.world.entityManager.entities then
                    for _, entity in pairs(Game.world.entityManager.entities) do
                        if entity and entity.position and entity.type and entity ~= Game.player then
                            -- Apply color based on entity type
                            local color = getEntityColor(entity.type)
                            love.graphics.setColor(color)
                            
                            -- Draw a circle for the entity
                            local radius = 16
                            love.graphics.circle("fill", entity.position.x, entity.position.y, radius)
                            
                            -- Draw entity name or type above it
                            love.graphics.setColor(1, 1, 1)
                            love.graphics.print(entity.name or entity.type,
                                entity.position.x - 20, entity.position.y - 25)
                        end
                    end
                end
            end
            
            -- Draw player if enabled
            if Settings and Settings.debug and Settings.debug.modules and 
            Settings.debug.modules.player and Game and Game.player then
                if Game.player.position then
                    -- Apply color for player
                    local color = getEntityColor("player")
                    love.graphics.setColor(color)
                    
                    -- Draw a circle for the player with slightly larger radius
                    local radius = 20
                    love.graphics.circle("fill", playerX, playerY, radius)
                    
                    -- Draw a direction indicator (small line showing facing direction)
                    local dirX = 0
                    local dirY = -1 -- Default facing up
                    if Game.player.facing then
                        if Game.player.facing == "right" then dirX, dirY = 1, 0
                        elseif Game.player.facing == "left" then dirX, dirY = -1, 0
                        elseif Game.player.facing == "down" then dirX, dirY = 0, 1
                        end
                    end
                    
                    love.graphics.setColor(1, 0, 0)
                    love.graphics.line(
                        playerX, 
                        playerY, 
                        playerX + dirX * radius, 
                        playerY + dirY * radius
                    )
                    
                    -- Draw player name above
                    love.graphics.setColor(1, 1, 1)
                    love.graphics.print(Game.player.name or "Player",
                        playerX - 30, playerY - 40)
                end
            end
        end
    end
    
    -- End camera transform
    love.graphics.pop()
    
    -- Draw UI elements (always in screen space, not affected by camera)
    if Game and Game.uiSystem then
        Game.uiSystem:draw()
    end
    
    -- Draw player position info (only when playing, not in main menu)
    if Game.player and Game.player.position and not Game.uiSystem.mainMenuActive then
        love.graphics.setColor(1, 1, 1)
        local posInfo = string.format("Position: %.1f, %.1f", Game.player.position.x, Game.player.position.y)
        love.graphics.print(posInfo, 10, 30)
        
        -- Get current chunk coordinates
        local tileSize = 32
        local chunkSize = Game.chunkSize or 32
        local chunkX = math.floor(Game.player.position.x / (chunkSize * tileSize))
        local chunkY = math.floor(Game.player.position.y / (chunkSize * tileSize))
        local chunkInfo = string.format("Chunk: %d, %d", chunkX, chunkY)
        love.graphics.print(chunkInfo, 10, 50)
        
        -- Show controls
        love.graphics.print("WASD: Move | ESC: Menu | F1: Debug Menu", 10, 70)
    end
    
    -- Draw FPS if enabled
    if Settings and Settings.display and Settings.display.showFPS then
        love.graphics.setColor(1, 1, 1)
        love.graphics.print("FPS: " .. love.timer.getFPS(), 10, 10)
    end
    
    -- Draw version info in both main menu and gameplay
    love.graphics.setColor(1, 1, 1, 0.7)
    love.graphics.print("Version: Alpha 0.1", 10, Settings.display.screenHeight - 30)
end

-- Helper function to get tile color by type
function getTileColor(tileType)
    if not tileType then
        return {0.5, 0.5, 0.5} -- Default gray for nil tileType
    end
    
    local colors = {
        grass = {0.2, 0.8, 0.2},
        water = {0.2, 0.2, 0.8},
        desert_sand = {0.8, 0.8, 0.2},
        forest_floor = {0.2, 0.6, 0.2},
        mountain = {0.5, 0.5, 0.5},
        ruins = {0.6, 0.6, 0.6},
        crystal_formation = {0.8, 0.2, 0.8},
        void = {0.1, 0.1, 0.1},
        hub_floor = {0.7, 0.7, 0.7},
        hub_grass = {0.5, 0.8, 0.5},
        hub_path = {0.8, 0.7, 0.6},
        sanctuary_stone = {1.0, 1.0, 0.7},
        ocean_deep = {0.0, 0.0, 0.6},
        ocean_shallow = {0.0, 0.4, 0.8},
        coral_reef = {1.0, 0.5, 0.5},
        stone_floor = {0.5, 0.5, 0.5},
        lava = {1.0, 0.3, 0.0},
        mana_pool = {0.3, 0.3, 1.0},
        placeholder = {0.7, 0.7, 0.7} -- Add color for placeholder tiles
    }
    
    return colors[tileType] or {0.5, 0.5, 0.5}
end

-- Helper function to get entity color by type
function getEntityColor(entityType)
    if not entityType then
        return {1, 0, 1} -- Default magenta for nil entityType
    end
    
    local colors = {
        player = {1, 1, 1},
        npc = {0, 1, 1},
        deer = {0.8, 0.6, 0.4},
        rabbit = {0.8, 0.8, 0.7},
        wolf = {0.5, 0.5, 0.7},
        fox = {0.9, 0.4, 0.1},
        bear = {0.6, 0.4, 0.2},
        raccoon = {0.5, 0.5, 0.6},
        squirrel = {0.7, 0.5, 0.3},
        bird = {0.3, 0.7, 0.9},
        horse = {0.6, 0.5, 0.4},
        bandit = {1, 0.3, 0.3},
        fish = {0.3, 0.6, 0.9},
        crystal_lizard = {0.7, 0.8, 1.0},
        mana_sprite = {0.5, 0.5, 1.0},
        ancient_treant = {0.5, 0.8, 0.3},
        forest_guardian = {0.3, 0.9, 0.4},
        mystic_deer = {0.9, 0.8, 1.0},
        desert_lizard = {0.8, 0.7, 0.4},
        sand_wurm = {0.8, 0.6, 0.4},
        crystal_golem = {0.6, 0.8, 0.9}
    }
    
    return colors[entityType] or {1, 0, 1}  -- Default to magenta for unknown entities
end

-- Input callback functions
function love.keypressed(key, scancode, isrepeat)
    -- Handle text input for character name
    if Game.uiSystem.editingName then
        if key == "backspace" then
            -- Remove the last character
            local byteoffset = utf8.offset(Game.uiSystem.tempName, -1)
            if byteoffset then
                Game.uiSystem.tempName = string.sub(Game.uiSystem.tempName, 1, byteoffset - 1)
            end
        elseif key == "return" or key == "escape" then
            -- Confirm or cancel name edit
            if key == "return" and string.len(Game.uiSystem.tempName) > 0 then
                Game.uiSystem.characterName = Game.uiSystem.tempName
                Game.uiSystem:showMessage("Name set to: " .. Game.uiSystem.characterName)
            end
            Game.uiSystem.editingName = false
            love.keyboard.setTextInput(false)
        end
        return
    end
    
    -- Store key press
    Game.inputManager.keyboard.pressed[key] = true
    
    -- Check for global controls
    if key == "escape" then
        -- Open menu instead of toggling pause
        if Game.uiSystem.activeScreen then
            Game.uiSystem:closeScreen()
        else
            -- Open main menu
            Game.uiSystem:openCharacterScreen(player)
            Game.uiSystem:showMessage("Menu Opened")
        end
    elseif key == "p" then
        -- Move pause functionality to P key
        Game.isPaused = not Game.isPaused
        
        if Game.isPaused then
            Game.uiSystem:showMessage("Game Paused")
        else
            Game.uiSystem:showMessage("Game Resumed")
        end
    elseif key == "f" then
        -- Toggle fullscreen
        Settings.display.fullscreen = not Settings.display.fullscreen
        love.window.setFullscreen(Settings.display.fullscreen)
    elseif key == "f3" then
        -- Toggle FPS display
        Settings.display.showFPS = not Settings.display.showFPS
    elseif key == "f1" then
        -- Toggle debug menu
        Game.uiSystem.debugMenuVisible = not Game.uiSystem.debugMenuVisible
        Game.uiSystem:showMessage("Debug menu " .. (Game.uiSystem.debugMenuVisible and "shown" or "hidden"))
    end
end

function love.keyreleased(key, scancode)
    -- Handle key releases if needed
end

function love.mousepressed(x, y, button)
    if button == 1 then  -- Left click
        -- Check if main menu is active
        if Game.uiSystem.mainMenuActive then
            Game.uiSystem:handleMainMenuClick(x, y)
            return
        end
        
        -- Handle debug menu clicks first
        Game.uiSystem:handleDebugMenuClick(x, y)
        
        -- Only handle other clicks if debug menu is not visible
        if not Game.uiSystem.debugMenuVisible then
            -- Handle other UI clicks here
            if Game.uiSystem.activeScreen then
                Game.uiSystem:handleScreenClick(Game.uiSystem.activeScreen, x, y)
            end
        end
    end
end

function love.mousereleased(x, y, button, istouch, presses)
    -- Handle mouse button releases
    Game.inputManager.mouse.buttons[button] = false
end

function love.wheelmoved(x, y)
    -- Store mouse wheel movement
    Game.inputManager.mouse.wheel = y
    
    -- Use wheel for zooming
    if y > 0 then
        -- Zoom in
        Game.viewportManager:setZoom(Game.viewportManager.zoomLevel + 0.1)
    elseif y < 0 then
        -- Zoom out
        Game.viewportManager:setZoom(Game.viewportManager.zoomLevel - 0.1)
    end
end

function love.joystickadded(joystick)
    -- Notify of new controller
    print("Controller connected: " .. joystick:getName())
    
    -- Update input manager controller list
    Game.inputManager:update()
    
    -- Show message
    if Game.uiSystem then
        Game.uiSystem:showMessage("Controller connected: " .. joystick:getName())
    end
end

function love.joystickremoved(joystick)
    -- Notify of controller disconnect
    print("Controller disconnected: " .. joystick:getName())
    
    -- Update input manager controller list
    Game.inputManager:update()
    
    -- Show message
    if Game.uiSystem then
        Game.uiSystem:showMessage("Controller disconnected: " .. joystick:getName())
    end
end

function love.resize(w, h)
    -- Update screen dimensions
    Settings.display.screenWidth = w
    Settings.display.screenHeight = h
    
    -- Resize viewport manager
    Game.viewportManager.screenWidth = w
    Game.viewportManager.screenHeight = h
    Game.viewportManager:updateViewportLayout()
end

function love.quit()
    -- Perform any cleanup before exiting
    print("Game shutting down...")
    
    -- Save any unsaved data
    -- (You would add your save system calls here)
    
    -- Shut down game systems
    if Game.shutdown then
        Game.shutdown()
    end
    
    return false  -- Allow the game to close
end

-- Add textinput handler
function love.textinput(text)
    if Game.uiSystem.editingName then
        -- Only allow alphanumeric and some special characters
        if string.match(text, "[%w%s_%-]") then
            Game.uiSystem.tempName = Game.uiSystem.tempName .. text
        end
    end
end