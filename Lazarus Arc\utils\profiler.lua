-- utils/profiler.lua
-- Performance profiler with visual graph display

local Profiler = {}

-- Initialize profiler
function Profiler.init()
    Profiler.enabled = false
    Profiler.maxSamples = 120 -- 2 seconds at 60 FPS
    Profiler.samples = {
        frameTime = {},
        memory = {},
        drawCalls = {},
        entities = {},
        tiles = {}
    }
    Profiler.currentFrame = 0
    Profiler.lastUpdateTime = 0
    Profiler.updateInterval = 1/60 -- Update 60 times per second
    
    -- Graph display settings
    Profiler.graphWidth = 300
    Profiler.graphHeight = 150
    Profiler.graphX = 10
    Profiler.graphY = 100
    Profiler.backgroundColor = {0, 0, 0, 0.7}
    Profiler.gridColor = {0.3, 0.3, 0.3, 0.8}
    Profiler.textColor = {1, 1, 1, 1}
    
    -- Performance thresholds
    Profiler.thresholds = {
        frameTime = {
            good = 16.67, -- 60 FPS
            warning = 33.33, -- 30 FPS
            critical = 50 -- 20 FPS
        },
        memory = {
            good = 50, -- 50 MB
            warning = 100, -- 100 MB
            critical = 200 -- 200 MB
        }
    }
    
    -- Colors for different metrics
    Profiler.colors = {
        frameTime = {0.2, 0.8, 0.2, 1}, -- Green
        memory = {0.2, 0.2, 0.8, 1}, -- Blue
        drawCalls = {0.8, 0.8, 0.2, 1}, -- Yellow
        entities = {0.8, 0.2, 0.8, 1}, -- Magenta
        tiles = {0.8, 0.4, 0.2, 1} -- Orange
    }
end

-- Start profiling a frame
function Profiler.startFrame()
    if not Profiler.enabled then return end
    
    Profiler.frameStartTime = love.timer.getTime()
    Profiler.currentFrame = Profiler.currentFrame + 1
end

-- End profiling a frame
function Profiler.endFrame()
    if not Profiler.enabled then return end
    
    local frameTime = (love.timer.getTime() - Profiler.frameStartTime) * 1000 -- Convert to milliseconds
    local memory = collectgarbage("count") / 1024 -- Convert to MB
    
    -- Add samples
    Profiler.addSample("frameTime", frameTime)
    Profiler.addSample("memory", memory)
    
    -- Get additional metrics if available
    local stats = love.graphics.getStats()
    if stats then
        Profiler.addSample("drawCalls", stats.drawcalls or 0)
    end
end

-- Add a sample to the profiler
function Profiler.addSample(metric, value)
    if not Profiler.samples[metric] then
        Profiler.samples[metric] = {}
    end
    
    table.insert(Profiler.samples[metric], value)
    
    -- Remove old samples
    if #Profiler.samples[metric] > Profiler.maxSamples then
        table.remove(Profiler.samples[metric], 1)
    end
end

-- Get average value for a metric
function Profiler.getAverage(metric)
    local samples = Profiler.samples[metric]
    if not samples or #samples == 0 then return 0 end
    
    local sum = 0
    for _, value in ipairs(samples) do
        sum = sum + value
    end
    
    return sum / #samples
end

-- Get maximum value for a metric
function Profiler.getMax(metric)
    local samples = Profiler.samples[metric]
    if not samples or #samples == 0 then return 0 end
    
    local max = samples[1]
    for _, value in ipairs(samples) do
        if value > max then
            max = value
        end
    end
    
    return max
end

-- Draw the profiler graph
function Profiler.draw()
    if not Profiler.enabled then return end
    
    love.graphics.push()
    
    -- Draw background
    love.graphics.setColor(Profiler.backgroundColor)
    love.graphics.rectangle("fill", Profiler.graphX, Profiler.graphY, Profiler.graphWidth, Profiler.graphHeight)
    
    -- Draw grid
    love.graphics.setColor(Profiler.gridColor)
    love.graphics.setLineWidth(1)
    
    -- Vertical grid lines
    for i = 0, 10 do
        local x = Profiler.graphX + (i / 10) * Profiler.graphWidth
        love.graphics.line(x, Profiler.graphY, x, Profiler.graphY + Profiler.graphHeight)
    end
    
    -- Horizontal grid lines
    for i = 0, 5 do
        local y = Profiler.graphY + (i / 5) * Profiler.graphHeight
        love.graphics.line(Profiler.graphX, y, Profiler.graphX + Profiler.graphWidth, y)
    end
    
    -- Draw frame time graph
    Profiler.drawMetricGraph("frameTime", Profiler.thresholds.frameTime.critical)
    
    -- Draw memory graph (scaled)
    Profiler.drawMetricGraph("memory", Profiler.thresholds.memory.critical)
    
    -- Draw statistics text
    Profiler.drawStats()
    
    love.graphics.pop()
end

-- Draw a graph for a specific metric
function Profiler.drawMetricGraph(metric, maxValue)
    local samples = Profiler.samples[metric]
    if not samples or #samples < 2 then return end
    
    love.graphics.setColor(Profiler.colors[metric])
    love.graphics.setLineWidth(2)
    
    local points = {}
    for i, value in ipairs(samples) do
        local x = Profiler.graphX + ((i - 1) / (Profiler.maxSamples - 1)) * Profiler.graphWidth
        local y = Profiler.graphY + Profiler.graphHeight - (value / maxValue) * Profiler.graphHeight
        
        -- Clamp y to graph bounds
        y = math.max(Profiler.graphY, math.min(Profiler.graphY + Profiler.graphHeight, y))
        
        table.insert(points, x)
        table.insert(points, y)
    end
    
    if #points >= 4 then
        love.graphics.line(points)
    end
end

-- Draw statistics text
function Profiler.drawStats()
    love.graphics.setColor(Profiler.textColor)
    love.graphics.setFont(love.graphics.getFont())
    
    local textY = Profiler.graphY + Profiler.graphHeight + 10
    local lineHeight = 15
    
    -- Frame time stats
    local avgFrameTime = Profiler.getAverage("frameTime")
    local maxFrameTime = Profiler.getMax("frameTime")
    local fps = avgFrameTime > 0 and (1000 / avgFrameTime) or 0
    
    love.graphics.print(string.format("FPS: %.1f (avg: %.2fms, max: %.2fms)", fps, avgFrameTime, maxFrameTime), 
                       Profiler.graphX, textY)
    
    -- Memory stats
    local avgMemory = Profiler.getAverage("memory")
    local maxMemory = Profiler.getMax("memory")
    
    love.graphics.print(string.format("Memory: %.1fMB (max: %.1fMB)", avgMemory, maxMemory), 
                       Profiler.graphX, textY + lineHeight)
    
    -- Draw calls if available
    local avgDrawCalls = Profiler.getAverage("drawCalls")
    if avgDrawCalls > 0 then
        love.graphics.print(string.format("Draw Calls: %.0f", avgDrawCalls), 
                           Profiler.graphX, textY + lineHeight * 2)
    end
end

-- Toggle profiler
function Profiler.toggle()
    Profiler.enabled = not Profiler.enabled
    if Profiler.enabled then
        print("Profiler enabled")
    else
        print("Profiler disabled")
    end
    return Profiler.enabled
end

-- Set profiler position
function Profiler.setPosition(x, y)
    Profiler.graphX = x
    Profiler.graphY = y
end

-- Set profiler size
function Profiler.setSize(width, height)
    Profiler.graphWidth = width
    Profiler.graphHeight = height
end

return Profiler
