-- biomes/kingdom_region.lua
local KingdomRegionBiome = {
    id = "kingdom_region",
    name = "Kingdom Region",
    description = "A generalized region representing the varied territory of a kingdom, blending plains, forests, hills, rivers, and settled areas.",

    -- Environmental factors (Balanced for a typical kingdom territory)
    environment = {
        humidity = 0.6,
        temperature = 0.5,
        sunlight = 0.7,
        windStrength = 0.4,
        airQuality = 0.8,
        noiseLevel = 0.3
    },

    -- Core tile types used in this biome
    primaryTiles = {"grass", "dirt", "stone"},
    secondaryTiles = {"forest", "hills", "river"},
    rareTiles = {"ancient_ruins", "magic_spring", "crystal_formation"},

    -- Default proportions (Adjustable through variants)
    tileDistribution = {
        grass = 0.4,
        dirt = 0.2,
        stone = 0.1,
        forest = 0.15,
        hills = 0.1,
        river = 0.03,
        ancient_ruins = 0.01,
        magic_spring = 0.005,
        crystal_formation = 0.005
    },

    -- Entities common to this biome
    commonEntities = {
        "deer", "rabbit", "bird", "fox", "farmer"
    },
    
    uncommonEntities = {
        "wolf", "bear", "merchant", "traveler", "guard"
    },
    
    rareEntities = {
        "royal_guard", "mystic_wanderer", "ancient_spirit"
    },

    -- Biome variants for diverse generation
    variants = {
        fertile = {
            name = "Fertile Kingdom Region",
            tileAdjustments = {
                grass = 0.5,
                forest = 0.2,
                river = 0.05
            },
            entityAdjustments = {
                farmer = 1.5,
                merchant = 1.3
            }
        },
        wild = {
            name = "Wild Kingdom Region",
            tileAdjustments = {
                forest = 0.3,
                hills = 0.2,
                grass = 0.3
            },
            entityAdjustments = {
                wolf = 1.5,
                bear = 1.2
            }
        },
        ancient = {
            name = "Ancient Kingdom Region",
            tileAdjustments = {
                ancient_ruins = 0.1,
                magic_spring = 0.05,
                crystal_formation = 0.05
            },
            entityAdjustments = {
                ancient_spirit = 1.5,
                mystic_wanderer = 1.2
            }
        }
    },

    -- Structures that can generate in this biome
    structures = {
        {
            name = "farmstead",
            chance = 0.2,
            entities = {"farmer", "crop_field", "barn"}
        },
        {
            name = "trading_post",
            chance = 0.15,
            entities = {"merchant", "guard", "storage"}
        },
        {
            name = "watchtower",
            chance = 0.1,
            entities = {"guard", "signal_fire"}
        },
        {
            name = "ancient_shrine",
            chance = 0.05,
            requiresVariant = "ancient",
            entities = {"ancient_spirit", "magic_spring"}
        }
    },

    -- Weather patterns
    weather = {
        clear = 0.4,
        cloudy = 0.3,
        rain = 0.2,
        storm = 0.1
    }
}

-- Generation algorithm
KingdomRegionBiome.generate = function(chunk, world, variant)
    print("Generating " .. (variant and KingdomRegionBiome.variants[variant].name or "Kingdom Region") .. " biome")
    
    -- Initialize 2D tiles array if not exists
    chunk.tiles = chunk.tiles or {}
    for x = 0, world.CHUNK_SIZE - 1 do
        chunk.tiles[x] = chunk.tiles[x] or {}
    end

    -- Generate tiles
    for x = 0, world.CHUNK_SIZE - 1 do
        for y = 0, world.CHUNK_SIZE - 1 do
            local worldX = chunk.x * world.CHUNK_SIZE + x
            local worldY = chunk.y * world.CHUNK_SIZE + y

            -- Create tile
            local tile = {
                type = "grass", -- Default type
                x = x, y = y,
                variant = math.random(1, 3),
                passable = true
            }

            chunk.tiles[x][y] = tile
        end
    end

    -- Call helper functions
    KingdomRegionBiome.generateFeatures(chunk, world, variant)
    KingdomRegionBiome.populateEntities(chunk, world, variant)
    KingdomRegionBiome.generateStructures(chunk, world, variant)
    KingdomRegionBiome.postProcess(chunk, world, variant)
end

-- Helper functions
KingdomRegionBiome.generateFeatures = function(chunk, world, variant)
    print("Generating Kingdom Region features...")
    -- Feature generation logic here
end

KingdomRegionBiome.populateEntities = function(chunk, world, variant)
    print("Populating Kingdom Region entities...")
    local function spawnEntity(entityType, count)
        for i = 1, count do
            local x = math.random(0, world.CHUNK_SIZE - 1)
            local y = math.random(0, world.CHUNK_SIZE - 1)
            local tile = chunk.tiles[x][y]
            if tile and tile.passable then
                world.entitySystem:addEntity(entityType, chunk.x * world.CHUNK_SIZE + x, chunk.y * world.CHUNK_SIZE + y)
            end
        end
    end

    -- Spawn entities based on lists
    for _, et in ipairs(KingdomRegionBiome.commonEntities) do spawnEntity(et, math.random(2,4)) end
    for _, et in ipairs(KingdomRegionBiome.uncommonEntities) do spawnEntity(et, math.random(1,2)) end
    for _, et in ipairs(KingdomRegionBiome.rareEntities) do if math.random() < 0.1 then spawnEntity(et, 1) end end
end

KingdomRegionBiome.generateStructures = function(chunk, world, variant)
    print("Generating Kingdom Region structures...")
    for _, structure in ipairs(KingdomRegionBiome.structures) do
        if math.random() < structure.chance then
            if not structure.requiresVariant or structure.requiresVariant == variant then
                local x = math.random(0, world.CHUNK_SIZE - 1)
                local y = math.random(0, world.CHUNK_SIZE - 1)
                local tile = chunk.tiles[x][y]
                if tile and tile.passable then
                    -- Structure placement logic here
                end
            end
        end
    end
end

KingdomRegionBiome.postProcess = function(chunk, world, variant)
    print("Post-processing Kingdom Region chunk...")
    -- Post-processing logic here
end

-- Initialize the biome module
KingdomRegionBiome.init = function(worldCore)
    print("Kingdom Region biome module initialized")
    
    -- Store reference to WorldCore for later use
    KingdomRegionBiome.worldCore = worldCore
    
    -- Don't try to register generators at init time
    -- Instead, we'll register them when a world is created
    
    print("Kingdom Region biome registered successfully")
end

-- Add a function to register generators with a specific world instance
KingdomRegionBiome.registerWithWorld = function(world)
    print("Registering Kingdom Region biome generators with world")
    
    if not world or not world.chunkSystem then
        print("Warning: Cannot register Kingdom Region biome - world or chunkSystem is nil")
        return false
    end
    
    -- Register generation algorithms with the world
    world.chunkSystem:registerGenerator("kingdom_region", KingdomRegionBiome.generate)
    
    -- Register biome variants
    for variantId, variant in pairs(KingdomRegionBiome.variants) do
        world.chunkSystem:registerGenerator("kingdom_region_" .. variantId, function(chunk, world)
            KingdomRegionBiome.generate(chunk, world, variantId)
            KingdomRegionBiome.postProcess(chunk, world, variantId)
        end)
    end
    
    return true
end

return KingdomRegionBiome 