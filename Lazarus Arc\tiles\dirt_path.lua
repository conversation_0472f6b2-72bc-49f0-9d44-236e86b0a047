-- tiles/dirt_path.lua
local DirtPathTile = {
    id = "dirt_path",
    name = "Dirt Path",
    passable = true,
    variants = 4, -- Different path appearances (straight, curved, etc.)
    
    -- Which entities can spawn naturally on this tile
    spawns = {"small_rock", "footprint", "lost_coin", "worm"},
    
    -- Movement modifiers
    movementSpeed = 1.2, -- Slightly faster movement on paths
    
    -- Interaction function when player activates this tile
    interact = function(tile, entity)
        -- Small chance to find a lost item
        if math.random() < 0.05 then
            local items = {"coin", "button", "rusty_key", "map_scrap"}
            local foundItem = items[math.random(#items)]
            
            return {
                success = true,
                message = "You found a " .. foundItem .. " on the path!",
                item = foundItem
            }
        end
        
        -- Examine footprints
        if tile.hasFootprints and math.random() < 0.3 then
            local directions = {"north", "east", "south", "west"}
            local direction = directions[math.random(#directions)]
            
            return {
                success = true,
                message = "You notice footprints leading " .. direction .. ".",
                effects = {
                    {type = "reveal_direction", direction = direction}
                }
            }
        end
        
        return {
            success = false,
            message = "A well-worn dirt path. Years of travel have compacted the soil."
        }
    end,
    
    -- How this tile changes with weather
    weatherEffects = {
        rain = function(tile)
            -- Path gets muddy in rain
            tile.isMuddy = true
            tile.movementSpeed = 0.8 -- Slower when muddy
            
            -- Might show footprints better
            tile.hasFootprints = math.random() < 0.3
            
            return nil
        end,
        
        sun = function(tile)
            -- Path dries out in sun
            if tile.isMuddy then
                tile.isMuddy = false
                tile.movementSpeed = 1.2 -- Normal speed when dry
            end
            
            -- Footprints fade in dry weather
            if tile.hasFootprints and math.random() < 0.5 then
                tile.hasFootprints = false
            end
            
            return nil
        end,
        
        snow = function(tile)
            -- Snow covers the path
            tile.isSnowCovered = true
            tile.variant = 5 -- Snow-covered variant
            tile.movementSpeed = 0.9 -- Slightly slower in snow
            
            -- Fresh snow shows footprints clearly
            tile.hasFootprints = false -- Clear existing
            return nil
        end
    },
    
    -- Connect to other path types
    connects_to = {
        "dirt_path",
        "road",
        "grass",
        "stone_path"
    },
    
    -- Visual properties
    visual = {
        base_color = {0.6, 0.4, 0.2}, -- Brown
        variants = {
            {name = "straight", rotation = 0},
            {name = "curved", rotation = 0},
            {name = "t_junction", rotation = 0},
            {name = "crossroads", rotation = 0},
            {name = "snow_covered", base = "straight", overlay = "snow"}
        },
        -- Path should match up with connected tiles
        connection_rules = "path", -- Special connection type for paths
        ground_decals = true -- Can have footprints, etc.
    },
    
    -- Optional custom draw function for special effects
    draw = function(tile, x, y)
        -- Would draw the tile with appropriate effects
        -- like footprints, mud puddles, etc.
    end,
    
    -- Audio effects when walking on this tile
    footstepSound = "dirt_step",
    
    -- Path-specific function to determine if this connects to another tile
    shouldConnectTo = function(otherTileType)
        if otherTileType == "dirt_path" or 
           otherTileType == "road" or 
           otherTileType == "stone_path" then
            return true
        end
        return false
    end,
    
    -- Time-based effects
    timeEffects = {
        night = function(tile)
            -- Paths are harder to follow at night
            tile.visibility = 0.7
            return nil
        end,
        
        dawn = function(tile)
            -- Reset visibility in morning
            tile.visibility = 1.0
            return nil
        end
    }
}

-- Initialize function called when the module is loaded
function DirtPathTile.init(world)
    print("Dirt path tile module initialized")
    -- Register this tile type with any systems that need to know
    if world.systems and world.systems.pathfinding then
        world.systems.pathfinding:registerFastPath("dirt_path", DirtPathTile.movementSpeed)
    end
end

return DirtPathTile