-- tiles/desert_sand.lua
local DesertSandTile = {
    id = "desert_sand",
    name = "Desert Sand",
    passable = true,
    variants = 5, -- Different sand appearances (e.g., dunes, flat, rippled)

    -- Entities that can spawn in the desert sand
    spawns = {"scorpion", "desert_lizard", "vulture", "sand_worm_small", "skeletal_remains"}, -- Adapted from rocky_ground [cite: 130] and mountain [cite: 229] spawns

    -- Movement properties
    movementSpeed = 0.85, -- Slightly slower due to loose sand [Similar to ruins: 44, rocky_ground: 131]

    -- Desert-specific properties
    temperature = 35, -- Base temperature in Celsius (Can be modified by time/weather)
    dehydrationRisk = 0.3, -- Base risk factor per hour/turn (0-1)
    canContainOasis = false, -- Set during world generation if applicable
    
    -- Interaction function
    interact = function(tile, entity)
        -- Chance to find something buried
        if math.random() < 0.15 then
            local items = {
                {id = "skeletal_fragment", chance = 0.4},
                {id = "sunstone_shard", chance = 0.2}, -- Desert-specific resource
                {id = "lost_coin", chance = 0.2},
                {id = "water_flask_empty", chance = 0.1},
                {id = "ancient_artifact_piece", chance = 0.1} -- Connects to potential desert ruins
            }
            
            for _, item in ipairs(items) do
                if math.random() < item.chance then
                    return {
                        success = true,
                        message = "Digging in the sand, you uncover a " .. item.id .. ".",
                        effects = {
                            {type = "add_item", item = item.id, quantity = 1} -- Similar structure to ruins [cite: 48, 49]
                        }
                    }
                end
            end
        end

        -- Chance to experience a mirage (especially during high heat)
        if tile.temperature > 40 and math.random() < 0.2 then
            return {
                success = false, -- Not a tangible success
                message = "The intense heat creates a shimmering mirage in the distance. Is it water, or just a trick of the light?",
                effects = {
                    {type = "apply_status", status = "confused", duration = 5} -- Potential minor debuff
                }
            }
        end

        -- Check for dehydration
        if entity.needs and entity.needs.hydration and entity.needs.hydration < 30 then
             return {
                success = false,
                message = "The scorching sun beats down on the endless sand. You feel incredibly thirsty.",
                effects = {
                    {type = "increase_dehydration", amount = 5}
                }
            }
        end

        -- Standard examination
        return {
            success = false,
            message = "Endless dunes of fine sand stretch out before you, radiating heat." -- Basic description
        }
    end,

    -- Weather effects
    weatherEffects = {
        heat = function(tile) -- Critical effect for deserts
            tile.temperature = tile.temperature + 10 -- Increase temperature significantly
            tile.dehydrationRisk = tile.dehydrationRisk + 0.2
            -- Add visual heat haze effect
            return "visual_effect", "heat_haze" 
        end,
        
        sun = function(tile) -- Normal daytime effect
            tile.temperature = 35 -- Reset to base or slightly higher
            tile.dehydrationRisk = 0.3
             return "remove_visual_effect", "heat_haze"
        end,

        wind = function(tile) -- Sandstorm potential
             if math.random() < 0.3 then -- Chance of sandstorm in high wind
                tile.movementSpeed = tile.movementSpeed * 0.7
                tile.visibility = 0.5 -- Reduce visibility
                return "trigger_event", "sandstorm"
             end
             return nil
        end,

        rain = function(tile) -- Rare desert rain
            tile.temperature = tile.temperature - 15
            tile.dehydrationRisk = 0.1 -- Temporarily lower risk
            -- Chance to reveal something washed up
            if math.random() < 0.1 then
                 return "create_object", "desert_bloom_rare" -- Temporary rare flower
            end
            return nil
        end
    },

    -- Time effects
    timeEffects = {
        night = function(tile) -- Cooler at night
            tile.temperature = 15 -- Much cooler
            tile.dehydrationRisk = 0.05
            tile.ambientSound = "desert_night_wind"
            -- Potential for nocturnal creatures
             return "spawn_creature", "desert_fox_nocturnal"
        end,
        
        dawn = function(tile) -- Reset night effects
             tile.temperature = 25 -- Warming up
             tile.ambientSound = "desert_wind_gentle"
            return nil
        end
    },

    -- Visual properties
    visual = {
        base_color = {0.9, 0.8, 0.6}, -- Sandy yellow/beige
        variants = {
            {name = "dunes", height_variation = 0.5, pattern = "wave"},
            {name = "flat", height_variation = 0.1},
            {name = "rippled", pattern = "ripple", height_variation = 0.2},
            {name = "rocky_patches", overlay_objects={{name="small_rock", chance=0.3}}}, -- Similar to rocky_ground [cite: 139]
            {name = "sun_baked", color_shift = {0.05, -0.05, -0.1}} 
        },
        decoration_objects = {
            {name = "dry_bush", chance = 0.1},
            {name = "animal_bones", chance = 0.08},
            {name = "cactus_small", chance = 0.05} -- Similar to rocky_ground [cite: 130]
        },
        special_effects = {
             heat_haze = {type="distortion", intensity=0.3, speed=0.5, when="temperature>40"},
             sparkle = {chance=0.01, color={1.0, 0.9, 0.7}} -- Sun reflecting off sand grains
        },
         weather_overlays = {
             sand_drift = {effect="particle", density=0.4, when="sandstorm"} -- Overlay during sandstorm
        }
    },

    -- Audio properties
    ambientSound = "desert_wind_gentle", -- Default ambient sound
    footstepSound = "sand_step", -- Specific sound for walking on sand
     specialSounds = {
        {name = "scorpion_hiss", trigger = "nearby_entity", chance = 0.05},
        {name = "wind_howl", trigger = "weather_change", condition="windy", chance = 0.3}
    },

    -- Connections to other tiles
    connects_to = {
        "desert_sand", -- Connects to itself
        "oasis", -- Potential connection
        "rocky_ground", -- Transition tile [cite: 130]
        "desert_ruins", -- Possible future tile
        "savanna" -- Transition biome
    },

    -- Effects on entities that walk on this tile
    onWalk = function(tile, entity)
        -- Increase dehydration slightly
        if entity.needs and entity.needs.hydration then
             entity.needs.hydration = math.max(0, entity.needs.hydration - (tile.dehydrationRisk * 0.1)) -- Small effect per step
        end

        -- Slower movement sound
        return {
            sound = "sand_step",
            volume = 0.8,
            effects = { -- Apply minor fatigue due to heat/sand
                 {type="increase_fatigue", amount = 0.1 * (tile.temperature / 30)} -- Fatigue scales slightly with temp
            }
        }
    end,
     
    -- Effect when discovered
    onDiscover = function(tile, entity) -- Similar to road/ruins [cite: 39, 72]
        if entity.journal and entity.journal.addDiscovery then
             entity.journal.addDiscovery({
                type = "biome",
                name = "Desert",
                 location = {x = tile.x, y = tile.y}
            })
        end
        return {
            message = "You enter a vast, sandy desert.",
            effects = {
                {type = "reveal_map", radius = 2}
            }
        }
    end
}

function DesertSandTile.init(world)
    print("Desert sand tile module initialized")
    
    -- Register with ecosystem or weather system if applicable
    if world.systems and world.systems.weather then
        world.systems.weather:registerBiomeType("desert", {baseTemp=35, precipitationChance=0.05})
    end
    if world.systems and world.systems.ecosystem then
        world.systems.ecosystem:registerTerrain("desert_sand", {foodScarcity=0.8, waterScarcity=0.9})
    end
end

return DesertSandTile