-- weather/sandstorm.lua
-- Sandstorm weather pattern - reduces visibility and damages exposed equipment

local SandstormWeather = {}
SandstormWeather.__index = SandstormWeather

-- Basic properties
SandstormWeather.id = "sandstorm"
SandstormWeather.name = "Sandstorm"

-- Visual properties
SandstormWeather.visual = {
    skyColor = {r = 200, g = 180, b = 140}, -- Sandy brown
    sunIntensity = 0.6,
    cloudCoverage = 0.9,
    cloudColor = {r = 190, g = 170, b = 130}, -- Dust clouds
    ambientLightLevel = 0.7,
    sandColor = {r = 210, g = 190, b = 150} -- Flying sand
}

-- Environment modifiers
SandstormWeather.environment = {
    temperature = 1.2, -- Hotter due to friction
    visibility = 0.3, -- Severely reduced visibility
    humidity = 0.2, -- Very dry
    windStrength = 0.9, -- Strong wind
    sandDensity = 0.8 -- High sand concentration
}

-- Particle systems
SandstormWeather.particles = {
    sandParticles = {
        enabled = true,
        intensity = 0.9,
        color = {r = 210, g = 190, b = 150},
        size = {min = 1, max = 3},
        speed = {min = 200, max = 400}
    },
    dustClouds = {
        enabled = true,
        intensity = 0.7,
        color = {r = 190, g = 170, b = 130},
        size = {min = 5, max = 10},
        speed = {min = 100, max = 200}
    }
}

-- Sound effects
SandstormWeather.sounds = {
    ambient = "sandstorm_wind",
    impact = "sand_impact",
    wind = "strong_wind",
    volume = 0.8
}

-- Synth orchestra sound configurations
SandstormWeather.synthSounds = {
    sandstorm_wind = {
        notes = {110, 146.8, 196, 261.6}, -- A2, D3, G3, C4 - harsh, gritty wind
        durations = {2.0, 2.0, 2.0, 2.0},
        instrument = "accordion",
        volume = 0.35,
        vibrato = true,
        vibratoRate = 1.2,
        vibratoDepth = 0.25
    },
    sand_impact = {
        notes = {329.6, 392, 466.2}, -- E4, G4, A#4 - sand hitting surfaces
        durations = {0.2, 0.15, 0.1},
        instrument = "marimba",
        volume = 0.25,
        vibrato = false
    },
    strong_wind = {
        notes = {87.3, 110, 146.8}, -- F2, A2, D3 - powerful wind gusts
        durations = {1.5, 1.5, 1.5},
        instrument = "accordion",
        volume = 0.4,
        vibrato = true,
        vibratoRate = 2.0,
        vibratoDepth = 0.3
    }
}

-- Effects on game entities and tiles
SandstormWeather.effects = {
    -- Damage to exposed entities
    exposureDamage = 2, -- Damage per second
    
    -- Equipment wear
    equipmentWearRate = 1.5,
    
    -- Movement effects
    movementSpeedMultiplier = 0.7,
    
    -- Vision effects
    visionRange = 0.3,
    
    -- Mechanical effects
    mechanicalJamChance = 0.2, -- Chance for mechanical devices to jam
    
    -- Protection effects
    shieldEffectiveness = 0.8 -- Reduced shield effectiveness
}

-- Transition probabilities to other weather (per game hour)
SandstormWeather.transitions = {
    clear = 0.3,
    cloudy = 0.2,
    sunny = 0.2,
    -- Implied: 0.3 chance to stay sandstorm
}

-- Day/night cycle modifiers
SandstormWeather.timeModifiers = {
    dawn = {
        skyColor = {r = 210, g = 190, b = 160},
        ambientLightLevel = 0.6
    },
    day = {
        -- Uses default properties
    },
    dusk = {
        skyColor = {r = 200, g = 180, b = 150},
        ambientLightLevel = 0.5
    },
    night = {
        skyColor = {r = 100, g = 90, b = 70},
        ambientLightLevel = 0.3
    }
}

-- Helper function to deep copy a table
local function deepCopy(orig)
    local copy
    if type(orig) == 'table' then
        copy = {}
        for orig_key, orig_value in next, orig, nil do
            copy[deepCopy(orig_key)] = deepCopy(orig_value)
        end
        setmetatable(copy, deepCopy(getmetatable(orig)))
    else
        copy = orig
    end
    return copy
end

-- Create a new instance of the weather pattern
function SandstormWeather.new()
    local instance = setmetatable({}, SandstormWeather)
    -- Copy all properties
    for k, v in pairs(SandstormWeather) do
        if k ~= "__index" and k ~= "new" and type(v) ~= "function" then
            instance[k] = deepCopy(v)
        end
    end
    return instance
end

-- Initialize the weather pattern
function SandstormWeather:init(world)
    print("Initializing sandstorm weather")
    
    -- Set global light level
    if world and self.visual and self.visual.ambientLightLevel then
        world.lightLevel = self.visual.ambientLightLevel
    end
    
    -- Start ambient sounds
    if self.sounds and self.sounds.ambient then
        -- Play ambient sandstorm sound
        print("Playing sound: " .. self.sounds.ambient)
    end
    
    -- Create particle systems
    if self.particles and self.particles.sandParticles and self.particles.sandParticles.enabled then
        -- Create sand particles
        print("Creating sand particles with intensity: " .. self.particles.sandParticles.intensity)
    end
    
    if self.particles and self.particles.dustClouds and self.particles.dustClouds.enabled then
        -- Create dust cloud particles
        print("Creating dust cloud particles with intensity: " .. self.particles.dustClouds.intensity)
    end
    
    -- Display warning message to players
    if world and world.messageSystem then
        world.messageSystem:broadcast("WARNING: Sandstorm approaching. Seek shelter and protect equipment.")
    end
    
    return self
end

-- Update function called every frame
function SandstormWeather:update(world, dt)
    if not world then return end
    
    -- Update day/night cycle based on world time
    local timeOfDay = world.timeOfDay or "day"
    local modifiers = self.timeModifiers and self.timeModifiers[timeOfDay]
    
    if modifiers then
        -- Apply time-specific modifiers
        if modifiers.skyColor then
            -- Change sky color
        end
        
        if modifiers.ambientLightLevel then
            world.lightLevel = modifiers.ambientLightLevel
        end
    end
    
    -- Apply effects to game world
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            -- Check if entity is exposed to sandstorm
            if entity.isExposed then
                -- Apply damage
                if entity.health and self.effects then
                    entity.health = entity.health - (self.effects.exposureDamage * dt)
                end
                
                -- Apply equipment wear
                if entity.equipment and self.effects then
                    for _, item in pairs(entity.equipment) do
                        if item.durability then
                            item.durability = item.durability - 
                                (self.effects.equipmentWearRate * dt)
                        end
                    end
                end
                
                -- Apply movement speed reduction
                if entity.speed and self.effects then
                    entity.speed = entity.speed * self.effects.movementSpeedMultiplier
                end
            end
            
            -- Special effects for different entity types
            if entity.categories and self.effects then
                -- Effect on mechanical devices
                if table.contains(entity.categories, "mechanical") then
                    if math.random() < self.effects.mechanicalJamChance * dt then
                        if entity.onMalfunction then
                            entity:onMalfunction("sand_jam")
                        end
                    end
                end
                
                -- Effect on shields
                if table.contains(entity.categories, "shield") then
                    if entity.effectiveness then
                        entity.effectiveness = entity.effectiveness * 
                            self.effects.shieldEffectiveness
                    end
                end
                
                -- Effect on sensors and vision devices
                if table.contains(entity.categories, "sensor") then
                    if entity.range then
                        entity.range = entity.range * self.effects.visionRange
                    end
                end
            end
        end
    end
    
    -- Random impact sounds
    if math.random() < 0.1 and self.sounds then
        -- Play sand impact sound at random location
        print("Playing sound: " .. self.sounds.impact)
    end
end

-- Clean up when weather changes
function SandstormWeather:cleanUp(world)
    if not world then return end
    
    -- Stop ongoing sounds and particle effects
    print("Sandstorm weather ending")
    
    -- Notify players
    if world.messageSystem then
        world.messageSystem:broadcast("Sandstorm dissipating. Air clearing.")
    end
    
    -- Reset entity states
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            -- Reset movement speed
            if entity.speed and entity.defaultSpeed then
                entity.speed = entity.defaultSpeed
            end
            
            -- Reset shield effectiveness
            if entity.categories and table.contains(entity.categories, "shield") then
                if entity.effectiveness and entity.defaultEffectiveness then
                    entity.effectiveness = entity.defaultEffectiveness
                end
            end
            
            -- Reset sensor ranges
            if entity.categories and table.contains(entity.categories, "sensor") then
                if entity.range and entity.defaultRange then
                    entity.range = entity.defaultRange
                end
            end
        end
    end
end

-- Table contains helper function
function table.contains(table, element)
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

return SandstormWeather 