-- utils/ai_debug_overlay.lua
-- AI state debugging overlay

local AIDebugOverlay = {}

-- Initialize AI debug overlay
function AIDebugOverlay.init()
    AIDebugOverlay.enabled = false
    
    -- Display settings
    AIDebugOverlay.textColor = {1, 1, 1, 1}
    AIDebugOverlay.backgroundColor = {0, 0, 0, 0.7}
    AIDebugOverlay.borderColor = {0.5, 0.5, 0.5, 1}
    
    -- State colors
    AIDebugOverlay.stateColors = {
        idle = {0.7, 0.7, 0.7, 1},
        moving = {0.2, 0.8, 0.2, 1},
        attacking = {0.8, 0.2, 0.2, 1},
        fleeing = {1, 1, 0, 1},
        patrolling = {0.2, 0.2, 0.8, 1},
        searching = {0.8, 0.4, 0.2, 1},
        following = {0.4, 0.8, 0.4, 1},
        dead = {0.4, 0.4, 0.4, 1},
        stunned = {0.8, 0.2, 0.8, 1},
        casting = {0.2, 0.8, 0.8, 1},
        default = {1, 1, 1, 1}
    }
    
    -- Font size
    AIDebugOverlay.fontSize = 12
    AIDebugOverlay.font = love.graphics.newFont(AIDebugOverlay.fontSize)
end

-- Draw AI state overlay for all entities
function AIDebugOverlay.draw(entities, camera)
    if not AIDebugOverlay.enabled then return end
    if not entities then return end
    
    love.graphics.push()
    love.graphics.setFont(AIDebugOverlay.font)
    
    for _, entity in ipairs(entities) do
        if entity and entity.position and entity ~= entities.player then
            AIDebugOverlay.drawEntityAIState(entity, camera)
        end
    end
    
    love.graphics.pop()
end

-- Draw AI state for a single entity
function AIDebugOverlay.drawEntityAIState(entity, camera)
    if not entity.position then return end
    
    -- Get screen position
    local screenX, screenY = entity.position.x, entity.position.y
    
    -- Apply camera transform if available
    if camera then
        screenX, screenY = love.graphics.transformPoint(screenX, screenY)
    end
    
    -- Get AI state information
    local aiState = AIDebugOverlay.getEntityAIState(entity)
    if not aiState then return end
    
    -- Position text above entity
    local textY = screenY - 40
    local textX = screenX
    
    -- Draw AI state
    AIDebugOverlay.drawStateText(aiState.state, textX, textY, aiState.color)
    
    -- Draw additional info if available
    if aiState.target then
        AIDebugOverlay.drawStateText("Target: " .. aiState.target, textX, textY + 15, {0.8, 0.8, 0.8, 1})
    end
    
    if aiState.health then
        local healthColor = AIDebugOverlay.getHealthColor(aiState.health.current, aiState.health.max)
        AIDebugOverlay.drawStateText(string.format("HP: %d/%d", aiState.health.current, aiState.health.max), 
                                   textX, textY + 30, healthColor)
    end
    
    -- Draw path if entity is moving
    if aiState.path and #aiState.path > 1 then
        AIDebugOverlay.drawPath(aiState.path, camera)
    end
    
    -- Draw vision range if available
    if aiState.visionRange then
        AIDebugOverlay.drawVisionRange(entity.position.x, entity.position.y, aiState.visionRange, camera)
    end
end

-- Get AI state information from entity
function AIDebugOverlay.getEntityAIState(entity)
    local aiState = {
        state = "unknown",
        color = AIDebugOverlay.stateColors.default
    }
    
    -- Try to get state from different AI system formats
    if entity.ai then
        if entity.ai.state then
            aiState.state = entity.ai.state
        elseif entity.ai.currentState then
            aiState.state = entity.ai.currentState
        elseif entity.ai.behavior then
            aiState.state = entity.ai.behavior
        end
        
        -- Get target information
        if entity.ai.target then
            if type(entity.ai.target) == "string" then
                aiState.target = entity.ai.target
            elseif entity.ai.target.name then
                aiState.target = entity.ai.target.name
            elseif entity.ai.target.type then
                aiState.target = entity.ai.target.type
            else
                aiState.target = "entity"
            end
        end
        
        -- Get path information
        if entity.ai.path then
            aiState.path = entity.ai.path
        end
        
        -- Get vision range
        if entity.ai.visionRange then
            aiState.visionRange = entity.ai.visionRange
        end
    end
    
    -- Try alternative state locations
    if aiState.state == "unknown" then
        if entity.state then
            aiState.state = entity.state
        elseif entity.behavior then
            aiState.state = entity.behavior
        elseif entity.currentAction then
            aiState.state = entity.currentAction
        end
    end
    
    -- Get health information
    if entity.health then
        aiState.health = {
            current = entity.health.current or entity.health,
            max = entity.health.max or entity.maxHealth or 100
        }
    elseif entity.hp then
        aiState.health = {
            current = entity.hp,
            max = entity.maxHP or 100
        }
    end
    
    -- Set color based on state
    aiState.color = AIDebugOverlay.stateColors[aiState.state] or AIDebugOverlay.stateColors.default
    
    return aiState
end

-- Draw state text with background
function AIDebugOverlay.drawStateText(text, x, y, color)
    local textWidth = AIDebugOverlay.font:getWidth(text)
    local textHeight = AIDebugOverlay.font:getHeight()
    
    -- Center text horizontally
    local textX = x - textWidth / 2
    
    -- Draw background
    love.graphics.setColor(AIDebugOverlay.backgroundColor)
    love.graphics.rectangle("fill", textX - 2, y - 1, textWidth + 4, textHeight + 2)
    
    -- Draw border
    love.graphics.setColor(AIDebugOverlay.borderColor)
    love.graphics.setLineWidth(1)
    love.graphics.rectangle("line", textX - 2, y - 1, textWidth + 4, textHeight + 2)
    
    -- Draw text
    love.graphics.setColor(color)
    love.graphics.print(text, textX, y)
end

-- Get health color based on percentage
function AIDebugOverlay.getHealthColor(current, max)
    local percentage = current / max
    
    if percentage > 0.7 then
        return {0.2, 0.8, 0.2, 1} -- Green
    elseif percentage > 0.3 then
        return {0.8, 0.8, 0.2, 1} -- Yellow
    else
        return {0.8, 0.2, 0.2, 1} -- Red
    end
end

-- Draw entity path
function AIDebugOverlay.drawPath(path, camera)
    if not path or #path < 2 then return end
    
    love.graphics.setColor(0.2, 0.8, 0.2, 0.7)
    love.graphics.setLineWidth(2)
    
    local points = {}
    for _, point in ipairs(path) do
        local x, y = point.x or point[1], point.y or point[2]
        if camera then
            x, y = love.graphics.transformPoint(x, y)
        end
        table.insert(points, x)
        table.insert(points, y)
    end
    
    if #points >= 4 then
        love.graphics.line(points)
    end
    
    -- Draw waypoint markers
    love.graphics.setColor(0.8, 0.2, 0.2, 0.8)
    for i, point in ipairs(path) do
        local x, y = point.x or point[1], point.y or point[2]
        if camera then
            x, y = love.graphics.transformPoint(x, y)
        end
        love.graphics.circle("fill", x, y, 3)
    end
end

-- Draw vision range
function AIDebugOverlay.drawVisionRange(x, y, range, camera)
    if camera then
        x, y = love.graphics.transformPoint(x, y)
    end
    
    love.graphics.setColor(0.8, 0.8, 0.2, 0.2)
    love.graphics.circle("fill", x, y, range)
    
    love.graphics.setColor(0.8, 0.8, 0.2, 0.5)
    love.graphics.setLineWidth(1)
    love.graphics.circle("line", x, y, range)
end

-- Toggle AI debug overlay
function AIDebugOverlay.toggle()
    AIDebugOverlay.enabled = not AIDebugOverlay.enabled
    print("AI Debug Overlay " .. (AIDebugOverlay.enabled and "enabled" or "disabled"))
    return AIDebugOverlay.enabled
end

-- Set whether to show detailed information
function AIDebugOverlay.setDetailLevel(level)
    AIDebugOverlay.detailLevel = level or 1
end

-- Add custom state color
function AIDebugOverlay.addStateColor(state, color)
    AIDebugOverlay.stateColors[state] = color
end

return AIDebugOverlay
