local SteelSpear = {
    id = "steel_spear",
    name = "Steel Spear",
    type = "weapon",
    category = "spear",
    
    -- Properties
    properties = {
        damage = 30,
        attackSpeed = 1.0,
        range = 2.8,
        durability = 180,
        maxDurability = 180,
        pierceChance = 0.4,
        pierceCount = 3,
        armorPenetration = 0.45,
        weight = 1.2,
        thrustPower = 1.4,
        staggerChance = 0.25,
        staggerDuration = 0.4
    },
    
    -- Appearance
    appearance = {
        sprite = "steel_spear",
        scale = 1.2,
        animations = {
            "idle",
            "thrust",
            "sweep",
            "heavy"
        },
        variants = {
            "steel",
            "iron",
            "bronze",
            "copper"
        },
        blendMode = "normal",
        tint = {0.8, 0.8, 0.8},
        alpha = 1.0
    },
    
    -- Sound effects
    sounds = {
        thrust = "spear_thrust",
        sweep = "spear_sweep",
        pierce = "spear_pierce",
        stagger = "spear_stagger"
    },
    
    -- Effects
    effects = {
        pierce = {
            type = "status",
            duration = 0.4,
            effects = {
                pierce = true,
                count = 3
            }
        },
        stagger = {
            type = "status",
            duration = 0.4,
            effects = {
                stagger = true
            }
        }
    }
}

-- Initialize the weapon
function SteelSpear.init(weapon, world)
    -- Copy all fields from SteelSpear template to weapon instance
    for k, v in pairs(SteelSpear) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0
    }

    return weapon
end

-- Update the weapon
function SteelSpear.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- No special state updates needed
    end
end

-- Handle attack
function SteelSpear.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Calculate attack direction
    local angle = math.atan2(
        target.position.y - attacker.position.y,
        target.position.x - attacker.position.x
    )
    
    -- Create thrust effect
    if world.createEffect then
        world.createEffect({
            type = "thrust",
            position = attacker.position,
            angle = angle,
            range = weapon.properties.range,
            duration = 0.4
        })
    end
    
    -- Check for pierce effect
    if math.random() < weapon.properties.pierceChance then
        -- Create pierce effect
        if world.createEffect then
            world.createEffect({
                type = "pierce",
                position = target.position,
                duration = 0.4
            })
        end
        
        -- Apply pierce effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.pierce)
        end
        
        -- Play pierce sound
        if world.playSound then
            world.playSound(weapon.sounds.pierce)
        end
    end
    
    -- Check for stagger effect
    if math.random() < weapon.properties.staggerChance then
        -- Create stagger effect
        if world.createEffect then
            world.createEffect({
                type = "stagger",
                position = target.position,
                duration = weapon.properties.staggerDuration
            })
        end
        
        -- Apply stagger effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.stagger)
        end
        
        -- Play stagger sound
        if world.playSound then
            world.playSound(weapon.sounds.stagger)
        end
    end
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function SteelSpear.specialAttack(weapon, world, attacker, target)
    -- Create sweep effect
    if world.createEffect then
        world.createEffect({
            type = "sweep",
            position = attacker.position,
            angle = math.atan2(
                target.position.y - attacker.position.y,
                target.position.x - attacker.position.x
            ),
            range = weapon.properties.range * 1.2,
            duration = 0.6
        })
    end
    
    -- Apply pierce effect with increased power
    if target.applyEffect then
        target.applyEffect({
            type = "pierce",
            duration = 0.5,
            effects = {
                pierce = true,
                count = weapon.properties.pierceCount + 1
            }
        })
    end
    
    -- Apply stagger effect with increased duration
    if target.applyEffect then
        target.applyEffect({
            type = "stagger",
            duration = weapon.properties.staggerDuration * 1.5,
            effects = {
                stagger = true
            }
        })
    end
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 2)
    
    return true
end

return SteelSpear 