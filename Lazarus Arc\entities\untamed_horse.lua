local UntamedHorse = {
    id = "untamed_horse",
    name = "Untamed Horse",
    type = "untamed_horse",

    -- Entity categories
    categories = {"animal", "herbivore", "wild", "mount"},

    -- Threat categories
    threatCategories = {"predator", "monster"},
    foodCategories = {"plant", "grass"},

    -- Stats
    maxHealth = 60,
    health = 60,
    maxStamina = 100,
    stamina = 100,
    speed = 3.5, -- Slightly faster than a regular horse
    -- Increased speed when fleeing
    fleeSpeed = 5.0,
    visionRange = 25, -- Enhanced awareness

    -- Behaviors
    behaviors = {"wander", "flee", "graze"},

    -- Behavior configurations
    behaviorConfigs = {
        wander = {
            moveSpeed = 2.0,
            changeDirectionChance = 0.05,
            idleChance = 0.2,
            idleDuration = {4, 10},
            wanderRadius = 30
        },
        flee = {
            useCategories = true,
            moveSpeed = 5.0,
            detectionRadius = 12
        },
        graze = {
            foodTypes = {"grass", "tall_grass"},
            foodValue = {
                grass = 8,
                tall_grass = 12
            },
            fleeWhenThreatened = true,
            grazeTime = {8, 15},
            moveTime = {6, 12},
            grazeRadius = 20
        }
    },

    -- Abilities
    abilities = {
        buck = {
            -- (Implementation for bucking when a player tries to mount)
        }
    },

    -- Appearance
    appearance = {
        sprite = "untamed_horse", -- Replace with your sprite
        scale = 1.1,
        animations = {
            "idle",
            "walk",
            "gallop",
            "graze",
            "buck", -- Add animation for bucking
            "rear" -- Add animation for rearing
        },
        -- Could have variations in coat color
        variants = {
            "black",
            "brown",
            "white",
            "appaloosa"
        }
    },

    -- Sound effects
    sounds = {
        neigh = "horse_neigh",
        gallop = "horse_gallop",
        buck = "horse_buck"
    }
}

-- Initialize the untamed horse entity
function UntamedHorse.init(entity, world)
    entity.x = entity.x or 0
    entity.y = entity.y or 0
    entity.rotation = entity.rotation or 0
    entity.width = 1
    entity.height = 1
    entity.type = "untamed_horse"
    
    -- Apply behavior
    local behaviorName = "untamed_horse_behavior"
    -- Check if world is not nil before trying to access it
    if world and world.modules and world.modules.behaviors then
        local behavior = world.modules.behaviors[behaviorName]
        if behavior then
            behavior.apply(entity)
        end
    end
    
    return entity
end

return UntamedHorse