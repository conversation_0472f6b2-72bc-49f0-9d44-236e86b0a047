local OverdriveLoader = {}

function OverdriveLoader.load_overdrive(ability_name)
    local path = "overdrive/" .. ability_name
    local status, ability = pcall(require, path)

    if not status then
        print("Failed to load Overdrive ability: " .. ability_name)
        return nil
    end

    return ability
end

function OverdriveLoader.activate_overdrive(character, ability_name)
    local ability = OverdriveLoader.load_overdrive(ability_name)

    if ability then
        return ability.activate(character)
    else
        print("Invalid Overdrive ability: " .. ability_name)
        return false
    end
end

return OverdriveLoader
