-- tiles/frozen_river_bank.lua
local FrozenRiverBankTile = {
    id = "frozen_river_bank",
    name = "Frozen River Bank",
    passable = true,
    variants = 4, -- Different frozen bank patterns
    
    -- Properties
    properties = {
        thickness = 0.6,
        temperature = -8,
        waterContent = 0.4,
        iceContent = 0.6,
        hardness = 0.8,
        hasFish = true,
        hasIceFishing = true,
        thawing = false,
        erosion = 0.0
    },
    
    -- Visual properties
    visual = {
        base_color = {0.5, 0.6, 0.7}, -- Blue-grey
        variants = {
            {name = "smooth_bank", type = "smooth"},
            {name = "rough_bank", type = "rough"},
            {name = "fishing_hole", type = "fishing"},
            {name = "eroded_bank", type = "eroded"}
        },
        ice_patterns = {
            {name = "ice_sheet", type = "sheet"},
            {name = "ice_crystal", type = "crystal"},
            {name = "ice_vein", type = "vein"}
        }
    },
    
    -- Sound properties
    sounds = {
        ambient = {
            "ice_creaking",
            "water_rushing",
            "wind_whistling"
        },
        footsteps = {
            "ice_step",
            "snow_crunch"
        }
    },
    
    -- Resources
    resources = {
        fish = {
            amount = 3,
            regenRate = 0.1,
            harvestAmount = 1
        },
        ice = {
            amount = 5,
            regenRate = 0.2,
            harvestAmount = 1
        }
    },
    
    -- Effects
    effects = {
        thawing = {
            active = false,
            progress = 0.0,
            rate = 0.1
        },
        erosion = {
            active = false,
            progress = 0.0,
            rate = 0.05
        }
    },
    
    -- Spawn rules
    spawnRules = {
        fish = {
            chance = 0.5,
            minDistance = 2,
            maxDensity = 0.4
        },
        fishing_holes = {
            chance = 0.3,
            minDistance = 4,
            maxDensity = 0.2
        }
    },
    
    -- Weather effects
    weatherEffects = {
        snow = function(tile)
            -- Snow can strengthen the bank
            tile.properties.thickness = math.min(1.0, tile.properties.thickness + 0.1)
            tile.properties.hardness = math.min(1.0, tile.properties.hardness + 0.1)
            return "freeze_deep"
        end,
        rain = function(tile)
            -- Rain can start thawing and erosion
            tile.properties.thickness = math.max(0, tile.properties.thickness - 0.1)
            tile.properties.hardness = math.max(0, tile.properties.hardness - 0.1)
            tile.properties.erosion = math.min(1.0, tile.properties.erosion + 0.1)
            if tile.properties.thickness < 0.3 then
                tile.properties.thawing = true
                return "start_thaw"
            end
            return "soften"
        end,
        heat = function(tile)
            -- Heat rapidly thaws the bank
            tile.properties.thickness = math.max(0, tile.properties.thickness - 0.3)
            tile.properties.hardness = math.max(0, tile.properties.hardness - 0.3)
            tile.properties.erosion = math.min(1.0, tile.properties.erosion + 0.2)
            if tile.properties.thickness <= 0 then
                return "complete_thaw"
            end
            return "thaw"
        end
    },
    
    -- Time effects
    timeEffects = {
        day = function(tile)
            -- Daytime can start thawing
            if tile.properties.temperature > 0 then
                tile.properties.thickness = math.max(0, tile.properties.thickness - 0.1)
                tile.properties.hardness = math.max(0, tile.properties.hardness - 0.1)
                tile.properties.erosion = math.min(1.0, tile.properties.erosion + 0.05)
                if tile.properties.thickness < 0.3 then
                    tile.properties.thawing = true
                    return "start_thaw"
                end
                return "soften"
            end
            return nil
        end,
        night = function(tile)
            -- Nighttime can refreeze
            if tile.properties.temperature < 0 then
                tile.properties.thickness = math.min(1.0, tile.properties.thickness + 0.1)
                tile.properties.hardness = math.min(1.0, tile.properties.hardness + 0.1)
                if tile.properties.thawing then
                    tile.properties.thawing = false
                    return "refreeze"
                end
                return "freeze"
            end
            return nil
        end
    }
}

-- Initialize the tile
function FrozenRiverBankTile.init(tile, world)
    -- Initialize properties first
    tile.properties = tile.properties or {}
    
    -- Copy all fields from FrozenRiverBankTile template to tile instance
    for k, v in pairs(FrozenRiverBankTile) do
        if type(v) ~= "function" and tile[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                tile[k] = {}
                for subk, subv in pairs(v) do
                    tile[k][subk] = subv
                end
            else
                tile[k] = v
            end
        end
    end
    
    -- Set random variant
    if tile.visual and tile.visual.variants then
        local variants = tile.visual.variants
        tile.currentVariant = variants[math.random(#variants)]
    end
    
    -- Initialize resources
    if tile.resources then
        for resourceType, resource in pairs(tile.resources) do
            resource.currentAmount = resource.amount
            resource.lastHarvest = 0
            resource.growthStage = "mature"
        end
    end
    
    return tile
end

return FrozenRiverBankTile 