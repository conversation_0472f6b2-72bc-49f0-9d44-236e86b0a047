-- behaviors/celebration.lua
-- Declare CelebrationBehavior at the top level so it can be referenced from within functions
local CelebrationBehavior = {}

-- Define the behavior properties
CelebrationBehavior = {
    id = "celebration",
    name = "Celebration Behavior",

    -- Configuration parameters
    defaultConfig = {
        festivalArea = {x = 0, y = 0, radius = 15}, -- Central location of festivities
        moveSpeed = 1.2,                           -- Speed while moving around festival area
        performAnimations = true,                  -- Perform festive animations
        animations = {"dance", "cheer", "clap"}, -- Available festive animations
        interactionChance = 0.5,                   -- Chance to interact with nearby entities
        interactRadius = 5,                        -- Radius to find other entities to interact with
        idleChance = 0.2,                          -- Chance of idling between actions
        idleDuration = {3, 7},                     -- Idle duration between festive activities
        duration = {60, 120},                      -- Duration of staying in the festival area
        returnHomeAfterFestival = true,            -- Return home after festivities
        homePosition = nil                         -- Home location to return after festival
    },

    -- Initialize the behavior
    init = function(entity, customConfig)
        entity.festivalState = entity.festivalState or {
            festivalTimer = math.random(CelebrationBehavior.defaultConfig.duration[1], CelebrationBehavior.defaultConfig.duration[2]),
            isCelebrating = true,
            config = {}
        }

        local config = entity.festivalState.config
        for k, v in pairs(CelebrationBehavior.defaultConfig) do
            config[k] = v
        end

        if customConfig then
            for k, v in pairs(customConfig) do
                config[k] = v
            end
        end
    end,

    -- Update function called every game tick
    update = function(entity, world, dt)
        local state = entity.festivalState
        local config = state.config

        if state.isCelebrating then
            state.festivalTimer = state.festivalTimer - dt
            if state.festivalTimer <= 0 then
                state.isCelebrating = false
                if config.returnHomeAfterFestival then
                    CelebrationBehavior.returnHome(entity, dt)
                end
                return
            end

            if config.performAnimations and math.random() < config.idleChance then
                CelebrationBehavior.performAnimation(entity, config.animations)
            else
                CelebrationBehavior.moveAroundFestivalArea(entity, config, dt)
            end
        end
    end,

    -- Perform festive animations
    performAnimations = function(entity)
        -- Implementation details...
    end
}

return CelebrationBehavior
