-- structure_components.lua
-- Handles different damageable components of structures

local StructureComponents = {}

-- Base health multiplier for structures (compared to player health)
local STRUCTURE_HEALTH_MULTIPLIER = 10

-- Seasonal overlay definitions
StructureComponents.SeasonalOverlays = {
    spring = {
        name = "spring_overlay",
        effects = {
            "new_growth",
            "flowering",
            "renewal"
        },
        visualModifiers = {
            alpha = 0.3,
            blend = "add"
        }
    },
    summer = {
        name = "summer_overlay",
        effects = {
            "full_growth",
            "blooming",
            "vibrant"
        },
        visualModifiers = {
            alpha = 0.2,
            blend = "normal"
        }
    },
    autumn = {
        name = "autumn_overlay",
        effects = {
            "falling_leaves",
            "color_change",
            "harvest"
        },
        visualModifiers = {
            alpha = 0.4,
            blend = "multiply"
        }
    },
    winter = {
        name = "winter_overlay",
        effects = {
            "snow",
            "frost",
            "ice"
        },
        visualModifiers = {
            alpha = 0.5,
            blend = "normal"
        }
    }
}

-- Component types and their properties
StructureComponents.Types = {
    WALL = {
        name = "wall",
        baseHealth = 1000 * STRUCTURE_HEALTH_MULTIPLIER, -- 10,000 HP
        damageResistance = 2.5, -- Very resistant to damage
        criticalPoints = {"foundation", "load_bearing_points"},
        repairMaterials = {"stone", "wood", "metal", "reinforced_metal"},
        damageEffects = {
            visual = {"cracked", "damaged", "destroyed"},
            functional = {"leaking", "unstable", "collapsed"}
        },
        seasonalEffects = {
            spring = {"moss_growth", "vine_start"},
            summer = {"full_vines", "flowering"},
            autumn = {"leaf_fall", "color_change"},
            winter = {"frost", "snow"}
        }
    },
    DOOR = {
        name = "door",
        baseHealth = 500 * STRUCTURE_HEALTH_MULTIPLIER, -- 5,000 HP
        damageResistance = 2.0, -- High resistance
        criticalPoints = {"hinges", "lock", "frame"},
        repairMaterials = {"wood", "metal", "reinforced_metal", "titanium"},
        damageEffects = {
            visual = {"scratched", "bent", "broken"},
            functional = {"stuck", "unlocked", "destroyed"}
        }
    },
    WINDOW = {
        name = "window",
        baseHealth = 300 * STRUCTURE_HEALTH_MULTIPLIER, -- 3,000 HP
        damageResistance = 1.5, -- Moderate resistance
        criticalPoints = {"frame", "glass", "reinforcement"},
        repairMaterials = {"glass", "metal", "reinforced_glass", "bulletproof_glass"},
        damageEffects = {
            visual = {"cracked", "shattered", "broken"},
            functional = {"leaking", "unstable", "destroyed"}
        }
    },
    POWER_SOURCE = {
        name = "power_source",
        baseHealth = 800 * STRUCTURE_HEALTH_MULTIPLIER, -- 8,000 HP
        damageResistance = 2.0, -- High resistance
        criticalPoints = {"core", "connections", "cooling", "containment"},
        repairMaterials = {"metal", "electronics", "power_core", "reinforced_metal"},
        damageEffects = {
            visual = {"sparking", "overheating", "destroyed"},
            functional = {"unstable", "offline", "destroyed"}
        }
    },
    SUPPORT = {
        name = "support",
        baseHealth = 1200 * STRUCTURE_HEALTH_MULTIPLIER, -- 12,000 HP
        damageResistance = 3.0, -- Very high resistance
        criticalPoints = {"base", "joints", "reinforcement"},
        repairMaterials = {"stone", "metal", "reinforced_metal", "titanium"},
        damageEffects = {
            visual = {"cracked", "bent", "broken"},
            functional = {"unstable", "weakened", "collapsed"}
        }
    },
    ROOF = {
        name = "roof",
        baseHealth = 900 * STRUCTURE_HEALTH_MULTIPLIER, -- 9,000 HP
        damageResistance = 2.0, -- High resistance
        criticalPoints = {"beams", "joints", "support_points"},
        repairMaterials = {"wood", "metal", "stone", "reinforced_metal"},
        damageEffects = {
            visual = {"damaged", "leaking", "collapsed"},
            functional = {"leaking", "unstable", "destroyed"}
        }
    }
}

-- Create a new component instance
function StructureComponents.createComponent(type, options)
    local componentType = StructureComponents.Types[type]
    if not componentType then
        error("Invalid component type: " .. tostring(type))
    end

    local component = {
        type = type,
        name = componentType.name,
        health = componentType.baseHealth,
        maxHealth = componentType.baseHealth,
        damageResistance = componentType.damageResistance,
        criticalPoints = table.deepcopy(componentType.criticalPoints),
        repairMaterials = table.deepcopy(componentType.repairMaterials),
        damageEffects = table.deepcopy(componentType.damageEffects),
        seasonalEffects = componentType.seasonalEffects and table.deepcopy(componentType.seasonalEffects) or {},
        status = "intact",
        damageHistory = {},
        lastRepair = nil,
        currentSeason = "summer", -- Default season
        seasonalOverlay = nil,
        options = options or {}
    }

    -- Apply material-based modifiers
    if options and options.material then
        if options.material == "reinforced_metal" then
            component.damageResistance = component.damageResistance * 1.5
            component.health = component.health * 1.3
            component.maxHealth = component.maxHealth * 1.3
        elseif options.material == "titanium" then
            component.damageResistance = component.damageResistance * 2.0
            component.health = component.health * 1.5
            component.maxHealth = component.maxHealth * 1.5
        elseif options.material == "bulletproof_glass" then
            component.damageResistance = component.damageResistance * 1.8
            component.health = component.health * 1.4
            component.maxHealth = component.maxHealth * 1.4
        end
    end

    return component
end

-- Apply damage to a component
function StructureComponents.applyDamage(component, damage, damageType)
    -- Apply damage resistance
    local actualDamage = damage * (1 / component.damageResistance)
    
    -- Check for critical hits
    local isCritical = false
    if #component.criticalPoints > 0 and math.random() < 0.15 then -- Reduced critical hit chance
        isCritical = true
        actualDamage = actualDamage * 2.0 -- Increased critical damage multiplier
    end

    -- Apply damage
    component.health = math.max(0, component.health - actualDamage)
    
    -- Record damage
    table.insert(component.damageHistory, {
        amount = actualDamage,
        type = damageType,
        isCritical = isCritical,
        timestamp = os.time()
    })

    -- Update status with more granular thresholds
    if component.health <= 0 then
        component.status = "destroyed"
    elseif component.health < component.maxHealth * 0.2 then
        component.status = "critical"
    elseif component.health < component.maxHealth * 0.5 then
        component.status = "heavily_damaged"
    elseif component.health < component.maxHealth * 0.8 then
        component.status = "damaged"
    end

    return {
        actualDamage = actualDamage,
        isCritical = isCritical,
        newStatus = component.status
    }
end

-- Repair a component
function StructureComponents.repair(component, material, amount)
    -- Check if material is valid
    local validMaterial = false
    for _, validMat in ipairs(component.repairMaterials) do
        if material == validMat then
            validMaterial = true
            break
        end
    end

    if not validMaterial then
        return {
            success = false,
            message = "Invalid repair material for this component"
        }
    end

    -- Calculate repair amount based on material
    local repairAmount = amount
    if material == "reinforced_metal" then
        repairAmount = repairAmount * 1.5
    elseif material == "titanium" then
        repairAmount = repairAmount * 2.0
    elseif material == "bulletproof_glass" then
        repairAmount = repairAmount * 1.8
    end

    -- Apply repair
    local oldHealth = component.health
    component.health = math.min(component.maxHealth, component.health + repairAmount)
    component.lastRepair = os.time()

    -- Update status with more granular thresholds
    if component.health >= component.maxHealth * 0.8 then
        component.status = "intact"
    elseif component.health >= component.maxHealth * 0.5 then
        component.status = "damaged"
    elseif component.health >= component.maxHealth * 0.2 then
        component.status = "heavily_damaged"
    end

    return {
        success = true,
        amount = component.health - oldHealth,
        newStatus = component.status
    }
end

-- Get component status effects
function StructureComponents.getStatusEffects(component)
    local effects = {}
    
    if component.status == "destroyed" then
        effects = component.damageEffects.functional
    elseif component.status == "critical" then
        effects = {component.damageEffects.functional[1], component.damageEffects.visual[2]}
    elseif component.status == "heavily_damaged" then
        effects = {component.damageEffects.functional[1], component.damageEffects.visual[1]}
    elseif component.status == "damaged" then
        effects = {component.damageEffects.visual[1]}
    end

    return effects
end

-- Update component's seasonal state
function StructureComponents.updateSeason(component, season)
    component.currentSeason = season
    if StructureComponents.SeasonalOverlays[season] then
        component.seasonalOverlay = {
            name = StructureComponents.SeasonalOverlays[season].name,
            effects = table.deepcopy(StructureComponents.SeasonalOverlays[season].effects),
            visualModifiers = table.deepcopy(StructureComponents.SeasonalOverlays[season].visualModifiers)
        }
    end
end

-- Get component's current seasonal effects
function StructureComponents.getSeasonalEffects(component)
    local effects = {}
    
    -- Get base seasonal effects
    if component.seasonalOverlay then
        effects = table.deepcopy(component.seasonalOverlay.effects)
    end
    
    -- Get component-specific seasonal effects
    if component.seasonalEffects and component.seasonalEffects[component.currentSeason] then
        for _, effect in ipairs(component.seasonalEffects[component.currentSeason]) do
            table.insert(effects, effect)
        end
    end
    
    return effects
end

-- Get component's visual state including seasonal overlay
function StructureComponents.getVisualState(component)
    local state = {
        baseTile = component.options.material or component.name,
        overlay = nil,
        effects = {}
    }
    
    -- Add damage-based visual effects
    local damageEffects = StructureComponents.getStatusEffects(component)
    for _, effect in ipairs(damageEffects) do
        table.insert(state.effects, effect)
    end
    
    -- Add seasonal overlay if present
    if component.seasonalOverlay then
        state.overlay = {
            name = component.seasonalOverlay.name,
            modifiers = component.seasonalOverlay.visualModifiers
        }
    end
    
    -- Add seasonal effects
    local seasonalEffects = StructureComponents.getSeasonalEffects(component)
    for _, effect in ipairs(seasonalEffects) do
        table.insert(state.effects, effect)
    end
    
    return state
end

-- Serialize component data
function StructureComponents.serialize(component)
    return {
        type = component.type,
        health = component.health,
        maxHealth = component.maxHealth,
        status = component.status,
        damageHistory = component.damageHistory,
        lastRepair = component.lastRepair,
        currentSeason = component.currentSeason,
        seasonalOverlay = component.seasonalOverlay,
        options = component.options
    }
end

-- Deserialize component data
function StructureComponents.deserialize(data)
    local component = StructureComponents.createComponent(data.type, data.options)
    component.health = data.health
    component.maxHealth = data.maxHealth
    component.status = data.status
    component.damageHistory = data.damageHistory
    component.lastRepair = data.lastRepair
    component.currentSeason = data.currentSeason
    component.seasonalOverlay = data.seasonalOverlay
    return component
end

return StructureComponents 