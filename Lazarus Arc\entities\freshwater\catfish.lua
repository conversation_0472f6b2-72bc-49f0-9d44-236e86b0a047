local Catfish = {
    id = "catfish",
    name = "Channel Catfish",
    type = "fish",
    
    -- Entity categories
    categories = {"animal", "fish", "freshwater", "predator", "bottom_feeder"},
    
    -- Threat and food categories
    threatCategories = {"player", "predator"},
    foodCategories = {"fish", "insect", "carrion", "plant", "small_prey"},
    
    -- Stats
    maxHealth = 40,
    health = 40,
    maxStamina = 50,
    stamina = 50,
    speed = 1.5,
    
    -- Water properties
    water = {
        swimSpeed = 1.5,
        maxDepth = 10,
        preferredDepth = 6,
        oxygenLevel = 1.0,
        temperature = "warm",
        bottomFeeder = true
    },
    
    -- Behaviors
    behaviors = {"hunt", "patrol", "bottom_feed", "ambush"},
    
    -- Behavior configurations
    behaviorConfigs = {
        hunt = {
            moveSpeed = 2.0,
            searchRadius = 12,
            attackRange = 1.2,
            damage = 18,
            preferredPrey = {"fish", "insect", "carrion"},
            successChance = 0.75,
            nightBonus = 0.2
        },
        patrol = {
            moveSpeed = 1.2,
            patrolRadius = 15,
            preferredDepth = 6,
            restInterval = {15, 20}
        },
        bottom_feed = {
            moveSpeed = 0.8,
            searchRadius = 8,
            preferredFood = {"carrion", "plant", "insect"},
            successChance = 0.7,
            nightBonus = 0.15
        },
        ambush = {
            moveSpeed = 0.3,
            waitDuration = {10, 20},
            attackSpeed = 2.5,
            successChance = 0.85,
            preferredTerrain = "mud",
            nightBonus = 0.25
        }
    },
    
    -- Special abilities
    abilities = {
        whiskerSense = {
            range = 8,
            duration = 3,
            cooldown = 5,
            effect = "detect"
        },
        mudCloud = {
            range = 4,
            duration = 2,
            cooldown = 4,
            effect = "blind"
        },
        thrash = {
            damage = 15,
            duration = 0.4,
            cooldown = 3,
            staminaCost = 10
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "catfish",
        scale = 1.2,
        animations = {
            "idle", "swim", "bottom_feed", "attack", "thrash"
        },
        variants = {
            "brown", "gray", "spotted", "dark"
        }
    },
    
    -- Sound effects
    sounds = {
        splash = "fish_splash",
        attack = "fish_attack",
        swim = "fish_swim",
        thrash = "fish_thrash"
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.8, quantity = {1, 2}},
        {id = "scale", chance = 0.5, quantity = {1, 3}},
        {id = "whisker", chance = 0.3, quantity = {1, 2}},
        {id = "bone", chance = 0.4, quantity = {1, 1}}
    }
}

-- Initialize the entity
function Catfish.init(entity, world)
    -- Copy all fields from Catfish template to entity instance
    for k, v in pairs(Catfish) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random catfish variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Catfish.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update water properties
    if entity.water then
        -- Adjust behavior success based on time of day (nocturnal)
        local nightBonus = 0
        if world and world.timeOfDay == "night" then
            nightBonus = 0.2
            entity.water.preferredDepth = 4
        else
            entity.water.preferredDepth = 6
        end

        -- Apply night bonus to all behaviors
        for _, behaviorName in ipairs(entity.behaviors) do
            if entity.behaviorConfigs[behaviorName].nightBonus then
                entity.behaviorConfigs[behaviorName].successChance = 
                    entity.behaviorConfigs[behaviorName].successChance + nightBonus
            end
        end
    end
end

return Catfish 