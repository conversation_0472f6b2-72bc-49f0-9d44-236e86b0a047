local Butterfly = {
    id = "butterfly",
    name = "Butterfly",
    type = "butterfly",
    shape = {
        {0, -1}, {1, 0}, {0, 1}, {-1, 0}
    },
    size = 2,

    -- Entity categories
    categories = {"insect", "prey"},

    -- Threat and food categories
    threatCategories = {"predator"},  -- Butterflies are prey for various animals
    foodCategories = {"plant"},  -- Butterflies primarily consume nectar from plants

    -- Stats
    maxHealth = 3,
    health = 3,
    speed = 1.2,
    canFly = true,

    -- Behaviors
    behaviors = {"wander", "flee", "pollinate"},

    -- Behavior configurations
    behaviorConfigs = {
        wander = {
            moveSpeed = 1.0,
            changeDirectionChance = 0.2,  -- Butterflies have erratic flight patterns
            idleChance = 0.1,
            idleDuration = {0.5, 1.5},
            wanderRadius = 8,
            flightChance = 0.9  -- High chance to fly while wandering
        },
        flee = {
            useCategories = true,
            moveSpeed = 2.0,  -- Quickly fly away when threatened
            detectionRadius = 6
        },
        pollinate = {  -- Behavior for interacting with flowers
            targetTypes = {"flower"},  -- Specific plant types to pollinate
            pollinationRadius = 2,
            pollinationCooldown = 2.0  -- Time spent on a flower
        }
    },

    -- Appearance
    appearance = {
        sprite = "butterfly",
        scale = 0.6,
        animations = {
            "idle", "fly"
        }
    },

    -- Sound effects with synth configuration (delicate, ethereal sounds)
    sounds = {
        flutter = {
            synth = {
                instrument = "vibraphone",
                notes = {"C5", "E5", "G5"},
                durations = {0.1, 0.1, 0.15},
                volume = 0.15,
                vibrato = true,
                vibratoRate = 8.0
            }
        },
        fly = {
            synth = {
                instrument = "vibraphone",
                notes = {"E5", "G5"},
                durations = {0.2, 0.2},
                volume = 0.12,
                vibrato = true,
                vibratoRate = 6.0
            }
        },
        land = {
            synth = {
                instrument = "kalimba",
                notes = {"G4"},
                duration = 0.08,
                volume = 0.1
            }
        },
        hurt = {
            synth = {
                instrument = "xylophone",
                notes = {"D#5"},
                duration = 0.1,
                volume = 0.2,
                vibrato = true,
                vibratoRate = 12.0
            }
        },
        death = {
            synth = {
                instrument = "vibraphone",
                notes = {"G5", "E5", "C5"},
                durations = {0.2, 0.2, 0.4},
                volume = 0.2,
                vibrato = true,
                vibratoRate = 3.0
            }
        }
    },

    -- No drops
    drops = {}
}

function Butterfly.init(entity, worldCore)
    -- First, handle case where init is called during module registration
    -- by world_core.lua line 77: module.init(module, WorldCore)
    if entity == Butterfly and worldCore then
        -- This is a module registration call, not an entity initialization call
        -- Can store WorldCore reference if needed
        Butterfly.worldCore = worldCore
        return Butterfly
    end
    
    -- Regular entity initialization - Copy properties from template to entity
    for key, value in pairs(Butterfly) do
        if entity[key] == nil and key ~= "init" then
            entity[key] = value
        end
    end
    
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}
    entity.behaviors = entity.behaviors or {}
    
    -- Initialize behaviors with safety check
    if worldCore and worldCore.modules and worldCore.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = worldCore.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end
    
    return entity
end

return Butterfly