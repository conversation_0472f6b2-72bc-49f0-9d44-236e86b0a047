-- items/armor/cloth_armor.lua

local Armor = {
    id = "cloth_armor",
    name = "Cloth Armor",
    description = "Simple cloth garments that provide minimal protection.",
    type = "armor",
    category = "armor",
    stackable = false,

    stats = {
        defense = 3.0,
        durability = 40.0,
        repair_cost = 5,
        weight = 2.0,
    },

    material = "cloth",

    sprite = "res://Sprite/items/armor/cloth_armor.png",
    size = { width = 64, height = 64 },

    effects = {
        magic_resistance = 0.02
    },

    meta = {
        rarity = "common"
    },

    take_damage = function(self, amount)
        self.stats.durability = math.max(0, self.stats.durability - amount)
        if self.stats.durability == 0 then
            print(self.name .. " is torn! Penalties applied.")
            -- Potential penalties could be applied here
        end
    end,

    repair = function(self)
        local repair_amount = 40 - self.stats.durability
        self.stats.durability = 40
        print(self.name .. " repaired by " .. repair_amount .. " points.")
    end
}

return Armor
