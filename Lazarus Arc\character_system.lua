-- character_system.lua
-- A new character system that connects the controller to the anime renderer

local CharacterSystem = {}

-- Default character appearance
CharacterSystem.defaultAppearance = {
    head = 1,
    hair = { style = 1, color = "brown" },
    skin = "medium",
    eyes = { style = 1, color = "blue" },
    mouth = 1,
    body = { type = "average" },
    outfit = 1,
    effect = nil
}

-- Initialize the character system
function CharacterSystem.init()
    print("Initializing Character System...")

    -- Load the anime renderer
    CharacterSystem.animeRenderer = require("utils.anime_renderer")
    CharacterSystem.animeRenderer.init()

    -- Initialize character cache
    CharacterSystem.characters = {}

    print("Character System initialized")
    return true
end

-- Create a new character
function CharacterSystem.createCharacter(id, gender, appearance)
    -- Default values
    id = id or "player1"
    gender = gender or "male"
    appearance = appearance or CharacterSystem.defaultAppearance

    -- Create the character
    local character = {
        id = id,
        type = "player",
        entityType = "player",
        gender = gender,
        appearance = appearance,
        position = {x = 0, y = 0},
        velocity = {x = 0, y = 0},
        direction = "down",
        isMoving = false,
        state = "idle",

        -- Animation state
        animation = {
            current = "idle",
            frame = 1,
            timer = 0,
            speed = 0.1
        },

        -- Stats
        health = 100,
        maxHealth = 100,
        mana = 100,
        maxMana = 100,

        -- Methods
        update = CharacterSystem.updateCharacter,
        draw = CharacterSystem.drawCharacter,
        handleInput = CharacterSystem.handleInput
    }

    -- Add character stats for compatibility with existing code
    character.character = {
        stats = {
            health = 100,
            maxHealth = 100,
            mana = 100,
            maxMana = 100,
            strength = 10,
            dexterity = 10,
            intelligence = 10,
            wisdom = 10,
            constitution = 10,
            charisma = 10
        },
        class = "Warrior",
        level = 1,
        experience = 0,
        skills = {},
        inventory = { items = {} }
    }

    -- Store the character
    CharacterSystem.characters[id] = character

    return character
end

-- Update a character
function CharacterSystem.updateCharacter(self, dt)
    -- Update animation
    self.animation.timer = self.animation.timer + dt
    if self.animation.timer >= self.animation.speed then
        self.animation.timer = 0
        self.animation.frame = self.animation.frame + 1
        if self.animation.frame > 4 then
            self.animation.frame = 1
        end
    end

    -- Update animation state based on movement
    if self.isMoving then
        self.animation.current = "walk"
    else
        self.animation.current = "idle"
    end
end

-- Draw a character
function CharacterSystem.drawCharacter(self)
    -- Draw the character in all states
    local Engine = require("engine")

    -- Ensure the character has required fields
    if not self.position then
        self.position = {x = 0, y = 0}
        print("Warning: Character has no position, using (0,0)")
    end

    if not self.direction then
        self.direction = "down"
        print("Warning: Character has no direction, using 'down'")
    end

    -- Draw the character using the anime renderer
    if CharacterSystem.animeRenderer then
        -- Calculate scale based on distance from camera
        local scale = 1.0

        -- Draw the character
        CharacterSystem.animeRenderer.drawCharacter(
            self.position.x,
            self.position.y,
            scale,
            self.gender or "male",
            self.appearance or {}
        )

        -- Draw health and mana bars above the character
        local barWidth = 20
        local barHeight = 3
        local barSpacing = 2
        local barY = self.position.y - 30

        -- Get health and mana values with fallbacks
        local health = self.health or 100
        local maxHealth = self.maxHealth or 100
        local mana = self.mana or 100
        local maxMana = self.maxMana or 100

        -- Draw health bar
        love.graphics.setColor(1, 0, 0)
        love.graphics.rectangle("fill", self.position.x - barWidth/2, barY, barWidth * (health / maxHealth), barHeight)

        -- Draw mana bar
        love.graphics.setColor(0, 0, 1)
        love.graphics.rectangle("fill", self.position.x - barWidth/2, barY + barHeight + barSpacing, barWidth * (mana / maxMana), barHeight)

        -- Reset color
        love.graphics.setColor(1, 1, 1)
    else
        -- Fallback to simple triangle rendering
        local triangleSize = 12

        -- Calculate triangle vertices based on player direction
        local vx1, vy1, vx2, vy2, vx3, vy3

        if self.direction == "up" then
            vx1, vy1 = self.position.x, self.position.y - triangleSize
            vx2, vy2 = self.position.x - triangleSize, self.position.y + triangleSize
            vx3, vy3 = self.position.x + triangleSize, self.position.y + triangleSize
        elseif self.direction == "down" then
            vx1, vy1 = self.position.x, self.position.y + triangleSize
            vx2, vy2 = self.position.x - triangleSize, self.position.y - triangleSize
            vx3, vy3 = self.position.x + triangleSize, self.position.y - triangleSize
        elseif self.direction == "left" then
            vx1, vy1 = self.position.x - triangleSize, self.position.y
            vx2, vy2 = self.position.x + triangleSize, self.position.y - triangleSize
            vx3, vy3 = self.position.x + triangleSize, self.position.y + triangleSize
        elseif self.direction == "right" then
            vx1, vy1 = self.position.x + triangleSize, self.position.y
            vx2, vy2 = self.position.x - triangleSize, self.position.y - triangleSize
            vx3, vy3 = self.position.x - triangleSize, self.position.y + triangleSize
        else
            -- Default to down if direction is unknown
            vx1, vy1 = self.position.x, self.position.y + triangleSize
            vx2, vy2 = self.position.x - triangleSize, self.position.y - triangleSize
            vx3, vy3 = self.position.x + triangleSize, self.position.y - triangleSize
        end

        -- Draw the triangle
        love.graphics.setColor(1, 1, 1)
        love.graphics.polygon("fill", vx1, vy1, vx2, vy2, vx3, vy3)
    end
end

-- Handle input for a character
function CharacterSystem.handleInput(self, inputManager, world, dt)
    -- Ensure the character has required fields
    if not self.position then
        self.position = {x = 0, y = 0}
    end

    if not self.velocity then
        self.velocity = {x = 0, y = 0}
    end

    -- Get input state
    local input = inputManager.getInputState()

    -- Process movement
    if input.move then
        local moveX = input.move.x
        local moveY = input.move.y

        -- Ensure move values are numbers
        if type(moveX) ~= "number" then
            moveX = 0
        end

        if type(moveY) ~= "number" then
            moveY = 0
        end

        -- Update velocity based on input
        self.velocity.x = moveX * 150 -- Scale by movement speed
        self.velocity.y = moveY * 150 -- Scale by movement speed

        -- Update position
        self.position.x = self.position.x + self.velocity.x * dt
        self.position.y = self.position.y + self.velocity.y * dt

        -- Update direction
        if moveX ~= 0 or moveY ~= 0 then
            -- Convert vector direction to string direction for animation
            local dirString = "down" -- Default direction

            if math.abs(moveX) > math.abs(moveY) then
                -- Horizontal movement is dominant
                dirString = moveX > 0 and "right" or "left"
            else
                -- Vertical movement is dominant
                dirString = moveY > 0 and "down" or "up"
            end

            -- Store direction
            self.direction = dirString
            self.isMoving = true
        else
            self.isMoving = false
        end
    end

    -- Make sure the character is registered with the world
    if world and world.entitySystem then
        world.entitySystem:register(self)
    end

    -- Make sure the character is set as the player in the world
    if world and world.setPlayer and self.type == "player" then
        world:setPlayer(self)
    end
end

return CharacterSystem
