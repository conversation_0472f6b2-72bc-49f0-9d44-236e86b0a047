local Pelican = {
    id = "pelican",
    name = "Pelican",
    type = "bird",
    
    -- Entity categories
    categories = {"animal", "bird", "flying", "water", "coastal"},
    
    -- Threat and food categories
    threatCategories = {"player", "predator"},
    foodCategories = {"fish", "small_prey", "food"},
    
    -- Stats
    maxHealth = 45,
    health = 45,
    maxStamina = 80,
    stamina = 80,
    speed = 2.0,
    
    -- Flight properties
    flight = {
        maxHeight = 15,
        minHeight = 1,
        ascentSpeed = 0.9,
        descentSpeed = 2.0,
        hoverHeight = 3,
        currentHeight = 3,
        wingFlapRate = 0.1,
        soarChance = 0.4
    },
    
    -- Water properties
    water = {
        swimSpeed = 1.5,
        diveSpeed = 3.0,
        diveDepth = 4,
        diveDuration = 3,
        fishingRange = 5,
        pouchCapacity = 3
    },
    
    -- Behaviors
    behaviors = {"fish", "patrol", "roost", "coastal"},
    
    -- Behavior configurations
    behaviorConfigs = {
        fish = {
            moveSpeed = 2.0,
            searchRadius = 20,
            diveSpeed = 3.0,
            attackRange = 2,
            damage = 15,
            preferredPrey = {"fish", "small_prey"},
            successChance = 0.6,
            pouchCapacity = 3
        },
        patrol = {
            moveSpeed = 2.0,
            patrolRadius = 40,
            soarChance = 0.4,
            restInterval = {15, 25},
            preferredTime = "day"
        },
        roost = {
            startTime = "dusk",
            endTime = "dawn",
            roostHeight = {2, 4},
            healthRegen = 0.03,
            staminaRegen = 0.1
        },
        coastal = {
            moveSpeed = 1.8,
            preferredTerrain = "coast",
            searchRadius = 25,
            fishChance = 0.7
        }
    },
    
    -- Special abilities
    abilities = {
        dive = {
            speed = 3.0,
            duration = 3,
            cooldown = 6,
            staminaCost = 20
        },
        pouchGrab = {
            damage = 10,
            duration = 1,
            cooldown = 2,
            staminaCost = 10,
            pouchCapacity = 3
        },
        squawk = {
            range = 15,
            duration = 1,
            cooldown = 3,
            effect = "alert"
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "pelican",
        scale = 1.2,
        animations = {
            "idle", "fly", "dive", "fish", "squawk", "roost"
        },
        variants = {
            "brown", "white", "gray", "pink"
        }
    },
    
    -- Sound effects
    sounds = {
        squawk = "pelican_squawk",
        wingFlap = "pelican_wing_flap",
        splash = "pelican_splash"
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.6, quantity = {2, 3}},
        {id = "feather", chance = 0.7, quantity = {2, 4}},
        {id = "beak", chance = 0.3, quantity = {1, 1}},
        {id = "fish", chance = 0.5, quantity = {1, 2}},
        {id = "bone", chance = 0.4, quantity = {1, 2}}
    }
}

-- Initialize the entity
function Pelican.init(entity, world)
    -- Copy all fields from Pelican template to entity instance
    for k, v in pairs(Pelican) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random pelican variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Pelican.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update flight properties
    if entity.flight then
        -- Update wing flapping based on height and activity
        if entity.flight.currentHeight > entity.flight.minHeight then
            if entity.flight.soarChance > math.random() then
                entity.flight.wingFlapRate = 0.05
            else
                entity.flight.wingFlapRate = 0.1
            end
        else
            entity.flight.wingFlapRate = 0.2
        end
    end

    -- Update water properties
    if entity.water then
        -- Adjust fishing success based on time of day
        if world and world.timeOfDay == "dawn" or world.timeOfDay == "dusk" then
            entity.water.fishingRange = 6
            entity.water.fishChance = 0.8
        else
            entity.water.fishingRange = 5
            entity.water.fishChance = 0.7
        end
    end
end

return Pelican 