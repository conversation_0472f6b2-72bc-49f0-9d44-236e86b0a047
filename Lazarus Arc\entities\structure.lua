-- entities/structure.lua
local Structure = {
    id = "structure",
    name = "Structure",
    
    -- Base properties
    properties = {
        type = "generic", -- building, decoration, interactive, etc.
        width = 1,
        height = 1,
        rotation = 0,
        durability = 100,
        maxDurability = 100,
        interactable = false,
        storage = nil,
        effects = {},
        requirements = {},
        description = "",
        sprite = nil,
        model = nil,
        sound = nil,
        owner = nil,
        faction = "neutral"
    },
    
    -- Core methods
    new = function(self, properties)
        local structure = setmetatable({}, { __index = self })
        for k, v in pairs(properties or {}) do
            structure[k] = v
        end
        return structure
    end,
    
    -- Lifecycle methods
    init = function(self)
        -- Initialize structure state
        self.durability = self.maxDurability
        self.position = self.position or { x = 0, y = 0 }
        self.rotation = self.rotation or 0
        self.state = self.state or "idle"
        
        -- Initialize storage if present
        if self.storage then
            self.storage = {
                slots = {},
                maxSlots = self.storage.maxSlots or 20,
                maxStack = self.storage.maxStack or 99
            }
        end
    end,
    
    update = function(self, dt)
        -- Update structure state
        if self.effects then
            for _, effect in ipairs(self.effects) do
                effect:update(dt)
            end
        end
        
        -- Update animations
        if self.animation then
            self.animation:update(dt)
        end
    end,
    
    draw = function(self)
        -- Draw structure sprite
        if self.sprite then
            love.graphics.draw(self.sprite, self.position.x, self.position.y, self.rotation)
        end
        
        -- Draw effects
        if self.effects then
            for _, effect in ipairs(self.effects) do
                effect:draw()
            end
        end
    end,
    
    -- Structure interaction methods
    interact = function(self, entity)
        if not self.interactable then
            return false
        end
        
        -- Handle interaction based on structure type
        if self.type == "storage" then
            return self:openStorage(entity)
        elseif self.type == "crafting" then
            return self:openCrafting(entity)
        elseif self.type == "teleporter" then
            return self:teleport(entity)
        end
        
        return false
    end,
    
    damage = function(self, amount)
        self.durability = math.max(0, self.durability - amount)
        
        if self.durability <= 0 then
            self:destroy()
        end
    end,
    
    repair = function(self, amount)
        self.durability = math.min(self.maxDurability, self.durability + amount)
    end,
    
    destroy = function(self)
        self.state = "destroyed"
        -- Drop stored items
        if self.storage then
            self:dropStorage()
        end
        -- Trigger destruction event
        if self.onDestroy then
            self:onDestroy()
        end
    end,
    
    -- Storage methods
    openStorage = function(self, entity)
        if self.storage then
            -- Open storage UI
            -- Show inventory interface
            return true
        end
        return false
    end,
    
    dropStorage = function(self)
        if self.storage then
            for _, item in ipairs(self.storage.slots) do
                if item then
                    -- Create dropped item entity
                    -- Add to world
                end
            end
        end
    end,
    
    -- Utility methods
    checkRequirements = function(self, entity)
        if self.requirements then
            for _, req in ipairs(self.requirements) do
                if not req:check(entity) then
                    return false
                end
            end
        end
        return true
    end,
    
    -- Serialization
    serialize = function(self)
        return {
            id = self.id,
            name = self.name,
            type = self.type,
            position = self.position,
            rotation = self.rotation,
            durability = self.durability,
            maxDurability = self.maxDurability,
            storage = self.storage,
            effects = self.effects,
            requirements = self.requirements,
            owner = self.owner,
            faction = self.faction
        }
    end,
    
    deserialize = function(self, data)
        for k, v in pairs(data) do
            self[k] = v
        end
    end
}

return Structure 