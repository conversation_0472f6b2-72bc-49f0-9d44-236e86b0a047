local WoodenClub = {
    id = "wooden_club",
    name = "Wooden Club",
    type = "weapon",
    category = "club",
    
    -- Properties
    properties = {
        damage = 20,
        attackSpeed = 1.1,
        range = 1.5,
        durability = 140,
        maxDurability = 140,
        staggerChance = 0.3,
        knockback = 0.5,
        bluntPower = 1.2,
        armorPenetration = 0.2,
        weight = 1.0,
        combo = 0,
        maxCombo = 3,
        comboDamage = 1.1
    },
    
    -- Appearance
    appearance = {
        sprite = "wooden_club",
        scale = 1.0,
        animations = {
            "idle",
            "swing",
            "heavy",
            "combo"
        },
        variants = {
            "oak",
            "birch",
            "maple",
            "pine"
        },
        blendMode = "normal",
        tint = {0.6, 0.4, 0.2},
        alpha = 1.0
    },
    
    -- Sound effects
    sounds = {
        swing = "club_swing",
        impact = "club_impact",
        stagger = "club_stagger",
        combo = "club_combo"
    },
    
    -- Effects
    effects = {
        stagger = {
            type = "status",
            duration = 0.3,
            effects = {
                stagger = true
            }
        },
        combo = {
            type = "status",
            duration = 0.3,
            effects = {
                damage = 1.1
            }
        }
    }
}

-- Initialize the weapon
function WoodenClub.init(weapon, world)
    -- Copy all fields from WoodenClub template to weapon instance
    for k, v in pairs(WoodenClub) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        combo = 0,
        lastComboTime = 0
    }

    return weapon
end

-- Update the weapon
function WoodenClub.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Decay combo
        if world.time - weapon.properties.weaponState.lastComboTime > 1.0 then
            weapon.properties.weaponState.combo = 0
        end
    end
end

-- Handle attack
function WoodenClub.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Calculate attack direction
    local angle = math.atan2(
        target.position.y - attacker.position.y,
        target.position.x - attacker.position.x
    )
    
    -- Create swing effect
    if world.createEffect then
        world.createEffect({
            type = "swing",
            position = attacker.position,
            angle = angle,
            range = weapon.properties.range,
            duration = 0.4
        })
    end
    
    -- Check for stagger effect
    if math.random() < weapon.properties.staggerChance then
        -- Create stagger effect
        if world.createEffect then
            world.createEffect({
                type = "stagger",
                position = target.position,
                duration = 0.3
            })
        end
        
        -- Apply stagger effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.stagger)
        end
        
        -- Play stagger sound
        if world.playSound then
            world.playSound(weapon.sounds.stagger)
        end
    end
    
    -- Update combo
    if world.time - weapon.properties.weaponState.lastComboTime <= 1.0 then
        weapon.properties.weaponState.combo = math.min(weapon.properties.maxCombo,
            weapon.properties.weaponState.combo + 1)
    else
        weapon.properties.weaponState.combo = 1
    end
    weapon.properties.weaponState.lastComboTime = world.time
    
    -- Apply combo effect if active
    if weapon.properties.weaponState.combo > 1 then
        if target.applyEffect then
            target.applyEffect(weapon.effects.combo)
        end
        
        -- Play combo sound
        if world.playSound then
            world.playSound(weapon.sounds.combo)
        end
    end
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function WoodenClub.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough combo
    if weapon.properties.weaponState.combo < 2 then
        return false
    end
    
    -- Create heavy swing effect
    if world.createEffect then
        world.createEffect({
            type = "heavy_swing",
            position = attacker.position,
            angle = math.atan2(
                target.position.y - attacker.position.y,
                target.position.x - attacker.position.x
            ),
            range = weapon.properties.range * 1.2,
            duration = 0.6
        })
    end
    
    -- Apply stagger effect with increased power
    if target.applyEffect then
        target.applyEffect({
            type = "stagger",
            duration = 0.4,
            effects = {
                stagger = true,
                knockback = weapon.properties.knockback
            }
        })
    end
    
    -- Reset combo
    weapon.properties.weaponState.combo = 0
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 2)
    
    return true
end

return WoodenClub 