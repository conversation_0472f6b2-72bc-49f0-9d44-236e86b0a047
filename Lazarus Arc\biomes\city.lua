-- biomes/city.lua
local CityBiome = {
    id = "city",
    name = "City",
    description = "A densely populated urban area with constructed buildings, roads, and infrastructure.",

    -- Environmental factors (Urban heat island, potentially poor air, artificial light)
    environment = {
        humidity = 0.5,    -- Variable based on location/weather
        temperature = 0.6, -- Slightly warmer due to buildings/activity
        sunlight = 0.9,    -- Open areas, but shadows from buildings
        windStrength = 0.5, -- Can be channeled or blocked by buildings
        airQuality = 0.6   -- Can be lower due to population/industry
    },

    -- Core tile types used in this biome
    -- NOTE: Needs 'pavement', 'building_wall'(impassable), 'cobblestone', 'alley', 'park_grass' etc. Uses 'road', 'stone_path', 'ruins'.
    primaryTiles = {"pavement", "road", "building_wall"},
    secondaryTiles = {"cobblestone", "alley", "park_grass", "stone_path"},
    rareTiles = {"fountain_base", "sewer_grate", "market_stall_tile", "ruins"}, -- Ruins for older/damaged sections

    -- Default proportions (Highly dependent on district/generation logic)
    tileDistribution = {
        pavement = 0.4,
        road = 0.2,
        building_wall = 0.25, -- Impassable building boundaries
        cobblestone = 0.05,
        alley = 0.05,
        park_grass = 0.03,
        stone_path = 0.01,
        ruins = 0.01
    },

    -- Entities common to this biome
    commonEntities = {
        "citizen", "city_guard", "merchant_street", "rat", "pigeon", "stray_cat"
    },
    uncommonEntities = {
        "noble", "beggar", "courier", "guard_captain", "sewer_monster_minor" -- Added sewer monster
    },
    rareEntities = {
        "guild_master", "crime_boss", "city_golem", "visiting_dignitary", "ancient_city_spirit" -- Added golem/spirit
    },

    -- Biome variants representing different city districts
    variants = {
        market_district = {
            name = "Market District",
            environment = { airQuality = 0.5 }, -- Crowded
            tileAdjustments = { pavement=0.5, road=0.1, cobblestone=0.1, market_stall_tile=0.15, building_wall=0.1 },
            entityAdjustments = { merchant_street=2.5, citizen=1.5, city_guard=1.2, beggar=1.3 },
            specialEffects = {"crowd_noise", "market_smells"}
        },
        residential_common = {
            name = "Common Residential",
            tileAdjustments = { pavement=0.3, road=0.2, building_wall=0.35, alley=0.1, dirt=0.05 }, -- Maybe some dirt patches
            entityAdjustments = { citizen=1.8, stray_cat=1.2, city_guard=0.8 },
        },
        noble_district = {
            name = "Noble District",
            environment = { airQuality = 0.8 },
            tileAdjustments = { pavement=0.4, stone_path=0.2, park_grass=0.1, building_wall=0.25, fountain_base=0.05 }, -- Cleaner tiles
            entityAdjustments = { noble=2.0, city_guard=1.5, citizen=0.5 },
            specialEffects = {"clean_streets", "quiet_atmosphere"}
        },
        slums = {
            name = "Slums",
            environment = { airQuality = 0.3 },
            tileAdjustments = { dirt=0.3, mud=0.1, alley=0.2, building_wall=0.3, ruins=0.1 }, -- Poorer quality tiles
            entityAdjustments = { beggar=2.0, rat=1.5, crime_boss=1.1, city_guard=0.5 },
            specialEffects = {"stench", "high_crime_rate_event"}
        },
        docks = {
             name = "Docks",
             environment = { humidity=0.7 },
             tileAdjustments = { wood_plank_tile=0.4, water=0.3, road=0.1, building_wall=0.15, warehouse_floor=0.05 }, -- Need dock tiles
             entityAdjustments = { dock_worker=1.8, merchant_ship=1.0, seagull=1.5, sewer_monster_minor=1.1 },
             specialEffects = {"sea_air_smell", "creaking_wood_sound"}
        }
        -- Could add Industrial, Temple, Park districts etc.
    },

    -- Structures (Represent key buildings/areas rather than small generated ones)
    structures = {
        -- These might be placed by higher-level generation logic defining the city layout
        { name = "City Walls", chance=1.0, type="Boundary"}, -- Assume walls exist
        { name = "Main Gate", chance=1.0, type="Entry"},
        { name = "Market Square", chance=0.8, requiresVariant="market_district"},
        { name = "Guard Barracks", chance=0.7},
        { name = "Temple District", chance=0.5},
        { name = "Noble Manor", chance=0.3, requiresVariant="noble_district"},
        { name = "Warehouse", chance=0.6, requiresVariant="docks|industrial"},
        { name = "Guild Hall", chance=0.4}
    },

    -- Weather patterns (Urban effects)
    weather = {
        transitions = {
             clear = { clear = 60, cloudy = 25, rain_light = 10, fog_smog = 5 },
             cloudy = { clear = 40, cloudy = 40, rain_light = 15, fog_smog = 5 },
             rain_light = { cloudy = 50, rain_light = 30, rain_heavy = 15, clear = 5 },
             rain_heavy = { rain_light = 70, rain_heavy = 30 },
             fog_smog = { clear = 40, cloudy = 40, fog_smog = 20 }
        },
        default = "clear"
    },

    -- Unique features generation
    features = {
        { name = "Central Plaza", chance = 0.1, unique=true, size={min=10, max=20} },
        { name = "Grand Fountain", chance = 0.15, requiresTile="plaza|park_grass" },
        { name = "Statue Monument", chance = 0.2 },
        { name = "City Park", chance = 0.1, size={min=15, max=30} }, -- Area of park_grass tiles
        { name = "Canal/Sewer Access", chance = 0.2, -- Places sewer grate tiles/access points
        },
        { name = "Bridge", chance = 0.15, isConnector=true, requiresTile="water|canal" }
    },

    -- Generation algorithm (Highly Simplified Placeholder)
    generate = function(chunk, world, variant)
        print("Generating " .. (variant and CityBiome.variants[variant].name or "City") .. " biome")
        local variantData = variant and CityBiome.variants[variant] or nil

        -- Adjust tile distribution
        local tileDistribution = {}
        for tileType, chance in pairs(CityBiome.tileDistribution) do
            tileDistribution[tileType] = chance
            if variantData and variantData.tileAdjustments and variantData.tileAdjustments[tileType] then
                tileDistribution[tileType] = variantData.tileAdjustments[tileType]
            end
        end

        -- Noise functions
        local seed = world.seed + (chunk.x * 13 + chunk.y * 991)
        math.randomseed(seed)
        local function pnoise2D(x, y, frequency, seed_offset) return love.math.noise(x * frequency, y * frequency, (world.seed + (seed_offset or 0)) * 0.01) end

        -- *** Very Basic City Generation Placeholder ***
        -- A real generator needs complex logic for roads, buildings blocks, districts etc.
        -- This just places tiles based somewhat on noise and distribution.

        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                local worldX = chunk.x * world.CHUNK_SIZE + x
                local worldY = chunk.y * world.CHUNK_SIZE + y

                local roadNoise = pnoise2D(worldX, worldY, 0.1, 1) -- Road network rough placement
                local buildingNoise = pnoise2D(worldX, worldY, 0.15, 2) -- Building block placement

                local tileType = "pavement" -- Default city ground

                -- Crude road placement
                if roadNoise > 0.6 or roadNoise < -0.6 then
                    tileType = "road"
                -- Crude building placement
                elseif buildingNoise > 0.5 then
                     -- Simple box buildings - needs refinement to make walls vs floors
                     -- Mark as building wall for now (impassable)
                     tileType = "building_wall"
                end

                -- Apply distribution for other types (simple override)
                if tileType == "pavement" then
                     local tileRoll = math.random()
                     local cumulativeChance = 0
                     for tType, chance in pairs(tileDistribution) do
                          cumulativeChance = cumulativeChance + chance
                          if tileRoll <= cumulativeChance and tType ~= "road" and tType ~= "building_wall" and tType ~= "pavement" then
                               tileType = tType
                               break
                          end
                     end
                end

                -- Create the tile
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = math.random(1, 3),
                    isUrban = true,
                    passable = (tileType ~= "building_wall"), -- Walls block passage
                    isUnderground = false,
                    biome = "city",
                    variant = variant
                }

                -- Add variant-specific properties
                if variantData then
                    tile.variantData = variantData
                end

                -- Add to chunk
                chunk.tiles[x][y] = tile
            end
        end

        return chunk
    end,

    -- Initialize the biome
    init = function(worldCore)
        print("City biome module initialized")
        CityBiome.worldCore = worldCore
        print("City biome registered successfully for later use")
        return true
    end,

    registerWithWorld = function(world)
        print("Registering City biome generators with world")
        if not world or not world.chunkSystem then 
            print("Warning: Cannot register City biome - world or chunkSystem is nil") 
            return false 
        end
        world.chunkSystem:registerGenerator(CityBiome.id, CityBiome.generate)
        for variantId, variantData in pairs(CityBiome.variants or {}) do
            local fullVariantId = CityBiome.id .. "_" .. variantId
            world.chunkSystem:registerGenerator(fullVariantId, function(chunk, worldInstance)
                CityBiome.generate(chunk, worldInstance, variantId)
            end)
        end
        return true
    end
}

return CityBiome