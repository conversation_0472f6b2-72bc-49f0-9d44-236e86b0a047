-- behaviors/flocking.lua
-- Flocking behavior specifically for birds or flying entities

-- Declare FlockingBehavior at the top level so it can be referenced from within functions
local FlockingBehavior = {}

-- Define the behavior properties
FlockingBehavior = {
    id = "flocking",
    name = "Flocking Behavior",

    -- Configuration parameters
    defaultConfig = {
        neighborRadius = 6,        -- Radius to find neighbors
        separationDistance = 2,      -- Minimum distance between birds
        cohesionWeight = 0.25,        -- Attraction to flock center
        alignmentWeight = 0.35,       -- Alignment with flock direction
        separationWeight = 0.4,       -- Avoid crowding
        maxSpeed = 3.0,               -- Maximum flying speed
        verticalMovement = true,      -- Enables vertical flight
        altitudeRange = {min = 3, max = 8}, -- Altitude range birds fly within
        predatorAvoidance = true,     -- Whether flock flees predators
        predatorAvoidRadius = 15,     -- Radius to detect predators
        avoidObstacles = true,        -- Whether to avoid obstacles
        obstacleAvoidanceRadius = 2   -- How far ahead to check for obstacles
    },

    -- Initialize the behavior
    init = function(entity, customConfig)
        entity.flockingState = {
            neighbors = {},
            avgPosition = {x = 0, y = 0},
            avgDirection = {x = 0, y = 0},
            config = {}
        }

        local config = entity.flockingState.config
        for k, v in pairs(FlockingBehavior.defaultConfig) do
            config[k] = v
        end

        if customConfig then
            for k, v in pairs(customConfig) do
                config[k] = v
            end
        end
    end,

    -- Update flocking behavior
    update = function(entity, world, dt)
        local state = entity.flockingState
        local config = state.config

        -- Find neighbors
        state.neighbors = FlockingBehavior.findNeighbors(entity, world)

        -- Calculate group properties
        FlockingBehavior.calculateGroupProperties(entity)

        -- Compute flocking forces
        local cohesionForce = FlockingBehavior.calculateCohesionForce(entity)
        local alignmentForce = FlockingBehavior.calculateAlignmentForce(entity)
        local separationForce = FlockingBehavior.calculateSeparationForce(entity)

        -- Combine forces
        entity.velocity.x = entity.velocity.x + (cohesionForce.x * config.cohesionWeight
                                + alignmentForce.x * config.alignmentWeight
                                + separationForce.x * config.separationWeight)

        entity.velocity.y = entity.velocity.y +
                           (cohesionForce.y * config.cohesionWeight +
                            alignmentForce.y * config.alignmentWeight +
                            separationForce.y * config.separationWeight)

        -- Limit the speed
        local speed = math.sqrt(entity.velocity.x^2 + entity.velocity.y^2)
        if speed > config.maxSpeed then
            entity.velocity.x = (entity.velocity.x / speed) * config.maxSpeed
            entity.velocity.y = (entity.velocity.y / speed) * config.maxSpeed
        end

        -- Avoid obstacles if needed
        if config.avoidObstacles then
            FlockingBehavior.avoidObstacles(entity, world)
        end
    end,

    -- Find neighbors for flocking
    findNeighbors = function(entity, world)
        -- Implementation details to detect nearby flock members
    end,

    -- Calculate group cohesion force
    calculateCohesionForce = function(entity)
        -- Implementation details
    end,

    -- Calculate alignment force
    calculateAlignmentForce = function(entity)
        -- Implementation details
    end,

    -- Calculate separation force
    calculateSeparationForce = function(entity)
        -- Implementation details
    end,

    -- Avoid obstacles while flying
    avoidObstacles = function(entity, world)
        -- Implementation details
    end
}

return FlockingBehavior
