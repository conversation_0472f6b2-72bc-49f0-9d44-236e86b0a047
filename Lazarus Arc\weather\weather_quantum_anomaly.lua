-- weather/quantum_anomaly.lua
-- Quantum anomaly weather pattern - reality-bending weather effects

local QuantumAnomalyWeather = {
    id = "quantum_anomaly",
    name = "Quantum Anomaly",
    
    -- Visual properties
    visual = {
        skyColor = {r = 150, g = 100, b = 200}, -- Purple-pink sky
        sunIntensity = 0.4,
        cloudCoverage = 0.7,
        cloudColor = {r = 180, g = 120, b = 220}, -- Light purple clouds
        ambientLightLevel = 0.5,
        distortionColor = {r = 200, g = 150, b = 255} -- Distortion effect color
    },
    
    -- Environment modifiers
    environment = {
        temperature = 1.0, -- Normal temperature
        visibility = 0.6, -- Reduced visibility
        humidity = 0.5, -- Moderate humidity
        windStrength = 0.3, -- Light wind
        distortionLevel = 0.8, -- High reality distortion
        quantumLevel = 1.2 -- High quantum activity
    },
    
    -- Particle systems
    particles = {
        distortion = {
            enabled = true,
            intensity = 0.8,
            color = {r = 200, g = 150, b = 255},
            size = {min = 2, max = 4},
            speed = {min = 20, max = 40},
            image = "assets/particles/Complex/others/distortion_1.png",
            blendMode = "add",
            lifetime = {min = 2.0, max = 4.0},
            rotation = {min = 0, max = 360},
            alpha = {min = 0.3, max = 0.6}
        },
        quantum = {
            enabled = true,
            intensity = 0.6,
            color = {r = 150, g = 100, b = 200},
            size = {min = 1, max = 3},
            speed = {min = 15, max = 30},
            image = "assets/particles/Complex/others/quantum_1.png",
            blendMode = "add",
            lifetime = {min = 1.5, max = 3.0},
            rotation = {min = 0, max = 360},
            alpha = {min = 0.2, max = 0.4}
        },
        reality = {
            enabled = true,
            intensity = 0.4,
            color = {r = 180, g = 120, b = 220},
            size = {min = 1, max = 2},
            speed = {min = 10, max = 20},
            image = "assets/particles/Complex/others/reality_1.png",
            blendMode = "add",
            lifetime = {min = 1.0, max = 2.0},
            rotation = {min = 0, max = 360},
            alpha = {min = 0.1, max = 0.3}
        }
    },
    
    -- Sound effects
    sounds = {
        ambient = "quantum_anomaly_ambient",
        distortion = "quantum_distortion",
        reality = "quantum_reality",
        volume = 0.4
    },
    
    -- Effects on game entities and tiles
    effects = {
        -- Movement effects
        movementSpeedMultiplier = 0.9, -- Slightly reduced speed
        
        -- Vision effects
        visionRange = 0.6, -- Reduced vision
        
        -- Temperature effects
        temperatureModifier = 1.0, -- Normal temperature
        
        -- Energy effects
        energyDrainRate = 1.2, -- Slightly increased energy drain
        
        -- Shield effects
        shieldEffectiveness = 0.9, -- Slightly reduced shield effectiveness
        
        -- Sensor effects
        sensorRange = 0.7, -- Reduced sensor range
        
        -- Special effects
        realityShift = 0.1, -- Chance of reality shift
        quantumBoost = 1.2 -- Quantum energy boost
    },
    
    -- Transition probabilities to other weather (per game hour)
    transitions = {
        clear = 0.3,
        cloudy = 0.4,
        plasma_storm = 0.2,
        -- Implied: 0.1 chance to stay quantum anomaly
    },
    
    -- Day/night cycle modifiers
    timeModifiers = {
        dawn = {
            skyColor = {r = 170, g = 120, b = 220},
            ambientLightLevel = 0.6,
            distortionLevel = 0.8 -- Less distortion at dawn
        },
        day = {
            -- Uses default properties
        },
        dusk = {
            skyColor = {r = 130, g = 80, b = 180},
            ambientLightLevel = 0.4,
            distortionLevel = 1.1 -- More distortion at dusk
        },
        night = {
            skyColor = {r = 100, g = 50, b = 120},
            ambientLightLevel = 0.6,
            distortionLevel = 1.0 -- More visible at night
        }
    }
}

-- Helper function to safely set particle system properties
local function setupParticleSystem(ps, config)
    if not ps or type(ps) ~= "table" then
        print("Warning: Invalid particle system")
        return false
    end
    
    local methods = {
        setParticleLifetime = function() return ps:setParticleLifetime(config.lifetime.min, config.lifetime.max) end,
        setEmissionRate = function() return ps:setEmissionRate(config.emissionRate or 0) end,
        setSizeVariation = function() return ps:setSizeVariation(config.sizeVariation or 0.3) end,
        setLinearAcceleration = function() return ps:setLinearAcceleration(config.acceleration.x1 or 0, config.acceleration.y1 or 0, config.acceleration.x2 or 0, config.acceleration.y2 or 0) end,
        setColors = function() return ps:setColors(config.colors.r1 or 255, config.colors.g1 or 255, config.colors.b1 or 255, config.colors.a1 or 255, config.colors.r2 or 255, config.colors.g2 or 255, config.colors.b2 or 255, config.colors.a2 or 0) end,
        setBlendMode = function() return ps:setBlendMode(config.blendMode or "normal") end
    }
    
    for methodName, method in pairs(methods) do
        if type(ps[methodName]) ~= "function" then
            print("Warning: Particle system missing method: " .. methodName)
            return false
        end
        
        local success, err = pcall(method)
        if not success then
            print("Warning: Failed to call " .. methodName .. ": " .. tostring(err))
            return false
        end
    end
    
    return true
end

-- Initialize the weather pattern
function QuantumAnomalyWeather.init(world)
    if not world then
        print("Warning: world parameter is nil in QuantumAnomalyWeather.init")
        return
    end
    
    print("Initializing quantum anomaly weather")
    
    -- Set global light level
    world.lightLevel = QuantumAnomalyWeather.visual.ambientLightLevel
    
    -- Start ambient sounds if weather system exists
    if world.weatherSystem then
        if QuantumAnomalyWeather.sounds and QuantumAnomalyWeather.sounds.ambient then
            -- Use silent error handling for sound
            pcall(function()
                world.weatherSystem:playSound(QuantumAnomalyWeather.sounds.ambient, {
                    verbose = false,  -- Don't print warnings if sound is missing
                    loop = true,      -- Ambient sounds should loop
                    volume = 0.5      -- Lower volume for ambient weather
                })
            end)
        end
    end
    
    -- Create particle systems
    if QuantumAnomalyWeather.particles.distortion.enabled then
        -- Create distortion particles
        print("Creating distortion particles with intensity: " .. QuantumAnomalyWeather.particles.distortion.intensity)
        local success, image = pcall(love.graphics.newImage, QuantumAnomalyWeather.particles.distortion.image)
        if success and image then
            local success2, ps = pcall(love.graphics.newParticleSystem, image)
            if success2 and ps then
                local config = {
                    lifetime = QuantumAnomalyWeather.particles.distortion.lifetime,
                    emissionRate = 12,
                    sizeVariation = 0.3,
                    acceleration = {x1 = -8, y1 = -8, x2 = 8, y2 = 8},
                    colors = {r1 = 255, g1 = 255, b1 = 255, a1 = 255, r2 = 200, g2 = 150, b2 = 255, a2 = 0},
                    blendMode = "add"
                }
                
                if setupParticleSystem(ps, config) then
                    QuantumAnomalyWeather.distortionSystem = ps
                else
                    print("Warning: Failed to setup distortion particle system")
                end
            else
                print("Warning: Failed to create distortion particle system")
            end
        else
            print("Warning: Could not load distortion particle image: " .. QuantumAnomalyWeather.particles.distortion.image)
        end
    end
    
    if QuantumAnomalyWeather.particles.quantum.enabled then
        -- Create quantum particles
        print("Creating quantum particles with intensity: " .. QuantumAnomalyWeather.particles.quantum.intensity)
        local success, image = pcall(love.graphics.newImage, QuantumAnomalyWeather.particles.quantum.image)
        if success and image then
            local success2, ps = pcall(love.graphics.newParticleSystem, image)
            if success2 and ps then
                local config = {
                    lifetime = QuantumAnomalyWeather.particles.quantum.lifetime,
                    emissionRate = 8,
                    sizeVariation = 0.2,
                    acceleration = {x1 = -4, y1 = -4, x2 = 4, y2 = 4},
                    colors = {r1 = 255, g1 = 255, b1 = 255, a1 = 255, r2 = 150, g2 = 100, b2 = 200, a2 = 0},
                    blendMode = "add"
                }
                
                if setupParticleSystem(ps, config) then
                    QuantumAnomalyWeather.quantumSystem = ps
                else
                    print("Warning: Failed to setup quantum particle system")
                end
            else
                print("Warning: Failed to create quantum particle system")
            end
        else
            print("Warning: Could not load quantum particle image: " .. QuantumAnomalyWeather.particles.quantum.image)
        end
    end
    
    if QuantumAnomalyWeather.particles.reality.enabled then
        -- Create reality particles
        print("Creating reality particles with intensity: " .. QuantumAnomalyWeather.particles.reality.intensity)
        local success, image = pcall(love.graphics.newImage, QuantumAnomalyWeather.particles.reality.image)
        if success and image then
            local success2, ps = pcall(love.graphics.newParticleSystem, image)
            if success2 and ps then
                local config = {
                    lifetime = QuantumAnomalyWeather.particles.reality.lifetime,
                    emissionRate = 15,
                    sizeVariation = 0.2,
                    acceleration = {x1 = -2, y1 = -2, x2 = 2, y2 = 2},
                    colors = {r1 = 255, g1 = 255, b1 = 255, a1 = 255, r2 = 180, g2 = 120, b2 = 220, a2 = 0},
                    blendMode = "add"
                }
                
                if setupParticleSystem(ps, config) then
                    QuantumAnomalyWeather.realitySystem = ps
                else
                    print("Warning: Failed to setup reality particle system")
                end
            else
                print("Warning: Failed to create reality particle system")
            end
        else
            print("Warning: Could not load reality particle image: " .. QuantumAnomalyWeather.particles.reality.image)
        end
    end
    
    -- Display message to players if message system exists
    if world.messageSystem then
        world.messageSystem:broadcast("Quantum anomaly detected. Reality distortions observed.")
    else
        print("Quantum anomaly detected. Reality distortions observed.")
    end
end

-- Update function called every frame
function QuantumAnomalyWeather.update(world, dt)
    if not world then
        print("Warning: world parameter is nil in QuantumAnomalyWeather.update")
        return
    end
    
    -- Update day/night cycle based on world time
    local timeOfDay = world.timeOfDay or "day"
    local modifiers = QuantumAnomalyWeather.timeModifiers[timeOfDay]
    
    if modifiers then
        -- Apply time-specific modifiers
        if modifiers.skyColor then
            -- Change sky color
        end
        
        if modifiers.ambientLightLevel then
            world.lightLevel = modifiers.ambientLightLevel
        end
        
        if modifiers.distortionLevel then
            QuantumAnomalyWeather.particles.distortion.intensity = modifiers.distortionLevel
            if QuantumAnomalyWeather.distortionSystem then
                QuantumAnomalyWeather.distortionSystem:setEmissionRate(12 * modifiers.distortionLevel)
            end
        end
    end
    
    -- Update particle systems
    if QuantumAnomalyWeather.distortionSystem then
        QuantumAnomalyWeather.distortionSystem:update(dt)
    end
    if QuantumAnomalyWeather.quantumSystem then
        QuantumAnomalyWeather.quantumSystem:update(dt)
    end
    if QuantumAnomalyWeather.realitySystem then
        QuantumAnomalyWeather.realitySystem:update(dt)
    end
    
    -- Apply effects to game world if entity system exists
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            -- Apply movement speed reduction
            if entity.speed then
                entity.speed = entity.speed * QuantumAnomalyWeather.effects.movementSpeedMultiplier
            end
            
            -- Apply energy effects
            if entity.energy then
                entity.energy = entity.energy - 
                    (QuantumAnomalyWeather.effects.energyDrainRate * dt)
                -- Random quantum boost
                if math.random() < 0.1 then
                    entity.energy = entity.energy * QuantumAnomalyWeather.effects.quantumBoost
                end
            end
            
            -- Random reality shift
            if math.random() < QuantumAnomalyWeather.effects.realityShift * dt then
                if entity.position then
                    -- Slight random position shift
                    entity.position.x = entity.position.x + (math.random() - 0.5) * 10
                    entity.position.y = entity.position.y + (math.random() - 0.5) * 10
                end
            end
        end
    end
    
    -- Apply weather effects if weather system exists
    if world.weatherSystem then
        world.weatherSystem:applyEffects(QuantumAnomalyWeather.environment)
    end
end

-- Clean up when weather changes
function QuantumAnomalyWeather.cleanUp(world)
    if not world then
        print("Warning: world parameter is nil in QuantumAnomalyWeather.cleanUp")
        return
    end
    
    -- Stop sounds if weather system exists
    if world.weatherSystem then
        if QuantumAnomalyWeather.sounds and QuantumAnomalyWeather.sounds.ambient then
            -- Use silent error handling for sound
            pcall(function()
                world.weatherSystem:stopSound(QuantumAnomalyWeather.sounds.ambient)
            end)
        end
    end
    
    -- Clean up particle systems
    if QuantumAnomalyWeather.distortionSystem then
        QuantumAnomalyWeather.distortionSystem:stop()
    end
    if QuantumAnomalyWeather.quantumSystem then
        QuantumAnomalyWeather.quantumSystem:stop()
    end
    if QuantumAnomalyWeather.realitySystem then
        QuantumAnomalyWeather.realitySystem:stop()
    end
    
    -- Notify players if message system exists
    if world.messageSystem then
        world.messageSystem:broadcast("Quantum anomaly dissipating. Reality stabilizing.")
    else
        print("Quantum anomaly dissipating. Reality stabilizing.")
    end
    
    print("Quantum anomaly weather ending")
end

return QuantumAnomalyWeather 