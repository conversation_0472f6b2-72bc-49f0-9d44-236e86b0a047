-- biomes/mountain.lua
local MountainBiome = {
    id = "mountain",
    name = "Mountain",
    description = "A rugged terrain with steep slopes, rocky outcrops, and high elevations.",
    
    -- Environmental factors
    environment = {
        humidity = 0.4,    -- Lower humidity at high altitudes
        temperature = 0.3, -- Cooler temperatures
        sunlight = 0.8,    -- Strong sunlight at high altitudes
        windStrength = 1.0 -- Strong winds
    },
    
    -- Core tile types used in this biome
    primaryTiles = {"rock", "stone"},
    secondaryTiles = {"snow", "grass"},
    rareTiles = {"crystal", "ore"},
    
    -- Default proportions
    tileDistribution = {
        rock = 0.5,
        stone = 0.3,
        snow = 0.1,
        grass = 0.05,
        crystal = 0.03,
        ore = 0.02
    },
    
    -- Entities common to this biome
    commonEntities = {
        "mountain_goat", "eagle", "rock", "grass"
    },
    
    uncommonEntities = {
        "bear", "wolf", "crystal_formation", "ore_vein"
    },
    
    rareEntities = {
        "dragon", "mountain_troll", "ancient_ruins", "magic_spring"
    },
    
    -- Environmental properties specific to mountain biomes
    elevation = {min = 5, max = 20},
    hasSnow = true,
    hasWind = true,
    hasAvalanches = true,
    
    -- Biome variants for diverse generation
    variants = {
        snowy = {
            name = "Snowy Mountain",
            temperature = 0.1,
            tileAdjustments = {
                rock = 0.3,
                stone = 0.2,
                snow = 0.4
            },
            entityAdjustments = {
                mountain_goat = 1.5,
                eagle = 1.2
            },
            specialEffects = {
                "snowfall",
                "ice_crystals"
            }
        },
        rocky = {
            name = "Rocky Mountain",
            temperature = 0.4,
            tileAdjustments = {
                rock = 0.6,
                stone = 0.3,
                grass = 0.05
            },
            entityAdjustments = {
                mountain_troll = 1.5,
                bear = 1.2
            },
            specialEffects = {
                "rock_slides",
                "strong_winds"
            }
        },
        crystal = {
            name = "Crystal Mountain",
            temperature = 0.3,
            tileAdjustments = {
                rock = 0.4,
                stone = 0.3,
                crystal = 0.2
            },
            entityAdjustments = {
                crystal_formation = 2.0,
                dragon = 1.5
            },
            specialEffects = {
                "crystal_glow",
                "magic_aurora"
            }
        }
    },
    
    -- Structures that can generate in this biome
    structures = {
        {
            name = "mountain_fortress",
            chance = 0.1,
            entities = {"guard", "mountain_king"}
        },
        {
            name = "dragon_lair",
            chance = 0.05,
            requiresVariant = "crystal",
            entities = {"dragon", "dragon_guardian"}
        },
        {
            name = "troll_cave",
            chance = 0.08,
            requiresVariant = "rocky",
            entities = {"mountain_troll", "troll_warrior"}
        },
        {
            name = "eagle_nest",
            chance = 0.12,
            entities = {"eagle", "eagle_chick"}
        }
    },
    
    -- Environmental effects
    environmentalEffects = {
        wind = 0.8,
        snow = 0.6,
        rock_slides = 0.4,
        avalanches = 0.2,
        lightning = 0.1
    },
    
    -- Unique features
    features = {
        {
            name = "peak",
            chance = 0.1,
            unique = true
        },
        {
            name = "crystal_cave",
            chance = 0.05,
            requiresVariant = "crystal"
        },
        {
            name = "avalanche_path",
            chance = 0.08,
            requiresVariant = "snowy",
            danger = true
        },
        {
            name = "ancient_ruins",
            chance = 0.03
        },
        {
            name = "waterfall",
            chance = 0.15
        }
    },
    
    -- Generation algorithm for this biome
    generate = function(self, chunk, world, variant)
        print("Generating " .. (variant and self.variants[variant].name or "Mountain") .. " biome")
        
        -- Get variant-specific adjustments
        local variantData = variant and self.variants[variant] or nil
        local elevationMultiplier = variantData and variantData.elevationMultiplier or 1.0
        local temperatureOffset = variantData and variantData.temperatureOffset or 0.0
        
        -- Adjust tile distribution based on variant
        local tileDistribution = {}
        for tileType, chance in pairs(self.tileDistribution) do
            tileDistribution[tileType] = chance
            
            -- Apply variant adjustments if any
            if variantData and variantData.tileAdjustments and variantData.tileAdjustments[tileType] then
                tileDistribution[tileType] = variantData.tileAdjustments[tileType]
            end
        end
        
        -- Add variant-specific tiles if they don't exist in base distribution
        if variantData and variantData.tileAdjustments then
            for tileType, chance in pairs(variantData.tileAdjustments) do
                if not tileDistribution[tileType] then
                    tileDistribution[tileType] = chance
                end
            end
        end
        
        -- Generate base terrain using noise functions
        local seed = world.seed + (chunk.x * 823 + chunk.y * 419)
        math.randomseed(seed)
        
        -- Create perlin noise-like function (simplified for example)
        local function noise2D(x, y, frequency)
            return math.abs(math.sin(x * frequency) * math.cos(y * frequency))
        end
        
        -- Initialize 2D tiles array if not exists
        chunk.tiles = chunk.tiles or {}
        for x = 0, world.CHUNK_SIZE - 1 do
            chunk.tiles[x] = chunk.tiles[x] or {}
        end
        
        -- Generate tiles
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                -- Calculate world coordinates
                local worldX = chunk.x * world.CHUNK_SIZE + x
                local worldY = chunk.y * world.CHUNK_SIZE + y
                
                -- Generate different noise layers
                local baseNoise = noise2D(worldX, worldY, 0.01)  -- Large mountain shapes
                local detailNoise = noise2D(worldX, worldY, 0.05) -- Small features
                local featureNoise = noise2D(worldX, worldY, 0.02) -- Medium features
                
                -- Calculate elevation - higher values = higher altitude
                local elevation = (baseNoise * 0.6 + detailNoise * 0.3 + featureNoise * 0.1) * elevationMultiplier
                
                -- Determine tile type based on elevation, noise, and distribution
                local tileType = "rock" -- Default
                
                -- Higher elevations are more likely to be snow, cliff or rock
                if elevation > 0.8 and (variant == "alpine" or variant == nil) then
                    -- High peaks in alpine mountains are snow
                    tileType = "snow"
                elseif elevation > 0.7 and detailNoise > 0.6 then
                    -- Very steep areas are cliffs
                    tileType = "cliff"
                elseif elevation < 0.3 and detailNoise < 0.4 then
                    -- Lower areas tend to be gravel and loose stone
                    tileType = "gravel"
                else
                    -- Use the probability distribution for middle elevations
                    local tileRoll = math.random()
                    local cumulativeChance = 0
                    
                    for tType, chance in pairs(tileDistribution) do
                        cumulativeChance = cumulativeChance + chance
                        if tileRoll <= cumulativeChance then
                            tileType = tType
                            break
                        end
                    end
                end
                
                -- Variant-specific modifications
                if variant == "volcanic" then
                    -- Add lava and hot springs in volcanic variant
                    if elevation < 0.3 and featureNoise > 0.7 and math.random() < 0.5 then
                        tileType = "lava_flow"
                    elseif elevation < 0.4 and featureNoise > 0.6 and math.random() < 0.3 then
                        tileType = "hot_spring"
                    elseif elevation > 0.7 and math.random() < 0.4 then
                        tileType = "lava_rock"
                    end
                elseif variant == "crystal" then
                    -- More crystal veins in crystal mountains
                    if detailNoise > 0.75 and math.random() < 0.4 then
                        tileType = "crystal_vein"
                    elseif detailNoise > 0.85 and featureNoise > 0.7 then
                        tileType = "glowing_crystal"
                    end
                elseif variant == "ancient" then
                    -- Ancient ruins are more common in ancient mountains
                    if elevation > 0.5 and elevation < 0.7 and featureNoise > 0.6 then
                        if math.random() < 0.3 then
                            tileType = "ancient_ruins"
                        end
                    elseif elevation < 0.5 and featureNoise > 0.5 then
                        if math.random() < 0.4 then
                            tileType = "moss_covered_stone"
                        end
                    end
                end
                
                -- Create the tile
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = math.random(1, 3),
                    passable = (tileType ~= "cliff" and tileType ~= "water"),
                    isUnderground = false,
                    elevation = elevation * 100, -- Scale to 0-100 range
                    temperature = 20 - elevation * 10 + temperatureOffset + math.random() * 2 - 1, -- Base temperature adjusted by elevation
                    steepness = detailNoise * elevation * 2 -- How steep this section is (affects movement)
                }
                
                -- Add to chunk using 2D indexing
                chunk.tiles[x][y] = tile
            end
        end
        
        -- Place features
        self.generateFeatures(chunk, world, variant)
        
        -- Add entities
        self.populateEntities(chunk, world, variant)
        
        -- Place structures
        self.generateStructures(chunk, world, variant)
    end,
    
    -- Helper function to generate features
    generateFeatures = function(self, chunk, world, variant)
        -- Iterate through each potential feature
        for _, feature in ipairs(self.features) do
            -- Check if feature should spawn based on chance
            if math.random() < feature.chance then
                -- Check variant requirements if any
                if feature.requiresVariant and feature.requiresVariant ~= variant then
                    goto continue
                end
                
                -- For unique features, only place one per chunk
                if feature.unique and chunk.uniqueFeatures and chunk.uniqueFeatures[feature.name] then
                    goto continue
                end
                
                -- Determine position for feature
                local featureX = math.random(3, world.CHUNK_SIZE - 4)
                local featureY = math.random(3, world.CHUNK_SIZE - 4)
                
                -- Place feature based on type
                if feature.name == "mountain_peak" then
                    -- Create a mountain peak feature
                    local radius = math.random(6, 12)
                    local peakHeight = math.random(feature.elevation.min, feature.elevation.max)
                    
                    for dx = -radius, radius do
                        for dy = -radius, radius do
                            local dist = math.sqrt(dx*dx + dy*dy)
                            if dist <= radius then
                                local tileX = featureX + dx
                                local tileY = featureY + dy
                                
                                -- Only modify if within chunk bounds
                                if tileX >= 0 and tileX < world.CHUNK_SIZE and
                                   tileY >= 0 and tileY < world.CHUNK_SIZE then
                                    local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                    local tile = chunk.tiles[tileX][tileY]
                                    
                                    -- Higher as you get closer to center
                                    local heightFactor = 1.0 - dist/radius
                                    local newElevation = tile.elevation + peakHeight * heightFactor * heightFactor
                                    
                                    -- Update tile with new elevation
                                    tile.elevation = newElevation
                                    
                                    -- Adjust tile type based on new elevation
                                    if newElevation > 120 and variant ~= "volcanic" then
                                        tile.type = "snow" -- Snow on very high peaks
                                    elseif newElevation > 100 or dist < 2 then
                                        tile.type = "peak_stone" -- Special peak stone type
                                    elseif newElevation > 80 then
                                        tile.type = "cliff" -- Cliffs on high slopes
                                    end
                                    
                                    -- Variant-specific peak types
                                    if variant == "volcanic" and dist < radius * 0.3 then
                                        tile.type = "volcanic_crater"
                                    elseif variant == "crystal" and dist < radius * 0.3 then
                                        tile.type = "crystal_peak"
                                    end
                                    
                                    -- Adjust temperature based on elevation
                                    tile.temperature = 20 - newElevation / 10
                                end
                            end
                        end
                    end
                    
                    -- Create a summit marker
                    local worldX = chunk.x * world.CHUNK_SIZE + featureX
                    local worldY = chunk.y * world.CHUNK_SIZE + featureY
                    
                    world.entitySystem:addEntity("summit_marker", worldX, worldY, {
                        elevation = peakHeight,
                        name = chunk.name and chunk.name .. " Peak" or "Mountain Peak"
                    })
                    
                elseif feature.name == "mountain_lake" then
                    -- Create a high-altitude lake
                    local size = math.random(feature.size.min, feature.size.max)
                    local radius = size / 2
                    
                    -- Ensure the area is relatively flat (using 2D index for elevation)
                    local centerElevation = chunk.tiles[featureX][featureY].elevation
                    
                    -- Lakes form in depressions
                    for dx = -radius, radius do
                        for dy = -radius, radius do
                            local dist = math.sqrt(dx*dx + dy*dy)
                            if dist <= radius then
                                local tileX = featureX + dx
                                local tileY = featureY + dy
                                
                                if tileX >= 0 and tileX < world.CHUNK_SIZE and
                                   tileY >= 0 and tileY < world.CHUNK_SIZE then
                                    local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                    local tile = chunk.tiles[tileX][tileY]
                                    
                                    -- Keep elevation consistent across lake
                                    tile.elevation = centerElevation - 5 - dist/2
                                    
                                    -- Set to lake tile
                                    if dist < radius * 0.9 then
                                        tile.type = "mountain_lake"
                                        tile.depth = (radius - dist) / 3
                                        tile.temperature = 10 + math.random() * 5
                                    else
                                        -- Shore
                                        tile.type = "lake_shore"
                                    end
                                    
                                    -- Add some fish
                                    if dist < radius * 0.7 and math.random() < 0.1 then
                                        local fishX = chunk.x * world.CHUNK_SIZE + tileX
                                        local fishY = chunk.y * world.CHUNK_SIZE + tileY
                                        
                                        world.entitySystem:addEntity("mountain_fish", fishX, fishY, {
                                            size = math.random(5, 15) / 10
                                        })
                                    end
                                end
                            end
                        end
                    end
                    
                elseif feature.name == "cliff_face" then
                    -- Create a vertical cliff face
                    local length = math.random(8, 16)
                    local height = math.random(feature.height.min, feature.height.max)
                    
                    -- Determine cliff direction (0-3 = N, E, S, W)
                    local direction = math.random(0, 3)
                    local dirX = (direction == 1) and 1 or ((direction == 3) and -1 or 0)
                    local dirY = (direction == 0) and -1 or ((direction == 2) and 1 or 0)
                    
                    -- Perpendicular directions for cliff width
                    local perpX = dirY
                    local perpY = -dirX
                    
                    -- Create the cliff
                    for i = 0, length do
                        for j = -2, 2 do
                            local tileX = featureX + dirX * i + perpX * j
                            local tileY = featureY + dirY * i + perpY * j
                            
                            if tileX >= 0 and tileX < world.CHUNK_SIZE and
                               tileY >= 0 and tileY < world.CHUNK_SIZE then
                                local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                local tile = chunk.tiles[tileX][tileY]
                                
                                if j == 0 then
                                    -- Central cliff face
                                    tile.type = "cliff"
                                    tile.elevation = tile.elevation + height
                                    tile.steepness = 1.0
                                    tile.impassable = true
                                else
                                    -- Areas near cliff
                                    tile.type = "cliff_approach"
                                    tile.elevation = tile.elevation + height * (1 - math.abs(j) * 0.3)
                                    tile.steepness = 0.7
                                end
                            end
                        end
                    end
                    
                    -- Add climbing anchors
                    if math.random() < 0.4 then
                        local anchorCount = math.random(1, 3)
                        for i = 1, anchorCount do
                            local anchorPos = math.random(0, length)
                            local anchorX = chunk.x * world.CHUNK_SIZE + featureX + dirX * anchorPos
                            local anchorY = chunk.y * world.CHUNK_SIZE + featureY + dirY * anchorPos
                            
                            world.entitySystem:addEntity("climbing_anchor", anchorX, anchorY, {
                                direction = direction
                            })
                        end
                    end
                    
                elseif feature.name == "cave_system" then
                    -- Create an entrance to a cave system
                    local worldX = chunk.x * world.CHUNK_SIZE + featureX
                    local worldY = chunk.y * world.CHUNK_SIZE + featureY
                    local depth = math.random(feature.depth.min, feature.depth.max)
                    
                    -- Mark the tile as a cave entrance
                    local tileIndex = featureX + featureY * world.CHUNK_SIZE
                    chunk.tiles[featureX][featureY].type = "cave_entrance"
                    
                    -- Add cave entrance entity
                    world.entitySystem:addEntity("cave_entrance", worldX, worldY, {
                        depth = depth,
                        caveType = variant == "crystal" and "crystal_cave" or "standard",
                        temperature = variant == "volcanic" and 40 or 10
                    })
                    
                    -- Create some rocks and details around the entrance
                    local entranceRadius = math.random(2, 4)
                    for dx = -entranceRadius, entranceRadius do
                        for dy = -entranceRadius, entranceRadius do
                            if dx ~= 0 or dy ~= 0 then -- Not the center
                                local dist = math.sqrt(dx*dx + dy*dy)
                                if dist <= entranceRadius then
                                    local tileX = featureX + dx
                                    local tileY = featureY + dy
                                    
                                    if tileX >= 0 and tileX < world.CHUNK_SIZE and
                                       tileY >= 0 and tileY < world.CHUNK_SIZE then
                                        local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                        
                                        -- Approach to cave
                                        if math.random() < 0.7 then
                                            chunk.tiles[tileX][tileY].type = "cave_approach"
                                        end
                                        
                                        -- Add some boulders
                                        if math.random() < 0.2 then
                                            local boulderX = chunk.x * world.CHUNK_SIZE + tileX
                                            local boulderY = chunk.y * world.CHUNK_SIZE + tileY
                                            
                                            world.entitySystem:addEntity("boulder", boulderX, boulderY, {
                                                size = math.random(5, 20) / 10
                                            })
                                        end
                                    end
                                end
                            end
                        end
                    end
                    
                elseif feature.name == "magical_spring" then
                    -- Create a magical spring with crystals
                    local radius = math.random(3, 6)
                    
                    for dx = -radius, radius do
                        for dy = -radius, radius do
                            local dist = math.sqrt(dx*dx + dy*dy)
                            if dist <= radius then
                                local tileX = featureX + dx
                                local tileY = featureY + dy
                                
                                if tileX >= 0 and tileX < world.CHUNK_SIZE and
                                   tileY >= 0 and tileY < world.CHUNK_SIZE then
                                    local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                    
                                    if dist < radius * 0.4 then
                                        -- Center pool
                                        chunk.tiles[tileX][tileY].type = "magic_water"
                                        chunk.tiles[tileX][tileY].manaConcentration = math.random(7, 10) / 10
                                    else
                                        -- Crystal border
                                        chunk.tiles[tileX][tileY].type = "crystal_vein"
                                        
                                        -- Add some crystal formations
                                        if math.random() < 0.3 then
                                            local crystalX = chunk.x * world.CHUNK_SIZE + tileX
                                            local crystalY = chunk.y * world.CHUNK_SIZE + tileY
                                            
                                            world.entitySystem:addEntity("crystal_formation", crystalX, crystalY, {
                                                size = math.random(5, 15) / 10,
                                                manaConcentration = math.random(5, 8) / 10
                                            })
                                        end
                                    end
                                end
                            end
                        end
                    end
                    
                    -- Add magical properties
                    local springX = chunk.x * world.CHUNK_SIZE + featureX
                    local springY = chunk.y * world.CHUNK_SIZE + featureY

                    -- Define the elements table separately
                    local elements = {"air", "water", "earth", "spirit"}
                    local chosenElement = elements[math.random(1, #elements)] -- Get random element

                    world.entitySystem:addEntity("mana_spring", springX, springY, {
                        power = math.random(3, 5),
                        rechargeRate = math.random(10, 20) / 10,
                        element = chosenElement -- Assign the chosen element
                    })
                    
                elseif feature.name == "ancient_statue" then
                    -- Create a giant mountain statue
                    local worldX = chunk.x * world.CHUNK_SIZE + featureX
                    local worldY = chunk.y * world.CHUNK_SIZE + featureY
                    
                    -- Define the poses table separately
                    local poses = {"sitting", "standing", "meditative", "guardian"}
                    local chosenPose = poses[math.random(1, #poses)] -- Get random pose
                    
                    -- Add statue entity
                    world.entitySystem:addEntity("mountain_statue", worldX, worldY, {
                        height = math.random(15, 30),
                        age = "ancient",
                        pose = chosenPose, -- Assign the chosen pose
                        weathered = true
                    })
                    
                    -- Create a small plateau or clearing around the statue
                    local clearingRadius = 5
                    for dx = -clearingRadius, clearingRadius do
                        for dy = -clearingRadius, clearingRadius do
                            local dist = math.sqrt(dx*dx + dy*dy)
                            if dist <= clearingRadius then
                                local tileX = featureX + dx
                                local tileY = featureY + dy
                                
                                if tileX >= 0 and tileX < world.CHUNK_SIZE and
                                   tileY >= 0 and tileY < world.CHUNK_SIZE then
                                    local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                    
                                    -- Flatten area around statue for a plateau
                                    local centerElevation = chunk.tiles[featureX][featureY].elevation
                                    chunk.tiles[tileX][tileY].elevation = centerElevation
                                    
                                    -- Set type to ancient stone platform
                                    if dist <= clearingRadius * 0.6 then
                                        chunk.tiles[tileX][tileY].type = "ancient_platform"
                                    else
                                        chunk.tiles[tileX][tileY].type = "ancient_ruins"
                                    end
                                end
                            end
                        end
                    end
                    
                elseif feature.name == "lava_pool" then
                    -- Create a pool of lava
                    local size = math.random(feature.size.min, feature.size.max)
                    local radius = size / 2
                    
                    for dx = -radius, radius do
                        for dy = -radius, radius do
                            local dist = math.sqrt(dx*dx + dy*dy)
                            if dist <= radius then
                                local tileX = featureX + dx
                                local tileY = featureY + dy
                                
                                if tileX >= 0 and tileX < world.CHUNK_SIZE and
                                   tileY >= 0 and tileY < world.CHUNK_SIZE then
                                    local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                    
                                    if dist < radius * 0.8 then
                                        -- Center is lava
                                        chunk.tiles[tileX][tileY].type = "lava_flow"
                                        chunk.tiles[tileX][tileY].temperature = 600 + math.random() * 200
                                        chunk.tiles[tileX][tileY].dangerous = true
                                    else
                                        -- Edges are hot rock
                                        chunk.tiles[tileX][tileY].type = "lava_rock"
                                        chunk.tiles[tileX][tileY].temperature = 100 + math.random() * 50
                                    end
                                end
                            end
                        end
                    end
                    
                    -- Add some fire effects
                    if math.random() < 0.6 then
                        local fireX = chunk.x * world.CHUNK_SIZE + featureX
                        local fireY = chunk.y * world.CHUNK_SIZE + featureY
                        
                        world.entitySystem:addEntity("lava_geyser", fireX, fireY, {
                            height = math.random(2, 5),
                            eruption_frequency = math.random(10, 30)
                        })
                    end
                end
                
                -- Mark unique features as placed
                if feature.unique then
                    if not chunk.uniqueFeatures then
                        chunk.uniqueFeatures = {}
                    end
                    chunk.uniqueFeatures[feature.name] = true
                end
            end
            
            ::continue::
        end
    end,
    
    -- Helper function to add entities
    populateEntities = function(self, chunk, world, variant)
        -- Get variant-specific entity adjustments
        local variantData = variant and self.variants[variant] or nil
        
        -- Mountains have sparser life due to harsh conditions
        local commonCount = math.random(4, 10)
        local uncommonCount = math.random(1, 3)
        local rareCount = math.random(0, 1)
        
        -- Helper function to spawn an entity with variant adjustments
        local function spawnEntity(entityType, count)
            -- Apply variant adjustment if available
            local multiplier = 1.0
            if variantData and variantData.entityAdjustments and variantData.entityAdjustments[entityType] then
                multiplier = variantData.entityAdjustments[entityType]
            end
            
            -- Adjust count based on variant
            local adjustedCount = math.floor(count * multiplier + 0.5)
            
            -- Spawn entities
            for i = 1, adjustedCount do
                -- Pick a random position in the chunk
                local x = math.random(0, world.CHUNK_SIZE - 1)
                local y = math.random(0, world.CHUNK_SIZE - 1)
                local tileIndex = x + y * world.CHUNK_SIZE
                local tile = chunk.tiles[x][y]
                
                -- Check if tile is suitable (not too steep or dangerous)
                if tile.steepness < 0.7 and tile.type ~= "cliff" and tile.type ~= "lava_flow" then
                    local worldX = chunk.x * world.CHUNK_SIZE + x
                    local worldY = chunk.y * world.CHUNK_SIZE + y
                    
                    -- Spawn the entity
                    world.entitySystem:addEntity(entityType, worldX, worldY)
                end
            end
        end
        
        -- Spawn common entities
        for _, entityType in ipairs(self.commonEntities) do
            local count = math.random(1, 3)
            spawnEntity(entityType, count)
        end
        
        -- Spawn uncommon entities
        for _, entityType in ipairs(self.uncommonEntities) do
            local count = math.random(0, 1)
            spawnEntity(entityType, count)
        end
        
        -- Spawn rare entities
        for _, entityType in ipairs(self.rareEntities) do
            if math.random() < 0.15 then -- 15% chance for each rare entity
                spawnEntity(entityType, 1)
            end
        end
        
        -- Add variant-specific entities
        if variant == "volcanic" then
            spawnEntity("fire_salamander", math.random(1, 3))
            spawnEntity("lava_elemental", math.random(0, 1))
            spawnEntity("phoenix", math.random(0, 1))
        elseif variant == "crystal" then
            spawnEntity("crystal_golem", math.random(1, 2))
            spawnEntity("mana_sprite", math.random(2, 5))
        elseif variant == "alpine" then
            spawnEntity("yeti", math.random(0, 1))
            spawnEntity("snow_hare", math.random(2, 4))
        elseif variant == "ancient" then
            spawnEntity("stone_guardian", math.random(1, 2))
            spawnEntity("mountain_spirit", math.random(1, 3))
        end
        
        -- Add flying creatures in sky above mountains
        local skyCreatureCount = math.random(2, 5)
        for i = 1, skyCreatureCount do
            local x = math.random(0, world.CHUNK_SIZE - 1)
            local y = math.random(0, world.CHUNK_SIZE - 1)
            local worldX = chunk.x * world.CHUNK_SIZE + x
            local worldY = chunk.y * world.CHUNK_SIZE + y
            
            local skyCreatures = {"eagle", "hawk", "mountain_drake", "cloud_serpent"}
            local creatureType = skyCreatures[math.random(1, #skyCreatures)]
            
            world.entitySystem:addEntity(creatureType, worldX, worldY, {
                flying = true,
                altitude = math.random(10, 30)
            })
        end
    end,
    
    -- Helper function to place structures
    generateStructures = function(self, chunk, world, variant)
        -- Go through each structure type
        for _, structure in ipairs(self.structures) do
            -- Check chance and variant requirements
            if math.random() < structure.chance and
               (not structure.requiresVariant or structure.requiresVariant == variant) then
                
                -- Find suitable location for structure
                local structX = math.random(3, world.CHUNK_SIZE - 4)
                local structY = math.random(3, world.CHUNK_SIZE - 4)
                
                -- Check if location is suitable (not too steep)
                local tileIndex = structX + structY * world.CHUNK_SIZE
                local tile = chunk.tiles[structX][structY]
                
                if tile.steepness < 0.5 and tile.type ~= "cliff" and tile.type ~= "lava_flow" then
                    local worldX = chunk.x * world.CHUNK_SIZE + structX
                    local worldY = chunk.y * world.CHUNK_SIZE + structY
                    
                    -- Place structure
                    world.chunkSystem:placeStructure(chunk, structure.name, worldX, worldY)
                    
                    -- Add associated entities
                    if structure.entities then
                        for _, entityType in ipairs(structure.entities) do
                            -- Add entity near structure
                            local offsetX = math.random(-2, 2)
                            local offsetY = math.random(-2, 2)
                            world.entitySystem:addEntity(entityType, worldX + offsetX, worldY + offsetY)
                        end
                    end
                    
                    -- Special setup for different structure types
                    if structure.name == "mountain_shrine" then
                        -- Create a small plateau for the shrine
                        local shrineRadius = 4
                        for dx = -shrineRadius, shrineRadius do
                            for dy = -shrineRadius, shrineRadius do
                                local dist = math.sqrt(dx*dx + dy*dy)
                                if dist <= shrineRadius then
                                    local tileX = structX + dx
                                    local tileY = structY + dy
                                    
                                    if tileX >= 0 and tileX < world.CHUNK_SIZE and
                                       tileY >= 0 and tileY < world.CHUNK_SIZE then
                                        local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                        
                                        -- Flatten area
                                        local centerElevation = chunk.tiles[structX][structY].elevation
                                        chunk.tiles[tileX][tileY].elevation = centerElevation
                                        
                                        -- Special shrine tiles
                                        if dist < shrineRadius * 0.7 then
                                            chunk.tiles[tileX][tileY].type = "shrine_stone"
                                        end
                                    end
                                end
                            end
                        end
                        
                        -- Add prayer flags around shrine
                        local flagCount = math.random(4, 8)
                        for i = 1, flagCount do
                            local angle = (i / flagCount) * 2 * math.pi
                            local flagX = worldX + math.cos(angle) * 3
                            local flagY = worldY + math.sin(angle) * 3
                            
                            world.entitySystem:addEntity("prayer_flag", flagX, flagY, {
                                color = math.random(1, 5)
                            })
                        end
                    elseif structure.name == "dragon_lair" then
                        -- Create a cave entrance for the dragon
                        world.entitySystem:addEntity("large_cave_entrance", worldX, worldY, {
                            lairOwner = "dragon",
                            heatSignature = true,
                            dangerLevel = math.random(8, 10)
                        })
                        
                        -- Add bones and treasures around the entrance
                        local debrisCount = math.random(5, 10)
                        for i = 1, debrisCount do
                            local offsetX = math.random(-5, 5)
                            local offsetY = math.random(-5, 5)
                            
                            if math.random() < 0.7 then
                                world.entitySystem:addEntity("bone_pile", worldX + offsetX, worldY + offsetY, {
                                    size = math.random(5, 15) / 10
                                })
                            else
                                world.entitySystem:addEntity("treasure_piece", worldX + offsetX, worldY + offsetY, {
                                    value = math.random(1, 5),
                                    gleaming = true
                                })
                            end
                        end
                    elseif structure.name == "volcano_caldera" then
                        -- Create a volcanic caldera
                        local calderaRadius = math.random(6, 10)
                        for dx = -calderaRadius, calderaRadius do
                            for dy = -calderaRadius, calderaRadius do
                                local dist = math.sqrt(dx*dx + dy*dy)
                                if dist <= calderaRadius then
                                    local tileX = structX + dx
                                    local tileY = structY + dy
                                    
                                    if tileX >= 0 and tileX < world.CHUNK_SIZE and
                                       tileY >= 0 and tileY < world.CHUNK_SIZE then
                                        local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                        
                                        if dist < calderaRadius * 0.4 then
                                            -- Center is lava
                                            chunk.tiles[tileX][tileY].type = "lava_flow"
                                            chunk.tiles[tileX][tileY].temperature = 800
                                            chunk.tiles[tileX][tileY].dangerous = true
                                        elseif dist < calderaRadius * 0.7 then
                                            -- Middle ring is hot rock
                                            chunk.tiles[tileX][tileY].type = "lava_rock"
                                            chunk.tiles[tileX][tileY].temperature = 150
                                        else
                                            -- Outer ring is volcanic stone
                                            chunk.tiles[tileX][tileY].type = "volcanic_stone"
                                            chunk.tiles[tileX][tileY].temperature = 80
                                        end
                                    end
                                end
                            end
                        end
                        
                        -- Add some volcanic effects
                        world.entitySystem:addEntity("volcano", worldX, worldY, {
                            active = true,
                            lastEruption = math.random(10, 100),
                            eruptionChance = 0.001
                        })
                    end
                end
            end
        end
    end,
    
    -- Apply post-processing effects
    postProcess = function(self, chunk, world, variant)
        local variantData = variant and self.variants[variant] or nil
        
        -- Apply variant-specific special effects
        if variantData and variantData.specialEffects then
            chunk.environmentalEffects = chunk.environmentalEffects or {}
            
            for _, effect in ipairs(variantData.specialEffects) do
                table.insert(chunk.environmentalEffects, effect)
            end
        end
        
        -- Apply elevation effects to all mountain tiles
        for _, tile in pairs(chunk.tiles) do
            -- Adjust temperature based on elevation
            local baseTemp = 20 - (tile.elevation / 10)
            local tempOffset = variantData and variantData.temperatureOffset or 0
            tile.temperature = baseTemp + tempOffset + math.random() * 2 - 1
            
            -- Apply movement penalties for steep terrain
            if tile.steepness > 0.5 then
                tile.movementFactor = 0.5 + (1 - tile.steepness) * 0.5
            end
            
            -- Make very steep tiles impassable
            if tile.steepness > 0.9 or tile.type == "cliff" then
                tile.impassable = true
            end
            
            -- Make dangerous tiles actually dangerous
            if tile.type == "lava_flow" then
                tile.damagePerSecond = 10
                tile.damageType = "fire"
            end
            
            -- Add variant-specific tile effects
            if variant == "crystal" and (tile.type == "crystal_vein" or tile.type == "glowing_crystal") then
                tile.manaBoost = math.random(1, 5)
                tile.glowIntensity = math.random(3, 7) / 10
            elseif variant == "volcanic" and (tile.type == "lava_rock" or tile.type == "volcanic_stone") then
                tile.heatDamage = math.random(0, 2)
                tile.smokeEmission = math.random(1, 5) / 10
            elseif variant == "alpine" and tile.type == "snow" then
                tile.slippery = true
                tile.coldDamage = math.random(0, 1)
            end
        end
        
        -- Add weather effects based on elevation
        local averageElevation = 0
        local tileCount = 0
        
        for _, tile in pairs(chunk.tiles) do
            averageElevation = averageElevation + tile.elevation
            tileCount = tileCount + 1
        end
        
        averageElevation = averageElevation / tileCount
        
        -- Higher elevations have more extreme weather
        if averageElevation > 70 then
            chunk.stormFrequency = 0.3
            chunk.windIntensity = 2.0
            chunk.lightningChance = 0.2
        elseif averageElevation > 50 then
            chunk.stormFrequency = 0.2
            chunk.windIntensity = 1.5
            chunk.lightningChance = 0.1
        end
        
        -- Add variant-specific weather
        if variant == "alpine" then
            chunk.snowChance = 0.4
            chunk.avalancheRisk = 0.05
        elseif variant == "volcanic" then
            chunk.ashFallChance = 0.3
            chunk.temperatureVariance = 5.0
        end
    end,
    
    -- Initialize the biome module
    init = function(self, worldCore)
        print("Mountain biome module initialized")
        
        -- Store reference to WorldCore for later use
        self.worldCore = worldCore
        
        -- Don't try to register generators at init time
        -- Instead, we'll register them when a world is created
        
        -- Store the biome in the biomes registry
        -- This allows the module to be used later when worlds are created
        print("Mountain biome registered successfully")
    end,
    
    -- Add a new function to register generators with a specific world instance
    registerWithWorld = function(self, world)
        print("Registering mountain biome generators with world")
        
        if not world or not world.chunkSystem then
            print("Warning: Cannot register mountain biome - world or chunkSystem is nil")
            return false
        end
        
        -- Register generation algorithms with the world
        world.chunkSystem:registerGenerator("mountain", self.generate)
        
        -- Register biome variants
        for variantId, variant in pairs(self.variants) do
            local biome = self -- Create a local reference to access in the closure
            world.chunkSystem:registerGenerator("mountain_" .. variantId, function(chunk, world)
                biome.generate(chunk, world, variantId)
                biome.postProcess(chunk, world, variantId)
            end)
        end
        
        return true
    end
}

return MountainBiome