-- tiles/hub_path.lua
-- Placeholder for Hub Path tile

local HubPathTile = {
    id = "hub_path",
    name = "Hub Path",
    type = "tile",
    
    -- Categories
    categories = {"ground", "path", "hub", "structure"},
    
    -- Properties
    properties = {
        walkable = true,
        swimable = false,
        climbable = false,
        flyable = true,
        diggable = false,
        buildable = false,
        flammable = false,
        movementSpeed = 1.2, -- Faster movement on paths
        durability = 1.0,
        cleanliness = 0.9,
        hasDecorations = true,
        hasLanterns = true,
        hasBenches = true
    },
    
    -- Appearance
    appearance = {
        sprite = "hub_path",
        scale = 1.0,
        animations = {
            "idle",
            "decorated"
        },
        variants = {
            "stone",
            "marble",
            "granite",
            "decorated"
        },
        blendMode = "normal",
        tint = {1.0, 1.0, 1.0},
        alpha = 1.0
    },
    
    -- Sound effects
    sounds = {
        walk = "stone_step_light",
        run = "stone_step_heavy",
        ambient = "hub_ambient"
    },
    
    -- Resources
    resources = {
        stone = {
            type = "material",
            amount = 10,
            regrowthRate = 0,
            harvestable = false
        }
    },
    
    -- Effects
    effects = {
        light = {
            type = "environment",
            duration = 0,
            effects = {
                illumination = true,
                lightRadius = 5
            }
        },
        clean = {
            type = "environment",
            duration = 0,
            effects = {
                cleanliness = true,
                maintenance = true
            }
        }
    },
    
    -- Spawn rules
    spawnRules = {
        decorations = {
            "lantern",
            "bench",
            "flower_pot",
            "statue",
            "fountain"
        },
        npcs = {
            "merchant",
            "guard",
            "citizen",
            "traveler"
        }
    },

    -- Connections
    connects_to = {
        "hub_path",
        "hub_grass",
        "hub_floor",
        "sanctuary_stone",
        "hub_building"
    }
}

-- Initialize the tile
function HubPathTile.init(tile, world)
    -- Initialize properties first
    tile.properties = tile.properties or {}
    
    -- Copy all fields from HubPathTile template to tile instance
    for k, v in pairs(HubPathTile) do
        if type(v) ~= "function" and tile[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                tile[k] = {}
                for subk, subv in pairs(v) do
                    tile[k][subk] = subv
                end
            else
                tile[k] = v
            end
        end
    end

    -- Set random variant
    if tile.appearance and tile.appearance.variants then
        local variants = tile.appearance.variants
        tile.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize path state
    tile.properties.pathState = {
        decorated = math.random() < 0.3, -- 30% chance of being decorated
        clean = true,
        maintained = true
    }

    -- Initialize resources
    if tile.resources then
        for resourceType, resource in pairs(tile.resources) do
            resource.currentAmount = resource.amount
            resource.lastHarvest = 0
            resource.growthStage = "mature"
        end
    end

    return tile
end

-- Update the tile
function HubPathTile.update(tile, world, dt)
    -- Update path state based on environment
    if tile.properties.pathState then
        -- Check for maintenance needs
        if world.time and world.time % 86400 < 3600 then -- Once per day
            tile.properties.pathState.maintained = true
            tile.properties.pathState.clean = true
        end
        
        -- Check for weather effects
        if world.weather and world.weather.type == "rain" then
            tile.properties.pathState.clean = false
        end
    end
end

-- Handle interaction
function HubPathTile.interact(tile, world, entity, action)
    if action == "clean" then
        -- Handle cleaning
        if tile.properties.pathState then
            tile.properties.pathState.clean = true
            return { success = true, message = "Path cleaned." }
        end
    elseif action == "decorate" then
        -- Handle decoration
        if tile.properties.pathState then
            tile.properties.pathState.decorated = true
            return { success = true, message = "Path decorated." }
        end
    end
    
    return { success = false, message = "A well-maintained path in the hub area." }
end

-- Handle walking
function HubPathTile.onWalk(tile, world, entity)
    local effects = {
        sound = tile.sounds.walk,
        volume = 0.8,
        effects = {}
    }
    
    -- Add movement speed bonus
    if entity and entity.moveSpeed then
        entity.moveSpeed = entity.moveSpeed * tile.properties.movementSpeed
    end
    
    return effects
end

return HubPathTile
