-- behaviors/climb.lua
-- Climbing behavior for entities that can climb certain surfaces

local ClimbBehavior = {}  -- create an empty table

ClimbBehavior.id = "climb"
ClimbBehavior.name = "Climb Behavior"

-- Configuration parameters
ClimbBehavior.defaultConfig = {
    climbableTypes = {"ladder", "vine", "wall", "Tree"}, -- Surface types the entity can climb
    climbSpeed = 1.5,              -- Vertical climbing speed
    moveSpeed = 1.0,               -- Horizontal movement speed while climbing
    checkRadius = 1,               -- Radius to check for climbable surfaces
    climbAnimation = "climb",      -- Name of the climbing animation
}

-- Initialize the behavior
function ClimbBehavior.init(entity, customConfig)
    entity.climbState = entity.climbState or {
        isClimbing = false,
        climbable = nil,
        config = {}
    }

    local config = entity.climbState.config
    -- Merge default configuration values
    for k, v in pairs(ClimbBehavior.defaultConfig) do
        config[k] = v
    end
    -- Override with custom configuration if provided
    if customConfig then
        for k, v in pairs(customConfig) do
            config[k] = v
        end
    end
end

-- Update climb behavior
function ClimbBehavior.update(entity, world, dt)
    local state = entity.climbState
    local config = state.config

    -- Check for nearby climbable surfaces
    if not state.isClimbing then
        state.climbable = ClimbBehavior.findClimbable(entity, world)
        if state.climbable then
            state.isClimbing = true
        end
    else
        -- Stop climbing if the surface is no longer there or we've reached the top/bottom
        if not state.climbable or not world.entitySystem.entities[state.climbable.id] then
            state.isClimbing = false
            state.climbable = nil
        else
            -- Continue climbing
            ClimbBehavior.climb(entity, world, dt)
        end
    end
end

-- Find nearby climbable surfaces
function ClimbBehavior.findClimbable(entity, world)
    -- Implementation details...
end

-- Perform climbing movement and animation
function ClimbBehavior.climb(entity, world, dt)
    -- Implementation details...
end

return ClimbBehavior
