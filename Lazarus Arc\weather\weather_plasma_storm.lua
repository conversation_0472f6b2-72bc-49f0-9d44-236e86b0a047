-- weather/plasma_storm.lua
-- Plasma storm weather pattern - intense plasma storms with energy effects

local PlasmaStormWeather = {
    id = "plasma_storm",
    name = "Plasma Storm",
    
    -- Visual properties
    visual = {
        skyColor = {r = 100, g = 50, b = 255}, -- Purple sky
        sunIntensity = 0.2,
        cloudCoverage = 0.9,
        cloudColor = {r = 150, g = 50, b = 255}, -- Purple clouds
        ambientLightLevel = 0.3,
        plasmaColor = {r = 200, g = 100, b = 255} -- Bright plasma
    },
    
    -- Environment modifiers
    environment = {
        temperature = 1.2, -- Warmer due to plasma
        visibility = 0.4, -- Reduced visibility
        humidity = 0.8, -- High humidity
        windStrength = 0.7, -- Strong wind
        plasmaDensity = 0.8, -- High plasma density
        energyLevel = 1.5 -- High energy level
    },
    
    -- Particle systems
    particles = {
        plasma = {
            enabled = true,
            intensity = 0.9,
            color = {r = 200, g = 100, b = 255},
            size = {min = 2, max = 5},
            speed = {min = 30, max = 60},
            image = "assets/particles/Complex/others/plasma_1.png",
            blendMode = "add",
            lifetime = {min = 1.5, max = 3.0},
            rotation = {min = 0, max = 360},
            alpha = {min = 0.4, max = 0.8}
        },
        energy = {
            enabled = true,
            intensity = 0.7,
            color = {r = 150, g = 50, b = 255},
            size = {min = 1, max = 3},
            speed = {min = 20, max = 40},
            image = "assets/particles/Complex/others/energy_1.png",
            blendMode = "add",
            lifetime = {min = 2.0, max = 4.0},
            rotation = {min = 0, max = 360},
            alpha = {min = 0.3, max = 0.6}
        },
        sparks = {
            enabled = true,
            intensity = 0.5,
            color = {r = 255, g = 200, b = 255},
            size = {min = 1, max = 2},
            speed = {min = 40, max = 80},
            image = "assets/particles/Complex/others/spark_1.png",
            blendMode = "add",
            lifetime = {min = 0.5, max = 1.0},
            rotation = {min = 0, max = 360},
            alpha = {min = 0.5, max = 0.9}
        }
    },
    
    -- Sound effects
    sounds = {
        ambient = "plasma_storm_ambient",
        thunder = "plasma_thunder",
        energy = "plasma_energy",
        volume = 0.5
    },
    
    -- Effects on game entities and tiles
    effects = {
        -- Movement effects
        movementSpeedMultiplier = 0.8, -- Reduced speed
        
        -- Vision effects
        visionRange = 0.4, -- Reduced vision
        
        -- Temperature effects
        temperatureModifier = 1.2, -- Warmer
        
        -- Energy effects
        energyDrainRate = 1.5, -- Increased energy drain
        
        -- Shield effects
        shieldEffectiveness = 0.8, -- Reduced shield effectiveness
        
        -- Sensor effects
        sensorRange = 0.5, -- Reduced sensor range
        
        -- Special effects
        plasmaDamage = 0.1, -- Plasma damage
        energyBoost = 1.3 -- Energy boost
    },
    
    -- Transition probabilities to other weather (per game hour)
    transitions = {
        clear = 0.2,
        cloudy = 0.3,
        em_storm = 0.3,
        -- Implied: 0.2 chance to stay plasma storm
    },
    
    -- Day/night cycle modifiers
    timeModifiers = {
        dawn = {
            skyColor = {r = 120, g = 60, b = 255},
            ambientLightLevel = 0.4,
            plasmaIntensity = 0.8 -- Less intense plasma at dawn
        },
        day = {
            -- Uses default properties
        },
        dusk = {
            skyColor = {r = 90, g = 40, b = 255},
            ambientLightLevel = 0.3,
            plasmaIntensity = 1.1 -- More intense plasma at dusk
        },
        night = {
            skyColor = {r = 70, g = 20, b = 255},
            ambientLightLevel = 0.4,
            plasmaIntensity = 1.2 -- More intense plasma at night
        }
    }
}

-- Helper function to safely set particle system properties
local function setupParticleSystem(ps, config)
    if not ps or type(ps) ~= "table" then
        print("Warning: Invalid particle system")
        return false
    end
    
    local methods = {
        setParticleLifetime = function() return ps:setParticleLifetime(config.lifetime.min, config.lifetime.max) end,
        setEmissionRate = function() return ps:setEmissionRate(config.emissionRate or 0) end,
        setSizeVariation = function() return ps:setSizeVariation(config.sizeVariation or 0.3) end,
        setLinearAcceleration = function() return ps:setLinearAcceleration(config.acceleration.x1 or 0, config.acceleration.y1 or 0, config.acceleration.x2 or 0, config.acceleration.y2 or 0) end,
        setColors = function() return ps:setColors(config.colors.r1 or 255, config.colors.g1 or 255, config.colors.b1 or 255, config.colors.a1 or 255, config.colors.r2 or 255, config.colors.g2 or 255, config.colors.b2 or 255, config.colors.a2 or 0) end,
        setBlendMode = function() return ps:setBlendMode(config.blendMode or "normal") end
    }
    
    for methodName, method in pairs(methods) do
        if type(ps[methodName]) ~= "function" then
            print("Warning: Particle system missing method: " .. methodName)
            return false
        end
        
        local success, err = pcall(method)
        if not success then
            print("Warning: Failed to call " .. methodName .. ": " .. tostring(err))
            return false
        end
    end
    
    return true
end

-- Initialize the weather pattern
function PlasmaStormWeather.init(world)
    if not world then
        print("Warning: world parameter is nil in PlasmaStormWeather.init")
        return
    end
    
    print("Initializing plasma storm weather")
    
    -- Set global light level
    world.lightLevel = PlasmaStormWeather.visual.ambientLightLevel
    
    -- Start ambient sounds if weather system exists
    if world.weatherSystem then
        if PlasmaStormWeather.sounds.ambient then
            world.weatherSystem:playSound(PlasmaStormWeather.sounds.ambient, {
                verbose = false,  -- Don't print warnings if sound is missing
                loop = true,      -- Ambient sounds should loop
                volume = 0.5      -- Lower volume for ambient weather
            })
        end
    end
    
    -- Create particle systems
    if PlasmaStormWeather.particles.plasma.enabled then
        -- Create plasma particles
        print("Creating plasma particles with intensity: " .. PlasmaStormWeather.particles.plasma.intensity)
        local success, image = pcall(love.graphics.newImage, PlasmaStormWeather.particles.plasma.image)
        if success and image then
            local success2, ps = pcall(love.graphics.newParticleSystem, image)
            if success2 and ps then
                local config = {
                    lifetime = PlasmaStormWeather.particles.plasma.lifetime,
                    emissionRate = 15,
                    sizeVariation = 0.3,
                    acceleration = {x1 = -10, y1 = -10, x2 = 10, y2 = 10},
                    colors = {r1 = 255, g1 = 255, b1 = 255, a1 = 255, r2 = 200, g2 = 100, b2 = 255, a2 = 0},
                    blendMode = "add"
                }
                
                if setupParticleSystem(ps, config) then
                    PlasmaStormWeather.plasmaSystem = ps
                else
                    print("Warning: Failed to setup plasma particle system")
                end
            else
                print("Warning: Failed to create plasma particle system")
            end
        else
            print("Warning: Could not load plasma particle image: " .. PlasmaStormWeather.particles.plasma.image)
        end
    end
    
    if PlasmaStormWeather.particles.energy.enabled then
        -- Create energy particles
        print("Creating energy particles with intensity: " .. PlasmaStormWeather.particles.energy.intensity)
        local success, image = pcall(love.graphics.newImage, PlasmaStormWeather.particles.energy.image)
        if success and image then
            local success2, ps = pcall(love.graphics.newParticleSystem, image)
            if success2 and ps then
                local config = {
                    lifetime = PlasmaStormWeather.particles.energy.lifetime,
                    emissionRate = 10,
                    sizeVariation = 0.2,
                    acceleration = {x1 = -5, y1 = -5, x2 = 5, y2 = 5},
                    colors = {r1 = 255, g1 = 255, b1 = 255, a1 = 255, r2 = 150, g2 = 50, b2 = 255, a2 = 0},
                    blendMode = "add"
                }
                
                if setupParticleSystem(ps, config) then
                    PlasmaStormWeather.energySystem = ps
                else
                    print("Warning: Failed to setup energy particle system")
                end
            else
                print("Warning: Failed to create energy particle system")
            end
        else
            print("Warning: Could not load energy particle image: " .. PlasmaStormWeather.particles.energy.image)
        end
    end
    
    if PlasmaStormWeather.particles.sparks.enabled then
        -- Create spark particles
        print("Creating spark particles with intensity: " .. PlasmaStormWeather.particles.sparks.intensity)
        local success, image = pcall(love.graphics.newImage, PlasmaStormWeather.particles.sparks.image)
        if success and image then
            local success2, ps = pcall(love.graphics.newParticleSystem, image)
            if success2 and ps then
                local config = {
                    lifetime = PlasmaStormWeather.particles.sparks.lifetime,
                    emissionRate = 20,
                    sizeVariation = 0.2,
                    acceleration = {x1 = -15, y1 = -15, x2 = 15, y2 = 15},
                    colors = {r1 = 255, g1 = 255, b1 = 255, a1 = 255, r2 = 255, g2 = 200, b2 = 255, a2 = 0},
                    blendMode = "add"
                }
                
                if setupParticleSystem(ps, config) then
                    PlasmaStormWeather.sparkSystem = ps
                else
                    print("Warning: Failed to setup spark particle system")
                end
            else
                print("Warning: Failed to create spark particle system")
            end
        else
            print("Warning: Could not load spark particle image: " .. PlasmaStormWeather.particles.sparks.image)
        end
    end
    
    -- Display message to players if message system exists
    if world.messageSystem then
        world.messageSystem:broadcast("Plasma storm detected. High energy readings.")
    else
        print("Plasma storm detected. High energy readings.")
    end
end

-- Update function called every frame
function PlasmaStormWeather.update(world, dt)
    if not world then
        print("Warning: world parameter is nil in PlasmaStormWeather.update")
        return
    end
    
    -- Update day/night cycle based on world time
    local timeOfDay = world.timeOfDay or "day"
    local modifiers = PlasmaStormWeather.timeModifiers[timeOfDay]
    
    if modifiers then
        -- Apply time-specific modifiers
        if modifiers.skyColor then
            -- Change sky color
        end
        
        if modifiers.ambientLightLevel then
            world.lightLevel = modifiers.ambientLightLevel
        end
        
        if modifiers.plasmaIntensity then
            PlasmaStormWeather.particles.plasma.intensity = modifiers.plasmaIntensity
            if PlasmaStormWeather.plasmaSystem then
                PlasmaStormWeather.plasmaSystem:setEmissionRate(15 * modifiers.plasmaIntensity)
            end
        end
    end
    
    -- Update particle systems
    if PlasmaStormWeather.plasmaSystem then
        PlasmaStormWeather.plasmaSystem:update(dt)
    end
    if PlasmaStormWeather.energySystem then
        PlasmaStormWeather.energySystem:update(dt)
    end
    if PlasmaStormWeather.sparkSystem then
        PlasmaStormWeather.sparkSystem:update(dt)
    end
    
    -- Apply effects to game world if entity system exists
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            -- Apply movement speed reduction
            if entity.speed then
                entity.speed = entity.speed * PlasmaStormWeather.effects.movementSpeedMultiplier
            end
            
            -- Apply energy effects
            if entity.energy then
                entity.energy = entity.energy - 
                    (PlasmaStormWeather.effects.energyDrainRate * dt)
                -- Random energy boost
                if math.random() < 0.1 then
                    entity.energy = entity.energy * PlasmaStormWeather.effects.energyBoost
                end
            end
            
            -- Random plasma damage
            if math.random() < 0.05 then
                if entity.health then
                    entity.health = entity.health - 
                        (PlasmaStormWeather.effects.plasmaDamage * dt)
                end
            end
        end
    end
    
    -- Apply weather effects if weather system exists
    if world.weatherSystem then
        world.weatherSystem:applyEffects(PlasmaStormWeather.environment)
    end
end

-- Clean up when weather changes
function PlasmaStormWeather.cleanUp(world)
    if not world then
        print("Warning: world parameter is nil in PlasmaStormWeather.cleanUp")
        return
    end
    
    -- Stop sounds if weather system exists
    if world.weatherSystem then
        if PlasmaStormWeather.sounds.ambient then
            world.weatherSystem:stopSound(PlasmaStormWeather.sounds.ambient)
        end
    end
    
    -- Clean up particle systems
    if PlasmaStormWeather.plasmaSystem then
        PlasmaStormWeather.plasmaSystem:stop()
    end
    if PlasmaStormWeather.energySystem then
        PlasmaStormWeather.energySystem:stop()
    end
    if PlasmaStormWeather.sparkSystem then
        PlasmaStormWeather.sparkSystem:stop()
    end
    
    -- Notify players if message system exists
    if world.messageSystem then
        world.messageSystem:broadcast("Plasma storm clearing. Energy levels stabilizing.")
    else
        print("Plasma storm clearing. Energy levels stabilizing.")
    end
    
    print("Plasma storm weather ending")
end

return PlasmaStormWeather 