-- teleport_system.lua
-- Handles teleportation between player hubs and other locations

local TeleportSystem = {
    initialized = false,
    teleportPads = {}, -- Stores all active teleport pads in the world
    availableHubs = {}, -- List of available hub destinations
    onPlayerTeleportCallback = nil, -- Called when a player teleports
    activeTeleportEffect = nil, -- Currently active teleport effect
    teleportEffectDuration = 1.0, -- Duration of teleport effect in seconds
    padSize = 3, -- Size of teleport pads in tiles
    padActivationDistance = 1.5, -- How close a player needs to be to activate a pad
    padActivationDelay = 0.5, -- How long a player needs to stand on a pad to activate
    activationTimers = {} -- Track activation for each player
}

-- Initialize the teleport system
function TeleportSystem.init(teleportCallback)
    print("Initializing teleport system...")
    
    TeleportSystem.onPlayerTeleportCallback = teleportCallback
    TeleportSystem.teleportPads = {}
    TeleportSystem.availableHubs = {}
    TeleportSystem.activationTimers = {}
    
    TeleportSystem.initialized = true
    print("Teleport system initialized")
    
    return true
end

-- Update teleport system state
function TeleportSystem.update(dt, world)
    if not TeleportSystem.initialized then return end
    
    -- Update any active teleport effect
    if TeleportSystem.activeTeleportEffect then
        TeleportSystem.activeTeleportEffect.timeLeft = TeleportSystem.activeTeleportEffect.timeLeft - dt
        
        if TeleportSystem.activeTeleportEffect.timeLeft <= 0 then
            -- Teleport effect completed, execute the actual teleport
            local effect = TeleportSystem.activeTeleportEffect
            TeleportSystem.activeTeleportEffect = nil
            
            -- Call the teleport callback
            if TeleportSystem.onPlayerTeleportCallback then
                TeleportSystem.onPlayerTeleportCallback(effect.targetHubId)
            end
        end
    end
    
    -- Update teleport pad activation timers
    for playerId, timer in pairs(TeleportSystem.activationTimers) do
        if timer.active then
            timer.timeLeft = timer.timeLeft - dt
            
            if timer.timeLeft <= 0 then
                -- Timer completed, initiate teleport
                TeleportSystem.initiateTeleport(playerId, timer.pad.targetHubId)
                timer.active = false
            end
        end
    end
end

-- Register a hub as available for teleportation
function TeleportSystem.registerHub(hubId, hubName, hubOwner)
    TeleportSystem.availableHubs[hubId] = {
        id = hubId,
        name = hubName or ("Hub " .. hubId),
        owner = hubOwner or nil
    }
    
    print("Registered hub: " .. (hubName or hubId))
    return true
end

-- Create teleport pads for all registered hubs
function TeleportSystem.createHubTeleportPads(world)
    if not TeleportSystem.initialized or not world then return false end
    
    print("Creating hub teleport pads...")
    
    -- Clear existing teleport pads
    TeleportSystem.teleportPads = {}
    
    -- Get the current hub ID
    local currentHubId = world.hubId or "temporary"
    
    -- Create a teleport circle in the hub world
    -- Place teleport pads in a circle around the hub center
    local hubCenterX = 0
    local hubCenterY = 0
    local circleRadius = 8 -- Distance from hub center
    local padCount = 0
    
    -- Count available hubs (excluding current)
    for hubId, _ in pairs(TeleportSystem.availableHubs) do
        if hubId ~= currentHubId then
            padCount = padCount + 1
        end
    end
    
    if padCount == 0 then
        print("No other hubs available for teleportation")
        return true
    end
    
    -- Create pads in a circle around hub center
    local padIndex = 0
    for hubId, hubInfo in pairs(TeleportSystem.availableHubs) do
        -- Skip current hub
        if hubId ~= currentHubId then
            -- Calculate position on the circle
            local angle = (2 * math.pi * padIndex) / padCount
            local padX = hubCenterX + math.cos(angle) * circleRadius
            local padY = hubCenterY + math.sin(angle) * circleRadius
            
            -- Create teleport pad
            TeleportSystem.createTeleportPad(
                world,
                padX,
                padY,
                hubId,
                hubInfo.name
            )
            
            padIndex = padIndex + 1
        end
    end
    
    print("Created " .. padIndex .. " teleport pads")
    return true
end

-- Create a single teleport pad
function TeleportSystem.createTeleportPad(world, x, y, targetHubId, name)
    if not TeleportSystem.initialized or not world then return nil end
    
    -- Generate a unique ID for this pad
    local padId = "pad_" .. x .. "_" .. y .. "_" .. targetHubId
    
    -- Create teleport pad
    local pad = {
        id = padId,
        position = {x = x, y = y},
        targetHubId = targetHubId,
        name = name or ("Teleport to " .. targetHubId),
        active = true,
        size = TeleportSystem.padSize,
        -- Visual properties
        color = {0.3, 0.5, 0.9, 0.8},
        glowColor = {0.4, 0.6, 1.0, 0.4},
        glowSize = TeleportSystem.padSize + 1,
        -- References to game objects (if applicable)
        tileIds = {}, -- Store IDs of tiles that make up this pad
        entityId = nil -- ID of entity representing this pad (if any)
    }
    
    -- Create teleport pad tiles/entity in the world
    -- This would modify the actual tiles in the world
    TeleportSystem.placeTeleportPadInWorld(world, pad)
    
    -- Store the pad
    TeleportSystem.teleportPads[padId] = pad
    
    print("Created teleport pad to " .. targetHubId .. " at " .. x .. "," .. y)
    return pad
end

-- Place teleport pad tiles/entity in the world
function TeleportSystem.placeTeleportPadInWorld(world, pad)
    if not world or not world.chunkSystem then return false end
    
    -- This is a placeholder - in an actual implementation, 
    -- you would modify the world tiles to create a visible teleport pad
    
    -- For example, you might use special "teleport_pad" tiles
    local halfSize = math.floor(pad.size / 2)
    
    for offsetX = -halfSize, halfSize do
        for offsetY = -halfSize, halfSize do
            local tileX = math.floor(pad.position.x) + offsetX
            local tileY = math.floor(pad.position.y) + offsetY
            
            -- Check the distance from center (for circular pad)
            local distance = math.sqrt(offsetX^2 + offsetY^2)
            
            if distance <= halfSize then
                -- Get the existing tile
                local tile = world.chunkSystem:getTileAt(tileX, tileY)
                
                if tile then
                    -- Store original tile type
                    tile.originalType = tile.type
                    
                    -- Change to teleport pad tile
                    tile.type = "teleport_pad"
                    
                    -- Store reference to this tile
                    table.insert(pad.tileIds, tileX .. "," .. tileY)
                end
            end
        end
    end
    
    -- Create a teleport pad entity if needed
    -- This could be a particle effect, glowing object, etc.
    -- The entity would be added to the world's entity system
    
    return true
end

-- Remove teleport pad from the world
function TeleportSystem.removeTeleportPad(world, padId)
    local pad = TeleportSystem.teleportPads[padId]
    if not pad or not world or not world.chunkSystem then return false end
    
    -- Restore original tiles
    for _, tileId in ipairs(pad.tileIds) do
        local x, y = tileId:match("([^,]+),([^,]+)")
        x, y = tonumber(x), tonumber(y)
        
        if x and y then
            local tile = world.chunkSystem:getTileAt(x, y)
            
            if tile and tile.originalType then
                -- Restore original tile type
                tile.type = tile.originalType
                tile.originalType = nil
            end
        end
    end
    
    -- Remove entity if one was created
    if pad.entityId and world.entitySystem then
        world.entitySystem:removeEntity(pad.entityId)
    end
    
    -- Remove pad from registry
    TeleportSystem.teleportPads[padId] = nil
    
    print("Removed teleport pad: " .. padId)
    return true
end

-- Check if a player is standing on a teleport pad
function TeleportSystem.checkTeleportInteraction(player, world)
    if not TeleportSystem.initialized or not player or not world then return false end
    
    -- Skip if teleport effect is already active
    if TeleportSystem.activeTeleportEffect then return false end
    
    -- Get player position
    local playerX = player.position.x
    local playerY = player.position.y
    
    -- Check each teleport pad
    for padId, pad in pairs(TeleportSystem.teleportPads) do
        -- Calculate distance from pad center
        local dx = playerX - pad.position.x
        local dy = playerY - pad.position.y
        local distance = math.sqrt(dx*dx + dy*dy)
        
        -- Check if player is within activation distance
        if distance <= TeleportSystem.padActivationDistance then
            -- Check if this player already has an activation timer
            if not TeleportSystem.activationTimers[player.id] then
                TeleportSystem.activationTimers[player.id] = {
                    active = true,
                    timeLeft = TeleportSystem.padActivationDelay,
                    pad = pad
                }
                
                -- Show message to player
                if world.uiSystem and world.uiSystem.showMessage then
                    world.uiSystem.showMessage("Teleporting to " .. pad.name .. "...", TeleportSystem.padActivationDelay)
                end
                
                print("Player " .. player.id .. " standing on teleport pad to " .. pad.targetHubId)
            end
            
            return true
        else
            -- Reset activation timer if player walks away
            if TeleportSystem.activationTimers[player.id] and 
               TeleportSystem.activationTimers[player.id].pad.id == padId then
                TeleportSystem.activationTimers[player.id].active = false
            end
        end
    end
    
    return false
end

-- Initiate teleport for a player
function TeleportSystem.initiateTeleport(playerId, targetHubId)
    print("Initiating teleport for player " .. playerId .. " to hub " .. targetHubId)
    
    -- Create teleport effect
    TeleportSystem.activeTeleportEffect = {
        playerId = playerId,
        targetHubId = targetHubId,
        timeLeft = TeleportSystem.teleportEffectDuration,
        phase = "start" -- Teleport effect phases: start -> middle -> end
    }
    
    return true
end

-- Cancel a teleport in progress
function TeleportSystem.cancelTeleport(playerId)
    if TeleportSystem.activeTeleportEffect and 
       TeleportSystem.activeTeleportEffect.playerId == playerId then
        TeleportSystem.activeTeleportEffect = nil
        
        print("Teleport cancelled for player " .. playerId)
        return true
    end
    
    if TeleportSystem.activationTimers[playerId] then
        TeleportSystem.activationTimers[playerId].active = false
        print("Teleport activation cancelled for player " .. playerId)
        return true
    end
    
    return false
end

-- Render teleport pads and effects
function TeleportSystem.render(world)
    if not TeleportSystem.initialized or not world then return end
    
    -- Render teleport pads
    for _, pad in pairs(TeleportSystem.teleportPads) do
        -- In a real implementation, this would render visual effects for teleport pads
        -- For LÖVE, you might do something like:
        --[[
        -- Calculate screen position using viewport
        local screenX, screenY = world.viewportManager:worldToScreen(pad.position.x, pad.position.y, 1)
        
        if screenX and screenY then
            -- Render glow effect
            love.graphics.setColor(pad.glowColor)
            love.graphics.circle("fill", screenX, screenY, pad.glowSize * 32 * world.viewportManager.zoomLevel)
            
            -- Render pad
            love.graphics.setColor(pad.color)
            love.graphics.circle("fill", screenX, screenY, pad.size * 32 * world.viewportManager.zoomLevel)
        end
        --]]
    end
    
    -- Render active teleport effect
    if TeleportSystem.activeTeleportEffect then
        -- This would render the teleport effect animation
        -- For now, just a placeholder
    end
end

-- Clean up and shut down teleport system
function TeleportSystem.shutdown()
    if not TeleportSystem.initialized then return end
    
    -- Clear teleport pads
    TeleportSystem.teleportPads = {}
    TeleportSystem.availableHubs = {}
    TeleportSystem.activationTimers = {}
    TeleportSystem.activeTeleportEffect = nil
    
    TeleportSystem.initialized = false
    print("Teleport system shutdown complete")
end

return TeleportSystem
