local SeaTurtle = {
    id = "turtle",
    name = "Green Sea Turtle",
    type = "fish",
    
    -- Entity categories
    categories = {"animal", "fish", "saltwater", "herbivore", "armored"},
    
    -- Threat and food categories
    threatCategories = {"player", "shark"},
    foodCategories = {"seaweed", "algae", "jellyfish"},
    
    -- Stats
    maxHealth = 65,
    health = 65,
    maxStamina = 70,
    stamina = 70,
    speed = 1.2,
    
    -- Water properties
    water = {
        swimSpeed = 1.2,
        maxDepth = 15,
        preferredDepth = 5,
        oxygenLevel = 1.0,
        temperature = "warm",
        currentSpeed = 1.0
    },
    
    -- Behaviors
    behaviors = {"graze", "patrol", "nest", "flee"},
    
    -- Behavior configurations
    behaviorConfigs = {
        graze = {
            moveSpeed = 0.8,
            searchRadius = 10,
            preferredFood = {"seaweed", "algae", "jellyfish"},
            successChance = 0.7,
            armorBonus = 0.2
        },
        patrol = {
            moveSpeed = 1.0,
            patrolRadius = 25,
            preferredDepth = 5,
            restInterval = {30, 40}
        },
        nest = {
            moveSpeed = 1.5,
            searchRadius = 20,
            preferredDepth = 2,
            season = "summer",
            successChance = 0.8
        },
        flee = {
            moveSpeed = 1.8,
            detectionRange = 15,
            preferredDepth = 8,
            staminaCost = 10
        }
    },
    
    -- Special abilities
    abilities = {
        shellDefense = {
            duration = 3,
            cooldown = 6,
            effect = "armor",
            staminaCost = 15
        },
        bite = {
            range = 2,
            damage = 20,
            duration = 0.3,
            cooldown = 2,
            effect = "crush"
        },
        dive = {
            speed = 1.5,
            duration = 2,
            cooldown = 4,
            staminaCost = 10
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "turtle",
        scale = 1.2,
        animations = {
            "idle", "swim", "graze", "dive", "nest"
        },
        variants = {
            "green", "brown", "spotted", "dark"
        }
    },
    
    -- Sound effects
    sounds = {
        splash = "fish_splash",
        swim = "turtle_swim",
        bite = "turtle_bite",
        dive = "turtle_dive"
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.7, quantity = {1, 2}},
        {id = "shell", chance = 0.6, quantity = {1, 1}},
        {id = "scale", chance = 0.4, quantity = {1, 2}},
        {id = "bone", chance = 0.3, quantity = {1, 1}}
    }
}

-- Initialize the entity
function SeaTurtle.init(entity, world)
    -- Copy all fields from SeaTurtle template to entity instance
    for k, v in pairs(SeaTurtle) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random turtle variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function SeaTurtle.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update water properties
    if entity.water then
        -- Adjust grazing success based on time of day
        if world and world.timeOfDay == "dawn" or world.timeOfDay == "dusk" then
            entity.behaviorConfigs.graze.successChance = 0.8
        else
            entity.behaviorConfigs.graze.successChance = 0.7
        end

        -- Adjust preferred depth based on time of day
        if world and world.timeOfDay == "night" then
            entity.water.preferredDepth = 3
        else
            entity.water.preferredDepth = 5
        end

        -- Apply armor bonus when near surface
        if entity.position.y < 3 then
            entity.behaviorConfigs.graze.successChance = 
                entity.behaviorConfigs.graze.successChance + 
                entity.behaviorConfigs.graze.armorBonus
        end

        -- Check nesting conditions
        if world and world.season == "summer" then
            entity.behaviorConfigs.nest.successChance = 0.8
        else
            entity.behaviorConfigs.nest.successChance = 0.0
        end

        -- Adjust fleeing behavior based on threats
        if world and world.entities then
            local nearbyThreats = 0
            for _, other in ipairs(world.entities) do
                if other.type == "fish" and other.id == "shark" then
                    local distance = math.sqrt(
                        (other.position.x - entity.position.x)^2 + 
                        (other.position.y - entity.position.y)^2
                    )
                    if distance < 10 then
                        nearbyThreats = nearbyThreats + 1
                    end
                end
            end
            if nearbyThreats > 0 then
                entity.behaviorConfigs.flee.moveSpeed = 2.0
            else
                entity.behaviorConfigs.flee.moveSpeed = 1.8
            end
        end
    end
end

return SeaTurtle 