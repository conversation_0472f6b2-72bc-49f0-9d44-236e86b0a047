-- items/potions/bread.lua

local Food = {
    id = "bread",
    name = "Bread",
    description = "A loaf of bread that restores a small amount of health and stamina.",
    type = "food",
    category = "consumable",
    stackable = true,
    maxStack = 20,

    stats = {
        health_restore = 10.0,
        stamina_restore = 5.0,
        weight = 0.5,
    },

    sprite = "res://Sprite/items/potions/bread.png",
    size = { width = 32, height = 32 },

    effects = {
        instant_restore = true,
        duration = 0
    },

    meta = {
        rarity = "common"
    },

    onUse = function(self, user)
        if user and user.stats then
            local healthRestored = 0
            local staminaRestored = 0
            
            -- Apply health restoration if applicable
            if user.stats.health then
                local maxHealth = user.stats.maxHealth or 100
                local currentHealth = user.stats.health
                healthRestored = math.min(maxHealth - currentHealth, self.stats.health_restore)
                user.stats.health = currentHealth + healthRestored
            end
            
            -- Apply stamina restoration if applicable
            if user.stats.stamina then
                local maxStamina = user.stats.maxStamina or 100
                local currentStamina = user.stats.stamina
                staminaRestored = math.min(maxStamina - currentStamina, self.stats.stamina_restore)
                user.stats.stamina = currentStamina + staminaRestored
            end
            
            -- Notify the user
            print(user.name .. " ate Bread and recovered " .. healthRestored .. " health and " .. staminaRestored .. " stamina!")
            return true
        end
        return false
    end
}

return Food
