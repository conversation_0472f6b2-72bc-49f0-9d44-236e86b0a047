-- biomes/robot_factory.lua
-- Declare RobotFactoryBiome at the top level so it can be referenced from within functions
local RobotFactoryBiome = {}

-- Define the biome properties
RobotFactoryBiome = {
    id = "robot_factory",
    name = "Automated Factory",
    description = "A sprawling industrial complex operated by autonomous robots, filled with clanking machinery, conveyor belts, and the potential for valuable goods or dangerous encounters.",

    -- Environmental factors (Industrial, hazardous)
    environment = {
        humidity = 0.4,    -- Can be steamy near pipes
        temperature = 0.7, -- Generally hot near machinery
        sunlight = 0.1,    -- Mostly artificial light or gloom
        windStrength = 0.1, -- Internal air circulation, maybe drafts
        airQuality = 0.3,  -- Oil, smoke, ozone, steam
        noiseLevel = 0.9   -- Very noisy
    },

    -- Core tile types used in this biome
    -- NOTE: Needs 'factory_floor', 'conveyor_belt', 'assembly_machine', 'steam_pipe', 'power_generator', 'control_panel', 'scrap_pile', 'robot_charging_station'. Uses 'ruins', 'metal_floor', 'ancient_technology'.
    primaryTiles = {"factory_floor", "conveyor_belt", "metal_floor"}, -- Main walkable/functional areas
    secondaryTiles = {"assembly_machine", "steam_pipe", "scrap_pile", "control_panel"}, -- Interactive/hazard/obstacle tiles
    rareTiles = {"power_generator", "robot_charging_station", "containment_cell", "ruins", "ancient_technology"}, -- Key locations or broken areas

    -- Default proportions (Adjustable through variants)
    tileDistribution = {
        factory_floor = 0.5,
        conveyor_belt = 0.15,
        metal_floor = 0.1,
        assembly_machine = 0.05,
        steam_pipe = 0.05,
        scrap_pile = 0.05,
        control_panel = 0.03,
        power_generator = 0.01,
        robot_charging_station = 0.01,
        containment_cell = 0.005,
        ruins = 0.03,
        ancient_technology = 0.005
    },

    -- Entities common to this biome
    commonEntities = {
        "worker_robot_basic", "maintenance_drone", "scrap_metal_item", "conveyor_item_generic" -- Items moving on belts
    },
    uncommonEntities = {
        "security_robot_patrol", "broken_robot", "factory_foreman_robot", "steam_elemental" -- If steam powered
    },
    rareEntities = {
        "heavy_security_robot", "factory_central_ai", "prototype_robot", "nuclear_core_unstable", "rare_component_item"
    },

    -- Specific biome properties
    isIndustrial = true,
    powerSource = "steam", -- Default: steam, electrical, nuclear
    robotSentience = "programmed", -- programmed, networked, rogue, dormant
    robotHostility = "neutral_unless_provoked", -- neutral, hostile_to_organics, dormant, friendly?

    -- Biome variants for diverse generation
    variants = {
        steam_works = {
            name = "Steam Works",
            powerSource = "steam",
            environment = { humidity=0.7, airQuality=0.2},
            tileAdjustments = { steam_pipe=0.15, factory_floor=0.4, power_generator=0.02 },
            entityAdjustments = { steam_elemental=1.3, maintenance_drone=1.2 },
            specialEffects = {"steam_leaks", "clanking_machinery_sound", "ambient_heat"}
        },
        electrical_plant = {
            name = "Electrical Plant",
            powerSource = "electrical",
            environment = { airQuality=0.4 },
            tileAdjustments = { control_panel=0.05, power_generator=0.03, wire_tile=0.1 }, -- Need wire tile
            entityAdjustments = { security_robot_patrol=1.3, tech_drone=1.0 },
            specialEffects = {"electrical_arcs", "humming_power_sound", "static_discharge"}
        },
        nuclear_core = {
             name = "Nuclear Core Facility",
             powerSource = "nuclear",
             environment = { airQuality = 0.1, radiationLevel = 0.5 }, -- Added radiation hazard
             tileAdjustments = { containment_cell=0.05, power_generator=0.05, heavy_blast_door=0.02 }, -- Need door tile
             entityAdjustments = { heavy_security_robot=1.5, mutated_creature=1.0, prototype_robot=1.1 },
             specialEffects = {"radiation_hazard", "containment_alarms", "flickering_lights"}
        },
        abandoned_factory = {
             name = "Abandoned Factory",
             powerSource = "dormant",
             environment = { noiseLevel=0.2, airQuality=0.6 }, -- Quieter, less fumes
             robotHostility = "dormant_or_hostile_on_wake",
             tileAdjustments = { ruins=0.2, scrap_pile=0.15, factory_floor=0.3, broken_machine=0.1 }, -- Need broken machine tile
             entityAdjustments = { broken_robot=1.8, scavenger_humanoid=1.2, security_robot_patrol=0.3 },
             specialEffects = {"decay", "dust", "occasional_sparks"}
        }
    },

    -- Structures (Key areas within the factory)
    structures = {
        { name = "Assembly Line", chance = 0.4 }, -- Area with machines and conveyors
        { name = "Power Plant", chance = 0.15 }, -- Location of power_generator tiles
        { name = "Storage Warehouse", chance = 0.2 }, -- Area with shelves, crates, potential loot
        { name = "Central Control Room", chance = 0.08 }, -- Area with control_panel tiles, maybe AI core
        { name = "Scrap Yard", chance = 0.15 }, -- Area with scrap_pile tiles
        { name = "Robot Bay / Charging Station", chance = 0.1 } -- Area with robot_charging_station tiles
    },

    -- No weather inside, use environmental effects
    environmentalEffects = {
        ambient_noise = 0.9, -- Base noise level
        machine_vibration = 0.4,
        oil_slick_hazard = 0.1, -- Chance of slippery oil patches
        steam_burst_hazard = 0.05, -- If steam powered
        electrical_hazard = 0.05 -- If electrical powered
    },

    -- Unique features generation
    features = {
        { name = "Main Conveyor Loop", chance = 0.3 }, -- Large conveyor system feature
        { name = "Giant Press Machine", chance = 0.1, danger=true }, -- Hazard/obstacle
        { name = "Item Production Output", chance = 0.15 }, -- Point where items appear (loot source)
        { name = "Locked Vault / Storage", chance = 0.08 }, -- Requires key/hacking
        { name = "Prototype Chamber", chance = 0.05, requiresVariant="!abandoned_factory" }, -- Contains prototype robot
        { name = "Central AI Core Room", chance = 0.02, unique=true } -- Location of AI entity?
    },

    -- Generation algorithm (Complex - Placeholder)
    generate = function(chunk, world, variant)
        print("Generating " .. (variant and RobotFactoryBiome.variants[variant].name or "Automated Factory") .. " biome")
        local variantData = variant and RobotFactoryBiome.variants[variant] or nil
        local powerSource = variantData and variantData.powerSource or RobotFactoryBiome.powerSource

        -- Adjust tile distribution
        local tileDistribution = {}
        for tileType, chance in pairs(RobotFactoryBiome.tileDistribution) do
            tileDistribution[tileType] = chance
            if variantData and variantData.tileAdjustments and variantData.tileAdjustments[tileType] then
                tileDistribution[tileType] = variantData.tileAdjustments[tileType]
            end
        end

        -- Noise functions
        local seed = world.seed + (chunk.x * 677 + chunk.y * 109)
        math.randomseed(seed)
        local function pnoise2D(x, y, frequency, seed_offset) return love.math.noise(x * frequency, y * frequency, (world.seed + (seed_offset or 0)) * 0.01) end

        -- Initialize 2D tiles array if not exists
        chunk.tiles = chunk.tiles or {}
        for x = 0, world.CHUNK_SIZE - 1 do
            chunk.tiles[x] = chunk.tiles[x] or {}
        end

        -- Generate tiles
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                local worldX = chunk.x * world.CHUNK_SIZE + x
                local worldY = chunk.y * world.CHUNK_SIZE + y

                local structureNoise = pnoise2D(worldX, worldY, 0.08, 1) -- Rough room/corridor layout
                local machineNoise = pnoise2D(worldX, worldY, 0.2, 2) -- Machine placement
                local hazardNoise = pnoise2D(worldX, worldY, 0.3, 3) -- Pipes, scrap, hazards

                local tileType = "factory_floor" -- Default
                local passable = true

                if structureNoise < -0.5 or structureNoise > 0.5 then -- Define walls crudely
                     tileType = "building_wall" -- Use existing wall or factory specific?
                     passable = false
                elseif machineNoise > 0.6 then
                     tileType = "assembly_machine"
                     passable = false -- Machines block path
                elseif hazardNoise > 0.7 then
                     tileType = "steam_pipe" -- Or electrical conduit, based on variant
                     passable = false -- Pipes block path
                elseif hazardNoise < -0.7 then
                     tileType = "scrap_pile"
                     -- Passable but slow? tile.movementSpeed = 0.7
                end

                 -- Conveyors might follow paths or machine noise contours
                 if tileType == "factory_floor" and math.abs(machineNoise) > 0.4 and math.abs(machineNoise) < 0.6 then
                      tileType = "conveyor_belt"
                      -- Conveyor tile needs direction property
                 end

                -- Apply distribution for rare tiles
                -- ... (simplified override logic) ...

                -- Create the tile
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = math.random(1, 2),
                    isIndustrial = true,
                    passable = passable,
                    isUnderground = false -- Assume surface level unless specified
                }

                -- Add to chunk using 2D indexing
                chunk.tiles[x][y] = tile
            end
        end

        -- Call helper functions
        RobotFactoryBiome.generateFeatures(chunk, world, variant)
        RobotFactoryBiome.populateEntities(chunk, world, variant)
        RobotFactoryBiome.generateStructures(chunk, world, variant)
        RobotFactoryBiome.postProcess(chunk, world, variant)
    end,

    -- Helper function implementations (Placeholders - Need detail)
    generateFeatures = function(chunk, world, variant)
        print("Generating Factory features...")
        -- Place conveyor systems, large machines, item outputs, vaults, AI core location
    end,

    populateEntities = function(chunk, world, variant)
        print("Populating Factory entities...")
        local variantData = variant and RobotFactoryBiome.variants[variant] or nil
        local hostility = variantData and variantData.robotHostility or RobotFactoryBiome.robotHostility

        local function spawnEntity(entityType, count)
             local multiplier = 1.0
             if variantData and variantData.entityAdjustments and variantData.entityAdjustments[entityType] then multiplier = variantData.entityAdjustments[entityType] end
             local adjustedCount = math.floor(count * multiplier + 0.5)
             for i = 1, adjustedCount do
                 local attempts = 0; local placed = false
                 while attempts < 5 and not placed do
                      local x = math.random(0, world.CHUNK_SIZE - 1)
                      local y = math.random(0, world.CHUNK_SIZE - 1)
                      local tile = chunk.tiles[x][y]
                      if tile and tile.passable then -- Ensure spawning on passable tile
                           -- Apply hostility settings to robots
                           local props = {}
                           if string.find(entityType, "robot") then props.hostility = hostility end
                           world.entitySystem:addEntity(entityType, chunk.x * world.CHUNK_SIZE + x, chunk.y * world.CHUNK_SIZE + y, props)
                           placed = true
                      end
                      attempts = attempts + 1
                 end
             end
         end
        -- Spawn based on lists
        for _, et in ipairs(RobotFactoryBiome.commonEntities) do spawnEntity(et, math.random(6,15)) end -- Factories are busy
        for _, et in ipairs(RobotFactoryBiome.uncommonEntities) do spawnEntity(et, math.random(3,8)) end
        for _, et in ipairs(RobotFactoryBiome.rareEntities) do if math.random() < 0.08 then spawnEntity(et, 1) end end
    end,

    generateStructures = function(chunk, world, variant)
        print("Generating Factory structures...")
        for _, structure in ipairs(RobotFactoryBiome.structures) do
            if math.random() < structure.chance then
                if not structure.requiresVariant or structure.requiresVariant == variant then
                    local attempts = 0; local placed = false; local sx, sy
                    while attempts < 10 and not placed do
                        sx = math.random(2, world.CHUNK_SIZE - 3)
                        sy = math.random(2, world.CHUNK_SIZE - 3)
                        local tile = chunk.tiles[sx][sy]
                        if tile and tile.passable and tile.type == "factory_floor" then placed = true end
                        attempts = attempts + 1
                    end
                    if placed then
                        local worldX = chunk.x * world.CHUNK_SIZE + sx
                        local worldY = chunk.y * world.CHUNK_SIZE + sy
                        print("Marking structure area: "..structure.name.." at "..worldX..","..worldY)
                        world.entitySystem:addEntity("structure_poi", worldX, worldY, { 
                            structureType = structure.name, 
                            description="The "..structure.name.." is centered here." 
                        })
                    end
                end
            end
        end
    end,

    postProcess = function(chunk, world, variant)
        print("Post-processing Factory chunk...")
        -- Apply ambient noise, hazard effects, link conveyors/power?
        local variantData = variant and RobotFactoryBiome.variants[variant] or nil
        chunk.environmentalEffects = chunk.environmentalEffects or {}
        table.insert(chunk.environmentalEffects, {type="ambient_noise", sound="factory_machinery", level=RobotFactoryBiome.environment.noiseLevel})
        if variant == "nuclear_core" then table.insert(chunk.environmentalEffects, {type="radiation", level=0.5}) end

        if variantData and variantData.specialEffects then
             for _, effect in ipairs(variantData.specialEffects) do table.insert(chunk.environmentalEffects, effect) end
         end
         -- Maybe connect conveyor belts based on neighbours?
    end,

    init = function(worldCore)
        print("Robot Factory biome module initialized")
        RobotFactoryBiome.worldCore = worldCore
        print("Robot Factory biome registered successfully for later use")
    end,

    registerWithWorld = function(world)
        print("Registering Robot Factory biome generators with world")
        if not world or not world.chunkSystem then print("Warning: Cannot register Robot Factory biome - world or chunkSystem is nil") return false end
        world.chunkSystem:registerGenerator(RobotFactoryBiome.id, RobotFactoryBiome.generate)
        for variantId, variantData in pairs(RobotFactoryBiome.variants) do
            local fullVariantId = RobotFactoryBiome.id .. "_" .. variantId
            world.chunkSystem:registerGenerator(fullVariantId, function(chunk, worldInstance)
                RobotFactoryBiome.generate(chunk, worldInstance, variantId)
            end)
        end
        return true
    end,
}

return RobotFactoryBiome