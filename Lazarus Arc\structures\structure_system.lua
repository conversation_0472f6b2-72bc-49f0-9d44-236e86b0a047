-- structure_system.lua
-- Manages structure instances in the world
-- Handles structure creation, updates, and state management

local UUID = require("utils.uuid")
local StructureComponents = require("structures.structure_components")
local StructureSystem = {}
StructureSystem.__index = StructureSystem

-- Create a new StructureSystem instance
function StructureSystem.new()
    local self = setmetatable({}, StructureSystem)
    self.structures = {}   -- Stores structures by their UUID
    self.updateQueue = {} -- Queue for parallel structure updates
    self.batchSize = 10   -- Number of structures to update in parallel
    return self
end

-- Create a new structure instance
-- Returns the structure's UUID
function StructureSystem:createStructure(structureType, x, y, options)
    if not structureType then
        error("Cannot create structure without type")
    end
    
    -- Load structure template
    local template = require("structures." .. structureType)
    if not template then
        error("Invalid structure type: " .. structureType)
    end
    
    -- Create structure instance
    local structure = {
        uuid = UUID.generate(),
        type = structureType,
        x = x,
        y = y,
        options = options or {},
        created = os.time(),
        lastUpdate = os.time(),
        components = {} -- Store structure components
    }
    
    -- Initialize structure using template
    if template.init then
        template.init(structure, self.world, x, y)
    end
    
    -- Initialize components based on template
    if template.components then
        for _, componentData in ipairs(template.components) do
            local component = StructureComponents.createComponent(componentData.type, componentData.options)
            structure.components[componentData.id] = component
        end
    end
    
    -- Store structure
    self.structures[structure.uuid] = structure
    
    return structure.uuid
end

-- Get a structure by its UUID
function StructureSystem:getStructure(uuid)
    return self.structures[uuid]
end

-- Remove a structure
function StructureSystem:removeStructure(uuid)
    if self.structures[uuid] then
        self.structures[uuid] = nil
        return true
    end
    return false
end

-- Apply damage to a structure component
function StructureSystem:applyDamageToComponent(structure, componentId, damage, damageType)
    local component = structure.components[componentId]
    if not component then
        return {
            success = false,
            message = "Component not found"
        }
    end
    
    local result = StructureComponents.applyDamage(component, damage, damageType)
    
    -- Update structure's overall condition based on component status
    if result.newStatus == "destroyed" then
        -- Check if critical components are destroyed
        local criticalComponents = 0
        local totalComponents = 0
        for _, comp in pairs(structure.components) do
            totalComponents = totalComponents + 1
            if comp.status == "destroyed" then
                criticalComponents = criticalComponents + 1
            end
        end
        
        -- If more than 50% of components are destroyed, mark structure as destroyed
        if criticalComponents / totalComponents > 0.5 then
            structure.status = "destroyed"
        end
    end
    
    return {
        success = true,
        result = result
    }
end

-- Repair a structure component
function StructureSystem:repairComponent(structure, componentId, material, amount)
    local component = structure.components[componentId]
    if not component then
        return {
            success = false,
            message = "Component not found"
        }
    end
    
    local result = StructureComponents.repair(component, material, amount)
    
    -- Update structure's overall condition based on component repairs
    if result.success then
        local allComponentsIntact = true
        for _, comp in pairs(structure.components) do
            if comp.status ~= "intact" then
                allComponentsIntact = false
                break
            end
        end
        
        if allComponentsIntact then
            structure.status = "intact"
        end
    end
    
    return result
end

-- Get structure's overall status
function StructureSystem:getStructureStatus(structure)
    local status = {
        overall = structure.status or "intact",
        components = {}
    }
    
    for componentId, component in pairs(structure.components) do
        status.components[componentId] = {
            type = component.type,
            status = component.status,
            health = component.health,
            maxHealth = component.maxHealth,
            effects = StructureComponents.getStatusEffects(component)
        }
    end
    
    return status
end

-- Update a single structure
function StructureSystem:updateStructure(structure, dt)
    local template = require("structures." .. structure.type)
    if template and template.update then
        template.update(structure, self.world, dt)
    end
    
    -- Update components
    for _, component in pairs(structure.components) do
        -- Apply environmental damage if needed
        if component.status ~= "destroyed" then
            local envDamage = self:calculateEnvironmentalDamage(structure, component)
            if envDamage > 0 then
                StructureComponents.applyDamage(component, envDamage, "environmental")
            end
        end
    end
    
    structure.lastUpdate = os.time()
end

-- Calculate environmental damage for a component
function StructureSystem:calculateEnvironmentalDamage(structure, component)
    local damage = 0
    
    -- Check weather effects
    if self.world and self.world.weather then
        local weather = self.world.weather
        if weather.type == "storm" then
            if component.type == "ROOF" or component.type == "WINDOW" then
                damage = damage + 0.1
            end
        elseif weather.type == "acid_rain" then
            if component.type == "WALL" or component.type == "ROOF" then
                damage = damage + 0.2
            end
        end
    end
    
    -- Check biome effects
    if self.world and self.world.getBiomeAt then
        local biome = self.world.getBiomeAt(structure.x, structure.y)
        if biome == "volcanic" then
            if component.type == "WALL" or component.type == "SUPPORT" then
                damage = damage + 0.15
            end
        elseif biome == "swamp" then
            if component.type == "WOOD" or component.type == "DOOR" then
                damage = damage + 0.1
            end
        end
    end
    
    return damage
end

-- Update structures in parallel batches
function StructureSystem:update(dt)
    -- Queue all structures for update
    for _, structure in pairs(self.structures) do
        table.insert(self.updateQueue, structure)
    end
    
    -- Process structures in batches
    while #self.updateQueue > 0 do
        local batch = {}
        for i = 1, math.min(self.batchSize, #self.updateQueue) do
            table.insert(batch, table.remove(self.updateQueue))
        end
        
        -- Process batch in parallel
        local threads = {}
        for _, structure in ipairs(batch) do
            local thread = coroutine.create(function()
                self:updateStructure(structure, dt)
            end)
            table.insert(threads, thread)
            coroutine.resume(thread)
        end
        
        -- Wait for all threads in batch to complete
        for _, thread in ipairs(threads) do
            while coroutine.status(thread) ~= "dead" do
                coroutine.resume(thread)
            end
        end
    end
end

-- Serialize structure data for saving
function StructureSystem:serialize()
    local data = {}
    for uuid, structure in pairs(self.structures) do
        data[uuid] = {
            type = structure.type,
            x = structure.x,
            y = structure.y,
            options = structure.options,
            created = structure.created,
            lastUpdate = structure.lastUpdate,
            status = structure.status,
            components = {}
        }
        
        -- Serialize components
        for componentId, component in pairs(structure.components) do
            data[uuid].components[componentId] = StructureComponents.serialize(component)
        end
    end
    return data
end

-- Deserialize structure data
function StructureSystem:deserialize(data)
    local threads = {}
    
    -- Create threads for parallel deserialization
    for uuid, structureData in pairs(data) do
        local thread = coroutine.create(function()
            local structure = {
                uuid = uuid,
                type = structureData.type,
                x = structureData.x,
                y = structureData.y,
                options = structureData.options,
                created = structureData.created,
                lastUpdate = structureData.lastUpdate,
                status = structureData.status,
                components = {}
            }
            
            -- Reinitialize structure using template
            local template = require("structures." .. structure.type)
            if template and template.init then
                template.init(structure, self.world, structure.x, structure.y)
            end
            
            -- Deserialize components
            for componentId, componentData in pairs(structureData.components) do
                structure.components[componentId] = StructureComponents.deserialize(componentData)
            end
            
            self.structures[uuid] = structure
        end)
        table.insert(threads, thread)
        coroutine.resume(thread)
    end
    
    -- Wait for all deserialization threads to complete
    for _, thread in ipairs(threads) do
        while coroutine.status(thread) ~= "dead" do
            coroutine.resume(thread)
        end
    end
end

-- Query structures using a filter function (async)
function StructureSystem:query(filterFn)
    local result = {}
    local threads = {}
    
    -- Create threads for parallel filtering
    for _, structure in pairs(self.structures) do
        local thread = coroutine.create(function()
            if filterFn(structure) then
                table.insert(result, structure)
            end
        end)
        table.insert(threads, thread)
        coroutine.resume(thread)
    end
    
    -- Wait for all filter threads to complete
    for _, thread in ipairs(threads) do
        while coroutine.status(thread) ~= "dead" do
            coroutine.resume(thread)
        end
    end
    
    return result
end

-- Get structures by type (async)
function StructureSystem:getStructuresByType(typeName)
    return self:query(function(structure)
        return structure.type == typeName
    end)
end

-- Get structures in an area (async)
function StructureSystem:getStructuresInArea(x, y, radius)
    return self:query(function(structure)
        local dx = structure.x - x
        local dy = structure.y - y
        return dx * dx + dy * dy <= radius * radius
    end)
end

return StructureSystem 