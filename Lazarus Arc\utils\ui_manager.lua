-- ui_manager.lua
-- Handles UI elements and rendering for Lazarus Arc

local love = love -- Cache the love module

local UI = {
    initialized = false,
    screenWidth = 1280,
    screenHeight = 720,
    scale = 1.0,
    fonts = {},
    colors = {},
    elements = {},
    messages = {},
    activeScreen = nil,
    showFullMap = false,
    miniMapSize = 150,
    miniMapPosition = {x = 20, y = 20},
    isInputActive = false,
    inputCallback = nil,
    inputText = "",
    inputPrompt = "",
    activePrompt = nil,
    healthBarWidth = 200,
    healthBarHeight = 20,
    manaBarWidth = 200,
    manaBarHeight = 20,
    barSpacing = 5,
    barPosition = {x = 20, y = 20},
    usePixelPerfect = true,
    
    -- Coordinate display settings
    coordsDisplay = {
        enabled = true,
        visible = true,
        position = {x = 10, y = 10},
        backgroundColor = {0, 0, 0, 0.7},
        textColor = {1, 1, 1, 1},
        font = nil,
        currentCoords = nil
    },
    
    -- Debug console settings
    debugConsole = {
        enabled = true,
        visible = true,
        position = {x = 10, y = 40},  -- Moved down to make room for coords
        width = 400,
        height = 200,
        maxLines = 50,
        lines = {},
        backgroundColor = {0, 0, 0, 0.7},
        textColor = {1, 1, 1, 1},
        warningColor = {1, 0.8, 0, 1},
        errorColor = {1, 0, 0, 1},
        font = nil
    }
}

-- Initialize the UI system
function UI.init(screenWidth, screenHeight)
    print("Initializing UI system...")
    
    UI.screenWidth = screenWidth or love.graphics.getWidth()
    UI.screenHeight = screenHeight or love.graphics.getHeight()
    
    -- Load fonts
    UI.fonts.small = love.graphics.newFont(12)
    UI.fonts.regular = love.graphics.newFont(16)
    UI.fonts.medium = love.graphics.newFont(20)
    UI.fonts.large = love.graphics.newFont(24)
    UI.fonts.title = love.graphics.newFont(32)
    
    -- Initialize fonts for displays
    UI.coordsDisplay.font = UI.fonts.small
    UI.debugConsole.font = UI.fonts.small
    
    -- Define color palette
    UI.colors = {
        background = {0.1, 0.1, 0.12, 1},
        text = {0.9, 0.9, 0.9, 1},
        textHighlight = {1, 1, 1, 1},
        textDim = {0.7, 0.7, 0.7, 1},
        primary = {0.2, 0.6, 0.9, 1},
        secondary = {0.8, 0.3, 0.9, 1},
        success = {0.3, 0.8, 0.4, 1},
        warning = {0.9, 0.7, 0.2, 1},
        danger = {0.9, 0.3, 0.3, 1},
        health = {0.9, 0.2, 0.2, 1},
        mana = {0.2, 0.4, 0.9, 1},
        stamina = {0.2, 0.9, 0.4, 1},
        overlay = {0, 0, 0, 0.7},
        mapBackground = {0.15, 0.15, 0.2, 0.85},
        mapBorder = {0.5, 0.5, 0.6, 1},
        playerMarker = {1, 1, 1, 1},
        enemyMarker = {0.9, 0.3, 0.3, 1},
        npcMarker = {0.3, 0.9, 0.3, 1},
        messageBackground = {0, 0, 0, 0.6},
        pinPromptBackground = {0.1, 0.1, 0.1, 0.9}
    }
    
    -- Register base UI elements
    UI.elements = {
        statusBars = true,
        miniMap = true,
        messages = true,
        inventoryButton = {x = UI.screenWidth - 50, y = UI.screenHeight - 50, width = 40, height = 40},
        mapButton = {x = UI.screenWidth - 100, y = UI.screenHeight - 50, width = 40, height = 40}
    }
    
    -- Calculate bar positions
    UI.updateBarPositions()
    
    UI.initialized = true
    print("UI system initialized")
    
    return true
end

-- Update UI positions based on screen size
function UI.updateBarPositions()
    -- Player health/mana bars at bottom left
    UI.barPosition = {
        x = 20,
        y = UI.screenHeight - (UI.healthBarHeight + UI.manaBarHeight + UI.barSpacing + 20)
    }
    
    -- Mini-map at top right
    UI.miniMapPosition = {
        x = UI.screenWidth - UI.miniMapSize - 20,
        y = 20
    }
end

-- Update UI state
function UI.update(dt)
    if not UI.initialized then return end
    
    -- Update message timers and remove expired messages
    for i = #UI.messages, 1, -1 do
        UI.messages[i].timeLeft = UI.messages[i].timeLeft - dt
        
        if UI.messages[i].timeLeft <= 0 then
            table.remove(UI.messages, i)
        end
    end
    
    -- Update any animations or UI transitions
    -- (Would implement animation system here)
end

-- Render the UI
function UI.render(players)
    if not UI.initialized then return end
    
    -- Set default font
    love.graphics.setFont(UI.fonts.regular)
    
    -- Draw coordinates display
    UI:renderCoordsDisplay()
    
    -- Draw debug console
    UI:renderDebugConsole()
    
    -- Draw player status bars
    UI.renderStatusBars(players)
    
    -- Draw mini-map
    if UI.elements.miniMap then
        UI.renderMiniMap()
    end
    
    -- Draw messages
    UI.renderMessages()
    
    -- Draw active screen (if any)
    if UI.activeScreen then
        UI.renderScreen(UI.activeScreen)
    end
    
    -- Draw full map overlay if active
    if UI.showFullMap then
        UI.renderFullMap()
    end
    
    -- Draw any active prompts
    if UI.activePrompt then
        UI.renderPrompt(UI.activePrompt)
    end
    
    -- Draw text input if active
    if UI.isInputActive then
        UI.renderTextInput()
    end
    
    -- Reset color and font to default
    love.graphics.setColor(1, 1, 1, 1)
    love.graphics.setFont(UI.fonts.regular)
end

-- Render player status bars (health, mana, etc.)
function UI.renderStatusBars(players)
    if not players or #players == 0 then return end
    
    -- Get player 1 (main player) for now
    local player = players[1]
    if not player or not player.character then return end
    
    local character = player.character
    
    -- Draw health bar
    if character.stats and character.stats.health then
        local x = UI.barPosition.x
        local y = UI.barPosition.y
        local width = UI.healthBarWidth
        local height = UI.healthBarHeight
        local healthPercent = character.stats.health / 100 -- Assuming max health is 100
        
        -- Draw background
        love.graphics.setColor(0.2, 0.2, 0.2, 0.8)
        love.graphics.rectangle("fill", x, y, width, height)
        
        -- Draw health bar
        love.graphics.setColor(UI.colors.health)
        love.graphics.rectangle("fill", x, y, width * healthPercent, height)
        
        -- Draw border
        love.graphics.setColor(0.4, 0.4, 0.4, 1)
        love.graphics.rectangle("line", x, y, width, height)
        
        -- Draw text
        love.graphics.setColor(UI.colors.text)
        love.graphics.setFont(UI.fonts.small)
        love.graphics.printf(
            string.format("Health: %.1f/100", character.stats.health),
            x, y + (height - UI.fonts.small:getHeight()) / 2,
            width, "center"
        )
    end
    
    -- Draw mana bar
    if character.stats and character.stats.mana then
        local x = UI.barPosition.x
        local y = UI.barPosition.y + UI.healthBarHeight + UI.barSpacing
        local width = UI.manaBarWidth
        local height = UI.manaBarHeight
        local manaPercent = character.stats.mana / 100 -- Assuming max mana is 100
        
        -- Draw background
        love.graphics.setColor(0.2, 0.2, 0.2, 0.8)
        love.graphics.rectangle("fill", x, y, width, height)
        
        -- Draw mana bar
        love.graphics.setColor(UI.colors.mana)
        love.graphics.rectangle("fill", x, y, width * manaPercent, height)
        
        -- Draw border
        love.graphics.setColor(0.4, 0.4, 0.4, 1)
        love.graphics.rectangle("line", x, y, width, height)
        
        -- Draw text
        love.graphics.setColor(UI.colors.text)
        love.graphics.setFont(UI.fonts.small)
        love.graphics.printf(
            string.format("Mana: %.1f/100", character.stats.mana),
            x, y + (height - UI.fonts.small:getHeight()) / 2,
            width, "center"
        )
    end
    
    -- Could add more bars (stamina, etc.) using the same pattern
end

-- Render mini-map
function UI.renderMiniMap()
    local x = UI.miniMapPosition.x
    local y = UI.miniMapPosition.y
    local size = UI.miniMapSize
    
    -- Draw background
    love.graphics.setColor(UI.colors.mapBackground)
    love.graphics.rectangle("fill", x, y, size, size)
    
    -- Draw border
    love.graphics.setColor(UI.colors.mapBorder)
    love.graphics.rectangle("line", x, y, size, size)
    
    -- Here we would draw the actual map content
    -- This is a placeholder for the actual map rendering
    -- which would show the nearby tiles and entities
    
    -- Draw player location (center)
    love.graphics.setColor(UI.colors.playerMarker)
    love.graphics.circle("fill", x + size/2, y + size/2, 4)
    
    -- Sample: Draw some random entities
    love.graphics.setColor(UI.colors.enemyMarker)
    love.graphics.circle("fill", x + size/2 + 20, y + size/2 - 15, 3)
    
    love.graphics.setColor(UI.colors.npcMarker)
    love.graphics.circle("fill", x + size/2 - 30, y + size/2 + 25, 3)
    
    -- Draw mini-map label
    love.graphics.setColor(UI.colors.text)
    love.graphics.setFont(UI.fonts.small)
    love.graphics.print("Map", x + 5, y + 5)
end

-- Render full map overlay
function UI.renderFullMap()
    -- Draw semi-transparent background
    love.graphics.setColor(UI.colors.overlay)
    love.graphics.rectangle("fill", 0, 0, UI.screenWidth, UI.screenHeight)
    
    -- Draw map background
    local mapWidth = UI.screenWidth * 0.8
    local mapHeight = UI.screenHeight * 0.8
    local mapX = (UI.screenWidth - mapWidth) / 2
    local mapY = (UI.screenHeight - mapHeight) / 2
    
    love.graphics.setColor(UI.colors.mapBackground)
    love.graphics.rectangle("fill", mapX, mapY, mapWidth, mapHeight)
    
    -- Draw map border
    love.graphics.setColor(UI.colors.mapBorder)
    love.graphics.rectangle("line", mapX, mapY, mapWidth, mapHeight)
    
    -- Here we would draw the actual map content
    -- This is a placeholder for the actual map rendering
    -- which would show a larger area than the mini-map
    
    -- Draw player location
    love.graphics.setColor(UI.colors.playerMarker)
    love.graphics.circle("fill", mapX + mapWidth/2, mapY + mapHeight/2, 6)
    
    -- Draw map title
    love.graphics.setColor(UI.colors.text)
    love.graphics.setFont(UI.fonts.medium)
    love.graphics.printf("World Map", mapX, mapY + 10, mapWidth, "center")
    
    -- Draw instructions
    love.graphics.setFont(UI.fonts.small)
    love.graphics.printf(
        "Press 'M' to close map",
        mapX, mapY + mapHeight - 30,
        mapWidth, "center"
    )
end

-- Render messages
function UI.renderMessages()
    if #UI.messages == 0 then return end
    
    local messageHeight = 30
    local padding = 10
    local startY = 50
    
    for i, message in ipairs(UI.messages) do
        -- Calculate alpha (fade in/out)
        local alpha = 1.0
        if message.timeLeft < 0.5 then
            alpha = message.timeLeft / 0.5
        elseif message.duration - message.timeLeft < 0.5 then
            alpha = (message.duration - message.timeLeft) / 0.5
        end
        
        -- Draw message background
        love.graphics.setColor(UI.colors.messageBackground[1], 
                              UI.colors.messageBackground[2], 
                              UI.colors.messageBackground[3], 
                              UI.colors.messageBackground[4] * alpha)
        
        -- Calculate width based on text length
        local textWidth = UI.fonts.regular:getWidth(message.text) + padding * 2
        local x = (UI.screenWidth - textWidth) / 2
        local y = startY + (i-1) * (messageHeight + 5)
        
        love.graphics.rectangle("fill", x, y, textWidth, messageHeight)
        
        -- Draw message text
        love.graphics.setColor(UI.colors.text[1], 
                              UI.colors.text[2], 
                              UI.colors.text[3], 
                              UI.colors.text[4] * alpha)
        
        love.graphics.setFont(UI.fonts.regular)
        love.graphics.printf(
            message.text,
            x + padding, y + (messageHeight - UI.fonts.regular:getHeight()) / 2,
            textWidth - padding * 2, "center"
        )
    end
end

-- Render an active screen (inventory, character sheet, etc.)
function UI.renderScreen(screenName)
    -- Draw semi-transparent background overlay
    love.graphics.setColor(UI.colors.overlay)
    love.graphics.rectangle("fill", 0, 0, UI.screenWidth, UI.screenHeight)
    
    -- Render the appropriate screen
    if screenName == "inventory" then
        UI.renderInventoryScreen()
    elseif screenName == "character" then
        UI.renderCharacterScreen()
    elseif screenName == "settings" then
        UI.renderSettingsScreen()
    end
end

-- Render inventory screen
function UI.renderInventoryScreen()
    -- Draw the inventory panel
    local panelWidth = UI.screenWidth * 0.7
    local panelHeight = UI.screenHeight * 0.8
    local x = (UI.screenWidth - panelWidth) / 2
    local y = (UI.screenHeight - panelHeight) / 2
    
    -- Draw panel background
    love.graphics.setColor(UI.colors.background)
    love.graphics.rectangle("fill", x, y, panelWidth, panelHeight)
    
    -- Draw panel border
    love.graphics.setColor(UI.colors.primary)
    love.graphics.rectangle("line", x, y, panelWidth, panelHeight)
    
    -- Draw title
    love.graphics.setFont(UI.fonts.title)
    love.graphics.setColor(UI.colors.text)
    love.graphics.printf("Inventory", x, y + 20, panelWidth, "center")
    
    -- Draw inventory slots (placeholder)
    love.graphics.setFont(UI.fonts.regular)
    love.graphics.printf(
        "Inventory items would be displayed here",
        x + 20, y + panelHeight / 2,
        panelWidth - 40, "center"
    )
    
    -- Draw close button
    local buttonWidth = 100
    local buttonHeight = 40
    local buttonX = x + (panelWidth - buttonWidth) / 2
    local buttonY = y + panelHeight - buttonHeight - 20
    
    love.graphics.setColor(UI.colors.primary)
    love.graphics.rectangle("fill", buttonX, buttonY, buttonWidth, buttonHeight)
    
    love.graphics.setColor(UI.colors.text)
    love.graphics.printf(
        "Close",
        buttonX, buttonY + (buttonHeight - UI.fonts.regular:getHeight()) / 2,
        buttonWidth, "center"
    )
end

-- Render character screen
function UI.renderCharacterScreen()
    -- Similar to inventory screen but with character stats
    local panelWidth = UI.screenWidth * 0.7
    local panelHeight = UI.screenHeight * 0.8
    local x = (UI.screenWidth - panelWidth) / 2
    local y = (UI.screenHeight - panelHeight) / 2
    
    -- Draw panel background
    love.graphics.setColor(UI.colors.background)
    love.graphics.rectangle("fill", x, y, panelWidth, panelHeight)
    
    -- Draw panel border
    love.graphics.setColor(UI.colors.secondary)
    love.graphics.rectangle("line", x, y, panelWidth, panelHeight)
    
    -- Draw title
    love.graphics.setFont(UI.fonts.title)
    love.graphics.setColor(UI.colors.text)
    love.graphics.printf("Character", x, y + 20, panelWidth, "center")
    
    -- Draw character stats (placeholder)
    love.graphics.setFont(UI.fonts.regular)
    love.graphics.printf(
        "Character stats would be displayed here",
        x + 20, y + panelHeight / 2,
        panelWidth - 40, "center"
    )
    
    -- Draw close button
    local buttonWidth = 100
    local buttonHeight = 40
    local buttonX = x + (panelWidth - buttonWidth) / 2
    local buttonY = y + panelHeight - buttonHeight - 20
    
    love.graphics.setColor(UI.colors.secondary)
    love.graphics.rectangle("fill", buttonX, buttonY, buttonWidth, buttonHeight)
    
    love.graphics.setColor(UI.colors.text)
    love.graphics.printf(
        "Close",
        buttonX, buttonY + (buttonHeight - UI.fonts.regular:getHeight()) / 2,
        buttonWidth, "center"
    )
end

-- Render settings screen
function UI.renderSettingsScreen()
    -- Settings screen
    local panelWidth = UI.screenWidth * 0.7
    local panelHeight = UI.screenHeight * 0.8
    local x = (UI.screenWidth - panelWidth) / 2
    local y = (UI.screenHeight - panelHeight) / 2
    
    -- Draw panel background
    love.graphics.setColor(UI.colors.background)
    love.graphics.rectangle("fill", x, y, panelWidth, panelHeight)
    
    -- Draw panel border
    love.graphics.setColor(UI.colors.primary)
    love.graphics.rectangle("line", x, y, panelWidth, panelHeight)
    
    -- Draw title
    love.graphics.setFont(UI.fonts.title)
    love.graphics.setColor(UI.colors.text)
    love.graphics.printf("Settings", x, y + 20, panelWidth, "center")
    
    -- Draw settings options (placeholder)
    love.graphics.setFont(UI.fonts.regular)
    love.graphics.printf(
        "Game settings would be displayed here",
        x + 20, y + panelHeight / 2,
        panelWidth - 40, "center"
    )
    
    -- Draw close button
    local buttonWidth = 100
    local buttonHeight = 40
    local buttonX = x + (panelWidth - buttonWidth) / 2
    local buttonY = y + panelHeight - buttonHeight - 20
    
    love.graphics.setColor(UI.colors.primary)
    love.graphics.rectangle("fill", buttonX, buttonY, buttonWidth, buttonHeight)
    
    love.graphics.setColor(UI.colors.text)
    love.graphics.printf(
        "Close",
        buttonX, buttonY + (buttonHeight - UI.fonts.regular:getHeight()) / 2,
        buttonWidth, "center"
    )
end

-- Render pause menu
function UI.renderPauseMenu()
    -- Draw semi-transparent overlay
    love.graphics.setColor(UI.colors.overlay)
    love.graphics.rectangle("fill", 0, 0, UI.screenWidth, UI.screenHeight)
    
    -- Draw pause menu panel
    local panelWidth = 300
    local panelHeight = 400
    local x = (UI.screenWidth - panelWidth) / 2
    local y = (UI.screenHeight - panelHeight) / 2
    
    love.graphics.setColor(UI.colors.background)
    love.graphics.rectangle("fill", x, y, panelWidth, panelHeight)
    
    love.graphics.setColor(UI.colors.primary)
    love.graphics.rectangle("line", x, y, panelWidth, panelHeight)
    
    -- Draw pause title
    love.graphics.setFont(UI.fonts.title)
    love.graphics.setColor(UI.colors.text)
    love.graphics.printf("Paused", x, y + 30, panelWidth, "center")
    
    -- Draw menu options
    local options = {
        {text = "Resume", action = "resume"},
        {text = "Settings", action = "settings"},
        {text = "Save Game", action = "save"},
        {text = "Exit Game", action = "exit"}
    }
    
    local optionHeight = 50
    local optionWidth = 200
    local startY = y + 120
    
    love.graphics.setFont(UI.fonts.medium)
    
    for i, option in ipairs(options) do
        local optionX = x + (panelWidth - optionWidth) / 2
        local optionY = startY + (i-1) * (optionHeight + 10)
        
        -- Draw button background
        love.graphics.setColor(UI.colors.primary)
        love.graphics.rectangle("fill", optionX, optionY, optionWidth, optionHeight)
        
        -- Draw button text
        love.graphics.setColor(UI.colors.text)
        love.graphics.printf(
            option.text,
            optionX, optionY + (optionHeight - UI.fonts.medium:getHeight()) / 2,
            optionWidth, "center"
        )
    end
end

-- Render a PIN entry prompt
function UI.renderPINPrompt()
    -- Draw semi-transparent overlay
    love.graphics.setColor(UI.colors.overlay)
    love.graphics.rectangle("fill", 0, 0, UI.screenWidth, UI.screenHeight)
    
    -- Draw PIN prompt panel
    local panelWidth = 300
    local panelHeight = 200
    local x = (UI.screenWidth - panelWidth) / 2
    local y = (UI.screenHeight - panelHeight) / 2
    
    love.graphics.setColor(UI.colors.pinPromptBackground)
    love.graphics.rectangle("fill", x, y, panelWidth, panelHeight)
    
    love.graphics.setColor(UI.colors.primary)
    love.graphics.rectangle("line", x, y, panelWidth, panelHeight)
    
    -- Draw title
    love.graphics.setFont(UI.fonts.medium)
    love.graphics.setColor(UI.colors.text)
    love.graphics.printf("Enter PIN", x, y + 20, panelWidth, "center")
    
    -- Draw PIN field
    local fieldWidth = 200
    local fieldHeight = 40
    local fieldX = x + (panelWidth - fieldWidth) / 2
    local fieldY = y + 70
    
    love.graphics.setColor(0.2, 0.2, 0.2)
    love.graphics.rectangle("fill", fieldX, fieldY, fieldWidth, fieldHeight)
    
    love.graphics.setColor(UI.colors.text)
    love.graphics.rectangle("line", fieldX, fieldY, fieldWidth, fieldHeight)
    
    -- Draw PIN text (masked with asterisks)
    local maskedText = string.rep("*", #UI.inputText)
    love.graphics.setFont(UI.fonts.medium)
    love.graphics.printf(
        maskedText,
        fieldX + 10, fieldY + (fieldHeight - UI.fonts.medium:getHeight()) / 2,
        fieldWidth - 20, "left"
    )
    
    -- Draw confirm button
    local buttonWidth = 100
    local buttonHeight = 40
    local buttonX = x + (panelWidth - buttonWidth) / 2
    local buttonY = y + panelHeight - buttonHeight - 20
    
    love.graphics.setColor(UI.colors.primary)
    love.graphics.rectangle("fill", buttonX, buttonY, buttonWidth, buttonHeight)
    
    love.graphics.setColor(UI.colors.text)
    love.graphics.printf(
        "Confirm",
        buttonX, buttonY + (buttonHeight - UI.fonts.regular:getHeight()) / 2,
        buttonWidth, "center"
    )
end

-- Render text input
function UI.renderTextInput()
    -- Draw input background
    local width = 400
    local height = 40
    local x = (UI.screenWidth - width) / 2
    local y = UI.screenHeight / 2
    
    love.graphics.setColor(0.2, 0.2, 0.2, 0.9)
    love.graphics.rectangle("fill", x, y, width, height)
    
    love.graphics.setColor(UI.colors.primary)
    love.graphics.rectangle("line", x, y, width, height)
    
    -- Draw prompt text
    love.graphics.setColor(UI.colors.text)
    love.graphics.setFont(UI.fonts.regular)
    love.graphics.print(UI.inputPrompt, x, y - 30)
    
    -- Draw input text
    love.graphics.setColor(UI.colors.text)
    love.graphics.print(UI.inputText, x + 10, y + 10)
    
    -- Draw cursor
    local cursorX = x + 10 + UI.fonts.regular:getWidth(UI.inputText)
    love.graphics.setColor(UI.colors.text)
    if math.floor(love.timer.getTime() * 2) % 2 == 0 then
        love.graphics.rectangle("fill", cursorX, y + 10, 2, UI.fonts.regular:getHeight())
    end
end

-- Render generic prompt
function UI.renderPrompt(prompt)
    -- Draw semi-transparent overlay
    love.graphics.setColor(UI.colors.overlay)
    love.graphics.rectangle("fill", 0, 0, UI.screenWidth, UI.screenHeight)
    
    -- Draw prompt panel
    local panelWidth = 400
    local panelHeight = 200
    local x = (UI.screenWidth - panelWidth) / 2
    local y = (UI.screenHeight - panelHeight) / 2
    
    love.graphics.setColor(UI.colors.background)
    love.graphics.rectangle("fill", x, y, panelWidth, panelHeight)
    
    love.graphics.setColor(UI.colors.primary)
    love.graphics.rectangle("line", x, y, panelWidth, panelHeight)
    
    -- Draw title
    love.graphics.setFont(UI.fonts.medium)
    love.graphics.setColor(UI.colors.text)
    love.graphics.printf(prompt.title, x, y + 20, panelWidth, "center")
    
    -- Draw message
    love.graphics.setFont(UI.fonts.regular)
    love.graphics.printf(
        prompt.message,
        x + 20, y + 70,
        panelWidth - 40, "center"
    )
    
    -- Draw buttons
    local buttonWidth = 120
    local buttonHeight = 40
    local buttonSpacing = 20
    local totalButtonWidth = #prompt.buttons * buttonWidth + (#prompt.buttons - 1) * buttonSpacing
    local startX = x + (panelWidth - totalButtonWidth) / 2
    local buttonY = y + panelHeight - buttonHeight - 20
    
    for i, button in ipairs(prompt.buttons) do
        local buttonX = startX + (i-1) * (buttonWidth + buttonSpacing)
        
        love.graphics.setColor(button.color or UI.colors.primary)
        love.graphics.rectangle("fill", buttonX, buttonY, buttonWidth, buttonHeight)
        
        love.graphics.setColor(UI.colors.text)
        love.graphics.printf(
            button.text,
            buttonX, buttonY + (buttonHeight - UI.fonts.regular:getHeight()) / 2,
            buttonWidth, "center"
        )
    end
end

-- Show a temporary message
function UI.showMessage(text, duration)
    duration = duration or 3.0
    
    table.insert(UI.messages, {
        text = text,
        duration = duration,
        timeLeft = duration
    })
end

-- Show PIN prompt
function UI.showPINPrompt(callback)
    UI.isInputActive = true
    UI.inputText = ""
    UI.inputPrompt = "Enter PIN:"
    UI.inputCallback = function(text)
        UI.isInputActive = false
        UI.inputText = ""
        
        if callback then
            callback(text)
        end
    end
    
    -- Start handling text input
    if love.keyboard then
        love.keyboard.setTextInput(true)
    end
end

-- Show a generic prompt
function UI.showPrompt(title, message, buttons, callback)
    UI.activePrompt = {
        title = title,
        message = message,
        buttons = buttons,
        callback = callback
    }
end

-- Toggle full map display
function UI.toggleFullMap()
    UI.showFullMap = not UI.showFullMap
end

-- Show a specific screen
function UI.showScreen(screenName)
    UI.activeScreen = screenName
end

-- Close the active screen
function UI.closeScreen()
    UI.activeScreen = nil
end

-- Handle screen resize
function UI.resize(width, height)
    UI.screenWidth = width
    UI.screenHeight = height
    
    -- Update UI element positions
    UI.updateBarPositions()
    
    print("UI resized to " .. width .. "x" .. height)
end

-- Handle text input
function UI.textInput(text)
    if UI.isInputActive then
        UI.inputText = UI.inputText .. text
    end
end

-- Handle key press for input
function UI.keyPressed(key)
    if UI.isInputActive then
        if key == "backspace" then
            -- Remove the last character
            UI.inputText = string.sub(UI.inputText, 1, -2)
        elseif key == "return" then
            -- Confirm input
            local text = UI.inputText
            if UI.inputCallback then
                UI.inputCallback(text)
            end
        elseif key == "escape" then
-- Cancel input
            UI.isInputActive = false
            if UI.inputCallback then
                UI.inputCallback(nil)
            end
        end
    elseif UI.activePrompt then
        if key == "escape" then
            -- Cancel prompt
            local callback = UI.activePrompt.callback
            UI.activePrompt = nil
            
            if callback then
                callback(nil)
            end
        end
    elseif UI.showFullMap and key == "m" then
        -- Close full map with M key
        UI.showFullMap = false
    elseif UI.activeScreen then
        if key == "escape" then
            -- Close active screen with escape
            UI.closeScreen()
        end
    end
end

-- Handle mouse press
function UI.mousePressed(x, y, button)
    -- Check if clicking on a button in a prompt
    if UI.activePrompt then
        local panelWidth = 400
        local panelHeight = 200
        local panelX = (UI.screenWidth - panelWidth) / 2
        local panelY = (UI.screenHeight - panelHeight) / 2
        
        local buttonWidth = 120
        local buttonHeight = 40
        local buttonSpacing = 20
        local totalButtonWidth = #UI.activePrompt.buttons * buttonWidth + (#UI.activePrompt.buttons - 1) * buttonSpacing
        local startX = panelX + (panelWidth - totalButtonWidth) / 2
        local buttonY = panelY + panelHeight - buttonHeight - 20
        
        for i, button in ipairs(UI.activePrompt.buttons) do
            local buttonX = startX + (i-1) * (buttonWidth + buttonSpacing)
            
            if x >= buttonX and x <= buttonX + buttonWidth and
               y >= buttonY and y <= buttonY + buttonHeight then
                -- Button clicked
                local callback = UI.activePrompt.callback
                local buttonAction = button.action
                UI.activePrompt = nil
                
                if callback then
                    callback(buttonAction)
                end
                
                return true
            end
        end
        
        return true -- Consume the click if it's anywhere in the prompt overlay
    end
    
    -- Check if clicking on a button in a screen
    if UI.activeScreen then
        -- Example: Check for close button in inventory
        if UI.activeScreen == "inventory" or UI.activeScreen == "character" or UI.activeScreen == "settings" then
            local panelWidth = UI.screenWidth * 0.7
            local panelHeight = UI.screenHeight * 0.8
            local panelX = (UI.screenWidth - panelWidth) / 2
            local panelY = (UI.screenHeight - panelHeight) / 2
            
            local buttonWidth = 100
            local buttonHeight = 40
            local buttonX = panelX + (panelWidth - buttonWidth) / 2
            local buttonY = panelY + panelHeight - buttonHeight - 20
            
            if x >= buttonX and x <= buttonX + buttonWidth and
               y >= buttonY and y <= buttonY + buttonHeight then
                -- Close button clicked
                UI.closeScreen()
                return true
            end
        end
        
        return true -- Consume the click if it's in an active screen
    end
    
    -- Check if clicking on mini-map to toggle full map
    if UI.elements.miniMap then
        local mapX = UI.miniMapPosition.x
        local mapY = UI.miniMapPosition.y
        local mapSize = UI.miniMapSize
        
        if x >= mapX and x <= mapX + mapSize and
           y >= mapY and y <= mapY + mapSize then
            -- Mini-map clicked
            UI.toggleFullMap()
            return true
        end
    end
    
    -- Check if clicking on inventory button
    if UI.elements.inventoryButton then
        local button = UI.elements.inventoryButton
        if x >= button.x and x <= button.x + button.width and
           y >= button.y and y <= button.y + button.height then
            -- Inventory button clicked
            UI.showScreen("inventory")
            return true
        end
    end
    
    -- Check if clicking on map button
    if UI.elements.mapButton then
        local button = UI.elements.mapButton
        if x >= button.x and x <= button.x + button.width and
           y >= button.y and y <= button.y + button.height then
            -- Map button clicked
            UI.toggleFullMap()
            return true
        end
    end
    
    return false -- Didn't handle the click
end

-- Check if a point is within UI elements
function UI.isPointOverUI(x, y)
    -- Check active screens and prompts first
    if UI.activePrompt or UI.activeScreen or UI.showFullMap or UI.isInputActive then
        return true
    end
    
    -- Check mini-map
    if UI.elements.miniMap then
        local mapX = UI.miniMapPosition.x
        local mapY = UI.miniMapPosition.y
        local mapSize = UI.miniMapSize
        
        if x >= mapX and x <= mapX + mapSize and
           y >= mapY and y <= mapY + mapSize then
            return true
        end
    end
    
    -- Check status bars
    local barX = UI.barPosition.x
    local barY = UI.barPosition.y
    local barWidth = UI.healthBarWidth
    local totalBarHeight = UI.healthBarHeight + UI.manaBarHeight + UI.barSpacing
    
    if x >= barX and x <= barX + barWidth and
       y >= barY and y <= barY + totalBarHeight then
        return true
    end
    
    -- Check inventory button
    if UI.elements.inventoryButton then
        local button = UI.elements.inventoryButton
        if x >= button.x and x <= button.x + button.width and
           y >= button.y and y <= button.y + button.height then
            return true
        end
    end
    
    -- Check map button
    if UI.elements.mapButton then
        local button = UI.elements.mapButton
        if x >= button.x and x <= button.x + button.width and
           y >= button.y and y <= button.y + button.height then
            return true
        end
    end
    
    return false
end

-- Clean up and shutdown UI system
function UI.shutdown()
    -- Close any open screens or prompts
    UI.closeScreen()
    UI.activePrompt = nil
    UI.isInputActive = false
    UI.showFullMap = false
    
    -- Clear messages
    UI.messages = {}
    
    -- End text input mode if active
    if love.keyboard and love.keyboard.hasTextInput() then
        love.keyboard.setTextInput(false)
    end
    
    print("UI system shutdown complete")
end

-- Love2D callbacks for UI system
function love.textinput(text)
    if UI.initialized and UI.isInputActive then
        UI.textInput(text)
    end
end

-- Update coordinates display
function UI:updateCoords(x, y)
    if not self.coordsDisplay.enabled then return end
    self.coordsDisplay.currentCoords = string.format("Pos: %.1f, %.1f", x, y)
end

-- Render coordinates display
function UI:renderCoordsDisplay()
    if not self.coordsDisplay.enabled or not self.coordsDisplay.visible then return end
    
    local display = self.coordsDisplay
    local x, y = display.position.x, display.position.y
    
    -- Draw background
    love.graphics.setColor(display.backgroundColor)
    local textWidth = display.font:getWidth(display.currentCoords or "") + 20
    local textHeight = display.font:getHeight() + 10
    love.graphics.rectangle("fill", x, y, textWidth, textHeight)
    
    -- Draw border
    love.graphics.setColor(1, 1, 1, 0.3)
    love.graphics.rectangle("line", x, y, textWidth, textHeight)
    
    -- Draw coordinates
    if display.currentCoords then
        love.graphics.setColor(display.textColor)
        love.graphics.setFont(display.font)
        love.graphics.print(display.currentCoords, x + 10, y + 5)
    end
end

-- Add a debug message to the console
function UI:addDebugMessage(message, type)
    if not self.debugConsole.enabled then return end
    
    -- Skip coordinate messages as they're handled separately
    if message:match("Pos:") then return end
    
    -- Skip chunk and tile-related debug spam
    if message:match("getChunkAt") or 
       message:match("getTileAt") or 
       message:match("getChunkKey") or
       message:match("chunk coordinates") then
        return
    end
    
    type = type or "info"
    local color = self.debugConsole.textColor
    
    if type == "warning" then
        color = self.debugConsole.warningColor
    elseif type == "error" then
        color = self.debugConsole.errorColor
    end
    
    -- Add timestamp
    local timestamp = os.date("%H:%M:%S")
    local fullMessage = string.format("[%s] %s", timestamp, message)
    
    -- Add to lines array
    table.insert(self.debugConsole.lines, {
        text = fullMessage,
        color = color
    })
    
    -- Keep only the last maxLines messages
    while #self.debugConsole.lines > self.debugConsole.maxLines do
        table.remove(self.debugConsole.lines, 1)
    end
end

-- Render the debug console
function UI:renderDebugConsole()
    if not self.debugConsole.enabled or not self.debugConsole.visible then return end
    
    local console = self.debugConsole
    local x, y = console.position.x, console.position.y
    
    -- Draw background
    love.graphics.setColor(console.backgroundColor)
    love.graphics.rectangle("fill", x, y, console.width, console.height)
    
    -- Draw border
    love.graphics.setColor(1, 1, 1, 0.3)
    love.graphics.rectangle("line", x, y, console.width, console.height)
    
    -- Set font
    love.graphics.setFont(console.font)
    
    -- Draw messages
    local lineHeight = console.font:getHeight() + 2
    local currentY = y + 5
    
    for i, line in ipairs(console.lines) do
        love.graphics.setColor(line.color)
        love.graphics.print(line.text, x + 5, currentY)
        currentY = currentY + lineHeight
    end
end

-- Export the UI module
return UI