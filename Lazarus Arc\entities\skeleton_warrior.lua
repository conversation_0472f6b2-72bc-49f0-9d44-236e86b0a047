-- entities/skeleton_warrior.lua
-- Undead skeleton warrior with enhanced system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local SkeletonWarrior = {
    id = "skeleton_warrior",
    name = "Skeleton Warrior",
    type = "skeleton_warrior",
    shape = {
        {0, -1}, {0.7, -0.7}, {1, 0}, {0.7, 0.7},
        {0, 1}, {-0.7, 0.7}, {-1, 0}, {-0.7, -0.7}
    },
    size = 8,

    -- Entity categories
    categories = {"undead", "humanoid", "warrior", "cursed"},
    threatCategories = {"player", "living", "holy"},
    
    -- Base stats (balanced undead warrior)
    maxHealth = 80,
    health = 80,
    maxStamina = 60,
    stamina = 60,
    speed = 2.0,
    attack = 20,
    defense = 12,
    undeadResistance = 12,
    holyVulnerability = 2.0,
    
    -- Behaviors
    behaviors = {"guard_undead", "patrol_area", "combat_formation"},
    behaviorConfigs = {
        guard_undead = {
            guardRadius = 15,
            alertRadius = 20,
            pursuitDistance = 25,
            returnToPost = true
        },
        patrol_area = {
            moveSpeed = 1.8,
            patrolRadius = 12,
            systematic = true,
            never_rest = true
        },
        combat_formation = {
            formationBonus = 1.3,
            shieldWall = true,
            coordinated_attacks = true
        }
    },
    
    -- Enhanced variant system for skeleton warriors
    variantChances = {
        normal = 0.70,          -- 70% skeleton soldier
        shiny = 0.20,           -- 20% skeleton knight (shiny)
        rare = 0.08,            -- 8% skeleton captain (rare)
        legendary = 0.02        -- 2% lich king (legendary)
    },
    
    variants = {
        normal = {
            name = "Skeleton Soldier",
            description = "A basic undead warrior with sword and shield",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.9, 0.9, 0.8, 1.0},  -- Bone white
                equipment = "basic"
            }
        },
        
        shiny = {
            name = "Skeleton Knight",
            description = "An armored undead knight with enhanced combat skills",
            statModifiers = {
                maxHealth = 1.5,    -- 120 health
                speed = 1.2,        -- 2.4 speed
                attack = 1.4,       -- 28 attack
                defense = 1.6,      -- 19.2 defense
                armor_mastery = 1.5
            },
            appearanceModifiers = {
                scale = 1.1,
                glow = true,
                colorTint = {1.0, 1.0, 1.2, 1.0},  -- Polished bone with blue glow
                heavy_armor = true,
                enchanted_weapons = true,
                knight_plume = true
            },
            soundModifiers = {
                pitch = 0.9,
                volume = 1.2,
                armor_clank = true
            }
        },
        
        rare = {
            name = "Skeleton Captain",
            description = "A commanding undead officer leading skeleton legions",
            statModifiers = {
                maxHealth = 1.8,    -- 144 health
                speed = 1.3,        -- 2.6 speed
                attack = 1.6,       -- 32 attack
                defense = 1.8,      -- 21.6 defense
                leadership = 2.5,
                tactical_genius = 2.0
            },
            appearanceModifiers = {
                scale = 1.2,
                colorTint = {1.1, 0.9, 0.7, 1.0},  -- Golden bone with command insignia
                captain_armor = true,
                command_banner = true,
                officer_insignia = true,
                battle_scars = true
            },
            soundModifiers = {
                pitch = 0.8,
                volume = 1.4,
                reverb = true,
                commanding_voice = true
            }
        },
        
        legendary = {
            name = "Lich King",
            description = "An ancient undead ruler with immense necromantic power",
            statModifiers = {
                maxHealth = 2.5,    -- 200 health
                speed = 1.4,        -- 2.8 speed
                attack = 2.0,       -- 40 attack
                defense = 2.2,      -- 26.4 defense
                necromantic_mastery = 10.0,
                royal_authority = 5.0
            },
            appearanceModifiers = {
                scale = 1.4,
                glow = true,
                colorTint = {0.8, 0.4, 1.0, 1.0},  -- Purple necromantic energy
                lich_crown = true,
                royal_robes = true,
                staff_of_power = true,
                necromantic_aura = "legendary",
                floating_runes = true
            },
            soundModifiers = {
                pitch = 0.6,
                volume = 1.8,
                reverb = true,
                echo = true,
                royal_authority = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "bone_fragment", chance = 0.9, quantity = {2, 4}},
        {id = "rusty_weapon", chance = 0.6, quantity = {1, 1}},
        {id = "tattered_armor", chance = 0.5, quantity = {1, 1}},
        {id = "dark_essence", chance = 0.7, quantity = {1, 2}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "knight_armor_piece", chance = 0.8, quantity = {1, 2}},
            {id = "enchanted_sword", chance = 0.7, quantity = {1, 1}},
            {id = "knight_shield", chance = 0.6, quantity = {1, 1}},
            {id = "honor_essence", chance = 0.5, quantity = {1, 1}}
        },
        rare = {
            {id = "captain_insignia", chance = 0.9, quantity = {1, 1}},
            {id = "command_banner", chance = 0.8, quantity = {1, 1}},
            {id = "tactical_manual", chance = 0.7, quantity = {1, 1}},
            {id = "leadership_essence", chance = 0.6, quantity = {1, 1}},
            {id = "officer_sword", chance = 0.5, quantity = {1, 1}}
        },
        legendary = {
            {id = "lich_king_crown", chance = 0.95, quantity = {1, 1}},
            {id = "necromantic_staff", chance = 0.9, quantity = {1, 1}},
            {id = "royal_undead_seal", chance = 0.8, quantity = {1, 1}},
            {id = "lich_phylactery", chance = 0.7, quantity = {1, 1}},
            {id = "death_mastery_codex", chance = 0.6, quantity = {1, 1}},
            {id = "undead_kingdom_key", chance = 0.5, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "skeleton_warrior",
        scale = 1.0,
        animations = {
            "idle", "march", "attack", "defend", "command", "cast_necromancy"
        },
        variants = {
            "skeleton_soldier", "skeleton_knight", "skeleton_captain", "lich_king"
        }
    },
    
    -- Sound effects with undead warrior characteristics
    sounds = {
        march = {
            file = "skeleton_march",
            synth = {
                instrument = "percussion",
                notes = {"C3", "C3", "C3", "C3"},
                durations = {0.2, 0.2, 0.2, 0.2},
                volume = 0.4,
                military = true
            }
        },
        bone_clatter = {
            file = "bone_clatter",
            synth = {
                instrument = "xylophone",
                notes = {"D3", "F3", "A3"},
                durations = {0.1, 0.1, 0.1},
                volume = 0.3,
                skeletal = true
            }
        },
        sword_clash = {
            file = "sword_clash",
            synth = {
                instrument = "percussion",
                notes = {"G3"},
                duration = 0.2,
                volume = 0.6,
                metallic = true
            }
        },
        command_shout = {
            file = "command_shout",
            synth = {
                instrument = "brass",
                notes = {"C3", "G3"},
                durations = {0.3, 0.5},
                volume = 0.7,
                authoritative = true
            }
        },
        necromantic_incantation = {
            file = "necromantic_incantation",
            synth = {
                instrument = "organ",
                notes = {"D2", "F2", "Ab2", "C3"},
                durations = {0.5, 0.5, 0.5, 1.0},
                volume = 0.8,
                dark_magic = true
            }
        }
    },
    
    -- Special undead warrior abilities
    abilities = {
        undead_resilience = {
            type = "passive",
            description = "Immunity to poison, disease, and fear",
            effect = "status_immunity"
        },
        bone_armor = {
            type = "passive",
            description = "Natural bone armor provides damage reduction",
            effect = "damage_reduction"
        },
        formation_fighting = {
            type = "passive",
            description = "Gains bonuses when fighting alongside other skeletons",
            effect = "formation_bonus"
        },
        undead_command = {
            type = "active",
            description = "Rally nearby undead allies",
            effect = "ally_buff",
            cooldown = 20
        },
        necromantic_strike = {
            type = "active",
            description = "Weapon attacks drain life force",
            effect = "life_drain",
            cooldown = 10
        }
    }
}

-- Initialize the skeleton warrior entity using enhanced template
function SkeletonWarrior.init(entity, world)
    -- Copy all fields from SkeletonWarrior template to entity instance
    for k, v in pairs(SkeletonWarrior) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return SkeletonWarrior
