-- tiles/shallow_water.lua
local ShallowWaterTile = {
    id = "shallow_water",
    name = "Shallow Water",
    passable = true,
    variants = 3, -- Different shallow water appearances
    
    -- Entities that can spawn in shallow water
    spawns = {"fish_small", "water_plant", "frog", "crab"},
    
    -- Movement properties
    movementSpeed = 0.6, -- Slower movement through water
    
    -- Resource type for AI entities to find
    resourceType = "water",
    
    -- Interaction function when player activates this tile
    interact = function(tile, entity)
        if entity.type == "player" then
            if entity.hasItem and entity.hasItem("fishing_rod") then
                -- Simple fishing mechanic
                if math.random() < 0.4 then
                    return {
                        success = true,
                        message = "You caught a small fish!",
                        item = "small_fish"
                    }
                else
                    return {
                        success = false,
                        message = "The fish got away."
                    }
                end
            else
                -- Drink water
                return {
                    success = true,
                    message = "You drink from the shallow water. It's somewhat refreshing.",
                    effects = {
                        {type = "restore_hydration", amount = 15}
                    }
                }
            end
        end
        return nil
    end,
    
    -- Weather effects
    weatherEffects = {
        freeze = function(tile)
            -- Water freezes in cold weather
            if math.random() < 0.8 then -- High chance to freeze
                tile.frozen = true
                return "transform", "thin_ice"
            end
            return nil
        end,
        
        drought = function(tile)
            -- May dry up during drought
            if math.random() < 0.3 then
                return "transform", "mud"
            end
            return nil
        end,
        
        rain = function(tile)
            -- Rain might deepen shallow water
            if math.random() < 0.1 then
                return "transform", "water"
            end
            return nil
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {0.2, 0.6, 0.8, 0.7}, -- Blue with transparency
        animation = {
            type = "wave",
            speed = 1.0,
            amplitude = 0.5
        },
        overlays = {
            {name = "ripples", chance = 0.7},
            {name = "water_plants", chance = 0.3}
        },
        reflective = true,
        transparency = 0.7
    },
    
    -- Effects on entities that walk on this tile
    onWalk = function(tile, entity)
        -- Wet effect for entities
        if entity.applyStatus then
            entity.applyStatus("wet", 30)
        end
        
        -- Make sound and ripples
        return {
            sound = "splash_small",
            visual_effect = "ripples",
            message = "You wade through the shallow water."
        }
    end,
    
    -- Audio properties
    ambientSound = "water_ripple",
    footstepSound = "splash_small",
    
    -- Special property for water tiles
    waterDepth = 0.5, -- How deep in tiles
    
    -- Connections to other tiles
    connects_to = {
        "water",
        "deep_water",
        "mud",
        "sand",
        "grass"
    }
}

function ShallowWaterTile.init(world)
    print("Shallow water tile module initialized")
end

return ShallowWaterTile