local Turtle = {
    id = "turtle",
    name = "Turtle",
    type = "turtle",

    -- Entity categories
    categories = {"animal", "reptile", "aquatic", "medium"},

    -- Threat and food categories
    threatCategories = {"predator", "player"},
    foodCategories = {"plant", "insect", "fish"},

    -- Stats
    maxHealth = 40,
    health = 40,
    maxStamina = 60,
    stamina = 60,
    speed = 1.0,

    -- Aquatic properties
    aquatic = {
        swimSpeed = 2.0,
        diveDepth = 4,
        breathHoldTime = 300,
        currentBreath = 300,
        waterTemp = 20
    },

    -- Behaviors
    behaviors = {"wander", "swim", "bask", "forage"},

    -- Behavior configurations
    behaviorConfigs = {
        wander = {
            moveSpeed = 0.8,
            changeDirectionChance = 0.05,
            idleChance = 0.3,
            idleDuration = {2, 5},
            wanderRadius = 8
        },
        swim = {
            moveSpeed = 2.0,
            diveChance = 0.4,
            diveDuration = {10, 30},
            surfaceInterval = {5, 15}
        },
        bask = {
            duration = {30, 60},
            tempIncrease = 0.5,
            healthRegen = 0.1,
            staminaRegen = 0.2
        },
        forage = {
            moveSpeed = 0.5,
            searchRadius = 5,
            forageTime = {5, 10},
            foodTypes = {"plant", "insect", "fish"},
            waterSearch = true
        }
    },

    -- Special abilities
    abilities = {
        shellRetreat = {
            duration = 5,
            cooldown = 8,
            defenseBoost = 2.0
        },
        swimBoost = {
            speedBoost = 1.5,
            duration = 3,
            cooldown = 6,
            staminaCost = 15
        }
    },

    -- Appearance
    appearance = {
        sprite = "turtle",
        scale = 0.8,
        animations = {
            "idle", "walk", "swim", "dive", "bask"
        },
        variants = {
            "green", "brown", "spotted", "red-eared"
        }
    },

    -- Sound effects
    sounds = {
        splash = "turtle_splash",
        swim = "turtle_swim",
        hiss = "turtle_hiss"
    },

    -- Loot drops
    drops = {
        {id = "meat", chance = 0.7, quantity = {1, 2}},
        {id = "shell", chance = 0.5, quantity = {1, 1}},
        {id = "scale", chance = 0.4, quantity = {1, 2}}
    }
}

-- Initialize the entity
function Turtle.init(entity, world)
    -- Copy all fields from Turtle template to entity instance
    for k, v in pairs(Turtle) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random turtle variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Turtle.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update aquatic properties
    if entity.aquatic then
        -- Update breath holding
        if entity.aquatic.currentBreath > 0 then
            entity.aquatic.currentBreath = math.max(0, entity.aquatic.currentBreath - dt)
        else
            -- Take damage when out of breath
            entity.health = math.max(0, entity.health - 0.5 * dt)
        end

        -- Regenerate breath when on surface
        if entity.aquatic.currentBreath < entity.aquatic.breathHoldTime then
            entity.aquatic.currentBreath = math.min(
                entity.aquatic.breathHoldTime,
                entity.aquatic.currentBreath + 2 * dt
            )
        end
    end
end

return Turtle