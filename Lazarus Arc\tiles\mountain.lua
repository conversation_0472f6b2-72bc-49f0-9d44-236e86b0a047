-- tiles/mountain.lua
local MountainTile = {
    id = "mountain",
    name = "Mountain",
    passable = false,
    variants = 8,

    spawns = {"mountain_goat", "eagle", "snow_leopard", "mountain_herb", "ore_deposit"},

    height = 3,
    steepness = 0.8,
    rockType = nil,
    hasSnowcap = false,
    canClimb = true,

    interact = function(tile, entity)
        if entity.skills and entity.skills.climbing and entity.skills.climbing > 3 then
            return {
                success = true,
                message = "With your climbing skill, you could scale this mountain.",
                effects = {{type = "suggest_action", action = "climb"}}
            }
        elseif entity.hasItem and entity.hasItem("climbing_gear") then
            return {
                success = true,
                message = "Using your climbing gear, you could scale this mountain.",
                effects = {{type = "suggest_action", action = "climb"}}
            }
        end

        if entity.hasItem and entity.hasItem("pickaxe") then
            if tile.hasOre then
                local oreType = tile.oreType or MountainTile.determineOreType()
                return {
                    success = true,
                    message = "You notice traces of " .. oreType .. " in the rock face. You could mine here.",
                    effects = {{type = "suggest_action", action = "mine"}}
                }
            else
                return {
                    success = false,
                    message = "The mountain stone appears solid, with no visible mineral deposits."
                }
            end
        end

        return {
            success = false,
            message = "The mountain is too steep to climb without proper equipment or skills."
        }
    end,

    determineOreType = function()
        local oreTypes = {
            {id = "iron", name = "Iron", chance = 0.4},
            {id = "copper", name = "Copper", chance = 0.3},
            {id = "gold", name = "Gold", chance = 0.15},
            {id = "crystal", name = "Crystal", chance = 0.1},
            {id = "mythril", name = "Mythril", chance = 0.05}
        }

        local roll = math.random()
        local cumulativeChance = 0

        for _, oreType in ipairs(oreTypes) do
            cumulativeChance = cumulativeChance + oreType.chance
            if roll <= cumulativeChance then
                return oreType.id
            end
        end

        return "iron"
    end,

    weatherEffects = {
        snow = function(tile)
            tile.snowAmount = (tile.snowAmount or 0) + 0.2
            if tile.snowAmount > 1.0 then
                tile.snowAmount = 1.0
                tile.hasSnowcap = true
            end
            return nil
        end,

        rain = function(tile)
            tile.erosion = (tile.erosion or 0) + 0.01
            if tile.erosion > 0.5 and math.random() < 0.05 then
                return "trigger_event", "rockslide"
            end
            if tile.hasSnowcap and not (world.season == "winter") then
                tile.snowAmount = tile.snowAmount - 0.2
                if tile.snowAmount <= 0 then
                    tile.snowAmount = 0
                    tile.hasSnowcap = false
                end
            end
            return nil
        end,

        heat = function(tile)
            if math.random() < 0.02 then
                return "trigger_event", "minor_rockfall"
            end
            if tile.hasSnowcap then
                tile.snowAmount = tile.snowAmount - 0.1
                if tile.snowAmount <= 0 then
                    tile.snowAmount = 0
                    tile.hasSnowcap = false
                end
            end
            return nil
        end,

        lightning = function(tile)
            if math.random() < 0.2 and not tile.hasOre then
                tile.hasOre = true
                tile.oreType = MountainTile.determineOreType()
                return "visual_effect", "exposed_minerals"
            end
            return nil
        end
    },

    seasonEffects = {
        winter = function(tile)
            tile.snowAmount = 1.0
            tile.hasSnowcap = true
            return nil
        end,

        spring = function(tile)
            if tile.hasSnowcap then
                tile.snowAmount = tile.snowAmount - 0.5
                if tile.snowAmount <= 0 and tile.height < 5 then
                    tile.snowAmount = 0
                    tile.hasSnowcap = false
                else
                    tile.snowAmount = math.max(0.3, tile.snowAmount)
                end
            end
            return nil
        end,

        summer = function(tile)
            if tile.hasSnowcap and tile.height < 4 then
                tile.snowAmount = 0
                tile.hasSnowcap = false
            else
                tile.snowAmount = math.max(0.1, tile.snowAmount - 0.7)
            end
            return nil
        end,

        fall = function(tile)
            if tile.height > 3 then
                tile.snowAmount = 0.3
                tile.hasSnowcap = true
            end
            return nil
        end
    },

    visual = {
        base_color = {0.5, 0.5, 0.5},
        variants = {
            {name = "granite", color_shift = {0.1, 0.1, 0.1}},
            {name = "basalt", color_shift = {-0.2, -0.2, -0.2}},
            {name = "sandstone", color_shift = {0.3, 0.2, 0}},
            {name = "limestone", color_shift = {0.3, 0.3, 0.2}},
            {name = "slate", color_shift = {-0.1, 0, 0.1}},
            {name = "marble", color_shift = {0.3, 0.3, 0.3}},
            {name = "volcanic", color_shift = {0.1, -0.2, -0.2}},
            {name = "crystal_veined", has_sparkle = true}
        },
        height_map = true,
        casts_shadow = true,
        shadow_length = 2.0,
        snowcap = {
            color = {0.9, 0.9, 0.95},
            height_threshold = 0.7,
            seasonal = true
        },
        specialty_renders = {
            ore_veins = {chance = 0.3, types = {"gold", "silver", "iron", "crystal"}},
            waterfalls = {chance = 0.1},
            caves = {chance = 0.2}
        }
    },

    ambientSound = "mountain_wind",
    specialSounds = {
        {name = "rockfall", trigger = "weather_change", chance = 0.1},
        {name = "eagle_cry", trigger = "daytime", chance = 0.05}
    },

    mining = {
        hardness = 5,
        requiredTool = "pickaxe",
        oreChance = 0.2,
        special_resource_chance = 0.05,
        yields = {
            default = {
                {item = "stone", quantity = {3, 6}, chance = 1.0}
            },
            with_ore = {
                {item = "stone", quantity = {2, 4}, chance = 1.0},
                {item = "ore", quantity = {1, 3}, chance = 0.8},
                {item = "gem", quantity = {1, 1}, chance = 0.1}
            }
        }
    },

    climbing = {
        difficulty = 7,
        skillRequired = 3,
        equipmentBonus = 4,
        hazards = {
            {name = "falling", risk = 0.2, damage = {5, 15}},
            {name = "rockslide", risk = 0.05, damage = {10, 20}}
        },
        energy_cost = 15,
        viewBenefits = true
    },

    weatherInfluence = {
        chance = 0.3,
        types = {"snow", "fog", "rain"}
    },

    connects_to = {
        "mountain", "mountain_base", "rocky_ground", "cliff"
    },

    adjacency_effects = {
        {
            type = "shadow",
            direction = "east",
            intensity = 0.7,
            distance = 2
        },
        {
            type = "soil_quality",
            direction = "all",
            effect = "reduce",
            amount = 0.2
        }
    },

    onClimb = function(tile, entity)
        local climbingSkill = (entity.skills and entity.skills.climbing) or 0
        local hasGear = entity.hasItem and entity.hasItem("climbing_gear")
        local difficulty = tile.climbing.difficulty
        local skillValue = climbingSkill + (hasGear and tile.climbing.equipmentBonus or 0)

        if skillValue >= difficulty or math.random() < (skillValue / difficulty) then
            if entity.stamina then
                entity.stamina = entity.stamina - tile.climbing.energy_cost
            end
            return {
                success = true,
                message = "You successfully climb the mountain.",
                effects = {
                    {type = "move_to", x = tile.x, y = tile.y},
                    {type = "stamina_drain", amount = tile.climbing.energy_cost}
                }
            }
        else
            local fallDamage = math.random(tile.climbing.hazards[1].damage[1], tile.climbing.hazards[1].damage[2])
            return {
                success = false,
                message = "You slip and fall while climbing the mountain!",
                effects = {
                    {type = "damage", amount = fallDamage}
                }
            }
        end
    end
}

function MountainTile.init(world)
    print("Mountain tile module initialized")
    if world.systems and world.systems.pathfinding then
        world.systems.pathfinding:registerBlockingTerrain("mountain")
    end
    if world.systems and world.systems.weather then
        world.systems.weather:registerMountain("mountain")
    end
    if world.systems and world.systems.crafting then
        world.systems.crafting:registerOreSource("mountain")
    end
end

return MountainTile
