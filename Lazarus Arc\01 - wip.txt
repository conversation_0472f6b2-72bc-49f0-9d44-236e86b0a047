need to finish:


Game is 2.5D 

more biomes [cities]
more structures
more tiles 
more skills
factions? robots, elves, humans, dwarves, orcs, goblins, animals, etc.

STORY

sprite generator [wip]
more entities [shinies? corrupted]
npc's and npc randomization

scaled threats and monsters [wip]

-----------------------------------------

character class work to be done:

maybe balance necromancer and druid as opposing forces of life and death

dark light and grey paladin?

rogue and necromaner being inventory fiends one is loot [noisy and needs managment] the other is souls they can summon [unruly or just pure amount of souls]

------------------------------------------

raids [wip sudo code written and raid ideas]
needs more depth and locked until level 20 with harder raids every 10 levels to max level


structure health, destructability

------------------------------------------------
gm tools [moderation, modification to characters]

convert player to enemy entity. [give unruly players a hard time and make them into content]

------------------------------------------
Tile System Progress:
- Implemented comprehensive tile system with properties, visual effects, sounds, resources, and environmental interactions
- Added weather and time effects for dynamic tile behavior
- Created specialized tiles for different environments:
  * Snowy tiles (clearing_snowy, mountain_peak)
  * Ice tiles (thin_ice, thin_ice_patch)
  * Water tiles (frozen_river_bank)
  * Cave tiles (cave_entrance)
  * Mud tiles (frozen_mud)
  * Desert tiles (sand_dune, oasis)

Next Tile System Tasks:
- Implement more specialized tiles:
  * Jungle tiles (dense_jungle, jungle_clearing, ancient_ruins)
  * Swamp tiles (marsh, quicksand, swamp_water)
  * Underground tiles (deep_cave, crystal_cavern, lava_cave)
  * City tiles (cobblestone, market_place, residential)
  * Special tiles (portal, altar, magical_spring)

- Add more interactive features:
  * Tile transformation (e.g., water freezing into ice)
  * Resource gathering mechanics
  * Environmental hazards
  * Special events and encounters

- Enhance weather system integration:
  * More complex weather effects
  * Seasonal changes
  * Climate zones
  * Weather-based tile transformations

- Improve visual system:
  * Better variant handling
  * Dynamic lighting effects
  * Particle effects
  * Weather-based visual changes

------------------------------------------
Biome System Tasks:
- Create new biomes:
  * Desert biome (sand dunes, oases, rocky outcrops)
  * Jungle biome (dense vegetation, rivers, ruins)
  * Swamp biome (marshlands, quicksand, water features)
  * Underground biome (caves, crystal formations, lava)
  * City biome (buildings, streets, marketplaces)

- Enhance biome features:
  * Biome transitions
  * Biome-specific weather patterns
  * Biome-specific creatures and resources
  * Biome-specific structures and landmarks

------------------------------------------
Entity System Tasks:
- Create new entity types:
  * Desert creatures (scorpions, snakes, camels)
  * Jungle creatures (monkeys, parrots, jaguars)
  * Swamp creatures (alligators, frogs, insects)
  * Underground creatures (bats, spiders, worms)
  * City NPCs (merchants, guards, citizens)

- Enhance entity behaviors:
  * More complex AI patterns
  * Environmental interactions
  * Weather reactions
  * Social behaviors
  * Combat mechanics

------------------------------------------
Combat System Tasks:
- Implement new combat features:
  * Environmental hazards in combat
  * Weather effects on combat
  * Terrain advantages/disadvantages
  * Special abilities based on environment
  * Combat modifiers from weather/terrain

------------------------------------------
Resource System Tasks:
- Add new resources:
  * Desert resources (sand, cactus, water)
  * Jungle resources (wood, fruit, herbs)
  * Swamp resources (mushrooms, reeds, fish)
  * Underground resources (minerals, crystals, gems)
  * City resources (crafting materials, trade goods)

- Enhance resource mechanics:
  * Resource gathering tools
  * Resource processing
  * Resource trading
  * Resource-based crafting
  * Resource regeneration rates

------------------------------------------
Quest System Tasks:
- Create environment-specific quests:
  * Desert survival quests
  * Jungle exploration quests
  * Swamp mystery quests
  * Underground treasure hunts
  * City intrigue quests

- Enhance quest mechanics:
  * Dynamic quest generation
  * Environment-based objectives
  * Weather-dependent quests
  * Time-sensitive quests
  * Multi-stage quests
  
  
  
  move everything from the monolithic main.lua to engine.lua and its broken out parts.
  make sure it doesnt delete things for no reason!!!