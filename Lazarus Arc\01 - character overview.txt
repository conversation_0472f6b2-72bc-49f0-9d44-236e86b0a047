/character
  character.lua          - Core character module
  /components
    inventory.lua        - Inventory management
    equipment.lua        - Equipment management
    skills.lua           - Skills and abilities
    magic.lua            - Magic system
    overdrive.lua        - Overdrive mechanics
  /data
    classes.lua          - Class definitions
    weapons/             - Weapon definitions
    armor/               - Armor definitions
    items/               - Item definitions
    spells/              - Spell definitions
    abilities/           - Ability definitions
	
	Easy Entry, Hidden Depth: Simple on the surface but with rich systems underneath
Balanced Progression: Meaningful advancement without requiring grinding
Social Connection: NFC card system that makes playing with friends seamless
Discoverable Content: Systems that reward experimentation without requiring it

Character.lua as the Hub
Character.lua will be the combiner that references other components but doesn't contain them directly. It will store:

Core Identity

Character ID (links to database)
Character PIN for card security
Hub seed (for teleport system)
Personal details (name, birthday, etc.)
Level and experience (slow progression to 100)


References to Components

Inventory (load from inventory.lua)
Equipment (paper doll system)
Class(es) (loaded from classes.lua)
Skills (references to skill.lua files)
Magic affinities (with the opposing balance system)



Identification System
For the identification system:

Items have hidden properties until identified
Identification could require a skill, spell, or NPC service
Different levels of identification reveal different depths of information
Some rare items might require special means to fully identify

Magic Balance System
For the magic balance system:

Each element has an opposite (fire/water, light/dark)
100% affinity in one means 0% in its opposite
Spells are individual Lua files for easy addition/modification
Magic proficiency increases through use
Hidden magic combinations unlock through specific element combinations

Equipment and Items

Equipment follows the weapon_01.lua pattern - each piece is its own file
Equipment has durability, properties, and potentially hidden effects
Different types of equipment affect different stats
Rare equipment might have special abilities that unlock at specific levels or conditions

Friend System

Friend data stored in database with offline fallback in friend.lua
Friend system allows visiting each other's hubs
Friends can maybe share certain benefits (trading, buffs, etc.)

Level Progression

100 level cap but very slow progression
Focus on horizontal progression (more skills, equipment, spells) rather than vertical (just stats)
Higher levels unlock more abilities and equipment options but don't make earlier content obsolete
Player skill remains important regardless of level