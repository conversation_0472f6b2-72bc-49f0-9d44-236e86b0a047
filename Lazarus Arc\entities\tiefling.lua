-- entities/tiefling.lua
-- Tiefling entity with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local Tiefling = {
    id = "tiefling",
    name = "Tiefling",
    type = "tiefling",
    shape = {
        {0, -1}, {0.8, -0.7}, {1, 0}, {0.8, 0.7},
        {0, 1}, {-0.8, 0.7}, {-1, 0}, {-0.8, -0.7}
    },
    size = 7,
    description = "A being with infernal heritage, possessing both charm and dark magic",

    -- Entity categories
    categories = {"humanoid", "tiefling", "infernal", "charismatic"},
    threatCategories = {"player", "celestial", "paladin", "cleric"},
    
    -- Base stats (balanced with magical focus)
    maxHealth = 90,
    health = 90,
    maxStamina = 60,
    stamina = 60,
    maxMana = 120,
    mana = 120,
    speed = 2.3,
    attack = 13,
    defense = 8,
    magicAttack = 18,
    magicDefense = 15,
    charisma = 16,
    infernal_resistance = 10,
    
    -- Behaviors (tieflings are cunning and magical)
    behaviors = {"charm", "cast_infernal_magic", "manipulate", "wander"},
    behaviorConfigs = {
        charm = {
            radius = 10,
            charmChance = 0.3,
            duration = 8,
            charismaBonus = 1.5
        },
        cast_infernal_magic = {
            spells = {"hellish_rebuke", "charm_person", "darkness"},
            castChance = 0.2,
            manaCost = 25,
            infernal_power = true
        },
        manipulate = {
            socialEngineering = true,
            deceptionBonus = 1.8,
            frequency = 0.15
        },
        wander = {
            moveSpeed = 2.0,
            changeDirectionChance = 0.04,
            wanderRadius = 7,
            urban_preference = true
        }
    },
    
    -- Enhanced variant system for tieflings
    variantChances = {
        normal = 0.70,          -- 70% common tiefling
        shiny = 0.18,           -- 18% noble tiefling (shiny)
        rare = 0.10,            -- 10% cambion (rare)
        legendary = 0.02        -- 2% archfiend (legendary)
    },
    
    variants = {
        normal = {
            name = "Common Tiefling",
            description = "A tiefling with typical infernal heritage",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {1.1, 0.8, 0.8, 1.0},  -- Reddish skin tone
                horns = "small"
            }
        },
        
        shiny = {
            name = "Noble Tiefling",
            description = "A tiefling of noble infernal bloodline",
            statModifiers = {
                maxHealth = 1.3,
                maxMana = 1.4,
                magicAttack = 1.4,
                charisma = 1.5,
                infernal_resistance = 1.3
            },
            appearanceModifiers = {
                scale = 1.1,
                glow = true,
                colorTint = {1.3, 0.7, 0.7, 1.0},  -- Deep red with golden highlights
                horns = "elegant",
                noble_attire = true,
                infernal_aura = true
            },
            soundModifiers = {
                pitch = 1.1,
                volume = 1.2,
                seductive_tone = true
            }
        },
        
        rare = {
            name = "Cambion",
            description = "A half-fiend with significant demonic power",
            statModifiers = {
                maxHealth = 1.7,
                maxMana = 1.8,
                magicAttack = 1.8,
                charisma = 1.7,
                infernal_resistance = 1.8,
                demonic_power = 2.0
            },
            appearanceModifiers = {
                scale = 1.2,
                colorTint = {1.2, 0.6, 0.6, 1.0},  -- Dark crimson
                horns = "prominent",
                wings = "small_demonic",
                tail = "barbed",
                demonic_features = true
            },
            soundModifiers = {
                pitch = 0.9,
                volume = 1.4,
                reverb = true,
                demonic_undertone = true
            }
        },
        
        legendary = {
            name = "Archfiend",
            description = "A powerful fiendish entity of immense dark power",
            statModifiers = {
                maxHealth = 2.5,
                maxMana = 3.0,
                magicAttack = 2.5,
                charisma = 2.5,
                infernal_resistance = 3.0,
                archfiend_power = 5.0
            },
            appearanceModifiers = {
                scale = 1.4,
                glow = true,
                colorTint = {1.5, 0.4, 0.4, 1.0},  -- Burning crimson
                horns = "massive",
                wings = "large_demonic",
                tail = "massive_barbed",
                hellfire_aura = "legendary",
                reality_warping = true
            },
            soundModifiers = {
                pitch = 0.7,
                volume = 1.8,
                reverb = true,
                echo = true,
                infernal_authority = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "infernal_essence", chance = 0.7, quantity = {1, 2}},
        {id = "tiefling_horn_fragment", chance = 0.5, quantity = {1, 1}},
        {id = "dark_magic_component", chance = 0.6, quantity = {1, 1}},
        {id = "gold_coin", chance = 0.8, quantity = {3, 7}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "noble_tiefling_signet", chance = 0.8, quantity = {1, 1}},
            {id = "infernal_contract", chance = 0.6, quantity = {1, 1}},
            {id = "charm_amulet", chance = 0.5, quantity = {1, 1}},
            {id = "hellish_silk", chance = 0.7, quantity = {1, 2}}
        },
        rare = {
            {id = "cambion_wing_membrane", chance = 0.7, quantity = {1, 1}},
            {id = "demonic_power_core", chance = 0.6, quantity = {1, 1}},
            {id = "barbed_tail_spike", chance = 0.5, quantity = {1, 2}},
            {id = "half_fiend_essence", chance = 0.8, quantity = {1, 1}}
        },
        legendary = {
            {id = "archfiend_crown", chance = 0.9, quantity = {1, 1}},
            {id = "hellfire_orb", chance = 0.8, quantity = {1, 1}},
            {id = "infernal_throne_fragment", chance = 0.6, quantity = {1, 1}},
            {id = "reality_warping_gem", chance = 0.5, quantity = {1, 1}},
            {id = "archfiend_soul_shard", chance = 0.4, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "tiefling",
        scale = 1.0,
        animations = {
            "idle", "walk", "cast_spell", "charm", "intimidate", "death"
        },
        variants = {
            "common_tiefling", "noble_tiefling", "cambion", "archfiend"
        }
    },
    
    -- Sound effects with infernal characteristics
    sounds = {
        idle = {
            file = "tiefling_idle",
            synth = {
                instrument = "violin",
                notes = {"D4", "F#4", "A4"},
                durations = {0.4, 0.3, 0.5},
                volume = 0.3,
                seductive = true
            }
        },
        cast_spell = {
            file = "tiefling_cast",
            synth = {
                instrument = "organ",
                notes = {"C4", "Eb4", "G4", "Bb4"},
                durations = {0.3, 0.3, 0.3, 0.6},
                volume = 0.5,
                infernal = true
            }
        },
        charm = {
            file = "tiefling_charm",
            synth = {
                instrument = "flute",
                notes = {"A4", "C5", "E5"},
                durations = {0.4, 0.4, 0.6},
                volume = 0.4,
                hypnotic = true
            }
        },
        hurt = {
            file = "tiefling_hurt",
            synth = {
                instrument = "violin",
                notes = {"F#4"},
                duration = 0.3,
                volume = 0.5,
                pained = true
            }
        },
        death = {
            file = "tiefling_death",
            synth = {
                instrument = "organ",
                notes = {"D4", "Bb3", "F3", "D3"},
                durations = {0.5, 0.5, 0.7, 1.2},
                volume = 0.6,
                dramatic = true
            }
        },
        hellish_rebuke = {
            file = "tiefling_rebuke",
            synth = {
                instrument = "distorted_guitar",
                notes = {"E3", "G3", "B3"},
                durations = {0.2, 0.2, 0.4},
                volume = 0.7,
                hellfire = true
            }
        }
    },
    
    -- Special abilities
    abilities = {
        hellish_rebuke = {
            type = "active",
            description = "Retaliate with infernal fire when damaged",
            effect = "damage_reflection",
            manaCost = 25
        },
        infernal_legacy = {
            type = "passive",
            description = "Resistance to fire and charm effects",
            effect = "status_resistance"
        },
        fiendish_charm = {
            type = "active",
            description = "Charm enemies with infernal charisma",
            effect = "mind_control",
            manaCost = 30
        },
        darkvision = {
            type = "passive",
            description = "See perfectly in darkness",
            effect = "night_vision"
        }
    }
}

-- Initialize the tiefling entity using enhanced template
function Tiefling.init(entity, world)
    -- Copy all fields from Tiefling template to entity instance
    for k, v in pairs(Tiefling) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return Tiefling
