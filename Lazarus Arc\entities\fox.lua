-- entities/fox.lua
-- Enhanced fox with variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local Fox = {
    id = "fox",
    name = "<PERSON>",
    type = "fox",
    shape = {
        {0, -1}, {0.7, -0.7}, {1, 0}, {0.7, 0.7},
        {0, 1}, {-0.7, 0.7}, {-1, 0}, {-0.7, -0.7}
    },
    size = 7,

    -- Entity categories for relationship handling
    categories = {"animal", "predator", "mammal", "medium"},

    -- Threat and food categories
    threatCategories = {"monster", "player"},
    foodCategories = {"small_prey", "livestock"},

    -- Stats
    maxHealth = 35,
    health = 35,
    maxStamina = 50,
    stamina = 50,
    speed = 2.5, -- Foxes are quite fast

    -- Behaviors
    behaviors = {"hunt", "wander", "scavenge"},

    -- Behavior configurations
    behaviorConfigs = {
        hunt = {
            moveSpeed = 3.0,
            huntRadius = 12,
            chaseRadius = 15,
            attackRange = 1.5,
            attackDamage = 10,
            attackCooldown = 1.0,
            preferredTargets = {"rabbit", "field_mouse", "bird"}
        },
        wander = {
            moveSpeed = 1.5,
            changeDirectionChance = 0.03,
            wanderRadius = 8
        },
        scavenge = {
            moveSpeed = 1.0,
            searchRadius = 10,
            foodTypes = {"meat", "carrion"}
        }
    },

    -- Special abilities
    abilities = {
        pounce = {
            damageMultiplier = 1.5,
            range = 3,
            cooldown = 5
        },
        stealth = {
            detectionReduction = 0.5,
            duration = 3,
            cooldown = 8
        }
    },

    -- Appearance
    appearance = {
        sprite = "fox",
        scale = 1.0,
        animations = {
            "idle", "walk", "run", "pounce", "stealth"
        },
        variants = {
            "red", "gray", "arctic", "desert"
        }
    },

    -- Sound effects
    sounds = {
        bark = "fox_bark",
        growl = "fox_growl",
        yip = "fox_yip"
    },

    -- === ENHANCED VARIANT SYSTEM ===
    variantChances = {
        normal = 0.75,          -- 75% red fox
        shiny = 0.18,           -- 18% silver fox (shiny)
        rare = 0.06,            -- 6% arctic fox (rare)
        legendary = 0.01        -- 1% spirit fox (legendary)
    },

    variants = {
        normal = {
            name = "Red Fox",
            description = "A common red fox with russet fur",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {1.1, 0.7, 0.5, 1.0}  -- Reddish fur
            }
        },

        shiny = {
            name = "Silver Fox",
            description = "A rare fox with lustrous silver fur",
            statModifiers = {
                maxHealth = 1.4,    -- 49 health instead of 35
                speed = 1.3,        -- 3.25 speed instead of 2.5
                maxStamina = 1.3,   -- 65 stamina instead of 50
                stealth = 1.5       -- Enhanced stealth
            },
            appearanceModifiers = {
                scale = 1.1,
                glow = true,
                colorTint = {1.2, 1.2, 1.3, 1.0},  -- Silver fur
                elegant_posture = true
            },
            soundModifiers = {
                pitch = 1.2,
                volume = 1.1,
                refined = true
            }
        },

        rare = {
            name = "Arctic Fox",
            description = "A hardy fox adapted to frozen lands",
            statModifiers = {
                maxHealth = 1.6,    -- 56 health
                speed = 1.2,        -- 3.0 speed
                maxStamina = 1.5,   -- 75 stamina
                cold_resistance = 2.0,
                ice_magic = 1.0
            },
            appearanceModifiers = {
                scale = 1.15,
                colorTint = {1.3, 1.3, 1.4, 1.0},  -- White with blue tint
                thick_fur = true,
                frost_aura = true
            },
            soundModifiers = {
                pitch = 1.0,
                volume = 1.2,
                reverb = true,
                crystalline = true
            }
        },

        legendary = {
            name = "Spirit Fox",
            description = "A mystical fox with supernatural powers",
            statModifiers = {
                maxHealth = 2.5,    -- 87.5 health
                speed = 1.8,        -- 4.5 speed
                maxStamina = 2.0,   -- 100 stamina
                spirit_power = 3.0,
                phase_ability = 1.0
            },
            appearanceModifiers = {
                scale = 1.3,
                glow = true,
                colorTint = {1.4, 1.1, 1.6, 1.0},  -- Ethereal purple-white
                spirit_flames = true,
                multiple_tails = 9,
                legendary_presence = true
            },
            soundModifiers = {
                pitch = 0.9,
                volume = 1.4,
                reverb = true,
                echo = true,
                mystical_resonance = true
            }
        }
    },

    -- === ENHANCED DROP SYSTEM ===

    -- Base drops (available from all variants)
    baseDrops = {
        {id = "meat", chance = 0.8, quantity = {1, 2}},
        {id = "hide", chance = 0.7, quantity = {1, 1}},
        {id = "fur", chance = 0.6, quantity = {1, 2}},
        {id = "fang", chance = 0.3, quantity = {1, 1}}
    },

    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "silver_fox_pelt", chance = 0.9, quantity = {1, 1}},
            {id = "elegant_tail", chance = 0.8, quantity = {1, 1}},
            {id = "refined_essence", chance = 0.6, quantity = {1, 1}}
        },
        rare = {
            {id = "arctic_fox_fur", chance = 0.9, quantity = {1, 2}},
            {id = "ice_crystal", chance = 0.7, quantity = {1, 2}},
            {id = "frost_essence", chance = 0.6, quantity = {1, 1}},
            {id = "winter_charm", chance = 0.4, quantity = {1, 1}}
        },
        legendary = {
            {id = "spirit_fox_tail", chance = 0.9, quantity = {1, 3}},
            {id = "mystical_fox_fire", chance = 0.8, quantity = {1, 2}},
            {id = "nine_tail_essence", chance = 0.7, quantity = {1, 1}},
            {id = "spirit_realm_key", chance = 0.5, quantity = {1, 1}},
            {id = "fox_deity_blessing", chance = 0.3, quantity = {1, 1}}
        }
    }
}

-- Initialize the entity using enhanced template
function Fox.init(entity, world)
    -- Copy all fields from Fox template to entity instance
    for k, v in pairs(Fox) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return Fox