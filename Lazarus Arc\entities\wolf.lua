-- entities/wolf.lua
-- Enhanced wolf with variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local Wolf = {
    id = "wolf",
    name = "Wolf",
    type = "wolf",
    -- PLACEHOLDER: Basic shape for visual representation
    shape = {
        {0, -1}, {0.8, -0.8}, {1, 0}, {0.8, 0.8},
        {0, 1}, {-0.8, 0.8}, {-1, 0}, {-0.8, -0.8}
    },
    size = 9,

    -- Entity categories for relationship handling
    categories = {"animal", "predator", "mammal", "large"},

    -- Threat and food categories
    threatCategories = {"monster", "player"},
    foodCategories = {"medium_prey", "small_prey", "livestock"},

    -- Stats
    maxHealth = 45,
    health = 45,
    maxStamina = 60,
    stamina = 60,
    speed = 2.7,

    -- Behaviors
    behaviors = {"hunt", "pack", "wander"},

    -- Behavior configurations
    behaviorConfigs = {
        hunt = {
            moveSpeed = 3.0,
            huntRadius = 15,
            chaseRadius = 20,
            packBonus = 0.2
        },
        pack = {
            packRadius = 8,
            minPackSize = 2,
            maxPackSize = 5
        },
        wander = {
            moveSpeed = 1.8,
            changeDirectionChance = 0.04
        }
    },

    -- Special abilities
    abilities = {
        bite = {
            damageMultiplier = 1.2,
            cooldown = 3
        },
        howl = {
            range = 30, -- How far the howl travels
            effect = "summon_pack" -- Can summon other wolves
        }
    },

    -- Appearance
    appearance = {
        sprite = "wolf",
        scale = 1.2,
        animations = {
            "idle", "walk", "run", "attack", "howl"
        },
        variants = {
            "gray", "black", "white", "timber"
        }
    },

    -- Sound effects with synth configuration (deep, powerful sounds)
    sounds = {
        howl = {
            file = "wolf_howl",
            synth = {
                instrument = "bass_guitar",
                notes = {"A2", "C3", "E3", "C3", "A2"},
                durations = {0.6, 0.4, 0.8, 0.4, 0.8},
                volume = 0.6,
                vibrato = true,
                vibratoRate = 3.5
            }
        },
        bark = {
            file = "wolf_bark",
            synth = {
                instrument = "electric_guitar",
                notes = {"G3", "E3"},
                durations = {0.2, 0.3},
                volume = 0.5
            }
        },
        growl = {
            file = "wolf_growl",
            synth = {
                instrument = "bass_guitar",
                notes = {"E2", "C2"},
                durations = {0.4, 0.5},
                volume = 0.55,
                vibrato = true,
                vibratoRate = 5.0
            }
        },
        footstep = {
            file = "wolf_footstep",
            synth = {
                instrument = "marimba",
                notes = {"D3"},
                duration = 0.15,
                volume = 0.25
            }
        },
        attack = {
            file = "wolf_attack",
            synth = {
                instrument = "electric_guitar",
                notes = {"G3", "D3"},
                durations = {0.2, 0.3},
                volume = 0.6
            }
        },
        hurt = {
            file = "wolf_hurt",
            synth = {
                instrument = "bass_guitar",
                notes = {"D#2"},
                duration = 0.4,
                volume = 0.6,
                vibrato = true,
                vibratoRate = 8.0
            }
        },
        death = {
            file = "wolf_death",
            synth = {
                instrument = "bass_guitar",
                notes = {"A2", "F2", "C2"},
                durations = {0.6, 0.6, 1.2},
                volume = 0.6,
                vibrato = true,
                vibratoRate = 2.0
            }
        },
        idle = {
            file = "wolf_idle",
            synth = {
                instrument = "cello",
                notes = {"A2"},
                duration = 0.8,
                volume = 0.3,
                vibrato = true,
                vibratoRate = 2.5
            }
        },
        snarl = {
            file = "wolf_snarl",
            synth = {
                instrument = "electric_guitar",
                notes = {"F#3", "D3"},
                durations = {0.25, 0.35},
                volume = 0.55,
                vibrato = true,
                vibratoRate = 10.0
            }
        }
    },

    -- === ENHANCED VARIANT SYSTEM ===
    variantChances = {
        normal = 0.70,          -- 70% gray wolf
        shiny = 0.20,           -- 20% dire wolf (shiny)
        rare = 0.08,            -- 8% alpha wolf (rare)
        legendary = 0.02        -- 2% fenrir wolf (legendary)
    },

    variants = {
        normal = {
            name = "Gray Wolf",
            description = "A common pack wolf with gray fur",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.8, 0.8, 0.9, 1.0}  -- Gray fur
            }
        },

        shiny = {
            name = "Dire Wolf",
            description = "A massive wolf with enhanced strength and ferocity",
            statModifiers = {
                maxHealth = 1.6,    -- 72 health instead of 45
                speed = 1.2,        -- 3.24 speed instead of 2.7
                maxStamina = 1.4,   -- 84 stamina instead of 60
                attack_power = 1.5  -- Enhanced bite damage
            },
            appearanceModifiers = {
                scale = 1.3,
                glow = true,
                colorTint = {0.6, 0.6, 0.7, 1.0},  -- Dark gray with menacing glow
                intimidating_presence = true,
                larger_fangs = true
            },
            soundModifiers = {
                pitch = 0.8,
                volume = 1.3,
                menacing = true
            }
        },

        rare = {
            name = "Alpha Wolf",
            description = "A legendary pack leader with commanding presence",
            statModifiers = {
                maxHealth = 2.0,    -- 90 health
                speed = 1.4,        -- 3.78 speed
                maxStamina = 1.8,   -- 108 stamina
                pack_leadership = 2.5,
                howl_range = 2.0
            },
            appearanceModifiers = {
                scale = 1.4,
                colorTint = {1.0, 0.9, 0.6, 1.0},  -- Golden-brown alpha coloring
                alpha_markings = true,
                commanding_posture = true,
                battle_scars = true
            },
            soundModifiers = {
                pitch = 0.7,
                volume = 1.5,
                reverb = true,
                authoritative = true
            }
        },

        legendary = {
            name = "Fenrir Wolf",
            description = "A mythical wolf of enormous size and supernatural power",
            statModifiers = {
                maxHealth = 3.0,    -- 135 health
                speed = 1.6,        -- 4.32 speed
                maxStamina = 2.5,   -- 150 stamina
                mythical_power = 5.0,
                reality_bending = 1.0
            },
            appearanceModifiers = {
                scale = 2.0,
                glow = true,
                colorTint = {0.4, 0.4, 0.6, 1.0},  -- Dark mystical blue-black
                chains_of_fate = true,
                glowing_eyes = true,
                legendary_aura = "apocalyptic",
                size_distortion = true
            },
            soundModifiers = {
                pitch = 0.5,
                volume = 2.0,
                reverb = true,
                echo = true,
                world_shaking = true
            }
        }
    },

    -- === ENHANCED DROP SYSTEM ===

    -- Base drops (available from all variants)
    baseDrops = {
        {id = "meat", chance = 0.8, quantity = {1, 2}},
        {id = "hide", chance = 0.7, quantity = {1, 1}},
        {id = "fang", chance = 0.4, quantity = {1, 2}},
        {id = "claw", chance = 0.5, quantity = {2, 4}},
        {id = "bone", chance = 0.6, quantity = {1, 2}}
    },

    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "dire_wolf_pelt", chance = 0.9, quantity = {1, 1}},
            {id = "massive_fang", chance = 0.8, quantity = {1, 2}},
            {id = "intimidation_essence", chance = 0.6, quantity = {1, 1}},
            {id = "dire_wolf_claw", chance = 0.7, quantity = {2, 4}}
        },
        rare = {
            {id = "alpha_wolf_pelt", chance = 0.9, quantity = {1, 1}},
            {id = "leadership_totem", chance = 0.8, quantity = {1, 1}},
            {id = "pack_bond_crystal", chance = 0.6, quantity = {1, 1}},
            {id = "alpha_howl_essence", chance = 0.7, quantity = {1, 1}},
            {id = "command_fang", chance = 0.5, quantity = {1, 1}}
        },
        legendary = {
            {id = "fenrir_pelt", chance = 0.95, quantity = {1, 1}},
            {id = "chain_of_fate_link", chance = 0.9, quantity = {1, 3}},
            {id = "world_ending_howl", chance = 0.8, quantity = {1, 1}},
            {id = "mythical_wolf_essence", chance = 0.8, quantity = {1, 2}},
            {id = "reality_bending_fang", chance = 0.6, quantity = {1, 1}},
            {id = "apocalypse_fragment", chance = 0.4, quantity = {1, 1}}
        }
    }
}

-- Initialize the entity using enhanced template
function Wolf.init(entity, world)
    -- Copy all fields from Wolf template to entity instance
    for k, v in pairs(Wolf) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return Wolf