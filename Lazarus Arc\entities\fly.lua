local Fly = {
    id = "fly",
    name = "Fly",
    type = "fly",
    shape = {
        {0, -1}, {1, 0}, {0, 1}, {-1, 0}
    },
    size = 1,

    -- Entity categories
    categories = {"insect", "prey"},

    -- No threat categories, only food
    foodCategories = {}, -- Flies don't eat in this simulation

    -- Stats
    maxHealth = 2,
    health = 2,
    speed = 1.5,
    canFly = true,

    -- Behaviors
    behaviors = {"wander"},

    -- Behavior configurations
    behaviorConfigs = {
        wander = {
            moveSpeed = 1.0,
            changeDirectionChance = 0.2,
            idleChance = 0.1,
            idleDuration = {0.5, 1.5},
            wanderRadius = 5,
            flightChance = 0.8 -- High chance to fly while wandering
        }
    },

    -- Appearance
    appearance = {
        sprite = "fly",
        scale = 0.4,
        animations = {
            "idle", "fly"
        }
    },

    -- Sound effects with synth configuration
    sounds = {
        buzz = {
            file = "fly_buzz",
            synth = {
                instrument = "xylophone",
                notes = {"A5", "C6"},
                durations = {0.1, 0.1},
                volume = 0.2,
                vibrato = true,
                vibratoRate = 15.0
            }
        },
        fly = {
            synth = {
                instrument = "xylophone",
                notes = {"C6"},
                duration = 0.15,
                volume = 0.15,
                vibrato = true,
                vibratoRate = 12.0
            }
        },
        land = {
            synth = {
                instrument = "kalimba",
                notes = {"A4"},
                duration = 0.05,
                volume = 0.08
            }
        },
        hurt = {
            synth = {
                instrument = "xylophone",
                notes = {"D#6"},
                duration = 0.08,
                volume = 0.25,
                vibrato = true,
                vibratoRate = 20.0
            }
        },
        death = {
            synth = {
                instrument = "kalimba",
                notes = {"A5", "F5", "C5"},
                durations = {0.1, 0.1, 0.2},
                volume = 0.2
            }
        }
    },

    -- No drops
    drops = {}
}

-- Initialize the fly entity
function Fly.init(entity, world)
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}
    entity.altitude = 0 -- Current flight height

    -- Initialize behaviors only if the world parameter contains a valid modules table
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    else
        print("Warning: Fly.init did not receive a valid world with modules.")
    end

    return entity
end

return Fly
