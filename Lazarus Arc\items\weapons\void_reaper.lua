local VoidReaper = {
    id = "void_reaper",
    name = "Void Reaper",
    type = "weapon",
    category = "scythe",
    
    -- Properties
    properties = {
        damage = 45,
        attackSpeed = 1.0,
        range = 2.5,
        durability = 200,
        maxDurability = 200,
        voidDamage = 35,
        voidRadius = 2.0,
        voidDuration = 4,
        soulStealChance = 0.3,
        soulStealAmount = 15,
        soulStealDuration = 3,
        teleportChance = 0.25,
        teleportRange = 4.0,
        teleportCooldown = 2.0,
        voidBurstChance = 0.2,
        voidBurstDamage = 40,
        voidBurstRadius = 3.0,
        voidBurstForce = 2.0,
        voidCharge = 0,
        maxVoidCharge = 100,
        chargeRate = 4,
        dischargeRate = 8,
        voidResistance = 0.8,
        soulCount = 0,
        maxSouls = 5,
        soulPower = 1.2
    },
    
    -- Appearance
    appearance = {
        sprite = "void_reaper",
        scale = 1.0,
        animations = {
            "idle",
            "slash",
            "teleport",
            "soul"
        },
        variants = {
            "black",
            "purple",
            "void",
            "eternal"
        },
        blendMode = "add",
        tint = {0.2, 0.0, 0.3},
        alpha = 0.9
    },
    
    -- Sound effects
    sounds = {
        slash = "void_reaper_slash",
        teleport = "void_reaper_teleport",
        soul = "void_reaper_soul",
        void = "void_reaper_void"
    },
    
    -- Effects
    effects = {
        void = {
            type = "status",
            duration = 4,
            effects = {
                damage = 35,
                slow = true,
                radius = 2.0
            }
        },
        soulSteal = {
            type = "status",
            duration = 3,
            effects = {
                heal = 15,
                speed = 1.2,
                soul = true
            }
        },
        voidBurst = {
            type = "status",
            duration = 0.5,
            effects = {
                damage = 40,
                knockback = 2.0,
                radius = 3.0
            }
        }
    }
}

-- Initialize the weapon
function VoidReaper.init(weapon, world)
    -- Copy all fields from VoidReaper template to weapon instance
    for k, v in pairs(VoidReaper) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        voidCharge = 0,
        lastChargeTime = 0,
        lastTeleportTime = 0,
        soulCount = 0
    }

    return weapon
end

-- Update the weapon
function VoidReaper.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Update void charge
        if world.darkness and world.darkness > 0.7 then
            local chargeFactor = (world.darkness * 0.7 + 0.3) * dt
            weapon.properties.weaponState.voidCharge = math.min(weapon.properties.maxVoidCharge,
                weapon.properties.weaponState.voidCharge + chargeFactor * weapon.properties.chargeRate)
        else
            weapon.properties.weaponState.voidCharge = math.max(0,
                weapon.properties.weaponState.voidCharge - weapon.properties.dischargeRate * dt)
        end
        
        -- Create void effect
        if world.createEffect then
            world.createEffect({
                type = "void",
                position = weapon.position,
                radius = weapon.properties.voidRadius,
                duration = dt
            })
        end
    end
end

-- Handle attack
function VoidReaper.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Calculate slash direction
    local angle = math.atan2(
        target.position.y - attacker.position.y,
        target.position.x - attacker.position.x
    )
    
    -- Create void slash effect
    if world.createEffect then
        world.createEffect({
            type = "void_slash",
            position = attacker.position,
            angle = angle,
            range = weapon.properties.range,
            duration = 0.5
        })
    end
    
    -- Check for void effect
    if math.random() < weapon.properties.voidDamage / weapon.properties.damage then
        -- Create void effect
        if world.createEffect then
            world.createEffect({
                type = "void",
                position = target.position,
                radius = weapon.properties.voidRadius,
                duration = weapon.properties.voidDuration
            })
        end
        
        -- Apply void effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.void)
        end
        
        -- Play void sound
        if world.playSound then
            world.playSound(weapon.sounds.void)
        end
    end
    
    -- Check for soul steal
    if math.random() < weapon.properties.soulStealChance then
        -- Create soul steal effect
        if world.createEffect then
            world.createEffect({
                type = "soul_steal",
                position = target.position,
                target = attacker.position,
                duration = weapon.properties.soulStealDuration
            })
        end
        
        -- Apply soul steal effect to attacker
        if attacker.applyEffect then
            attacker.applyEffect(weapon.effects.soulSteal)
        end
        
        -- Increase soul count
        weapon.properties.weaponState.soulCount = math.min(weapon.properties.maxSouls,
            weapon.properties.weaponState.soulCount + 1)
        
        -- Play soul sound
        if world.playSound then
            world.playSound(weapon.sounds.soul)
        end
    end
    
    -- Check for teleport
    if math.random() < weapon.properties.teleportChance and 
       world.time - weapon.properties.weaponState.lastTeleportTime >= weapon.properties.teleportCooldown then
        -- Calculate teleport position
        local teleportAngle = angle + (math.random() - 0.5) * math.pi
        local teleportDistance = math.random() * weapon.properties.teleportRange
        local teleportX = attacker.position.x + math.cos(teleportAngle) * teleportDistance
        local teleportY = attacker.position.y + math.sin(teleportAngle) * teleportDistance
        
        -- Create teleport effect
        if world.createEffect then
            world.createEffect({
                type = "void_teleport",
                position = attacker.position,
                target = {x = teleportX, y = teleportY},
                duration = 0.5
            })
        end
        
        -- Teleport attacker
        if attacker.move then
            attacker.move({
                x = teleportX - attacker.position.x,
                y = teleportY - attacker.position.y
            })
        end
        
        -- Update last teleport time
        weapon.properties.weaponState.lastTeleportTime = world.time
        
        -- Play teleport sound
        if world.playSound then
            world.playSound(weapon.sounds.teleport)
        end
    end
    
    -- Check for void burst
    if math.random() < weapon.properties.voidBurstChance then
        -- Create void burst effect
        if world.createEffect then
            world.createEffect({
                type = "void_burst",
                position = target.position,
                radius = weapon.properties.voidBurstRadius,
                duration = 0.5
            })
        end
        
        -- Apply void burst effect to nearby entities
        if world.entities then
            for _, entity in ipairs(world.entities) do
                if entity.position then
                    local distance = math.sqrt(
                        (entity.position.x - target.position.x)^2 + 
                        (entity.position.y - target.position.y)^2
                    )
                    
                    if distance <= weapon.properties.voidBurstRadius then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.voidBurst)
                        end
                        
                        -- Apply knockback
                        if entity.move then
                            local force = (1 - distance / weapon.properties.voidBurstRadius) * 
                                weapon.properties.voidBurstForce
                            local angle = math.atan2(
                                entity.position.y - target.position.y,
                                entity.position.x - target.position.x
                            )
                            entity.move({
                                x = math.cos(angle) * force,
                                y = math.sin(angle) * force
                            })
                        end
                    end
                end
            end
        end
    end
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function VoidReaper.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough void charge and souls
    if weapon.properties.weaponState.voidCharge < 50 or weapon.properties.weaponState.soulCount < 2 then
        return false
    end
    
    -- Create void reaper effect
    if world.createEffect then
        world.createEffect({
            type = "void_reaper",
            position = attacker.position,
            radius = weapon.properties.voidRadius * 3,
            duration = 6,
            damage = weapon.properties.damage * 2
        })
    end
    
    -- Apply void effects to nearby entities
    if world.entities then
        for _, entity in ipairs(world.entities) do
            if entity.position then
                local distance = math.sqrt(
                    (entity.position.x - attacker.position.x)^2 + 
                    (entity.position.y - attacker.position.y)^2
                )
                
                if distance <= weapon.properties.voidRadius * 3 then
                    -- Apply random void effect
                    local effect = math.random()
                    if effect < 0.4 then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.void)
                        end
                    elseif effect < 0.7 then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.voidBurst)
                        end
                    else
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.soulSteal)
                        end
                    end
                end
            end
        end
    end
    
    -- Reset void charge and consume souls
    weapon.properties.weaponState.voidCharge = 0
    weapon.properties.weaponState.lastChargeTime = world.time
    weapon.properties.weaponState.soulCount = math.max(0,
        weapon.properties.weaponState.soulCount - 2)
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 5)
    
    return true
end

return VoidReaper 