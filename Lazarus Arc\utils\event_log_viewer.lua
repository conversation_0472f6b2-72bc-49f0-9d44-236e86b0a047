-- utils/event_log_viewer.lua
-- Event log viewer for debugging

local EventLogViewer = {}

-- Initialize event log viewer
function EventLogViewer.init()
    EventLogViewer.enabled = false
    EventLogViewer.events = {}
    EventLogViewer.filteredEvents = {}
    EventLogViewer.maxEvents = 1000
    
    -- Display settings
    EventLogViewer.x = 400
    EventLogViewer.y = 50
    EventLogViewer.width = 500
    EventLogViewer.height = 400
    EventLogViewer.backgroundColor = {0, 0, 0, 0.9}
    EventLogViewer.borderColor = {0.5, 0.5, 0.5, 1}
    EventLogViewer.textColor = {1, 1, 1, 1}
    EventLogViewer.headerColor = {0.8, 0.8, 0.8, 1}
    
    -- Scrolling
    EventLogViewer.scrollOffset = 0
    EventLogViewer.lineHeight = 16
    EventLogViewer.visibleLines = 20
    
    -- Filtering
    EventLogViewer.filters = {
        type = "all", -- all, combat, movement, interaction, system
        level = "all", -- all, info, warning, error
        searchText = ""
    }
    
    -- Event type colors
    EventLogViewer.typeColors = {
        combat = {1, 0.3, 0.3, 1},
        movement = {0.3, 1, 0.3, 1},
        interaction = {0.3, 0.3, 1, 1},
        system = {1, 1, 0.3, 1},
        error = {1, 0.2, 0.2, 1},
        warning = {1, 0.8, 0.2, 1},
        info = {0.8, 0.8, 0.8, 1},
        default = {1, 1, 1, 1}
    }
    
    -- UI elements
    EventLogViewer.buttons = {}
    EventLogViewer.inputActive = false
    EventLogViewer.inputText = ""
end

-- Add event to log
function EventLogViewer.addEvent(eventType, level, message, details)
    local event = {
        timestamp = love.timer.getTime(),
        type = eventType or "system",
        level = level or "info",
        message = message or "",
        details = details or {},
        id = #EventLogViewer.events + 1
    }
    
    table.insert(EventLogViewer.events, event)
    
    -- Remove old events if we exceed max
    if #EventLogViewer.events > EventLogViewer.maxEvents then
        table.remove(EventLogViewer.events, 1)
    end
    
    -- Update filtered events
    EventLogViewer.updateFilteredEvents()
end

-- Update filtered events based on current filters
function EventLogViewer.updateFilteredEvents()
    EventLogViewer.filteredEvents = {}
    
    for _, event in ipairs(EventLogViewer.events) do
        if EventLogViewer.passesFilter(event) then
            table.insert(EventLogViewer.filteredEvents, event)
        end
    end
    
    -- Auto-scroll to bottom when new events are added
    EventLogViewer.scrollOffset = math.max(0, #EventLogViewer.filteredEvents - EventLogViewer.visibleLines)
end

-- Check if event passes current filters
function EventLogViewer.passesFilter(event)
    -- Type filter
    if EventLogViewer.filters.type ~= "all" and event.type ~= EventLogViewer.filters.type then
        return false
    end
    
    -- Level filter
    if EventLogViewer.filters.level ~= "all" and event.level ~= EventLogViewer.filters.level then
        return false
    end
    
    -- Search text filter
    if EventLogViewer.filters.searchText ~= "" then
        local searchLower = string.lower(EventLogViewer.filters.searchText)
        local messageLower = string.lower(event.message)
        if not string.find(messageLower, searchLower, 1, true) then
            return false
        end
    end
    
    return true
end

-- Toggle event log viewer
function EventLogViewer.toggle()
    EventLogViewer.enabled = not EventLogViewer.enabled
    
    if EventLogViewer.enabled then
        EventLogViewer.updateFilteredEvents()
        print("Event Log Viewer opened")
    else
        print("Event Log Viewer closed")
    end
    
    return EventLogViewer.enabled
end

-- Draw event log viewer
function EventLogViewer.draw()
    if not EventLogViewer.enabled then return end
    
    love.graphics.push()
    
    -- Draw background
    love.graphics.setColor(EventLogViewer.backgroundColor)
    love.graphics.rectangle("fill", EventLogViewer.x, EventLogViewer.y, EventLogViewer.width, EventLogViewer.height)
    
    -- Draw border
    love.graphics.setColor(EventLogViewer.borderColor)
    love.graphics.setLineWidth(1)
    love.graphics.rectangle("line", EventLogViewer.x, EventLogViewer.y, EventLogViewer.width, EventLogViewer.height)
    
    -- Draw header
    EventLogViewer.drawHeader()
    
    -- Draw filters
    EventLogViewer.drawFilters()
    
    -- Draw events
    EventLogViewer.drawEvents()
    
    -- Draw scrollbar
    EventLogViewer.drawScrollbar()
    
    love.graphics.pop()
end

-- Draw header
function EventLogViewer.drawHeader()
    love.graphics.setColor(EventLogViewer.headerColor)
    love.graphics.setFont(love.graphics.getFont())
    
    local headerY = EventLogViewer.y + 10
    love.graphics.print("Event Log Viewer", EventLogViewer.x + 10, headerY)
    
    -- Event count
    local countText = string.format("(%d/%d events)", #EventLogViewer.filteredEvents, #EventLogViewer.events)
    local countWidth = love.graphics.getFont():getWidth(countText)
    love.graphics.print(countText, EventLogViewer.x + EventLogViewer.width - countWidth - 10, headerY)
end

-- Draw filters
function EventLogViewer.drawFilters()
    local filterY = EventLogViewer.y + 35
    local filterHeight = 60
    
    -- Filter background
    love.graphics.setColor(0.1, 0.1, 0.1, 0.8)
    love.graphics.rectangle("fill", EventLogViewer.x + 5, filterY, EventLogViewer.width - 10, filterHeight)
    
    love.graphics.setColor(EventLogViewer.textColor)
    
    -- Type filter
    love.graphics.print("Type:", EventLogViewer.x + 10, filterY + 5)
    EventLogViewer.drawFilterButton("all", EventLogViewer.x + 50, filterY + 5, "type")
    EventLogViewer.drawFilterButton("combat", EventLogViewer.x + 80, filterY + 5, "type")
    EventLogViewer.drawFilterButton("movement", EventLogViewer.x + 130, filterY + 5, "type")
    EventLogViewer.drawFilterButton("interaction", EventLogViewer.x + 190, filterY + 5, "type")
    EventLogViewer.drawFilterButton("system", EventLogViewer.x + 260, filterY + 5, "type")
    
    -- Level filter
    love.graphics.print("Level:", EventLogViewer.x + 10, filterY + 25)
    EventLogViewer.drawFilterButton("all", EventLogViewer.x + 50, filterY + 25, "level")
    EventLogViewer.drawFilterButton("info", EventLogViewer.x + 80, filterY + 25, "level")
    EventLogViewer.drawFilterButton("warning", EventLogViewer.x + 115, filterY + 25, "level")
    EventLogViewer.drawFilterButton("error", EventLogViewer.x + 165, filterY + 25, "level")
    
    -- Search box
    love.graphics.print("Search:", EventLogViewer.x + 10, filterY + 45)
    EventLogViewer.drawSearchBox(EventLogViewer.x + 60, filterY + 45, 200, 15)
end

-- Draw filter button
function EventLogViewer.drawFilterButton(text, x, y, filterType)
    local isActive = EventLogViewer.filters[filterType] == text
    local width = love.graphics.getFont():getWidth(text) + 6
    local height = 15
    
    -- Button background
    if isActive then
        love.graphics.setColor(0.3, 0.5, 0.7, 0.8)
    else
        love.graphics.setColor(0.2, 0.2, 0.2, 0.8)
    end
    love.graphics.rectangle("fill", x, y, width, height)
    
    -- Button border
    love.graphics.setColor(0.5, 0.5, 0.5, 1)
    love.graphics.rectangle("line", x, y, width, height)
    
    -- Button text
    love.graphics.setColor(isActive and {1, 1, 1, 1} or {0.7, 0.7, 0.7, 1})
    love.graphics.print(text, x + 3, y + 1)
    
    -- Store button for click detection
    EventLogViewer.buttons[filterType .. "_" .. text] = {x = x, y = y, width = width, height = height, 
                                                         action = function() EventLogViewer.setFilter(filterType, text) end}
end

-- Draw search box
function EventLogViewer.drawSearchBox(x, y, width, height)
    -- Search box background
    love.graphics.setColor(0.2, 0.2, 0.2, 0.9)
    love.graphics.rectangle("fill", x, y, width, height)
    
    -- Search box border
    love.graphics.setColor(EventLogViewer.inputActive and {0.5, 0.7, 1, 1} or {0.5, 0.5, 0.5, 1})
    love.graphics.rectangle("line", x, y, width, height)
    
    -- Search text
    love.graphics.setColor(EventLogViewer.textColor)
    local displayText = EventLogViewer.filters.searchText
    if EventLogViewer.inputActive then
        displayText = displayText .. "|" -- Cursor
    end
    love.graphics.print(displayText, x + 3, y + 1)
    
    -- Store search box for click detection
    EventLogViewer.buttons.searchBox = {x = x, y = y, width = width, height = height,
                                       action = function() EventLogViewer.activateSearch() end}
end

-- Draw events list
function EventLogViewer.drawEvents()
    local eventsY = EventLogViewer.y + 105
    local eventsHeight = EventLogViewer.height - 115
    
    -- Events background
    love.graphics.setColor(0.05, 0.05, 0.05, 0.9)
    love.graphics.rectangle("fill", EventLogViewer.x + 5, eventsY, EventLogViewer.width - 25, eventsHeight)
    
    -- Draw events
    for i = 1, EventLogViewer.visibleLines do
        local eventIndex = EventLogViewer.scrollOffset + i
        if eventIndex <= #EventLogViewer.filteredEvents then
            local event = EventLogViewer.filteredEvents[eventIndex]
            EventLogViewer.drawEvent(event, EventLogViewer.x + 10, eventsY + (i - 1) * EventLogViewer.lineHeight)
        end
    end
end

-- Draw single event
function EventLogViewer.drawEvent(event, x, y)
    -- Format timestamp
    local timestamp = string.format("%.2f", event.timestamp)
    
    -- Event type color
    local typeColor = EventLogViewer.typeColors[event.type] or EventLogViewer.typeColors.default
    
    -- Draw timestamp
    love.graphics.setColor(0.6, 0.6, 0.6, 1)
    love.graphics.print(timestamp, x, y)
    
    -- Draw type
    love.graphics.setColor(typeColor)
    love.graphics.print("[" .. event.type .. "]", x + 60, y)
    
    -- Draw level
    local levelColor = EventLogViewer.typeColors[event.level] or EventLogViewer.typeColors.default
    love.graphics.setColor(levelColor)
    love.graphics.print(event.level:upper(), x + 120, y)
    
    -- Draw message
    love.graphics.setColor(EventLogViewer.textColor)
    local maxMessageWidth = EventLogViewer.width - 180
    local message = event.message
    if love.graphics.getFont():getWidth(message) > maxMessageWidth then
        -- Truncate message if too long
        while love.graphics.getFont():getWidth(message .. "...") > maxMessageWidth and #message > 0 do
            message = message:sub(1, -2)
        end
        message = message .. "..."
    end
    love.graphics.print(message, x + 160, y)
end

-- Draw scrollbar
function EventLogViewer.drawScrollbar()
    if #EventLogViewer.filteredEvents <= EventLogViewer.visibleLines then return end
    
    local scrollbarX = EventLogViewer.x + EventLogViewer.width - 15
    local scrollbarY = EventLogViewer.y + 105
    local scrollbarHeight = EventLogViewer.height - 115
    
    -- Scrollbar background
    love.graphics.setColor(0.2, 0.2, 0.2, 0.8)
    love.graphics.rectangle("fill", scrollbarX, scrollbarY, 10, scrollbarHeight)
    
    -- Scrollbar thumb
    local thumbHeight = math.max(20, scrollbarHeight * (EventLogViewer.visibleLines / #EventLogViewer.filteredEvents))
    local thumbY = scrollbarY + (EventLogViewer.scrollOffset / (#EventLogViewer.filteredEvents - EventLogViewer.visibleLines)) * (scrollbarHeight - thumbHeight)
    
    love.graphics.setColor(0.5, 0.5, 0.5, 1)
    love.graphics.rectangle("fill", scrollbarX, thumbY, 10, thumbHeight)
end

-- Set filter
function EventLogViewer.setFilter(filterType, value)
    EventLogViewer.filters[filterType] = value
    EventLogViewer.updateFilteredEvents()
end

-- Activate search input
function EventLogViewer.activateSearch()
    EventLogViewer.inputActive = true
    love.keyboard.setTextInput(true)
end

-- Handle text input
function EventLogViewer.textInput(text)
    if EventLogViewer.inputActive then
        EventLogViewer.filters.searchText = EventLogViewer.filters.searchText .. text
        EventLogViewer.updateFilteredEvents()
    end
end

-- Handle key press
function EventLogViewer.keyPressed(key)
    if not EventLogViewer.enabled then return false end
    
    if EventLogViewer.inputActive then
        if key == "backspace" then
            EventLogViewer.filters.searchText = EventLogViewer.filters.searchText:sub(1, -2)
            EventLogViewer.updateFilteredEvents()
        elseif key == "return" or key == "escape" then
            EventLogViewer.inputActive = false
            love.keyboard.setTextInput(false)
        end
        return true
    end
    
    return false
end

-- Handle mouse click
function EventLogViewer.mousePressed(x, y, button)
    if not EventLogViewer.enabled then return false end
    
    -- Check button clicks
    for _, btn in pairs(EventLogViewer.buttons) do
        if x >= btn.x and x <= btn.x + btn.width and y >= btn.y and y <= btn.y + btn.height then
            btn.action()
            return true
        end
    end
    
    return false
end

-- Handle mouse wheel for scrolling
function EventLogViewer.wheelMoved(x, y)
    if not EventLogViewer.enabled then return false end
    
    EventLogViewer.scrollOffset = math.max(0, math.min(#EventLogViewer.filteredEvents - EventLogViewer.visibleLines, 
                                                      EventLogViewer.scrollOffset - y * 3))
    return true
end

-- Clear all events
function EventLogViewer.clear()
    EventLogViewer.events = {}
    EventLogViewer.filteredEvents = {}
    EventLogViewer.scrollOffset = 0
    print("Event log cleared")
end

return EventLogViewer
