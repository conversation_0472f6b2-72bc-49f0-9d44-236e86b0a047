-- anime_renderer.lua
-- A simple anime-style character renderer for Lazarus Arc

local AnimeRenderer = {}

-- Default settings
AnimeRenderer.settings = {
    defaultScale = 1.0,
    defaultColor = {1, 1, 1, 1},
    defaultShadowColor = {0, 0, 0, 0.5},
    defaultOutlineColor = {0, 0, 0, 1},
    defaultOutlineWidth = 1,
    enableShadows = true,
    enableOutlines = true,
    enableAnimations = true,
    debugMode = false
}

-- Character part definitions
AnimeRenderer.parts = {
    head = {
        styles = {
            -- Style 1: Round head
            {
                draw = function(x, y, scale, color)
                    -- Draw head shape
                    love.graphics.setColor(color)
                    love.graphics.circle("fill", x, y, 15 * scale)

                    -- Draw outline
                    if AnimeRenderer.settings.enableOutlines then
                        love.graphics.setColor(AnimeRenderer.settings.defaultOutlineColor)
                        love.graphics.setLineWidth(AnimeRenderer.settings.defaultOutlineWidth)
                        love.graphics.circle("line", x, y, 15 * scale)
                    end
                end
            },
            -- Style 2: Oval head
            {
                draw = function(x, y, scale, color)
                    -- Draw head shape
                    love.graphics.setColor(color)
                    love.graphics.ellipse("fill", x, y, 12 * scale, 16 * scale)

                    -- Draw outline
                    if AnimeRenderer.settings.enableOutlines then
                        love.graphics.setColor(AnimeRenderer.settings.defaultOutlineColor)
                        love.graphics.setLineWidth(AnimeRenderer.settings.defaultOutlineWidth)
                        love.graphics.ellipse("line", x, y, 12 * scale, 16 * scale)
                    end
                end
            }
        }
    },

    eyes = {
        styles = {
            -- Style 1: Simple eyes
            {
                draw = function(x, y, scale, color)
                    -- Draw eyes
                    love.graphics.setColor(color)
                    love.graphics.circle("fill", x - 5 * scale, y - 2 * scale, 2 * scale)
                    love.graphics.circle("fill", x + 5 * scale, y - 2 * scale, 2 * scale)
                end
            },
            -- Style 2: Large anime eyes
            {
                draw = function(x, y, scale, color)
                    -- Draw eye whites
                    love.graphics.setColor(1, 1, 1)
                    love.graphics.ellipse("fill", x - 5 * scale, y - 2 * scale, 4 * scale, 5 * scale)
                    love.graphics.ellipse("fill", x + 5 * scale, y - 2 * scale, 4 * scale, 5 * scale)

                    -- Draw pupils
                    love.graphics.setColor(color)
                    love.graphics.circle("fill", x - 5 * scale, y - 2 * scale, 2 * scale)
                    love.graphics.circle("fill", x + 5 * scale, y - 2 * scale, 2 * scale)

                    -- Draw highlights
                    love.graphics.setColor(1, 1, 1)
                    love.graphics.circle("fill", x - 6 * scale, y - 3 * scale, 1 * scale)
                    love.graphics.circle("fill", x + 4 * scale, y - 3 * scale, 1 * scale)

                    -- Draw outlines
                    if AnimeRenderer.settings.enableOutlines then
                        love.graphics.setColor(AnimeRenderer.settings.defaultOutlineColor)
                        love.graphics.setLineWidth(AnimeRenderer.settings.defaultOutlineWidth)
                        love.graphics.ellipse("line", x - 5 * scale, y - 2 * scale, 4 * scale, 5 * scale)
                        love.graphics.ellipse("line", x + 5 * scale, y - 2 * scale, 4 * scale, 5 * scale)
                    end
                end
            }
        }
    },

    hair = {
        styles = {
            -- Style 1: Short hair
            {
                draw = function(x, y, scale, color)
                    -- Draw hair
                    love.graphics.setColor(color)

                    -- Top of head
                    love.graphics.rectangle("fill", x - 15 * scale, y - 15 * scale, 30 * scale, 10 * scale)

                    -- Sides
                    love.graphics.rectangle("fill", x - 15 * scale, y - 15 * scale, 5 * scale, 20 * scale)
                    love.graphics.rectangle("fill", x + 10 * scale, y - 15 * scale, 5 * scale, 20 * scale)

                    -- Draw outline
                    if AnimeRenderer.settings.enableOutlines then
                        love.graphics.setColor(AnimeRenderer.settings.defaultOutlineColor)
                        love.graphics.setLineWidth(AnimeRenderer.settings.defaultOutlineWidth)

                        -- Top outline
                        love.graphics.line(
                            x - 15 * scale, y - 15 * scale,
                            x + 15 * scale, y - 15 * scale
                        )

                        -- Side outlines
                        love.graphics.line(
                            x - 15 * scale, y - 15 * scale,
                            x - 15 * scale, y + 5 * scale
                        )
                        love.graphics.line(
                            x + 15 * scale, y - 15 * scale,
                            x + 15 * scale, y + 5 * scale
                        )
                    end
                end
            },
            -- Style 2: Long hair
            {
                draw = function(x, y, scale, color)
                    -- Draw hair
                    love.graphics.setColor(color)

                    -- Top of head
                    love.graphics.rectangle("fill", x - 15 * scale, y - 15 * scale, 30 * scale, 10 * scale)

                    -- Sides and back
                    love.graphics.rectangle("fill", x - 15 * scale, y - 15 * scale, 5 * scale, 30 * scale)
                    love.graphics.rectangle("fill", x + 10 * scale, y - 15 * scale, 5 * scale, 30 * scale)
                    love.graphics.rectangle("fill", x - 10 * scale, y + 15 * scale, 20 * scale, 15 * scale)

                    -- Draw outline
                    if AnimeRenderer.settings.enableOutlines then
                        love.graphics.setColor(AnimeRenderer.settings.defaultOutlineColor)
                        love.graphics.setLineWidth(AnimeRenderer.settings.defaultOutlineWidth)

                        -- Outline
                        love.graphics.line(
                            x - 15 * scale, y - 15 * scale,
                            x + 15 * scale, y - 15 * scale
                        )
                        love.graphics.line(
                            x - 15 * scale, y - 15 * scale,
                            x - 15 * scale, y + 15 * scale
                        )
                        love.graphics.line(
                            x + 15 * scale, y - 15 * scale,
                            x + 15 * scale, y + 15 * scale
                        )
                        love.graphics.line(
                            x - 15 * scale, y + 15 * scale,
                            x - 10 * scale, y + 15 * scale
                        )
                        love.graphics.line(
                            x + 15 * scale, y + 15 * scale,
                            x + 10 * scale, y + 15 * scale
                        )
                        love.graphics.line(
                            x - 10 * scale, y + 30 * scale,
                            x + 10 * scale, y + 30 * scale
                        )
                        love.graphics.line(
                            x - 10 * scale, y + 15 * scale,
                            x - 10 * scale, y + 30 * scale
                        )
                        love.graphics.line(
                            x + 10 * scale, y + 15 * scale,
                            x + 10 * scale, y + 30 * scale
                        )
                    end
                end
            }
        }
    },

    mouth = {
        styles = {
            -- Style 1: Simple line
            {
                draw = function(x, y, scale, color)
                    -- Draw mouth
                    love.graphics.setColor(color)
                    love.graphics.line(x - 3 * scale, y + 5 * scale, x + 3 * scale, y + 5 * scale)
                end
            },
            -- Style 2: Smile
            {
                draw = function(x, y, scale, color)
                    -- Draw mouth
                    love.graphics.setColor(color)
                    love.graphics.arc("line", x, y + 5 * scale, 5 * scale, 0.2, 2.9)
                end
            }
        }
    },

    body = {
        styles = {
            -- Style 1: Average body
            {
                draw = function(x, y, scale, color)
                    -- Draw body
                    love.graphics.setColor(color)

                    -- Torso
                    love.graphics.rectangle("fill", x - 10 * scale, y + 15 * scale, 20 * scale, 25 * scale)

                    -- Draw outline
                    if AnimeRenderer.settings.enableOutlines then
                        love.graphics.setColor(AnimeRenderer.settings.defaultOutlineColor)
                        love.graphics.setLineWidth(AnimeRenderer.settings.defaultOutlineWidth)
                        love.graphics.rectangle("line", x - 10 * scale, y + 15 * scale, 20 * scale, 25 * scale)
                    end
                end
            }
        }
    }
}

-- Initialize the renderer
function AnimeRenderer.init(options)
    print("Initializing Anime Renderer...")

    -- Apply custom settings if provided
    if options then
        for k, v in pairs(options) do
            AnimeRenderer.settings[k] = v
        end
    end

    -- Initialize any resources needed
    -- (none for now, but could load textures or shaders here)

    print("Anime Renderer initialized")
    return AnimeRenderer
end

-- Draw a character at the specified position
function AnimeRenderer.drawCharacter(x, y, scale, gender, appearance)
    -- Default values and error handling
    if not x or not y then
        x, y = 0, 0
        print("Warning: Invalid position for character drawing, using (0,0)")
    end

    scale = scale or AnimeRenderer.settings.defaultScale
    gender = gender or "male"
    appearance = appearance or {}

    -- Get skin color based on skin type
    local skinColors = {
        light = {0.98, 0.86, 0.78},
        medium = {0.94, 0.78, 0.62},
        dark = {0.55, 0.37, 0.23},
        tan = {0.92, 0.73, 0.52}
    }
    local skinColor = skinColors[appearance.skin] or skinColors.medium

    -- Get hair color
    local hairColors = {
        black = {0.1, 0.1, 0.1},
        brown = {0.4, 0.2, 0.1},
        blonde = {0.9, 0.8, 0.4},
        red = {0.8, 0.3, 0.2},
        blue = {0.3, 0.4, 0.8},
        green = {0.3, 0.8, 0.4},
        purple = {0.6, 0.3, 0.8},
        white = {0.9, 0.9, 0.9},
        gray = {0.7, 0.7, 0.7}
    }
    local hairColor = hairColors.brown
    if appearance.hair and appearance.hair.color then
        hairColor = hairColors[appearance.hair.color] or hairColors.brown
    end

    -- Get eye color
    local eyeColors = {
        blue = {0.3, 0.5, 0.9},
        green = {0.3, 0.8, 0.4},
        brown = {0.5, 0.3, 0.1},
        gray = {0.5, 0.5, 0.5},
        amber = {0.8, 0.6, 0.2},
        red = {0.8, 0.2, 0.2}
    }
    local eyeColor = eyeColors.blue
    if appearance.eyes and appearance.eyes.color then
        eyeColor = eyeColors[appearance.eyes.color] or eyeColors.blue
    end

    -- Draw shadow if enabled
    if AnimeRenderer.settings.enableShadows then
        love.graphics.setColor(AnimeRenderer.settings.defaultShadowColor)
        love.graphics.ellipse("fill", x, y + 40 * scale, 15 * scale, 5 * scale)
    end

    -- Draw body
    local bodyStyle = appearance.body and appearance.body.type or "average"
    local bodyStyleIndex = 1 -- Default to first style
    AnimeRenderer.parts.body.styles[bodyStyleIndex].draw(x, y, scale, {0.2, 0.4, 0.8}) -- Blue shirt

    -- Draw head
    local headStyleIndex = appearance.head or 1
    if headStyleIndex < 1 or headStyleIndex > #AnimeRenderer.parts.head.styles then
        headStyleIndex = 1
    end
    AnimeRenderer.parts.head.styles[headStyleIndex].draw(x, y, scale, skinColor)

    -- Draw hair
    local hairStyleIndex = appearance.hair and appearance.hair.style or 1
    if hairStyleIndex < 1 or hairStyleIndex > #AnimeRenderer.parts.hair.styles then
        hairStyleIndex = 1
    end
    AnimeRenderer.parts.hair.styles[hairStyleIndex].draw(x, y, scale, hairColor)

    -- Draw eyes
    local eyeStyleIndex = appearance.eyes and appearance.eyes.style or 1
    if eyeStyleIndex < 1 or eyeStyleIndex > #AnimeRenderer.parts.eyes.styles then
        eyeStyleIndex = 1
    end
    AnimeRenderer.parts.eyes.styles[eyeStyleIndex].draw(x, y, scale, eyeColor)

    -- Draw mouth
    local mouthStyleIndex = appearance.mouth or 1
    if mouthStyleIndex < 1 or mouthStyleIndex > #AnimeRenderer.parts.mouth.styles then
        mouthStyleIndex = 1
    end
    AnimeRenderer.parts.mouth.styles[mouthStyleIndex].draw(x, y, scale, {0.8, 0.2, 0.2})

    -- Draw debug info if enabled
    if AnimeRenderer.settings.debugMode then
        love.graphics.setColor(1, 0, 0, 0.5)
        love.graphics.circle("line", x, y, 2)
        love.graphics.line(x - 5, y, x + 5, y)
        love.graphics.line(x, y - 5, x, y + 5)

        love.graphics.setColor(1, 1, 1, 0.8)
        love.graphics.print("Character: " .. gender, x + 20, y - 30)
        love.graphics.print("Head: " .. headStyleIndex, x + 20, y - 15)
        love.graphics.print("Hair: " .. hairStyleIndex, x + 20, y)
        love.graphics.print("Eyes: " .. eyeStyleIndex, x + 20, y + 15)
        love.graphics.print("Mouth: " .. mouthStyleIndex, x + 20, y + 30)
    end

    -- Reset color
    love.graphics.setColor(1, 1, 1, 1)
end

return AnimeRenderer
