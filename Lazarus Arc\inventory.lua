-- inventory.lua
-- UUID-based inventory system for Lazarus Arc

local ItemDatabase = require("item_database")
local UUID = require("utils.uuid")
local json = require("lunajson")


local Inventory = {}

function Inventory.new(maxWeight)
    local self = {
        items = {},           -- [uuid] = itemInstance
        maxWeight = maxWeight or 100,
        currentWeight = 0,
        gold = 0,
        categories = {},
        favoriteItems = {},
        identifiedItems = {},
        customCategories = {},
        maxHistorySize = 10,
        itemHistory = {}
    }

    -- Add an item (creates new unique instances)
    function self.addItem(baseId, quantity)
        quantity = quantity or 1
        local base = ItemDatabase.getTemplate(baseId)
        if not base then
            print("Invalid item ID:", baseId)
            return false
        end

        for _ = 1, quantity do
            local item = ItemDatabase.createItemInstance(baseId)
            if not item then
                print("Failed to create item instance.")
                return false
            end

            local weight = base.weight or 1
            if self.currentWeight + weight > self.maxWeight then
                print("Cannot carry more. Inventory full.")
                return false
            end

            self.items[item.uuid] = item
            self.currentWeight = self.currentWeight + weight

            -- Add to category
            self.categories[item.category] = self.categories[item.category] or {}
            table.insert(self.categories[item.category], item.uuid)

            self.addToHistory("added", item.uuid, 1)
        end

        return true
    end

    -- Remove an item by UUID
    function self.removeItem(uuid)
        local item = self.items[uuid]
        if not item then return false end

        self.currentWeight = self.currentWeight - (item.weight or 1)

        -- Remove from category
        local cat = self.categories[item.category]
        if cat then
            for i, id in ipairs(cat) do
                if id == uuid then table.remove(cat, i) break end
            end
        end

        self.items[uuid] = nil
        self.favoriteItems[uuid] = nil
        self.identifiedItems[uuid] = nil
        self.addToHistory("removed", uuid, 1)
        return true
    end

    -- Get item instance
    function self.get(uuid)
        return self.items[uuid]
    end

    function self.identifyItem(uuid)
        if not self.items[uuid] then return false end
        self.identifiedItems[uuid] = true
        return true
    end

    function self.isIdentified(uuid)
        return self.identifiedItems[uuid] == true
    end

    function self.toggleFavorite(uuid)
        self.favoriteItems[uuid] = not self.favoriteItems[uuid]
    end

    function self.addToHistory(action, uuid, amount)
        if #self.itemHistory >= self.maxHistorySize then table.remove(self.itemHistory, 1) end
        table.insert(self.itemHistory, {
            action = action,
            uuid = uuid,
            timestamp = os.time(),
            quantity = amount or 1
        })
    end

    function self.listItems()
        print("Inventory Contents:")
        for _, item in pairs(self.items) do
            local id = item.uuid
            local name = item.name or item.baseId
            local extra = self.favoriteItems[id] and "★" or ""
            local info = self.isIdentified(id) and name or ("Unknown " .. item.category)
            print("  - " .. info .. " [" .. id .. "] " .. extra)
        end
        print("Weight: " .. self.currentWeight .. " / " .. self.maxWeight)
    end

    -- Serialize for saving
    function self.serialize()
        local data = {
            gold = self.gold,
            maxWeight = self.maxWeight,
            items = {},
            favoriteItems = self.favoriteItems,
            identifiedItems = self.identifiedItems,
            customCategories = self.customCategories
        }

        for uuid, item in pairs(self.items) do
            data.items[uuid] = {
                baseId = item.baseId,
                name = item.name,
                category = item.category,
                value = item.value,
                stats = item.stats,
                createdAt = item.createdAt,
                stackable = item.stackable,
                weight = item.weight or 1,
                meta = item.meta
            }
        end

        return data
    end

    return self
end

-- Load from saved data
function Inventory.load(data)
    local inv = Inventory.new(data.maxWeight or 100)
    inv.gold = data.gold or 0
    inv.favoriteItems = data.favoriteItems or {}
    inv.identifiedItems = data.identifiedItems or {}
    inv.customCategories = data.customCategories or {}

    for uuid, saved in pairs(data.items or {}) do
        local item = {
            uuid = uuid,
            baseId = saved.baseId,
            name = saved.name,
            category = saved.category,
            value = saved.value,
            stats = saved.stats,
            createdAt = saved.createdAt,
            stackable = saved.stackable,
            weight = saved.weight or 1,
            meta = saved.meta or {}
        }

        inv.items[uuid] = item
        inv.categories[item.category] = inv.categories[item.category] or {}
        table.insert(inv.categories[item.category], uuid)
        inv.currentWeight = inv.currentWeight + item.weight
    end

    return inv
end

return Inventory
