local Vulture = {
    id = "vulture",
    name = "Vulture",
    type = "bird",
    
    -- Entity categories
    categories = {"animal", "bird", "flying", "scavenger"},
    
    -- Threat and food categories
    threatCategories = {"player", "eagle"},
    foodCategories = {"carrion", "rotten", "meat"},
    
    -- Stats
    maxHealth = 40,
    health = 40,
    maxStamina = 100,
    stamina = 100,
    speed = 2.0,
    
    -- Flight properties
    flight = {
        maxHeight = 20,
        minHeight = 2,
        ascentSpeed = 0.8,
        descentSpeed = 1.5,
        hoverHeight = 8,
        currentHeight = 8,
        wingFlapRate = 0.05,
        soarChance = 0.9
    },
    
    -- Behaviors
    behaviors = {"scavenge", "patrol", "roost", "group"},
    
    -- Behavior configurations
    behaviorConfigs = {
        scavenge = {
            moveSpeed = 2.5,
            searchRadius = 40,
            diveSpeed = 3.0,
            attackRange = 2,
            damage = 15,
            preferredFood = {"carrion", "rotten", "meat"},
            smellRange = 50
        },
        patrol = {
            moveSpeed = 2.0,
            patrolRadius = 60,
            soarChance = 0.9,
            restInterval = {15, 30}
        },
        roost = {
            startTime = "dusk",
            endTime = "dawn",
            roostHeight = {5, 10},
            healthRegen = 0.03,
            staminaRegen = 0.1
        },
        group = {
            moveSpeed = 2.2,
            followDistance = 5,
            separationDistance = 3,
            alignmentStrength = 0.2,
            cohesionStrength = 0.3,
            maxGroupSize = 8,
            feedingRadius = 3
        }
    },
    
    -- Special abilities
    abilities = {
        dive = {
            speedBoost = 3.0,
            duration = 2,
            cooldown = 6,
            staminaCost = 15
        },
        beakRip = {
            damage = 10,
            duration = 1.5,
            cooldown = 2,
            staminaCost = 8
        },
        hiss = {
            range = 8,
            duration = 1,
            cooldown = 3,
            effect = "intimidate"
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "vulture",
        scale = 1.0,
        animations = {
            "idle", "fly", "dive", "feed", "hiss"
        },
        variants = {
            "turkey", "black", "griffon", "king"
        }
    },
    
    -- Sound effects
    sounds = {
        hiss = "vulture_hiss",
        wingFlap = "vulture_wing_flap",
        call = "vulture_call"
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.6, quantity = {2, 3}},
        {id = "feather", chance = 0.7, quantity = {2, 4}},
        {id = "beak", chance = 0.3, quantity = {1, 1}},
        {id = "bone", chance = 0.4, quantity = {1, 2}}
    }
}

-- Initialize the entity
function Vulture.init(entity, world)
    -- Copy all fields from Vulture template to entity instance
    for k, v in pairs(Vulture) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random vulture variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Vulture.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update flight properties
    if entity.flight then
        -- Update wing flapping based on height and activity
        if entity.flight.currentHeight > entity.flight.minHeight then
            if entity.flight.soarChance > math.random() then
                entity.flight.wingFlapRate = 0.02
            else
                entity.flight.wingFlapRate = 0.05
            end
        else
            entity.flight.wingFlapRate = 0.1
        end
    end
end

return Vulture 