-- engine.lua
-- Minimal central coordinator for Lazarus Arc

local Engine = {
    initialized = false,
    systems = {}, -- References to loaded subsystems
    gameState = "startup",
    nextState = nil,
    stateHandlers = {}, -- Will be replaced by state_handlers module
    settings = {}, -- Will be loaded from conf.lua
    debug = true, -- Enable debug mode to help with UI issues
    debugMode = true, -- Alternative name for debug flag
    currentWorld = nil  -- Added to store the current world instance
}

-- Initialize engine
function Engine.init()
    print("Initializing Lazarus Arc Engine...")

    -- Load settings from conf.lua
    Engine.loadSettings()

    -- Initialize game time
    Engine.time = 0

    -- Track if we're in a session
    Engine.inSession = false

    -- Load and initialize core systems
    Engine.systems.controller = require("controller")
    Engine.systems.controller.init()

    Engine.systems.worldCore = require("world_core")

    -- Load gameplay systems
    Engine.systems.inventory = require("inventory")
    Engine.systems.combat = require("combat")
    Engine.systems.magic = require("magic")
    Engine.systems.skills = require("skills")
    Engine.systems.classes = require("classes")
    Engine.systems.characterCreator = require("character_creator")

    -- Load utility systems
    Engine.timer = require("utils.timer_system"):init()
    Engine.systems.effectSystem = require("utils.effect_system"):init()
    Engine.systems.uiSystem = require("utils.ui_system"):init({
        screenWidth = Engine.settings.screenWidth,
        screenHeight = Engine.settings.screenHeight
    })
    Engine.systems.colorUtils = require("utils.color_utils")

    -- Initialize the new Renderer system
    Engine.systems.renderer = require("utils.renderer"):init({
        settings = Engine.settings,
        colorUtils = Engine.systems.colorUtils
    })

    Engine.systems.soundSystem = require("utils.sound_system")
    Engine.systems.soundSystem.init({
        musicVolume = Engine.settings.musicVolume,
        sfxVolume = Engine.settings.sfxVolume,
        enabled = Engine.settings.soundEnabled
    })

    Engine.systems.viewportManager = require("viewport_management").new(
        Engine.settings.screenWidth,
        Engine.settings.screenHeight
    )

    -- Load database and event log systems (with special handling for circular dependencies)
    Engine.systems.database = require("utils.database_manager")
    -- Add an alias for database_manager to ensure compatibility
    Engine.systems.databaseManager = Engine.systems.database
    Engine.systems.eventLog = require("utils.event_log")

    -- Connect the two systems to each other
    Engine.systems.database.setEventLogger(Engine.systems.eventLog)
    Engine.systems.eventLog.setDatabaseManager(Engine.systems.database)

    print("Database manager loaded and aliased")

    -- Initialize database connection
    local dbSuccess = Engine.systems.database.connect()
    if dbSuccess then
        print("Database connection successful")
    else
        print("WARNING: Database connection failed, using fallback storage")
    end

    Engine.systems.nfc = require("utils.nfc_system")
    Engine.systems.nfc.init(Engine.onNFCConnect, Engine.onNFCDisconnect)

    Engine.systems.teleport = require("teleport_system")
    Engine.systems.teleport.init(Engine.onTeleport)

    -- Initialize required subsystems for world functionality
    Engine.systems.entityManager = require("utils.entity_manager").new()
    Engine.systems.chunkSystem = require("chunk_system").new({
        chunkSize = 16,
        viewDistance = 2,
        maxLoadedChunks = 25
    })
    Engine.systems.weatherSystem = require("utils.weather_system").init(nil, {
        updateInterval = 60,
        transitionDuration = 10
    })

    -- Add UTF-8 library for text input
    Engine.systems.utf8 = require("utf8")

    -- Initialize world (will be replaced at game start)
    Engine.currentWorld = Engine.systems.worldCore.createWorld({
        name = "Main Menu World",
        seed = os.time(),
        isHubWorld = true,  -- This is important! Mark it as a hub world
        hubOwner = "main_menu",
        hubSeed = os.time(),
        entitySystem = Engine.systems.entityManager,
        chunkSystem = Engine.systems.chunkSystem,
        weatherSystem = Engine.systems.weatherSystem
    })

    -- Make sure the weather system is properly initialized
    if Engine.currentWorld and Engine.systems.weatherSystem then
        Engine.currentWorld.weatherSystem = Engine.systems.weatherSystem
        Engine.systems.weatherSystem.world = Engine.currentWorld
        Engine.systems.weatherSystem.setWeather("clear", true) -- Start with clear weather (instant change)
    end

    -- Make sure we have tiles in the hub biome for visuals
    if Engine.currentWorld and Engine.currentWorld.chunkSystem then
        -- Force creation of hub chunk and surrounding chunks for main menu visualization
        for x = -1, 1 do
            for y = -1, 1 do
                local chunk = Engine.currentWorld.chunkSystem:getChunkAt(x, y)
                if chunk and (x == 0 and y == 0) then
                    -- Mark center chunk as hub
                    chunk.isHub = true

                    -- Set hub biome for the center chunk (only once)
                    local chunkSize = Engine.currentWorld.chunkSystem.chunkSize
                    local chunkX = x * chunkSize
                    local chunkY = y * chunkSize

                    print("Setting hub biome for main menu center chunk")
                    Engine.currentWorld.chunkSystem:setBiomeAt(chunkX, chunkY, "hub")
                else
                    -- For surrounding chunks, set biome to forest or other
                    local distance = math.sqrt(x*x + y*y)
                    local biomeName = "forest"  -- Default to forest for surrounding chunks

                    -- Vary biomes in surrounding chunks for visual interest
                    if distance > 0.8 then
                        if (x + y) % 2 == 0 then
                            biomeName = "plains"
                        else
                            biomeName = "desert"
                        end
                    end

                    -- Set biome for the chunk (only once)
                    local chunkSize = Engine.currentWorld.chunkSystem.chunkSize
                    local chunkX = x * chunkSize
                    local chunkY = y * chunkSize

                    Engine.currentWorld.chunkSystem:setBiomeAt(chunkX, chunkY, biomeName)
                end
            end
        end
        print("Hub biome initialized for main menu")
    end

    -- Load the state handlers system
    Engine.stateManager = require("utils.state_handlers"):init()

    -- Set up LÖVE callbacks
    Engine.setupLoveCallbacks()

    -- Set up menu view in viewport manager
    if Engine.systems.viewportManager then
        Engine.systems.viewportManager:setMenuView({x = 0, y = 0})
    end

    -- Change to initial state (menu)
    Engine.stateManager:changeState("startup")

    Engine.initialized = true
    print("Engine initialization complete")
end

-- Load settings from conf.lua
function Engine.loadSettings()
    local width, height, flags = love.window.getMode()

    Engine.settings = {
        screenWidth = width or 1280,
        screenHeight = height or 720,
        fullscreen = love.window.getFullscreen(),
        soundEnabled = true,
        musicVolume = 0.7,
        sfxVolume = 0.8,
        language = "english",
        enableNFC = true,
        showFPS = true,
        debug = false,
        autoSave = true,
        performanceMode = false  -- Performance mode (default: off)
    }

    local success = Engine.loadSavedSettings()
    if not success then
        print("Using default settings")
    end
end

-- Load saved settings from file
function Engine.loadSavedSettings()
    -- Attempt to load configuration from user_settings.lua or settings.lua
    local filename
    if love.filesystem.getInfo("user_settings.lua") then
        filename = "user_settings.lua"
    elseif love.filesystem.getInfo("settings.lua") then
        filename = "settings.lua"
    else
        return false
    end
    local chunk, loadErr = love.filesystem.load(filename)
    if not chunk then
        print("Ignoring config ("..filename..") – load error: " .. tostring(loadErr))
        return false
    end
    local ok, loadedSettings = pcall(chunk)
    if not ok then
        print("Ignoring config ("..filename..") – parse error: " .. tostring(loadedSettings))
        return false
    end
    if type(loadedSettings) ~= "table" then
        print("Ignoring config ("..filename..") – invalid format")
        return false
    end
    -- Merge loaded settings into defaults
    for k, v in pairs(loadedSettings) do
        Engine.settings[k] = v
    end
    return true
end

-- Save settings to file
function Engine.saveSettings()
    local settingsStr = "return " .. Engine.tableToString(Engine.settings)
    love.filesystem.write("settings.lua", settingsStr)
end

-- Helper function to convert table to string for saving
function Engine.tableToString(tbl, indent)
    indent = indent or ""
    local str = "{\n"
    local indentNext = indent .. "  "

    for k, v in pairs(tbl) do
        str = str .. indentNext

        if type(k) == "string" then
            str = str .. k .. " = "
        else
            str = str .. "[" .. tostring(k) .. "] = "
        end

        if type(v) == "table" then
            str = str .. Engine.tableToString(v, indentNext)
        elseif type(v) == "string" then
            str = str .. "\"" .. v .. "\""
        else
            str = str .. tostring(v)
        end

        str = str .. ",\n"
    end

    str = str .. indent .. "}"
    return str
end

-- Set up LÖVE callbacks
function Engine.setupLoveCallbacks()
    love.update = function(dt)
        Engine.update(dt)
    end

    love.draw = function()
        Engine.draw()
    end

    love.keypressed = function(key, scancode, isrepeat)
        Engine.keyPressed(key, scancode, isrepeat)
    end

    love.keyreleased = function(key, scancode)
        Engine.keyReleased(key, scancode)
    end

    love.mousepressed = function(x, y, button, istouch, presses)
        Engine.mousePressed(x, y, button, istouch, presses)
    end

    love.mousereleased = function(x, y, button, istouch, presses)
        Engine.mouseReleased(x, y, button, istouch, presses)
    end

    love.mousemoved = function(x, y, dx, dy, istouch)
        Engine.mouseMoved(x, y, dx, dy, istouch)
    end

    love.resize = function(w, h)
        Engine.resize(w, h)
    end

    love.quit = function()
        return Engine.quit()
    end

    love.textinput = function(text)
        Engine.textInput(text)
    end

    love.joystickadded = function(joystick)
        if Engine.systems.controller then
            Engine.systems.controller.scanForGamepads()
        end
    end

    love.joystickremoved = function(joystick)
        if Engine.systems.controller then
            Engine.systems.controller.scanForGamepads()
        end
    end
end

-- Check if all players have left the game and revert to menu if needed
function Engine.checkForPlayerExit()
    -- Only check during gameplay state
    if not Engine.stateManager or Engine.stateManager:getCurrentState() ~= "gameplay" then
        return
    end

    -- Count active players
    local activePlayers = 0
    if Engine.currentWorld and Engine.currentWorld.entitySystem then
        for _, entity in pairs(Engine.currentWorld.entitySystem.entities) do
            if entity.type == "player" then
                activePlayers = activePlayers + 1
            end
        end
    end

    -- If no active players and we're in gameplay, revert to menu
    if activePlayers == 0 and not Engine.player then
        print("No active players detected, reverting to menu")
        Engine.stateManager:changeState("menu")
    end
end

-- Core update function - delegates to appropriate systems
function Engine.update(dt)
    if not Engine.initialized then return end

    -- Update state manager
    Engine.stateManager:processStateChange()

    -- Update time
    Engine.time = Engine.time + dt

    -- Update controller
    if Engine.systems.controller then
        Engine.systems.controller.update(dt)
    end

    -- Update current state
    Engine.stateManager:update(dt)

    -- Check if all players have left (only in gameplay state)
    Engine.checkForPlayerExit()

    -- Update utility systems
    if Engine.systems.nfc then
        Engine.systems.nfc.update(dt)
    end

    if Engine.systems.soundSystem then
        Engine.systems.soundSystem.update(dt)
    end

    if Engine.timer then
        Engine.timer:update(dt)
    end

    if Engine.systems.effectSystem then
        Engine.systems.effectSystem:update(dt)
    end

    if Engine.systems.uiSystem then
        Engine.systems.uiSystem:update(dt)
    end
end

-- Core draw function - delegates to appropriate systems
function Engine.draw()
    if not Engine.initialized then
        love.graphics.setColor(0, 0, 0, 1)
        love.graphics.rectangle("fill", 0, 0, 800, 600)
        love.graphics.setColor(1, 1, 1, 1)
        love.graphics.print("Initializing...", 20, 20)
        return
    end

    -- Draw current state
    Engine.stateManager:draw()

    -- Draw FPS counter if enabled
    if Engine.settings.showFPS then
        love.graphics.setColor(1, 1, 1, 0.7)
        love.graphics.print("FPS: " .. love.timer.getFPS(), 10, 10)
    end
    -- In‑game menus are now rendered by the UISystem during state drawing
end

-- NFC card connection handler
function Engine.onNFCConnect(cardId, verifyCallback)
    print("NFC Card connected: " .. cardId)
    if Engine.stateHandlers[Engine.gameState] and Engine.stateHandlers[Engine.gameState].onNFCConnect then
        Engine.stateHandlers[Engine.gameState].onNFCConnect(cardId, verifyCallback)
    else
        verifyCallback(true)
    end
end

-- NFC card disconnection handler
function Engine.onNFCDisconnect(cardId)
    print("NFC Card disconnected: " .. cardId)
    if Engine.stateHandlers[Engine.gameState] and Engine.stateHandlers[Engine.gameState].onNFCDisconnect then
        Engine.stateHandlers[Engine.gameState].onNFCDisconnect(cardId)
    end
end

-- Teleport handler
function Engine.onTeleport(targetHubId)
    print("Teleporting to: " .. targetHubId)
    if Engine.stateHandlers[Engine.gameState] and Engine.stateHandlers[Engine.gameState].onTeleport then
        Engine.stateHandlers[Engine.gameState].onTeleport(targetHubId)
    end
end

-- Start a gameplay session
function Engine.startSession()
    print("Starting gameplay session")
    Engine.inSession = true

    -- Disable menu view in viewport manager
    if Engine.systems.viewportManager then
        Engine.systems.viewportManager:disableMenuView()
    end

    -- Notify systems that a session has started
    if Engine.systems.eventLog then
        Engine.systems.eventLog.logEvent("session_start", {
            time = os.time(),
            player = Engine.player and Engine.player.name or "Unknown"
        })
    end
end

-- End a gameplay session
function Engine.endSession()
    print("Ending gameplay session")
    Engine.inSession = false

    -- Set up menu view in viewport manager
    if Engine.systems.viewportManager then
        Engine.systems.viewportManager:setMenuView({x = 0, y = 0})
    end

    -- Notify systems that a session has ended
    if Engine.systems.eventLog then
        Engine.systems.eventLog.logEvent("session_end", {
            time = os.time(),
            player = Engine.player and Engine.player.name or "Unknown"
        })
    end

    -- Save game state
    if Engine.currentWorld and Engine.currentWorld.chunkSystem then
        Engine.currentWorld.chunkSystem:saveAllChunks()
    end

    -- Save player data if available
    if Engine.player and Engine.systems.database then
        Engine.systems.database.saveCharacter(Engine.player)
    end
end

-- Input event handlers

function Engine.keyPressed(key, scancode, isrepeat)
    -- Toggle debug menu with F1
    if key == "f1" then
        if Engine.systems.uiSystem and Engine.systems.uiSystem.toggleDebugMenu then
            -- Use the UI system's toggle method to show/hide the debug menu
            Engine.systems.uiSystem:toggleDebugMenu()
        end
        return
    end

    -- Direct handler for escape key in character creation
    if key == "escape" and Engine.systems.uiSystem and Engine.systems.uiSystem.characterCreation and Engine.systems.uiSystem.characterCreation.active then
        print("ENGINE: Escape key pressed in character creation - returning to main menu")
        if Engine.systems.uiSystem.handleBackButtonClick then
            Engine.systems.uiSystem:handleBackButtonClick()
        else
            -- Fallback if handleBackButtonClick doesn't exist
            Engine.systems.uiSystem.characterCreation.active = false
            Engine.systems.uiSystem.mainMenuActive = true
        end
        return
    end

    if not Engine.initialized then return end

    -- Debug output for key presses
    if Engine.debug or Engine.debugMode then
        print("Engine: Key pressed: " .. key)
    end

    -- Forward to state manager first
    if Engine.stateManager and Engine.stateManager.keyPressed then
        Engine.stateManager:keyPressed(key, scancode, isrepeat)
    end

    -- Block Enter key during character creation to prevent accidental game entry
    if (key == "return" or key == "kpenter") then
        -- Check if we're in character creation mode
        if Engine.systems.uiSystem and Engine.systems.uiSystem.characterCreation and
           Engine.systems.uiSystem.characterCreation.active then
            print("ENGINE: Blocking Enter key during character creation")
            return -- Block the key press
        end

        -- Also check the state manager
        if Engine.stateManager and Engine.stateManager.currentState == "menu" then
            if Engine.systems.uiSystem and Engine.systems.uiSystem.characterCreation and
               Engine.systems.uiSystem.characterCreation.active then
                print("ENGINE: Blocking Enter key during character creation (via state check)")
                return -- Block the key press
            end
        end
    end

    -- Add emergency exit from character creation with Escape key
    if key == "escape" then
        -- Check if we're in character creation mode
        if Engine.systems.uiSystem and Engine.systems.uiSystem.characterCreation and
           Engine.systems.uiSystem.characterCreation.active then
            print("ENGINE: Emergency exit from character creation with Escape key")
            Engine.systems.uiSystem.characterCreation.active = false
            if Engine.systems.uiSystem.addMessage then
                Engine.systems.uiSystem:addMessage("Character creation canceled", 3)
            end
            return
        end

        -- Also check the state manager
        if Engine.stateManager and Engine.stateManager.currentState == "menu" then
            if Engine.systems.uiSystem and Engine.systems.uiSystem.characterCreation and
               Engine.systems.uiSystem.characterCreation.active then
                print("ENGINE: Emergency exit from character creation with Escape key (via state check)")
                Engine.systems.uiSystem.characterCreation.active = false
                if Engine.systems.uiSystem.addMessage then
                    Engine.systems.uiSystem:addMessage("Character creation canceled", 3)
                end
                return
            end
        end
    end

    -- Try to handle global key presses with the controller first
    if Engine.systems.controller and Engine.systems.controller.handleGlobalKeyPress then
        local handled = Engine.systems.controller.handleGlobalKeyPress(key)
        if handled then
            return -- Key was handled by controller
        end
    end

    -- Global controls
    if key == "f12" then
        -- Toggle FPS counter
        Engine.settings.showFPS = not Engine.settings.showFPS
    end
end

function Engine.keyReleased(key, scancode)
    if not Engine.initialized then return end

    -- Forward to state manager
    Engine.stateManager:keyReleased(key, scancode)
end

function Engine.mousePressed(x, y, button, istouch, presses)
    if not Engine.initialized then return end

    -- Debug output for mouse clicks
    if Engine.debug or Engine.debugMode then
        print("Engine: Mouse clicked at " .. x .. ", " .. y .. ", button: " .. button)
    end

    -- Direct handler for back button in character creation
    if Engine.systems.uiSystem and Engine.systems.uiSystem.characterCreation and Engine.systems.uiSystem.characterCreation.active then
        local form = Engine.systems.uiSystem.components.forms and Engine.systems.uiSystem.components.forms.characterCreation
        if form and form.buttons and form.buttons.back then
            local btn = form.buttons.back
            if x >= btn.x and x <= btn.x + btn.width and
               y >= btn.y and y <= btn.y + btn.height then
                print("ENGINE: Back button clicked in character creation - returning to main menu")
                if Engine.systems.uiSystem.handleBackButtonClick then
                    Engine.systems.uiSystem:handleBackButtonClick()
                else
                    -- Fallback if handleBackButtonClick doesn't exist
                    Engine.systems.uiSystem.characterCreation.active = false
                    Engine.systems.uiSystem.mainMenuActive = true
                end
                return
            end
        end
    end

    -- Handle debug menu clicks first
    if Engine.systems.uiSystem and Engine.systems.uiSystem.debugMenuVisible then
        if Engine.systems.uiSystem.handleDebugMenuClick and Engine.systems.uiSystem:handleDebugMenuClick(x, y) then
            return -- Click was handled by debug menu
        end
    end

    -- Track clicks for double-click detection
    local currentTime = love.timer.getTime()
    local isDoubleClick = false

    -- Check for double click (within 0.5 seconds and 10 pixels)
    if Engine.lastClickTime and currentTime - Engine.lastClickTime < 0.5 and
       Engine.lastClickX and Engine.lastClickY and
       math.abs(Engine.lastClickX - x) < 10 and math.abs(Engine.lastClickY - y) < 10 then
        isDoubleClick = true
        print("Double click detected")

        -- Handle double-click in popup menu
        if Engine.systems.uiSystem and Engine.systems.uiSystem.showingPopup then
            -- Get the currently selected item
            local item = Engine.systems.uiSystem.popupItems[Engine.systems.uiSystem.selectedPopupItem]
            if item then
                print("Double-clicked on popup item: " .. (item.name or "Unknown"))
                Engine.systems.uiSystem:selectPopupItem(item)
            end
        end
    end

    Engine.lastClickX = x
    Engine.lastClickY = y
    Engine.lastClickTime = currentTime

    -- Forward to state manager
    Engine.stateManager:mousePressed(x, y, button, istouch, presses)
end

function Engine.mouseReleased(x, y, button, istouch, presses)
    if not Engine.initialized then return end

    -- Forward to state manager
    Engine.stateManager:mouseReleased(x, y, button, istouch, presses)
end

function Engine.resize(w, h)
    if Engine.systems.viewportManager and Engine.systems.viewportManager.resize then
        Engine.systems.viewportManager.resize(w, h)
    end
end

function Engine.quit()
    print("Game is closing, performing cleanup...")

    -- Save all characters before quitting
    if Engine.systems.databaseManager then
        print("Saving all characters before exit")
        Engine.systems.databaseManager.saveAllCharacters()
    else
        print("Database manager not available, cannot save characters")
    end

    -- Close database connection
    if Engine.systems.databaseManager and Engine.systems.databaseManager.disconnect then
        print("Closing database connection")
        Engine.systems.databaseManager.disconnect()
    end

    print("Cleanup complete, exiting game")
    return false  -- return false to allow LÖVE to exit
end

function Engine.textInput(text)
    print("Engine.textInput called with: " .. text)

    -- IMPORTANT FIX: Check if text is a movement key (w, a, s, d)
    -- If so, don't process it as text input during gameplay
    if Engine.stateManager and Engine.stateManager:getCurrentState() == "gameplay" then
        local movementKeys = {w = true, a = true, s = true, d = true}
        if movementKeys[text:lower()] then
            print("MOVEMENT KEY DETECTED: " .. text .. " - NOT forwarding to text input")
            return -- Skip text input processing for movement keys
        end
    end

    -- Forward to UI system directly for immediate testing
    if Engine.systems.uiSystem then
        print("Forwarding to UI system directly")
        Engine.systems.uiSystem:textInput(text)
        return
    end

    -- Normal state handler path
    if Engine.stateManager and Engine.stateManager.handlers and
       Engine.stateManager.currentState and
       Engine.stateManager.handlers[Engine.stateManager.currentState] and
       Engine.stateManager.handlers[Engine.stateManager.currentState].textInput then
        print("Forwarding to state handler")
        Engine.stateManager.handlers[Engine.stateManager.currentState].textInput(text)
    else
        print("No valid state handler found for text input")
    end
end

-- Handle session start (when player enters gameplay)
function Engine.startSession()
    print("Starting game session")
    Engine.inSession = true

    -- Any session start logic can go here

    print("Session started")
end

-- Handle session end (when player leaves gameplay)
function Engine.endSession()
    print("Ending game session")

    -- Save player data when session ends
    if Engine.player and Engine.systems.databaseManager then
        print("Saving player data at session end: " .. (Engine.player.name or "Unknown"))
        Engine.systems.databaseManager.saveCharacter(Engine.player)
    else
        print("No player data to save at session end")
    end

    Engine.inSession = false
    print("Session ended")
end

-- Mouse movement handler
function Engine.mouseMoved(x, y, dx, dy, istouch)
    if not Engine.initialized then return end

    -- Forward to state manager
    if Engine.stateManager and Engine.stateManager.mouseMoved then
        Engine.stateManager:mouseMoved(x, y, dx, dy, istouch)
    end

    -- Debug output for mouse movements (only when debug tracing is enabled)
    if Engine.debugMode and Engine.debugOptions and Engine.debugOptions.traceMouseMovements then
        print("Engine: Mouse moved: " .. x .. ", " .. y)
    end
end

return Engine
