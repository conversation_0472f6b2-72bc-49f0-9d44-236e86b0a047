-- classes/cleric.lua
-- Cleric class definition for Lazarus Arc

local Cleric = {
    name = "Cleric",
    description = "A devoted healer and divine spellcaster, capable of both restoring life and smiting foes with holy power.",
    icon = "cleric_icon.png",

    -- 🎭 **Playstyle & Trade-offs**
    pros = {
        "Powerful healing abilities",
        "Strong against undead and dark magic users",
        "Can wield both offensive and defensive divine spells"
    },
    cons = {
        "Not as durable as a Paladin",
        "Limited offensive power outside of holy magic",
        "Mana-dependent for both offense and support"
    },

    -- 🏋 **Base Stats & Growth**
    base_stats = { 
        health = 100, 
        maxHealth = 100,
        stamina = 50, 
        maxStamina = 50,
        mana = 80, 
        maxMana = 80,
        pattack = 8, 
        pdefense = 6, 
        mattack = 12, 
        mdefense = 10, 
        speed = 4, 
        luck = 5 
    },

    stat_growth = {
        health = 8,
        stamina = 4,
        mana = 8,
        pattack = 0.6,
        pdefense = 0.7,
        mattack = 1.2,
        mdefense = 1.0,
        speed = 0.3,
        luck = 0.4
    },

    -- ⚔️ **Weapon Proficiency**
    proficiencies = { 
        mace = 1.5,  -- Traditional cleric weapon
        staff = 1.8, -- Used for channeling divine energy
        sword = 1.0, 
        hammer = 1.2, 
        bow = 0.6, 
        gun = 0.5, 
        magic = 2.0 -- Strong divine spellcasting
    },

    -- 🌿 **Elemental Affinities**
    element_affinities = {
        light = 15, -- Divine magic mastery
        earth = 5,  -- Sturdy and resilient
        dark = -10  -- Weakened against dark magic corruption
    },

    -- 💥 **Overdrive Abilities**
    overdrive = { 
        "divine_miracle",  -- Fully heals all allies and removes all debuffs
        "holy_smite",  -- Unleashes a powerful holy blast, stronger vs. undead
        "celestial_guardian" -- Summons a divine shield for the entire party
    },

    -- 🎯 **Starting Skills**
    starting_skills = {
        "heal",
        "holy_light",
        "bless"
    },

    -- 📈 **Skill Progression**
    skill_tree = {
        tier1 = {
            "greater_heal",
            "purification",
            "holy_bolt"
        },
        tier2 = {
            "resurrection",
            "smite", 
            "sanctuary",
            "divine_aura"
        },
        tier3 = {
            "mass_heal",
            "divine_wrath",
            "angelic_intervention"
        }
    },

    -- 🎒 **Inventory & Carrying Capacity**
    inventory_limit = 100, -- Average carrying capacity

    -- ✨ **Special Abilities (Passive Perks)**
    special_abilities = {
        "Sacred Touch", -- Healing abilities have a chance to cleanse negative effects
        "Devout Faith", -- Reduced mana cost for divine spells
        "Martyr's Endurance", -- Takes less damage when at low health
        "Blessing of Light", -- Increases party-wide resistance to dark magic
        "Holy Fury", -- Temporarily boosts attack power after healing an ally
    },

    -- 🎭 **Prestige Paths**
    prestige_paths = {
        high_priest = {
            description = "A master of divine healing and blessings, with near-limitless power to restore and protect.",
            bonuses = {
                "Massively increased healing effectiveness",
                "Can resurrect allies with no penalty",
                "Permanent passive mana regeneration"
            },
            weaknesses = {
                "Very limited offensive abilities",
                "Weaker against enemies resistant to magic"
            }
        },

        battle_cleric = {
            description = "A frontline cleric who wields divine magic alongside melee combat.",
            bonuses = {
                "Stronger melee attacks infused with holy energy",
                "Can self-heal while attacking",
                "Higher physical defense than other clerics"
            },
            weaknesses = {
                "Less potent healing compared to High Priests",
                "More reliant on stamina than pure magic users"
            }
        },

        inquisitor = {
            description = "A divine enforcer who specializes in purging corruption and hunting down dark forces.",
            bonuses = {
                "Bonus damage against dark magic users",
                "Can weaken enemy magic resistances",
                "Faster spell-casting speed"
            },
            weaknesses = {
                "Less focused on healing",
                "Loses access to resurrection spells"
            }
        }
    },

    -- ⚔️ **Hybrid & Multiclass Options**
    hybrid_options = {
        divine_sorcerer = {
            description = "A Cleric who embraces a broader range of magic beyond just divine spells.",
            pros = {
                "Can wield elemental magic alongside divine magic",
                "Greater mana pool"
            },
            cons = {
                "Weaker in physical combat",
                "More susceptible to mana drain effects"
            }
        },

        guardian_saint = {
            description = "A defensive Cleric focused on shielding allies rather than direct healing.",
            pros = {
                "Can create powerful barriers",
                "Takes reduced damage when guarding allies"
            },
            cons = {
                "Healing is weaker compared to standard Clerics",
                "Lower mobility due to heavy focus on defense"
            }
        }
    }
}

return Cleric
