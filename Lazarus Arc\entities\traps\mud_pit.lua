local MudPit = {
    id = "mud_pit",
    name = "Mud Pit",
    type = "trap",
    
    -- Entity categories
    categories = {"trap", "environmental", "swamp", "mud"},
    
    -- Target categories
    targetCategories = {"player", "animal", "monster"},
    
    -- Stats
    maxHealth = 35,
    health = 35,
    duration = 25,
    cooldown = 40,
    
    -- Trap properties
    properties = {
        entrapSpeed = 0.4,
        slowAmount = 0.8,
        damagePerSecond = 2,
        radius = 3.5,
        setupTime = 3,
        isArmed = false,
        isTriggered = false,
        maxDepth = 2.5,
        pullStrength = 0.3
    },
    
    -- Appearance
    appearance = {
        sprite = "mud_pit",
        scale = 1.1,
        animations = {
            "idle", "trigger", "entrap"
        },
        variants = {
            "brown", "dark", "green", "black"
        }
    },
    
    -- Sound effects
    sounds = {
        trigger = "mud_pit_trigger",
        entrap = "mud_pit_entrap",
        release = "mud_pit_release"
    },
    
    -- Effects
    effects = {
        entrap = {
            type = "status",
            duration = 10,
            effects = {
                slow = 0.8,
                damage = 2,
                interval = 1,
                immobilize = true
            }
        }
    }
}

-- Initialize the trap
function MudPit.init(entity, world)
    -- Copy all fields from MudPit template to entity instance
    for k, v in pairs(MudPit) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position
    entity.position = entity.position or {x = 0, y = 0}

    -- Set random variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize trap state
    entity.properties.isArmed = false
    entity.properties.isTriggered = false
    entity.properties.triggerTime = 0
    entity.properties.currentDepth = 0
    entity.properties.entrappedTargets = {}

    return entity
end

-- Update the trap
function MudPit.update(entity, world, dt)
    -- Check if trap is armed and not triggered
    if entity.properties.isArmed and not entity.properties.isTriggered then
        -- Check for targets in radius
        if world and world.entities then
            for _, target in ipairs(world.entities) do
                if target.position and target.categories then
                    -- Check if target is in trap categories
                    local isTarget = false
                    for _, category in ipairs(entity.targetCategories) do
                        if table.contains(target.categories, category) then
                            isTarget = true
                            break
                        end
                    end
                    
                    if isTarget then
                        local distance = math.sqrt(
                            (target.position.x - entity.position.x)^2 + 
                            (target.position.y - entity.position.y)^2
                        )
                        
                        if distance <= entity.properties.radius then
                            -- Trigger trap
                            entity.properties.isTriggered = true
                            entity.properties.triggerTime = world.time
                            
                            -- Add target to entrapped list
                            table.insert(entity.properties.entrappedTargets, target)
                            
                            -- Apply entrap effect to target
                            if target.applyEffect then
                                target.applyEffect(entity.effects.entrap)
                            end
                            
                            -- Play trigger sound
                            if world.playSound then
                                world.playSound(entity.sounds.trigger)
                            end
                            
                            break
                        end
                    end
                end
            end
        end
    end
    
    -- Handle entrapment effect
    if entity.properties.isTriggered then
        entity.properties.currentDepth = math.min(
            entity.properties.currentDepth + entity.properties.entrapSpeed * dt,
            entity.properties.maxDepth
        )
        
        -- Apply pull effect to entrapped targets
        for _, target in ipairs(entity.properties.entrappedTargets) do
            if target.position then
                local dx = entity.position.x - target.position.x
                local dy = entity.position.y - target.position.y
                local distance = math.sqrt(dx * dx + dy * dy)
                
                if distance > 0 then
                    local pullX = (dx / distance) * entity.properties.pullStrength
                    local pullY = (dy / distance) * entity.properties.pullStrength
                    
                    if target.velocity then
                        target.velocity.x = target.velocity.x + pullX
                        target.velocity.y = target.velocity.y + pullY
                    end
                end
                
                -- Apply additional slow effect based on depth
                local depthFactor = entity.properties.currentDepth / entity.properties.maxDepth
                if target.applyEffect then
                    target.applyEffect({
                        type = "status",
                        duration = 1,
                        effects = {
                            slow = 0.8 + (0.2 * depthFactor)
                        }
                    })
                end
            end
        end
    end
    
    -- Check if trap should be destroyed
    if entity.properties.isTriggered and world.time - entity.properties.triggerTime >= entity.duration then
        -- Release entrapped targets
        for _, target in ipairs(entity.properties.entrappedTargets) do
            if target.applyEffect then
                target.applyEffect({
                    type = "status",
                    duration = 1,
                    effects = {
                        slow = 0.5
                    }
                })
            end
        end
        
        if world.removeEntity then
            world.removeEntity(entity)
        end
    end
    
    -- Check if trap can be rearmed
    if not entity.properties.isArmed and world.time - entity.properties.triggerTime >= entity.cooldown then
        entity.properties.isArmed = true
        entity.properties.isTriggered = false
        entity.properties.currentDepth = 0
        entity.properties.entrappedTargets = {}
    end
end

-- Arm the trap
function MudPit.arm(entity, world)
    if not entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = true
        entity.properties.triggerTime = world.time
        entity.properties.currentDepth = 0
        entity.properties.entrappedTargets = {}
        return true
    end
    return false
end

-- Disarm the trap
function MudPit.disarm(entity, world)
    if entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = false
        entity.properties.triggerTime = world.time
        entity.properties.currentDepth = 0
        entity.properties.entrappedTargets = {}
        return true
    end
    return false
end

return MudPit 