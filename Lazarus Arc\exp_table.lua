-- exp_table.lua
-- Experience Table for Levels 1 to 100
-- Designed for slow leveling: players must accumulate a large amount of XP to level up,
-- emphasizing gear and other progression systems over raw stat increases.

local ExpTable = {}

-- Configuration for the XP curve:
local baseXP = 150      -- Increase base XP for higher thresholds
local exponent = 2.8    -- Higher exponent makes leveling slower and more challenging

-- Pre-calculate cumulative XP thresholds for levels 1 to 100.
-- Level 1 starts at 0 XP.
ExpTable.levels = {}
ExpTable.levels[1] = 0
for level = 2, 100 do
    -- The formula used is: XP = floor(baseXP * (level - 1)^exponent)
    -- This results in an exponential growth of XP requirements.
    ExpTable.levels[level] = math.floor(baseXP * math.pow(level - 1, exponent))
end

-- Returns the cumulative XP required to reach a given level (1 to 100).
function ExpTable.getXPForLevel(level)
    if level < 1 or level > 100 then
        error("Level must be between 1 and 100")
    end
    return ExpTable.levels[level]
end

-- Checks if a character's current XP qualifies for a level-up.
-- Returns the new level (if any) based on the cumulative XP.
function ExpTable.checkLevelUp(currentXP, currentLevel)
    local newLevel = currentLevel
    while newLevel < 100 and currentXP >= ExpTable.levels[newLevel + 1] do
        newLevel = newLevel + 1
    end
    return newLevel
end

-- Returns the XP needed to reach the next level from the current XP.
-- If the character is at max level, returns nil and a message.
function ExpTable.getXPToNextLevel(currentXP, currentLevel)
    if currentLevel >= 100 then
        return nil, "Max level reached"
    end
    local nextLevelXP = ExpTable.levels[currentLevel + 1]
    return nextLevelXP - currentXP
end

return ExpTable
