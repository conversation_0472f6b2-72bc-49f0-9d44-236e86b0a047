local NorthernPike = {
    id = "northern_pike",
    name = "Northern Pike",
    type = "northern_pike",

    -- Entity categories
    categories = { "animal", "fish", "medium", "predator" },

    -- Threat categories
    threatCategories = { "predator", "large" },
    foodCategories = { "fish", "prey" },

    -- Stats
    maxHealth = 30,
    health = 30,
    speed = 4.5,
    -- High chase speed
    chaseSpeed = 6.0,

    -- Behaviors
    behaviors = { "hunt", "flee" },

    -- Behavior configurations
    behaviorConfigs = {
        hunt = {
            huntRadius = 12,
            chaseRadius = 20, -- Longer chase
            attackRadius = 2.5, -- Larger attack range
            preferredTargets = { "walleye", "perch", "bluegill", "crappie", "sunfish" }
        },
        flee = {
            useCategories = true,
            moveSpeed = 7.0, -- Fast escape
            detectionRadius = 10
        }
    },

    -- Appearance
    appearance = {
        sprite = "northern_pike", -- Replace with your pike sprite
        scale = 1.5,
        animations = {
            "swim",
            "flee",
            "attack"
        }
    },

    -- Sound effects
    sounds = {
        flop = "fish_flop"
    },

    -- Loot drops
    drops = {
        {
            id = "pike_meat",
            chance = 1.0,
            quantity = { 1, 3 }
        }
    }
}

-- Initialize the northern pike entity
function NorthernPike.init(entity, world)
    entity.position = entity.position or { x = 0, y = 0 }
    entity.velocity = entity.velocity or { x = 0, y = 0 }

    -- Initialize behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.init then
            behavior.init(entity, entity.behaviorConfigs[behaviorName])
        end
    end

    return entity
end

return NorthernPike