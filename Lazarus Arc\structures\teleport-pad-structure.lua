-- structures/teleport_pad.lua
-- Defines the teleport pad structure for hub teleportation

local TeleportPadStructure = {
    name = "Teleport Pad",
    type = "teleport_structure",
    width = 7,  -- Total width in tiles
    height = 7, -- Total height in tiles
    interactable = true,
    
    -- Visual definition
    appearance = {
        baseColor = {0.3, 0.5, 0.9, 1.0}, -- Blue glow
        activeColor = {0.4, 0.7, 1.0, 1.0}, -- Brighter blue when active
        pulseRate = 1.5, -- Seconds per pulse cycle
        particleEffects = true,
        variant = "circular" -- Could also be "square", "hexagonal"
    },
    
    -- Tile layout - defines which tiles make up the structure
    -- 0 = no change to existing tile
    -- 1 = teleport pad base
    -- 2 = teleport pad center
    -- 3 = teleport pad edge
    -- 4 = teleport pad corner
    layout = {
        {0, 0, 3, 3, 3, 0, 0},
        {0, 3, 1, 1, 1, 3, 0},
        {3, 1, 1, 1, 1, 1, 3},
        {3, 1, 1, 2, 1, 1, 3},
        {3, 1, 1, 1, 1, 1, 3},
        {0, 3, 1, 1, 1, 3, 0},
        {0, 0, 3, 3, 3, 0, 0}
    },
    
    -- Collision properties
    collision = {
        walkable = true, -- Players can walk on teleport pads
        blocksProjectiles = false,
        blocksVision = false
    },
    
    -- Teleport properties
    teleport = {
        activationTime = 0.5, -- Time player must stand on pad before teleport activates
        cooldownTime = 3.0, -- Time before pad can be used again after teleport
        activationDistance = 1.5, -- How close to center player must be to activate
        effectRadius = 3, -- Radius of teleport effect
        teleportSound = "teleport_activate", -- Sound to play when teleporting
        idleSound = "teleport_hum" -- Ambient sound when pad is idle
    },
    
    -- Light emitted by the structure
    lighting = {
        emitsLight = true,
        lightColor = {0.3, 0.6, 0.9, 0.7},
        lightRadius = 8,
        pulsingLight = true
    },
    
    -- Particle effects
    particles = {
        idle = {
            type = "sparkle",
            count = 5,
            color = {0.5, 0.7, 1.0, 0.7},
            size = {0.5, 1.5},
            lifetime = {0.5, 2.0},
            speed = {0.5, 1.0}
        },
        active = {
            type = "vortex",
            count = 20,
            color = {0.3, 0.5, 1.0, 0.8},
            size = {1.0, 2.0},
            lifetime = {0.3, 1.0},
            speed = {1.5, 3.0}
        },
        teleport = {
            type = "explosion",
            count = 50,
            color = {0.8, 0.9, 1.0, 0.9},
            size = {1.0, 3.0},
            lifetime = {0.2, 1.0},
            speed = {3.0, 8.0}
        }
    }
}

-- Functions to manipulate the structure

-- Create a new teleport pad instance
function TeleportPadStructure.create(world, x, y, targetHubId, name)
    -- Create structure data
    local pad = {
        id = "teleport_pad_" .. x .. "_" .. y,
        position = {x = x, y = y},
        targetHubId = targetHubId,
        name = name or ("Hub " .. targetHubId),
        active = true,
        state = "idle", -- idle, activating, active, cooldown
        stateTime = 0,
        activatingPlayerId = nil,
        tiles = {},
        pulsePhase = 0,
        variant = math.random(1, 3) -- Random visual variant
    }
    
    -- Place the structure in the world
    TeleportPadStructure.place(world, pad)
    
    return pad
end

-- Place the structure tiles in the world
function TeleportPadStructure.place(world, pad)
    if not world or not world.chunkSystem then return false end
    
    local centerX = math.floor(pad.position.x)
    local centerY = math.floor(pad.position.y)
    
    -- Calculate offset to align structure correctly
    local offsetX = math.floor(TeleportPadStructure.width / 2)
    local offsetY = math.floor(TeleportPadStructure.height / 2)
    
    -- Place each tile according to the layout
    for y = 1, TeleportPadStructure.height do
        for x = 1, TeleportPadStructure.width do
            local tileType = TeleportPadStructure.layout[y][x]
            
            -- Skip if tile type is 0 (no change)
            if tileType > 0 then
                local worldX = centerX + (x - offsetX - 1)
                local worldY = centerY + (y - offsetY - 1)
                
                -- Get existing tile
                local tile = world.chunkSystem:getTileAt(worldX, worldY)
                
                if tile then
                    -- Save original tile type
                    if not tile.originalType then
                        tile.originalType = tile.type
                    end
                    
                    -- Set new tile type based on layout code
                    if tileType == 1 then
                        tile.type = "teleport_pad_base"
                    elseif tileType == 2 then
                        tile.type = "teleport_pad_center"
                    elseif tileType == 3 then
                        tile.type = "teleport_pad_edge"
                    elseif tileType == 4 then
                        tile.type = "teleport_pad_corner"
                    end
                    
                    -- Store reference to this tile
                    table.insert(pad.tiles, {x = worldX, y = worldY, tileId = worldX .. "," .. worldY})
                end
            end
        end
    end
    
    -- Create light source
    if TeleportPadStructure.lighting.emitsLight and world.lightingSystem then
        pad.lightId = world.lightingSystem:addLight(
            centerX,
            centerY,
            TeleportPadStructure.lighting.lightRadius,
            TeleportPadStructure.lighting.lightColor
        )
    end
    
    -- Initialize particle system
    if TeleportPadStructure.particles and world.particleSystem then
        pad.particleId = world.particleSystem:createEmitter(
            centerX,
            centerY,
            TeleportPadStructure.particles.idle
        )
    end
    
    return true
end

-- Update teleport pad state
function TeleportPadStructure.update(world, pad, dt)
    if not pad or not pad.active then return end
    
    -- Update pulse animation
    pad.pulsePhase = (pad.pulsePhase + dt / TeleportPadStructure.appearance.pulseRate) % 1
    local pulseIntensity = 0.5 + 0.5 * math.sin(pad.pulsePhase * math.pi * 2)
    
    -- Update lighting
    if pad.lightId and world.lightingSystem and TeleportPadStructure.lighting.pulsingLight then
        local baseIntensity = TeleportPadStructure.lighting.lightColor[4]
        local currentIntensity = baseIntensity * (0.7 + 0.3 * pulseIntensity)
        
        world.lightingSystem:setLightIntensity(pad.lightId, currentIntensity)
    end
    
    -- Update state
    pad.stateTime = pad.stateTime + dt
    
    if pad.state == "activating" then
        -- Check if activation is complete
        if pad.stateTime >= TeleportPadStructure.teleport.activationTime then
            pad.state = "active"
            pad.stateTime = 0
            
            -- Change particle effect
            if pad.particleId and world.particleSystem then
                world.particleSystem:changeEmitter(pad.particleId, TeleportPadStructure.particles.teleport)
            end
            
            -- Play teleport sound
            if world.soundSystem then
                world.soundSystem:playSound(
                    TeleportPadStructure.teleport.teleportSound,
                    pad.position.x,
                    pad.position.y
                )
            end
            
            -- Trigger teleport
            if world.teleportSystem then
                world.teleportSystem.initiateTeleport(pad.activatingPlayerId, pad.targetHubId)
            end
        else
            -- Update activation particles
            if pad.particleId and world.particleSystem then
                world.particleSystem:changeEmitter(pad.particleId, TeleportPadStructure.particles.active)
            end
        end
    elseif pad.state == "active" then
        -- Active for a moment before cooldown
        if pad.stateTime >= 1.0 then
            pad.state = "cooldown"
            pad.stateTime = 0
            pad.activatingPlayerId = nil
            
            -- Reset particles
            if pad.particleId and world.particleSystem then
                world.particleSystem:changeEmitter(pad.particleId, TeleportPadStructure.particles.idle)
            end
        end
    elseif pad.state == "cooldown" then
        -- Cooling down before allowing new teleports
        if pad.stateTime >= TeleportPadStructure.teleport.cooldownTime then
            pad.state = "idle"
            pad.stateTime = 0
        end
    end
end

-- Check if a player is close enough to activate the pad
function TeleportPadStructure.checkPlayerActivation(world, pad, player)
    if not pad.active or pad.state ~= "idle" then return false end
    
    local dx = player.position.x - pad.position.x
    local dy = player.position.y - pad.position.y
    local distance = math.sqrt(dx*dx + dy*dy)
    
    -- Check if player is within activation distance
    if distance <= TeleportPadStructure.teleport.activationDistance then
        -- Start activation
        pad.state = "activating"
        pad.stateTime = 0
        pad.activatingPlayerId = player.id
        
        -- Show message to player
        if world.uiSystem and world.uiSystem.showMessage then
            world.uiSystem.showMessage("Teleporting to " .. pad.name .. "...", TeleportPadStructure.teleport.activationTime)
        end
        
        -- Play activation sound
        if world.soundSystem then
            world.soundSystem:playSound(
                "teleport_activate_start",
                pad.position.x,
                pad.position.y
            )
        end
        
        return true
    end
    
    return false
end

-- Remove the teleport pad from the world
function TeleportPadStructure.remove(world, pad)
    if not pad or not world or not world.chunkSystem then return false end
    
    -- Restore original tiles
    for _, tileInfo in ipairs(pad.tiles) do
        local tile = world.chunkSystem:getTileAt(tileInfo.x, tileInfo.y)
        
        if tile and tile.originalType then
            -- Restore original tile type
            tile.type = tile.originalType
            tile.originalType = nil
        end
    end
    
    -- Remove light source
    if pad.lightId and world.lightingSystem then
        world.lightingSystem:removeLight(pad.lightId)
    end
    
    -- Remove particle emitter
    if pad.particleId and world.particleSystem then
        world.particleSystem:removeEmitter(pad.particleId)
    end
    
    return true
end

-- Register with structures system
function TeleportPadStructure.register(structures)
    structures.teleport_pad = TeleportPadStructure
    print("Registered Teleport Pad structure")
end

return TeleportPadStructure
