local Whale = {
    id = "whale",
    name = "Blue Whale",
    type = "fish",
    
    -- Entity categories
    categories = {"animal", "fish", "saltwater", "filter_feeder", "mammal", "migratory"},
    
    -- Threat categories
    threatCategories = {"player"},
    foodCategories = {"plankton", "krill", "small_fish"},
    
    -- Stats
    maxHealth = 150,
    health = 150,
    maxStamina = 120,
    stamina = 120,
    speed = 1.5,
    
    -- Water properties
    water = {
        swimSpeed = 1.5,
        maxDepth = 25,
        preferredDepth = 10,
        oxygenLevel = 1.0,
        temperature = "cold",
        currentSpeed = 1.0
    },
    
    -- Behaviors
    behaviors = {"filter_feed", "patrol", "migrate", "social"},
    
    -- Behavior configurations
    behaviorConfigs = {
        filter_feed = {
            moveSpeed = 1.0,
            searchRadius = 15,
            preferredFood = {"plankton", "krill", "small_fish"},
            successChance = 0.8,
            filterRate = 2.0
        },
        patrol = {
            moveSpeed = 1.2,
            patrolRadius = 40,
            preferredDepth = 10,
            restInterval = {30, 40}
        },
        migrate = {
            moveSpeed = 1.8,
            searchRadius = 50,
            preferredDepth = 15,
            season = "winter",
            successChance = 0.9
        },
        social = {
            moveSpeed = 1.3,
            followDistance = 5,
            separationDistance = 4,
            alignmentStrength = 0.5,
            cohesionStrength = 0.6,
            maxGroupSize = 4
        }
    },
    
    -- Special abilities
    abilities = {
        breach = {
            speed = 2.5,
            duration = 1.0,
            cooldown = 8,
            staminaCost = 20
        },
        blow = {
            range = 8,
            duration = 0.5,
            cooldown = 3,
            effect = "stun"
        },
        call = {
            range = 40,
            duration = 2,
            cooldown = 5,
            effect = "attract",
            staminaCost = 10
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "whale",
        scale = 2.0,
        animations = {
            "idle", "swim", "breach", "filter_feed", "migrate"
        },
        variants = {
            "blue", "gray", "spotted", "dark"
        }
    },
    
    -- Sound effects
    sounds = {
        splash = "whale_splash",
        swim = "whale_swim",
        call = "whale_call",
        blow = "whale_blow"
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.9, quantity = {3, 4}},
        {id = "blubber", chance = 0.8, quantity = {2, 3}},
        {id = "bone", chance = 0.7, quantity = {2, 3}},
        {id = "baleen", chance = 0.6, quantity = {1, 2}}
    }
}

-- Initialize the entity
function Whale.init(entity, world)
    -- Copy all fields from Whale template to entity instance
    for k, v in pairs(Whale) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random whale variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Whale.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update water properties
    if entity.water then
        -- Adjust filter feeding success based on time of day
        if world and world.timeOfDay == "dawn" or world.timeOfDay == "dusk" then
            entity.behaviorConfigs.filter_feed.successChance = 0.9
        else
            entity.behaviorConfigs.filter_feed.successChance = 0.8
        end

        -- Adjust preferred depth based on time of day
        if world and world.timeOfDay == "night" then
            entity.water.preferredDepth = 5
        else
            entity.water.preferredDepth = 10
        end

        -- Check migration conditions
        if world and world.season == "winter" then
            entity.behaviorConfigs.migrate.successChance = 0.9
        else
            entity.behaviorConfigs.migrate.successChance = 0.0
        end

        -- Check for nearby whales for social behavior
        if world and world.entities then
            local nearbyWhales = 0
            for _, other in ipairs(world.entities) do
                if other.type == "fish" and other.id == "whale" then
                    local distance = math.sqrt(
                        (other.position.x - entity.position.x)^2 + 
                        (other.position.y - entity.position.y)^2
                    )
                    if distance < 10 then
                        nearbyWhales = nearbyWhales + 1
                    end
                end
            end
            if nearbyWhales > 0 then
                entity.behaviorConfigs.filter_feed.successChance = 
                    entity.behaviorConfigs.filter_feed.successChance + 0.1
            end
        end
    end
end

return Whale 