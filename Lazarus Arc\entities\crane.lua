local Crane = {
    id = "crane",
    name = "Crane",
    type = "bird",
    
    -- Entity categories
    categories = {"animal", "bird", "flying", "water", "wading", "social"},
    
    -- Threat and food categories
    threatCategories = {"player", "predator"},
    foodCategories = {"insect", "fish", "plant", "small_prey"},
    
    -- Stats
    maxHealth = 40,
    health = 40,
    maxStamina = 75,
    stamina = 75,
    speed = 1.8,
    
    -- Flight properties
    flight = {
        maxHeight = 14,
        minHeight = 1,
        ascentSpeed = 0.8,
        descentSpeed = 1.5,
        hoverHeight = 3,
        currentHeight = 3,
        wingFlapRate = 0.1,
        soarChance = 0.4
    },
    
    -- Water properties
    water = {
        wadeDepth = 2,
        swimSpeed = 1.2,
        foragingRange = 4
    },
    
    -- Behaviors
    behaviors = {"forage", "dance", "migrate", "roost"},
    
    -- Behavior configurations
    behaviorConfigs = {
        forage = {
            moveSpeed = 1.2,
            searchRadius = 12,
            preferredFood = {"insect", "fish", "plant"},
            successChance = 0.7
        },
        dance = {
            moveSpeed = 1.5,
            duration = 5,
            staminaCost = 10,
            attractionRadius = 8,
            preferredTime = "dawn"
        },
        migrate = {
            moveSpeed = 2.5,
            formationSpacing = 3,
            restInterval = {20, 30},
            preferredSeason = "autumn"
        },
        roost = {
            startTime = "dusk",
            endTime = "dawn",
            roostHeight = {2, 4},
            healthRegen = 0.03,
            staminaRegen = 0.1
        }
    },
    
    -- Special abilities
    abilities = {
        leap = {
            height = 3,
            duration = 0.5,
            cooldown = 2,
            staminaCost = 5
        },
        call = {
            range = 15,
            duration = 1,
            cooldown = 3,
            effect = "attract"
        },
        display = {
            duration = 2,
            cooldown = 4,
            staminaCost = 8,
            effect = "attract"
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "crane",
        scale = 1.1,
        animations = {
            "idle", "fly", "wade", "dance", "leap", "roost"
        },
        variants = {
            "sandhill", "whooping", "gray", "white"
        }
    },
    
    -- Sound effects
    sounds = {
        call = "crane_call",
        wingFlap = "crane_wing_flap",
        dance = "crane_dance"
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.6, quantity = {1, 2}},
        {id = "feather", chance = 0.7, quantity = {2, 3}},
        {id = "beak", chance = 0.3, quantity = {1, 1}},
        {id = "bone", chance = 0.4, quantity = {1, 1}},
        {id = "talon", chance = 0.3, quantity = {1, 2}}
    }
}

-- Initialize the entity
function Crane.init(entity, world)
    -- Copy all fields from Crane template to entity instance
    for k, v in pairs(Crane) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random crane variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Crane.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update flight properties
    if entity.flight then
        -- Update wing flapping based on height and activity
        if entity.flight.currentHeight > entity.flight.minHeight then
            if entity.flight.soarChance > math.random() then
                entity.flight.wingFlapRate = 0.05
            else
                entity.flight.wingFlapRate = 0.1
            end
        else
            entity.flight.wingFlapRate = 0.2
        end
    end

    -- Update water properties
    if entity.water then
        -- Adjust foraging success based on time of day
        if world and world.timeOfDay == "dawn" or world.timeOfDay == "dusk" then
            entity.behaviorConfigs.forage.successChance = 0.8
        else
            entity.behaviorConfigs.forage.successChance = 0.7
        end
    end
end

return Crane 