-- weather/rain.lua
-- Rain weather pattern - moderate rain with optional thunder

local WeatherRain = {}
WeatherRain.__index = WeatherRain

-- Static module-level properties (for when init is called directly on the module)
WeatherRain.id = "rain"
WeatherRain.name = "Rain"

-- Visual properties
WeatherRain.visual = {
    skyColor = {r = 100, g = 100, b = 120}, -- Dark gray sky
    sunIntensity = 0.2,
    cloudCoverage = 0.8,
    cloudColor = {r = 120, g = 120, b = 140}, -- Dark gray clouds
    ambientLightLevel = 0.3,
    rainColor = {r = 150, g = 150, b = 170}
}

-- Environment modifiers
WeatherRain.environment = {
    temperature = 0.8, -- Slightly cooler
    visibility = 0.6, -- Reduced visibility
    humidity = 0.9, -- High humidity
    windStrength = 0.4, -- Moderate wind
    floodRisk = 0.3 -- Moderate flood risk
}

-- Particle systems
WeatherRain.particles = {
    rain = {
        enabled = true,
        intensity = 0.7,
        color = {r = 150, g = 150, b = 170},
        size = {min = 1, max = 2},
        speed = {min = 100, max = 200},
        image = "assets/particles/Complex/lines/lines_1.png",
        blendMode = "add",
        lifetime = {min = 1.0, max = 2.0},
        rotation = {min = 0, max = 360},
        alpha = {min = 0.3, max = 0.6}
    },
    splash = {
        enabled = true,
        intensity = 0.4,
        color = {r = 180, g = 180, b = 200},
        size = {min = 0.5, max = 1.5},
        speed = {min = 50, max = 100},
        image = "assets/particles/Complex/lines/lines_2.png",
        blendMode = "add",
        lifetime = {min = 0.2, max = 0.5},
        rotation = {min = 0, max = 360},
        alpha = {min = 0.2, max = 0.4}
    }
}

-- Sound effects
WeatherRain.sounds = {
    ambient = "rain_ambient",
    heavyRain = "heavy_rain",
    thunder = "thunder",
    volume = 0.5
}

-- Synth orchestra sound configurations
WeatherRain.synthSounds = {
    rain_ambient = {
        notes = {220, 165, 110}, -- A3, E3, A2 - gentle rain harmony
        durations = {2.0, 2.0, 2.0},
        instrument = "soft_pad",
        volume = 0.3,
        vibrato = true,
        vibratoRate = 0.5,
        vibratoDepth = 0.1
    },
    heavy_rain = {
        notes = {220, 165, 110, 82.5}, -- A3, E3, A2, E2 - deeper, more intense
        durations = {1.5, 1.5, 1.5, 1.5},
        instrument = "soft_pad",
        volume = 0.5,
        vibrato = true,
        vibratoRate = 0.8,
        vibratoDepth = 0.15
    },
    thunder = {
        notes = {55, 73.5, 98}, -- A1, D2, G2 - low rumbling thunder
        durations = {0.3, 0.5, 0.8},
        instrument = "bass_guitar",
        volume = 0.8,
        vibrato = false
    },
    rain_drops = {
        notes = {523, 659, 784, 1047}, -- C5, E5, G5, C6 - individual droplets
        durations = {0.1, 0.1, 0.1, 0.1},
        instrument = "kalimba",
        volume = 0.2,
        vibrato = false
    },
    rain_on_leaves = {
        notes = {392, 494, 587, 698}, -- G4, B4, D5, F5 - rain hitting foliage
        durations = {0.2, 0.2, 0.2, 0.2},
        instrument = "marimba",
        volume = 0.25,
        vibrato = false
    }
}

-- Thunder settings
WeatherRain.thunder = {
    enabled = false,
    minDelay = 5,
    maxDelay = 15,
    nextStrike = 0
}

-- Intensity variation
WeatherRain.intensityVariation = {
    current = 0.7,
    target = 0.7,
    transitionSpeed = 1.0
}

-- Effects on game entities and tiles
WeatherRain.effects = {
    -- Movement effects
    movementSpeedMultiplier = 0.8, -- Reduced speed

    -- Vision effects
    visionRange = 0.6, -- Reduced vision

    -- Temperature effects
    temperatureModifier = 0.9, -- Slightly cooler

    -- Energy effects
    energyDrainRate = 1.1, -- Slightly increased energy drain

    -- Shield effects
    shieldEffectiveness = 0.95, -- Slightly reduced shield effectiveness

    -- Sensor effects
    sensorRange = 0.8, -- Reduced sensor range
}

-- Transition probabilities to other weather (per game hour)
WeatherRain.transitions = {
    clear = 0.2,
    cloudy = 0.5,
    heavy_rain = 0.2,
    -- Implied: 0.1 chance to stay rain
}

-- Day/night cycle modifiers
WeatherRain.timeModifiers = {
    dawn = {
        skyColor = {r = 120, g = 120, b = 140},
        ambientLightLevel = 0.4
    },
    day = {
        -- Uses default properties
    },
    dusk = {
        skyColor = {r = 90, g = 90, b = 110},
        ambientLightLevel = 0.3
    },
    night = {
        skyColor = {r = 70, g = 70, b = 90},
        ambientLightLevel = 0.2,
        starVisibility = 0.0
    }
}

-- Default properties for new instances (copy of module-level properties)
WeatherRain.defaults = {
    id = WeatherRain.id,
    name = WeatherRain.name,
    visual = WeatherRain.visual,
    environment = WeatherRain.environment,
    particles = WeatherRain.particles,
    sounds = WeatherRain.sounds,
    thunder = WeatherRain.thunder,
    intensityVariation = WeatherRain.intensityVariation,
    effects = WeatherRain.effects,
    transitions = WeatherRain.transitions,
    timeModifiers = WeatherRain.timeModifiers
}

-- Helper function to safely set particle system properties
local function setupParticleSystem(ps, config)
    if not ps or type(ps) ~= "table" then
        print("Warning: Invalid particle system")
        return false
    end

    local methods = {
        setParticleLifetime = function() return ps:setParticleLifetime(config.lifetime.min, config.lifetime.max) end,
        setEmissionRate = function() return ps:setEmissionRate(config.emissionRate or 0) end,
        setSizeVariation = function() return ps:setSizeVariation(config.sizeVariation or 0.3) end,
        setLinearAcceleration = function() return ps:setLinearAcceleration(config.acceleration.x1 or 0, config.acceleration.y1 or 0, config.acceleration.x2 or 0, config.acceleration.y2 or 0) end,
        setColors = function() return ps:setColors(config.colors.r1 or 255, config.colors.g1 or 255, config.colors.b1 or 255, config.colors.a1 or 255, config.colors.r2 or 255, config.colors.g2 or 255, config.colors.b2 or 255, config.colors.a2 or 0) end,
        setBlendMode = function() return ps:setBlendMode(config.blendMode or "normal") end
    }

    for methodName, method in pairs(methods) do
        if type(ps[methodName]) ~= "function" then
            print("Warning: Particle system missing method: " .. methodName)
            return false
        end

        local success, err = pcall(method)
        if not success then
            print("Warning: Failed to call " .. methodName .. ": " .. tostring(err))
            return false
        end
    end

    return true
end

-- Helper function to deep copy a table
local function deepCopy(orig)
    local copy
    if type(orig) == 'table' then
        copy = {}
        for orig_key, orig_value in next, orig, nil do
            copy[deepCopy(orig_key)] = deepCopy(orig_value)
        end
        setmetatable(copy, deepCopy(getmetatable(orig)))
    else
        copy = orig
    end
    return copy
end

-- Create a new instance of the weather pattern
function WeatherRain.new()
    local instance = setmetatable({}, WeatherRain)
    -- Copy all default properties
    for k, v in pairs(WeatherRain.defaults) do
        instance[k] = deepCopy(v)
    end
    return instance
end

-- Required functions for weather patterns
function WeatherRain:init(worldCore)
    print("Initializing WeatherRain")

    -- Ensure particles property exists (handle both module and instance initialization)
    if not self.particles then
        print("WeatherRain: Creating particles table")
        self.particles = deepCopy(WeatherRain.particles)
    end

    -- Initialize particle systems
    if self.particles and self.particles.rain and self.particles.rain.enabled then
        -- Create rain particles
        print("Creating rain particles with intensity: " .. self.particles.rain.intensity)

        -- Create rain particle image programmatically
        local rainParticleCanvas = love.graphics.newCanvas(2, 8)
        love.graphics.setCanvas(rainParticleCanvas)
        love.graphics.clear(0, 0, 0, 0)
        love.graphics.setColor(0.6, 0.7, 0.9, 0.7) -- Slightly blue for rain
        love.graphics.rectangle("fill", 0, 0, 2, 8)
        love.graphics.setCanvas()

        -- Create particle system with the canvas
        local success, ps = pcall(love.graphics.newParticleSystem, rainParticleCanvas, 1000)
        if success and ps then
            local config = {
                lifetime = self.particles.rain.lifetime,
                emissionRate = 100,
                sizeVariation = 0.3,
                acceleration = {x1 = 0, y1 = 200, x2 = 0, y2 = 200},
                colors = {r1 = 255, g1 = 255, b1 = 255, a1 = 255, r2 = 150, g2 = 150, b2 = 170, a2 = 0},
                blendMode = "add"
            }

            if setupParticleSystem(ps, config) then
                self.raindropSystem = ps
            else
                print("Warning: Failed to setup rain particle system")
            end
        else
            print("Warning: Failed to create rain particle system")
        end
    end

    if self.particles and self.particles.splash and self.particles.splash.enabled then
        -- Create splash particles
        print("Creating splash particles with intensity: " .. self.particles.splash.intensity)

        -- Create splash particle image programmatically
        local splashParticleCanvas = love.graphics.newCanvas(4, 4)
        love.graphics.setCanvas(splashParticleCanvas)
        love.graphics.clear(0, 0, 0, 0)
        love.graphics.setColor(0.7, 0.8, 0.9, 0.6) -- Slightly blue for water splash
        love.graphics.circle("fill", 2, 2, 2)
        love.graphics.setCanvas()

        -- Create particle system with the canvas
        local success, ps = pcall(love.graphics.newParticleSystem, splashParticleCanvas, 500)
        if success and ps then
            local config = {
                lifetime = self.particles.splash.lifetime,
                emissionRate = 50,
                sizeVariation = 0.2,
                acceleration = {x1 = -50, y1 = -50, x2 = 50, y2 = 50},
                colors = {r1 = 255, g1 = 255, b1 = 255, a1 = 255, r2 = 180, g2 = 180, b2 = 200, a2 = 0},
                blendMode = "add"
            }

            if setupParticleSystem(ps, config) then
                self.splashSystem = ps
            else
                print("Warning: Failed to setup splash particle system")
            end
        else
            print("Warning: Failed to create splash particle system")
        end
    end

    return self
end

function WeatherRain:update(dt, weatherSystem)
    -- Update particle systems
    if self.raindropSystem then
        self.raindropSystem:update(dt)
    end
    if self.splashSystem then
        self.splashSystem:update(dt)
    end

    -- Update rain intensity
    if self.intensityVariation then
        local intensityVar = self.intensityVariation
        if intensityVar.current ~= intensityVar.target then
            local diff = intensityVar.target - intensityVar.current
            local change = diff * intensityVar.transitionSpeed * dt
            intensityVar.current = intensityVar.current + change
        end
    end

    -- Update thunder if enabled
    if self.thunder and self.thunder.enabled then
        self.thunder.nextStrike = self.thunder.nextStrike - dt
        if self.thunder.nextStrike <= 0 then
            if weatherSystem and weatherSystem.playSound then
                weatherSystem:playSound(self.sounds.thunder, {
                    verbose = false,  -- Don't print warnings if sound is missing
                    volume = 0.7 + math.random() * 0.3
                })
            end
            self.thunder.nextStrike = math.random(self.thunder.minDelay, self.thunder.maxDelay)
        end
    end

    -- Apply weather effects
    if weatherSystem and weatherSystem.applyEffects and self.environment then
        weatherSystem:applyEffects(self.environment)
    end
end

function WeatherRain:onStart(weatherSystem)
    -- Start particle systems
    if self.raindropSystem then
        self.raindropSystem:start()
    end
    if self.splashSystem then
        self.splashSystem:start()
    end

    -- Play ambient sounds based on intensity
    if weatherSystem and weatherSystem.playSound and self.sounds then
        if self.intensityVariation and self.intensityVariation.current > 0.7 then
            weatherSystem:playSound(self.sounds.heavyRain, {
                verbose = false,  -- Don't print warnings if sound is missing
                loop = true,      -- Ambient sounds should loop
                volume = 0.6      -- Moderate volume for rain
            })
        else
            weatherSystem:playSound(self.sounds.ambient, {
                verbose = false,  -- Don't print warnings if sound is missing
                loop = true,      -- Ambient sounds should loop
                volume = 0.5      -- Lower volume for ambient weather
            })
        end
    end

    -- Initialize thunder if enabled
    if math.random() < 0.3 and self.thunder then
        self.thunder.enabled = true
        self.thunder.frequency = math.random() * 0.4 + 0.1
        self.thunder.nextStrike = math.random(self.thunder.minDelay, self.thunder.maxDelay)
    end
end

function WeatherRain:onEnd(weatherSystem)
    -- Stop particle systems
    if self.raindropSystem then
        self.raindropSystem:stop()
    end
    if self.splashSystem then
        self.splashSystem:stop()
    end

    -- Stop all sounds
    if weatherSystem and weatherSystem.stopSound and self.sounds then
        weatherSystem:stopSound(self.sounds.ambient)
        weatherSystem:stopSound(self.sounds.heavyRain)
        weatherSystem:stopSound(self.sounds.thunder)
    end
end

function WeatherRain:draw()
    -- Draw particle systems
    if self.raindropSystem then
        love.graphics.draw(self.raindropSystem, 0, 0)
    end
    if self.splashSystem then
        love.graphics.draw(self.splashSystem, 0, 0)
    end
end

function WeatherRain:cleanUp(world)
    -- Handle the case where cleanUp is called directly on the module
    if not world and type(self) ~= "table" then
        -- world parameter was passed as self
        world = self
        self = WeatherRain
    end

    print("Cleaning up rain weather")

    -- Stop particle systems
    if self.raindropSystem then
        self.raindropSystem:stop()
        self.raindropSystem = nil
    end
    if self.splashSystem then
        self.splashSystem:stop()
        self.splashSystem = nil
    end

    -- Stop all sounds if weather system is available
    if world and world.weatherSystem and world.weatherSystem.stopSound and self.sounds then
        world.weatherSystem:stopSound(self.sounds.ambient)
        world.weatherSystem:stopSound(self.sounds.heavyRain)
        world.weatherSystem:stopSound(self.sounds.thunder)
    end

    -- Reset thunder state
    if self.thunder then
        self.thunder.enabled = false
        self.thunder.nextStrike = 0
    end

    return self
end

-- Static function versions for weather system compatibility
function WeatherRain.init(world)
    return WeatherRain:init(world)
end

function WeatherRain.update(world, dt)
    return WeatherRain:update(dt, world.weatherSystem)
end

function WeatherRain.cleanUp(world)
    return WeatherRain:cleanUp(world)
end

return WeatherRain