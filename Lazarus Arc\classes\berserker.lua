-- classes/berserker.lua
-- Berserker class definition for Lazarus Arc

local Berserker = {
    name = "Berserk<PERSON>",
    description = "A frenzied warrior sacrificing defense for overwhelming offensive power.",
    icon = "berserker_icon.png",

    -- 🎭 **Playstyle & Trade-offs**
    pros = {
        "Massive melee damage output",
        "Becomes stronger the more damage they take",
        "Highly resistant to stun and fear effects"
    },
    cons = {
        "Virtually no defense",
        "Attacks consume high stamina",
        "Reckless—can damage allies if not careful"
    },

    -- 🏋 **Base Stats & Growth**
    base_stats = { 
        health = 130, 
        maxHealth = 130,
        stamina = 90, 
        maxStamina = 90,
        mana = 10, 
        maxMana = 10,
        pattack = 20, 
        pdefense = 2, 
        mattack = 2, 
        mdefense = 1, 
        speed = 6, 
        luck = 4 
    },

    stat_growth = {
        health = 12,
        stamina = 9,
        mana = 0.5,
        pattack = 2.0,
        pdefense = 0.1,
        mattack = 0.1,
        mdefense = 0.1,
        speed = 0.5,
        luck = 0.3
    },

    -- 🎭 **Prestige Paths**
    prestige_paths = {
        dreadnought = {
            description = "A towering force of destruction, using raw strength to crush enemies.",
            bonuses = {
                "Can wield massive two-handed weapons effortlessly",
                "Gains temporary damage immunity after a kill",
                "Higher stamina pool"
            },
            weaknesses = {
                "Slow movement",
                "Requires high endurance management"
            }
        },

        reaver = {
            description = "A savage combatant who fuels attacks with pain and blood.",
            bonuses = {
                "Life-steal on melee hits",
                "Attacks deal bonus damage when at low health",
                "Can use dual-wielded great weapons"
            },
            weaknesses = {
                "Can be countered with healing reduction effects",
                "Less effective against tanky opponents"
            }
        }
    }
}

return Berserker
