-- behaviors/construction_repair.lua
-- Construction and repair behavior for entities such as blacksmiths or builders

local ConstructionRepairBehavior = {}  -- create an empty table

ConstructionRepairBehavior.id = "construction_repair"
ConstructionRepairBehavior.name = "Construction and Repair Behavior"

-- Configuration parameters
ConstructionRepairBehavior.defaultConfig = {
    workRadius = 10,              -- Radius to detect items needing repair or construction
    moveSpeed = 1.2,              -- Movement speed while performing tasks
    repairSpeed = 1.0,            -- Speed at which entities perform repairs
    constructionSpeed = 1.0,      -- Speed of constructing new objects
    gatherMaterials = true,       -- Whether entity gathers needed materials
    materialSearchRadius = 15,    -- Radius to find materials
    returnHomeAfterTask = true,   -- Whether entity returns home after task completion
    homePosition = nil,           -- Home or workshop location
    taskPriority = {"repair", "construction"}, -- Priority order for tasks
    idleBehaviorWhenNoTask = true -- Perform idle animations/actions if no tasks
}

-- Initialize the behavior
function ConstructionRepairBehavior.init(entity, customConfig)
    entity.constructionRepairState = entity.constructionRepairState or {
        currentTask = nil,
        materialsNeeded = {},
        isWorking = false,
        config = {}
    }

    local config = entity.constructionRepairState.config
    -- Merge default configuration values into the entity's state
    for k, v in pairs(ConstructionRepairBehavior.defaultConfig) do
        config[k] = v
    end

    if customConfig then
        for k, v in pairs(customConfig) do
            config[k] = v
        end
    end
end

-- Update function called every game tick
function ConstructionRepairBehavior.update(entity, world, dt)
    local state = entity.constructionRepairState
    local config = state.config

    -- Check for tasks if not currently working
    if not state.currentTask then
        state.currentTask = ConstructionRepairBehavior.findTask(entity, world)
        if not state.currentTask then return end

        -- Check if materials are needed
        if config.gatherMaterials and not ConstructionRepairBehavior.hasMaterials(entity, state.currentTask) then
            ConstructionRepairBehavior.gatherMaterials(entity, world)
        end
    end

    -- Perform task if one is assigned
    if state.currentTask then
        if state.isWorking then
            ConstructionRepairBehavior.performTask(entity, dt)
        else
            -- Move to the task location
            ConstructionRepairBehavior.moveToTask(entity, dt)
        end
    end

    -- Return home if enabled and no tasks remain
    if config.returnHomeAfterTask and not state.currentTask then
        ConstructionRepairBehavior.returnHome(entity, dt)
    end
end

-- Find new tasks (placeholder implementation)
function ConstructionRepairBehavior.findTask(entity, world)
    -- Implementation details...
end

-- Check if the entity has the materials needed (placeholder implementation)
function ConstructionRepairBehavior.hasMaterials(entity, task)
    -- Implementation details...
end

-- Gather materials (placeholder implementation)
function ConstructionRepairBehavior.gatherMaterials(entity, world)
    -- Implementation details...
end

-- Move entity to task location (placeholder implementation)
function ConstructionRepairBehavior.moveToTask(entity, dt)
    -- Implementation details...
end

-- Perform the current construction or repair task (placeholder implementation)
function ConstructionRepairBehavior.performTask(entity, dt)
    -- Implementation details...
end

-- Return home after completing tasks (placeholder implementation)
function ConstructionRepairBehavior.returnHome(entity, dt)
    -- Implementation details...
end

return ConstructionRepairBehavior
