local TeleportTrap = {
    id = "teleport_trap",
    name = "Teleport Trap",
    type = "trap",
    
    -- Entity categories
    categories = {"trap", "dungeon", "magic"},
    
    -- Target categories
    targetCategories = {"player", "monster"},
    
    -- Stats
    maxHealth = 15,
    health = 15,
    duration = 0, -- Instant effect
    cooldown = 30,
    
    -- Trap properties
    properties = {
        radius = 1.5,
        setupTime = 2,
        isArmed = false,
        isTriggered = false,
        destroyOnActivate = false,
        teleportRadius = 8,
        swapChance = 0.7,
        stunDuration = 2
    },
    
    -- Appearance
    appearance = {
        sprite = "teleport_trap",
        scale = 1.0,
        animations = {
            "idle", "trigger", "teleport"
        },
        variants = {
            "blue", "purple", "void", "crystal"
        }
    },
    
    -- Sound effects
    sounds = {
        trigger = "teleport_trap_trigger",
        teleport = "teleport_trap_teleport",
        swap = "teleport_trap_swap"
    },
    
    -- Effects
    effects = {
        stun = {
            type = "status",
            duration = 2,
            effects = {
                stun = true
            }
        }
    }
}

-- Initialize the trap
function TeleportTrap.init(entity, world)
    -- Copy all fields from TeleportTrap template to entity instance
    for k, v in pairs(TeleportTrap) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position
    entity.position = entity.position or {x = 0, y = 0}

    -- Set random variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize trap state
    entity.properties.isArmed = false
    entity.properties.isTriggered = false
    entity.properties.triggerTime = 0

    return entity
end

-- Find nearest valid target for swapping
function TeleportTrap.findNearestTarget(entity, world, excludeTarget)
    local nearestTarget = nil
    local minDistance = math.huge
    
    if world and world.entities then
        for _, target in ipairs(world.entities) do
            if target ~= excludeTarget and target.position and target.categories then
                -- Check if target is in trap categories
                local isTarget = false
                for _, category in ipairs(entity.targetCategories) do
                    if table.contains(target.categories, category) then
                        isTarget = true
                        break
                    end
                end
                
                if isTarget then
                    local distance = math.sqrt(
                        (target.position.x - entity.position.x)^2 + 
                        (target.position.y - entity.position.y)^2
                    )
                    
                    if distance <= entity.properties.teleportRadius and distance < minDistance then
                        nearestTarget = target
                        minDistance = distance
                    end
                end
            end
        end
    end
    
    return nearestTarget
end

-- Update the trap
function TeleportTrap.update(entity, world, dt)
    -- Check if trap is armed and not triggered
    if entity.properties.isArmed and not entity.properties.isTriggered then
        -- Check for targets in radius
        if world and world.entities then
            for _, target in ipairs(world.entities) do
                if target.position and target.categories then
                    -- Check if target is in trap categories
                    local isTarget = false
                    for _, category in ipairs(entity.targetCategories) do
                        if table.contains(target.categories, category) then
                            isTarget = true
                            break
                        end
                    end
                    
                    if isTarget then
                        local distance = math.sqrt(
                            (target.position.x - entity.position.x)^2 + 
                            (target.position.y - entity.position.y)^2
                        )
                        
                        if distance <= entity.properties.radius then
                            -- Trigger trap
                            entity.properties.isTriggered = true
                            entity.properties.triggerTime = world.time
                            
                            -- Find nearest target for swapping
                            local swapTarget = TeleportTrap.findNearestTarget(entity, world, target)
                            
                            if swapTarget and math.random() < entity.properties.swapChance then
                                -- Store positions
                                local targetPos = {x = target.position.x, y = target.position.y}
                                local swapPos = {x = swapTarget.position.x, y = swapTarget.position.y}
                                
                                -- Swap positions
                                target.position.x = swapPos.x
                                target.position.y = swapPos.y
                                swapTarget.position.x = targetPos.x
                                swapTarget.position.y = targetPos.y
                                
                                -- Apply stun effect to both targets
                                if target.applyEffect then
                                    target.applyEffect(entity.effects.stun)
                                end
                                if swapTarget.applyEffect then
                                    swapTarget.applyEffect(entity.effects.stun)
                                end
                                
                                -- Play swap sound
                                if world.playSound then
                                    world.playSound(entity.sounds.swap)
                                end
                            else
                                -- Random teleport if no swap target or swap failed
                                local angle = math.random() * math.pi * 2
                                local distance = math.random() * entity.properties.teleportRadius
                                target.position.x = entity.position.x + math.cos(angle) * distance
                                target.position.y = entity.position.y + math.sin(angle) * distance
                                
                                -- Apply stun effect
                                if target.applyEffect then
                                    target.applyEffect(entity.effects.stun)
                                end
                                
                                -- Play teleport sound
                                if world.playSound then
                                    world.playSound(entity.sounds.teleport)
                                end
                            end
                            
                            -- Play trigger sound
                            if world.playSound then
                                world.playSound(entity.sounds.trigger)
                            end
                            
                            break
                        end
                    end
                end
            end
        end
    end
    
    -- Check if trap can be rearmed
    if not entity.properties.isArmed and world.time - entity.properties.triggerTime >= entity.cooldown then
        entity.properties.isArmed = true
        entity.properties.isTriggered = false
    end
end

-- Arm the trap
function TeleportTrap.arm(entity, world)
    if not entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = true
        entity.properties.triggerTime = world.time
        return true
    end
    return false
end

-- Disarm the trap
function TeleportTrap.disarm(entity, world)
    if entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = false
        entity.properties.triggerTime = world.time
        return true
    end
    return false
end

return TeleportTrap 