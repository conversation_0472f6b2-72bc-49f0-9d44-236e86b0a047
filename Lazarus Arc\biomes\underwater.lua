-- biomes/underwater.lua
-- Declare UnderwaterBiome at the top level so it can be referenced from within functions
local UnderwaterBiome = {}

-- Define the biome properties
UnderwaterBiome = {
    id = "underwater",
    name = "Underwater",
    description = "A vast and mysterious world beneath the ocean surface.", --

    -- Environmental factors (High pressure, variable temp/light, currents)
    environment = {
        -- Humidity is irrelevant underwater
        temperature = 0.2, -- Represents cold deep water, can vary near surface/vents
        sunlight = 0.1,    -- Varies greatly with depth, default is low
        currentStrength = 0.3, -- Water currents instead of wind
        pressure = 0.8     -- High pressure environment (0=surface, 1=deep)
    },

    -- Core tile types used in this biome
    -- NOTE: Needs 'ocean_floor', 'coral_reef', 'kelp_forest', 'underwater_cave', 'trench'. Uses 'water', 'deep_water', 'ruins', 'lava', 'volcanic_vent'.
    primaryTiles = {"ocean_floor", "water_column"}, -- Seabed and the water itself
    secondaryTiles = {"coral_reef", "kelp_forest", "deep_water"}, -- Specific habitats or deeper zones
    rareTiles = {"underwater_cave", "trench", "hydrothermal_vent"}, -- Renamed volcanic_vent

    -- Default proportions (Represents seabed composition primarily)
    tileDistribution = {
        ocean_floor = 0.7,
        coral_reef = 0.1,
        kelp_forest = 0.08,
        deep_water = 0.05, -- Transition to deeper zones?
        underwater_cave = 0.03,
        trench = 0.02,
        hydrothermal_vent = 0.02
    },

    -- Entities common to this biome
    commonEntities = {
        "small_fish_school", "jellyfish", "seahorse", "sea_anemone", "crab_ocean" -- Added anemone/crab
    },
    uncommonEntities = {
        "shark", "dolphin", "sea_turtle", "bioluminescent_fish" -- Added turtle/bio-fish
    },
    rareEntities = {
        "whale", "giant_squid", "merfolk_scout", "deep_sea_angler" -- Added merfolk/angler
    },

    -- Biome variants for diverse generation
    variants = {
        coral_reef = {
            name = "Coral Reef",
            temperature = 0.5, -- Warmer shallow water
            sunlight = 0.7,
            tileAdjustments = {
                ocean_floor = 0.2,
                coral_reef = 0.6, -- Dominated by reef
                shallow_water = 0.1, -- Assuming reef is shallower
                sand = 0.1 -- Sandy patches between coral
            },
            entityAdjustments = {
                 small_fish_school = 2.0,
                 seahorse = 1.5,
                 shark = 0.8, -- Different types maybe? Reef shark?
                 dolphin = 1.2
            },
            specialEffects = {"vibrant_colors", "high_biodiversity", "gentle_currents"}
        },
        kelp_forest = {
            name = "Kelp Forest",
            temperature = 0.3,
            sunlight = 0.4,
            tileAdjustments = {
                ocean_floor = 0.4,
                kelp_forest = 0.5, -- Dominated by kelp tiles/entities
                rocky_ground_underwater = 0.1 -- Kelp often grows on rocks
            },
            entityAdjustments = {
                 sea_otter = 1.5, -- Added otter
                 kelp_fish = 1.8, -- Specific fish type
                 shark = 1.1 -- Sharks hunt here
            },
            specialEffects = {"swaying_kelp_visual", "dappled_light", "hidden_paths"}
        },
        deep_trench = {
             name = "Deep Trench",
             temperature = 0.05, -- Very cold
             sunlight = 0.0,
             pressure = 1.0, -- Maximum pressure
             tileAdjustments = {
                 trench = 0.7, -- Dominated by trench floor tile
                 ocean_floor = 0.2,
                 hydrothermal_vent = 0.1
             },
             entityAdjustments = {
                  deep_sea_angler = 1.8,
                  giant_squid = 1.2,
                  bioluminescent_fish = 2.0,
                  whale = 0.1 -- Only occasional visits?
             },
             specialEffects = {"extreme_pressure", "total_darkness", "strange_bioluminescence"}
        },
         sunken_city = { -- Tied to feature?
              name = "Sunken City Ruins",
              tileAdjustments = {
                  ocean_floor = 0.3,
                  ruins_underwater = 0.5, -- Underwater ruins tiles
                  sand = 0.1,
                  underwater_cave = 0.1
              },
              entityAdjustments = {
                   merfolk_scout = 1.5,
                   guardian_construct_water = 1.0, -- Underwater variant
                   ancient_sea_creature = 1.0
              },
              specialEffects = {"ancient_architecture", "lingering_magic", "hidden_artifacts"}
         },
         hydrothermal_vents = {
              name = "Hydrothermal Vents",
              temperature = 0.9, -- Locally very hot
              tileAdjustments = {
                  ocean_floor = 0.5, -- Often volcanic rock floor
                  hydrothermal_vent = 0.3,
                  sulfur_deposit_underwater = 0.1,
                  lava_underwater = 0.1 -- Underwater lava?
              },
              entityAdjustments = {
                   tube_worm_colony = 1.8, -- Specific vent life
                   heat_resistant_creature = 1.5,
                   fire_elemental_water = 1.0 -- Paradoxical?
              },
              specialEffects = {"superheated_water", "chemical_plumes", "unique_ecosystem"}
         }
    },

    -- Structures that can generate in this biome
    structures = {
        {
            name = "merfolk_city",
            chance = 0.02,
            unique = true,
            -- Requires specific location? Deep but not trench?
            entities = {"merfolk_citizen", "merfolk_guard", "royal_merfolk"}
        },
        {
            name = "underwater_temple", -- Often tied to ruins/sunken city
            chance = 0.04,
            requiresVariant = "sunken_city", -- Or near ruins features
            entities = {"temple_guardian_water", "ancient_priest_spirit", "water_elemental_bound"}
        },
        {
            name = "research_station", -- Underwater version
            chance = 0.03,
            entities = {"marine_biologist", "submersible_pilot", "research_sub"}
        },
        {
            name = "abandoned_submarine", -- Found as wreck usually
            chance = 0.06,
            entities = {"rusted_hull", "ghost_crew", "salvageable_parts"}
        },
        { -- Added shipwreck from tile list
             name = "shipwreck",
             chance = 0.1,
             entities = {"rotting_wood", "treasure_chest_small", "skeleton_crew"}
        }
    },

    -- Environmental effects instead of weather
    environmentalEffects = {
        current_strength = 0.3, -- Base current
        current_direction = 0, -- Base direction (degrees)
        pressure_level = 0.8, -- Base pressure
        visibility = 0.7, -- Base visibility (affected by depth/murkiness)
        marine_snow = 0.2 -- Particle effect chance/intensity
    },

    -- Unique features generation
    features = {
        {
            name = "underwater_volcano", -- Places vents/lava/rock
            chance = 0.03,
            unique = true
        },
        {
            name = "trench", -- Creates deep trench tiles/area
            chance = 0.05,
            -- Generation logic needed to carve depth
        },
        {
            name = "sunken_city", -- Large area of underwater ruins
            chance = 0.02,
            unique = true,
            size = {min=20, max=50}
        },
        {
            name = "giant_squid_lair", -- Location associated with rare entity
            chance = 0.04,
            -- Places cave/structure and spawns squid?
        },
        {
            name = "bioluminescent_area", -- Area with glowing entities/plants
            chance = 0.1,
            size = {min=8, max=20}
        },
         { -- Added coral reef / kelp forest as features placing tiles/entities
             name = "coral_reef_patch",
             chance = 0.15,
             size = {min=10, max=30}
             -- Places coral reef tiles/entities
         },
         {
             name = "kelp_forest_patch",
             chance = 0.12,
             size = {min=15, max=40}
             -- Places kelp forest tiles/entities
         }
    },

    -- Generation algorithm (Needs specific logic for underwater context)
    generate = function(chunk, world, variant)
        print("Generating " .. (variant and UnderwaterBiome.variants[variant].name or "Underwater") .. " biome")
        local variantData = variant and UnderwaterBiome.variants[variant] or nil
        local baseTemp = variantData and variantData.temperature or UnderwaterBiome.environment.temperature
        local baseSunlight = variantData and variantData.sunlight or UnderwaterBiome.environment.sunlight

        -- Adjust tile distribution
        local tileDistribution = {}
        for tileType, chance in pairs(UnderwaterBiome.tileDistribution) do
            tileDistribution[tileType] = chance
            if variantData and variantData.tileAdjustments and variantData.tileAdjustments[tileType] then
                tileDistribution[tileType] = variantData.tileAdjustments[tileType]
            end
        end

        -- Noise functions
        local seed = world.seed + (chunk.x * 853 + chunk.y * 199)
        math.randomseed(seed)
        local function pnoise3D(x, y, z, frequency, seed_offset) return love.math.noise(x * frequency, y * frequency, z * frequency, (world.seed + (seed_offset or 0)) * 0.01) end -- Using 3D noise for depth

        -- Assume chunk has a base depth property
        local baseDepth = chunk.baseDepth or 50 -- Arbitrary base depth units
        local depthVariation = 20

        -- Initialize 2D tiles array if not exists
        chunk.tiles = chunk.tiles or {}
        for x = 0, world.CHUNK_SIZE - 1 do
            chunk.tiles[x] = chunk.tiles[x] or {}
        end

        -- Generate tiles (representing the seabed primarily)
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                local worldX = chunk.x * world.CHUNK_SIZE + x
                local worldY = chunk.y * world.CHUNK_SIZE + y

                -- Use 3D noise for depth and features
                local depthNoise = pnoise3D(worldX, worldY, baseDepth, 0.05, 1) -- Controls seabed elevation
                local featureNoise = pnoise3D(worldX, worldY, baseDepth, 0.1, 2) -- Controls features like reefs, kelp
                local compositionNoise = pnoise3D(worldX, worldY, baseDepth, 0.2, 3) -- Controls floor material

                local currentDepth = baseDepth + depthNoise * depthVariation
                local tileType = "ocean_floor" -- Default seabed

                -- Determine tile type based on noise and distribution
                if featureNoise > 0.6 then
                    tileType = "coral_reef"
                elseif featureNoise < -0.5 then
                    tileType = "kelp_forest"
                elseif compositionNoise > 0.7 then
                    tileType = "rocky_ground_underwater" -- Needs definition
                elseif compositionNoise < -0.6 then
                    tileType = "sand_underwater" -- Needs definition
                end

                -- Apply rarer tile distribution overrides (simplified)
                local tileRoll = math.random()
                local cumulativeChance = 0
                for tType, chance in pairs(tileDistribution) do
                    cumulativeChance = cumulativeChance + chance
                    if tileRoll <= cumulativeChance and tType ~= "ocean_floor" and tType ~= "water_column" then
                         -- Allow override for trench, cave, vent etc based on distribution
                         tileType = tType
                         break
                    end
                end

                -- Variant logic
                if variant == "deep_trench" and currentDepth > baseDepth + depthVariation * 0.5 then
                    tileType = "trench" -- Needs definition
                elseif variant == "coral_reef" and featureNoise > 0.3 then
                     if math.random() < 0.5 then tileType = "coral_reef" end
                end

                -- Create the tile (representing the seabed)
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = math.random(1, 3),
                    depth = currentDepth,
                    temperature = baseTemp + (1-baseTemp) * math.max(0, 1 - currentDepth / 100), -- Temp increases closer to surface/vents? Placeholder logic
                    pressure = UnderwaterBiome.environment.pressure * (currentDepth / baseDepth), -- Pressure increases with depth
                    sunlight = baseSunlight * math.max(0, 1 - currentDepth / 80), -- Sunlight decreases with depth
                    isUnderwater = true,
                    passable = true -- Seabed itself is usually passable, water column handled differently
                }

                chunk.tiles[x][y] = tile
            end
        end

        -- Call helper functions
        UnderwaterBiome.generateFeatures(chunk, world, variant)
        UnderwaterBiome.populateEntities(chunk, world, variant)
        UnderwaterBiome.generateStructures(chunk, world, variant)
        UnderwaterBiome.postProcess(chunk, world, variant)
    end,

    -- Helper function implementations (Placeholders)
    generateFeatures = function(chunk, world, variant)
        print("Generating Underwater features...")
        -- Logic to place volcanoes (lava/vent tiles), trenches (deep tiles), sunken cities (ruins), squid lairs (cave/entity), bioluminescent areas (entities/tile effects), coral/kelp patches (tiles/entities)
        -- Ensure features are placed on 'ocean_floor' or appropriate depth/locations
         for _, feature in ipairs(UnderwaterBiome.features) do
             if math.random() < feature.chance then
                  if not feature.requiresVariant or feature.requiresVariant == variant then
                      if not feature.unique or not (chunk.uniqueFeatures and chunk.uniqueFeatures[feature.name]) then
                         local fx = math.random(1, world.CHUNK_SIZE - 2)
                         local fy = math.random(1, world.CHUNK_SIZE - 2)
                         local fwx = chunk.x * world.CHUNK_SIZE + fx
                         local fwy = chunk.y * world.CHUNK_SIZE + fy
                         print("Placing feature: " .. feature.name .. " at " .. fwx .. "," .. fwy)
                         -- Add specific generation logic per feature
                         if feature.name == "trench" then
                              -- Modify elevation/tile type to 'trench' in an area
                         elseif feature.name == "sunken_city" then
                              -- Place 'ruins_underwater' tiles in an area
                         elseif feature.name == "coral_reef_patch" or feature.name == "kelp_forest_patch" then
                              -- Place corresponding tiles or entities in an area
                         end
                         if feature.unique then chunk.uniqueFeatures = chunk.uniqueFeatures or {}; chunk.uniqueFeatures[feature.name] = true end
                     end
                 end
             end
        end
    end,

    populateEntities = function(chunk, world, variant)
        print("Populating Underwater entities...")
        -- Spawn entities, ensuring they match depth/habitat (coral, kelp, open water, deep)
        local variantData = variant and UnderwaterBiome.variants[variant] or nil
        local function spawnEntity(entityType, count)
            local multiplier = 1.0
            if variantData and variantData.entityAdjustments and variantData.entityAdjustments[entityType] then multiplier = variantData.entityAdjustments[entityType] end
            local adjustedCount = math.floor(count * multiplier + 0.5)
            for i = 1, adjustedCount do
                 local attempts = 0; local placed = false
                 while attempts < 5 and not placed do
                      local x = math.random(0, world.CHUNK_SIZE - 1)
                      local y = math.random(0, world.CHUNK_SIZE - 1)
                      local tile = chunk.tiles[x][y]
                      -- Check if tile type is suitable habitat
                      if tile and tile.isUnderwater then
                           -- TODO: Add more specific habitat checks (e.g., fish need water, coral needs reef)
                           world.entitySystem:addEntity(entityType, chunk.x * world.CHUNK_SIZE + x, chunk.y * world.CHUNK_SIZE + y, {isUnderwater=true})
                           placed = true
                      end
                      attempts = attempts + 1
                 end
            end
        end
        -- Spawn based on lists
        for _, et in ipairs(UnderwaterBiome.commonEntities) do spawnEntity(et, math.random(3,8)) end -- Oceans are full of life
        for _, et in ipairs(UnderwaterBiome.uncommonEntities) do spawnEntity(et, math.random(1,4)) end
        for _, et in ipairs(UnderwaterBiome.rareEntities) do if math.random() < 0.05 then spawnEntity(et, 1) end end
        -- Add variant specifics
    end,

    generateStructures = function(chunk, world, variant)
        print("Generating Underwater structures...")
         -- Similar logic: Iterate list, check chance/variant, find location on ocean floor, place structure & entities
         -- Place shipwrecks as structures/features
          for _, structure in ipairs(UnderwaterBiome.structures) do
             if math.random() < structure.chance then
                  if not structure.requiresVariant or structure.requiresVariant == variant then
                      local attempts = 0; local placed = false; local sx, sy
                      while attempts < 10 and not placed do
                           sx = math.random(2, world.CHUNK_SIZE - 3)
                           sy = math.random(2, world.CHUNK_SIZE - 3)
                           local tile = chunk.tiles[sx][sy]
                           if tile and tile.isUnderwater and tile.type=="ocean_floor" then -- Place on basic floor
                                placed = true
                           end
                           attempts = attempts + 1
                      end
                      if placed then
                           local worldX = chunk.x * world.CHUNK_SIZE + sx
                           local worldY = chunk.y * world.CHUNK_SIZE + sy
                           print("Placing structure: "..structure.name.." at "..worldX..","..worldY)
                           if world.chunkSystem and world.chunkSystem.placeStructure then
                                world.chunkSystem:placeStructure(chunk, structure.name, worldX, worldY)
                           else
                               world.entitySystem:addEntity("structure_marker", worldX, worldY, { structureType = structure.name })
                           end
                           if structure.entities then
                                for _, et in ipairs(structure.entities) do
                                     world.entitySystem:addEntity(et, worldX + math.random(-1,1), worldY + math.random(-1,1))
                                end
                           end
                      end
                  end
             end
         end
    end,

    postProcess = function(chunk, world, variant)
        print("Post-processing Underwater chunk...")
        -- Apply effects like currents, pressure, ambient light based on depth/variant
         local variantData = variant and UnderwaterBiome.variants[variant] or nil
         local basePressure = variantData and variantData.pressure or UnderwaterBiome.environment.pressure
         chunk.environmentalEffects = chunk.environmentalEffects or {}
         table.insert(chunk.environmentalEffects, {type="water_current", strength=UnderwaterBiome.environment.currentStrength, direction=math.random()*360})
         table.insert(chunk.environmentalEffects, {type="water_pressure", level=basePressure})
         if variantData and variantData.specialEffects then
             for _, effect in ipairs(variantData.specialEffects) do table.insert(chunk.environmentalEffects, effect) end
         end
    end,

    init = function(worldCore)
        print("Underwater biome module initialized")
        UnderwaterBiome.worldCore = worldCore
        print("Underwater biome registered successfully for later use")
    end,

    registerWithWorld = function(world)
        print("Registering Underwater biome generators with world")
        if not world or not world.chunkSystem then print("Warning: Cannot register Underwater biome - world or chunkSystem is nil") return false end
        world.chunkSystem:registerGenerator(UnderwaterBiome.id, UnderwaterBiome.generate)
        for variantId, variantData in pairs(UnderwaterBiome.variants) do
            local fullVariantId = UnderwaterBiome.id .. "_" .. variantId
            world.chunkSystem:registerGenerator(fullVariantId, function(chunk, worldInstance)
                UnderwaterBiome.generate(chunk, worldInstance, variantId)
            end)
        end
        return true
    end,
}

return UnderwaterBiome