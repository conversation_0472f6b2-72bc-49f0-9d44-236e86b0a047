-- biome-template.lua
-- Template for creating dynamic biomes in Lazarus Arc

local UUID = require("uuid")
local BiomeTemplate = {
    id = "template",
    name = "Template Biome",
    description = "A template biome to demonstrate dynamic biome creation",
    
    -- Environmental factors (used by weather and entity systems)
    environment = {
        humidity = 0.5,    -- 0.0 to 1.0
        temperature = 0.5, -- 0.0 to 1.0
        sunlight = 0.8,    -- 0.0 to 1.0
        windStrength = 0.3 -- 0.0 to 1.0
    },
    
    -- Generation settings
    noiseScale = {
        base = 0.05,      -- Base terrain noise scale
        detail = 0.1,     -- Detail noise scale
        moisture = 0.07,  -- Moisture distribution scale
        feature = 0.2     -- Special features scale
    },
    
    -- Weather settings
    weather = {
        transitions = {
            -- Weather type => {possible next states with weights}
            clear = {
                clear = 70,
                cloudy = 20,
                rain = 5,
                fog = 5
            },
            cloudy = {
                clear = 30,
                cloudy = 40,
                rain = 25,
                fog = 5
            },
            rain = {
                clear = 20,
                cloudy = 40,
                rain = 35,
                storm = 5
            }
            -- Add more as needed
        }
    }
}

-- Available tile types cache
BiomeTemplate.tileTypes = nil

-- Available entity types cache
BiomeTemplate.entityTypes = nil

-- Available behavior types cache
BiomeTemplate.behaviorTypes = nil

-- Initialization function (called when biome is loaded)
function BiomeTemplate.init(self, world)
    print("Initializing " .. self.name .. " biome")
    
    -- Store reference to world
    self.world = world
    
    return self
end

-- Get all available tile types from the system
function BiomeTemplate.getTileTypes(self)
    if self.tileTypes then
        return self.tileTypes
    end
    
    self.tileTypes = {}
    
    -- Access the chunk system's loaded tile modules
    if self.world and self.world.chunkSystem and self.world.chunkSystem.tileModules then
        for tileName, _ in pairs(self.world.chunkSystem.tileModules) do
            table.insert(self.tileTypes, tileName)
        end
    end
    
    return self.tileTypes
end

-- Get all available entity types from the system
function BiomeTemplate.getEntityTypes(self)
    if self.entityTypes then
        return self.entityTypes
    end
    
    self.entityTypes = {}
    
    -- Access the chunk system's loaded entity modules
    if self.world and self.world.chunkSystem and self.world.chunkSystem.entityModules then
        for entityName, _ in pairs(self.world.chunkSystem.entityModules) do
            table.insert(self.entityTypes, entityName)
        end
    end
    
    return self.entityTypes
end

-- Get all available behavior types from the system
function BiomeTemplate.getBehaviorTypes(self)
    if self.behaviorTypes then
        return self.behaviorTypes
    end
    
    self.behaviorTypes = {}
    
    -- Access the chunk system's loaded behavior modules
    if self.world and self.world.chunkSystem and self.world.chunkSystem.behaviorModules then
        for behaviorName, _ in pairs(self.world.chunkSystem.behaviorModules) do
            table.insert(self.behaviorTypes, behaviorName)
        end
    end
    
    return self.behaviorTypes
end

-- Pick a random tile type from a category (based on available tiles)
function BiomeTemplate.getRandomTileType(self, category)
    local tileTypes = self:getTileTypes()
    local possibleTiles = {}
    
    -- Filter tiles by category
    for _, tileName in ipairs(tileTypes) do
        local tileModule = self.world.chunkSystem.getTileModule(tileName)
        
        if tileModule and tileModule.category == category then
            table.insert(possibleTiles, tileName)
        end
    end
    
    -- If no matching tiles found, fall back to a default
    if #possibleTiles == 0 then
        -- Try to find any common ground tile
        for _, tileName in ipairs(tileTypes) do
            if tileName == "grass" or tileName == "dirt" or tileName == "sand" then
                return tileName
            end
        end
        
        -- If still nothing, return the first tile type
        return tileTypes[1] or "empty"
    end
    
    -- Return a random tile from the filtered list
    local randomIndex = love.math.random(1, #possibleTiles)
    return possibleTiles[randomIndex]
end

-- Pick a random entity type from a category (based on available entities)
function BiomeTemplate.getRandomEntityType(self, category)
    local entityTypes = self:getEntityTypes()
    local possibleEntities = {}
    
    -- Filter entities by category
    for _, entityName in ipairs(entityTypes) do
        local entityModule = self.world.chunkSystem.getEntityModule(entityName)
        
        if entityModule and entityModule.categories and table.contains(entityModule.categories, category) then
            table.insert(possibleEntities, entityName)
        end
    end
    
    -- If no matching entities found, return nil
    if #possibleEntities == 0 then
        return nil
    end
    
    -- Return a random entity from the filtered list
    local randomIndex = love.math.random(1, #possibleEntities)
    return possibleEntities[randomIndex]
end

-- Generate a chunk using this biome's rules
function BiomeTemplate.generateChunk(chunk, worldSeed)
    local chunkX, chunkY = chunk.x, chunk.y
    local chunkSize = 16 -- Should match the chunk system's chunkSize
    
    -- Create a local random generator with chunk-specific seed
    local seed = worldSeed + chunkX * 374761393 + chunkY * 668265263
    love.math.setRandomSeed(seed)
    
    -- Get available tile types
    local tileTypes = BiomeTemplate:getTileTypes()
    
    -- Select primary ground tile type (dynamically)
    local primaryTile = BiomeTemplate:getRandomTileType("ground")
    local secondaryTile = BiomeTemplate:getRandomTileType("ground")
    local waterTile = BiomeTemplate:getRandomTileType("water")
    local decorativeTile = BiomeTemplate:getRandomTileType("decorative")
    
    -- Generate the base terrain
    for y = 0, chunkSize - 1 do
        for x = 0, chunkSize - 1 do
            -- Generate base terrain using noise
            local noise = love.math.noise(x * BiomeTemplate.noiseScale.base, y * BiomeTemplate.noiseScale.base)
            local detailNoise = love.math.noise(x * BiomeTemplate.noiseScale.detail, y * BiomeTemplate.noiseScale.detail)
            local moistureNoise = love.math.noise(x * BiomeTemplate.noiseScale.moisture, y * BiomeTemplate.noiseScale.moisture)
            
            -- Combine noise values
            local combinedNoise = noise + detailNoise * 0.2
            
            -- Select tile based on noise values
            if combinedNoise < 0.3 then
                chunk.tiles[y][x] = { type = waterTile }
            elseif combinedNoise < 0.5 then
                chunk.tiles[y][x] = { type = primaryTile }
            else
                chunk.tiles[y][x] = { type = secondaryTile }
            end
            
            -- Add decorative elements based on moisture
            if moistureNoise > 0.7 then
                chunk.tiles[y][x].decorative = decorativeTile
            end
        end
    end
    
    -- Populate the chunk with entities
    BiomeTemplate.populateEntities(chunk)
    
    return chunk
end

-- Populate a chunk with entities based on biome rules
function BiomeTemplate.populateEntities(chunk)
    local chunkSize = 16
    local chunkX, chunkY = chunk.x, chunk.y
    
    -- Determine entity counts based on biome rules
    local animalCount = love.math.random(3, 8)
    local plantCount = love.math.random(5, 15)
    local objectCount = love.math.random(2, 5)
    
    -- Add animals
    for i = 1, animalCount do
        local x = love.math.random(0, chunkSize - 1)
        local y = love.math.random(0, chunkSize - 1)
        
        -- Check if tile is walkable
        if chunk.tiles[y][x].walkable then
            -- Get a random animal entity
            local entityType = BiomeTemplate:getRandomEntityType("animal")
            
            if entityType then
                -- Create entity data with UUID
                local entity = {
                    type = entityType,
                    uuid = UUID.generate(),
                    position = {
                        x = chunk.x * chunkSize + x + love.math.random(),
                        y = chunk.y * chunkSize + y + love.math.random()
                    },
                    -- Get available behaviors from the system
                    behaviors = {}
                }
                
                -- Add appropriate behaviors based on entity type
                local entityModule = BiomeTemplate.world.chunkSystem.getEntityModule(entityType)
                if entityModule and entityModule.behaviors then
                    entity.behaviors = entityModule.behaviors
                else
                    -- Default behavior if none specified
                    local randomBehavior = BiomeTemplate:getBehaviorTypes()[love.math.random(1, #BiomeTemplate:getBehaviorTypes())]
                    if randomBehavior then
                        table.insert(entity.behaviors, randomBehavior)
                    end
                end
                
                -- Add to entities list
                chunk.entities = chunk.entities or {}
                chunk.entities[entity.uuid] = entity
            end
        end
    end
    
    -- Add plants and static objects (similar to animals)
    for i = 1, plantCount do
        local x = love.math.random(0, chunkSize - 1)
        local y = love.math.random(0, chunkSize - 1)
        
        if chunk.tiles[y][x].walkable then
            local entityType = BiomeTemplate:getRandomEntityType("plant")
            
            if entityType then
                local entity = {
                    type = entityType,
                    uuid = UUID.generate(),
                    position = {
                        x = chunk.x * chunkSize + x + love.math.random(),
                        y = chunk.y * chunkSize + y + love.math.random()
                    },
                    behaviors = {}
                }
                
                chunk.entities = chunk.entities or {}
                chunk.entities[entity.uuid] = entity
            end
        end
    end
    
    -- Add static objects
    for i = 1, objectCount do
        local x = love.math.random(0, chunkSize - 1)
        local y = love.math.random(0, chunkSize - 1)
        
        if chunk.tiles[y][x].walkable then
            local entityType = BiomeTemplate:getRandomEntityType("object")
            
            if entityType then
                local entity = {
                    type = entityType,
                    uuid = UUID.generate(),
                    position = {
                        x = chunk.x * chunkSize + x + love.math.random(),
                        y = chunk.y * chunkSize + y + love.math.random()
                    },
                    behaviors = {}
                }
                
                chunk.entities = chunk.entities or {}
                chunk.entities[entity.uuid] = entity
            end
        end
    end
end

-- Helper function to check if a value exists in a table
function table.contains(tbl, value)
    for _, v in ipairs(tbl) do
        if v == value then
            return true
        end
    end
    return false
end

return BiomeTemplate