-- utils/pathfinding_heatmap.lua
-- Pathfinding heatmap visualization for debugging

local PathfindingHeatmap = {}

-- Initialize pathfinding heatmap
function PathfindingHeatmap.init()
    PathfindingHeatmap.enabled = false
    PathfindingHeatmap.heatmapData = {}
    PathfindingHeatmap.maxCost = 100
    PathfindingHeatmap.minCost = 0
    
    -- Display settings
    PathfindingHeatmap.tileSize = 32
    PathfindingHeatmap.alpha = 0.6
    PathfindingHeatmap.showGrid = true
    PathfindingHeatmap.showCosts = true
    PathfindingHeatmap.showPaths = true
    
    -- Color scheme for heatmap
    PathfindingHeatmap.colors = {
        passable = {0, 1, 0, PathfindingHeatmap.alpha}, -- Green for low cost
        moderate = {1, 1, 0, PathfindingHeatmap.alpha}, -- Yellow for medium cost
        difficult = {1, 0.5, 0, PathfindingHeatmap.alpha}, -- Orange for high cost
        blocked = {1, 0, 0, PathfindingHeatmap.alpha}, -- Red for blocked
        path = {0, 0.5, 1, 0.8}, -- Blue for active paths
        start = {0, 1, 1, 1}, -- <PERSON>an for start points
        goal = {1, 0, 1, 1} -- Ma<PERSON><PERSON> for goal points
    }
    
    -- Current pathfinding data
    PathfindingHeatmap.activePaths = {}
    PathfindingHeatmap.startPoints = {}
    PathfindingHeatmap.goalPoints = {}
    
    -- Update frequency
    PathfindingHeatmap.updateInterval = 0.1
    PathfindingHeatmap.lastUpdate = 0
end

-- Toggle pathfinding heatmap
function PathfindingHeatmap.toggle()
    PathfindingHeatmap.enabled = not PathfindingHeatmap.enabled
    
    if PathfindingHeatmap.enabled then
        PathfindingHeatmap.generateHeatmapData()
        print("Pathfinding Heatmap enabled")
    else
        print("Pathfinding Heatmap disabled")
    end
    
    return PathfindingHeatmap.enabled
end

-- Update heatmap data
function PathfindingHeatmap.update(dt, world)
    if not PathfindingHeatmap.enabled then return end
    
    PathfindingHeatmap.lastUpdate = PathfindingHeatmap.lastUpdate + dt
    
    if PathfindingHeatmap.lastUpdate >= PathfindingHeatmap.updateInterval then
        PathfindingHeatmap.lastUpdate = 0
        PathfindingHeatmap.generateHeatmapData(world)
    end
end

-- Generate heatmap data from world
function PathfindingHeatmap.generateHeatmapData(world)
    PathfindingHeatmap.heatmapData = {}
    
    if not world then return end
    
    -- Get world bounds
    local minX, minY, maxX, maxY = PathfindingHeatmap.getWorldBounds(world)
    
    -- Generate cost data for each tile
    for x = minX, maxX do
        if not PathfindingHeatmap.heatmapData[x] then
            PathfindingHeatmap.heatmapData[x] = {}
        end
        
        for y = minY, maxY do
            local cost = PathfindingHeatmap.calculateTileCost(x, y, world)
            PathfindingHeatmap.heatmapData[x][y] = cost
        end
    end
end

-- Calculate pathfinding cost for a tile
function PathfindingHeatmap.calculateTileCost(x, y, world)
    -- Default cost
    local cost = 1
    
    -- Check if tile is blocked
    if PathfindingHeatmap.isTileBlocked(x, y, world) then
        return -1 -- Blocked
    end
    
    -- Get tile type and calculate cost
    local tileType = PathfindingHeatmap.getTileType(x, y, world)
    
    if tileType then
        -- Different terrain types have different costs
        local terrainCosts = {
            grass = 1,
            dirt = 1,
            stone = 2,
            water = 5,
            mud = 3,
            sand = 2,
            forest = 4,
            mountain = 8,
            swamp = 6
        }
        
        cost = terrainCosts[tileType] or 1
    end
    
    -- Add height-based cost if available
    local height = PathfindingHeatmap.getTileHeight(x, y, world)
    if height then
        cost = cost + math.abs(height) * 0.1
    end
    
    -- Add structure-based cost
    if PathfindingHeatmap.hasStructure(x, y, world) then
        cost = cost + 2
    end
    
    return cost
end

-- Check if tile is blocked
function PathfindingHeatmap.isTileBlocked(x, y, world)
    -- Check collision system if available
    if world.collisionSystem and world.collisionSystem.isBlocked then
        return world.collisionSystem:isBlocked(x, y)
    end
    
    -- Check tile system
    if world.tileSystem and world.tileSystem.isSolid then
        return world.tileSystem:isSolid(x, y)
    end
    
    -- Check world tiles directly
    if world.tiles and world.tiles[x] and world.tiles[x][y] then
        local tile = world.tiles[x][y]
        return tile.solid or tile.blocked or false
    end
    
    return false
end

-- Get tile type
function PathfindingHeatmap.getTileType(x, y, world)
    if world.tiles and world.tiles[x] and world.tiles[x][y] then
        local tile = world.tiles[x][y]
        return tile.type or tile.material or "grass"
    end
    
    return "grass"
end

-- Get tile height
function PathfindingHeatmap.getTileHeight(x, y, world)
    if world.heightMap and world.heightMap[x] and world.heightMap[x][y] then
        return world.heightMap[x][y]
    end
    
    if world.tiles and world.tiles[x] and world.tiles[x][y] then
        local tile = world.tiles[x][y]
        return tile.height or tile.elevation or 0
    end
    
    return 0
end

-- Check if tile has structure
function PathfindingHeatmap.hasStructure(x, y, world)
    if world.structures then
        for _, structure in ipairs(world.structures) do
            if structure.position and 
               math.floor(structure.position.x) == x and 
               math.floor(structure.position.y) == y then
                return true
            end
        end
    end
    
    return false
end

-- Get world bounds for heatmap generation
function PathfindingHeatmap.getWorldBounds(world)
    -- Default bounds
    local minX, minY, maxX, maxY = -50, -50, 50, 50
    
    -- Try to get bounds from world
    if world.bounds then
        minX = world.bounds.minX or minX
        minY = world.bounds.minY or minY
        maxX = world.bounds.maxX or maxX
        maxY = world.bounds.maxY or maxY
    end
    
    -- Try to get bounds from camera/viewport
    if world.camera then
        local viewRange = 25
        minX = math.floor(world.camera.x / PathfindingHeatmap.tileSize) - viewRange
        minY = math.floor(world.camera.y / PathfindingHeatmap.tileSize) - viewRange
        maxX = minX + viewRange * 2
        maxY = minY + viewRange * 2
    end
    
    return minX, minY, maxX, maxY
end

-- Draw pathfinding heatmap
function PathfindingHeatmap.draw(camera)
    if not PathfindingHeatmap.enabled then return end
    
    love.graphics.push()
    
    -- Apply camera transform if available
    if camera then
        love.graphics.translate(-camera.x, -camera.y)
    end
    
    -- Draw heatmap tiles
    for x, column in pairs(PathfindingHeatmap.heatmapData) do
        for y, cost in pairs(column) do
            PathfindingHeatmap.drawHeatmapTile(x, y, cost)
        end
    end
    
    -- Draw active paths
    if PathfindingHeatmap.showPaths then
        PathfindingHeatmap.drawActivePaths()
    end
    
    -- Draw start and goal points
    PathfindingHeatmap.drawStartGoalPoints()
    
    -- Draw grid if enabled
    if PathfindingHeatmap.showGrid then
        PathfindingHeatmap.drawGrid()
    end
    
    love.graphics.pop()
end

-- Draw single heatmap tile
function PathfindingHeatmap.drawHeatmapTile(x, y, cost)
    local screenX = x * PathfindingHeatmap.tileSize
    local screenY = y * PathfindingHeatmap.tileSize
    
    local color
    if cost < 0 then
        color = PathfindingHeatmap.colors.blocked
    elseif cost <= 2 then
        color = PathfindingHeatmap.colors.passable
    elseif cost <= 4 then
        color = PathfindingHeatmap.colors.moderate
    else
        color = PathfindingHeatmap.colors.difficult
    end
    
    love.graphics.setColor(color)
    love.graphics.rectangle("fill", screenX, screenY, PathfindingHeatmap.tileSize, PathfindingHeatmap.tileSize)
    
    -- Draw cost text if enabled
    if PathfindingHeatmap.showCosts and cost >= 0 then
        love.graphics.setColor(0, 0, 0, 1)
        love.graphics.print(tostring(cost), screenX + 2, screenY + 2)
    end
end

-- Draw active pathfinding paths
function PathfindingHeatmap.drawActivePaths()
    love.graphics.setColor(PathfindingHeatmap.colors.path)
    love.graphics.setLineWidth(3)
    
    for _, path in ipairs(PathfindingHeatmap.activePaths) do
        if #path > 1 then
            local points = {}
            for _, point in ipairs(path) do
                table.insert(points, point.x * PathfindingHeatmap.tileSize + PathfindingHeatmap.tileSize / 2)
                table.insert(points, point.y * PathfindingHeatmap.tileSize + PathfindingHeatmap.tileSize / 2)
            end
            
            if #points >= 4 then
                love.graphics.line(points)
            end
        end
    end
end

-- Draw start and goal points
function PathfindingHeatmap.drawStartGoalPoints()
    -- Draw start points
    love.graphics.setColor(PathfindingHeatmap.colors.start)
    for _, point in ipairs(PathfindingHeatmap.startPoints) do
        local x = point.x * PathfindingHeatmap.tileSize + PathfindingHeatmap.tileSize / 2
        local y = point.y * PathfindingHeatmap.tileSize + PathfindingHeatmap.tileSize / 2
        love.graphics.circle("fill", x, y, 8)
    end
    
    -- Draw goal points
    love.graphics.setColor(PathfindingHeatmap.colors.goal)
    for _, point in ipairs(PathfindingHeatmap.goalPoints) do
        local x = point.x * PathfindingHeatmap.tileSize + PathfindingHeatmap.tileSize / 2
        local y = point.y * PathfindingHeatmap.tileSize + PathfindingHeatmap.tileSize / 2
        love.graphics.circle("fill", x, y, 8)
    end
end

-- Draw grid
function PathfindingHeatmap.drawGrid()
    love.graphics.setColor(0.5, 0.5, 0.5, 0.3)
    love.graphics.setLineWidth(1)
    
    -- Get visible bounds
    local minX, minY, maxX, maxY = PathfindingHeatmap.getVisibleBounds()
    
    -- Draw vertical lines
    for x = minX, maxX do
        local screenX = x * PathfindingHeatmap.tileSize
        love.graphics.line(screenX, minY * PathfindingHeatmap.tileSize, screenX, maxY * PathfindingHeatmap.tileSize)
    end
    
    -- Draw horizontal lines
    for y = minY, maxY do
        local screenY = y * PathfindingHeatmap.tileSize
        love.graphics.line(minX * PathfindingHeatmap.tileSize, screenY, maxX * PathfindingHeatmap.tileSize, screenY)
    end
end

-- Get visible bounds for grid drawing
function PathfindingHeatmap.getVisibleBounds()
    -- Default to heatmap data bounds
    local minX, minY, maxX, maxY = math.huge, math.huge, -math.huge, -math.huge
    
    for x, column in pairs(PathfindingHeatmap.heatmapData) do
        minX = math.min(minX, x)
        maxX = math.max(maxX, x)
        for y, _ in pairs(column) do
            minY = math.min(minY, y)
            maxY = math.max(maxY, y)
        end
    end
    
    return minX, minY, maxX, maxY
end

-- Add active path for visualization
function PathfindingHeatmap.addPath(path)
    table.insert(PathfindingHeatmap.activePaths, path)
    
    -- Limit number of active paths
    if #PathfindingHeatmap.activePaths > 10 then
        table.remove(PathfindingHeatmap.activePaths, 1)
    end
end

-- Add start point
function PathfindingHeatmap.addStartPoint(x, y)
    table.insert(PathfindingHeatmap.startPoints, {x = x, y = y})
    
    -- Limit number of start points
    if #PathfindingHeatmap.startPoints > 5 then
        table.remove(PathfindingHeatmap.startPoints, 1)
    end
end

-- Add goal point
function PathfindingHeatmap.addGoalPoint(x, y)
    table.insert(PathfindingHeatmap.goalPoints, {x = x, y = y})
    
    -- Limit number of goal points
    if #PathfindingHeatmap.goalPoints > 5 then
        table.remove(PathfindingHeatmap.goalPoints, 1)
    end
end

-- Clear all pathfinding data
function PathfindingHeatmap.clear()
    PathfindingHeatmap.activePaths = {}
    PathfindingHeatmap.startPoints = {}
    PathfindingHeatmap.goalPoints = {}
end

-- Set tile size
function PathfindingHeatmap.setTileSize(size)
    PathfindingHeatmap.tileSize = math.max(8, math.min(64, size))
end

-- Toggle display options
function PathfindingHeatmap.toggleGrid()
    PathfindingHeatmap.showGrid = not PathfindingHeatmap.showGrid
    return PathfindingHeatmap.showGrid
end

function PathfindingHeatmap.toggleCosts()
    PathfindingHeatmap.showCosts = not PathfindingHeatmap.showCosts
    return PathfindingHeatmap.showCosts
end

function PathfindingHeatmap.togglePaths()
    PathfindingHeatmap.showPaths = not PathfindingHeatmap.showPaths
    return PathfindingHeatmap.showPaths
end

return PathfindingHeatmap
