-- behaviors/collect.lua
-- A behavior for entities to collect items from the world

-- Use factory function pattern for proper closure
local function createCollectBehavior()
    local CollectBehavior = {
        id = "collect",
        name = "Collect Items",
        description = "Enti<PERSON> will search for and collect nearby items",
        
        -- Configuration options
        defaults = {
            searchRadius = 5,            -- How far entity looks for items
            collectRadius = 1,           -- How close entity needs to be to collect
            moveSpeed = 1.0,             -- Movement speed modifier
            prioritizeRarity = true,     -- Prioritize rare items
            prioritizeValue = true,      -- Prioritize valuable items
            collectCooldown = 0.5,       -- Time between collections
            maxInventoryWeight = 100,    -- Maximum weight entity can carry
            returnWhenFull = true,       -- Whether to return to a 'home' position when inventory is full
            avoidDanger = true,          -- Avoid dangerous areas when collecting
            interactWithContainers = true, -- Open chests, break pots, etc.
            preferredItems = {}          -- List of preferred item types
        }
    }

    -- Initialization function called when behavior is applied to an entity
    function CollectBehavior.init(entity, options)
        -- Handle the case where this is being called during module registration
        if entity == CollectBehavior then
            return
        end
        
        -- Make sure options is not nil
        options = options or {}
        
        -- Create the behavior state in the entity
        entity.collectBehavior = {
            -- Apply default options, overridden by provided options
            searchRadius = options.searchRadius or CollectBehavior.defaults.searchRadius,
            collectRadius = options.collectRadius or CollectBehavior.defaults.collectRadius,
            moveSpeed = options.moveSpeed or CollectBehavior.defaults.moveSpeed,
            prioritizeRarity = options.prioritizeRarity ~= nil and options.prioritizeRarity or CollectBehavior.defaults.prioritizeRarity,
            prioritizeValue = options.prioritizeValue ~= nil and options.prioritizeValue or CollectBehavior.defaults.prioritizeValue,
            collectCooldown = options.collectCooldown or CollectBehavior.defaults.collectCooldown,
            maxInventoryWeight = options.maxInventoryWeight or CollectBehavior.defaults.maxInventoryWeight,
            returnWhenFull = options.returnWhenFull ~= nil and options.returnWhenFull or CollectBehavior.defaults.returnWhenFull,
            avoidDanger = options.avoidDanger ~= nil and options.avoidDanger or CollectBehavior.defaults.avoidDanger,
            interactWithContainers = options.interactWithContainers ~= nil and options.interactWithContainers or CollectBehavior.defaults.interactWithContainers,
            preferredItems = options.preferredItems or CollectBehavior.defaults.preferredItems,
            
            -- Internal state variables
            targetItem = nil,        -- Current item being targeted
            collectTimer = 0,        -- Cooldown timer for collection
            homePosition = {         -- Position to return to when inventory is full
                x = entity.position and entity.position.x or 0, 
                y = entity.position and entity.position.y or 0
            },
            state = "searching",     -- Current behavior state
            pathToTarget = {},       -- Path to target item
            lastSearchTime = 0,      -- Time of last search
            searchFrequency = 1.0,   -- How often to search for new items
            returnThreshold = 0.9,   -- Return home when inventory is this full (0-1)
            containerTarget = nil,   -- Current container being targeted
            collectHistory = {}      -- History of collected items
        }
        
        -- Ensure entity has an inventory
        if not entity.inventory then
            print("Warning: Entity " .. entity.id .. " has collect behavior but no inventory. Creating default inventory.")
            entity.inventory = {
                items = {},
                maxWeight = entity.collectBehavior.maxInventoryWeight,
                currentWeight = 0,
                
                addItem = function(self, item, quantity)
                    quantity = quantity or 1
                    
                    if not item then return false end
                    
                    local totalWeight = (item.weight or 1) * quantity
                    
                    -- Check if there's room in the inventory
                    if self.currentWeight + totalWeight > self.maxWeight then
                        print("Inventory full, can't add " .. item.id)
                        return false
                    end
                    
                    -- Add item to inventory
                    if self.items[item.id] then
                        self.items[item.id].quantity = self.items[item.id].quantity + quantity
                    else
                        self.items[item.id] = {
                            item = item,
                            quantity = quantity
                        }
                    end
                    
                    self.currentWeight = self.currentWeight + totalWeight
                    return true
                end,
                
                getWeight = function(self)
                    return self.currentWeight
                end,
                
                isFull = function(self, threshold)
                    threshold = threshold or 1.0
                    return self.currentWeight >= self.maxWeight * threshold
                end
            }
        end
        
        -- Initialize home position if entity has a position
        if entity.position then
            entity.collectBehavior.homePosition.x = entity.position.x
            entity.collectBehavior.homePosition.y = entity.position.y
        end
        
        print("Collect behavior initialized for entity " .. entity.id)
        return true
    end

    -- Update function called each frame
    function CollectBehavior.update(entity, dt, world)
        if not entity.collectBehavior then return end
        
        local behavior = entity.collectBehavior
        behavior.collectTimer = behavior.collectTimer - dt
        
        -- Update based on current state
        if behavior.state == "searching" then
            CollectBehavior.updateSearching(entity, dt, world)
        elseif behavior.state == "moving" then
            CollectBehavior.updateMoving(entity, dt, world)
        elseif behavior.state == "collecting" then
            CollectBehavior.updateCollecting(entity, dt, world)
        elseif behavior.state == "returning" then
            CollectBehavior.updateReturning(entity, dt, world)
        end
    end

    -- Helper functions for different states
    function CollectBehavior.updateSearching(entity, dt, world)
        local behavior = entity.collectBehavior
        
        -- Check if we need to search for new items
        if love.timer.getTime() - behavior.lastSearchTime >= behavior.searchFrequency then
            behavior.targetItem = CollectBehavior.findNearestItem(entity, world)
            behavior.lastSearchTime = love.timer.getTime()
        end
        
        if behavior.targetItem then
            behavior.state = "moving"
        elseif behavior.returnWhenFull and entity.inventory:isFull(behavior.returnThreshold) then
            behavior.state = "returning"
        end
    end

    function CollectBehavior.updateMoving(entity, dt, world)
        local behavior = entity.collectBehavior
        
        if not behavior.targetItem then
            behavior.state = "searching"
            return
        end
        
        -- Move towards target
        local dx = behavior.targetItem.position.x - entity.position.x
        local dy = behavior.targetItem.position.y - entity.position.y
        local distance = math.sqrt(dx * dx + dy * dy)
        
        if distance <= behavior.collectRadius then
            behavior.state = "collecting"
        else
            local angle = math.atan2(dy, dx)
            entity.position.x = entity.position.x + math.cos(angle) * behavior.moveSpeed * dt
            entity.position.y = entity.position.y + math.sin(angle) * behavior.moveSpeed * dt
        end
    end

    function CollectBehavior.updateCollecting(entity, dt, world)
        local behavior = entity.collectBehavior
        
        if not behavior.targetItem or behavior.collectTimer > 0 then return end
        
        -- Attempt to collect item
        if entity.inventory:addItem(behavior.targetItem) then
            table.insert(behavior.collectHistory, behavior.targetItem)
            behavior.targetItem = nil
            behavior.collectTimer = behavior.collectCooldown
            behavior.state = "searching"
        end
    end

    function CollectBehavior.updateReturning(entity, dt, world)
        local behavior = entity.collectBehavior
        
        -- Move towards home position
        local dx = behavior.homePosition.x - entity.position.x
        local dy = behavior.homePosition.y - entity.position.y
        local distance = math.sqrt(dx * dx + dy * dy)
        
        if distance <= behavior.collectRadius then
            -- Reset inventory and continue searching
            entity.inventory.currentWeight = 0
            entity.inventory.items = {}
            behavior.state = "searching"
        else
            local angle = math.atan2(dy, dx)
            entity.position.x = entity.position.x + math.cos(angle) * behavior.moveSpeed * dt
            entity.position.y = entity.position.y + math.sin(angle) * behavior.moveSpeed * dt
        end
    end

    function CollectBehavior.findNearestItem(entity, world)
        local behavior = entity.collectBehavior
        local nearestItem = nil
        local nearestDistance = math.huge
        
        -- Search for items in the world
        for _, item in ipairs(world.items or {}) do
            if CollectBehavior.isValidTarget(item, behavior) then
                local dx = item.position.x - entity.position.x
                local dy = item.position.y - entity.position.y
                local distance = math.sqrt(dx * dx + dy * dy)
                
                if distance < nearestDistance and distance <= behavior.searchRadius then
                    nearestItem = item
                    nearestDistance = distance
                end
            end
        end
        
        return nearestItem
    end

    function CollectBehavior.isValidTarget(item, behavior)
        if not item or not item.position then return false end
        
        -- Check if item is in preferred items list
        if #behavior.preferredItems > 0 then
            local isPreferred = false
            for _, preferred in ipairs(behavior.preferredItems) do
                if item.type == preferred then
                    isPreferred = true
                    break
                end
            end
            if not isPreferred then return false end
        end
        
        -- Check if item is valuable enough to collect
        if behavior.prioritizeValue and item.value then
            return item.value > 0
        end
        
        -- Check if item is rare enough to collect
        if behavior.prioritizeRarity and item.rarity then
            return item.rarity > 0
        end
        
        return true
    end
    
    return CollectBehavior
end

-- Create and return the behavior module
return createCollectBehavior() 