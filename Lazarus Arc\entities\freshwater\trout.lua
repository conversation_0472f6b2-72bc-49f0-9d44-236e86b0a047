local Trout = {
    id = "trout",
    name = "Rainbow Trout",
    type = "fish",
    
    -- Entity categories
    categories = {"animal", "fish", "freshwater", "predator", "spawner"},
    
    -- Threat and food categories
    threatCategories = {"player", "predator", "fish"},
    foodCategories = {"insect", "fish", "small_prey", "plant"},
    
    -- Stats
    maxHealth = 30,
    health = 30,
    maxStamina = 65,
    stamina = 65,
    speed = 2.2,
    
    -- Water properties
    water = {
        swimSpeed = 2.2,
        maxDepth = 8,
        preferredDepth = 3,
        oxygenLevel = 1.0,
        temperature = "cold",
        currentSpeed = 1.0
    },
    
    -- Behaviors
    behaviors = {"hunt", "patrol", "spawn", "school"},
    
    -- Behavior configurations
    behaviorConfigs = {
        hunt = {
            moveSpeed = 2.5,
            searchRadius = 10,
            attackRange = 1.2,
            damage = 12,
            preferredPrey = {"insect", "fish", "small_prey"},
            successChance = 0.7,
            currentBonus = 0.15
        },
        patrol = {
            moveSpeed = 1.8,
            patrolRadius = 15,
            preferredDepth = 3,
            restInterval = {8, 12}
        },
        spawn = {
            moveSpeed = 1.5,
            searchRadius = 20,
            preferredTerrain = "gravel",
            successChance = 0.8,
            season = "autumn",
            minTemperature = 10
        },
        school = {
            moveSpeed = 2.0,
            followDistance = 1.5,
            separationDistance = 1.0,
            alignmentStrength = 0.4,
            cohesionStrength = 0.5,
            maxGroupSize = 8
        }
    },
    
    -- Special abilities
    abilities = {
        leap = {
            speed = 3.0,
            duration = 0.4,
            cooldown = 2,
            staminaCost = 8
        },
        currentRide = {
            speed = 2.5,
            duration = 3,
            cooldown = 4,
            staminaCost = 5
        },
        flash = {
            range = 3,
            duration = 0.2,
            cooldown = 3,
            effect = "confuse"
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "trout",
        scale = 1.0,
        animations = {
            "idle", "swim", "leap", "spawn", "school"
        },
        variants = {
            "rainbow", "brown", "brook", "cutthroat"
        }
    },
    
    -- Sound effects
    sounds = {
        splash = "fish_splash",
        leap = "fish_leap",
        swim = "fish_swim",
        spawn = "fish_spawn"
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.6, quantity = {1, 2}},
        {id = "scale", chance = 0.5, quantity = {1, 3}},
        {id = "fin", chance = 0.3, quantity = {1, 1}},
        {id = "roe", chance = 0.2, quantity = {1, 2}}
    }
}

-- Initialize the entity
function Trout.init(entity, world)
    -- Copy all fields from Trout template to entity instance
    for k, v in pairs(Trout) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random trout variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Trout.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update water properties
    if entity.water then
        -- Adjust behavior success based on water current
        local currentBonus = 0
        if world and world.water and world.water.current then
            currentBonus = entity.behaviorConfigs.hunt.currentBonus
        end
        entity.behaviorConfigs.hunt.successChance = 0.7 + currentBonus

        -- Adjust preferred depth based on time of day
        if world and world.timeOfDay == "night" then
            entity.water.preferredDepth = 2
        else
            entity.water.preferredDepth = 3
        end

        -- Check spawning conditions
        if world and world.season == "autumn" and world.temperature >= 10 then
            entity.behaviorConfigs.spawn.successChance = 0.8
        else
            entity.behaviorConfigs.spawn.successChance = 0.0
        end
    end
end

return Trout 