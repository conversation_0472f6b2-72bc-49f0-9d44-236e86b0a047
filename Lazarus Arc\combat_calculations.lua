-- combat_calculations.lua
-- Handles core combat calculations for Lazarus Arc
-- Separated from combat.lua for better organization

local CombatCalculations = {}

--[[----------------------------------------------------------------------------
-- Utility Functions
------------------------------------------------------------------------------]]

-- Simple deep copy for stats manipulation (Consider a more robust library if needed)
local function deepcopy(original)
    local copy
    if type(original) == "table" then
        copy = {}
        for k, v in pairs(original) do
            copy[k] = deepcopy(v)
        end
        -- Copy metatable if necessary
        local mt = getmetatable(original)
        if mt then setmetatable(copy, deepcopy(mt)) end
    else
        copy = original
    end
    return copy
end

--[[----------------------------------------------------------------------------
-- Constants (Moved from combat.lua)
------------------------------------------------------------------------------]]

CombatCalculations.DAMAGE_TYPES = {
    PHYSICAL = "physical",
    MAGICAL = "magical",
    FIRE = "fire",
    ICE = "ice",
    LIGHTNING = "lightning",
    POISON = "poison",
    VOID = "void",
    HOLY = "holy"
}

CombatCalculations.STATUS_EFFECTS = {
    BURNING = "burning",
    FROZEN = "frozen",
    POISONED = "poisoned",
    STUNNED = "stunned",
    WEAKENED = "weakened",
    BLEEDING = "bleeding",
    STRENGTHENED = "strengthened",
    HASTED = "hasted",
    PROTECTED = "protected",
    CURSED = "cursed",
    BLESSED = "blessed",
    PRECISION = "precision",
    SILENCE = "silence"
    -- TODO: Add all relevant status effects for your game
}

-- Status effect definitions (Data used by calculations)
-- TODO: Define ALL status effects used in your game here, including icon paths, visual effects, etc.
CombatCalculations.StatusEffectData = {
    [CombatCalculations.STATUS_EFFECTS.BURNING] = {
        name = "Burning", icon = "icons/status/burning.png", color = {1, 0.3, 0},
        damagePerSecond = 5, damageType = CombatCalculations.DAMAGE_TYPES.FIRE,
        duration = 10, visualEffect = "fire_particles"
    },
    [CombatCalculations.STATUS_EFFECTS.FROZEN] = {
        name = "Frozen", icon = "icons/status/frozen.png", color = {0.5, 0.8, 1},
        affectsStat = "speed", statModifier = 0.5, duration = 8, -- 50% reduction
        visualEffect = "ice_particles"
    },
    [CombatCalculations.STATUS_EFFECTS.POISONED] = {
        name = "Poisoned", icon = "icons/status/poisoned.png", color = {0.2, 1, 0.2},
        damagePerSecond = 3, damageType = CombatCalculations.DAMAGE_TYPES.POISON,
        duration = 15, visualEffect = "poison_bubbles"
    },
    [CombatCalculations.STATUS_EFFECTS.STUNNED] = {
        name = "Stunned", icon = "icons/status/stunned.png", color = {1, 1, 0},
        preventsAction = true, duration = 3, visualEffect = "stun_stars"
    },
    [CombatCalculations.STATUS_EFFECTS.WEAKENED] = {
        name = "Weakened", icon = "icons/status/weakened.png", color = {0.5, 0.5, 0.5},
        affectsStat = "attack", statModifier = 0.8, -- 20% reduction (multiplicative)
        duration = 20, visualEffect = "weakness_aura"
    },
    -- Add definitions for BLEEDING, STRENGTHENED, HASTED, PROTECTED, CURSED, BLESSED, etc.
}

--[[----------------------------------------------------------------------------
-- Dependencies (Must be set via init)
------------------------------------------------------------------------------]]
local itemDatabase = nil
local Skills = nil -- Placeholder for Skills module access
local Magic = nil -- Placeholder for Magic module access

-- Initialize with necessary system references
function CombatCalculations.init(systems)
    if systems and systems.itemDatabase then
        itemDatabase = systems.itemDatabase
        print("CombatCalculations initialized with Item Database.")
    else
        print("CombatCalculations Warning: Item Database not provided!")
    end
    if systems and systems.skills then
        Skills = systems.skills -- Store reference if needed for skill data lookup
    end
     if systems and systems.magic then
        Magic = systems.magic -- Store reference if needed for spell data lookup
    end
    -- Add other required systems here
end

-- Helper function to get item by ID (requires itemDatabase)
local function getItemById(itemId)
    if not itemDatabase or not itemId then
        return nil
    end
    return itemDatabase.getItem(itemId) -- Use the correct function from item_database.lua
end

-- Helper function to get skill data (requires Skills module)
local function getSkillData(skillId)
    if not Skills or not skillId then return nil end
    return Skills.load(skillId) -- Use the correct function from skills.lua
end

-- Helper function to get spell data (requires Magic module)
local function getSpellData(spellId)
    if not Magic or not spellId then return nil end
    return Magic.loadSpell(spellId) -- Use the correct function from magic.lua
end


--[[----------------------------------------------------------------------------
-- Stat & Chance Calculation Functions
------------------------------------------------------------------------------]]

-- Calculate effective stats considering base stats, buffs, debuffs, equipment, skills etc.
-- This remains complex and highly dependent on your specific game systems.
function CombatCalculations.calculateEffectiveStats(entity)
    if not entity or not entity.stats then return {} end
    if not entity.baseStats then print("Warning: Entity "..(entity.name or entity.id).." missing baseStats for accurate calculations.") end

    -- Start with base stats or current stats if baseStats missing
    local effectiveStats = deepcopy(entity.baseStats or entity.stats)

    -- Ensure core stats exist
    effectiveStats.attack = effectiveStats.attack or 10
    effectiveStats.defense = effectiveStats.defense or 5
    effectiveStats.mattack = effectiveStats.mattack or 10
    effectiveStats.mdefense = effectiveStats.mdefense or 5
    effectiveStats.speed = effectiveStats.speed or 5
    effectiveStats.luck = effectiveStats.luck or 5
    effectiveStats.dexterity = effectiveStats.dexterity or 10
    effectiveStats.strength = effectiveStats.strength or 10
    effectiveStats.intelligence = effectiveStats.intelligence or 10
    effectiveStats.critChance = effectiveStats.critChance or 0.05
    effectiveStats.critMultiplier = effectiveStats.critMultiplier or 1.5
    effectiveStats.accuracy = effectiveStats.accuracy or 100 -- Represents a base hit chance factor
    effectiveStats.evasion = effectiveStats.evasion or 5 -- Represents evasion factor
    effectiveStats.blockChance = effectiveStats.blockChance or 0
    effectiveStats.blockReduction = effectiveStats.blockReduction or 0.5 -- % damage reduced on block
    effectiveStats.parryChance = effectiveStats.parryChance or 0
    effectiveStats.counterChance = effectiveStats.counterChance or 0
    effectiveStats.statusResistances = effectiveStats.statusResistances or {}
    effectiveStats.resistances = effectiveStats.resistances or {}
    effectiveStats.vulnerabilities = effectiveStats.vulnerabilities or {}


    -- TODO: Apply Equipment Bonuses (more thoroughly)
    if entity.equipment then
        for slot, itemId in pairs(entity.equipment) do
            if itemId then
                local itemData = getItemById(itemId)
                if itemData then
                    -- Additive stats from gear
                    if itemData.stats then
                        for stat, bonus in pairs(itemData.stats) do
                            effectiveStats[stat] = (effectiveStats[stat] or 0) + bonus
                        end
                    end
                    -- Resistances
                    if itemData.resistances then
                       for resType, value in pairs(itemData.resistances) do
                           effectiveStats.resistances[resType] = (effectiveStats.resistances[resType] or 0) + value
                       end
                    end
                    -- Crit/Block/Evasion/Etc.
                    effectiveStats.critChance = (effectiveStats.critChance or 0) + (itemData.critBonus or 0)
                    effectiveStats.blockChance = (effectiveStats.blockChance or 0) + (itemData.blockChance or 0)
                    effectiveStats.evasion = (effectiveStats.evasion or 0) + (itemData.evasionBonus or 0)
                    -- ... etc for other flat bonuses from gear
                end
            end
        end
    end

    -- TODO: Apply Class/Skill Passives
    -- Needs access to class/skill definitions
    -- Example:
    -- if entity.class and entity.class.name == "Warrior" then effectiveStats.attack = effectiveStats.attack * 1.1 end
    -- if entity.skills and entity.skills["precision"] then effectiveStats.accuracy = effectiveStats.accuracy + 10 end

    -- Apply Active Status Effects (Stat Modifiers)
    if entity.status_effects then
        for effectKey, effectInstance in pairs(entity.status_effects) do
            local effectData = CombatCalculations.StatusEffectData[effectKey]
            if effectData and effectData.affectsStat then
                local stat = effectData.affectsStat
                local modifier = effectData.statModifier or 1.0 -- Assume multiplicative if value < 2, additive otherwise? Needs clear definition.
                local potency = effectInstance.potency or 1.0

                if modifier > -1 and modifier < 2 then -- Treat as multiplier (e.g., 0.5 for 50% reduction, 1.2 for 20% increase)
                    effectiveStats[stat] = (effectiveStats[stat] or 0) * (modifier * potency)
                else -- Treat as additive (e.g., -10 for -10 defense)
                    effectiveStats[stat] = (effectiveStats[stat] or 0) + (modifier * potency)
                end
            end
        end
    end

    -- Clamp values if necessary
    effectiveStats.critChance = math.max(0, math.min(1.0, effectiveStats.critChance))
    effectiveStats.blockChance = math.max(0, math.min(1.0, effectiveStats.blockChance))
    -- Clamp resistances? Usually capped (e.g., 80%)

    return effectiveStats
end


-- Calculate hit chance based on attacker/target stats
function CombatCalculations.calculateHitChance(attackerStats, targetStats, skillData)
    local baseHit = 0.95 -- Base 95% chance before modifiers

    -- Use accuracy vs evasion stats if available, otherwise fallback to dex/speed
    local accuracy = attackerStats.accuracy or (attackerStats.dexterity or 10)
    local evasion = targetStats.evasion or (targetStats.speed or 10)

    -- Example Formula: More accuracy increases hit, more evasion decreases hit
    -- Adjust the divisor '50' to control how much stats affect hit chance
    local hitChance = baseHit + (accuracy - evasion) / 50.0

    -- Apply skill modifiers
    if skillData then
        hitChance = hitChance + (skillData.accuracyBonus or 0)
        if skillData.alwaysHits then hitChance = 1.0 end
    end

    -- Clamp between min and max hit chance (e.g., 5% to 100%)
    hitChance = math.max(0.05, math.min(1.0, hitChance))

    return hitChance
end

-- Calculate evasion chance
function CombatCalculations.calculateEvasionChance(targetStats, attackerStats, skillData)
    -- Note: This is often calculated implicitly via hit chance.
    -- But if needed explicitly:
    local baseEvasion = targetStats.evasionChance or 0.05 -- Base 5%

    -- Add stats contribution (e.g., speed/dex)
    baseEvasion = baseEvasion + (targetStats.speed or 0) * 0.005 -- Example: 0.5% per speed

    -- Reduce by attacker accuracy/dex
    baseEvasion = baseEvasion - (attackerStats.accuracy or attackerStats.dexterity or 0) * 0.002

    -- Skill influence (e.g., some attacks harder to evade)
    if skillData and skillData.unavoidable then return 0 end
    if skillData and skillData.evasionPenalty then
        baseEvasion = baseEvasion - skillData.evasionPenalty
    end

     -- Clamp between 0% and, say, 75% max evasion
    return math.max(0, math.min(0.75, baseEvasion))
end

-- Calculate block chance
function CombatCalculations.calculateBlockChance(targetStats, attackerStats, skillData, targetEntity)
    -- Block often requires specific action or equipment (shield)
    if not targetEntity.isBlocking then -- Check if the entity is actively blocking
        return 0 -- Can't block if not blocking (unless passive block exists)
    end

    local baseBlock = targetStats.blockChance or 0.3 -- Base 30% chance while blocking

    -- Add stat contribution (e.g., defense, strength)
    baseBlock = baseBlock + (targetStats.defense or 0) * 0.005

    -- Skill influence
    if skillData and skillData.guardBreak then return 0 end -- Cannot block guard break skills

    -- Clamp between 0% and 90% (or desired max)
    return math.max(0, math.min(0.90, baseBlock))
end

-- Calculate block effectiveness (% damage reduced)
function CombatCalculations.calculateBlockReduction(targetStats, attackerStats, skillData)
     local blockReduction = targetStats.blockReduction or 0.5 -- Base 50% reduction
     -- Add stat contribution (e.g., defense, shield mastery skill)
     blockReduction = blockReduction + (targetStats.defense or 0) * 0.002
     -- TODO: Add bonuses from equipped shield item data
     -- Clamp between, say, 25% and 90% reduction
     return math.max(0.25, math.min(0.90, blockReduction))
end

-- Calculate Parry Chance
function CombatCalculations.calculateParryChance(targetStats, attackerStats, skillData, targetEntity)
    -- Parry might require specific action, skill, or weapon
    -- Example: Requires Parry skill/stance and suitable weapon
    local canParry = targetEntity.isParrying or false -- Check state

    if not canParry then return 0 end

    local baseParry = targetStats.parryChance or 0.05 -- Base 5% chance when attempting

    -- Add stat contribution (e.g., dexterity)
    baseParry = baseParry + (targetStats.dexterity or 0) * 0.003

    -- Skill influence
    if skillData and skillData.unparryable then return 0 end -- Cannot parry specific skills

    -- Clamp between 0% and 50% (or desired max)
    return math.max(0, math.min(0.50, baseParry))
end

-- Calculate critical hit chance and multiplier
function CombatCalculations.calculateCrit(attackerStats, targetStats, skillData)
    local critChance = attackerStats.critChance or 0.05 -- Base 5%
    critChance = critChance + (attackerStats.luck or 0) * 0.005 -- Example: 0.5% per luck

    -- TODO: Add bonuses from skills, equipment, buffs (like Precision)

    local critMultiplier = attackerStats.critMultiplier or 1.5 -- Base 1.5x
    -- TODO: Add bonuses from skills, equipment

    critChance = math.max(0, math.min(1.0, critChance)) -- Clamp chance

    local isCrit = math.random() <= critChance
    return isCrit, critMultiplier
end


-- Calculate damage reduction from defense
function CombatCalculations.applyDefense(damage, damageType, targetStats)
    local defenseStat = 0

    local defenseMap = {
        [CombatCalculations.DAMAGE_TYPES.PHYSICAL] = "pdefense", [CombatCalculations.DAMAGE_TYPES.FIRE] = "pdefense",
        [CombatCalculations.DAMAGE_TYPES.ICE] = "pdefense", [CombatCalculations.DAMAGE_TYPES.LIGHTNING] = "pdefense",
        [CombatCalculations.DAMAGE_TYPES.POISON] = "pdefense",
        [CombatCalculations.DAMAGE_TYPES.MAGICAL] = "mdefense", [CombatCalculations.DAMAGE_TYPES.VOID] = "mdefense",
        [CombatCalculations.DAMAGE_TYPES.HOLY] = "mdefense"
    }
    local defenseStatName = defenseMap[damageType] or "pdefense"
    defenseStat = targetStats[defenseStatName] or 0

    -- Formula: Reduction = Defense / (Defense + K) ; K controls effectiveness
    local K = 100 -- Higher K = defense matters less; Lower K = defense matters more
    local reduction = defenseStat / (defenseStat + K)
    reduction = math.min(0.90, reduction) -- Cap at 90% reduction

    local damageAfterDefense = damage * (1 - reduction)
    return math.max(0, damageAfterDefense) -- Can't heal via defense
end

-- Calculate damage modification from resistances/vulnerabilities
function CombatCalculations.applyResistance(damage, damageType, targetStats)
    local resistances = targetStats.resistances or {}
    local vulnerabilities = targetStats.vulnerabilities or {}

    local resistance = resistances[damageType] or 0 -- Value between 0 and 1 (e.g., 0.2 for 20%)
    local vulnerability = vulnerabilities[damageType] or 0 -- Value >= 0 (e.g., 0.5 for 50% more damage)

    -- Ensure resistance doesn't exceed 100% (or desired cap)
    resistance = math.min(1.0, resistance)

    local multiplier = (1.0 - resistance) * (1.0 + vulnerability)
    return math.max(0, damage * multiplier)
end

-- Calculate if a status effect is applied, and its properties
function CombatCalculations.calculateStatusEffectApplication(attackerStats, targetStats, effectKey, baseChance, baseDuration, basePotency)
    if not effectKey then return false, 0, 0 end

    local effectTemplate = CombatCalculations.StatusEffectData[effectKey]
    if not effectTemplate then return false, 0, 0 end -- Invalid effect key

    baseChance = baseChance or (effectTemplate.applyChance or 0)
    baseDuration = baseDuration or (effectTemplate.duration or 10)
    basePotency = basePotency or (effectTemplate.potency or 1.0)

    -- Calculate resistance/potency influence
    -- TODO: Fetch relevant resistance stat for this effect type from targetStats
    local resistanceValue = targetStats.statusResistances and targetStats.statusResistances[effectKey] or 0
    -- TODO: Fetch relevant potency stat from attackerStats
    local potencyValue = attackerStats.statusPotency or 0

    -- Example formula: modify chance based on stats
    local effectiveChance = baseChance * (1 + potencyValue * 0.01) * (1 - resistanceValue * 0.01)
    effectiveChance = math.max(0, math.min(1.0, effectiveChance)) -- Clamp 0-100%

    -- Example formula: modify duration based on stats
    local effectiveDuration = baseDuration * (1 + potencyValue * 0.005) * (1 - resistanceValue * 0.005)
    effectiveDuration = math.max(0.5, effectiveDuration) -- Minimum duration

    -- Example formula: modify potency based on stats
    local effectivePotency = basePotency * (1 + potencyValue * 0.01) * (1 - resistanceValue * 0.01)

    local apply = math.random() <= effectiveChance

    return apply, effectiveDuration, effectivePotency
end

return CombatCalculations