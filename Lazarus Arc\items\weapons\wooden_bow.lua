local WoodenBow = {
    id = "wooden_bow",
    name = "Wooden Bow",
    type = "weapon",
    category = "bow",
    
    -- Properties
    properties = {
        damage = 18,
        attackSpeed = 1.0,
        range = 6.0,
        durability = 150,
        maxDurability = 150,
        arrowSpeed = 12,
        arrowSpread = 0.2,
        drawTime = 0.5,
        accuracy = 0.9,
        weight = 0.8,
        flexibility = 1.0
    },
    
    -- Appearance
    appearance = {
        sprite = "wooden_bow",
        scale = 1.0,
        animations = {
            "idle",
            "draw",
            "shoot",
            "aim"
        },
        variants = {
            "oak",
            "birch",
            "maple",
            "pine"
        },
        blendMode = "normal",
        tint = {0.6, 0.4, 0.2},
        alpha = 1.0
    },
    
    -- Sound effects
    sounds = {
        draw = "wooden_bow_draw",
        shoot = "wooden_bow_shoot",
        arrow = "wooden_bow_arrow",
        string = "wooden_bow_string"
    },
    
    -- Effects
    effects = {
        arrow = {
            type = "projectile",
            duration = 0.5,
            effects = {
                damage = 18,
                piercing = true
            }
        }
    }
}

-- Initialize the weapon
function WoodenBow.init(weapon, world)
    -- Copy all fields from WoodenBow template to weapon instance
    for k, v in pairs(WoodenBow) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        drawProgress = 0,
        isDrawing = false
    }

    return weapon
end

-- Update the weapon
function WoodenBow.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Update draw progress
        if weapon.properties.weaponState.isDrawing then
            weapon.properties.weaponState.drawProgress = math.min(1.0,
                weapon.properties.weaponState.drawProgress + dt / weapon.properties.drawTime)
        else
            weapon.properties.weaponState.drawProgress = math.max(0,
                weapon.properties.weaponState.drawProgress - dt * 2)
        end
    end
end

-- Handle attack
function WoodenBow.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Calculate arrow direction
    local angle = math.atan2(
        target.position.y - attacker.position.y,
        target.position.x - attacker.position.x
    )
    
    -- Add spread to arrow direction based on draw progress
    local spread = (math.random() - 0.5) * weapon.properties.arrowSpread * 
        (1 - weapon.properties.weaponState.drawProgress)
    angle = angle + spread
    
    -- Create arrow projectile
    if world.createProjectile then
        world.createProjectile({
            type = "arrow",
            position = attacker.position,
            direction = {
                x = math.cos(angle) * weapon.properties.arrowSpeed,
                y = math.sin(angle) * weapon.properties.arrowSpeed
            },
            damage = weapon.properties.damage * weapon.properties.weaponState.drawProgress,
            range = weapon.properties.range
        })
    end
    
    -- Create arrow effect
    if world.createEffect then
        world.createEffect({
            type = "arrow",
            position = attacker.position,
            angle = angle,
            range = weapon.properties.range,
            duration = 0.5
        })
    end
    
    -- Play shoot sound
    if world.playSound then
        world.playSound(weapon.sounds.shoot)
    end
    
    -- Reset draw state
    weapon.properties.weaponState.isDrawing = false
    weapon.properties.weaponState.drawProgress = 0
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function WoodenBow.specialAttack(weapon, world, attacker, target)
    -- Toggle drawing state
    weapon.properties.weaponState.isDrawing = not weapon.properties.weaponState.isDrawing
    
    -- Play appropriate sound
    if world.playSound then
        if weapon.properties.weaponState.isDrawing then
            world.playSound(weapon.sounds.draw)
        else
            world.playSound(weapon.sounds.string)
        end
    end
    
    return true
end

return WoodenBow 