-- biomes/crystal_cavern.lua
local CrystalCavernBiome = {
    id = "crystal_cavern",
    name = "Crystal Cavern",
    description = "A subterranean biome filled with crystalline formations and unique cave life.",

    -- Environmental factors
    environment = {
        humidity = 0.7,    -- Caves are often damp
        temperature = 0.4, -- Stable, cool temperature
        sunlight = 0.0,    -- No direct sunlight
        windStrength = 0.1 -- Minimal air movement
    },

    -- Core tile types used in this biome
    primaryTiles = {"crystal_formation", "cave_floor"},
    secondaryTiles = {"water", "ancient_technology"},
    rareTiles = {"lava", "void"},

    -- Default proportions
    tileDistribution = {
        crystal_formation = 0.4,
        cave_floor = 0.4,
        water = 0.1,
        ancient_technology = 0.07,
        lava = 0.02,
        void = 0.01
    },

    -- Entities common to this biome
    commonEntities = {
        "crystal_lizard", "cave_bat", "luminous_moss", "mana_sprite"
    },

    uncommonEntities = {
        "crystal_golem", "miner", "energy_elemental", "cave_dweller"
    },

    rareEntities = {
        "crystal_dragon", "void_walker", "ancient_sentinel"
    },

    -- Environmental properties specific to cave biomes
    isUnderground = true,
    naturalLight = 0.2,
    caveAmbience = true,
    echoEffect = true,
    hasBreathableAir = true,
    ceilingHeight = {min = 3, max = 15},

    -- Biome variants for diverse generation
    variants = {
        mana = {
            name = "Mana Crystal Cavern",
            crystalSize = 1.2,
            energyLevel = 2.0,
            tileAdjustments = {
                crystal_formation = 0.6,
                cave_floor = 0.3,
                mana_pool = 0.05
            },
            entityAdjustments = {
                mana_sprite = 2.0,
                energy_elemental = 1.5
            },
            specialEffects = {
                "mana_particles",
                "crystal_resonance",
                "ambient_energy"
            }
        },
        dark = {
            name = "Dark Crystal Cavern",
            crystalSize = 0.8,
            energyLevel = -1.0,
            tileAdjustments = {
                crystal_formation = 0.3,
                cave_floor = 0.5,
                void = 0.1
            },
            entityAdjustments = {
                cave_bat = 1.5,
                void_walker = 1.2
            },
            specialEffects = {
                "darkness_mist",
                "void_whispers"
            }
        },
        flooded = {
            name = "Flooded Crystal Cavern",
            crystalSize = 1.0,
            energyLevel = 1.0,
            tileAdjustments = {
                crystal_formation = 0.3,
                cave_floor = 0.2,
                water = 0.45
            },
            entityAdjustments = {
                water_elemental = 1.5,
                crystal_lizard = 0.5
            },
            specialEffects = {
                "water_reflections",
                "dripping_sounds"
            }
        },
        ancient = {
            name = "Ancient Crystal Technology Cavern",
            crystalSize = 1.1,
            energyLevel = 1.5,
            tileAdjustments = {
                crystal_formation = 0.4,
                cave_floor = 0.3,
                ancient_technology = 0.25
            },
            entityAdjustments = {
                ancient_sentinel = 2.0,
                tech_drone = 1.5
            },
            specialEffects = {
                "tech_pulses",
                "energy_flows",
                "holographic_projections"
            }
        },
        lava = {
            name = "Magma Crystal Cavern",
            crystalSize = 1.3,
            energyLevel = 1.8,
            tileAdjustments = {
                crystal_formation = 0.3,
                cave_floor = 0.4,
                lava = 0.2
            },
            entityAdjustments = {
                fire_elemental = 1.8,
                lava_crawler = 1.5
            },
            specialEffects = {
                "heat_waves",
                "ember_particles",
                "lava_bubbling"
            }
        }
    },

    -- Structures that can generate in this biome
    structures = {
        {
            name = "crystal_altar",
            chance = 0.15,
            entities = {"crystal_priest", "energy_wisp"}
        },
        {
            name = "ancient_laboratory",
            chance = 0.08,
            requiresVariant = "ancient",
            entities = {"robot_guardian", "forgotten_scientist"}
        },
        {
            name = "crystal_garden",
            chance = 0.1,
            entities = {"crystal_tender", "living_crystal"}
        },
        {
            name = "void_portal",
            chance = 0.05,
            requiresVariant = "dark",
            entities = {"void_guardian", "dark_crystal"}
        },
        {
            name = "mana_well",
            chance = 0.12,
            requiresVariant = "mana",
            entities = {"mana_elemental", "arcane_researcher"}
        }
    },

    -- Underground biomes don't have weather, but can have environmental effects
    environmentalEffects = {
        crystal_resonance = 0.3,
        energy_surges = 0.2,
        cave_ins = 0.05,
        mana_fluctuations = 0.15,
        crystal_growth = 0.1
    },

    -- Unique features
    features = {
        {
            name = "giant_crystal",
            chance = 0.2,
            unique = true
        },
        {
            name = "mana_pool",
            chance = 0.1,
            requiresVariant = "mana"
        },
        {
            name = "void_tear",
            chance = 0.05,
            requiresVariant = "dark",
            danger = true
        },
        {
            name = "crystal_geode",
            chance = 0.15
        },
        {
            name = "ancient_mechanism",
            chance = 0.08,
            requiresVariant = "ancient"
        },
        {
            name = "magma_crystal_vein",
            chance = 0.12,
            requiresVariant = "lava"
        }
    }
}

return CrystalCavernBiome