-- items/industrial.lua
-- Industrial items and components used in various structures

local IndustrialItems = {
    -- Basic Materials
    scrap_metal = {
        id = "scrap_metal",
        name = "Scrap Metal",
        description = "A piece of discarded metal, potentially useful for crafting or recycling.",
        type = "material",
        rarity = 1,
        value = 5,
        weight = 2,
        stackable = true,
        maxStack = 50,
        tags = {"metal", "scrap", "crafting_material"}
    },
    
    scrap_metal_low = {
        id = "scrap_metal_low",
        name = "Low Grade Scrap Metal",
        description = "Poor quality scrap metal, barely worth the effort to collect.",
        type = "material",
        rarity = 1,
        value = 2,
        weight = 2,
        stackable = true,
        maxStack = 50,
        tags = {"metal", "scrap", "crafting_material", "low_quality"}
    },
    
    scrap_metal_medium = {
        id = "scrap_metal_medium",
        name = "Medium Grade Scrap Metal",
        description = "Decent quality scrap metal, useful for basic crafting.",
        type = "material",
        rarity = 2,
        value = 8,
        weight = 2,
        stackable = true,
        maxStack = 50,
        tags = {"metal", "scrap", "crafting_material", "medium_quality"}
    },
    
    scrap_metal_high_grade = {
        id = "scrap_metal_high_grade",
        name = "High Grade Scrap Metal",
        description = "High quality scrap metal, valuable for advanced crafting.",
        type = "material",
        rarity = 3,
        value = 20,
        weight = 2,
        stackable = true,
        maxStack = 50,
        tags = {"metal", "scrap", "crafting_material", "high_quality"}
    },
    
    scrap_metal_clean = {
        id = "scrap_metal_clean",
        name = "Clean Scrap Metal",
        description = "Clean, processed scrap metal, ready for use.",
        type = "material",
        rarity = 2,
        value = 10,
        weight = 2,
        stackable = true,
        maxStack = 50,
        tags = {"metal", "scrap", "crafting_material", "clean"}
    },

    -- Basic Components
    broken_component = {
        id = "broken_component",
        name = "Broken Component",
        description = "A damaged or malfunctioning component, potentially repairable.",
        type = "component",
        rarity = 1,
        value = 3,
        weight = 1,
        stackable = true,
        maxStack = 20,
        tags = {"component", "broken", "repairable"}
    },
    
    working_component_basic = {
        id = "working_component_basic",
        name = "Basic Working Component",
        description = "A simple, functional component used in basic machinery.",
        type = "component",
        rarity = 2,
        value = 15,
        weight = 1,
        stackable = true,
        maxStack = 20,
        tags = {"component", "working", "basic"}
    },
    
    working_component_advanced = {
        id = "working_component_advanced",
        name = "Advanced Working Component",
        description = "A sophisticated component used in advanced machinery.",
        type = "component",
        rarity = 3,
        value = 40,
        weight = 1,
        stackable = true,
        maxStack = 20,
        tags = {"component", "working", "advanced"}
    },
    
    generic_component = {
        id = "generic_component",
        name = "Generic Component",
        description = "A standard component used in various machines and devices.",
        type = "component",
        rarity = 2,
        value = 12,
        weight = 1,
        stackable = true,
        maxStack = 20,
        tags = {"component", "generic"}
    },
    
    advanced_component = {
        id = "advanced_component",
        name = "Advanced Component",
        description = "A high-tech component used in sophisticated systems.",
        type = "component",
        rarity = 3,
        value = 35,
        weight = 1,
        stackable = true,
        maxStack = 20,
        tags = {"component", "advanced"}
    },
    
    rare_component = {
        id = "rare_component",
        name = "Rare Component",
        description = "A valuable and hard-to-find component.",
        type = "component",
        rarity = 4,
        value = 60,
        weight = 1,
        stackable = true,
        maxStack = 10,
        tags = {"component", "rare"}
    },

    -- Power Components
    power_cell_drained = {
        id = "power_cell_drained",
        name = "Drained Power Cell",
        description = "A completely depleted power cell, potentially rechargeable.",
        type = "power",
        rarity = 1,
        value = 5,
        weight = 2,
        stackable = true,
        maxStack = 10,
        tags = {"power", "drained", "rechargeable"}
    },
    
    power_cell_partial = {
        id = "power_cell_partial",
        name = "Partially Charged Power Cell",
        description = "A power cell with some remaining charge.",
        type = "power",
        rarity = 2,
        value = 15,
        weight = 2,
        stackable = true,
        maxStack = 10,
        tags = {"power", "partial", "rechargeable"}
    },
    
    power_cell_charged = {
        id = "power_cell_charged",
        name = "Fully Charged Power Cell",
        description = "A fully charged power cell, ready for use.",
        type = "power",
        rarity = 3,
        value = 30,
        weight = 2,
        stackable = true,
        maxStack = 10,
        tags = {"power", "charged"}
    },

    -- Tools
    maintenance_tool = {
        id = "maintenance_tool",
        name = "Maintenance Tool",
        description = "A basic tool used for maintenance and repairs.",
        type = "tool",
        rarity = 2,
        value = 20,
        weight = 3,
        stackable = false,
        tags = {"tool", "maintenance"}
    },
    
    tools_basic = {
        id = "tools_basic",
        name = "Basic Tool Set",
        description = "A set of basic tools for simple repairs and maintenance.",
        type = "tool",
        rarity = 2,
        value = 25,
        weight = 5,
        stackable = false,
        tags = {"tool", "basic"}
    },
    
    advanced_toolkit = {
        id = "advanced_toolkit",
        name = "Advanced Toolkit",
        description = "A comprehensive set of tools for complex repairs and maintenance.",
        type = "tool",
        rarity = 3,
        value = 50,
        weight = 8,
        stackable = false,
        tags = {"tool", "advanced"}
    },

    -- Raw Materials
    raw_metal = {
        id = "raw_metal",
        name = "Raw Metal",
        description = "Unprocessed metal ore or ingots.",
        type = "material",
        rarity = 2,
        value = 10,
        weight = 3,
        stackable = true,
        maxStack = 40,
        tags = {"metal", "raw", "crafting_material"}
    },
    
    plastic_pellets = {
        id = "plastic_pellets",
        name = "Plastic Pellets",
        description = "Raw plastic material in pellet form.",
        type = "material",
        rarity = 2,
        value = 8,
        weight = 1,
        stackable = true,
        maxStack = 50,
        tags = {"plastic", "raw", "crafting_material"}
    }
}

return IndustrialItems 