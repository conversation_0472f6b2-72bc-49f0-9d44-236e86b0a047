-- viewport_management.lua
-- Handles the display of game world through one or more viewports
-- Updated to support isometric projection mode

local ViewportManager = {}

-- Create a new viewport manager
function ViewportManager.new(screenWidth, screenHeight)
    local self = {
        screenWidth = screenWidth or 1280,
        screenHeight = screenHeight or 720,
        activeViewports = {},
        players = {},
        defaultViewportSize = {width = 1.0, height = 1.0},  -- Full screen by default
        splitScreenEnabled = false,
        cameraSmoothing = 0.1,  -- How quickly the camera follows the player (0-1)
        zoomLevel = 1.0,
        minZoom = 0.5,
        maxZoom = 2.0,
        borders = true,  -- Show borders between viewports
        borderColor = {0.1, 0.1, 0.1, 1.0},  -- Dark gray borders
        borderWidth = 2,
        renderDistance = 15,  -- How many tiles to render around player
        -- New field to determine projection mode: "topdown" or "isometric"
        projection = "isometric",

        -- Menu view settings
        menuView = {
            active = false,
            position = {x = 0, y = 0},
            targetPosition = {x = 0, y = 0},
            rotation = 0,
            zoom = 1.2,
            orbitRadius = 10,
            orbitSpeed = 0.05,
            orbitTime = 0
        }
    }

    -- Add a player to be tracked with a viewport
    function self:addPlayer(playerInfo)
        -- Ensure player has required fields
        playerInfo.viewport = playerInfo.viewport or {}

        -- Initialize viewport position based on player data
        local initialX, initialY = 0, 0

        if playerInfo.position then
            -- Use position component if available
            initialX = playerInfo.position.x or 0
            initialY = playerInfo.position.y or 0
        elseif playerInfo.x ~= nil and playerInfo.y ~= nil then
            -- Use direct x,y if available
            initialX = playerInfo.x
            initialY = playerInfo.y
        end

        -- Initialize viewport coordinates
        playerInfo.viewport.x = initialX
        playerInfo.viewport.y = initialY
        playerInfo.viewport.targetX = initialX
        playerInfo.viewport.targetY = initialY
        playerInfo.viewport.width = 1.0
        playerInfo.viewport.height = 1.0

        -- Apply menu view settings for consistency if this is a gameplay player
        if not playerInfo.isMenuCamera and self.lastMenuPosition then
            -- Use the same camera settings as the menu for gameplay
            playerInfo.viewport.x = self.lastMenuPosition.x
            playerInfo.viewport.y = self.lastMenuPosition.y
            playerInfo.viewport.targetX = self.lastMenuPosition.x
            playerInfo.viewport.targetY = self.lastMenuPosition.y

            -- Apply the same zoom level
            self:setZoom(self.gameplayZoom or self.menuView.zoom)
        end

        table.insert(self.players, playerInfo)

        -- Recalculate viewport layout
        self:updateViewportLayout()

        return #self.players
    end

    -- Remove a player and their viewport
    function self:removePlayer(playerId)
        -- Find the player in the players table
        local playerIndex = nil
        for i, player in ipairs(self.players) do
            if player.id == playerId then
                playerIndex = i
                break
            end
        end

        -- If found, remove by index
        if playerIndex then
            table.remove(self.players, playerIndex)
        else
            -- If not found by ID, try using playerId as an index directly
            -- (for backward compatibility)
            if type(playerId) == "number" and playerId >= 1 and playerId <= #self.players then
                table.remove(self.players, playerId)
            else
                print("Warning: Could not find player to remove from viewport manager")
            end
        end

        self:updateViewportLayout()
    end

    -- Update the layout of viewports based on number of players
    function self:updateViewportLayout()
        self.activeViewports = {}
        local playerCount = #self.players

        if playerCount == 0 then
            return
        end

        if playerCount == 1 or not self.splitScreenEnabled then
            table.insert(self.activeViewports, {
                x = 0,
                y = 0,
                width = 1.0,
                height = 1.0,
                playerId = 1
            })
            self.players[1].viewport.width = 1.0
            self.players[1].viewport.height = 1.0
        elseif playerCount == 2 then
            table.insert(self.activeViewports, {
                x = 0,
                y = 0,
                width = 1.0,
                height = 0.5,
                playerId = 1
            })
            table.insert(self.activeViewports, {
                x = 0,
                y = 0.5,
                width = 1.0,
                height = 0.5,
                playerId = 2
            })
            self.players[1].viewport.width = 1.0
            self.players[1].viewport.height = 0.5
            self.players[2].viewport.width = 1.0
            self.players[2].viewport.height = 0.5
        elseif playerCount == 3 then
            table.insert(self.activeViewports, {
                x = 0,
                y = 0,
                width = 1.0,
                height = 0.5,
                playerId = 1
            })
            table.insert(self.activeViewports, {
                x = 0,
                y = 0.5,
                width = 0.5,
                height = 0.5,
                playerId = 2
            })
            table.insert(self.activeViewports, {
                x = 0.5,
                y = 0.5,
                width = 0.5,
                height = 0.5,
                playerId = 3
            })
            self.players[1].viewport.width = 1.0
            self.players[1].viewport.height = 0.5
            self.players[2].viewport.width = 0.5
            self.players[2].viewport.height = 0.5
            self.players[3].viewport.width = 0.5
            self.players[3].viewport.height = 0.5
        elseif playerCount == 4 then
            table.insert(self.activeViewports, {
                x = 0,
                y = 0,
                width = 0.5,
                height = 0.5,
                playerId = 1
            })
            table.insert(self.activeViewports, {
                x = 0.5,
                y = 0,
                width = 0.5,
                height = 0.5,
                playerId = 2
            })
            table.insert(self.activeViewports, {
                x = 0,
                y = 0.5,
                width = 0.5,
                height = 0.5,
                playerId = 3
            })
            table.insert(self.activeViewports, {
                x = 0.5,
                y = 0.5,
                width = 0.5,
                height = 0.5,
                playerId = 4
            })
            for i = 1, 4 do
                self.players[i].viewport.width = 0.5
                self.players[i].viewport.height = 0.5
            end
        else
            print("Warning: More than 4 players. Only showing viewports for the first 4.")
            self:updateViewportLayout(4)
        end
    end

    -- Toggle split screen mode
    function self:toggleSplitScreen(enabled)
        self.splitScreenEnabled = enabled
        self:updateViewportLayout()
    end

    -- Update viewport positions to follow players
    function self:update(dt)
        -- Check if we're in isometric mode
        local isIsometric = false
        if Engine and Engine.systems and Engine.systems.colorUtils then
            isIsometric = Engine.systems.colorUtils.isometricDebug
            -- Sync our projection mode with the global isometric setting
            self.projection = isIsometric and "isometric" or "topdown"
        end

        -- Get current state to determine camera behavior
        local currentState = "gameplay"
        if Engine and Engine.stateManager and Engine.stateManager.getCurrentState then
            currentState = Engine.stateManager:getCurrentState()
        end

        -- In gameplay state, we want to allow player movement AND update the camera
        local isGameplayState = (currentState == "gameplay")

        -- Update menu view if active
        if self.menuView.active then
            self:updateMenuView(dt)
        end

        -- Check if we need to update the player list from Engine.player
        if isGameplayState and Engine and Engine.player and Engine.player.position then
            -- Check if we already have this player in our list
            local playerFound = false
            for i, player in ipairs(self.players) do
                if player.id == Engine.player.id then
                    playerFound = true
                    -- Update the player's position in our list
                    player.position = Engine.player.position
                    break
                end
            end

            -- If player not found, add them
            if not playerFound then
                self:addPlayer({
                    id = Engine.player.id,
                    position = Engine.player.position,
                    character = Engine.player.character
                })
                print("Added Engine.player to viewport manager")
            end
        end

        for i, player in ipairs(self.players) do
            -- Check if player has position properties
            if player.position then
                -- Use position component if available
                player.viewport.targetX = player.position.x or 0
                player.viewport.targetY = player.position.y or 0
            elseif player.x ~= nil and player.y ~= nil then
                -- Use direct x,y if available
                player.viewport.targetX = player.x
                player.viewport.targetY = player.y
            else
                -- Default to 0,0 if no position is available
                player.viewport.targetX = 0
                player.viewport.targetY = 0
            end

            -- Initialize viewport position if needed
            if not player.viewport.x then player.viewport.x = player.viewport.targetX end
            if not player.viewport.y then player.viewport.y = player.viewport.targetY end

            -- Apply camera smoothing with different values for isometric view
            local smoothingFactor = self.cameraSmoothing
            if isIsometric then
                -- Use higher smoothing value for isometric view for more responsive camera
                smoothingFactor = 0.3  -- More responsive in isometric mode
            end

            -- For menu view, use a slower camera movement for cinematic effect
            if player.isMenuCamera then
                -- Use a slower, more cinematic camera movement in menu
                smoothingFactor = 0.05
            end

            player.viewport.x = player.viewport.x + (player.viewport.targetX - player.viewport.x) * smoothingFactor
            player.viewport.y = player.viewport.y + (player.viewport.targetY - player.viewport.y) * smoothingFactor
        end
    end

    -- Set zoom level for all viewports
    function self:setZoom(zoomLevel)
        self.zoomLevel = math.max(self.minZoom, math.min(self.maxZoom, zoomLevel))
    end

    -- Get a specific viewport's bounds in screen coordinates
    function self:getViewportBounds(viewportIndex)
        local viewport = self.activeViewports[viewportIndex]
        if not viewport then return nil end
        return {
            x = viewport.x * self.screenWidth,
            y = viewport.y * self.screenHeight,
            width = viewport.width * self.screenWidth,
            height = viewport.height * self.screenHeight
        }
    end

    -- Convert world coordinates to screen coordinates for a specific viewport
    function self:worldToScreen(worldX, worldY, viewportIndex)
        local viewport = self.activeViewports[viewportIndex]
        local player = self.players[viewport.playerId]
        if not viewport or not player then
            return nil
        end

        local screenX, screenY
        if self.projection == "isometric" then
            -- Define tile dimensions for isometric conversion
            local tileWidth = 32 * self.zoomLevel
            local tileHeight = 16 * self.zoomLevel  -- Typically, tileHeight is about half of tileWidth in isometric views
            -- Convert world (grid) coordinates to isometric coordinates
            local isoX = (worldX - worldY) * (tileWidth / 2)
            local isoY = (worldX + worldY) * (tileHeight / 2)

            -- Convert player's world position to isometric as well
            local playerIsoX = (player.viewport.x - player.viewport.y) * (tileWidth / 2)
            local playerIsoY = (player.viewport.x + player.viewport.y) * (tileHeight / 2)

            -- Calculate final screen position: offset by camera and center the viewport
            screenX = isoX - playerIsoX + viewport.x * self.screenWidth + (viewport.width * self.screenWidth / 2)
            screenY = isoY - playerIsoY + viewport.y * self.screenHeight + (viewport.height * self.screenHeight / 2)
        else
            local tileSize = 32 * self.zoomLevel
            screenX = (worldX - player.viewport.x) * tileSize + viewport.x * self.screenWidth + (viewport.width * self.screenWidth / 2)
            screenY = (worldY - player.viewport.y) * tileSize + viewport.y * self.screenHeight + (viewport.height * self.screenHeight / 2)
        end

        return screenX, screenY
    end

    -- Check if a world position is visible in a specific viewport
    function self:isVisibleInViewport(worldX, worldY, viewportIndex)
        local viewport = self.activeViewports[viewportIndex]
        local player = self.players[viewport.playerId]
        if not viewport or not player then return false end

        local renderDist = self.renderDistance
        local minX = player.viewport.x - renderDist
        local maxX = player.viewport.x + renderDist
        local minY = player.viewport.y - renderDist
        local maxY = player.viewport.y + renderDist

        return worldX >= minX and worldX <= maxX and worldY >= minY and worldY <= maxY
    end

    -- Set up menu view (for main menu background)
    function self:setMenuView(position)
        -- Create a dummy player for the menu view if none exists
        if #self.players == 0 then
            self:addPlayer({
                id = "menu_camera",
                position = position or {x = 0, y = 0},
                isMenuCamera = true
            })
        end

        -- Set menu view as active
        self.menuView.active = true
        self.menuView.position = position or {x = 0, y = 0}
        self.menuView.targetPosition = {x = position.x, y = position.y}
        self.menuView.orbitTime = 0

        -- Set zoom level for menu view
        self:setZoom(self.menuView.zoom)

        -- Update the first player's viewport to match menu position
        if self.players[1] then
            self.players[1].viewport.x = position.x
            self.players[1].viewport.y = position.y
            self.players[1].viewport.targetX = position.x
            self.players[1].viewport.targetY = position.y
            self.players[1].isMenuCamera = true
        end
    end

    -- Update menu view (slow orbit around hub)
    function self:updateMenuView(dt)
        if not self.menuView.active then return end

        -- Update orbit time
        self.menuView.orbitTime = self.menuView.orbitTime + dt * self.menuView.orbitSpeed

        -- Calculate new position in orbit
        local orbitX = math.cos(self.menuView.orbitTime) * self.menuView.orbitRadius
        local orbitY = math.sin(self.menuView.orbitTime) * self.menuView.orbitRadius

        -- Update target position
        self.menuView.targetPosition = {
            x = self.menuView.position.x + orbitX,
            y = self.menuView.position.y + orbitY
        }

        -- Update the first player's viewport to match menu position
        if self.players[1] and self.players[1].isMenuCamera then
            self.players[1].viewport.targetX = self.menuView.targetPosition.x
            self.players[1].viewport.targetY = self.menuView.targetPosition.y
        end
    end

    -- Disable menu view (when entering gameplay)
    function self:disableMenuView()
        -- Store menu view settings for gameplay consistency
        local menuZoom = self.menuView.zoom
        local menuPosition = {
            x = self.menuView.position.x,
            y = self.menuView.position.y
        }

        self.menuView.active = false

        -- Remove menu camera if it exists
        for i, player in ipairs(self.players) do
            if player.isMenuCamera then
                table.remove(self.players, i)
                break
            end
        end

        -- Use menu zoom instead of default zoom for visual consistency
        self:setZoom(self.gameplayZoom or menuZoom)

        -- Update viewport layout
        self:updateViewportLayout()

        -- Store menu settings for later use
        self.lastMenuPosition = menuPosition
    end

    -- Render function (placeholder - would integrate with your rendering system)
    function self:render()
        if not self.activeViewports then return end

        for i, viewport in ipairs(self.activeViewports) do
            local player = self.players[viewport.playerId]
            if not player then goto continue end

            -- Here you would set the scissor and clear the viewport area
            -- For example, using love.graphics.setScissor(...)

            -- In this placeholder, we simply call worldToScreen for demonstration
            -- and optionally draw viewport borders
            if self.borders and #self.activeViewports > 1 then
                -- Example (using LÖVE):
                love.graphics.setColor(self.borderColor)
                love.graphics.rectangle("line",
                    viewport.x * self.screenWidth,
                    viewport.y * self.screenHeight,
                    viewport.width * self.screenWidth,
                    viewport.height * self.screenHeight,
                    self.borderWidth
                )
            end

            ::continue::
        end
    end

    return self
end

return ViewportManager
