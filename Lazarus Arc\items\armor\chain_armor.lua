-- items/armor/chain_armor.lua

local Armor = {
    id = "chain_armor",
    name = "Chain Armor",
    description = "Armor made of interlocking metal rings, offering good protection against slashing attacks.",
    type = "armor",
    category = "armor",
    stackable = false,

    stats = {
        defense = 15.0,
        durability = 120.0,
        repair_cost = 35,
        weight = 15.0,
    },

    material = "iron",

    sprite = "res://Sprite/items/armor/chain_armor.png",
    size = { width = 64, height = 64 },

    effects = {
        slash_resistance = 0.15,
        pierce_resistance = 0.05
    },

    meta = {
        rarity = "uncommon"
    },

    take_damage = function(self, amount)
        self.stats.durability = math.max(0, self.stats.durability - amount)
        if self.stats.durability == 0 then
            print(self.name .. " is broken! Penalties applied.")
            -- Potential penalties could be applied here
        end
    end,

    repair = function(self)
        local repair_amount = 120 - self.stats.durability
        self.stats.durability = 120
        print(self.name .. " repaired by " .. repair_amount .. " points.")
    end
}

return Armor
