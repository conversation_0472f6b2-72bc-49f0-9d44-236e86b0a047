-- tiles/crystal_formation.lua
local CrystalFormationTile = {
    id = "crystal_formation",
    name = "Crystal Formation",
    passable = true,
    variants = 5, -- Different crystal types and patterns
    
    -- Entities that can spawn near crystals
    spawns = {"crystal_lizard", "mana_sprite", "energy_wisp", "crystal_golem"},
    
    -- Crystal-specific properties
    crystalType = nil, -- Set during init
    energyLevel = 0, -- Set during init
    resonance = 0, -- How strongly it reacts to magic
    harvestable = true, -- Can be harvested for crystal shards
    
    -- Movement properties
    movementSpeed = 0.9, -- Slightly slower around crystal formations
    
    -- Interaction function
    interact = function(tile, entity)
        -- Check if crystal can be harvested
        if tile.harvestable then
            -- Check if player has mining tools
            if entity.hasItem and entity.hasItem("pickaxe") then
                tile.harvestable = false -- Can only harvest once
                
                local crystalAmount = math.random(1, 3)
                local crystalType = tile.crystalType or "energy"
                
                return {
                    success = true,
                    message = "You carefully extract " .. crystalAmount .. " " .. crystalType .. " crystal shards.",
                    effects = {
                        {type = "add_item", item = crystalType .. "_crystal", quantity = crystalAmount}
                    }
                }
            else
                return {
                    success = false,
                    message = "The crystal formation could be harvested with the right tools."
                }
            end
        end
        
        -- Crystal energy effects
        if tile.energyLevel > 0 then
            -- Different effects based on crystal type
            if tile.crystalType == "mana" then
                if entity.stats and entity.stats.mana then
                    local manaGain = math.floor(tile.energyLevel * 10)
                    entity.stats.mana = math.min(entity.stats.mana + manaGain, entity.stats.maxMana)
                    
                    -- Reduce crystal energy
                    tile.energyLevel = tile.energyLevel - 0.5
                    if tile.energyLevel < 0 then tile.energyLevel = 0 end
                    
                    return {
                        success = true,
                        message = "The mana crystal infuses you with magical energy!",
                        effects = {
                            {type = "restore_mana", amount = manaGain}
                        }
                    }
                end
            elseif tile.crystalType == "life" then
                if entity.stats and entity.stats.health then
                    local healthGain = math.floor(tile.energyLevel * 5)
                    entity.stats.health = math.min(entity.stats.health + healthGain, entity.stats.maxHealth)
                    
                    -- Reduce crystal energy
                    tile.energyLevel = tile.energyLevel - 0.5
                    if tile.energyLevel < 0 then tile.energyLevel = 0 end
                    
                    return {
                        success = true,
                        message = "The life crystal restores your vitality!",
                        effects = {
                            {type = "restore_health", amount = healthGain}
                        }
                    }
                end
            elseif tile.crystalType == "void" then
                -- Void crystals provide knowledge but at a cost
                tile.energyLevel = tile.energyLevel - 0.3
                if tile.energyLevel < 0 then tile.energyLevel = 0 end
                
                return {
                    success = true,
                    message = "The void crystal whispers forbidden knowledge to your mind.",
                    effects = {
                        {type = "gain_knowledge", amount = 1},
                        {type = "damage", amount = 5, type = "mental"}
                    }
                }
            elseif tile.crystalType == "time" then
                -- Time crystals show visions
                tile.energyLevel = tile.energyLevel - 0.2
                if tile.energyLevel < 0 then tile.energyLevel = 0 end
                
                return {
                    success = true,
                    message = "The time crystal shows you fleeting visions of past and future.",
                    effects = {
                        {type = "reveal_map", radius = 10},
                        {type = "apply_status", status = "disoriented", duration = 60}
                    }
                }
            end
        end
        
        -- Standard examination
        local crystalDescriptions = {
            mana = "Glowing blue crystals that pulse with magical energy.",
            life = "Vibrant green crystals that seem to enhance natural life around them.",
            void = "Dark purple crystals that seem to absorb light and sound.",
            time = "Shimmering yellow crystals that seem to distort the space around them.",
            elemental = "Multicolored crystals that change hue as you move around them."
        }
        
        local description = crystalDescriptions[tile.crystalType] or "Strange crystalline formations jut from the ground."
        
        return {
            success = false,
            message = description
        }
    end,
    
    -- Weather effects
    weatherEffects = {
        lightning = function(tile)
            -- Lightning recharges crystal energy
            tile.energyLevel = math.min(10, tile.energyLevel + 2)
            return "visual_effect", "crystal_charge"
        end,
        
        rain = function(tile)
            -- Rain can clear dirt from crystals, making them more effective
            tile.clarity = math.min(1.0, (tile.clarity or 0.5) + 0.1)
            return nil
        end
    },
    
    -- Time effects
    timeEffects = {
        night = function(tile)
            -- Crystals glow more at night
            tile.glowIntensity = (tile.glowIntensity or 1) * 2
            
            -- Regenerate energy at night
            if tile.energyLevel < 10 and math.random() < 0.2 then
                tile.energyLevel = tile.energyLevel + 0.5
                if tile.energyLevel > 10 then tile.energyLevel = 10 end
                return "regenerate"
            end
            return nil
        end,
        
        day = function(tile)
            -- Reset glow to normal in day
            tile.glowIntensity = tile.baseGlowIntensity or 1
            return nil
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {0.5, 0.5, 0.9}, -- Will be overridden by crystal type
        variants = {
            {name = "small_cluster", size = 0.7},
            {name = "medium_cluster", size = 1.0},
            {name = "large_formation", size = 1.3},
            {name = "geode", is_hollow = true},
            {name = "embedded", in_wall = true}
        },
        crystal_types = {
            mana = {color = {0.3, 0.3, 0.9}, glow = 0.7},
            life = {color = {0.2, 0.8, 0.3}, glow = 0.5},
            void = {color = {0.5, 0.1, 0.5}, glow = 0.3},
            time = {color = {0.9, 0.8, 0.2}, glow = 0.6},
            elemental = {color = {0.6, 0.6, 0.6}, color_shift = true, glow = 0.4}
        },
        light_source = true,
        emissive = true,
        refraction = 0.3,
        reflection = 0.2,
        animation = {
            pulse = {speed = 1.0, intensity = 0.3},
            rotate = {speed = 0.1, angle = 5}
        }
    },
    
    -- Audio properties
    ambientSound = "crystal_resonance",
    
    -- Magic properties
    magical = {
        amplifies_spells = true, -- Makes spells cast nearby more powerful
        spell_amplification = 0.2, -- 20% more powerful
        mana_regeneration = 0.5, -- Faster mana regen near crystals
        attunement_bonus = 0.1, -- Bonus to magical attunement
        resonance_frequency = nil -- Set during init
    },
    
    -- Connections to other tiles
    connects_to = {
        "stone",
        "cave_floor",
        "ruins",
        "ancient_technology"
    },
    
    -- Effects on entities that are nearby
    onProximity = function(tile, entity, distance)
        -- Magical aura affects nearby entities
        if distance <= 2 and tile.energyLevel > 0 then
            -- Apply crystal effects
            if tile.crystalType == "mana" and entity.stats and entity.stats.mana then
                -- Increased mana regen
                return {
                    passive = true,
                    effects = {
                        {type = "mana_regen_boost", amount = 0.5, duration = 5}
                    }
                }
            elseif tile.crystalType == "life" then
                -- Health regeneration
                return {
                    passive = true,
                    effects = {
                        {type = "health_regen_boost", amount = 0.3, duration = 5}
                    }
                }
            elseif tile.crystalType == "void" then
                -- Mental effects
                if math.random() < 0.1 then
                    return {
                        message = "The void crystal's influence clouds your mind.",
                        effects = {
                            {type = "apply_status", status = "void_touched", duration = 30}
                        }
                    }
                end
            end
        end
        return nil
    end,
    
    -- Effect when walking directly on crystal
    onWalk = function(tile, entity)
        -- Crystals can affect entities that walk over them
        local effects = {
            mana = {message = "The crystals glow as you pass over them, filling you with arcane energy.", effect = {type = "mana_surge", amount = 5}},
            life = {message = "A surge of vitality flows through you as you step on the life crystals.", effect = {type = "heal", amount = 3}},
            void = {message = "The void crystals seem to pull at your very essence as you walk over them.", effect = {type = "apply_status", status = "void_touched", duration = 10}},
            time = {message = "Time seems to slow around you as you step across the time crystals.", effect = {type = "apply_status", status = "haste", duration = 5}},
            elemental = {message = "The elemental crystals resonate with your natural energies.", effect = {type = "element_attunement", duration = 60}}
        }
        
        local effect = effects[tile.crystalType]
        if effect then
            return {
                message = effect.message,
                effects = {effect.effect}
            }
        end
        
        return nil
    end
}

function CrystalFormationTile.init(world)
    print("Crystal formation tile module initialized")
    
    -- Register with magic system
    if world.systems and world.systems.magic then
        world.systems.magic:registerMagicSource("crystal_formation")
    end
    
    -- Register with resource system
    if world.systems and world.systems.resources then
        world.systems.resources:registerCrystalSource("crystal_formation")
    end
end

return CrystalFormationTile