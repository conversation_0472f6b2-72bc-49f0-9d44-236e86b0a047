-- utils/tile_system_config.lua
-- Configuration system for managing competing tile rendering systems

local TileSystemConfig = {
    -- Available tile rendering systems
    SYSTEMS = {
        COMPLEX = "complex",    -- TileVisuals + TileDecorations (nice system)
        SIMPLE = "simple",      -- LODSystem (basic system)
        DEFINITIONS = "definitions" -- TileDefinitions system
    },
    
    -- Current active system
    activeSystem = "complex", -- Default to the complex system
    
    -- System-specific configurations
    config = {
        complex = {
            name = "Complex Isometric System",
            description = "TileVisuals + TileDecorations - sophisticated isometric rendering with decorations, seasonal effects, and height support",
            features = {
                "Isometric diamond rendering",
                "Procedural noise-based color variation", 
                "Seasonal effects and decorations",
                "Height support with proper depth",
                "Wind effects on decorations",
                "Canvas-based decoration caching",
                "Biome-specific decorations"
            },
            performance = "Medium-High",
            quality = "High"
        },
        
        simple = {
            name = "Simple LOD System", 
            description = "LODSystem - basic level-of-detail rendering with simple shapes",
            features = {
                "Level-of-detail optimization",
                "Simple diamond shapes",
                "Basic color mapping",
                "Horizon line culling",
                "Distance-based tile grouping"
            },
            performance = "High",
            quality = "Low-Medium"
        },
        
        definitions = {
            name = "Modular Tile Definitions",
            description = "TileDefinitions - modular tile type system with custom draw functions",
            features = {
                "Custom draw functions per tile type",
                "Modular tile type registration",
                "Fallback rendering support",
                "Isometric and top-down support"
            },
            performance = "Variable",
            quality = "Variable"
        }
    },
    
    -- Debug settings
    debug = {
        enabled = false,
        showSystemInfo = false,
        showPerformanceMetrics = false,
        allowRuntimeSwitching = true
    }
}

-- Initialize the tile system configuration
function TileSystemConfig:init(options)
    options = options or {}
    
    -- Set initial system
    if options.system and self.SYSTEMS[string.upper(options.system)] then
        self.activeSystem = self.SYSTEMS[string.upper(options.system)]
    end
    
    -- Configure debug settings
    if options.debug then
        self.debug.enabled = options.debug.enabled or false
        self.debug.showSystemInfo = options.debug.showSystemInfo or false
        self.debug.showPerformanceMetrics = options.debug.showPerformanceMetrics or false
        self.debug.allowRuntimeSwitching = options.debug.allowRuntimeSwitching or true
    end
    
    print("TileSystemConfig initialized with system: " .. self.activeSystem)
    if self.debug.enabled then
        print("Debug mode enabled")
        self:printSystemInfo()
    end
    
    return self
end

-- Get the currently active system
function TileSystemConfig:getActiveSystem()
    return self.activeSystem
end

-- Set the active system
function TileSystemConfig:setActiveSystem(system)
    if not system or not self.config[system] then
        print("Warning: Invalid tile system '" .. tostring(system) .. "'. Available systems:", table.concat(self:getAvailableSystems(), ", "))
        return false
    end
    
    local oldSystem = self.activeSystem
    self.activeSystem = system
    
    print("Tile system changed from '" .. oldSystem .. "' to '" .. system .. "'")
    
    if self.debug.enabled then
        self:printSystemInfo()
    end
    
    return true
end

-- Get list of available systems
function TileSystemConfig:getAvailableSystems()
    local systems = {}
    for system, _ in pairs(self.config) do
        table.insert(systems, system)
    end
    return systems
end

-- Get configuration for a specific system
function TileSystemConfig:getSystemConfig(system)
    system = system or self.activeSystem
    return self.config[system]
end

-- Check if a specific system is active
function TileSystemConfig:isSystemActive(system)
    return self.activeSystem == system
end

-- Print information about the current system
function TileSystemConfig:printSystemInfo()
    local config = self:getSystemConfig()
    if not config then return end
    
    print("=== Active Tile System: " .. config.name .. " ===")
    print("Description: " .. config.description)
    print("Performance: " .. config.performance)
    print("Quality: " .. config.quality)
    print("Features:")
    for _, feature in ipairs(config.features) do
        print("  - " .. feature)
    end
    print("=====================================")
end

-- Toggle between systems (for debugging)
function TileSystemConfig:toggleSystem()
    if not self.debug.allowRuntimeSwitching then
        print("Runtime system switching is disabled")
        return false
    end
    
    local systems = self:getAvailableSystems()
    local currentIndex = 1
    
    -- Find current system index
    for i, system in ipairs(systems) do
        if system == self.activeSystem then
            currentIndex = i
            break
        end
    end
    
    -- Switch to next system (wrap around)
    local nextIndex = (currentIndex % #systems) + 1
    return self:setActiveSystem(systems[nextIndex])
end

-- Enable/disable debug mode
function TileSystemConfig:setDebugMode(enabled)
    self.debug.enabled = enabled
    if enabled then
        print("Tile system debug mode enabled")
        self:printSystemInfo()
    else
        print("Tile system debug mode disabled")
    end
end

-- Get debug information
function TileSystemConfig:getDebugInfo()
    return {
        activeSystem = self.activeSystem,
        systemConfig = self:getSystemConfig(),
        availableSystems = self:getAvailableSystems(),
        debugEnabled = self.debug.enabled
    }
end

return TileSystemConfig
