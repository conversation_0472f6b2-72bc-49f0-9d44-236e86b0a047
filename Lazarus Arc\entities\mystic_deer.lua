local MysticDeer = {
    id = "mystic_deer",
    name = "Mystic Deer",
    type = "mystic_deer",

    -- Entity categories
    categories = {"animal", "herbivore", "rare", "ethereal"},

    -- Threat categories
    threatCategories = {"predator", "monster"},

    -- Stats
    maxHealth = 50,
    health = 50,
    speed = 3.0, -- Faster than regular deer
    visionRange = 20, -- Enhanced awareness

    -- Behaviors
    behaviors = {"wander", "flee", "graze"},

    -- Behavior configurations
    behaviorConfigs = {
        wander = {
            moveSpeed = 2.0,
            changeDirectionChance = 0.08,
            idleChance = 0.2,
            idleDuration = {3, 7},
            wanderRadius = 40 -- Larger range
        },
        flee = {
            useCategories = true,
            moveSpeed = 5.0, -- Very fast when fleeing
            detectionRadius = 15, -- Increased awareness
            useHiding = true
        },
        graze = {
            foodTypes = {"grass", "flowers", "mystic_herb"}, -- Can graze on special herbs
            foodValue = {
                grass = 5,
                flowers = 8,
                mystic_herb = 20 -- High value food
            },
            fleeWhenThreatened = true,
            grazeTime = {4, 10},
            moveTime = {5, 12},
            grazeRadius = 15
        }
    },

    -- Abilities
    abilities = {
        teleport = {
            cooldown = 20, -- Can teleport short distances
            range = 10
        },
        etherealShift = {
            -- (Implementation for becoming temporarily intangible)
        }
    },

    -- Appearance
    appearance = {
        sprite = "mystic_deer", -- Replace with your sprite
        scale = 1.2,
        animations = {
            "idle",
            "walk",
            "run",
            "graze",
            "teleport", -- Add teleport animation
            "ethereal_shift" -- Add animation for ethereal shift
        },
        -- Visual effects like a subtle glow or particle trail
        effects = {
            glow = {
                color = {1, 0.8, 0.5, 0.8}, -- Soft orange glow
                intensity = 0.8,
                radius = 1.5
            }
        }
    },

    -- Sound effects
    sounds = {
        step = "mystic_step", -- Unique sound
        call = "mystic_call"
    },

    -- Loot drops
    drops = {
        {id = "mystic_fur", chance = 0.6, quantity = {1, 3}},
        {id = "mystic_essence", chance = 0.3, quantity = {1, 1}},
        {id = "antler", chance = 0.4, quantity = {1, 2}},
        {id = "mystic_meat", chance = 0.5, quantity = {2, 3}},
        {id = "ethereal_hide", chance = 0.2, quantity = {1, 1}}
    }
}

-- Initialize the mystic deer entity
function MysticDeer.init(entity, world)
    entity.x = entity.x or 0
    entity.y = entity.y or 0
    entity.rotation = entity.rotation or 0
    entity.width = 1
    entity.height = 1
    entity.type = "mystic_deer"
    
    -- Apply behavior
    local behaviorName = "mystic_deer_behavior"
    -- Check if world is not nil before trying to access it
    if world and world.modules and world.modules.behaviors then
        local behavior = world.modules.behaviors[behaviorName]
        if behavior then
            behavior.apply(entity)
        end
    end
    
    return entity
end

return MysticDeer