-- tiles/dunes.lua
local DunesTile = {
    id = "dunes",
    name = "Sand Dunes",
    passable = true, -- Passable, but difficult
    variants = 6, -- e.g., Crescent Dunes, Linear Dunes, Star Dunes, Low Ripples, Steep Face, Eroded

    -- Entities that might spawn here (often fewer than flat desert)
    spawns = {"sand_viper", "dune_beetle", "wind_elemental_minor", "skeletal_remains_exposed"}, -- More specialized/exposed spawns

    -- Movement properties
    movementSpeed = 0.75, -- Slower than flat desert sand due to slopes and very loose sand
    -- Note: Actual speed might vary based on direction relative to slope in onWalk/onMoveAttempt

    -- Dune-specific properties
    heightVariation = 0.8, -- Significant height differences within/between tiles (0-1)
    slopeSteepness = 0.6, -- Average steepness (0-1), can vary by variant
    instability = 0.4, -- Chance of minor sand slips/slides (0-1)
    windExposure = 0.8, -- More exposed to wind effects (0-1)

    -- Interaction function
    interact = function(tile, entity)
        -- Examine the dunes
        local description = "Towering waves of sand, sculpted by the relentless wind. The slopes look treacherous."
        
        -- Chance to find something exposed by wind
        if math.random() < 0.1 then
            local exposedItems = {
                {id="fossilized_bone", chance=0.4},
                {id="wind_polished_stone", chance=0.3},
                {id="ancient_pottery_shard", chance=0.2}, -- Hinting at buried history
                {id="lost_traveler_gear", chance=0.1}
            }
             for _, item in ipairs(exposedItems) do
                 if math.random() < item.chance then
                     return {
                         success = true,
                         message = "The shifting sands have momentarily uncovered a " .. item.id .. ".",
                         effects = {{type="add_item", item=item.id, quantity=1}}
                     }
                 end
             end
        end

        -- Observing the view from a high dune
        if tile.relativeHeight and tile.relativeHeight > 0.7 and entity.skills and entity.skills.perception > 2 then -- Assuming tile has a relativeHeight property
             return {
                 success = true,
                 message = "From atop this high dune, you get a commanding view of the surrounding desert, potentially spotting distant features.",
                 effects = {
                     {type="reveal_map", radius=5, require_los=true}, -- Reveal distant tiles if line of sight allows
                     {type="apply_status", status="HeightAdvantage", duration=10} -- Temporary perception bonus?
                 }
             }
        end

        -- General description
        return {
            success = false,
            message = description
        }
    end,

    -- Weather effects
    weatherEffects = {
        heat = function(tile) -- Similar heat effects to desert sand
            -- Assuming world context is available if needed, e.g., via tile.worldRef
            -- local world = tile.worldRef
            tile.temperature = ((tile.worldRef and tile.worldRef.temperature) or 35) + (tile.temperatureOffset or 0) + 8 -- Slightly less temp increase than flat sand maybe?
            tile.dehydrationRisk = (tile.baseDehydrationRisk or 0.35) + 0.15
            return "visual_effect", "heat_haze_strong"
        end,
        
        sun = function(tile)
            tile.temperature = 35 + (tile.temperatureOffset or 0)
            tile.dehydrationRisk = tile.baseDehydrationRisk or 0.35
            return "remove_visual_effect", "heat_haze_strong"
        end,

        wind = function(tile) -- Dunes are heavily affected by wind
             local stormChance = 0.5 * tile.windExposure -- Higher chance of sandstorm effects here
             -- Assuming world context is available if needed, e.g., via tile.worldRef
             -- local world = tile.worldRef
             local currentWindStrength = (tile.worldRef and tile.worldRef.windStrength) or 0
             if currentWindStrength > 0.6 and math.random() < stormChance then
                tile.isSandstormActive = true
                tile.movementSpeedModifier = 0.6 -- Significantly slower in storm
                tile.visibility = 0.3 -- Very low visibility
                -- Chance for dunes shape to slightly change over time? ("event", "dune_shift")
                return "trigger_event", "sandstorm_severe"
             else -- Normal wind still shifts sand
                 tile.isSandstormActive = false
                 tile.movementSpeedModifier = 1.0
                 tile.visibility = 1.0
                 if math.random() < 0.1 then
                      return "visual_effect", "sand_drifting"
                 end
             end
             return nil
        end,

        rain = function(tile) -- Rain temporarily stabilizes sand
            tile.temperature = (tile.temperature or 35) - 12
            tile.dehydrationRisk = 0.1
            tile.instability = (tile.instability or 0.4) * 0.5 -- Temporarily less prone to slides
            tile.movementSpeedModifier = 1.1 -- Slightly easier to move on damp sand
            return "visual_effect", "wet_sand"
        end
    },

     -- Time effects
    timeEffects = {
        night = function(tile)
            tile.temperatureOffset = -12 -- Cools significantly
            tile.dehydrationRisk = 0.05
            tile.ambientSound = "dune_wind_howl_low"
            -- Navigation might become harder due to lack of distinct shadows/features
            tile.navigationDifficulty = 1.3
            return nil
        end,
        
        dawn = function(tile)
             tile.temperatureOffset = -5 -- Starting to warm
             tile.ambientSound = "dune_wind_whisper"
             tile.navigationDifficulty = 1.0
             tile.instability = 0.4 -- Reset instability if changed by rain
             tile.movementSpeedModifier = 1.0 -- Reset movement mod
            return nil
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {0.9, 0.8, 0.6}, -- Similar to desert sand
        height_map = true, -- Essential for dune shapes
        height_scale = 1.5, -- Taller features than flat sand
        variants = {
            {name = "crescent", shape = "barchan"},
            {name = "linear", shape = "seif", length_bias = 2.0},
            {name = "star", shape = "star", radial_symmetry = true},
            {name = "low_ripples", height_scale = 0.3, pattern="ripple_large"},
            {name = "steep_face", steepness_modifier = 1.5, lee_side=true}, -- Lee side might have specific properties
            {name = "eroded", texture="wind_scoured_sand", cracks=0.2}
        },
        decoration_objects = { -- Fewer objects typically
            {name = "skeletal_remains_partial", chance = 0.05},
            {name = "wind_polished_rock_outcrop", chance = 0.03}
        },
        special_effects = {
             sand_blowing = {type="particle", density=0.5, direction="wind", speed_scale=1.2, when="windy"},
             sharp_shadows = {intensity=0.8, dynamic=true} -- Strong shadows due to height
        },
         weather_overlays = {
             sand_vortex = {effect="particle_swirl", density=0.6, when="sandstorm_severe"}
        }
    },
    
    -- Audio properties
    ambientSound = "dune_wind_whisper",
    footstepSound = "sand_step_deep", -- Deeper, sinking sound
     specialSounds = {
        {name = "sand_slide_small", trigger = "movement_on_slope", base_chance = 0.05, chance_modifier="instability"}, -- Changed chance = 0.1 * tile.instability
        {name = "wind_howl_crest", trigger = "windy", chance = 0.3},
        {name = "sand_shifting_groan", trigger = "random", chance = 0.02} -- Low chance of unsettling sounds
    },

    -- Connections to other tiles
    connects_to = {
        "dunes", -- Connects to itself
        "desert_sand", -- Connects to flat sand
        "rocky_ground" -- Can border rocky areas
        -- Unlikely to directly connect to oasis or water
    },

    -- Pathfinding and Movement Modifiers
     pathfinding = {
        travel_cost = 1.4, -- Significantly higher cost
        slope_penalty_up = 1.8, -- Multiplier for moving up steep slopes
        slope_bonus_down = 0.7, -- Multiplier for moving/sliding down steep slopes
        line_of_sight_occlusion = true -- Height variation blocks LoS
    },

    -- Effects on entities that walk on this tile
    onWalk = function(tile, entity, direction) -- Direction might be useful here
        local moveData = {
            sound = "sand_step_deep",
            volume = 1.0,
            effects = {}
        }
        local fatigueIncrease = 0.25 -- Base fatigue increase is higher

        -- Check slope relative to direction (requires world/tile data access)
        -- local relativeSlope = world.getSlopeRelativeToDirection(tile, direction)
        local relativeSlope = 0 -- Placeholder

        if relativeSlope > 0.5 then -- Moving uphill
             fatigueIncrease = fatigueIncrease * 1.5
             -- movementSpeed could be temporarily reduced further
             table.insert(moveData.effects, {type="apply_status", status="struggling_uphill", duration=1})
        elseif relativeSlope < -0.6 then -- Moving steeply downhill
             fatigueIncrease = fatigueIncrease * 0.5
             -- Chance to slide uncontrollably?
             local instability = tile.instability or 0.4
             if math.random() < 0.3 * instability then
                 -- Trigger a slide effect instead of normal move?
                 -- return "trigger_slide", direction
                 moveData.message = "You lose your footing and slide down the dune!"
                 table.insert(moveData.effects, {type="apply_status", status="sliding", duration=3})
             end
        end

        -- Chance of minor sand slip even on level ground/gentle slopes
         local instability = tile.instability or 0.4
         if math.random() < 0.05 * instability then
             table.insert(moveData.effects, {type = "apply_status", status = "minor_slip", duration = 1})
             moveData.sound = "sand_slide_small"
         end

         table.insert(moveData.effects, {type="increase_fatigue", amount = fatigueIncrease})

        return moveData
    end,
    
    -- Effect when discovering dunes
    onDiscover = function(tile, entity)
        if entity.journal and entity.journal.addDiscovery then
             entity.journal.addDiscovery({
                type = "terrain",
                name = "Sand Dunes",
                location = {x = tile.x, y = tile.y},
                notes = "Towering, shifting dunes. Difficult to traverse."
            })
        end
        return {
            message = "You enter a region dominated by massive sand dunes.",
            effects = {
                {type = "reveal_map", radius = 3, require_los=true} -- Line of sight is important here
            }
        }
    end
}

function DunesTile.init(world)
    print("Dunes tile module initialized")
    -- Register with systems if needed
    if world and world.systems and world.systems.geography then
        world.systems.geography:registerTerrainFeature("dunes", {blocks_los=true, high_wind_zone=true})
    end
     if world and world.systems and world.systems.pathfinding then
        -- Register special movement rules related to slopes?
        -- world.systems.pathfinding:registerSlopeMovement("dunes", {up=1.8, down=0.7})
    end
end

return DunesTile
