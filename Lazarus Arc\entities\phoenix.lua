-- entities/phoenix.lua
-- Phoenix flying entity with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local Phoenix = {
    id = "phoenix",
    name = "<PERSON>",
    type = "phoenix",
    shape = {
        {0, -1.2}, {1.0, -0.8}, {1.5, 0}, {1.0, 0.8},
        {0, 1.2}, {-1.0, 0.8}, {-1.5, 0}, {-1.0, -0.8}
    },
    size = 12,

    -- Entity categories
    categories = {"flying", "magical", "fire", "immortal", "legendary"},
    threatCategories = {"player", "ice_creatures", "undead"},
    
    -- Base stats (powerful magical flyer)
    maxHealth = 200,
    health = 200,
    maxStamina = 150,
    stamina = 150,
    maxMana = 250,
    mana = 250,
    speed = 4.0,  -- Very fast flyer
    attack = 30,
    defense = 18,
    magicAttack = 40,
    magicDefense = 35,
    fireResistance = 50,
    iceVulnerability = 2.0,
    flight_altitude = 25,
    
    -- Behaviors
    behaviors = {"soaring_flight", "fire_magic", "rebirth_cycle", "territorial_guardian"},
    behaviorConfigs = {
        soaring_flight = {
            cruiseSpeed = 4.5,
            maxAltitude = 50,
            diveBombSpeed = 6.0,
            thermalRiding = true
        },
        fire_magic = {
            spells = {"fireball", "flame_burst", "healing_flame", "phoenix_fire"},
            castChance = 0.4,
            manaCost = 40,
            fireImmunity = true
        },
        rebirth_cycle = {
            rebirthChance = 0.8,  -- 80% chance to resurrect on death
            rebirthTime = 10,
            ashPhase = 5,
            rebirthHealth = 0.5
        },
        territorial_guardian = {
            territoryRadius = 100,
            protectedArea = "sacred_grove",
            guardianBonus = 1.5
        }
    },
    
    -- Enhanced variant system for phoenix
    variantChances = {
        normal = 0.60,          -- 60% fire phoenix
        shiny = 0.30,           -- 30% ice phoenix (shiny)
        rare = 0.08,            -- 8% storm phoenix (rare)
        legendary = 0.02        -- 2% cosmic phoenix (legendary)
    },
    
    variants = {
        normal = {
            name = "Fire Phoenix",
            description = "A magnificent bird of flame that rises from its own ashes",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {1.2, 0.8, 0.4, 1.0},  -- Fiery red-orange
                flame_aura = true
            }
        },
        
        shiny = {
            name = "Ice Phoenix",
            description = "A rare phoenix of crystalline ice and eternal winter",
            statModifiers = {
                maxHealth = 1.4,    -- 280 health
                maxMana = 1.5,      -- 375 mana
                magicAttack = 1.3,  -- 52 magic attack
                speed = 1.2,        -- 4.8 speed
                ice_mastery = 2.0,
                eternal_cold = 1.5
            },
            appearanceModifiers = {
                scale = 1.1,
                glow = true,
                colorTint = {0.6, 0.8, 1.4, 1.0},  -- Crystalline blue-white
                ice_crystals = true,
                frost_aura = true,
                diamond_feathers = true
            },
            soundModifiers = {
                pitch = 1.2,
                volume = 1.1,
                crystalline = true
            }
        },
        
        rare = {
            name = "Storm Phoenix",
            description = "A phoenix that commands lightning and thunder",
            statModifiers = {
                maxHealth = 1.6,    -- 320 health
                maxMana = 1.8,      -- 450 mana
                magicAttack = 1.6,  -- 64 magic attack
                speed = 1.5,        -- 6.0 speed
                lightning_mastery = 3.0,
                storm_control = 2.5
            },
            appearanceModifiers = {
                scale = 1.2,
                colorTint = {0.8, 0.6, 1.2, 1.0},  -- Electric purple-blue
                lightning_feathers = true,
                storm_aura = true,
                electric_discharge = true,
                thunder_wings = true
            },
            soundModifiers = {
                pitch = 0.9,
                volume = 1.3,
                reverb = true,
                thunder_echo = true
            }
        },
        
        legendary = {
            name = "Cosmic Phoenix",
            description = "A phoenix born from the heart of a dying star",
            statModifiers = {
                maxHealth = 2.5,    -- 500 health
                maxMana = 3.0,      -- 750 mana
                magicAttack = 2.0,  -- 80 magic attack
                speed = 1.8,        -- 7.2 speed
                cosmic_power = 5.0,
                star_birth = 3.0,
                reality_transcendence = 2.0
            },
            appearanceModifiers = {
                scale = 1.5,
                glow = true,
                colorTint = {1.5, 1.2, 1.8, 1.0},  -- Cosmic purple-gold with starlight
                stellar_aura = "legendary",
                constellation_feathers = true,
                nebula_trail = true,
                cosmic_fire = true
            },
            soundModifiers = {
                pitch = 0.7,
                volume = 1.6,
                reverb = true,
                echo = true,
                cosmic_resonance = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "phoenix_feather", chance = 0.9, quantity = {2, 4}},
        {id = "phoenix_ash", chance = 1.0, quantity = {1, 3}},  -- Always drops ash
        {id = "fire_essence", chance = 0.8, quantity = {2, 3}},
        {id = "rebirth_crystal", chance = 0.6, quantity = {1, 1}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "ice_phoenix_feather", chance = 0.9, quantity = {2, 3}},
            {id = "eternal_ice_crystal", chance = 0.8, quantity = {1, 2}},
            {id = "winter_essence", chance = 0.7, quantity = {1, 2}},
            {id = "diamond_feather_core", chance = 0.6, quantity = {1, 1}}
        },
        rare = {
            {id = "storm_phoenix_feather", chance = 0.9, quantity = {2, 3}},
            {id = "lightning_essence", chance = 0.8, quantity = {1, 2}},
            {id = "thunder_crystal", chance = 0.7, quantity = {1, 1}},
            {id = "storm_control_orb", chance = 0.6, quantity = {1, 1}}
        },
        legendary = {
            {id = "cosmic_phoenix_plume", chance = 0.95, quantity = {1, 2}},
            {id = "star_essence", chance = 0.9, quantity = {1, 3}},
            {id = "cosmic_rebirth_core", chance = 0.8, quantity = {1, 1}},
            {id = "stellar_fire_crystal", chance = 0.7, quantity = {1, 1}},
            {id = "reality_transcendence_gem", chance = 0.6, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "phoenix",
        scale = 1.3,
        animations = {
            "soar", "dive_bomb", "flame_burst", "rebirth", "cosmic_ascension"
        },
        variants = {
            "fire_phoenix", "ice_phoenix", "storm_phoenix", "cosmic_phoenix"
        }
    },
    
    -- Sound effects with majestic flying characteristics
    sounds = {
        phoenix_cry = {
            file = "phoenix_cry",
            synth = {
                instrument = "flute",
                notes = {"A4", "C5", "E5", "A5"},
                durations = {0.5, 0.4, 0.6, 1.0},
                volume = 0.8,
                majestic = true
            }
        },
        flame_burst = {
            file = "flame_burst",
            synth = {
                instrument = "organ",
                notes = {"C4", "E4", "G4", "C5"},
                durations = {0.3, 0.3, 0.4, 0.6},
                volume = 0.7,
                fiery = true
            }
        },
        rebirth_song = {
            file = "rebirth_song",
            synth = {
                instrument = "choir",
                notes = {"F3", "A3", "C4", "F4", "A4"},
                durations = {0.8, 0.6, 0.8, 0.6, 1.2},
                volume = 0.9,
                resurrection = true
            }
        },
        cosmic_harmony = {
            file = "cosmic_harmony",
            synth = {
                instrument = "celesta",
                notes = {"C4", "E4", "G4", "B4", "D5", "F#5"},
                durations = {0.6, 0.5, 0.6, 0.5, 0.7, 1.0},
                volume = 0.8,
                celestial = true
            }
        }
    },
    
    -- Special phoenix abilities
    abilities = {
        rebirth = {
            type = "passive",
            description = "Resurrects from ashes when killed",
            effect = "resurrection",
            cooldown = 300  -- 5 minute cooldown
        },
        flame_aura = {
            type = "passive",
            description = "Damages nearby enemies with fire",
            effect = "fire_aura"
        },
        healing_flame = {
            type = "active",
            description = "Heals allies with purifying fire",
            effect = "area_heal",
            manaCost = 60,
            cooldown = 20
        },
        dive_bomb = {
            type = "active",
            description = "Devastating diving attack from great height",
            effect = "high_damage_dive",
            cooldown = 15
        },
        immortal_essence = {
            type = "passive",
            description = "Immune to aging and most status effects",
            effect = "status_immunity"
        }
    }
}

-- Initialize the phoenix entity using enhanced template
function Phoenix.init(entity, world)
    -- Copy all fields from Phoenix template to entity instance
    for k, v in pairs(Phoenix) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return Phoenix
