local Crab = {
    id = "crab",
    name = "Crab",
    type = "crab",
    shape = {
        {0, -1}, {1, -0.5}, {1, 0.5}, {0, 1}, {-1, 0.5}, {-1, -0.5}
    },
    size = 4,

    -- Entity categories
    categories = {"animal", "crustacean", "aquatic", "small"},

    -- Threat and food categories
    threatCategories = {"predator", "player"},
    foodCategories = {"fish", "insect", "plant"},

    -- Stats
    maxHealth = 25,
    health = 25,
    maxStamina = 40,
    stamina = 40,
    speed = 1.2,

    -- Aquatic properties
    aquatic = {
        swimSpeed = 1.5,
        diveDepth = 3,
        breathHoldTime = 180,
        currentBreath = 180,
        waterTemp = 18
    },

    -- Behaviors
    behaviors = {"wander", "swim", "burrow", "forage"},

    -- Behavior configurations
    behaviorConfigs = {
        wander = {
            moveSpeed = 1.0,
            changeDirectionChance = 0.15,
            idleChance = 0.2,
            idleDuration = {1, 3},
            wanderRadius = 6
        },
        swim = {
            moveSpeed = 1.5,
            diveChance = 0.3,
            diveDuration = {5, 15},
            surfaceInterval = {3, 8}
        },
        burrow = {
            maxDepth = 2,
            digSpeed = 0.8,
            tunnelLength = {2, 4},
            exitChance = 0.4,
            exitInterval = {5, 15}
        },
        forage = {
            moveSpeed = 0.8,
            searchRadius = 4,
            forageTime = {3, 8},
            foodTypes = {"fish", "insect", "plant"},
            waterSearch = true
        }
    },

    -- Special abilities
    abilities = {
        pinch = {
            damage = 8,
            range = 1.5,
            cooldown = 2,
            staminaCost = 5
        },
        swimBoost = {
            speedBoost = 1.3,
            duration = 2,
            cooldown = 4,
            staminaCost = 10
        }
    },

    -- Appearance
    appearance = {
        sprite = "crab",
        scale = 0.6,
        animations = {
            "idle", "walk", "swim", "burrow", "pinch"
        },
        variants = {
            "blue", "red", "green", "brown"
        }
    },

    -- Sound effects with synth configuration
    sounds = {
        click = {
            file = "crab_click",
            synth = {
                instrument = "xylophone",
                notes = {"C5", "E5"},
                durations = {0.05, 0.05},
                volume = 0.3
            }
        },
        splash = {
            file = "crab_splash",
            synth = {
                instrument = "vibraphone",
                notes = {"G4", "E4", "C4"},
                durations = {0.1, 0.1, 0.15},
                volume = 0.35
            }
        },
        dig = {
            file = "crab_dig",
            synth = {
                instrument = "marimba",
                notes = {"F3", "A3"},
                durations = {0.1, 0.1},
                volume = 0.25
            }
        },
        footstep = {
            synth = {
                instrument = "kalimba",
                notes = {"C4"},
                duration = 0.06,
                volume = 0.15
            }
        },
        scuttle = {
            synth = {
                instrument = "xylophone",
                notes = {"E4", "G4", "E4", "G4"},
                durations = {0.08, 0.08, 0.08, 0.08},
                volume = 0.25
            }
        },
        pinch = {
            synth = {
                instrument = "xylophone",
                notes = {"A5"},
                duration = 0.1,
                volume = 0.4
            }
        },
        hurt = {
            synth = {
                instrument = "electric_guitar",
                notes = {"D#4"},
                duration = 0.2,
                volume = 0.4,
                vibrato = true,
                vibratoRate = 10.0
            }
        },
        death = {
            synth = {
                instrument = "vibraphone",
                notes = {"C4", "A3", "F3"},
                durations = {0.3, 0.3, 0.6},
                volume = 0.4,
                vibrato = true,
                vibratoRate = 3.0
            }
        },
        swim = {
            synth = {
                instrument = "vibraphone",
                notes = {"A3", "C4"},
                durations = {0.2, 0.2},
                volume = 0.25
            }
        }
    },

    -- Loot drops
    drops = {
        {id = "meat", chance = 0.6, quantity = {1, 1}},
        {id = "shell", chance = 0.5, quantity = {1, 1}},
        {id = "claw", chance = 0.4, quantity = {1, 2}},
        {id = "chitin", chance = 0.3, quantity = {1, 1}},
        {id = "eye", chance = 0.2, quantity = {1, 2}}
    }
}

-- Initialize the entity
function Crab.init(entity, world)
    -- Copy all fields from Crab template to entity instance
    for k, v in pairs(Crab) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random crab variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Crab.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update aquatic properties
    if entity.aquatic then
        -- Update breath holding
        if entity.aquatic.currentBreath > 0 then
            entity.aquatic.currentBreath = math.max(0, entity.aquatic.currentBreath - dt)
        else
            -- Take damage when out of breath
            entity.health = math.max(0, entity.health - 0.3 * dt)
        end

        -- Regenerate breath when on surface
        if entity.aquatic.currentBreath < entity.aquatic.breathHoldTime then
            entity.aquatic.currentBreath = math.min(
                entity.aquatic.breathHoldTime,
                entity.aquatic.currentBreath + 1.5 * dt
            )
        end
    end
end

return Crab