-- biomes/hub.lua
local HubBiome = {
    id = "hub",
    name = "Player Hub",
    
    -- Core tile types
    primaryTiles = {"hub_floor", "hub_grass"},
    secondaryTiles = {"hub_path", "ruins", "water"},
    rareTiles = {"crystal_formation", "ancient_technology"},
    
    -- Special tile: Sanctuary in the center
    specialTiles = {
        sanctuary = "sanctuary_stone"
    },
    
    -- Default proportions
    tileDistribution = {
        hub_floor = 0.4,
        hub_grass = 0.3,
        hub_path = 0.2,
        ruins = 0.05,
        water = 0.03,
        crystal_formation = 0.01,
        ancient_technology = 0.01
    },
    
    -- Safe passive creatures only
    commonEntities = {
        "raccoon", "bird", "rabbit", "squirrel", "mouse"
    },
    
    uncommonEntities = {
        "deer", "fox", "horse", "butterfly", "frog"
    },
    
    rareEntities = {
        "owl", "songbird", "turtle", "friendly_spirit"
    },
    
    -- Default sanctuary NPCs
    coreNPCs = {
        "hub_guardian", -- Always present
        "resting_place" -- Recovery point
    },
    
    -- Unlockable NPCs
    unlockableNPCs = {
        "memory_keeper",
        "craftsman",
        "dimensional_merchant",
        "echo_of_self"
    },
    
    -- Hub growth levels
    growthLevels = {
        {
            level = 1,
            name = "Awakened Sanctuary",
            description = "The beginning of your journey to restore the ruins",
            unlockedTiles = {"hub_floor", "hub_grass", "hub_path", "ruins"},
            unlockedObjects = {"resting_place", "basic_crafting_table"},
            unlockedNPCs = {"hub_guardian"}
        },
        {
            level = 2,
            name = "Recovering Memory",
            description = "The sanctuary begins to reflect your recovered memories",
            unlockedTiles = {"water"},
            unlockedObjects = {"storage_chest"},
            unlockedNPCs = {"memory_keeper"}
        },
        {
            level = 3,
            name = "Expanding Sanctuary",
            description = "Your sanctuary grows as more memories return",
            unlockedTiles = {"crystal_formation"},
            unlockedObjects = {"portal_frame", "garden_plot"},
            unlockedNPCs = {"craftsman"}
        },
        {
            level = 4,
            name = "Connected Realms",
            description = "Your sanctuary can now connect to other worlds",
            unlockedTiles = {"ancient_technology"},
            unlockedObjects = {"dimensional_analyzer", "training_grounds"},
            unlockedNPCs = {"dimensional_merchant"}
        },
        {
            level = 5,
            name = "Complete Sanctuary",
            description = "Your sanctuary is fully restored",
            unlockedObjects = {"memory_altar", "advanced_crafting_station"},
            unlockedNPCs = {"echo_of_self"}
        }
    },
    
    -- Customization options
    customizationCategories = {
        "tiles",
        "objects",
        "decorations",
        "npcs",
        "lighting"
    },
    
    -- Hub variants
    variants = {
        forest_sanctuary = {
            name = "Forest Sanctuary",
            tileAdjustments = {
                hub_grass = 0.4,
                hub_floor = 0.3,
                hub_path = 0.2
            },
            entityAdjustments = {
                deer = 1.5,
                rabbit = 1.3,
                squirrel = 2.0
            },
            specialEffects = {
                "forest_sounds",
                "gentle_breeze",
                "falling_leaves"
            }
        },
        riverside_haven = {
            name = "Riverside Haven",
            tileAdjustments = {
                hub_grass = 0.3,
                hub_floor = 0.3,
                water = 0.2
            },
            entityAdjustments = {
                frog = 2.0,
                turtle = 1.5,
                bird = 1.3
            },
            specialEffects = {
                "flowing_water",
                "water_shimmer",
                "fish_jumping"
            }
        },
        ancient_grounds = {
            name = "Ancient Grounds",
            tileAdjustments = {
                hub_floor = 0.4,
                ruins = 0.25,
                ancient_technology = 0.05
            },
            entityAdjustments = {
                owl = 1.5,
                friendly_spirit = 2.0
            },
            specialEffects = {
                "floating_lights",
                "whispers_of_past",
                "ancient_symbols"
            }
        },
        crystal_garden = {
            name = "Crystal Garden",
            tileAdjustments = {
                hub_floor = 0.35,
                hub_grass = 0.3,
                crystal_formation = 0.15
            },
            entityAdjustments = {
                butterfly = 2.0,
                songbird = 1.5
            },
            specialEffects = {
                "crystal_chimes",
                "glowing_particles",
                "prismatic_reflections"
            }
        }
    },
    
    -- Weather patterns (mild only)
    weather = {
        clear = 0.6,
        light_rain = 0.2,
        misty = 0.15,
        rainbow = 0.05
    },
    
    -- Unique features
    features = {
        {
            name = "meditation_pond",
            chance = 0.4,
            unique = true
        },
        {
            name = "memory_stones",
            chance = 0.3
        },
        {
            name = "ancient_tree",
            chance = 0.2,
            unique = true
        },
        {
            name = "flower_garden",
            chance = 0.5
        }
    },
    
    -- Generation method for new player hub
    generateHub = function(chunk, world, player)
        print("Generating new hub for player: " .. player.name)
        
        -- Create sanctuary in the center
        local centerX = math.floor(world.CHUNK_SIZE / 2)
        local centerY = math.floor(world.CHUNK_SIZE / 2)
        
        -- Track hub data in player
        if not player.hubData then
            player.hubData = {
                level = 1,
                customizationPoints = 0,
                unlockedFeatures = {
                    tiles = {"hub_floor", "hub_grass", "hub_path", "ruins"},
                    objects = {"resting_place", "basic_crafting_table"},
                    npcs = {"hub_guardian"},
                    portals = {}
                }
            }
        end
        
        -- Set hub level based on player's hub data
        local hubLevel = player.hubData.level or 1
        
        -- Determine hub variant
        local hubVariant = player.hubData.variant or "forest_sanctuary"
        local variantData = HubBiome.variants[hubVariant]
        
        -- Generate the basic hub layout (spiral pattern from center)
        for i = 0, world.CHUNK_SIZE - 1 do
            for j = 0, world.CHUNK_SIZE - 1 do
                local distFromCenter = math.sqrt((i-centerX)^2 + (j-centerY)^2)
                
                local tileType
                if distFromCenter < 1 then
                    -- Center sanctuary
                    tileType = "sanctuary_stone"
                elseif distFromCenter < 3 then
                    -- Inner ring is mostly paths
                    if math.random() < 0.8 then
                        tileType = "hub_path"
                    else
                        tileType = "hub_floor"
                    end
                elseif distFromCenter < 6 then
                    -- Middle area is a mix
                    local typeRoll = math.random()
                    if typeRoll < 0.4 then
                        tileType = "hub_grass"
                    elseif typeRoll < 0.7 then
                        tileType = "hub_floor"
                    elseif typeRoll < 0.9 then
                        tileType = "hub_path"
                    else
                        tileType = "ruins"
                    end
                elseif distFromCenter < 10 then
                    -- Outer area
                    local typeRoll = math.random()
                    
                    -- Apply variant adjustments
                    local tileDistribution = {}
                    for tile, chance in pairs(HubBiome.tileDistribution) do
                        tileDistribution[tile] = chance
                        if variantData and variantData.tileAdjustments and variantData.tileAdjustments[tile] then
                            tileDistribution[tile] = variantData.tileAdjustments[tile]
                        end
                    end
                    
                    -- Select tile based on adjusted distribution
                    local cumulativeChance = 0
                    for tile, chance in pairs(tileDistribution) do
                        cumulativeChance = cumulativeChance + chance
                        if typeRoll < cumulativeChance then
                            tileType = tile
                            break
                        end
                    end
                    
                    -- Level restrictions
                    if tileType == "water" and hubLevel < 2 then
                        tileType = "hub_grass"
                    elseif tileType == "crystal_formation" and hubLevel < 3 then
                        tileType = "ruins"
                    elseif tileType == "ancient_technology" and hubLevel < 4 then
                        tileType = "ruins"
                    end
                else
                    -- Edge
                    tileType = "void"
                end
                
                -- Create the tile
                local tile = {
                    type = tileType,
                    x = i,
                    y = j,
                    variant = math.random(1, 3),
                    isSafe = true  -- Hub is always safe
                }
                
                local tileIndex = i + j * world.CHUNK_SIZE
                chunk.tiles[tileIndex] = tile
            end
        end
        
        -- Add core NPCs
        -- Hub Guardian
        local guardianX = chunk.x * world.CHUNK_SIZE + centerX
        local guardianY = chunk.y * world.CHUNK_SIZE + centerY - 2
        world.entitySystem:addEntity("hub_guardian", guardianX, guardianY, {
            owner = player.id,
            name = "Forgotten Guardian",
            friendly = true,
            dialogue = {
                greeting = "Welcome back to your sanctuary, " .. player.name .. ".",
                level1 = "This place is beginning to remember who it was.",
                level2 = "I can feel more memories returning to this place.",
                level3 = "The sanctuary grows stronger with your efforts.",
                level4 = "You've connected this realm to others. Well done.",
                level5 = "The sanctuary is complete, but your journey continues."
            }
        })
        
        -- Resting place at the center
        local restingPlaceX = chunk.x * world.CHUNK_SIZE + centerX
        local restingPlaceY = chunk.y * world.CHUNK_SIZE + centerY
        world.entitySystem:addEntity("resting_place", restingPlaceX, restingPlaceY, {
            owner = player.id,
            healAmount = 5 + hubLevel * 5,  -- Healing scales with hub level
            restoreStamina = true,
            restoreMana = hubLevel >= 3  -- Mana restoration unlocks at level 3
        })
        
        -- Add unlocked NPCs based on hub level
        for level = 1, hubLevel do
            local levelData = HubBiome.growthLevels[level]
            if levelData and levelData.unlockedNPCs then
                for _, npcType in ipairs(levelData.unlockedNPCs) do
                    if npcType ~= "hub_guardian" then  -- Guardian already added
                        -- Place NPCs around the sanctuary at different positions
                        local angle = math.random() * math.pi * 2
                        local distance = 3 + math.random() * 2
                        local npcX = chunk.x * world.CHUNK_SIZE + centerX + math.cos(angle) * distance
                        local npcY = chunk.y * world.CHUNK_SIZE + centerY + math.sin(angle) * distance
                        
                        world.entitySystem:addEntity(npcType, npcX, npcY, {
                            owner = player.id,
                            friendly = true,
                            level = level
                        })
                    end
                end
            end
        end
        
        -- Add passive wildlife
        HubBiome.populateEntities(chunk, world, player, hubVariant)
        
        -- Add hub features
        HubBiome.generateFeatures(chunk, world, player, hubVariant)
        
        -- Mark chunk as hub
        chunk.isHub = true
        chunk.hubOwner = player.id
        chunk.hubLevel = hubLevel
        chunk.hubVariant = hubVariant
        
        print("Hub generated for " .. player.name .. " (Level " .. hubLevel .. ", " .. variantData.name .. ")")
        
        return chunk
    end,
    
    -- Helper function to add passive wildlife
    populateEntities = function(chunk, world, player, variant)
        local centerX = math.floor(world.CHUNK_SIZE / 2)
        local centerY = math.floor(world.CHUNK_SIZE / 2)
        local variantData = HubBiome.variants[variant]
        local hubLevel = player.hubData.level or 1
        
        -- Determine entity counts (more entities at higher levels)
        local commonCount = 3 + math.floor(hubLevel * 1.5)
        local uncommonCount = 1 + math.floor(hubLevel * 0.7)
        local rareCount = math.floor(hubLevel * 0.3)
        
        -- Helper function to spawn an entity with variant adjustments
        local function spawnEntity(entityType, count)
            -- Apply variant adjustment if available
            local multiplier = 1.0
            if variantData and variantData.entityAdjustments and variantData.entityAdjustments[entityType] then
                multiplier = variantData.entityAdjustments[entityType]
            end
            
            -- Adjust count based on variant
            local adjustedCount = math.floor(count * multiplier + 0.5)
            
            -- Spawn entities
            for i = 1, adjustedCount do
                -- Pick a random position in the chunk, but not too close to center
                local attempts = 0
                local x, y
                repeat
                    x = math.random(0, world.CHUNK_SIZE - 1)
                    y = math.random(0, world.CHUNK_SIZE - 1)
                    local distFromCenter = math.sqrt((x-centerX)^2 + (y-centerY)^2)
                    attempts = attempts + 1
                    
                    -- Accept if either far enough from center or we've tried too many times
                    if distFromCenter > 3 or attempts > 10 then
                        break
                    end
                until false
                
                local tileIndex = x + y * world.CHUNK_SIZE
                local tile = chunk.tiles[tileIndex]
                
                -- Don't spawn on void or sanctuary stone
                if tile.type ~= "void" and tile.type ~= "sanctuary_stone" then
                    local worldX = chunk.x * world.CHUNK_SIZE + x
                    local worldY = chunk.y * world.CHUNK_SIZE + y
                    
                    -- Add entity with friendly properties
                    world.entitySystem:addEntity(entityType, worldX, worldY, {
                        friendly = true,
                        passive = true,
                        fleeOnThreat = true,
                        hubResident = true
                    })
                end
            end
        end
        
        -- Spawn common entities
        for _, entityType in ipairs(HubBiome.commonEntities) do
            spawnEntity(entityType, math.random(1, commonCount))
        end
        
        -- Spawn uncommon entities
        for _, entityType in ipairs(HubBiome.uncommonEntities) do
            if math.random() < 0.7 then  -- 70% chance for each uncommon entity
                spawnEntity(entityType, math.random(1, uncommonCount))
            end
        end
        
        -- Spawn rare entities
        for _, entityType in ipairs(HubBiome.rareEntities) do
            if math.random() < 0.3 then  -- 30% chance for each rare entity
                spawnEntity(entityType, math.random(0, rareCount))
            end
        end
    end,
    
    -- Helper function to generate features
    generateFeatures = function(chunk, world, player, variant)
        local centerX = math.floor(world.CHUNK_SIZE / 2)
        local centerY = math.floor(world.CHUNK_SIZE / 2)
        local hubLevel = player.hubData.level or 1
        
        -- Iterate through each potential feature
        for _, feature in ipairs(HubBiome.features) do
            -- Check if feature should spawn based on chance and level
            if math.random() < feature.chance * hubLevel / 5 then
                -- For unique features, only place one
                if feature.unique and chunk.uniqueFeatures and chunk.uniqueFeatures[feature.name] then
                    goto continue
                end
                
                -- Determine position for feature (away from center)
                local angle = math.random() * math.pi * 2
                local distance = 4 + math.random() * 4
                local featureX = math.floor(centerX + math.cos(angle) * distance)
                local featureY = math.floor(centerY + math.sin(angle) * distance)
                
                -- Ensure within bounds
                featureX = math.max(2, math.min(world.CHUNK_SIZE - 3, featureX))
                featureY = math.max(2, math.min(world.CHUNK_SIZE - 3, featureY))
                
                -- Place feature based on type
                if feature.name == "meditation_pond" then
                    -- Create a small pond with special properties
                    local pondRadius = math.random(2, 3)
                    
                    for dx = -pondRadius, pondRadius do
                        for dy = -pondRadius, pondRadius do
                            local dist = math.sqrt(dx*dx + dy*dy)
                            if dist <= pondRadius then
                                local tileX = featureX + dx
                                local tileY = featureY + dy
                                
                                if tileX >= 0 and tileX < world.CHUNK_SIZE and
                                   tileY >= 0 and tileY < world.CHUNK_SIZE then
                                    local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                    
                                    -- Create pond
                                    chunk.tiles[tileIndex].type = "water"
                                    chunk.tiles[tileIndex].variant = 1  -- Clear water
                                    chunk.tiles[tileIndex].meditationPond = true
                                    chunk.tiles[tileIndex].restoreEffect = hubLevel * 2  -- Stronger at higher levels
                                    
                                    -- Add water plants at edges
                                    if dist > pondRadius * 0.7 and math.random() < 0.4 then
                                        local plantX = chunk.x * world.CHUNK_SIZE + tileX
                                        local plantY = chunk.y * world.CHUNK_SIZE + tileY
                                        
                                        world.entitySystem:addEntity("water_lily", plantX, plantY, {
                                            glowing = hubLevel >= 3
                                        })
                                    end
                                end
                            end
                        end
                    end
                    
                elseif feature.name == "memory_stones" then
                    -- Create a circle of memory stones
                    local stoneCount = 3 + math.min(hubLevel, 5)
                    local radius = 2 + hubLevel * 0.5
                    
                    for i = 1, stoneCount do
                        local angle = (i / stoneCount) * math.pi * 2
                        local stoneX = math.floor(featureX + math.cos(angle) * radius)
                        local stoneY = math.floor(featureY + math.sin(angle) * radius)
                        
                        if stoneX >= 0 and stoneX < world.CHUNK_SIZE and
                           stoneY >= 0 and stoneY < world.CHUNK_SIZE then
                            local worldX = chunk.x * world.CHUNK_SIZE + stoneX
                            local worldY = chunk.y * world.CHUNK_SIZE + stoneY
                            
                            world.entitySystem:addEntity("memory_stone", worldX, worldY, {
                                owner = player.id,
                                stoneLevel = math.min(i, hubLevel),
                                glowing = i <= hubLevel,
                                memoryFragment = i <= hubLevel and math.random(1, 3) or nil
                            })
                        end
                    end
                    
                elseif feature.name == "ancient_tree" then
                    -- Create a special ancient tree
                    local worldX = chunk.x * world.CHUNK_SIZE + featureX
                    local worldY = chunk.y * world.CHUNK_SIZE + featureY
                    
                    world.entitySystem:addEntity("ancient_tree", worldX, worldY, {
                        age = 500 + hubLevel * 100,
                        size = 1.5 + hubLevel * 0.1,
                        wisdomLevel = hubLevel,
                        fruitType = hubLevel >= 3 and "wisdom_fruit" or "healing_fruit",
                        hasFruit = hubLevel >= 2
                    })
                    
                    -- Clear area around tree and make it special grass
                    for dx = -2, 2 do
                        for dy = -2, 2 do
                            local tileX = featureX + dx
                            local tileY = featureY + dy
                            
                            if tileX >= 0 and tileX < world.CHUNK_SIZE and
                               tileY >= 0 and tileY < world.CHUNK_SIZE then
                                local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                
                                chunk.tiles[tileIndex].type = "hub_grass"
                                chunk.tiles[tileIndex].variant = 2  -- Special lush variant
                                chunk.tiles[tileIndex].fertile = true
                            end
                        end
                    end
                    
                elseif feature.name == "flower_garden" then
                    -- Create a garden of special flowers
                    local gardenRadius = math.random(3, 4)
                    
                    for dx = -gardenRadius, gardenRadius do
                        for dy = -gardenRadius, gardenRadius do
                            local dist = math.sqrt(dx*dx + dy*dy)
                            if dist <= gardenRadius and math.random() < 0.7 then
                                local tileX = featureX + dx
                                local tileY = featureY + dy
                                
                                if tileX >= 0 and tileX < world.CHUNK_SIZE and
                                   tileY >= 0 and tileY < world.CHUNK_SIZE then
                                    local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                    
                                    chunk.tiles[tileIndex].type = "hub_grass"
                                    chunk.tiles[tileIndex].flowery = true
                                    
                                    -- Add flowers
                                    if math.random() < 0.6 then
                                        local flowerX = chunk.x * world.CHUNK_SIZE + tileX
                                        local flowerY = chunk.y * world.CHUNK_SIZE + tileY
                                        
                                        local flowerTypes = {"red_flower", "blue_flower", "yellow_flower", "white_flower"}
                                        local flowerType = flowerTypes[math.random(#flowerTypes)]
                                        
                                        world.entitySystem:addEntity(flowerType, flowerX, flowerY, {
                                            glowing = hubLevel >= 4 and math.random() < 0.3
                                        })
                                        
                                        -- Add butterflies
                                        if math.random() < 0.2 then
                                            world.entitySystem:addEntity("butterfly", flowerX + math.random(-1, 1) * 0.5, flowerY + math.random(-1, 1) * 0.5, {
                                                friendly = true,
                                                colorful = true
                                            })
                                        end
                                    end
                                end
                            end
                        end
                    end
                end
                
                -- Mark unique features as placed
                if feature.unique then
                    if not chunk.uniqueFeatures then
                        chunk.uniqueFeatures = {}
                    end
                    chunk.uniqueFeatures[feature.name] = true
                end
            end
            
            ::continue::
        end
    end,
    
    -- Generate a new hub based on player's hub level
    generate = function(chunk, world, variant)
        print("Error: Hub biome should be generated using generateHub with a player parameter")
        -- Create a basic empty hub as fallback
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                local tile = {
                    type = "hub_floor",
                    x = x,
                    y = y,
                    variant = 1
                }
                local tileIndex = x + y * world.CHUNK_SIZE
                chunk.tiles[tileIndex] = tile
            end
        end
    end,
    
    -- Post-processing for additional effects
    postProcess = function(chunk, world, player)
        local hubLevel = player and player.hubData and player.hubData.level or 1
        local hubVariant = player and player.hubData and player.hubData.variant or "forest_sanctuary"
        local variantData = HubBiome.variants[hubVariant]
        
        -- Apply variant-specific effects
        if variantData and variantData.specialEffects then
            for _, effect in ipairs(variantData.specialEffects) do
                chunk.environmentalEffects = chunk.environmentalEffects or {}
                table.insert(chunk.environmentalEffects, effect)
            end
        end
        
        -- Make hub tiles safe
        for _, tile in pairs(chunk.tiles) do
            tile.isSafe = true
            tile.noHostileMobs = true
            tile.refreshRate = hubLevel  -- Higher level hubs restore more quickly
            
            -- Hub variant effects
            if hubVariant == "forest_sanctuary" then
                if tile.type == "hub_grass" then
                    tile.grassLushness = 1.0 + hubLevel * 0.1
                end
            elseif hubVariant == "riverside_haven" then
                if tile.type == "water" then
                    tile.waterPurity = 1.0 + hubLevel * 0.2
                    tile.healingEffect = hubLevel > 2
                end
            elseif hubVariant == "ancient_grounds" then
                if tile.type == "ruins" or tile.type == "ancient_technology" then
                    tile.mysticalEnergy = hubLevel
                    tile.wisdomBonus = hubLevel > 3
                end
            elseif hubVariant == "crystal_garden" then
                if tile.type == "crystal_formation" then
                    tile.crystalPower = hubLevel
                    tile.manaRegenBonus = hubLevel > 2
                end
            end
        end
        
        -- Add fast travel capability if hub level is high enough
        if hubLevel >= 4 then
            chunk.fastTravelEnabled = true
            chunk.fastTravelName = player.name .. "'s Sanctuary"
        end
    end
}

-- Initialize the biome module
function HubBiome.init(worldCore)
    print("Hub biome module initialized")
    
    -- Store reference to WorldCore for later use
    HubBiome.worldCore = worldCore
    
    -- Don't try to register generators at init time
    -- Instead, we'll register them when a world is created
    
    -- Store the biome in the biomes registry
    -- This allows the module to be used later when worlds are created
    print("Hub biome registered successfully")
end

-- Add a new function to register generators with a specific world instance
function HubBiome.registerWithWorld(world)
    print("Registering hub biome generators with world")
    
    if not world or not world.chunkSystem then
        print("Warning: Cannot register hub biome - world or chunkSystem is nil")
        return false
    end
    
    -- Register generation algorithm
    world.chunkSystem:registerGenerator("hub", HubBiome.generate)
    
    -- Register player hub generator function
    world.createPlayerHub = function(playerId)
        local player = world.players[playerId]
        if not player then
            print("Player not found: " .. playerId)
            return nil
        end
        
        -- Create hub chunk at player's current position
        local hubChunkX = math.floor(player.position.x / world.CHUNK_SIZE)
        local hubChunkY = math.floor(player.position.y / world.CHUNK_SIZE)
        local hubChunkKey = hubChunkX .. "," .. hubChunkY
        
        -- Check if hub already exists
        if world.chunks[hubChunkKey] and world.chunks[hubChunkKey].isHub then
            return world.chunks[hubChunkKey]
        end
        
        -- Create hub chunk
        local hubChunk = {
            x = hubChunkX,
            y = hubChunkY,
            biome = "hub",
            tiles = {},
            entities = {},
            structures = {},
            active = true,
            isHub = true,
            owner = playerId
        }
        
        -- Generate the hub
        HubBiome.generateHub(hubChunk, world, player)
        
        -- Apply post-processing
        HubBiome.postProcess(hubChunk, world, player)
        
        -- Store the hub chunk
        world.chunks[hubChunkKey] = hubChunk
        
        -- If player has no home point, set it to the hub center
        if not player.homePoint then
            local centerX = hubChunkX * world.CHUNK_SIZE + math.floor(world.CHUNK_SIZE / 2)
            local centerY = hubChunkY * world.CHUNK_SIZE + math.floor(world.CHUNK_SIZE / 2)
            player.homePoint = {x = centerX, y = centerY}
            print("Set home point for " .. player.name)
        end
        
        return hubChunk
    end
    
    -- Register hub variants
    for variantId, _ in pairs(HubBiome.variants) do
        world.chunkSystem:registerGenerator("hub_" .. variantId, function(chunk, world)
            -- This is just a placeholder - hubs are generated using generateHub with player data
            HubBiome.generate(chunk, world, variantId)
        end)
    end
    
    return true
end

return HubBiome
