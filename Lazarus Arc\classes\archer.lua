-- classes/archer.lua
-- Archer class definition for Lazarus Arc

-- 🏹 Archer: Tactical ranged fighter, speed-focused, high mobility, medium armor
local Archer = {
    name = "<PERSON>",
    description = "A nimble combatant specializing in ranged combat, using superior mobility and precision over brute force.",
    icon = "archer_icon.png",

    -- 🎭 **Playstyle & Trade-offs**
    pros = {
        "Exceptional ranged accuracy and mobility",
        "High critical hit potential",
        "Can lay traps and use tactical stealth"
    },
    cons = {
        "Limited melee capabilities",
        "Lower durability compared to heavier classes",
        "Environmental factors (wind, weather) can impact effectiveness"
    },

    -- 🏋 **Base Stats & Growth**
    base_stats = { 
        health = 95, 
        maxHealth = 95,
        stamina = 80, 
        maxStamina = 80,
        mana = 35, 
        maxMana = 35,
        pattack = 12, 
        pdefense = 4, 
        mattack = 7, 
        mdefense = 5, 
        speed = 9, 
        luck = 6 
    },

    stat_growth = {
        health = 8,
        stamina = 7,
        mana = 3,
        pattack = 1.2,
        pdefense = 0.3,
        mattack = 0.6,
        mdefense = 0.4,
        speed = 1.0, -- High growth in mobility
        luck = 0.5
    },

    -- ⚔️ **Weapon Proficiency (How well they use different weapons)**
    proficiencies = { 
        sword = 0.7, 
        dagger = 1.5,  -- Can be a backup weapon
        greatsword = 0.3, 
        bow = 2.2,      -- Primary weapon
        crossbow = 2.0, 
        gun = 1.4,      -- Optional hybrid path
        magic = 0.5     -- Not ideal but possible
    },

    -- 🌿 **Elemental Affinities (Better synergy with specific elements)**
    element_affinities = {
        air = 10,   -- Wind-based attacks increase arrow velocity
        earth = 5,  -- Stable footing, better performance in rough terrain
        fire = -5   -- Less effective in hot environments (heat warps bowstrings)
    },

    -- ⚡ **Overdrive Abilities (Ultimate Moves)**
    overdrive = { 
        "storm_of_arrows", 
        "piercing_shot", 
        "silent_execution" 
    },

    -- 🏹 **Starting Skills (Always available to an Archer)**
    starting_skills = {
        "quick_shot",
        "aimed_shot",
        "evasion"
    },

    -- 📈 **Skill Progression (Player-Selectable)**
    skill_tree = {
        tier1 = {
            "multishot",
            "eagle_eye",
            "trap_setting"
        },
        tier2 = {
            "volley",
            "crippling_shot", 
            "smoke_bomb",
            "stealth"
        },
        tier3 = {
            "rain_of_arrows",
            "death_from_above",
            "shadow_meld"
        }
    },

    -- 🎒 **Inventory & Carrying Capacity**
    inventory_limit = 100, -- Average carrying capacity

    -- 🔮 **Special Abilities (Roleplay Mechanics & Passive Perks)**
    special_abilities = {
        "Eagle Eye", -- Passive: Increased critical hit chance with ranged weapons
        "Fleet Footed", -- Passive: Movement speed bonus
        "Trap Master", -- Can place combat traps in the environment
        "Wind Reader", -- Can predict wind direction & adjust shots
        "Tactical Retreat", -- Can disengage without triggering attacks of opportunity
        "Adaptive Camouflage", -- Movement-based stealth in wooded areas
    },

    -- 🏹 **Equipment Preferences (Not forced, just optimal)**
    equipment_preferences = {
        armor_type = "medium", -- Suggested, but not enforced
        weapon_preference = {"bow", "crossbow", "gun"} -- Hybrid options exist
    },

    -- 🎭 **Prestige & Advanced Paths**
    level_requirements = {
        multiclass = 20,   -- Can multiclass at 20
        prestige = 50      -- Unlocks advanced paths at 50
    },

    -- 🚀 **Potential Prestige Paths (Specialized Archer Evolutions)**
    prestige_paths = {
        ranger = {
            description = "A survivalist hunter specializing in nature-based tactics, enhanced mobility, and ambush tactics.",
            bonuses = {
                "Higher stealth effectiveness",
                "Enhanced trap placement",
                "Increased damage in forests & wilderness"
            },
            weaknesses = {
                "Reduced efficiency in urban environments",
                "Slower ranged attack speed compared to Sniper"
            }
        },

        sniper = {
            description = "A marksman who prioritizes precision and single-shot kills, excelling at extreme long-range combat.",
            bonuses = {
                "Significant damage boost for long-range attacks",
                "Can target weak points (bonus critical damage)",
                "Higher accuracy under calm conditions"
            },
            weaknesses = {
                "Melee combat remains extremely weak",
                "Wind & terrain impact shots significantly"
            }
        },

        scout = {
            description = "A tactical reconnaissance expert with increased mobility, evasiveness, and intelligence gathering skills.",
            bonuses = {
                "Can detect hidden enemies more easily",
                "Faster movement and stamina recovery",
                "Excels in hit-and-run combat"
            },
            weaknesses = {
                "Limited offensive power compared to Sniper or Ranger",
                "Cannot sustain prolonged fights"
            }
        }
    },

    -- 🎭 **Hybrid & Alternate Class Options**
    hybrid_options = {
        rogue_archer = {
            description = "Mixes archery with stealthy dagger-based close-quarters combat.",
            pros = {
                "Higher burst damage in melee",
                "Can combine ranged & close-range tactics"
            },
            cons = {
                "Requires high dexterity to be effective",
                "Loses ranged accuracy compared to Sniper"
            }
        },
        magic_marksman = {
            description = "An archer who integrates magical energy into their shots.",
            pros = {
                "Elemental-infused arrows",
                "Can enhance accuracy using magic"
            },
            cons = {
                "Lower physical damage compared to Sniper",
                "Mana reliance limits sustainability"
            }
        }
    }
}

return Archer
