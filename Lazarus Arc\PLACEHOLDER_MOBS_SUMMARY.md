# Placeholder Mobs Implementation Summary

## Overview
Added placeholder shapes and color-coded behavioral categories for passive mobs and other entities in the game. This provides basic visual representation using geometric shapes with appropriate colors and sizes.

## Color Coding System

### 🔘 GREY - Passive Mobs
Peaceful animals that don't attack players:
- **Small mammals**: rabbit, mouse, field_mouse, squirrel
- **Medium mammals**: sheep, goat (also resource)
- **Large mammals**: deer, horse, untamed_horse
- **Birds**: bird, pigeon, songbird, duck, goose, swan, crane, flamingo, pelican
- **Small creatures**: butterfly, fly, frog, turtle, crab
- **Fish**: bass, trout, carp, catfish, bluegill, perch, pike, walleye, sunfish, minnow, goldfish, guppy, angelfish, betta, crappie, muskellunge, northern_pike, plecostomus, tuna, swordfish, ray, dolphin

### 🔴 RED - Aggressive Mobs  
Hostile creatures that attack:
- **Predators**: wolf, fox, bear, raccoon
- **Birds of prey**: crow, hawk, eagle, vulture, great_horned_owl, snowy_owl, owl
- **Marine predators**: shark, whale

### 🟢 GREEN - Resource Mobs
Creatures that provide valuable resources:
- **Livestock**: sheep (wool), goat (milk), horse (transportation)
- Note: These can also be passive, but resource category takes priority

### 🟣 PURPLE - Boss/Elite Mobs
Special powerful creatures:
- ancient_treant, forest_golem, mystic_deer, mimic

## Placeholder Shapes Added

### Entities with NEW placeholder shapes:
- **rabbit**: Small mammal shape (size 6)
- **sheep**: Medium mammal shape (size 8) 
- **wolf**: Medium mammal shape (size 9)
- **pigeon**: Medium bird shape (size 6)
- **mouse**: Small mammal shape (size 4)
- **bass**: Medium aquatic shape (size 7)

### Entities that ALREADY had shapes:
- deer, bear, fox, crow, crab, horse, goat, duck, npc, creature, item

## Shape Categories

### Small Mammals (size 4-6)
Rounded octagon shape for small creatures like rabbits, mice
```lua
shape = {
    {0, -1}, {0.8, -0.6}, {1, 0}, {0.8, 0.6},
    {0, 1}, {-0.8, 0.6}, {-1, 0}, {-0.8, -0.6}
}
```

### Medium Mammals (size 7-10)
Standard octagon for medium creatures like sheep, wolves
```lua
shape = {
    {0, -1}, {0.8, -0.8}, {1, 0}, {0.8, 0.8},
    {0, 1}, {-0.8, 0.8}, {-1, 0}, {-0.8, -0.8}
}
```

### Medium Birds (size 6-8)
Elongated oval shape for flying creatures
```lua
shape = {
    {0, -1}, {0.7, -0.5}, {1, 0}, {0.7, 0.5},
    {0, 1}, {-0.7, 0.5}, {-1, 0}, {-0.7, -0.5}
}
```

### Medium Aquatic (size 6-9)
Streamlined fish-like shape
```lua
shape = {
    {0, -0.9}, {0.9, -0.4}, {1, 0}, {0.9, 0.4},
    {0, 0.9}, {-0.9, 0.4}, {-1, 0}, {-0.9, -0.4}
}
```

## Implementation Details

### Color System (utils/color_utils.lua)
- Added `getBehavioralCategory()` function to categorize entities
- Updated `getEntityColor()` to use behavioral categories first, then fall back to specific entity colors
- Supports both color and grayscale modes
- Priority order: Boss > Aggressive > Resource > Passive

### Entity Files Modified
All modified entities include:
- PLACEHOLDER comment marking temporary visual representation
- Appropriate shape array with relative coordinates
- Size property matching entity scale
- Maintained all existing functionality

### Rendering System
The existing rendering system in `utils/renderer.lua` and `utils/entity_visuals.lua` already supports:
- Shape-based rendering using polygon drawing
- Color-coded entities via ColorUtils
- Size-appropriate scaling
- Simplified vs. detailed rendering modes

## Usage
Entities will now appear as colored geometric shapes when rendered:
- Shape size reflects the creature's actual size
- Colors indicate behavioral category at a glance
- All shapes are marked as PLACEHOLDER for future sprite replacement

## Future Improvements
- Add more entity shapes for remaining creatures
- Create shape variants for different creature types
- Add animation support for placeholder shapes
- Replace with actual sprite assets when available
