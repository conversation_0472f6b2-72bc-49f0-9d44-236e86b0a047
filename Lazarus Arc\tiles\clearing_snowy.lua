-- tiles/clearing_snowy.lua
local ClearingSnowyTile = {
    id = "clearing_snowy",
    name = "Snowy Clearing",
    passable = true,
    variants = 4, -- Different snowy clearing patterns
    
    -- Properties
    properties = {
        snowDepth = 0.8,
        temperature = -5,
        windExposure = 0.7,
        hasTracks = true,
        hasSnowDrifts = true,
        hasIcePatches = true,
        hasWildlife = true
    },
    
    -- Visual properties
    visual = {
        base_color = {0.9, 0.9, 0.9}, -- White
        variants = {
            {name = "fresh_snow", type = "fresh"},
            {name = "packed_snow", type = "packed"},
            {name = "drift_snow", type = "drift"},
            {name = "ice_patch", type = "ice"}
        },
        snow_patterns = {
            {name = "smooth_snow", type = "smooth"},
            {name = "crusty_snow", type = "crust"},
            {name = "powder_snow", type = "powder"}
        }
    },
    
    -- Sound properties
    sounds = {
        ambient = {
            "wind_whistling",
            "snow_crunching",
            "distant_animals"
        },
        footsteps = {
            "snow_step",
            "ice_crunch"
        }
    },
    
    -- Resources
    resources = {
        snow = {
            amount = 10,
            regenRate = 0.3,
            harvestAmount = 2
        },
        ice = {
            amount = 3,
            regenRate = 0.1,
            harvestAmount = 1
        }
    },
    
    -- Effects
    effects = {
        wind = {
            active = true,
            strength = 0.7,
            direction = "random",
            driftFormation = true
        },
        freezing = {
            active = true,
            rate = 0.1
        }
    },
    
    -- Spawn rules
    spawnRules = {
        tracks = {
            chance = 0.6,
            minDistance = 1,
            maxDensity = 0.5
        },
        snow_drifts = {
            chance = 0.4,
            minDistance = 2,
            maxDensity = 0.3
        },
        ice_patches = {
            chance = 0.3,
            minDistance = 3,
            maxDensity = 0.2
        }
    },
    
    -- Weather effects
    weatherEffects = {
        snow = function(tile)
            -- Snow increases depth
            tile.properties.snowDepth = math.min(1.0, tile.properties.snowDepth + 0.2)
            return "snow_accumulate"
        end,
        wind = function(tile)
            -- Wind can create drifts
            if tile.properties.windExposure > 0.5 then
                tile.properties.snowDepth = math.min(1.0, tile.properties.snowDepth + 0.1)
                return "drift_form"
            end
            return nil
        end,
        heat = function(tile)
            -- Heat melts snow
            tile.properties.snowDepth = math.max(0, tile.properties.snowDepth - 0.3)
            if tile.properties.snowDepth < 0.2 then
                return "snow_melt"
            end
            return "snow_reduce"
        end
    },
    
    -- Time effects
    timeEffects = {
        day = function(tile)
            -- Daytime can melt snow
            if tile.properties.temperature > 0 then
                tile.properties.snowDepth = math.max(0, tile.properties.snowDepth - 0.1)
                return "snow_melt"
            end
            return nil
        end,
        night = function(tile)
            -- Nighttime can freeze and accumulate snow
            if tile.properties.temperature < 0 then
                tile.properties.snowDepth = math.min(1.0, tile.properties.snowDepth + 0.1)
                return "snow_accumulate"
            end
            return nil
        end
    }
}

-- Initialize the tile
function ClearingSnowyTile.init(tile, world)
    -- Initialize properties first
    tile.properties = tile.properties or {}
    
    -- Copy all fields from ClearingSnowyTile template to tile instance
    for k, v in pairs(ClearingSnowyTile) do
        if type(v) ~= "function" and tile[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                tile[k] = {}
                for subk, subv in pairs(v) do
                    tile[k][subk] = subv
                end
            else
                tile[k] = v
            end
        end
    end
    
    -- Set random variant
    if tile.visual and tile.visual.variants then
        local variants = tile.visual.variants
        tile.currentVariant = variants[math.random(#variants)]
    end
    
    -- Initialize resources
    if tile.resources then
        for resourceType, resource in pairs(tile.resources) do
            resource.currentAmount = resource.amount
            resource.lastHarvest = 0
            resource.growthStage = "mature"
        end
    end
    
    return tile
end

return ClearingSnowyTile 