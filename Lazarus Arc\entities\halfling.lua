-- entities/halfling.lua
-- Halfling entity with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local Halfling = {
    id = "halfling",
    name = "Halfling",
    type = "halfling",
    shape = {
        {0, -0.7}, {0.5, -0.5}, {0.7, 0}, {0.5, 0.5},
        {0, 0.7}, {-0.5, 0.5}, {-0.7, 0}, {-0.5, -0.5}
    },
    size = 4,  -- Much smaller than other races
    description = "A small, cheerful being known for luck, courage, and love of comfort",

    -- Entity categories
    categories = {"humanoid", "halfling", "small", "lucky"},
    threatCategories = {"player", "giant", "dragon", "orc"},
    
    -- Base stats (small but lucky and agile)
    maxHealth = 70,
    health = 70,
    maxStamina = 80,
    stamina = 80,
    maxMana = 60,
    mana = 60,
    speed = 2.8,  -- Fast for their size
    attack = 10,
    defense = 6,
    magicAttack = 8,
    magicDefense = 12,
    luck = 20,    -- Very high luck
    stealth = 15, -- Natural stealth
    
    -- Behaviors (halflings are peaceful but resourceful)
    behaviors = {"wander", "forage", "hide", "feast"},
    behaviorConfigs = {
        wander = {
            moveSpeed = 2.5,
            changeDirectionChance = 0.06,
            wanderRadius = 4,
            countryside_preference = true
        },
        forage = {
            duration = {8, 15},
            efficiency = 1.8,
            frequency = 0.4,
            food_finding = true
        },
        hide = {
            stealthBonus = 2.0,
            hideChance = 0.7,
            duration = {5, 10}
        },
        feast = {
            duration = {10, 20},
            healthRegen = 1.5,
            happiness_boost = true,
            frequency = 0.2
        }
    },
    
    -- Enhanced variant system for halflings
    variantChances = {
        normal = 0.75,          -- 75% shire halfling
        shiny = 0.15,           -- 15% lucky halfling (shiny)
        rare = 0.08,            -- 8% hero halfling (rare)
        legendary = 0.02        -- 2% halfling legend (legendary)
    },
    
    variants = {
        normal = {
            name = "Shire Halfling",
            description = "A typical halfling from the peaceful shires",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {1.0, 0.95, 0.9, 1.0},  -- Warm, homey tones
                clothing = "simple"
            }
        },
        
        shiny = {
            name = "Lucky Halfling",
            description = "A halfling blessed with extraordinary fortune",
            statModifiers = {
                maxHealth = 1.2,
                speed = 1.3,
                luck = 1.8,
                stealth = 1.4,
                critical_chance = 1.5
            },
            appearanceModifiers = {
                scale = 1.05,
                glow = true,
                colorTint = {1.2, 1.2, 1.0, 1.0},  -- Golden lucky glow
                lucky_charm = true,
                four_leaf_clover = true
            },
            soundModifiers = {
                pitch = 1.2,
                volume = 1.1,
                cheerful = true
            }
        },
        
        rare = {
            name = "Hero Halfling",
            description = "A brave halfling who has faced great dangers",
            statModifiers = {
                maxHealth = 1.6,
                attack = 1.8,
                defense = 1.5,
                speed = 1.4,
                courage = 2.5,
                luck = 1.5
            },
            appearanceModifiers = {
                scale = 1.1,
                colorTint = {1.1, 1.0, 1.1, 1.0},  -- Heroic aura
                adventuring_gear = true,
                battle_scars = "honorable",
                determined_expression = true
            },
            soundModifiers = {
                pitch = 1.0,
                volume = 1.3,
                reverb = true,
                heroic_tone = true
            }
        },
        
        legendary = {
            name = "Halfling Legend",
            description = "A legendary halfling whose deeds are sung in tales",
            statModifiers = {
                maxHealth = 2.2,
                attack = 2.5,
                defense = 2.0,
                speed = 1.8,
                luck = 3.0,
                legendary_presence = 5.0
            },
            appearanceModifiers = {
                scale = 1.2,
                glow = true,
                colorTint = {1.4, 1.3, 1.1, 1.0},  -- Legendary golden aura
                mythical_equipment = true,
                legend_aura = "legendary",
                story_book_presence = true
            },
            soundModifiers = {
                pitch = 0.9,
                volume = 1.6,
                reverb = true,
                echo = true,
                legendary_resonance = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "halfling_bread", chance = 0.8, quantity = {1, 3}},
        {id = "pipe_weed", chance = 0.6, quantity = {1, 2}},
        {id = "small_coin_purse", chance = 0.7, quantity = {1, 1}},
        {id = "comfort_item", chance = 0.5, quantity = {1, 1}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "lucky_coin", chance = 0.9, quantity = {1, 1}},
            {id = "four_leaf_clover", chance = 0.8, quantity = {1, 1}},
            {id = "fortune_cookie", chance = 0.6, quantity = {1, 2}},
            {id = "rabbit_foot", chance = 0.5, quantity = {1, 1}}
        },
        rare = {
            {id = "heroic_medal", chance = 0.8, quantity = {1, 1}},
            {id = "adventure_journal", chance = 0.7, quantity = {1, 1}},
            {id = "courage_potion", chance = 0.6, quantity = {1, 1}},
            {id = "small_magic_weapon", chance = 0.5, quantity = {1, 1}}
        },
        legendary = {
            {id = "legendary_halfling_ring", chance = 0.9, quantity = {1, 1}},
            {id = "tale_of_legend_scroll", chance = 0.8, quantity = {1, 1}},
            {id = "mythical_halfling_artifact", chance = 0.6, quantity = {1, 1}},
            {id = "story_essence", chance = 0.7, quantity = {1, 1}},
            {id = "legend_maker_token", chance = 0.4, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "halfling",
        scale = 0.7,  -- Smaller than default
        animations = {
            "idle", "walk", "eat", "hide", "celebrate", "death"
        },
        variants = {
            "shire_halfling", "lucky_halfling", "hero_halfling", "halfling_legend"
        }
    },
    
    -- Sound effects with halfling characteristics
    sounds = {
        idle = {
            file = "halfling_idle",
            synth = {
                instrument = "recorder",
                notes = {"G4", "A4", "B4"},
                durations = {0.3, 0.3, 0.4},
                volume = 0.25,
                cheerful = true
            }
        },
        eat = {
            file = "halfling_eat",
            synth = {
                instrument = "xylophone",
                notes = {"C5", "E5", "G5"},
                durations = {0.2, 0.2, 0.3},
                volume = 0.3,
                satisfied = true
            }
        },
        hide = {
            file = "halfling_hide",
            synth = {
                instrument = "flute",
                notes = {"A4"},
                duration = 0.1,
                volume = 0.15,
                whisper_quiet = true
            }
        },
        hurt = {
            file = "halfling_hurt",
            synth = {
                instrument = "recorder",
                notes = {"F#4"},
                duration = 0.25,
                volume = 0.4,
                surprised = true
            }
        },
        death = {
            file = "halfling_death",
            synth = {
                instrument = "recorder",
                notes = {"G4", "E4", "C4", "G3"},
                durations = {0.4, 0.4, 0.5, 1.0},
                volume = 0.5,
                peaceful = true
            }
        },
        celebrate = {
            file = "halfling_celebrate",
            synth = {
                instrument = "xylophone",
                notes = {"C5", "D5", "E5", "G5", "C6"},
                durations = {0.2, 0.2, 0.2, 0.2, 0.4},
                volume = 0.6,
                joyful = true
            }
        }
    },
    
    -- Special abilities
    abilities = {
        lucky_dodge = {
            type = "passive",
            description = "Extraordinary luck helps avoid attacks",
            effect = "dodge_chance_boost"
        },
        brave_heart = {
            type = "passive",
            description = "Resistance to fear and intimidation",
            effect = "fear_immunity"
        },
        small_size = {
            type = "passive",
            description = "Harder to hit due to small stature",
            effect = "evasion_bonus"
        },
        comfort_food = {
            type = "active",
            description = "Eating restores health and morale",
            effect = "healing_feast"
        },
        natural_stealth = {
            type = "passive",
            description = "Naturally good at hiding and moving quietly",
            effect = "stealth_bonus"
        }
    }
}

-- Initialize the halfling entity using enhanced template
function Halfling.init(entity, world)
    -- Copy all fields from Halfling template to entity instance
    for k, v in pairs(Halfling) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return Halfling
