local Swordfish = {
    id = "swordfish",
    name = "Swordfish",
    type = "fish",
    
    -- Entity categories
    categories = {"animal", "fish", "saltwater", "predator", "fast"},
    
    -- Threat and food categories
    threatCategories = {"player", "shark"},
    foodCategories = {"fish", "squid", "crustacean"},
    
    -- Stats
    maxHealth = 55,
    health = 55,
    maxStamina = 85,
    stamina = 85,
    speed = 3.2,
    
    -- Water properties
    water = {
        swimSpeed = 3.2,
        maxDepth = 20,
        preferredDepth = 12,
        oxygenLevel = 1.0,
        temperature = "warm",
        currentSpeed = 1.5
    },
    
    -- Behaviors
    behaviors = {"hunt", "patrol", "migrate", "territorial"},
    
    -- Behavior configurations
    behaviorConfigs = {
        hunt = {
            moveSpeed = 4.0,
            searchRadius = 30,
            attackRange = 3.0,
            damage = 40,
            preferredPrey = {"fish", "squid", "crustacean"},
            successChance = 0.85,
            speedBonus = 0.2
        },
        patrol = {
            moveSpeed = 2.5,
            patrolRadius = 35,
            preferredDepth = 12,
            restInterval = {20, 25}
        },
        migrate = {
            moveSpeed = 2.8,
            searchRadius = 40,
            preferredDepth = 15,
            season = "winter",
            successChance = 0.9
        },
        territorial = {
            moveSpeed = 3.0,
            territoryRadius = 25,
            preferredDepth = 12,
            aggressionLevel = 0.8
        }
    },
    
    -- Special abilities
    abilities = {
        charge = {
            speed = 5.0,
            damage = 45,
            duration = 0.6,
            cooldown = 4,
            staminaCost = 20,
            effect = "pierce"
        },
        slash = {
            range = 4,
            damage = 35,
            duration = 0.3,
            cooldown = 2,
            effect = "bleed"
        },
        burst = {
            speed = 4.5,
            duration = 0.8,
            cooldown = 3,
            staminaCost = 15
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "swordfish",
        scale = 1.3,
        animations = {
            "idle", "swim", "charge", "slash", "burst"
        },
        variants = {
            "blue", "silver", "striped", "dark"
        }
    },
    
    -- Sound effects
    sounds = {
        splash = "fish_splash",
        swim = "swordfish_swim",
        charge = "swordfish_charge",
        slash = "swordfish_slash"
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.8, quantity = {2, 3}},
        {id = "sword", chance = 0.6, quantity = {1, 1}},
        {id = "fin", chance = 0.4, quantity = {1, 1}},
        {id = "bone", chance = 0.5, quantity = {1, 2}}
    }
}

-- Initialize the entity
function Swordfish.init(entity, world)
    -- Copy all fields from Swordfish template to entity instance
    for k, v in pairs(Swordfish) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random swordfish variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Swordfish.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update water properties
    if entity.water then
        -- Adjust hunting success based on time of day
        if world and world.timeOfDay == "dawn" or world.timeOfDay == "dusk" then
            entity.behaviorConfigs.hunt.successChance = 0.9
        else
            entity.behaviorConfigs.hunt.successChance = 0.85
        end

        -- Adjust preferred depth based on time of day
        if world and world.timeOfDay == "night" then
            entity.water.preferredDepth = 8
        else
            entity.water.preferredDepth = 12
        end

        -- Apply speed bonus when near surface
        if entity.position.y < 5 then
            entity.behaviorConfigs.hunt.successChance = 
                entity.behaviorConfigs.hunt.successChance + 
                entity.behaviorConfigs.hunt.speedBonus
        end

        -- Check migration conditions
        if world and world.season == "winter" then
            entity.behaviorConfigs.migrate.successChance = 0.9
        else
            entity.behaviorConfigs.migrate.successChance = 0.0
        end

        -- Adjust territorial behavior based on prey density
        if world and world.entities then
            local nearbyPrey = 0
            for _, other in ipairs(world.entities) do
                if other.type == "fish" and 
                   (other.categories and table.contains(other.categories, "fish") or
                    other.categories and table.contains(other.categories, "squid")) then
                    local distance = math.sqrt(
                        (other.position.x - entity.position.x)^2 + 
                        (other.position.y - entity.position.y)^2
                    )
                    if distance < 15 then
                        nearbyPrey = nearbyPrey + 1
                    end
                end
            end
            if nearbyPrey > 3 then
                entity.behaviorConfigs.territorial.aggressionLevel = 0.9
            else
                entity.behaviorConfigs.territorial.aggressionLevel = 0.8
            end
        end
    end
end

return Swordfish 