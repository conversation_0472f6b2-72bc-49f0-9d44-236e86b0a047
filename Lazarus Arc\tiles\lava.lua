local Lava = {
    id = "lava",
    name = "Lava",
    type = "tile",
    
    -- Categories
    categories = {"liquid", "lava", "volcanic", "dangerous"},
    
    -- Properties
    properties = {
        walkable = false,
        swimable = false,
        climbable = false,
        flyable = true,
        diggable = false,
        buildable = false,
        flammable = false,
        temperature = 1.0,
        viscosity = 0.8,
        flowRate = 0.3,
        damage = 50,
        damageInterval = 0.5,
        lightRadius = 5,
        lightColor = {1.0, 0.4, 0.0},
        lightIntensity = 0.8,
        bubbleRate = 0.2,
        bubbleSize = 0.5,
        steamRate = 0.1,
        steamHeight = 2
    },
    
    -- Appearance
    appearance = {
        sprite = "lava",
        scale = 1.0,
        animations = {
            "idle",
            "flow",
            "bubble",
            "steam"
        },
        variants = {
            "bright",
            "dark",
            "crystal",
            "magma"
        },
        blendMode = "add",
        tint = {1.0, 0.4, 0.0},
        alpha = 0.9
    },
    
    -- Sound effects
    sounds = {
        flow = "lava_flow",
        bubble = "lava_bubble",
        steam = "lava_steam",
        burn = "lava_burn"
    },
    
    -- Effects
    effects = {
        burn = {
            type = "damage",
            duration = 0,
            effects = {
                damage = 50,
                interval = 0.5,
                burn = true,
                burnDuration = 3
            }
        },
        heat = {
            type = "environment",
            duration = 0,
            effects = {
                temperature = 1.0,
                radius = 3
            }
        },
        light = {
            type = "light",
            duration = 0,
            effects = {
                radius = 5,
                color = {1.0, 0.4, 0.0},
                intensity = 0.8
            }
        }
    },
    
    -- Spawn rules
    spawnRules = {
        structures = {
            "lava_geyser",
            "volcanic_vent",
            "obsidian_formation"
        }
    }
}

-- Initialize the tile
function Lava.init(tile, world)
    -- Copy all fields from Lava template to tile instance
    for k, v in pairs(Lava) do
        if type(v) ~= "function" and tile[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                tile[k] = {}
                for subk, subv in pairs(v) do
                    tile[k][subk] = subv
                end
            else
                tile[k] = v
            end
        end
    end

    -- Set random variant
    if tile.appearance and tile.appearance.variants then
        local variants = tile.appearance.variants
        tile.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize lava state
    tile.properties.lavaState = {
        flowDirection = {x = 0, y = 0},
        bubbleTimer = 0,
        steamTimer = 0,
        lastDamageTime = 0
    }

    return tile
end

-- Update the tile
function Lava.update(tile, world, dt)
    -- Update lava state
    if tile.properties.lavaState then
        -- Update flow direction based on surrounding tiles
        local flowX, flowY = 0, 0
        if world.getTile then
            local left = world.getTile({x = tile.position.x - 1, y = tile.position.y})
            local right = world.getTile({x = tile.position.x + 1, y = tile.position.y})
            local up = world.getTile({x = tile.position.x, y = tile.position.y - 1})
            local down = world.getTile({x = tile.position.x, y = tile.position.y + 1})
            
            -- Calculate flow direction based on height differences
            if left and left.properties and left.properties.height then
                flowX = flowX + (tile.properties.height - left.properties.height)
            end
            if right and right.properties and right.properties.height then
                flowX = flowX - (tile.properties.height - right.properties.height)
            end
            if up and up.properties and up.properties.height then
                flowY = flowY + (tile.properties.height - up.properties.height)
            end
            if down and down.properties and down.properties.height then
                flowY = flowY - (tile.properties.height - down.properties.height)
            end
            
            -- Normalize flow direction
            local length = math.sqrt(flowX * flowX + flowY * flowY)
            if length > 0 then
                flowX = flowX / length * tile.properties.flowRate
                flowY = flowY / length * tile.properties.flowRate
            end
        end
        
        tile.properties.lavaState.flowDirection = {x = flowX, y = flowY}
        
        -- Update bubble and steam effects
        tile.properties.lavaState.bubbleTimer = tile.properties.lavaState.bubbleTimer + dt
        tile.properties.lavaState.steamTimer = tile.properties.lavaState.steamTimer + dt
        
        if tile.properties.lavaState.bubbleTimer >= 1 / tile.properties.bubbleRate then
            tile.properties.lavaState.bubbleTimer = 0
            -- Create bubble effect
            if world.createEffect then
                world.createEffect({
                    type = "bubble",
                    position = tile.position,
                    size = tile.properties.bubbleSize
                })
            end
        end
        
        if tile.properties.lavaState.steamTimer >= 1 / tile.properties.steamRate then
            tile.properties.lavaState.steamTimer = 0
            -- Create steam effect
            if world.createEffect then
                world.createEffect({
                    type = "steam",
                    position = tile.position,
                    height = tile.properties.steamHeight
                })
            end
        end
    end
    
    -- Apply damage to entities in lava
    if world.entities then
        for _, entity in ipairs(world.entities) do
            if entity.position then
                local distance = math.sqrt(
                    (entity.position.x - tile.position.x)^2 + 
                    (entity.position.y - tile.position.y)^2
                )
                
                if distance <= 1.0 then
                    -- Check if it's time to apply damage
                    if world.time - tile.properties.lavaState.lastDamageTime >= tile.properties.damageInterval then
                        if entity.takeDamage then
                            entity.takeDamage(tile.properties.damage)
                        end
                        
                        -- Apply burn effect
                        if entity.applyEffect then
                            entity.applyEffect(tile.effects.burn)
                        end
                        
                        tile.properties.lavaState.lastDamageTime = world.time
                        
                        -- Play burn sound
                        if world.playSound then
                            world.playSound(tile.sounds.burn)
                        end
                    end
                end
            end
        end
    end
end

-- Handle interaction
function Lava.interact(tile, world, entity, action)
    if action == "touch" then
        -- Apply damage and burn effect
        if entity.takeDamage then
            entity.takeDamage(tile.properties.damage)
        end
        
        if entity.applyEffect then
            entity.applyEffect(tile.effects.burn)
        end
        
        -- Play burn sound
        if world.playSound then
            world.playSound(tile.sounds.burn)
        end
    end
end

return Lava 