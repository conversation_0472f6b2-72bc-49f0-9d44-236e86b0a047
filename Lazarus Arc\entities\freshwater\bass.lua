local Bass = {
    id = "bass",
    name = "Largemouth Bass",
    type = "fish",
    -- PLACEHOLDER: Basic shape for visual representation
    shape = {
        {0, -0.9}, {0.9, -0.4}, {1, 0}, {0.9, 0.4},
        {0, 0.9}, {-0.9, 0.4}, {-1, 0}, {-0.9, -0.4}
    },
    size = 7,

    -- Entity categories
    categories = {"animal", "fish", "freshwater", "predator"},
    
    -- Threat and food categories
    threatCategories = {"player", "predator"},
    foodCategories = {"fish", "insect", "frog", "small_prey"},
    
    -- Stats
    maxHealth = 35,
    health = 35,
    maxStamina = 60,
    stamina = 60,
    speed = 2.0,
    
    -- Water properties
    water = {
        swimSpeed = 2.0,
        maxDepth = 8,
        preferredDepth = 3,
        oxygenLevel = 1.0,
        temperature = "warm"
    },
    
    -- Behaviors
    behaviors = {"hunt", "patrol", "ambush", "school"},
    
    -- Behavior configurations
    behaviorConfigs = {
        hunt = {
            moveSpeed = 2.5,
            searchRadius = 15,
            attackRange = 1.5,
            damage = 15,
            preferredPrey = {"fish", "frog", "insect"},
            successChance = 0.7
        },
        patrol = {
            moveSpeed = 1.5,
            patrolRadius = 20,
            preferredDepth = 3,
            restInterval = {10, 15}
        },
        ambush = {
            moveSpeed = 0.5,
            waitDuration = {5, 15},
            attackSpeed = 3.0,
            successChance = 0.8,
            preferredTerrain = "vegetation"
        },
        school = {
            moveSpeed = 1.8,
            followDistance = 2,
            separationDistance = 1,
            alignmentStrength = 0.3,
            cohesionStrength = 0.4,
            maxGroupSize = 6
        }
    },
    
    -- Special abilities
    abilities = {
        lunge = {
            speed = 3.0,
            damage = 20,
            duration = 0.5,
            cooldown = 3,
            staminaCost = 15
        },
        splash = {
            range = 3,
            duration = 0.3,
            cooldown = 2,
            effect = "stun"
        },
        camouflage = {
            duration = 5,
            cooldown = 8,
            successChance = 0.7
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "bass",
        scale = 1.0,
        animations = {
            "idle", "swim", "attack", "ambush", "school"
        },
        variants = {
            "green", "brown", "spotted", "dark"
        }
    },
    
    -- Sound effects with synth configuration (flowing, aquatic sounds)
    sounds = {
        splash = {
            file = "fish_splash",
            synth = {
                instrument = "vibraphone",
                notes = {"C4", "E4", "G4"],
                durations = {0.1, 0.1, 0.2},
                volume = 0.3
            }
        },
        attack = {
            file = "fish_attack",
            synth = {
                instrument = "electric_guitar",
                notes = {"G3", "E3"],
                durations = {0.15, 0.2},
                volume = 0.4
            }
        },
        swim = {
            file = "fish_swim",
            synth = {
                instrument = "vibraphone",
                notes = {"A3", "C4"],
                durations = {0.3, 0.3},
                volume = 0.2
            }
        },
        hurt = {
            file = "bass_hurt",
            synth = {
                instrument = "electric_guitar",
                notes = {"D#3"],
                duration = 0.2,
                volume = 0.35,
                vibrato = true,
                vibratoRate = 8.0
            }
        },
        death = {
            file = "bass_death",
            synth = {
                instrument = "vibraphone",
                notes = {"G3", "E3", "C3"],
                durations = {0.3, 0.3, 0.6},
                volume = 0.35,
                vibrato = true,
                vibratoRate = 2.0
            }
        },
        idle = {
            file = "bass_idle",
            synth = {
                instrument = "vibraphone",
                notes = {"C3"],
                duration = 0.8,
                volume = 0.15,
                vibrato = true,
                vibratoRate = 2.0
            }
        },
        bubble = {
            file = "bass_bubble",
            synth = {
                instrument = "kalimba",
                notes = {"E4", "G4", "C5"],
                durations = {0.1, 0.1, 0.1},
                volume = 0.2
            }
        },
        jump = {
            file = "bass_jump",
            synth = {
                instrument = "vibraphone",
                notes = {"C4", "E4", "G4", "C5"],
                durations = {0.1, 0.1, 0.1, 0.2},
                volume = 0.35
            }
        }
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.7, quantity = {1, 2}},
        {id = "scale", chance = 0.6, quantity = {1, 3}},
        {id = "fin", chance = 0.3, quantity = {1, 1}},
        {id = "bone", chance = 0.4, quantity = {1, 1}}
    }
}

-- Initialize the entity
function Bass.init(entity, world)
    -- Copy all fields from Bass template to entity instance
    for k, v in pairs(Bass) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random bass variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Bass.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update water properties
    if entity.water then
        -- Adjust hunting success based on time of day
        if world and world.timeOfDay == "dawn" or world.timeOfDay == "dusk" then
            entity.behaviorConfigs.hunt.successChance = 0.8
        else
            entity.behaviorConfigs.hunt.successChance = 0.7
        end

        -- Adjust preferred depth based on time of day
        if world and world.timeOfDay == "night" then
            entity.water.preferredDepth = 1
        else
            entity.water.preferredDepth = 3
        end
    end
end

return Bass 