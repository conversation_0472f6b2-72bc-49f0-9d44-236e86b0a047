Loot = {
    -- 💰 Currency (Coins, Ingots, Purses)
    gold_coin = {name = "Gold Coin", category = "currency", value = 1, weight = 0.01},
    silver_coin = {name = "Silver Coin", category = "currency", value = 0.50, weight = 0.01},
    copper_coin = {name = "Copper Coin", category = "currency", value = 0.10, weight = 0.01},
    gold_ingot = {name = "Gold Ingot", category = "currency", value = 100, weight = 5.00},
    silver_ingot = {name = "Silver Ingot", category = "currency", value = 50, weight = 4.00},
    steel_ingot = {name = "Steel Ingot", category = "crafting", value = 10, weight = 5.00},

    -- 🎒 Purses (Can contain random amounts of gold)
    small_purse = {name = "Small Purse", category = "container", value = "random(5,20)", weight = 0.25},
    big_purse_black = {name = "Big Black Purse", category = "container", value = "random(30,50)", weight = 0.50},
    big_purse_blue = {name = "Big Blue Purse", category = "container", value = "random(30,50)", weight = 0.50},
    big_purse_green = {name = "Big Green Purse", category = "container", value = "random(30,50)", weight = 0.50},
    big_purse_red = {name = "Big Red Purse", category = "container", value = "random(30,50)", weight = 0.50},
    big_purse_yellow = {name = "Big Yellow Purse", category = "container", value = "random(30,50)", weight = 0.50},
    noble_purse_01 = {name = "Noble Purse (1)", category = "container", value = "random(75,150)", weight = 0.75},
    noble_purse_02 = {name = "Noble Purse (2)", category = "container", value = "random(75,150)", weight = 0.75},
    noble_purse_03 = {name = "Noble Purse (3)", category = "container", value = "random(75,150)", weight = 0.75},
    noble_purse_04 = {name = "Noble Purse (4)", category = "container", value = "random(75,150)", weight = 0.75},
    noble_purse_05 = {name = "Noble Purse (5)", category = "container", value = "random(75,150)", weight = 0.75},

    -- 💎 Treasure Items
    gold_necklace = {name = "Gold Necklace", category = "treasure", value = 200, weight = 1.00},
    silver_necklace = {name = "Silver Necklace", category = "treasure", value = 100, weight = 1.00},
    bronze_necklace = {name = "Bronze Necklace", category = "treasure", value = 50, weight = 1.00},
    pearl_01 = {name = "Single Pearl", category = "treasure", value = 25, weight = 0.20},
    pearl_02 = {name = "Pearl Necklace", category = "treasure", value = 120, weight = 0.75},
    gold_strange_trinket = {name = "Gold Strange Trinket", category = "treasure", value = 250, weight = 1.50},
    silver_strange_trinket = {name = "Silver Strange Trinket", category = "treasure", value = 150, weight = 1.50},
    iron_strange_trinket = {name = "Iron Strange Trinket", category = "treasure", value = 75, weight = 1.50},

    -- 🗝️ Keys & Unlockable Items
    key_01 = {name = "Rusty Key", category = "key", value = 0, weight = 0.25},
    key_02 = {name = "Brass Key", category = "key", value = 0, weight = 0.25},
    key_03 = {name = "Silver Key", category = "key", value = 0, weight = 0.25},
    key_04 = {name = "Golden Key", category = "key", value = 0, weight = 0.25},

    -- 📦 Chests (Require a key or can be random loot)
    wooden_chest_01 = {name = "Wooden Chest (1)", category = "container", value = "random(50,150)", weight = 15.00, requires_key = false},
    wooden_chest_02 = {name = "Wooden Chest (2)", category = "container", value = "random(50,150)", weight = 15.00, requires_key = false},
    wooden_chest_03 = {name = "Wooden Chest (3)", category = "container", value = "random(50,150)", weight = 15.00, requires_key = false},
    silver_chest = {name = "Silver Chest", category = "container", value = "random(200,500)", weight = 20.00, requires_key = true},
    gold_chest = {name = "Gold Chest", category = "container", value = "random(500,1000)", weight = 25.00, requires_key = true}
}

-- Function to open purses and chests
function open_loot(item_id)
    local item = Loot[item_id]
    if not item then
        return "Item not found."
    end
    
    if item.category == "container" then
        if item.requires_key and not player_has_key(item_id) then
            return "You need a key to open this chest!"
        end
        local gold_amount = math.random(string.match(item.value, "%d+"), string.match(item.value, "%d+$"))
        return "You opened the " .. item.name .. " and found " .. gold_amount .. " gold!"
    else
        return "This item cannot be opened."
    end
end

return Loot
