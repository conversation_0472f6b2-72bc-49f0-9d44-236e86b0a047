local UndeadInvasion = {
    id = "undead_invasion",
    name = "Undead Invasion",
    location = {x = 0, y = 0},
    currentStage = 0,
    stages = {
        -- Stage 1: Initial Outbreak
        {
            name = "Initial Outbreak",
            duration = 60,
            objectives = {
                "Survive the initial undead assault",
            },
            enemies = {
                {type = "skeleton", count = 15},
                {type = "zombie", count = 10},
            },
        },
        -- Stage 2: The Necromancer's Influence
        {
            name = "The Necromancer's Influence",
            duration = 90,
            objectives = {
                "Defeat the Necromancer's lieutenants",
                "Destroy undead summoning pillars",
            },
            enemies = {
                {type = "skeleton", count = 20},
                {type = "zombie", count = 15},
                {type = "ghoul", count = 5},
                {type = "necromancer_lieutenant", count = 2}, -- New enemy
            },
        },
        -- Stage 3: The Necromancer
        {
            name = "The Necromancer",
            duration = 120,
            objectives = {
                "Defeat the Necromancer",
            },
            enemies = {
                {type = "skeleton", count = 10},
                {type = "zombie", count = 10},
                {type = "death_knight", count = 5},
                {type = "necromancer", count = 1}, -- New enemy
            },
        },
    },
    timer = 0,
    isActive = false,
    -- Raid-specific data
    corruptionLevel = 0,
}

function UndeadInvasion.init(config)
    for k, v in pairs(config or {}) do
        UndeadInvasion[k] = v
    end
    UndeadInvasion.timer = 0
    UndeadInvasion.currentStage = 0
    UndeadInvasion.isActive = true
    UndeadInvasion.corruptionLevel = 0
end

function UndeadInvasion.update(world, dt)
    if not UndeadInvasion.isActive then
        return
    end

    UndeadInvasion.timer = UndeadInvasion.timer + dt
    local currentStage = UndeadInvasion.stages[UndeadInvasion.currentStage + 1]

    if currentStage then
        UndeadInvasion.spawnEnemies(world, currentStage.enemies)

        -- Stage progression logic
        if UndeadInvasion.timer >= currentStage.duration then
            UndeadInvasion.currentStage = UndeadInvasion.currentStage + 1
            UndeadInvasion.timer = 0
            print("Stage " .. UndeadInvasion.currentStage .. " complete!")
        end

        -- Stage-specific logic
        if UndeadInvasion.currentStage == 2 then
            UndeadInvasion.updateNecromancerLieutenants()
        end
        if UndeadInvasion.currentStage == 3 then
            UndeadInvasion.updateNecromancer()
        end

        UndeadInvasion.updateCorruption(dt) -- Update corruption every frame
    else
        UndeadInvasion.isActive = false
        print("Undead Invasion complete!")
    end
end

function UndeadInvasion.spawnEnemies(world, enemies)
    for _, enemyGroup in ipairs(enemies) do
        for i = 1, enemyGroup.count do
            local entity = world.entityFactory:create(enemyGroup.type)
            if entity then
                entity.position = {
                    x = UndeadInvasion.location.x + math.random(-5, 5),
                    y = UndeadInvasion.location.y + math.random(-5, 5),
                }
                world.entitySystem:addEntity(entity)
            end
        end
    end
end

function UndeadInvasion.updateNecromancerLieutenants()
    -- Logic for the Necromancer's lieutenants (e.g., summoning, buffing)
    print("Necromancer's lieutenants are casting spells!")
end

function UndeadInvasion.updateNecromancer()
    -- Logic for the Necromancer's abilities (e.g., raising dead, powerful spells)
    print("The Necromancer unleashes dark magic!")
end

function UndeadInvasion.updateCorruption(dt)
    -- Example corruption mechanic: increases over time
    UndeadInvasion.corruptionLevel = math.min(100, UndeadInvasion.corruptionLevel + dt * 2)
    print("Corruption level: " .. UndeadInvasion.corruptionLevel)

    -- Add effects based on corruption level (e.g., enemy buffs, player debuffs)
    if UndeadInvasion.corruptionLevel > 50 then
        print("The air grows heavy with dark energy.")
        -- Example: Buff undead attack
    end
end

return UndeadInvasion