-- biomes/swamp.lua
-- Swamp biome for Lazarus Arc

local SwampBiome = {
    id = "swamp",
    name = "Swamp",
    
    -- Core tile types
    primaryTiles = {"mud", "swamp_water"},
    secondaryTiles = {"shallow_water", "grass"},
    rareTiles = {"quicksand", "mangrove"},
    
    -- Default proportions
    tileDistribution = {
        mud = 0.5,
        swamp_water = 0.3,
        shallow_water = 0.1,
        grass = 0.05,
        quicksand = 0.03,
        mangrove = 0.02
    },
    
    -- Common passive entities
    commonEntities = {
        "frog", "mosquito", "dragonfly", "snake"
    },
    
    uncommonEntities = {
        "crocodile", "turtle", "fish", "swamp_bird"
    },
    
    rareEntities = {
        "will_o_wisp", "bog_monster", "swamp_hag"
    },
    
    -- Weather patterns
    weather = {
        fog = 0.4,
        rain = 0.3,
        light_rain = 0.2,
        clear = 0.1
    },
    
    -- Unique features
    features = {
        {
            name = "bog_pit",
            chance = 0.4,
            size = {min = 4, max = 7}
        },
        {
            name = "ancient_tree",
            chance = 0.3,
            size = {min = 1, max = 1},
            unique = true
        },
        {
            name = "witch_hut",
            chance = 0.2,
            size = {min = 3, max = 3},
            unique = true
        },
        {
            name = "mushroom_circle",
            chance = 0.3,
            size = {min = 2, max = 4}
        }
    },
    
    -- Generate method - takes chunk, world, and biomeData as parameters
    generateChunk = function(chunk, world, biomeData)
        -- Initialize 2D tiles array if not exists
        chunk.tiles = chunk.tiles or {}
        for x = 0, world.CHUNK_SIZE - 1 do
            chunk.tiles[x] = chunk.tiles[x] or {}
        end
        
        local baseTemperature = 25 -- Celsius
        local waterLevel = 0.7 -- High water level
        
        -- Generate basic terrain
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                -- Calculate noise values for terrain
                local nx = (chunk.x * world.CHUNK_SIZE + x) / 100
                local ny = (chunk.y * world.CHUNK_SIZE + y) / 100
                
                local moistureNoise = love.math.noise(nx, ny) * 0.5 + 0.5
                local temperatureOffset = (love.math.noise(nx * 0.5, ny * 0.5) - 0.5) * 5
                
                -- Determine tile type based on moisture
                local tileType
                if moistureNoise > 0.8 then
                    tileType = "swamp_water"
                elseif moistureNoise > 0.7 then
                    tileType = "shallow_water"
                elseif moistureNoise > 0.4 then
                    tileType = "mud"
                elseif moistureNoise > 0.35 then
                    if love.math.random() < 0.3 then
                        tileType = "mangrove"
                    else
                        tileType = "mud"
                    end
                elseif moistureNoise > 0.3 then
                    tileType = "grass"
                else
                    if love.math.random() < 0.4 then
                        tileType = "quicksand"
                    else
                        tileType = "mud"
                    end
                end
                
                -- Create the tile
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = love.math.random(1, 3),
                    passable = (tileType ~= "deep_water" and tileType ~= "quicksand"),
                    isUnderground = false,
                    temperature = baseTemperature + temperatureOffset,
                    waterLevel = waterLevel
                }
                
                -- Add to chunk using 2D indexing
                chunk.tiles[x][y] = tile
            end
        end
        
        -- Add features
        for _, feature in ipairs(SwampBiome.features) do
            if love.math.random() < feature.chance then
                local featureX = love.math.random(world.CHUNK_SIZE / 4, world.CHUNK_SIZE * 3/4)
                local featureY = love.math.random(world.CHUNK_SIZE / 4, world.CHUNK_SIZE * 3/4)
                
                local featureSize = love.math.random(feature.size.min, feature.size.max)
                local radius = featureSize / 2
                
                for dx = -radius, radius do
                    for dy = -radius, radius do
                        local dist = math.sqrt(dx*dx + dy*dy)
                        if dist <= radius then
                            local tileX = featureX + dx
                            local tileY = featureY + dy
                            
                            if tileX >= 0 and tileX < world.CHUNK_SIZE and
                               tileY >= 0 and tileY < world.CHUNK_SIZE then
                                local tile = chunk.tiles[math.floor(tileX)][math.floor(tileY)]
                                
                                if feature.name == "bog_pit" then
                                    tile.type = "swamp_water"
                                    tile.variant = love.math.random(1, 2)
                                    
                                    -- Deep center of the bog
                                    if dist < radius * 0.5 then
                                        tile.passable = false
                                        tile.danger = "bog"
                                        tile.dangerLevel = love.math.random(1, 3)
                                    end
                                elseif feature.name == "ancient_tree" then
                                    tile.type = "mangrove"
                                    
                                    -- The central ancient tree
                                    if dist < 1 then
                                        local worldX = chunk.x * world.CHUNK_SIZE + tileX
                                        local worldY = chunk.y * world.CHUNK_SIZE + tileY
                                        world.entitySystem:addEntity("ancient_tree", worldX, worldY)
                                    end
                                elseif feature.name == "witch_hut" then
                                    if dist < 1 then
                                        -- Place the hut itself
                                        local worldX = chunk.x * world.CHUNK_SIZE + tileX
                                        local worldY = chunk.y * world.CHUNK_SIZE + tileY
                                        world.entitySystem:addEntity("witch_hut", worldX, worldY)
                                    else
                                        -- Surrounding area
                                        tile.type = "mud"
                                    end
                                elseif feature.name == "mushroom_circle" then
                                    -- Set to grass
                                    tile.type = "grass"
                                    
                                    -- Place mushrooms around the perimeter
                                    if dist > radius * 0.7 and dist < radius * 0.9 and love.math.random() < 0.7 then
                                        local worldX = chunk.x * world.CHUNK_SIZE + tileX
                                        local worldY = chunk.y * world.CHUNK_SIZE + tileY
                                        local mushroomTypes = {"red_mushroom", "blue_mushroom", "glowing_mushroom"}
                                        local mushType = mushroomTypes[love.math.random(#mushroomTypes)]
                                        world.entitySystem:addEntity(mushType, worldX, worldY)
                                    end
                                end
                            end
                        end
                    end
                end
            end
        end
        
        return chunk
    },
    
    -- Default entities for the swamp
    populateEntities = function(chunk, world)
        -- Calculate entity density based on noise
        local nx = chunk.x / 10
        local ny = chunk.y / 10
        local density = love.math.noise(nx, ny) * 0.5 + 0.5
        
        -- Swamps have moderate entity density
        local commonCount = love.math.random(5, 10) * density
        local uncommonCount = love.math.random(2, 5) * density
        local rareCount = love.math.random(0, 2)
        
        -- Spawn common entities
        for _, entityType in ipairs(SwampBiome.commonEntities) do
            local count = love.math.random(1, math.floor(commonCount / #SwampBiome.commonEntities) + 1)
            for i = 1, count do
                local x = love.math.random(0, world.CHUNK_SIZE - 1)
                local y = love.math.random(0, world.CHUNK_SIZE - 1)
                
                if chunk.tiles[x][y].passable then
                    local worldX = chunk.x * world.CHUNK_SIZE + x
                    local worldY = chunk.y * world.CHUNK_SIZE + y
                    world.entitySystem:addEntity(entityType, worldX, worldY)
                end
            end
        end
        
        -- Spawn uncommon entities
        for _, entityType in ipairs(SwampBiome.uncommonEntities) do
            local count = love.math.random(0, math.floor(uncommonCount / #SwampBiome.uncommonEntities) + 1)
            for i = 1, count do
                local x = love.math.random(0, world.CHUNK_SIZE - 1)
                local y = love.math.random(0, world.CHUNK_SIZE - 1)
                
                if chunk.tiles[x][y].passable then
                    local worldX = chunk.x * world.CHUNK_SIZE + x
                    local worldY = chunk.y * world.CHUNK_SIZE + y
                    world.entitySystem:addEntity(entityType, worldX, worldY)
                end
            end
        end
        
        -- Spawn rare entities
        for _, entityType in ipairs(SwampBiome.rareEntities) do
            if love.math.random() < 0.3 then
                local x = love.math.random(0, world.CHUNK_SIZE - 1)
                local y = love.math.random(0, world.CHUNK_SIZE - 1)
                
                if chunk.tiles[x][y].passable then
                    local worldX = chunk.x * world.CHUNK_SIZE + x
                    local worldY = chunk.y * world.CHUNK_SIZE + y
                    world.entitySystem:addEntity(entityType, worldX, worldY)
                end
            end
        end
    },
    
    -- Initialize the module
    init = function(worldCore)
        print("Swamp biome initialized")
        return true
    end
}

-- The generate function that the chunk system will call
function SwampBiome.generate(chunk, world, variant)
    SwampBiome.generateChunk(chunk, world, variant)
    SwampBiome.populateEntities(chunk, world)
    return chunk
end

return SwampBiome 