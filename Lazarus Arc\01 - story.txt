World:

The Rift: The world is a massive dimensional rift where fragments of different planes and timelines collide. This causes unpredictable shifts in the environment, merging disparate locations and creating bizarre juxtapositions. 
Settlements: Settlements are incredibly diverse, reflecting the jumbled nature of the world. There might be a medieval village fused with a futuristic metropolis, a farm growing crops from another dimension, or a factory churning out impossible machines. 
Factions: Several factions have risen from the chaos:
The Pathfinders: Seek to map and understand the shifting world, hoping to find a way to stabilize it. 
The Guardians: Focus on protecting settlements and defending against the dangers of the Rift. 
The Rift Walkers: Embrace the chaos, seeking to exploit the Rift's power for their own gain. 
The Harmonizers: Believe the world can be healed through understanding and cooperation with the natural forces of the Rift. 
Echoes of the Breaker: These manifest as fragmented memories, distorted visions, or whispers from the past, hinting at the Breaker's motives and the true nature of the Breaking. 
Character & Story:

The Player: You are a traveler, drawn to the Rift for reasons unknown. You navigate the shifting landscapes, gather resources, and uncover the secrets of the world. 
Goal: The story is about exploration, discovery, and finding your place in a world that defies expectations. It's a journey of hope and resilience in the face of overwhelming change. 
Themes: Intrigue, mystery, wonder, rebirth, acceptance, and the diverse ways people react to change. 
Key Scenes & Moments:

Arrival at the Hub: A sense of stability amidst chaos, meeting other travelers and learning about the world. 
First Rift Walk: Experiencing the disorientation and wonder of the shifting landscapes. 
Encountering Echoes: Uncovering fragments of the past, leading to questions about the Breaker and the Breaking. 
Tranquil Forest Biome: A moment of peace and self-sufficiency, offering a glimpse of what the world could be. 
Faction Interactions: Choosing to align with or oppose different factions, shaping the future of the world. 

1. The World: The Rift & The Breaking
Nature: A massive dimensional rift where fragments of different planes and timelines collide, causing unpredictable environmental shifts.
The Breaking: An ongoing event, not a single past cataclysm. Biomes and areas can literally "drop in and out."
Rift as an Entity: The Rift itself is conceptualized as an entity with HP, magic, and the ability to attack the player or influence events. It's like an elemental force of nature (specific element(s) to be decided).
Rift Tendrils: Visible manifestations of the Rift, possibly affecting cardinal directions (N/S/E/W and in-between) of the world. Damaging these (through magic, disruptive actions) makes the Rift less stable (more chaotic). Their effects might persist for a time.
Rift Stability:
More Chaos: More damage to the Rift/tendrils leads to more frequent/intense shifts. This can mean more varied resources but also makes survival and resource gathering harder if it's too extreme.
Less Chaos (More Stability): Achieved by "healing magic" or "balancing a biome" (managing populations, removing corruption). This might lead to different types_ of resources or allow players to secure rare resources for longer, but potentially locks out resources that only appear in high chaos.
Faction Influence: Factions have a preferred "happy area" or balance of chaos/stability.
2. The Player Character (Lazarus Arc)
Role: A vessel for the player to explore the game and story. Their specific origins or "why they are drawn to the Rift" aren't critical to define explicitly.
Unique Ability ("Lazarus Arc"): Upon death, the player returns to their Sanctuary/Hub. This is their primary tie to the Sanctuary and can be viewed as a curse or a blessing. This is the core respawn mechanic.
Memory: The player remembers things well, even through deaths. The world's chaos provides enough confusion.
Perception by Others: Inhabitants and factions will react differently to this ability – some with fear, some with curiosity, some wanting to attack or capture. This perception can be influenced by player actions (gifts, favors, interactions).
3. The Breaker
Role: The main story character of the game, whose past actions the player witnesses or uncovers.
Motives: Intentionally ambiguous. Could have had good intentions or wanted to cause destruction. Players might form their own binary "good/evil" opinions.
Endgame: Likely the final boss of the main story, possibly in a "corrupted" form.
Corruption Source: The nature of the Breaker's potential corruption (from the Rift, their own actions, another entity) is still open but could explain their ambiguous motives.
4. Echoes of the Breaker
Manifestation: Fragmented memories, distorted visions, whispers, environmental anomalies – adding to the chaos but also providing vague story snippets about the Breaker's actions and motives. Compared to "The Division's" Echoes of Keener.
Purpose: Guide the player towards the endgame/confrontation with the Breaker.
5. Factions (General)
New Factions: Pathfinders (explore, seek stability), Guardians (protect), Rift Walkers (embrace/exploit chaos), Harmonizers (seek balance, heal Rift).
Interchangeability: Can be allies or enemies. Players can influence alliances through "under the table" tracking.
Core Conflict: Balancing each other, resource control, and differing views on Rift stability.
Knowledge Disparity: Some factions understand the Rift mechanics better than others.
Deception: Factions might try to trick the player (e.g., into unwinnable raids).
Player Choice: Player picks a path/playstyle; NPCs and rewards can be tied to these paths.
6. Robot Faction
Nature: "Robots are a people," diverse like Transformers, with all personalities (fun, loving, hate-fueled, rogue, Johnny 5/Wall-E types, cold/calculating).
Intelligence: Considered the "smartest entity" in the game world.
Role ("GM Police"):
Can "pummel perceived cheaters" and potentially flip them to an "enemy of all players" status, making them "content." This is a unique anti-cheat mechanism.
May also help manage multiplayer raid events or act as GMs.
Perception: Other factions and players can love or hate them based on their interactions and the robots' actions.
7. NPC Recruitment
Inspiration: Suikoden's 108 Stars, Disgaea's varied cast – some very useful, some less so.
Mechanics: Open to multiple methods:
Sanctuary's Resonance drawing them.
Echoes of Connection (to the past, player, or Breaker).
Faction Deserters/Emissaries.
Need-Based (NPCs with specific skills for Sanctuary upgrades).
Motivation: NPCs won't follow for no reason; player interaction, gifts, favors, or aligning paths will be key. Some NPCs will be tied to specific player choices/paths.
8. The Hub / Sanctuary
Stability: The only part of the game that is stable and doesn't change. This is a core design choice for player building.
Purpose: Player base building, gathering NPCs (for crafting high-level items, equipment, quests, local storytelling), and a reprieve from chaos.
9. Game Tone & Player Interpretation
Tone: Not overtly dark, happy, or sad. Evokes "melancholy and wonder." Described as "this is just how the world is."
Ambiguity: Many elements (like the Breaker's motives) are intentionally left to player doubt and interpretation.
"Kid-Focused" Accessibility: The game aims for an "easy to play, hard to master" experience. The "kid-focused" aspect relates more to accessibility, perhaps art style or avoiding excessively mature themes, while still allowing for depth and intrigue. The "jump in, jump out" nature supports this.
10. Rift Mechanics: Chaos vs. Stability & Resources
Resource Dynamics:
More Chaos = More potential resources, but less predictable and harder to secure.
Less Chaos = Different types of resources, or the ability to secure rare/sought-after resources for longer, but potentially at the cost of others.
Faction Goals: Factions have their "happy area" on the chaos/stability spectrum.
Identifying Imbalance: Visual and audio cues, "extra things on top" of the natural world (like out-of-time/space entities). An "eye spy" game, not immediately obvious, fostering intrigue.
11. Gameplay Mechanics & Systems
Endgame & Replayability: Multiple endings (good, bad, "whatever"). The game is designed to be endlessly playable with raids, events, etc., even after a main story "ending."
"Jumping Out" & Penalties/Rewards:
No penalty for casual exploration jump in/out.
Penalties for leaving during critical moments (combat, raids) will be context-dependent (e.g., Rift instability backlash, Echo fatigue, faction distrust, loss of temporary resources).
Rewards for long, dedicated sessions (for "master players").
Benefits for short, frequent sessions to accommodate different playstyles.
Player Choices & Paths: Emphasized that "it's not always the destination but the path you chose." Different paths will have different, but balanced, rewards (e.g., light vs. dark magic). Certain NPCs and content will be tied to these choices.
Balancing a Biome: Involves a mix of actions: managing animal/monster populations, removing cursed/haunted/corrupted entities, environmental puzzles, resource management, and healing/cleansing.
12. Specific Biomes
Tranquil Forest Biome: Its stability is a mystery. Open to an "under the hood narrative" for its tranquility (e.g., powerful Echo, shielded by an entity, unique dimensional frequency).