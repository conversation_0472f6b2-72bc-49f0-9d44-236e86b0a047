-- classes/mage.lua
-- Mage class definition for Lazarus Arc

local Mage = {
    name = "Mage",
    description = "A master of the arcane, excelling in elemental magic while relying on intelligence over brute strength.",
    icon = "mage_icon.png",

    -- 🎭 **Playstyle & Trade-offs**
    pros = {
        "Extremely powerful ranged magic attacks",
        "Can control battlefield with AoE spells",
        "Flexible elemental specialization"
    },
    cons = {
        "Fragile; low defense and health",
        "Melee combat is extremely weak",
        "Spells require proper timing and mana management"
    },

    -- 🏋 **Base Stats & Growth**
    base_stats = { 
        health = 80, 
        maxHealth = 80,
        stamina = 50, 
        maxStamina = 50,
        mana = 120, 
        maxMana = 120,
        pattack = 3, 
        pdefense = 3, 
        mattack = 15, 
        mdefense = 7, 
        speed = 5, 
        luck = 6
    },

    stat_growth = {
        health = 5,
        stamina = 4,
        mana = 10,
        pattack = 0.2,
        pdefense = 0.2,
        mattack = 2.0,
        mdefense = 0.8,
        speed = 0.5,
        luck = 0.5
    },

    -- 🔮 **Weapon Proficiency**
    proficiencies = { 
        staff = 2.5,
        wand = 2.0,
        sword = 0.5,
        bow = 0.3,
        dagger = 0.8
    },

    -- 🌿 **Elemental Affinities**
    element_affinities = {
        fire = 10,
        water = 10,
        air = 10,
        earth = 5
    },

    -- 💥 **Overdrive Abilities**
    overdrive = { 
        "meteor_shower", 
        "arcane_cataclysm", 
        "time_stop" 
    },

    -- 🎯 **Starting Skills**
    starting_skills = {
        "fireball",
        "ice_shard",
        "arcane_bolt"
    },

    -- 📈 **Skill Progression**
    skill_tree = {
        tier1 = {
            "mana_shield",
            "elemental_mastery",
            "spell_focus"
        },
        tier2 = {
            "chain_lightning",
            "meteor_strike",
            "blink"
        },
        tier3 = {
            "dimensional_rift",
            "mana_overload",
            "arcane_surge"
        }
    },

    -- 🎭 **Prestige Paths**
    prestige_paths = {
        warlock = {
            description = "A dark caster wielding forbidden magics for immense destructive power.",
            bonuses = {
                "Life-draining spells",
                "High AoE damage",
                "Corrupts enemies over time"
            },
            weaknesses = {
                "Spells can harm the caster",
                "Vulnerable to holy magic"
            }
        },

        battlemage = {
            description = "A mage who fuses arcane power with melee combat.",
            bonuses = {
                "Can use weapons with magical enchantments",
                "Higher stamina than normal mages",
                "Balanced between offense and defense"
            },
            weaknesses = {
                "Weaker than pure melee fighters",
                "Consumes mana faster"
            }
        }
    }
}

return Mage
