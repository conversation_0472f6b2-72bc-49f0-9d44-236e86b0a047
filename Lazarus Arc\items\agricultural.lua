-- items/agricultural.lua
-- Agricultural and food-related items

local AgriculturalItems = {
    -- Food Items
    processed_food_unit = {
        id = "processed_food_unit",
        name = "Processed Food Unit",
        description = "A standardized unit of processed food, designed for efficient storage and consumption.",
        type = "food",
        rarity = 2,
        value = 15,
        weight = 1,
        stackable = true,
        maxStack = 20,
        tags = {"food", "processed"}
    },
    
    spoiled_grain = {
        id = "spoiled_grain",
        name = "Spoiled Grain",
        description = "Grain that has gone bad, potentially dangerous to consume.",
        type = "food",
        rarity = 1,
        value = 2,
        weight = 2,
        stackable = true,
        maxStack = 30,
        tags = {"food", "spoiled", "hazardous"}
    },
    
    grain_sample_bag = {
        id = "grain_sample_bag",
        name = "Grain Sample Bag",
        description = "A small bag containing grain samples for analysis.",
        type = "sample",
        rarity = 2,
        value = 10,
        weight = 0.5,
        stackable = true,
        maxStack = 15,
        tags = {"grain", "sample"}
    },

    -- Agricultural Resources
    purified_water = {
        id = "purified_water",
        name = "Purified Water",
        description = "Clean, purified water suitable for agricultural use.",
        type = "resource",
        rarity = 2,
        value = 8,
        weight = 1,
        stackable = true,
        maxStack = 30,
        tags = {"water", "purified", "agriculture"}
    },
    
    concentrated_nutrient_solution = {
        id = "concentrated_nutrient_solution",
        name = "Concentrated Nutrient Solution",
        description = "A highly concentrated solution of nutrients for hydroponic systems.",
        type = "resource",
        rarity = 2,
        value = 25,
        weight = 1,
        stackable = true,
        maxStack = 15,
        tags = {"nutrient", "hydroponics"}
    },
    
    rare_seed_sample = {
        id = "rare_seed_sample",
        name = "Rare Seed Sample",
        description = "A collection of rare or genetically modified seeds.",
        type = "seed",
        rarity = 3,
        value = 40,
        weight = 0.5,
        stackable = true,
        maxStack = 10,
        tags = {"seed", "rare"}
    },

    -- Agricultural Products
    raw_material_crate = {
        id = "raw_material_crate",
        name = "Raw Material Crate",
        description = "A crate containing raw agricultural materials.",
        type = "container",
        rarity = 2,
        value = 30,
        weight = 5,
        stackable = true,
        maxStack = 5,
        tags = {"agriculture", "raw_material"}
    },
    
    finished_product_box = {
        id = "finished_product_box",
        name = "Finished Product Box",
        description = "A box containing processed agricultural products.",
        type = "container",
        rarity = 2,
        value = 35,
        weight = 4,
        stackable = true,
        maxStack = 5,
        tags = {"agriculture", "finished_product"}
    },

    -- Animal Products
    rat_pelt = {
        id = "rat_pelt",
        name = "Rat Pelt",
        description = "The skin of a rat, potentially useful for crafting.",
        type = "material",
        rarity = 1,
        value = 5,
        weight = 0.5,
        stackable = true,
        maxStack = 20,
        tags = {"animal", "pelt", "crafting_material"}
    },

    -- Agricultural Tools
    basic_mechanical_parts = {
        id = "basic_mechanical_parts",
        name = "Basic Mechanical Parts",
        description = "Simple mechanical parts used in agricultural machinery.",
        type = "component",
        rarity = 1,
        value = 8,
        weight = 1,
        stackable = true,
        maxStack = 20,
        tags = {"mechanical", "agriculture", "basic"}
    }
}

return AgriculturalItems 