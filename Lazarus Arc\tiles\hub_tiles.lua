-- modules/hub_tiles.lua
local HubTiles = {}

-- Import all hub-related tiles
local HubFloor = require("tiles/hub_floor")
local HubGrass = require("tiles/hub_grass")
local HubPath = require("tiles/hub_path")
local SanctuaryStone = require("tiles/sanctuary_stone")

-- Register all hub-related tiles
function HubTiles.init(world)
    print("Initializing hub tiles module")
    
    -- Initialize individual tile types
    HubFloor.init(world)
    HubGrass.init(world)
    HubPath.init(world)
    SanctuaryStone.init(world)
    
    -- Register with world's tile system
    if world.tileSystem then
        world.tileSystem:registerTileType("hub_floor", HubFloor)
        world.tileSystem:registerTileType("hub_grass", HubGrass)
        world.tileSystem:registerTileType("hub_path", HubPath)
        world.tileSystem:registerTileType("sanctuary_stone", SanctuaryStone)
    end
    
    -- Register functional group for hub tiles
    if world.systems and world.systems.tile_groups then
        world.systems.tile_groups:registerTileGroup("hub", {
            "hub_floor", 
            "hub_grass", 
            "hub_path", 
            "sanctuary_stone"
        })
    end
    
    print("Hub tiles registered successfully")
end

-- Helper function to apply sanctuary level to all hub tiles in a chunk
function HubTiles.applySanctuaryLevel(chunk, level)
    if not chunk or not chunk.tiles then
        return false
    end
    
    print("Applying sanctuary level " .. level .. " to hub tiles")
    
    for _, tile in pairs(chunk.tiles) do
        if tile.type == "hub_floor" and tile.applyHubLevel then
            tile.applyHubLevel(tile, level)
        elseif tile.type == "hub_grass" and tile.applyHubLevel then
            tile.applyHubLevel(tile, level)
        elseif tile.type == "hub_path" and tile.applyHubLevel then
            tile.applyHubLevel(tile, level)
        elseif tile.type == "sanctuary_stone" and tile.applyHubLevel then
            tile.applyHubLevel(tile, level)
        end
    end
    
    return true
end

-- Helper function to upgrade a player's sanctuary
function HubTiles.upgradeSanctuary(world, player, newLevel)
    if not player or not player.hubData then
        return false, "Player has no sanctuary data"
    end
    
    local currentLevel = player.hubData.level or 1
    
    -- Check if upgrade is valid
    if newLevel <= currentLevel then
        return false, "Cannot downgrade sanctuary"
    end
    
    if newLevel > 5 then
        return false, "Maximum sanctuary level is 5"
    end
    
    -- Find the player's hub chunk
    local hubChunk = nil
    for _, chunk in pairs(world.chunks) do
        if chunk.isHub and chunk.hubOwner == player.id then
            hubChunk = chunk
            break
        end
    end
    
    if not hubChunk then
        return false, "Player's sanctuary not found"
    end
    
    -- Find the sanctuary stone
    local sanctuaryStone = nil
    for _, tile in pairs(hubChunk.tiles) do
        if tile.type == "sanctuary_stone" then
            sanctuaryStone = tile
            break
        end
    end
    
    if not sanctuaryStone then
        return false, "Sanctuary stone not found"
    end
    
    -- Perform the upgrade
    local result = SanctuaryStone.upgradeSanctuary(sanctuaryStone, player, newLevel)
    
    if result and result.success then
        -- Update player's hub data
        player.hubData.level = newLevel
        
        -- Apply the new level to all hub tiles
        HubTiles.applySanctuaryLevel(hubChunk, newLevel)
        
        -- Update chunk data
        hubChunk.hubLevel = newLevel
        
        -- Apply any post-upgrade effects to the chunk
        if world.postProcessChunk then
            world.postProcessChunk(hubChunk)
        end
        
        return true, "Sanctuary upgraded to level " .. newLevel
    else
        return false, result and result.message or "Upgrade failed"
    end
end

-- Helper function for sanctuary special events
function HubTiles.triggerSanctuaryEvent(world, player, eventType)
    if not player or not player.hubData then
        return false, "Player has no sanctuary data"
    end
    
    local hubLevel = player.hubData.level or 1
    
    -- Available event types depend on sanctuary level
    local availableEvents = {
        "memory_surge", -- Available at all levels
        "healing_wave", -- Available at all levels
        "energy_restoration" -- Available at all levels
    }
    
    if hubLevel >= 2 then
        table.insert(availableEvents, "whispers_of_past")
    end
    
    if hubLevel >= 3 then
        table.insert(availableEvents, "sanctuary_bloom")
        table.insert(availableEvents, "mana_fountain")
    end
    
    if hubLevel >= 4 then
        table.insert(availableEvents, "dimensional_ripple")
        table.insert(availableEvents, "memory_restoration")
    end
    
    if hubLevel >= 5 then
        table.insert(availableEvents, "time_reflection")
        table.insert(availableEvents, "sanctuary_awakening")
    end
    
    -- Check if requested event is available
    local eventAllowed = false
    for _, event in ipairs(availableEvents) do
        if event == eventType then
            eventAllowed = true
            break
        end
    end
    
    if not eventAllowed then
        return false, "Event '" .. eventType .. "' not available at sanctuary level " .. hubLevel
    end
    
    -- Find the player's hub chunk
    local hubChunk = nil
    for _, chunk in pairs(world.chunks) do
        if chunk.isHub and chunk.hubOwner == player.id then
            hubChunk = chunk
            break
        end
    end
    
    if not hubChunk then
        return false, "Player's sanctuary not found"
    end
    
    -- Trigger the event
    print("Triggering sanctuary event: " .. eventType)
    
    -- Event effects
    if eventType == "memory_surge" then
        -- Enable memory fragments to appear
        for _, tile in pairs(hubChunk.tiles) do
            if (tile.type == "hub_floor" or tile.type == "hub_grass") and math.random() < 0.2 then
                local worldX = hubChunk.x * world.CHUNK_SIZE + tile.x
                local worldY = hubChunk.y * world.CHUNK_SIZE + tile.y
                
                world.entitySystem:addEntity("memory_fragment", worldX, worldY, {
                    owner = player.id,
                    intensity = hubLevel * 0.2,
                    duration = 300
                })
            end
        end
        
        return true, "Memory fragments have appeared throughout your sanctuary"
        
    elseif eventType == "healing_wave" then
        -- Set healing effect on all tiles
        for _, tile in pairs(hubChunk.tiles) do
            tile.temporaryHealing = hubLevel * 2
            tile.healingDuration = 300
        end
        
        return true, "A wave of healing energy flows through your sanctuary"
        
    elseif eventType == "sanctuary_bloom" then
        -- Make special flowers bloom
        for _, tile in pairs(hubChunk.tiles) do
            if tile.type == "hub_grass" and math.random() < 0.4 then
                local worldX = hubChunk.x * world.CHUNK_SIZE + tile.x
                local worldY = hubChunk.y * world.CHUNK_SIZE + tile.y
                
                world.entitySystem:addEntity("sanctuary_flower", worldX, worldY, {
                    owner = player.id,
                    level = hubLevel,
                    duration = 600
                })
            end
        end
        
        return true, "Your sanctuary blooms with magical flowers"
    end
    
    -- Default case
    return false, "Event logic not implemented"
end

return HubTiles