local Pigeon = {
    id = "pigeon",
    name = "Pigeon",
    type = "bird",
    -- PLACEHOLDER: Basic shape for visual representation
    shape = {
        {0, -1}, {0.7, -0.5}, {1, 0}, {0.7, 0.5},
        {0, 1}, {-0.7, 0.5}, {-1, 0}, {-0.7, -0.5}
    },
    size = 6,

    -- Entity categories
    categories = {"animal", "bird", "flying", "urban"},
    
    -- Threat and food categories
    threatCategories = {"predator", "player"},
    foodCategories = {"seed", "food", "insect"},
    
    -- Stats
    maxHealth = 15,
    health = 15,
    maxStamina = 40,
    stamina = 40,
    speed = 1.8,
    
    -- Flight properties
    flight = {
        maxHeight = 8,
        minHeight = 1,
        ascentSpeed = 0.8,
        descentSpeed = 1.2,
        hoverHeight = 2,
        currentHeight = 2,
        wingFlapRate = 0.2
    },
    
    -- Behaviors
    behaviors = {"flock", "forage", "roost", "scavenge"},
    
    -- Behavior configurations
    behaviorConfigs = {
        flock = {
            moveSpeed = 2.0,
            followDistance = 3,
            separationDistance = 1.5,
            alignmentStrength = 0.2,
            cohesionStrength = 0.3,
            maxFlockSize = 20
        },
        forage = {
            moveSpeed = 1.2,
            searchRadius = 8,
            forageTime = {2, 5},
            foodTypes = {"seed", "food", "insect"},
            groundForage = true
        },
        roost = {
            startTime = "dusk",
            endTime = "dawn",
            roostHeight = {2, 5},
            healthRegen = 0.02,
            staminaRegen = 0.05
        },
        scavenge = {
            moveSpeed = 1.5,
            searchRadius = 10,
            scavengeTime = {1, 3},
            foodTypes = {"food", "seed"},
            alertRadius = 6
        }
    },
    
    -- Special abilities
    abilities = {
        quickFlight = {
            speedBoost = 1.5,
            duration = 1.5,
            cooldown = 4,
            staminaCost = 8
        },
        coo = {
            range = 6,
            duration = 0.5,
            cooldown = 2,
            effect = "attract"
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "pigeon",
        scale = 0.6,
        animations = {
            "idle", "walk", "fly", "forage", "coo"
        },
        variants = {
            "rock", "wood", "feral", "white"
        }
    },
    
    -- Sound effects (uses flute instrument for light, airy sounds)
    sounds = {
        coo = "pigeon_coo",
        wingFlap = "pigeon_wing_flap",
        call = "pigeon_call",
        footstep = "pigeon_footstep",
        hurt = "pigeon_hurt",
        death = "pigeon_death",
        idle = "pigeon_idle",
        chirp = "pigeon_chirp",
        takeoff = "pigeon_takeoff"
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.4, quantity = {1, 1}},
        {id = "feather", chance = 0.6, quantity = {1, 2}},
        {id = "egg", chance = 0.2, quantity = {1, 1}}
    }
}

-- Initialize the entity
function Pigeon.init(entity, world)
    -- Copy all fields from Pigeon template to entity instance
    for k, v in pairs(Pigeon) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random pigeon variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Pigeon.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update flight properties
    if entity.flight then
        -- Update wing flapping
        if entity.flight.currentHeight > entity.flight.minHeight then
            entity.flight.wingFlapRate = 0.2
        else
            entity.flight.wingFlapRate = 0.4
        end
    end
end

return Pigeon 