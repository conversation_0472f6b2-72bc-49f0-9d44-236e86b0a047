local ExplosiveTrap = {
    id = "explosive_trap",
    name = "Explosive Trap",
    type = "trap",
    
    -- Entity categories
    categories = {"trap", "dungeon", "explosive"},
    
    -- Target categories
    targetCategories = {"player", "monster"},
    
    -- Stats
    maxHealth = 25,
    health = 25,
    duration = 0, -- Instant effect
    cooldown = 0, -- No cooldown needed as it's destroyed
    
    -- Trap properties
    properties = {
        radius = 2.5,
        setupTime = 3,
        isArmed = false,
        isTriggered = false,
        destroyOnActivate = true,
        damage = 45,
        knockbackForce = 3.0,
        burnDuration = 4,
        burnDamage = 3
    },
    
    -- Appearance
    appearance = {
        sprite = "explosive_trap",
        scale = 1.0,
        animations = {
            "idle", "trigger", "explosion"
        },
        variants = {
            "powder", "crystal", "rune", "magic"
        }
    },
    
    -- Sound effects
    sounds = {
        trigger = "explosive_trap_trigger",
        explosion = "explosive_trap_explosion",
        burn = "explosive_trap_burn"
    },
    
    -- Effects
    effects = {
        explosion = {
            type = "area",
            radius = 2.5,
            effects = {
                damage = 45,
                knockback = 3.0,
                burn = {
                    type = "status",
                    duration = 4,
                    effects = {
                        damage = 3,
                        interval = 1
                    }
                }
            }
        }
    }
}

-- Initialize the trap
function ExplosiveTrap.init(entity, world)
    -- Copy all fields from ExplosiveTrap template to entity instance
    for k, v in pairs(ExplosiveTrap) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position
    entity.position = entity.position or {x = 0, y = 0}

    -- Set random variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize trap state
    entity.properties.isArmed = false
    entity.properties.isTriggered = false
    entity.properties.triggerTime = 0

    return entity
end

-- Update the trap
function ExplosiveTrap.update(entity, world, dt)
    -- Check if trap is armed and not triggered
    if entity.properties.isArmed and not entity.properties.isTriggered then
        -- Check for targets in radius
        if world and world.entities then
            for _, target in ipairs(world.entities) do
                if target.position and target.categories then
                    -- Check if target is in trap categories
                    local isTarget = false
                    for _, category in ipairs(entity.targetCategories) do
                        if table.contains(target.categories, category) then
                            isTarget = true
                            break
                        end
                    end
                    
                    if isTarget then
                        local distance = math.sqrt(
                            (target.position.x - entity.position.x)^2 + 
                            (target.position.y - entity.position.y)^2
                        )
                        
                        if distance <= entity.properties.radius then
                            -- Trigger trap
                            entity.properties.isTriggered = true
                            entity.properties.triggerTime = world.time
                            
                            -- Create explosion effect
                            if world.createEffect then
                                world.createEffect({
                                    type = "explosion",
                                    position = entity.position,
                                    radius = entity.properties.radius,
                                    effects = entity.effects.explosion.effects
                                })
                            end
                            
                            -- Apply effects to all targets in radius
                            for _, affected in ipairs(world.entities) do
                                if affected.position then
                                    local affectedDistance = math.sqrt(
                                        (affected.position.x - entity.position.x)^2 + 
                                        (affected.position.y - entity.position.y)^2
                                    )
                                    
                                    if affectedDistance <= entity.properties.radius then
                                        -- Apply damage
                                        if affected.takeDamage then
                                            affected.takeDamage(entity.properties.damage)
                                        end
                                        
                                        -- Apply knockback
                                        if affected.applyForce then
                                            local angle = math.atan2(
                                                affected.position.y - entity.position.y,
                                                affected.position.x - entity.position.x
                                            )
                                            local force = {
                                                x = math.cos(angle) * entity.properties.knockbackForce,
                                                y = math.sin(angle) * entity.properties.knockbackForce
                                            }
                                            affected.applyForce(force)
                                        end
                                        
                                        -- Apply burn effect
                                        if affected.applyEffect then
                                            affected.applyEffect(entity.effects.explosion.effects.burn)
                                        end
                                    end
                                end
                            end
                            
                            -- Play trigger sound
                            if world.playSound then
                                world.playSound(entity.sounds.trigger)
                            end
                            
                            -- Destroy trap if configured
                            if entity.properties.destroyOnActivate then
                                if world.removeEntity then
                                    world.removeEntity(entity)
                                end
                            end
                            
                            break
                        end
                    end
                end
            end
        end
    end
end

-- Arm the trap
function ExplosiveTrap.arm(entity, world)
    if not entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = true
        entity.properties.triggerTime = world.time
        return true
    end
    return false
end

-- Disarm the trap
function ExplosiveTrap.disarm(entity, world)
    if entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = false
        entity.properties.triggerTime = world.time
        return true
    end
    return false
end

return ExplosiveTrap 