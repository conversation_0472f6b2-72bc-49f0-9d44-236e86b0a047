-- behaviors/swarming.lua
-- Swarming behavior typically for insects or small entities moving in dense groups

local SwarmingBehavior = {}  -- create an empty table

SwarmingBehavior.id = "swarming"
SwarmingBehavior.name = "Swarming Behavior"

-- Configuration parameters
SwarmingBehavior.defaultConfig = {
    neighborRadius = 4,              -- Radius to find neighbors
    separationDistance = 0.5,        -- Minimum distance between swarm entities
    cohesionWeight = 0.1,            -- Attraction to swarm center
    alignmentWeight = 0.6,           -- Alignment with neighbor movement
    separationWeight = 0.8,          -- Avoid overcrowding
    maxSpeed = 3.0,                  -- Maximum movement speed
    rapidChangeDirectionChance = 0.4,-- Frequent random changes in direction
    predatorAvoidance = true,        -- Whether swarm avoids predators
    predatorAvoidRadius = 10,        -- Detection radius for predators
    obstacleAvoidance = true,        -- Whether to avoid obstacles
    obstacleAvoidanceRadius = 1      -- Radius to detect obstacles
}

-- Initialize the behavior
function SwarmingBehavior.init(entity, customConfig)
    entity.swarmingState = entity.swarmingState or {
        neighbors = {},
        avgPosition = {x = 0, y = 0},
        avgDirection = {x = 0, y = 0},
        config = {}
    }
    
    local config = entity.swarmingState.config
    for k, v in pairs(SwarmingBehavior.defaultConfig) do
        config[k] = v
    end
    
    if customConfig then
        for k, v in pairs(customConfig) do
            config[k] = v
        end
    end
end

-- Update function called every game tick
function SwarmingBehavior.update(entity, world, dt)
    local state = entity.swarmingState
    local config = state.config

    -- Find neighbors
    state.neighbors = SwarmingBehavior.findNeighbors(entity, world)

    -- Calculate group properties
    SwarmingBehavior.calculateGroupProperties(entity)

    -- Compute swarming forces
    local cohesionForce = SwarmingBehavior.calculateCohesionForce(entity)
    local alignmentForce = SwarmingBehavior.calculateAlignmentForce(entity)
    local separationForce = SwarmingBehavior.calculateSeparationForce(entity)

    -- Combine forces and apply them to velocity
    entity.velocity.x = entity.velocity.x +
        (cohesionForce.x * config.cohesionWeight +
         alignmentForce.x * config.alignmentWeight +
         separationForce.x * config.separationWeight) * dt

    entity.velocity.y = entity.velocity.y +
        (cohesionForce.y * config.cohesionWeight +
         alignmentForce.y * config.alignmentWeight +
         separationForce.y * config.separationWeight) * dt

    -- Limit the speed
    local speed = math.sqrt(entity.velocity.x^2 + entity.velocity.y^2)
    if speed > config.maxSpeed then
        entity.velocity.x = entity.velocity.x / speed * config.maxSpeed
        entity.velocity.y = entity.velocity.y / speed * config.maxSpeed
    end

    -- Avoid obstacles if needed
    if config.obstacleAvoidance then
        SwarmingBehavior.avoidObstacles(entity, world)
    end

    -- Random rapid direction changes for swarming
    if math.random() < config.rapidChangeDirectionChance * dt then
        entity.velocity.x = (math.random() * 2 - 1) * config.maxSpeed
        entity.velocity.y = (math.random() * 2 - 1) * config.maxSpeed
    end
end

-- Placeholder for finding neighbors
function SwarmingBehavior.findNeighbors(entity, world)
    -- Implementation details...
end

-- Placeholder for calculating group properties
function SwarmingBehavior.calculateGroupProperties(entity)
    local state = entity.swarmingState
    local avgPosition = {x = 0, y = 0}
    local avgDirection = {x = 0, y = 0}
    local neighborCount = 0

    for _, neighbor in ipairs(state.neighbors) do
        avgPosition.x = avgPosition.x + neighbor.position.x
        avgPosition.y = avgPosition.y + neighbor.position.y
        avgDirection.x = avgDirection.x + neighbor.velocity.x
        avgDirection.y = avgDirection.y + neighbor.velocity.y
        neighborCount = neighborCount + 1
    end

    if neighborCount > 0 then
        avgPosition.x = avgPosition.x / neighborCount
        avgPosition.y = avgPosition.y / neighborCount
        avgDirection.x = avgDirection.x / neighborCount
        avgDirection.y = avgDirection.y / neighborCount
    end

    state.avgPosition = avgPosition
    state.avgDirection = avgDirection
end

-- Placeholder for calculating cohesion force
function SwarmingBehavior.calculateCohesionForce(entity)
    local state = entity.swarmingState
    local force = {x = 0, y = 0}
    if #state.neighbors > 0 then
        force.x = state.avgPosition.x - entity.position.x
        force.y = state.avgPosition.y - entity.position.y
    end
    return force
end

-- Placeholder for calculating alignment force
function SwarmingBehavior.calculateAlignmentForce(entity)
    local state = entity.swarmingState
    local force = {x = 0, y = 0}
    if #state.neighbors > 0 then
        force.x = state.avgDirection.x - entity.velocity.x
        force.y = state.avgDirection.y - entity.velocity.y
    end
    return force
end

-- Placeholder for calculating separation force
function SwarmingBehavior.calculateSeparationForce(entity)
    local state = entity.swarmingState
    local config = state.config
    local force = {x = 0, y = 0}

    for _, neighbor in ipairs(state.neighbors) do
        local dx = entity.position.x - neighbor.position.x
        local dy = entity.position.y - neighbor.position.y
        local distSq = dx * dx + dy * dy
        if distSq < config.separationDistance * config.separationDistance then
            local dist = math.sqrt(distSq)
            force.x = force.x + dx / dist
            force.y = force.y + dy / dist
        end
    end

    return force
end

-- Placeholder for obstacle avoidance
function SwarmingBehavior.avoidObstacles(entity, world)
    local state = entity.swarmingState
    local config = state.config
    local pos = entity.position
    local chunkSystem = world.chunkSystem

    local tileAheadX = pos.x + entity.velocity.x * config.obstacleAvoidanceRadius
    local tileAheadY = pos.y + entity.velocity.y * config.obstacleAvoidanceRadius
    local tileAhead = chunkSystem:getTileAt(tileAheadX, tileAheadY)

    if tileAhead then
        local tileModule = world.modules.tiles[tileAhead.type]
        if tileModule and not tileModule.passable then
            local newDirection = {
                x = -entity.velocity.x + math.random() * 0.2 - 0.1,
                y = -entity.velocity.y + math.random() * 0.2 - 0.1
            }
            local dirLength = math.sqrt(newDirection.x^2 + newDirection.y^2)
            if dirLength > 0 then
                entity.velocity.x = newDirection.x / dirLength * config.maxSpeed
                entity.velocity.y = newDirection.y / dirLength * config.maxSpeed
            end
        end
    end
end

return SwarmingBehavior
