local ShadowDagger = {
    id = "shadow_dagger",
    name = "Shadow Dagger",
    type = "weapon",
    category = "dagger",
    
    -- Properties
    properties = {
        damage = 25,
        attackSpeed = 1.8,
        range = 1.5,
        durability = 120,
        maxDurability = 120,
        stealthBonus = 0.3,
        stealthDuration = 2.0,
        stealthCooldown = 8.0,
        criticalChance = 0.25,
        criticalMultiplier = 2.5,
        backstabMultiplier = 3.0,
        shadowDamage = 15,
        shadowDuration = 3,
        shadowRadius = 2.0,
        shadowSpeed = 0.8,
        shadowChance = 0.2,
        shadowBurstChance = 0.15,
        shadowBurstDamage = 20,
        shadowBurstRadius = 3.0,
        shadowBurstCount = 3,
        shadowCharge = 0,
        maxShadowCharge = 100,
        chargeRate = 5,
        dischargeRate = 10,
        shadowResistance = 0.7,
        lightLevel = 0.3
    },
    
    -- Appearance
    appearance = {
        sprite = "shadow_dagger",
        scale = 1.0,
        animations = {
            "idle",
            "stab",
            "stealth",
            "burst"
        },
        variants = {
            "void",
            "night",
            "phantom",
            "shade"
        },
        blendMode = "multiply",
        tint = {0.2, 0.2, 0.3},
        alpha = 0.9
    },
    
    -- Sound effects
    sounds = {
        stab = "shadow_dagger_stab",
        stealth = "shadow_dagger_stealth",
        burst = "shadow_dagger_burst",
        critical = "shadow_dagger_critical"
    },
    
    -- Effects
    effects = {
        shadow = {
            type = "status",
            duration = 3,
            effects = {
                damage = 15,
                slow = 0.3,
                radius = 2.0
            }
        },
        stealth = {
            type = "status",
            duration = 2,
            effects = {
                invisibility = true,
                speed = 1.2
            }
        },
        shadowBurst = {
            type = "status",
            duration = 0.5,
            effects = {
                damage = 20,
                radius = 3.0
            }
        }
    }
}

-- Initialize the weapon
function ShadowDagger.init(weapon, world)
    -- Copy all fields from ShadowDagger template to weapon instance
    for k, v in pairs(ShadowDagger) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        shadowCharge = 0,
        lastChargeTime = 0,
        lastStealthTime = 0,
        isStealthed = false
    }

    return weapon
end

-- Update the weapon
function ShadowDagger.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Update shadow charge
        if world.lightLevel and world.lightLevel < 0.5 then
            local chargeFactor = (1 - world.lightLevel) * dt
            weapon.properties.weaponState.shadowCharge = math.min(weapon.properties.maxShadowCharge,
                weapon.properties.weaponState.shadowCharge + chargeFactor * weapon.properties.chargeRate)
        else
            weapon.properties.weaponState.shadowCharge = math.max(0,
                weapon.properties.weaponState.shadowCharge - weapon.properties.dischargeRate * dt)
        end
        
        -- Update stealth state
        if weapon.properties.weaponState.isStealthed then
            if world.time - weapon.properties.weaponState.lastStealthTime > weapon.properties.stealthDuration then
                weapon.properties.weaponState.isStealthed = false
            end
        end
    end
end

-- Handle attack
function ShadowDagger.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Calculate damage
    local damage = weapon.properties.damage
    
    -- Check for critical hit
    if math.random() < weapon.properties.criticalChance then
        damage = damage * weapon.properties.criticalMultiplier
        
        -- Play critical sound
        if world.playSound then
            world.playSound(weapon.sounds.critical)
        end
    end
    
    -- Check for backstab
    if target.direction then
        local angle = math.atan2(
            target.position.y - attacker.position.y,
            target.position.x - attacker.position.x
        )
        local targetAngle = math.atan2(
            target.direction.y,
            target.direction.x
        )
        local angleDiff = math.abs(angle - targetAngle)
        
        if angleDiff > math.pi * 0.7 then
            damage = damage * weapon.properties.backstabMultiplier
        end
    end
    
    -- Apply damage
    if target.takeDamage then
        target.takeDamage(damage)
    end
    
    -- Check for shadow effect
    if math.random() < weapon.properties.shadowChance then
        -- Create shadow effect
        if world.createEffect then
            world.createEffect({
                type = "shadow",
                position = target.position,
                radius = weapon.properties.shadowRadius,
                duration = weapon.properties.shadowDuration
            })
        end
        
        -- Apply shadow effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.shadow)
        end
    end
    
    -- Check for shadow burst
    if math.random() < weapon.properties.shadowBurstChance then
        -- Create shadow burst effect
        if world.createEffect then
            world.createEffect({
                type = "shadow_burst",
                position = target.position,
                radius = weapon.properties.shadowBurstRadius,
                duration = 0.5
            })
        end
        
        -- Create shadow projectiles
        for i = 1, weapon.properties.shadowBurstCount do
            local angle = (i - 1) * (2 * math.pi / weapon.properties.shadowBurstCount)
            
            if world.createProjectile then
                world.createProjectile({
                    type = "shadow_shard",
                    position = target.position,
                    direction = {
                        x = math.cos(angle) * weapon.properties.shadowSpeed,
                        y = math.sin(angle) * weapon.properties.shadowSpeed
                    },
                    damage = weapon.properties.shadowBurstDamage,
                    range = weapon.properties.shadowBurstRadius
                })
            end
        end
        
        -- Play burst sound
        if world.playSound then
            world.playSound(weapon.sounds.burst)
        end
    end
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function ShadowDagger.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough shadow charge
    if weapon.properties.weaponState.shadowCharge < 50 then
        return false
    end
    
    -- Check stealth cooldown
    if world.time - weapon.properties.weaponState.lastStealthTime < weapon.properties.stealthCooldown then
        return false
    end
    
    -- Apply stealth effect
    if attacker.applyEffect then
        attacker.applyEffect(weapon.effects.stealth)
    end
    
    -- Create stealth effect
    if world.createEffect then
        world.createEffect({
            type = "stealth",
            position = attacker.position,
            radius = weapon.properties.shadowRadius * 2,
            duration = weapon.properties.stealthDuration
        })
    end
    
    -- Update stealth state
    weapon.properties.weaponState.isStealthed = true
    weapon.properties.weaponState.lastStealthTime = world.time
    
    -- Play stealth sound
    if world.playSound then
        world.playSound(weapon.sounds.stealth)
    end
    
    -- Reset shadow charge
    weapon.properties.weaponState.shadowCharge = 0
    weapon.properties.weaponState.lastChargeTime = world.time
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 3)
    
    return true
end

return ShadowDagger 