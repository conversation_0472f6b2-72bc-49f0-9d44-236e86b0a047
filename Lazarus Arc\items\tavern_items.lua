-- items/tavern_items.lua
-- Tavern-related items for food, drink, and quests

local TavernItems = {
    -- Common Items
    ale = {
        id = "ale",
        name = "Mug of Ale",
        category = "drink",
        description = "A refreshing mug of ale",
        value = 2,
        weight = 1.0,
        stackable = true,
        maxStack = 10,
        properties = {
            material = "glass",
            quality = 1.0,
            durability = 1,
            effect = "refresh"
        },
        onUse = function(user, target)
            -- Restores some stamina
            return true
        end
    },
    
    wine = {
        id = "wine",
        name = "Bottle of Wine",
        category = "drink",
        description = "A bottle of fine wine",
        value = 5,
        weight = 1.0,
        stackable = true,
        maxStack = 10,
        properties = {
            material = "glass",
            quality = 1.0,
            durability = 1,
            effect = "refresh"
        },
        onUse = function(user, target)
            -- Restores some stamina
            return true
        end
    },
    
    food = {
        id = "food",
        name = "Tavern Meal",
        category = "food",
        description = "A hearty meal served at the tavern",
        value = 3,
        weight = 1.0,
        stackable = true,
        maxStack = 10,
        properties = {
            material = "food",
            quality = 1.0,
            durability = 1,
            effect = "satiate"
        },
        onUse = function(user, target)
            -- Restores some health
            return true
        end
    },
    
    coin = {
        id = "coin",
        name = "Coin",
        category = "currency",
        description = "A standard coin used for trade",
        value = 1,
        weight = 0.1,
        stackable = true,
        maxStack = 999,
        properties = {
            material = "gold",
            quality = 1.0,
            durability = 100
        },
        onUse = function(user, target)
            -- Can be used for trading
            return true
        end
    },
    
    -- Uncommon Items
    rare_wine = {
        id = "rare_wine",
        name = "Rare Vintage Wine",
        category = "drink",
        description = "A bottle of rare and valuable wine",
        value = 50,
        weight = 1.0,
        stackable = true,
        maxStack = 5,
        properties = {
            material = "glass",
            quality = 2.0,
            durability = 1,
            effect = "refresh_enhanced"
        },
        onUse = function(user, target)
            -- Restores more stamina
            return true
        end
    },
    
    exotic_food = {
        id = "exotic_food",
        name = "Exotic Cuisine",
        category = "food",
        description = "A rare and exotic meal",
        value = 30,
        weight = 1.0,
        stackable = true,
        maxStack = 5,
        properties = {
            material = "food",
            quality = 2.0,
            durability = 1,
            effect = "satiate_enhanced"
        },
        onUse = function(user, target)
            -- Restores more health
            return true
        end
    },
    
    treasure_map = {
        id = "treasure_map",
        name = "Treasure Map",
        category = "quest",
        description = "A map leading to hidden treasure",
        value = 100,
        weight = 0.5,
        stackable = false,
        properties = {
            material = "parchment",
            quality = 1.5,
            durability = 50,
            quest = "treasure_hunt"
        },
        onUse = function(user, target)
            -- Starts a treasure hunt quest
            return true
        end
    },
    
    quest_item = {
        id = "quest_item",
        name = "Quest Item",
        category = "quest",
        description = "An item needed for a quest",
        value = 0,
        weight = 1.0,
        stackable = false,
        properties = {
            material = "various",
            quality = 1.0,
            durability = 100,
            quest = "various"
        },
        onUse = function(user, target)
            -- Used for quest completion
            return true
        end
    },
    
    -- Rare Items
    legendary_wine = {
        id = "legendary_wine",
        name = "Legendary Vintage",
        category = "drink",
        description = "A legendary bottle of wine with magical properties",
        value = 200,
        weight = 1.0,
        stackable = true,
        maxStack = 3,
        properties = {
            material = "glass",
            quality = 3.0,
            durability = 1,
            effect = "refresh_legendary",
            magical = true
        },
        onUse = function(user, target)
            -- Restores significant stamina and provides temporary buffs
            return true
        end
    },
    
    magical_food = {
        id = "magical_food",
        name = "Magical Feast",
        category = "food",
        description = "A magical meal with extraordinary effects",
        value = 150,
        weight = 1.0,
        stackable = true,
        maxStack = 3,
        properties = {
            material = "food",
            quality = 3.0,
            durability = 1,
            effect = "satiate_legendary",
            magical = true
        },
        onUse = function(user, target)
            -- Restores significant health and provides temporary buffs
            return true
        end
    },
    
    ancient_map = {
        id = "ancient_map",
        name = "Ancient Treasure Map",
        category = "quest",
        description = "An ancient map leading to legendary treasure",
        value = 500,
        weight = 0.5,
        stackable = false,
        properties = {
            material = "ancient_parchment",
            quality = 2.5,
            durability = 100,
            quest = "legendary_treasure_hunt"
        },
        onUse = function(user, target)
            -- Starts a legendary treasure hunt quest
            return true
        end
    },
    
    rare_artifact = {
        id = "rare_artifact",
        name = "Rare Artifact",
        category = "quest",
        description = "A valuable and mysterious artifact",
        value = 300,
        weight = 2.0,
        stackable = false,
        properties = {
            material = "ancient_metal",
            quality = 2.5,
            durability = 200,
            quest = "artifact_collection",
            magical = true
        },
        onUse = function(user, target)
            -- Used for high-level quest completion
            return true
        end
    }
}

return TavernItems 