local IronHandAxe = {
    id = "iron_hand_axe",
    name = "Iron Hand Axe",
    type = "weapon",
    category = "axe",
    
    -- Properties
    properties = {
        damage = 24,
        attackSpeed = 1.1,
        range = 1.4,
        durability = 160,
        maxDurability = 160,
        chopPower = 1.3,
        chopRadius = 0.9,
        chopSpeed = 0.9,
        woodcutting = 1.2,
        armorPenetration = 0.3,
        weight = 0.9,
        momentum = 0,
        maxMomentum = 3,
        momentumPower = 1.2
    },
    
    -- Appearance
    appearance = {
        sprite = "iron_hand_axe",
        scale = 1.0,
        animations = {
            "idle",
            "chop",
            "swing",
            "heavy"
        },
        variants = {
            "iron",
            "steel",
            "bronze",
            "copper"
        },
        blendMode = "normal",
        tint = {0.7, 0.7, 0.7},
        alpha = 1.0
    },
    
    -- Sound effects
    sounds = {
        chop = "axe_chop",
        swing = "axe_swing",
        woodcut = "axe_woodcut",
        impact = "axe_impact"
    },
    
    -- Effects
    effects = {
        chop = {
            type = "status",
            duration = 0.4,
            effects = {
                damage = 24,
                chop = true,
                radius = 0.9
            }
        },
        momentum = {
            type = "status",
            duration = 0.4,
            effects = {
                damage = 1.2
            }
        }
    }
}

-- Initialize the weapon
function IronHandAxe.init(weapon, world)
    -- Copy all fields from IronHandAxe template to weapon instance
    for k, v in pairs(IronHandAxe) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        momentum = 0,
        lastMomentumTime = 0
    }

    return weapon
end

-- Update the weapon
function IronHandAxe.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Decay momentum
        if world.time - weapon.properties.weaponState.lastMomentumTime > 1.5 then
            weapon.properties.weaponState.momentum = 0
        end
    end
end

-- Handle attack
function IronHandAxe.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Calculate chop direction
    local angle = math.atan2(
        target.position.y - attacker.position.y,
        target.position.x - attacker.position.x
    )
    
    -- Create chop effect
    if world.createEffect then
        world.createEffect({
            type = "chop",
            position = attacker.position,
            angle = angle,
            range = weapon.properties.range,
            duration = 0.4
        })
    end
    
    -- Check for chop effect
    if math.random() < weapon.properties.chopPower / weapon.properties.damage then
        -- Create chop effect
        if world.createEffect then
            world.createEffect({
                type = "chop",
                position = target.position,
                radius = weapon.properties.chopRadius,
                duration = 0.4
            })
        end
        
        -- Apply chop effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.chop)
        end
        
        -- Play chop sound
        if world.playSound then
            world.playSound(weapon.sounds.chop)
        end
        
        -- Check if target is wood
        if target.type == "wood" then
            -- Apply woodcutting effect
            if target.applyEffect then
                target.applyEffect({
                    type = "woodcut",
                    duration = 0.4,
                    effects = {
                        damage = weapon.properties.woodcutting
                    }
                })
            end
            
            -- Play woodcut sound
            if world.playSound then
                world.playSound(weapon.sounds.woodcut)
            end
        end
    end
    
    -- Update momentum
    if world.time - weapon.properties.weaponState.lastMomentumTime <= 1.5 then
        weapon.properties.weaponState.momentum = math.min(weapon.properties.maxMomentum,
            weapon.properties.weaponState.momentum + 1)
    else
        weapon.properties.weaponState.momentum = 1
    end
    weapon.properties.weaponState.lastMomentumTime = world.time
    
    -- Apply momentum effect if active
    if weapon.properties.weaponState.momentum > 1 then
        if target.applyEffect then
            target.applyEffect(weapon.effects.momentum)
        end
    end
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function IronHandAxe.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough momentum
    if weapon.properties.weaponState.momentum < 2 then
        return false
    end
    
    -- Create heavy chop effect
    if world.createEffect then
        world.createEffect({
            type = "heavy_chop",
            position = attacker.position,
            angle = math.atan2(
                target.position.y - attacker.position.y,
                target.position.x - attacker.position.x
            ),
            range = weapon.properties.range * 1.2,
            duration = 0.6
        })
    end
    
    -- Apply chop effect with momentum
    if target.applyEffect then
        target.applyEffect(weapon.effects.chop)
        target.applyEffect(weapon.effects.momentum)
    end
    
    -- Reset momentum
    weapon.properties.weaponState.momentum = 0
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 2)
    
    return true
end

return IronHandAxe 