-- Entity definition with category system
-- Enhanced deer with variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local Deer = {
    id = "deer",
    name = "Deer",
    type = "deer",
    shape = {
        {0, -1}, {0.8, -0.8}, {1, 0}, {0.8, 0.8},
        {0, 1}, {-0.8, 0.8}, {-1, 0}, {-0.8, -0.8}
    },
    size = 12,
    
    -- Entity categories
    categories = {"animal", "prey", "mammal", "large"},
    
    -- Threat categories
    threatCategories = {"predator", "player"},
    
    -- Stats
    maxHealth = 60,
    health = 60,
    maxStamina = 80,
    stamina = 80,
    speed = 2.8,
    
    -- Behaviors
    behaviors = {"flee", "wander", "graze", "herd"},
    
    -- Behavior configurations
    behaviorConfigs = {
        flee = {
            moveSpeed = 4.5,
            detectionRadius = 15,
            panicDuration = 4,
            zigzagChance = 0.2,
            jumpChance = 0.3
        },
        wander = {
            moveSpeed = 2.0,
            changeDirectionChance = 0.03,
            wanderRadius = 10
        },
        graze = {
            moveSpeed = 0.5,
            grazeRadius = 5,
            grazeTime = {8, 15}
        },
        herd = {
            moveSpeed = 2.5,
            followDistance = 5,
            separationDistance = 3,
            alignmentStrength = 0.3,
            cohesionStrength = 0.2
        }
    },
    
    -- Special abilities
    abilities = {
        jump = {
            height = 2,
            distance = 4,
            cooldown = 3.0
        },
        alert = {
            range = 20,
            duration = 5,
            cooldown = 10
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "deer",
        scale = 1.2,
        animations = {
            "idle", "walk", "run", "graze", "jump", "alert"
        },
        variants = {
            "brown", "white", "spotted", "dark"
        }
    },
    
    -- Sound effects with synth configuration
    sounds = {
        alert = {
            file = "deer_alert",
            synth = {
                instrument = "classical_guitar",
                notes = {"A4", "F4"},
                durations = {0.2, 0.3},
                volume = 0.35,
                vibrato = true,
                vibratoRate = 5.0
            }
        },
        grunt = {
            file = "deer_grunt",
            synth = {
                instrument = "cello",
                notes = {"D3", "G3"},
                durations = {0.3, 0.2},
                volume = 0.3
            }
        },
        snort = {
            file = "deer_snort",
            synth = {
                instrument = "harmonica",
                notes = {"F4"},
                duration = 0.15,
                volume = 0.3
            }
        },
        footstep = {
            synth = {
                instrument = "xylophone",
                notes = {"G3"},
                duration = 0.1,
                volume = 0.18
            }
        },
        run = {
            synth = {
                instrument = "marimba",
                notes = {"G3", "A3", "G3", "A3"},
                durations = {0.08, 0.08, 0.08, 0.08},
                volume = 0.25
            }
        },
        hurt = {
            synth = {
                instrument = "classical_guitar",
                notes = {"D#4"},
                duration = 0.3,
                volume = 0.4,
                vibrato = true,
                vibratoRate = 8.0
            }
        },
        death = {
            synth = {
                instrument = "cello",
                notes = {"A3", "F3", "D3"},
                durations = {0.4, 0.4, 0.8},
                volume = 0.4,
                vibrato = true,
                vibratoRate = 2.5
            }
        },
        call = {
            synth = {
                instrument = "classical_guitar",
                notes = {"C4", "A3"},
                durations = {0.4, 0.5},
                volume = 0.35,
                vibrato = true,
                vibratoRate = 4.0
            }
        }
    },
    
    -- === ENHANCED VARIANT SYSTEM ===
    variantChances = {
        normal = 0.75,          -- 75% white-tailed deer
        shiny = 0.18,           -- 18% golden deer (shiny)
        rare = 0.06,            -- 6% stag lord (rare)
        legendary = 0.01        -- 1% forest guardian (legendary)
    },

    variants = {
        normal = {
            name = "White-tailed Deer",
            description = "A common forest deer with a white tail",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.9, 0.8, 0.7, 1.0}  -- Natural brown
            }
        },

        shiny = {
            name = "Golden Deer",
            description = "A magnificent deer with golden fur and antlers",
            statModifiers = {
                maxHealth = 1.4,    -- 84 health instead of 60
                speed = 1.3,        -- 3.64 speed instead of 2.8
                maxStamina = 1.3,   -- 104 stamina instead of 80
                grace = 1.5         -- Enhanced jumping
            },
            appearanceModifiers = {
                scale = 1.15,
                glow = true,
                colorTint = {1.4, 1.2, 0.6, 1.0},  -- Golden fur
                golden_antlers = true,
                majestic_presence = true
            },
            soundModifiers = {
                pitch = 1.2,
                volume = 1.1,
                melodic = true
            }
        },

        rare = {
            name = "Stag Lord",
            description = "A powerful stag with massive antlers and commanding presence",
            statModifiers = {
                maxHealth = 1.8,    -- 108 health
                speed = 1.2,        -- 3.36 speed
                maxStamina = 1.6,   -- 128 stamina
                antler_power = 2.0,
                leadership = 1.8
            },
            appearanceModifiers = {
                scale = 1.3,
                colorTint = {0.7, 0.6, 0.5, 1.0},  -- Dark majestic brown
                massive_antlers = true,
                regal_bearing = true,
                forest_crown = true
            },
            soundModifiers = {
                pitch = 0.8,
                volume = 1.4,
                reverb = true,
                commanding = true
            }
        },

        legendary = {
            name = "Forest Guardian",
            description = "A mystical deer spirit that protects the ancient woods",
            statModifiers = {
                maxHealth = 2.5,    -- 150 health
                speed = 1.5,        -- 4.2 speed
                maxStamina = 2.2,   -- 176 stamina
                nature_magic = 5.0,
                forest_connection = 10.0
            },
            appearanceModifiers = {
                scale = 1.5,
                glow = true,
                colorTint = {0.8, 1.3, 0.8, 1.0},  -- Mystical green glow
                spirit_antlers = true,
                nature_aura = "legendary",
                ethereal_presence = true,
                seasonal_changes = true
            },
            soundModifiers = {
                pitch = 0.9,
                volume = 1.6,
                reverb = true,
                echo = true,
                nature_harmony = true
            }
        }
    },

    -- === ENHANCED DROP SYSTEM ===

    -- Base drops (available from all variants)
    baseDrops = {
        {id = "meat", chance = 0.9, quantity = {2, 4}},
        {id = "hide", chance = 0.8, quantity = {1, 2}},
        {id = "venison", chance = 0.7, quantity = {1, 2}}
    },

    -- Variant-specific drops
    variantDrops = {
        normal = {
            {id = "antler", chance = 0.4, quantity = {1, 1}}
        },
        shiny = {
            {id = "golden_antler", chance = 0.9, quantity = {1, 2}},
            {id = "golden_deer_hide", chance = 0.8, quantity = {1, 1}},
            {id = "grace_essence", chance = 0.6, quantity = {1, 1}},
            {id = "majestic_fur", chance = 0.7, quantity = {1, 2}}
        },
        rare = {
            {id = "stag_lord_antlers", chance = 0.9, quantity = {1, 1}},
            {id = "royal_deer_hide", chance = 0.8, quantity = {1, 1}},
            {id = "leadership_essence", chance = 0.7, quantity = {1, 1}},
            {id = "forest_crown_fragment", chance = 0.5, quantity = {1, 1}},
            {id = "commanding_presence", chance = 0.4, quantity = {1, 1}}
        },
        legendary = {
            {id = "guardian_spirit_antlers", chance = 0.95, quantity = {1, 1}},
            {id = "forest_guardian_hide", chance = 0.9, quantity = {1, 1}},
            {id = "nature_blessing_essence", chance = 0.8, quantity = {1, 2}},
            {id = "seasonal_spirit_orb", chance = 0.7, quantity = {1, 1}},
            {id = "ancient_forest_heart", chance = 0.5, quantity = {1, 1}},
            {id = "guardian_protection_charm", chance = 0.6, quantity = {1, 1}}
        }
    }
}

-- Initialize the entity using enhanced template
function Deer.init(entity, world)
    -- Copy all fields from Deer template to entity instance
    for k, v in pairs(Deer) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return Deer