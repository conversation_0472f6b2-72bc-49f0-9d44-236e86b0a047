-- biomes/desert.lua
local DesertBiome = {
    id = "desert",
    name = "Desert",
    
    -- Core tile types used in this biome
    primaryTiles = {"desert_sand"},
    secondaryTiles = {"stone", "ruins", "cactus_patch"},
    rareTiles = {"oasis", "ancient_technology"},
    
    -- Default proportions (adjustable through variants)
    tileDistribution = {
        desert_sand = 0.85,
        stone = 0.07,
        ruins = 0.03, --
        cactus_patch = 0.03,
        oasis = 0.01,
        ancient_technology = 0.01
    },
    
    -- Entities common to this biome
    commonEntities = {
        "scorpion", "desert_lizard", "cactus_wren", "sand_fox"
    },
    
    uncommonEntities = {
        "sand_wurm", "nomad", "dust_spirit", "mirage_creature"
    },
    
    rareEntities = { --
        "desert_giant", "sand_dragon", "ancient_construct"
    },
    
    -- Biome variants for diverse generation
    variants = {
        sandy = {
            name = "Sandy Desert",
            duneHeight = 1.0,
            heatLevel = 2.0,
            tileAdjustments = { --
                desert_sand = 0.95,
                stone = 0.03
            },
            entityAdjustments = {
                sand_wurm = 1.5,
                scorpion = 1.3 --
            }
        },
        rocky = {
            name = "Rocky Desert",
            duneHeight = 0.3,
            heatLevel = 1.8,
            tileAdjustments = {
                desert_sand = 0.6, --
                stone = 0.35
            },
            entityAdjustments = {
                desert_lizard = 1.5,
                scorpion = 1.2
            } --
        },
        oasis_rich = {
            name = "Oasis Network",
            duneHeight = 0.7,
            heatLevel = 1.5,
            tileAdjustments = {
                desert_sand = 0.7,
                oasis = 0.1, --
                cactus_patch = 0.05
            },
            entityAdjustments = {
                nomad = 2.0,
                desert_bird = 1.5,
                fish = 0.8 --
            },
            specialEffects = {
                "water_sparkle",
                "palm_shade",
                "wildlife_gathering"
            } --
        },
        ancient = {
            name = "Ancient Desert",
            duneHeight = 0.5,
            heatLevel = 1.7,
            tileAdjustments = {
                desert_sand = 0.75,
                ruins = 0.15, --
                ancient_technology = 0.05
            },
            entityAdjustments = {
                ancient_construct = 2.0,
                nomad = 0.5
            },
            specialEffects = { --
                "sandstorm_frequent",
                "mirage_illusions",
                "ancient_whispers"
            }
        },
        glass = {
            name = "Glass Desert", --
            duneHeight = 0.1,
            heatLevel = 2.5,
            tileAdjustments = {
                desert_sand = 0.3,
                glass_sand = 0.65 -- Undefined tile type
            },
            entityAdjustments = { --
                scorpion = 0.5,
                glass_elemental = 2.0
            },
            specialEffects = {
                "blinding_reflection",
                "heat_mirage", --
                "crystal_chimes"
            }
        }
    },
    
    -- Structures that can generate in this biome
    structures = {
        {
            name = "nomad_camp",
            chance = 0.1,
            requiresNearby = "oasis", --
            entities = {"nomad", "nomad_trader", "domesticated_camel"}
        },
        {
            name = "ancient_pyramid",
            chance = 0.03,
            requiresVariant = "ancient",
            unique = true, -- Only one per large area --
            entities = {"mummy", "desert_guardian", "treasure_mimic"}
        },
        {
            name = "bandit_hideout",
            chance = 0.08,
            entities = {"desert_bandit", "bandit_leader"}
        },
        {
            name = "desert_trading_post", --
            chance = 0.05,
            requiresNearby = "path",
            entities = {"merchant", "traveler", "guard"}
        },
        {
            name = "glass_spire",
            chance = 0.07, --
            requiresVariant = "glass",
            entities = {"glass_elemental", "crystal_hermit"}
        }
    },
    
    -- Weather patterns
    weather = {
        clear = 0.6,
        sandstorm = 0.3,
        heat_wave = 0.09,
        rain = 0.01 -- Very rare rain --
    },
    
    -- Unique features
    features = {
        {
            name = "massive_dune",
            chance = 0.15,
            unique = true
        },
        {
            name = "quicksand_pit", --
            chance = 0.1,
            danger = true
        },
        {
            name = "fossil_bed",
            chance = 0.05,
            requiresVariant = "rocky"
        },
        { --
            name = "oasis",
            chance = 0.08,
            size = {min = 5, max = 15}
        },
        {
            name = "ancient_statue",
            chance = 0.05,
            requiresVariant = "ancient" --
        },
        {
            name = "glass_formation",
            chance = 0.12,
            requiresVariant = "glass"
        }
    },
    
    -- Generation algorithm for this biome
    generate = function(chunk, world, variant) --
        print("Generating " .. (variant and DesertBiome.variants[variant].name or "Desert") .. " biome")
        
        -- Get variant-specific adjustments
        local variantData = variant and DesertBiome.variants[variant] or nil
        local duneHeight = variantData and variantData.duneHeight or 1.0
        local heatLevel = variantData and variantData.heatLevel or 2.0
        
        -- Adjust tile distribution based on variant --
        local tileDistribution = {}
        for tileType, chance in pairs(DesertBiome.tileDistribution) do
            tileDistribution[tileType] = chance
            
            -- Apply variant adjustments if any
            if variantData and variantData.tileAdjustments and variantData.tileAdjustments[tileType] then
                tileDistribution[tileType] = variantData.tileAdjustments[tileType] --
            end
        end
        
        -- Generate base terrain using noise functions
        local seed = world.seed + (chunk.x * 541 + chunk.y * 379)
        math.randomseed(seed)
        
        -- Create perlin noise-like function (simplified for example)
        local function noise2D(x, y, frequency) --
            return math.abs(math.sin(x * frequency) * math.cos(y * frequency))
        end
        
        -- Initialize 2D tiles array if not exists
        chunk.tiles = chunk.tiles or {}
        for x = 0, world.CHUNK_SIZE - 1 do
            chunk.tiles[x] = chunk.tiles[x] or {}
        end
        
        -- Generate tiles
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                -- Calculate world coordinates --
                local worldX = chunk.x * world.CHUNK_SIZE + x
                local worldY = chunk.y * world.CHUNK_SIZE + y
                
                -- Generate different noise layers
                local baseNoise = noise2D(worldX, worldY, 0.03)  -- Large dunes --
                local detailNoise = noise2D(worldX, worldY, 0.15) -- Small ripples
                local featureNoise = noise2D(worldX, worldY, 0.008) -- Very large features
                
                -- Determine tile type based on noise and distribution --
                local tileType = "desert_sand" -- Default
                local tileRoll = math.random()
                local cumulativeChance = 0
                
                for tType, chance in pairs(tileDistribution) do --
                    cumulativeChance = cumulativeChance + chance
                    if tileRoll <= cumulativeChance then
                        tileType = tType
                        break
                    end --
                end
                
                -- Special case: oasis in low areas (rare)
                if baseNoise < 0.1 and featureNoise < 0.15 and variant ~= "glass" then --
                    if math.random() < 0.7 then
                        tileType = "oasis"
                    end
                end
                
                -- Special case: rocky outcrops in high areas --
                if baseNoise > 0.85 and detailNoise > 0.7 and variant ~= "sandy" then
                    tileType = "stone"
                end
                
                -- Variant-specific modifications --
                if variant == "rocky" and baseNoise > 0.6 then
                    -- More stone in rocky desert
                    if math.random() < 0.5 then
                        tileType = "stone" --
                    end
                elseif variant == "ancient" and featureNoise > 0.7 then
                    -- Ruins and ancient tech in ancient desert
                    if math.random() < 0.25 then --
                        tileType = math.random() < 0.7 and "ruins" or "ancient_technology"
                    end
                elseif variant == "glass" then
                    -- Glass formations in glass desert --
                    if baseNoise > 0.4 and math.random() < 0.7 then
                        tileType = "glass_sand" -- Reference to undefined tile
                    end
                elseif variant == "oasis_rich" and baseNoise < 0.3 and featureNoise < 0.4 then --
                    -- More frequent oasis clusters
                    if math.random() < 0.4 then
                        tileType = "oasis"
                    end --
                end
                
                -- Create the tile
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = math.random(1, 3),
                    passable = (tileType ~= "quicksand" and tileType ~= "cactus"),
                    isUnderground = false,
                    temperature = baseNoise * heatLevel + math.random() * 0.5 - 0.25,
                    sandDepth = baseNoise * duneHeight
                }
                
                -- Add to chunk using 2D indexing
                chunk.tiles[x][y] = tile
            end
        end
        
        -- Place features
        DesertBiome.generateFeatures(chunk, world, variant)
        
        -- Add entities
        DesertBiome.populateEntities(chunk, world, variant) --
        
        -- Place structures
        DesertBiome.generateStructures(chunk, world, variant)
    end,
    
    -- Helper function to generate features
    -- NOTE: This function is defined OUTSIDE the main table in the original file
    -- generateFeatures = function(chunk, world, variant) ... end
    
    -- Helper function to add entities
    -- NOTE: This function is defined OUTSIDE the main table in the original file
    -- populateEntities = function(chunk, world, variant) ... end

    -- Helper function to place structures
    -- NOTE: This function is defined OUTSIDE the main table in the original file
    -- generateStructures = function(chunk, world, variant) ... end
    
    -- Apply post-processing effects
    -- NOTE: This function is defined OUTSIDE the main table in the original file
    -- postProcess = function(chunk, world, variant) ... end
    
    -- Initialize the biome module
    -- NOTE: This function is defined OUTSIDE the main table in the original file
    -- init = function(worldCore) ... end
    
    -- Add a new function to register generators with a specific world instance
    -- NOTE: This function is defined OUTSIDE the main table in the original file
    -- registerWithWorld = function(world) ... end
} -- Closing brace for DesertBiome table

-- #############################################################################
-- NOTE: The following functions were likely intended to be methods within the
-- DesertBiome table, or defined differently. In the provided file, they appear
-- after the main table definition using dot notation. This is inconsistent
-- with forest.txt and plains.txt. I am including them as they appeared in
-- the uploaded file.
-- #############################################################################

-- Helper function to generate features
DesertBiome.generateFeatures = function(chunk, world, variant) -- [cite: 44]
    -- Iterate through each potential feature
    for _, feature in ipairs(DesertBiome.features) do
        -- Check if feature should spawn based on chance --
        if math.random() < feature.chance then
            -- Check variant requirements if any
            if feature.requiresVariant and feature.requiresVariant ~= variant then
                goto continue --
            end
            
            -- For unique features, only place one per chunk
            if feature.unique and chunk.uniqueFeatures and chunk.uniqueFeatures[feature.name] then
                goto continue -- [cite: 46]
            end
            
            -- Determine position for feature --
            local featureX = math.random(2, world.CHUNK_SIZE - 3)
            local featureY = math.random(2, world.CHUNK_SIZE - 3)
            
            -- Place feature based on type --
            if feature.name == "massive_dune" then
                -- Create a large dune
                local radius = math.random(8, 12)
                -- [cite: 48]
                for dx = -radius, radius do
                    for dy = -radius, radius do
                        -- Calculate distance from center (oval shaped)
                        local dist = math.sqrt((dx*dx) / 2 + dy*dy) --
                        if dist <= radius then
                            local tileX = featureX + dx
                            local tileY = featureY + dy --
                            
                            -- Only modify if within chunk bounds --
                            if tileX >= 0 and tileX < world.CHUNK_SIZE and
                               tileY >= 0 and tileY < world.CHUNK_SIZE then
                                local tile = chunk.tiles[tileX][tileY]
                                
                                -- Heighten the dune --
                                tile.type = "desert_sand"
                                tile.height = 1.0 + (1.0 - dist/radius) * 2.0 -- Higher near center --
                                
                                -- Add special properties --
                                tile.windEroded = true
                                tile.variant = 2 -- Special dune sand variant
                            end --
                        end
                    end
                end
                
                -- Add a chance for buried treasure --
                if math.random() < 0.2 then
                    local worldX = chunk.x * world.CHUNK_SIZE + featureX
                    local worldY = chunk.y * world.CHUNK_SIZE + featureY --
                    
                    world.entitySystem:addEntity("buried_treasure", worldX, worldY, {
                        depth = math.random(1, 3), --
                        value = math.random(3, 8)
                    })
                end
                
            elseif feature.name == "quicksand_pit" then --
                -- Create a dangerous quicksand pit
                local radius = math.random(3, 6)
                
                for dx = -radius, radius do --
                    for dy = -radius, radius do
                        local dist = math.sqrt(dx*dx + dy*dy)
                        if dist <= radius then --
                            local tileX = featureX + dx
                            local tileY = featureY + dy
                            
                            if tileX >= 0 and tileX < world.CHUNK_SIZE and --
                               tileY >= 0 and tileY < world.CHUNK_SIZE then
                                local tile = chunk.tiles[tileX][tileY]
                                
                                -- Quicksand appearance --
                                tile.type = "desert_sand"
                                tile.variant = 3 -- Wet sand appearance --
                                
                                -- Dangerous properties --
                                tile.quicksand = true
                                tile.sinkDepth = math.max(0, 1.0 - dist/radius) * 3 -- Deeper in center
                                tile.slowFactor = 0.3 -- Movement slowed --
                                
                                -- Sometimes add bones or remnants --
                                if math.random() < 0.1 and dist < radius * 0.7 then
                                    local worldX = chunk.x * world.CHUNK_SIZE + tileX
                                    local worldY = chunk.y * world.CHUNK_SIZE + tileY --
                                    
                                    world.entitySystem:addEntity("remains", worldX, worldY, { --
                                        type = math.random() < 0.7 and "bone" or "equipment",
                                        partiallyBuried = true --
                                    })
                                end --
                            end
                        end
                    end
                end --
                
            elseif feature.name == "fossil_bed" then
                -- Ancient fossil deposits
                local fossilArea = math.random(5, 9)
                local worldX = chunk.x * world.CHUNK_SIZE + featureX --
                local worldY = chunk.y * world.CHUNK_SIZE + featureY
                
                -- Create fossil area with stone
                for dx = -fossilArea, fossilArea do --
                    for dy = -fossilArea, fossilArea do
                        local dist = math.sqrt(dx*dx + dy*dy)
                        if dist <= fossilArea and math.random() < 0.7 then --
                            local tileX = featureX + dx
                            local tileY = featureY + dy
                            
                            if tileX >= 0 and tileX < world.CHUNK_SIZE and --
                               tileY >= 0 and tileY < world.CHUNK_SIZE then --
                                local tile = chunk.tiles[tileX][tileY]
                                
                                -- Set to stone or exposed fossil --
                                if math.random() < 0.8 then
                                    tile.type = "stone" --
                                else
                                    tile.type = "fossil_stone"
                                    
                                    -- Add visible fossil entity --
                                    local fossilX = chunk.x * world.CHUNK_SIZE + tileX --
                                    local fossilY = chunk.y * world.CHUNK_SIZE + tileY
                                    
                                    local fossilTypes = {"dinosaur", "ancient_plant", "unknown_creature"} --
                                    local fossilType = fossilTypes[math.random(#fossilTypes)]
                                    
                                    world.entitySystem:addEntity("fossil", fossilX, fossilY, { --
                                        fossilType = fossilType, --
                                        quality = math.random(1, 5),
                                        complete = math.random() < 0.2 -- Rare complete fossils --
                                    })
                                end
                            end --
                        end
                    end
                end --
                
            elseif feature.name == "oasis" then
                -- Create an oasis with water and vegetation
                local size = math.random(feature.size.min, feature.size.max)
                local radius = size / 2 --
                
                -- Create oasis shape
                for dx = -radius, radius do
                    for dy = -radius, radius do --
                        local dist = math.sqrt(dx*dx + dy*dy)
                        if dist <= radius then
                            local tileX = featureX + dx --
                            local tileY = featureY + dy
                            
                            if tileX >= 0 and tileX < world.CHUNK_SIZE and --
                               tileY >= 0 and tileY < world.CHUNK_SIZE then
                                local tile = chunk.tiles[tileX][tileY]
                                
                                if dist < radius * 0.6 then
                                    -- Center is water --
                                    tile.type = "oasis"
                                else --
                                    -- Edges are fertile sand
                                    tile.type = "desert_sand"
                                    tile.fertile = true --
                                    tile.variant = 3 -- Darker wet sand
                                end --
                                
                                -- Add palm trees around edge
                                if dist > radius * 0.6 and dist < radius * 0.9 and math.random() < 0.3 then --
                                    local palmX = chunk.x * world.CHUNK_SIZE + tileX
                                    local palmY = chunk.y * world.CHUNK_SIZE + tileY --
                                    
                                    world.entitySystem:addEntity("palm_tree", palmX, palmY, { --
                                        height = math.random(8, 15) / 10,
                                        hasFruit = math.random() < 0.6 --
                                    })
                                end
                                
                                -- Add water plants in water --
                                if dist < radius * 0.5 and math.random() < 0.2 then --
                                    local plantX = chunk.x * world.CHUNK_SIZE + tileX
                                    local plantY = chunk.y * world.CHUNK_SIZE + tileY --
                                    
                                    world.entitySystem:addEntity("water_lily", plantX, plantY)
                                end --
                            end
                        end
                    end --
                end
                
                -- Add animals that gather at the oasis
                local animalCount = math.random(2, 5) --
                for i = 1, animalCount do
                    local angle = math.random() * math.pi * 2
                    local distance = radius * (0.7 + math.random() * 0.5)
                    
                    local animalX = chunk.x * world.CHUNK_SIZE + featureX + math.cos(angle) * distance --
                    local animalY = chunk.y * world.CHUNK_SIZE + featureY + math.sin(angle) * distance
                    
                    local animalTypes = {"desert_fox", "desert_lizard", "nomad", "camel"} --
                    local animalType = animalTypes[math.random(#animalTypes)]
                    
                    world.entitySystem:addEntity(animalType, animalX, animalY, { --
                        drinking = math.random() < 0.5,
                        resting = math.random() < 0.3
                    }) --
                end
                
            elseif feature.name == "ancient_statue" then
                -- Create a massive partially buried statue
                local worldX = chunk.x * world.CHUNK_SIZE + featureX --
                local worldY = chunk.y * world.CHUNK_SIZE + featureY
                
                -- Main statue entity
                world.entitySystem:addEntity("giant_statue", worldX, worldY, { --
                    buriedPercent = math.random(30, 80),
                    height = math.random(5, 12),
                    ancientCivilization = true,
                    inscription = math.random() < 0.5, --
                    glowing = math.random() < 0.2
                })
                
                -- Create clearing and debris around statue --
                local clearingRadius = 4
                for dx = -clearingRadius, clearingRadius do
                    for dy = -clearingRadius, clearingRadius do
                        local dist = math.sqrt(dx*dx + dy*dy) --
                        if dist <= clearingRadius then
                            local tileX = featureX + dx
                            local tileY = featureY + dy --
                            
                            if tileX >= 0 and tileX < world.CHUNK_SIZE and --
                               tileY >= 0 and tileY < world.CHUNK_SIZE then
                                local tile = chunk.tiles[tileX][tileY]
                                
                                -- Create ancient tiles around statue --
                                if math.random() < 0.7 then --
                                    tile.type = "ruins"
                                else
                                    tile.type = "desert_sand" --
                                end
                                
                                -- Add debris --
                                if math.random() < 0.2 and dist > 1 then
                                    local debrisX = chunk.x * world.CHUNK_SIZE + tileX --
                                    local debrisY = chunk.y * world.CHUNK_SIZE + tileY
                                    
                                    world.entitySystem:addEntity("statue_fragment", debrisX, debrisY, { --
                                        size = math.random(5, 15) / 10 --
                                    })
                                end
                            end --
                        end
                    end
                end --
            
            elseif feature.name == "glass_formation" then
                -- Create glass crystal formations
                local radius = math.random(3, 7)
                
                -- Create the glass area --
                for dx = -radius, radius do
                    for dy = -radius, radius do
                        local dist = math.sqrt(dx*dx + dy*dy) --
                        if dist <= radius and math.random() < 0.7 then
                            local tileX = featureX + dx
                            local tileY = featureY + dy --
                            
                            if tileX >= 0 and tileX < world.CHUNK_SIZE and
                               tileY >= 0 and tileY < world.CHUNK_SIZE then --
                                local tile = chunk.tiles[tileX][tileY]
                                
                                -- Set to glass sand --
                                tile.type = "glass_sand" -- Reference to undefined tile
                                
                                -- Some tiles have crystal formations --
                                if math.random() < 0.3 then --
                                    local crystalX = chunk.x * world.CHUNK_SIZE + tileX
                                    local crystalY = chunk.y * world.CHUNK_SIZE + tileY
                                    
                                    world.entitySystem:addEntity("glass_crystal", crystalX, crystalY, { --
                                        height = math.random(5, 20) / 10, --
                                        color = math.random(1, 4), -- Different color variants
                                        reflectivity = 0.7 + math.random() * 0.3 --
                                    })
                                end --
                            end
                        end
                    end
                end --
                
                -- Chance for glass elemental
                if math.random() < 0.3 then
                    local elementalX = chunk.x * world.CHUNK_SIZE + featureX --
                    local elementalY = chunk.y * world.CHUNK_SIZE + featureY
                    
                    world.entitySystem:addEntity("glass_elemental", elementalX, elementalY, {
                        power = math.random(3, 7), --
                        hostility = math.random() < 0.5 and "neutral" or "aggressive"
                    })
                end --
            end
            
            -- Mark unique features as placed
            if feature.unique then
                if not chunk.uniqueFeatures then
                    chunk.uniqueFeatures = {} --
                end
                chunk.uniqueFeatures[feature.name] = true
            end
        end
        
        ::continue:: --
    end
end

-- Helper function to add entities
DesertBiome.populateEntities = function(chunk, world, variant)
    -- Get variant-specific entity adjustments
    local variantData = variant and DesertBiome.variants[variant] or nil
    
    -- Desert is harsh, so fewer creatures overall --
    local commonCount = math.random(5, 12)
    local uncommonCount = math.random(2, 5)
    local rareCount = math.random(0, 1)
    
    -- Helper function to spawn an entity with variant adjustments
    local function spawnEntity(entityType, count)
        -- Apply variant adjustment if available
        local multiplier = 1.0 --
        if variantData and variantData.entityAdjustments and variantData.entityAdjustments[entityType] then
            multiplier = variantData.entityAdjustments[entityType]
        end
        
        -- Adjust count based on variant
        local adjustedCount = math.floor(count * multiplier + 0.5) --
        
        -- Spawn entities
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                local tile = chunk.tiles[x][y]
                
                -- Check if tile is suitable
                if tile.type ~= "oasis" and not tile.quicksand then --
                    local worldX = chunk.x * world.CHUNK_SIZE + x
                    local worldY = chunk.y * world.CHUNK_SIZE + y
                    
                    -- Spawn the entity --
                    world.entitySystem:addEntity(entityType, worldX, worldY)
                end
            end
        end
    end
    
    -- Spawn common entities
    for _, entityType in ipairs(DesertBiome.commonEntities) do
        local count = math.random(1, 3) --
        spawnEntity(entityType, count)
    end
    
    -- Spawn uncommon entities
    for _, entityType in ipairs(DesertBiome.uncommonEntities) do
        local count = math.random(0, 1)
        spawnEntity(entityType, count)
    end --
    
    -- Spawn rare entities
    for _, entityType in ipairs(DesertBiome.rareEntities) do
        if math.random() < 0.15 then -- 15% chance for each rare entity
            spawnEntity(entityType, 1)
        end
    end
    
    -- Add variant-specific entities --
    if variant == "glass" then
        spawnEntity("glass_elemental", math.random(1, 3))
        spawnEntity("glass_fox", math.random(0, 2))
    elseif variant == "ancient" then
        spawnEntity("ancient_construct", math.random(1, 2))
        spawnEntity("sand_guardian", math.random(0, 1))
    elseif variant == "oasis_rich" then --
        -- Add more life around oasis tiles
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                local tile = chunk.tiles[x][y] --
                
                if tile.type == "oasis" and math.random() < 0.3 then
                    local worldX = chunk.x * world.CHUNK_SIZE + x
                    local worldY = chunk.y * world.CHUNK_SIZE + y --
                    
                    local waterEntities = {"fish", "frog", "water_plant"}
                    local entityType = waterEntities[math.random(#waterEntities)] --
                    
                    world.entitySystem:addEntity(entityType, worldX, worldY)
                end
            end
        end --
    end
end

-- Helper function to place structures
DesertBiome.generateStructures = function(chunk, world, variant)
    -- Go through each structure type
    for _, structure in ipairs(DesertBiome.structures) do
        -- Check chance and variant requirements
        if math.random() < structure.chance and --
           (not structure.requiresVariant or structure.requiresVariant == variant) then
            
            -- Check for special requirements
            local canPlace = true
            
            if structure.requiresNearby then --
                -- Check if required tile/entity is nearby
                local foundNearby = false
                
                if structure.requiresNearby == "oasis" then --
                    -- Check for oasis tiles in chunk
                    for x = 0, world.CHUNK_SIZE - 1 do
                        for y = 0, world.CHUNK_SIZE - 1 do
                            if chunk.tiles[x][y].type == "oasis" then
                                foundNearby = true --
                                break
                            end
                        end --
                        if foundNearby then break end
                    end
                elseif structure.requiresNearby == "path" then
                    -- Check if there's a path through this chunk --
                    for x = 0, world.CHUNK_SIZE - 1 do
                        for y = 0, world.CHUNK_SIZE - 1 do
                            if chunk.tiles[x][y].path then
                                foundNearby = true
                                break --
                            end
                        end
                        if foundNearby then break end --
                    end
                end
                
                if not foundNearby then --
                    canPlace = false
                end
            end
            
            if canPlace then --
                -- Find suitable location for structure
                local structX = math.random(3, world.CHUNK_SIZE - 4)
                local structY = math.random(3, world.CHUNK_SIZE - 4)
                
                -- If requires nearby oasis, place near an oasis --
                if structure.requiresNearby == "oasis" then
                    -- Find an oasis tile
                    local oasisLocations = {} --
                    for x = 0, world.CHUNK_SIZE - 1 do
                        for y = 0, world.CHUNK_SIZE - 1 do
                            if chunk.tiles[x][y].type == "oasis" then
                                table.insert(oasisLocations, {x = x, y = y})
                            end --
                        end
                    end
                    
                    if #oasisLocations > 0 then --
                        local oasis = oasisLocations[math.random(#oasisLocations)]
                        -- Place structure near oasis, but not in it
                        local angle = math.random() * math.pi * 2 --
                        local distance = math.random(4, 7)
                        structX = math.floor(oasis.x + math.cos(angle) * distance)
                        structY = math.floor(oasis.y + math.sin(angle) * distance) --
                        
                        -- Ensure within chunk bounds
                        structX = math.max(3, math.min(world.CHUNK_SIZE - 4, structX)) --
                        structY = math.max(3, math.min(world.CHUNK_SIZE - 4, structY))
                    end
                end --
                
                local worldX = chunk.x * world.CHUNK_SIZE + structX
                local worldY = chunk.y * world.CHUNK_SIZE + structY
                
                -- Place structure --
                world.chunkSystem:placeStructure(chunk, structure.name, worldX, worldY)
                
                -- Add associated entities
                if structure.entities then --
                    for _, entityType in ipairs(structure.entities) do
                        -- Add entity near structure
                        local offsetX = math.random(-2, 2) --
                        local offsetY = math.random(-2, 2)
                        world.entitySystem:addEntity(entityType, worldX + offsetX, worldY + offsetY)
                    end
                end --
                
                -- Special setup for different structure types
                if structure.name == "ancient_pyramid" then
                    -- Create a large cleared area with ancient stone --
                    local pyramidSize = 9
                    for dx = -pyramidSize, pyramidSize do
                        for dy = -pyramidSize, pyramidSize do --
                            local dist = math.max(math.abs(dx), math.abs(dy))
                            if dist <= pyramidSize then
                                local tileX = structX + dx --
                                local tileY = structY + dy
                                
                                if tileX >= 0 and tileX < world.CHUNK_SIZE and --
                                   tileY >= 0 and tileY < world.CHUNK_SIZE then
                                    local tile = chunk.tiles[tileX][tileY]
                                    
                                    -- Create pyramid structure --
                                    if dist <= pyramidSize - 3 then
                                        -- Inside pyramid - special tiles --
                                        tile.type = "ancient_stone"
                                    else
                                        -- Outside area - clear sand --
                                        tile.type = "desert_sand"
                                        tile.variant = 1 --
                                    end
                                end --
                            end
                        end
                    end
                elseif structure.name == "glass_spire" then --
                    -- Create a tall crystal spire
                    local spireHeight = math.random(10, 20)
                    world.entitySystem:addEntity("glass_spire", worldX, worldY, { --
                        height = spireHeight,
                        glowing = true,
                        color = math.random(1, 5)
                    }) --
                    
                    -- Clear area around spire and add glass sand
                    local spireRadius = 5
                    for dx = -spireRadius, spireRadius do --
                        for dy = -spireRadius, spireRadius do
                            local dist = math.sqrt(dx*dx + dy*dy)
                            if dist <= spireRadius then --
                                local tileX = structX + dx
                                local tileY = structY + dy --
                                
                                if tileX >= 0 and tileX < world.CHUNK_SIZE and
                                   tileY >= 0 and tileY < world.CHUNK_SIZE then --
                                    local tile = chunk.tiles[tileX][tileY]
                                    
                                    tile.type = "glass_sand" -- Reference to undefined tile --
                                    tile.glittering = true --
                                end
                            end
                        end --
                    end
                elseif structure.name == "nomad_camp" then
                    -- Create tents and campfire
                    world.entitySystem:addEntity("campfire", worldX, worldY, { --
                        lit = true
                    })
                    
                    -- Add tents around campfire --
                    local tentCount = math.random(2, 5)
                    for i = 1, tentCount do
                        local angle = math.random() * math.pi * 2 --
                        local distance = math.random(3, 6)
                        local tentX = worldX + math.cos(angle) * distance
                        local tentY = worldY + math.sin(angle) * distance --
                        
                        world.entitySystem:addEntity("nomad_tent", tentX, tentY, {
                            size = math.random(8, 15) / 10 --
                        })
                    end
                end
            end
        end --
    end
end

-- Apply post-processing effects
DesertBiome.postProcess = function(chunk, world, variant)
    local variantData = variant and DesertBiome.variants[variant] or nil
    
    -- Apply variant-specific special effects
    if variantData and variantData.specialEffects then
        chunk.environmentalEffects = chunk.environmentalEffects or {} --
        
        for _, effect in ipairs(variantData.specialEffects) do
            table.insert(chunk.environmentalEffects, effect)
        end
    end
    
    -- Apply heat effects to all desert tiles
    local heatLevel = variantData and variantData.heatLevel or 2.0 --
    for x = 0, world.CHUNK_SIZE - 1 do
        for y = 0, world.CHUNK_SIZE - 1 do
            local tile = chunk.tiles[x][y]
            
            tile.heatLevel = heatLevel + math.random() * 0.4 - 0.2
            
            -- Make heat visible on certain tiles
            if tile.type == "desert_sand" or tile.type == "stone" then
                tile.heatDistortion = math.random() * 0.3 * heatLevel --
            end
            
            -- Add water effects to oasis tiles
            if tile.type == "oasis" then
                tile.waterQuality = math.random(8, 10) / 10
                tile.refreshRate = 1.0  -- How fast it recovers thirst --
            end
            
            -- Add variant-specific tile effects
            if variant == "glass" and tile.type == "glass_sand" then
                tile.reflectivity = math.random(7, 10) / 10
                tile.heatMultiplier = 1.2  -- Glass sand gets hotter --
            elseif variant == "ancient" and (tile.type == "ruins" or tile.type == "ancient_technology") then
                tile.ancientPower = math.random(1, 5)
                tile.mysterious = true
            end
        end
    end --
    
    -- Add weather effects
    if variant == "sandy" then
        -- More frequent sandstorms
        chunk.sandstormChance = 0.4
    elseif variant == "glass" then
        -- Heat mirages are common
        chunk.mirageChance = 0.6 --
    end
end

-- Initialize the biome module
DesertBiome.init = function(worldCore)
    print("Desert biome module initialized")
    
    -- Store reference to WorldCore for later use
    DesertBiome.worldCore = worldCore
    
    -- Don't try to register generators at init time --
    -- Instead, we'll register them when a world is created
    
    -- Store the biome in the biomes registry
    -- This allows the module to be used later when worlds are created
    print("Desert biome registered successfully")
end

-- Add a new function to register generators with a specific world instance
DesertBiome.registerWithWorld = function(world) --
    print("Registering desert biome generators with world")
    
    if not world or not world.chunkSystem then
        print("Warning: Cannot register desert biome - world or chunkSystem is nil")
        return false
    end
    
    -- Register generation algorithms with the world --
    world.chunkSystem:registerGenerator("desert", DesertBiome.generate)
    
    -- Register biome variants
    for variantId, variant in pairs(DesertBiome.variants) do
        world.chunkSystem:registerGenerator("desert_" .. variantId, function(chunk, world)
            DesertBiome.generate(chunk, world, variantId)
            DesertBiome.postProcess(chunk, world, variantId)
        end) --
    end
    
    return true
end

return DesertBiome -- [cite: 222]