local OverdriveAbility = {}

OverdriveAbility.name = "Blood Frenzy"
OverdriveAbility.effect = "lifesteal"
OverdriveAbility.duration = 10
OverdriveAbility.percentage = 50  -- Converts 50% of damage dealt into HP
OverdriveAbility.cost = 50
OverdriveAbility.description = "Gains 50% lifesteal for 10 seconds, draining enemies' health."

function OverdriveAbility.activate(character)
    if character.resources.overdrive < OverdriveAbility.cost then
        print(character.name .. " does not have enough Overdrive to use " .. OverdriveAbility.name .. "!")
        return false
    end

    character.resources.overdrive = character.resources.overdrive - OverdriveAbility.cost
    print(character.name .. " activated Overdrive: " .. OverdriveAbility.name .. " - " .. OverdriveAbility.description)

    -- Apply ability effects (lifesteal for 10 seconds)
    character.lifesteal_active = true
    character.lifesteal_duration = OverdriveAbility.duration
    character.lifesteal_percentage = OverdriveAbility.percentage

    return true
end

return OverdriveAbility
