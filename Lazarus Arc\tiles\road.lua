-- tiles/road.lua
local RoadTile = {
    id = "road",
    name = "Road",
    passable = true,
    variants = 6, -- Different road appearances
    
    -- Entities that can spawn on roads
    spawns = {"traveler", "merchant_caravan", "patrol", "bandit", "lost_item"},
    
    -- Movement properties
    movementSpeed = 1.5, -- Faster movement on proper roads
    
    -- Road-specific properties
    roadType = nil, -- Set during init (stone, dirt, paved, etc.)
    roadQuality = 0.8, -- How well-maintained the road is (0-1)
    roadWidth = 1, -- Width in tiles
    hasTraffic = false, -- Whether NPCs frequently use this road
    tradeRoute = false, -- Whether this is part of a major trade route
    
    -- Interaction function
    interact = function(tile, entity)
        -- Check for items dropped on the road
        if math.random() < 0.1 then
            local items = {
                {id = "coin", chance = 0.4, quantity = {1, 3}},
                {id = "lost_letter", chance = 0.2, quantity = 1},
                {id = "broken_wheel", chance = 0.15, quantity = 1},
                {id = "travelers_token", chance = 0.15, quantity = 1},
                {id = "trade_good", chance = 0.1, quantity = 1}
            }
            
            for _, item in ipairs(items) do
                if math.random() < item.chance then
                    local quantity = 1
                    if item.quantity and type(item.quantity) == "table" then
                        quantity = math.random(item.quantity[1], item.quantity[2])
                    elseif type(item.quantity) == "number" then
                        quantity = item.quantity
                    end
                    
                    return {
                        success = true,
                        message = "You find " .. (quantity > 1 and quantity .. " " or "a ") .. item.id .. " on the road.",
                        effects = {
                            {type = "add_item", item = item.id, quantity = quantity}
                        }
                    }
                end
            end
        end
        
        -- Check for road signs or markers
        if tile.hasMarker and math.random() < 0.5 then
            -- Read distance markers
            local nearbyLocations = {}
            if tile.nearbyLocations then
                nearbyLocations = tile.nearbyLocations
            else
                -- Placeholder - in real implementation, this would query the world
                nearbyLocations = {
                    {name = "Village", distance = math.random(1, 5)},
                    {name = "Crossroads", distance = math.random(2, 7)},
                    {name = "Bridge", distance = math.random(3, 8)}
                }
            end
            
            local message = "You see a road marker with directions:"
            for _, location in ipairs(nearbyLocations) do
                message = message .. "\n- " .. location.name .. ": " .. location.distance .. " miles"
            end
            
            return {
                success = true,
                message = message,
                effects = {
                    {type = "discover_locations", locations = nearbyLocations}
                }
            }
        end
        
        -- Examine the road itself
        local roadDescriptions = {
            stone = "A well-built stone road that has withstood the test of time.",
            paved = "A paved road made of fitted stones, designed for heavy traffic.",
            dirt = "A simple dirt road, muddy in places but serviceable.",
            ancient = "An ancient road with worn paving stones from a bygone era.",
            imperial = "A grand Imperial highway with elaborate craftsmanship.",
            forgotten = "A barely visible road, forgotten and reclaimed by nature."
        }
        
        local trafficDesc = ""
        if tile.hasTraffic then
            trafficDesc = " It shows signs of frequent use."
        else
            trafficDesc = " It doesn't appear to be used much."
        end
        
        local qualityDesc = ""
        if tile.roadQuality > 0.8 then
            qualityDesc = " The road is in excellent condition."
        elseif tile.roadQuality > 0.5 then
            qualityDesc = " The road is in fair condition with some wear."
        else
            qualityDesc = " The road is in poor condition with many damaged sections."
        end
        
        local description = roadDescriptions[tile.roadType] or "A road stretching across the landscape."
        description = description .. trafficDesc .. qualityDesc
        
        return {
            success = false,
            message = description
        }
    end,
    
    -- Weather effects
    weatherEffects = {
        rain = function(tile)
            -- Roads get muddy and slower in rain
            if tile.roadType == "dirt" then
                tile.isMuddy = true
                tile.movementSpeed = 0.8 -- Slower when muddy
                return nil
            elseif tile.roadQuality < 0.5 then
                -- Low quality paved roads also affected by rain
                tile.movementSpeed = tile.movementSpeed * 0.9
                return nil
            end
            return nil
        end,
        
        sun = function(tile)
            -- Roads dry out in sun
            if tile.isMuddy then
                tile.isMuddy = false
                tile.movementSpeed = 1.5 -- Normal road speed when dry
            end
            return nil
        end,
        
        snow = function(tile)
            -- Snow covers the road
            tile.isSnowCovered = true
            tile.movementSpeed = tile.movementSpeed * 0.8 -- Slower in snow
            return nil
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {0.7, 0.7, 0.7}, -- Grey stone roads
        variants = {
            {name = "stone", color_shift = {0, 0, 0}},
            {name = "paved", color_shift = {0.1, 0.1, 0.1}},
            {name = "dirt", color_shift = {0.2, 0.1, -0.2}, base_color = {0.6, 0.5, 0.3}},
            {name = "ancient", color_shift = {-0.1, -0.1, 0}, wear = 0.7},
            {name = "imperial", color_shift = {0.1, 0, 0.1}, decorative = true},
            {name = "forgotten", color_shift = {-0.2, 0, -0.1}, overgrown = true}
        },
        decoration_objects = {
            {name = "road_marker", chance = 0.1},
            {name = "milestone", chance = 0.05},
            {name = "wheel_ruts", chance = 0.4},
            {name = "debris", chance = 0.2}
        },
        condition_effects = {
            cracks = {threshold = 0.5, intensity = 0.7},
            potholes = {threshold = 0.3, intensity = 0.8},
            repairs = {threshold = 0.7, chance = 0.3}
        },
        weather_overlays = {
            mud = {color = {0.4, 0.3, 0.1}, when = "is_muddy"},
            snow = {color = {0.9, 0.9, 0.95}, when = "is_snow_covered"},
            puddles = {effect = "reflective", when = "after_rain"}
        },
        connection_rules = "road" -- Special connection type for roads
    },
    
    -- Audio properties
    footstepSound = "stone_step",
    horseHoofSound = "horse_road_clop",
    wagonWheelSound = "wagon_road_rumble",
    
    -- Road segments and connections
    segments = {
        straight = {rotation = {0, 90, 180, 270}},
        corner = {rotation = {0, 90, 180, 270}},
        t_junction = {rotation = {0, 90, 180, 270}},
        crossroads = {rotation = {0}},
        end_cap = {rotation = {0, 90, 180, 270}}
    },
    
    -- Connections to other tiles
    connects_to = {
        "road",
        "dirt_path",
        "bridge",
        "stone_path"
    },
    
    -- Road-specific pathfinding properties
    pathfinding = {
        preferred_path = true, -- NPCs prefer to travel on roads
        travel_cost = 0.6, -- Lower cost for pathfinding
        line_of_sight = true -- Can see farther along roads
    },
    
    -- Effect on travel time when used for fast travel
    fast_travel = {
        speed_multiplier = 1.5, -- Faster travel on roads
        encounter_chance_modifier = -0.2, -- Fewer random encounters on roads
        fatigue_modifier = -0.3 -- Less fatigue when traveling on roads
    },
    
    -- Effects on entities that walk on this tile
    onWalk = function(tile, entity)
        -- Roads provide easier travel
        if entity.applyStatus and not tile.isMuddy and not tile.isSnowCovered then
            entity.applyStatus("road_travel", 5) -- Temporary speed boost
        end
        
        -- Make appropriate sounds
        local sound = "footstep"
        if entity.type == "horse" or entity.type == "mounted" then
            sound = "horse_clop"
        elseif entity.type == "wagon" or entity.type == "cart" then
            sound = "wagon_rumble"
        end
        
        return {
            sound = sound,
            volume = 1.0
        }
    end,
    
    -- Effect when entering this road for the first time
    onDiscover = function(tile, entity)
        -- Add to player's map
        if entity.map and entity.map.addRoad then
            entity.map.addRoad(tile.x, tile.y, tile.roadType)
        end
        
        -- If this is part of a trade route, mark it
        if tile.tradeRoute and entity.journal and entity.journal.addNote then
            entity.journal.addNote({
                type = "trade_route",
                roadType = tile.roadType,
                location = {x = tile.x, y = tile.y}
            })
        end
        
        return {
            message = "You discover a " .. tile.roadType .. " road.",
            effects = {
                {type = "reveal_map_line", direction = tile.direction, distance = 10}
            }
        }
    end
}

function RoadTile.init(world)
    print("Road tile module initialized")
    
    -- Road types and their chances
    local roadTypes = {
        {id = "stone", chance = 0.3},
        {id = "paved", chance = 0.2},
        {id = "dirt", chance = 0.3},
        {id = "ancient", chance = 0.1},
        {id = "imperial", chance = 0.05},
        {id = "forgotten", chance = 0.05}
    }
    
    -- Register with pathfinding system
    if world.systems and world.systems.pathfinding then
        world.systems.pathfinding:registerRoadType("road", 0.6)
    end
    
    -- Register with map system
    if world.systems and world.systems.mapping then
        world.systems.mapping:registerRoadType("road")
    end
    
    -- Register with fast travel system
    if world.systems and world.systems.travel then
        world.systems.travel:registerTravelRoute("road", 1.5)
    end
    
    -- Register different road types with naming system
    if world.systems and world.systems.naming then
        world.systems.naming:registerRoadNaming("road", {
            prefixes = {"King's", "Queen's", "Imperial", "Old", "New", "East", "West", "North", "South", "Trade"},
            suffixes = {"Road", "Way", "Path", "Highway", "Route"},
            themes = {"imperial", "trade", "military", "pilgrim"}
        })
    end
end

return RoadTile