-- items/armor/tower_shield.lua

local Shield = {
    id = "tower_shield",
    name = "Tower Shield",
    description = "A massive shield that provides excellent protection but reduces mobility.",
    type = "shield",
    category = "shield",
    stackable = false,

    stats = {
        defense = 15.0,
        block_chance = 0.35,
        durability = 150.0,
        repair_cost = 45,
        weight = 12.0,
    },

    material = "steel",

    sprite = "res://Sprite/items/armor/tower_shield.png",
    size = { width = 64, height = 64 },

    effects = {
        projectile_resistance = 0.25,
        bash_damage = 7.0,
        speed_penalty = -0.1 -- 10% speed reduction
    },

    meta = {
        rarity = "rare"
    },

    take_damage = function(self, amount)
        self.stats.durability = math.max(0, self.stats.durability - amount)
        if self.stats.durability == 0 then
            print(self.name .. " is broken! Penalties applied.")
            -- Potential penalties could be applied here
        end
    end,

    repair = function(self)
        local repair_amount = 150 - self.stats.durability
        self.stats.durability = 150
        print(self.name .. " repaired by " .. repair_amount .. " points.")
    end,
    
    onUse = function(self, user, target)
        -- Shield bash attack
        if user and target then
            print(user.name .. " bashes " .. target.name .. " with the massive shield!")
            -- In a real implementation, this would apply damage and possibly a stun effect
            if target.takeDamage then
                target:takeDamage(self.effects.bash_damage, "physical")
            end
            
            -- Chance to stun
            local roll = math.random()
            if roll <= 0.3 then -- 30% chance to stun
                print(target.name .. " is stunned by the powerful shield bash!")
                if target.applyEffect then
                    target:applyEffect("stunned", 2.0) -- Stun for 2 seconds
                end
            end
            
            -- Chance to knock back
            roll = math.random()
            if roll <= 0.2 then -- 20% chance to knock back
                print(target.name .. " is knocked back by the force of the shield!")
                if target.applyEffect then
                    target:applyEffect("knockback", 1.0, {distance = 3}) -- Knock back 3 units
                end
            end
            
            return true
        end
        return false
    end,
    
    onBlock = function(self, user, attacker, damage)
        -- Successful block
        print(user.name .. " blocks " .. attacker.name .. "'s attack with the tower shield!")
        
        -- Reduce shield durability
        self:take_damage(math.max(1, damage * 0.15))
        
        -- Return the amount of damage blocked
        return damage * 0.9 -- Block 90% of the damage
    end,
    
    onEquip = function(self, user)
        if user and user.stats then
            -- Apply speed penalty
            if user.stats.speed then
                user.stats.speed = user.stats.speed * (1 + self.effects.speed_penalty)
                print(user.name .. "'s movement is slowed by the weight of the tower shield.")
            end
        end
    end,
    
    onUnequip = function(self, user)
        if user and user.stats then
            -- Remove speed penalty
            if user.stats.speed then
                user.stats.speed = user.stats.speed / (1 + self.effects.speed_penalty)
                print(user.name .. "'s movement returns to normal after unequipping the tower shield.")
            end
        end
    end
}

return Shield
