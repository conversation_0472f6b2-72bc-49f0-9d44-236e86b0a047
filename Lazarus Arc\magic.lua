-- magic.lua
-- Magic system for Lazarus Arc

local Magic = {
    spells = {}, -- Will store all loaded spell data
    elements = {}, -- Information about magical elements
    spellsByElement = {}, -- Spells organized by element
    spellsByTier = {}, -- Spells organized by tier/power level
    discoveryRequirements = {}, -- Requirements to discover spells
    loaded = {}, -- Tracks which spell category files have been loaded
    opposingElements = {} -- Maps elements to their opposites
}

-- Initialize the magic system
function Magic.init()
    print("Initializing magic system...")
    
    -- Setup elemental affinities and oppositions
    Magic.setupElements()
    
    -- Load base spell categories
    Magic.loadCategory("fire")
    Magic.loadCategory("water")
    Magic.loadCategory("earth")
    Magic.loadCategory("air")
    Magic.loadCategory("lightning")
    Magic.loadCategory("light") -- Special/hidden element
    Magic.loadCategory("dark") -- Special/hidden element
    Magic.loadCategory("time") -- Special/hidden element
    Magic.loadCategory("void") -- Special/hidden element
    
    -- Load combination spells
    Magic.loadCategory("ice") -- water + air
    Magic.loadCategory("magma") -- fire + earth
    Magic.loadCategory("storm") -- air + lightning
    Magic.loadCategory("nature") -- earth + water
    Magic.loadCategory("arcane") -- combination of multiple elements
    
    print("Magic system initialized with " .. Magic.getSpellCount() .. " spells")
    return true
end

-- Setup elements and their relationships
function Magic.setupElements()
    -- Define base elements
    Magic.elements = {
        fire = {
            name = "Fire",
            description = "Element of destruction and passion",
            color = {1.0, 0.3, 0.1},
            baseStrength = 10,
            minLevel = 1
        },
        water = {
            name = "Water",
            description = "Element of flexibility and healing",
            color = {0.2, 0.4, 1.0},
            baseStrength = 10,
            minLevel = 1
        },
        earth = {
            name = "Earth",
            description = "Element of stability and protection",
            color = {0.5, 0.3, 0.1},
            baseStrength = 10,
            minLevel = 1
        },
        air = {
            name = "Air",
            description = "Element of freedom and mobility",
            color = {0.8, 0.8, 1.0},
            baseStrength = 10,
            minLevel = 1
        },
        lightning = {
            name = "Lightning",
            description = "Element of energy and swift action",
            color = {0.9, 0.8, 0.1},
            baseStrength = 12,
            minLevel = 1
        },
        light = {
            name = "Light",
            description = "Element of illumination and purity",
            color = {1.0, 1.0, 0.8},
            baseStrength = 15,
            minLevel = 20,
            hidden = true
        },
        dark = {
            name = "Dark",
            description = "Element of shadow and secrecy",
            color = {0.2, 0.1, 0.3},
            baseStrength = 15,
            minLevel = 20,
            hidden = true
        },
        time = {
            name = "Time",
            description = "Hidden element of temporal manipulation",
            color = {0.6, 0.3, 0.9},
            baseStrength = 20,
            minLevel = 50,
            hidden = true
        },
        void = {
            name = "Void",
            description = "Hidden element of nothingness and space",
            color = {0.1, 0.0, 0.2},
            baseStrength = 20,
            minLevel = 50,
            hidden = true
        }
    }
    
    -- Define combination elements
    Magic.elements.ice = {
        name = "Ice",
        description = "Combination of Water and Air",
        color = {0.7, 0.8, 1.0},
        baseStrength = 13,
        minLevel = 10,
        components = {"water", "air"},
        componentThreshold = 50 -- Both affinities must be 50+ to unlock
    }
    
    Magic.elements.magma = {
        name = "Magma",
        description = "Combination of Fire and Earth",
        color = {0.9, 0.4, 0.1},
        baseStrength = 13,
        minLevel = 10,
        components = {"fire", "earth"},
        componentThreshold = 50
    }
    
    Magic.elements.storm = {
        name = "Storm",
        description = "Combination of Air and Lightning",
        color = {0.5, 0.5, 0.9},
        baseStrength = 15,
        minLevel = 15,
        components = {"air", "lightning"},
        componentThreshold = 50
    }
    
    Magic.elements.nature = {
        name = "Nature",
        description = "Combination of Earth and Water",
        color = {0.3, 0.7, 0.2},
        baseStrength = 13,
        minLevel = 10,
        components = {"earth", "water"},
        componentThreshold = 50
    }
    
    Magic.elements.arcane = {
        name = "Arcane",
        description = "Complex combination of multiple elements",
        color = {0.7, 0.2, 0.9},
        baseStrength = 18,
        minLevel = 25,
        components = {"fire", "water", "earth", "air"},
        componentThreshold = 30 -- Each affinity must be 30+ to unlock
    }
    
    -- Define opposing elements (100% in one means 0% in the other)
    Magic.opposingElements = {
        fire = "water",
        water = "fire",
        earth = "air",
        air = "earth",
        light = "dark",
        dark = "light"
    }
    
    -- Set up spell categories by element
    for element, _ in pairs(Magic.elements) do
        Magic.spellsByElement[element] = {}
    end
    
    -- Set up spell tiers
    for i = 1, 5 do
        Magic.spellsByTier[i] = {}
    end
end

-- Get the opposing element
function Magic.getOpposingElement(element)
    return Magic.opposingElements[element]
end

-- Load all spells in a category (element)
function Magic.loadCategory(element)
    -- Prevent reloading
    if Magic.loaded[element] then
        return true
    end
    
    -- Check if this is a valid element
    if not Magic.elements[element] then
        print("Error: Unknown element - " .. element)
        return false
    end
    
    print("Loading " .. element .. " spells...")
    
    -- Try to load the element module
    local success, elementModule = pcall(require, "spells." .. element)
    
    if not success then
        print("Failed to load " .. element .. " spells")
        print(elementModule) -- Show error
        return false
    end
    
    -- Register each spell from the element
    local count = 0
    for spellId, spellData in pairs(elementModule) do
        -- Skip non-spell entries
        if type(spellData) == "table" and spellData.name then
            -- Add element to spell data
            spellData.element = spellData.element or element
            
            -- Register the spell
            Magic.registerSpell(spellId, spellData)
            count = count + 1
        end
    end
    
    -- Mark as loaded
    Magic.loaded[element] = true
    
    print("Loaded " .. count .. " " .. element .. " spells")
    return true
end

-- Register a single spell
function Magic.registerSpell(spellId, spellData)
    -- Store the spell
    Magic.spells[spellId] = spellData
    
    -- Add to element index
    local element = spellData.element or "neutral"
    Magic.spellsByElement[element] = Magic.spellsByElement[element] or {}
    table.insert(Magic.spellsByElement[element], spellId)
    
    -- Add to tier index
    local tier = spellData.tier or 1
    Magic.spellsByTier[tier] = Magic.spellsByTier[tier] or {}
    table.insert(Magic.spellsByTier[tier], spellId)
    
    -- Register discovery requirements if specified
    if spellData.discoveryRequirements then
        Magic.discoveryRequirements[spellId] = spellData.discoveryRequirements
    end
    
    return true
end

-- Check if a spell exists
function Magic.spellExists(spellId)
    -- Direct lookup
    if Magic.spells[spellId] then
        return true
    end
    
    -- Try to load the spell directly
    local success, spellModule = pcall(require, "spells.individual." .. spellId)
    
    if success and type(spellModule) == "table" and spellModule.name then
        Magic.registerSpell(spellId, spellModule)
        return true
    end
    
    -- Check each element (if not already loaded)
    for element, _ in pairs(Magic.elements) do
        if not Magic.loaded[element] then
            Magic.loadCategory(element)
            
            -- Check if spell was loaded with element
            if Magic.spells[spellId] then
                return true
            end
        end
    end
    
    return false
end

-- Load a spell by ID
function Magic.loadSpell(spellId)
    -- Return cached spell if available
    if Magic.spells[spellId] then
        return Magic.spells[spellId]
    end
    
    -- Try to load from individual spell file
    local success, spellModule = pcall(require, "spells.individual." .. spellId)
    
    if success and type(spellModule) == "table" and spellModule.name then
        Magic.registerSpell(spellId, spellModule)
        return spellModule
    end
    
    -- Check each element (if not already loaded)
    for element, _ in pairs(Magic.elements) do
        if not Magic.loaded[element] then
            Magic.loadCategory(element)
            
            -- Check if spell was loaded with element
            if Magic.spells[spellId] then
                return Magic.spells[spellId]
            end
        end
    end
    
    return nil
end

-- Get all spells by element
function Magic.getSpellsByElement(element)
    if not Magic.loaded[element] then
        Magic.loadCategory(element)
    end
    
    return Magic.spellsByElement[element] or {}
end

-- Get spells by tier
function Magic.getSpellsByTier(tier)
    local spells = {}
    
    for spellId, spellData in pairs(Magic.spells) do
        if spellData.tier == tier then
            table.insert(spells, spellId)
        end
    end
    
    return spells
end

-- Check element combinations for a character to discover new elements
function Magic.checkElementCombinations(character)
    -- Check for each combination element
    for element, elementData in pairs(Magic.elements) do
        -- Skip base elements
        if elementData.components then
            local canUnlock = true
            
            -- Check if character meets the component requirements
            for _, componentElement in ipairs(elementData.components) do
                if (character.magicAffinities[componentElement] or 0) < (elementData.componentThreshold or 50) then
                    canUnlock = false
                    break
                end
            end
            
            -- Unlock the combination element if requirements met
            if canUnlock and (character.magicAffinities[element] or 0) == 0 then
                character.magicAffinities[element] = 1 -- Start with minimal affinity
                print(character.name .. " has discovered " .. elementData.name .. " magic!")
            end
        end
    end
end

-- Check if a character meets requirements for a spell
function Magic.checkSpellRequirements(character, spellId)
    -- Load spell if not loaded
    local spellData = Magic.loadSpell(spellId)
    
    if not spellData then
        return false, "Spell does not exist"
    end
    
    -- Check character level
    if spellData.levelRequirement and character.level < spellData.levelRequirement then
        return false, "Character level too low"
    end
    
    -- Check element affinity
    local element = spellData.element or "neutral"
    local requiredAffinity = spellData.affinityRequirement or 10
    
    if (character.magicAffinities[element] or 0) < requiredAffinity then
        return false, "Insufficient " .. element .. " affinity"
    end
    
    -- Check prerequisite spells
    if spellData.prerequisites then
        for _, prerequisite in ipairs(spellData.prerequisites) do
            if not character.discoveredSpells[prerequisite] then
                return false, "Missing prerequisite spell: " .. prerequisite
            end
        end
    end
    
    -- Check discovery requirements (special conditions)
    if Magic.discoveryRequirements[spellId] then
        local requirements = Magic.discoveryRequirements[spellId]
        
        -- Quest requirement
        if requirements.quest and not character.quests.completed[requirements.quest] then
            return false, "Required quest not completed"
        end
        
        -- Location requirement
        if requirements.location and character.currentLocation ~= requirements.location then
            return false, "Must be discovered at a specific location"
        end
        
        -- Book/scroll requirement
        if requirements.readItem and not character.readItems[requirements.readItem] then
            return false, "Required knowledge not found"
        end
    end
    
    -- All checks passed
    return true, nil
end

-- Calculate spell damage based on character stats and spell properties
function Magic.calculateSpellDamage(character, spellId, targets)
    local spellData = Magic.loadSpell(spellId)
    if not spellData then return 0 end
    
    -- Get spell level from character's knowledge
    local spellLevel = character.discoveredSpells[spellId] and 
                     character.discoveredSpells[spellId].level or 1
    
    -- Base damage calculation
    local baseDamage = spellData.baseDamage or 10
    
    -- Scale damage by spell level
    if spellLevel > 1 and spellData.damagePerLevel then
        baseDamage = baseDamage + ((spellLevel - 1) * spellData.damagePerLevel)
    end
    
    -- Apply element strength
    local elementMultiplier = 1.0
    local element = spellData.element
    
    if element and character.magicAffinities[element] then
        -- Scale damage by affinity (0 to 100)
        elementMultiplier = 1.0 + (character.magicAffinities[element] / 100)
    end
    
    -- Scale by magic attack stat
    local magicMultiplier = 1.0 + (character.stats.mattack / 50)
    
    -- Calculate final base damage
    local finalDamage = baseDamage * elementMultiplier * magicMultiplier
    
    -- Return damage info
    return {
        base = baseDamage,
        elementMultiplier = elementMultiplier,
        magicMultiplier = magicMultiplier,
        final = finalDamage
    }
end

-- Cast a spell
function Magic.castSpell(character, spellId, targets, options)
    options = options or {}
    
    -- Load spell if not loaded
    local spellData = Magic.loadSpell(spellId)
    
    if not spellData then
        return { success = false, message = "Spell not found" }
    end
    
    -- Check if character knows the spell
    if not character.discoveredSpells[spellId] then
        return { success = false, message = "Character doesn't know this spell" }
    end
    
    -- Check if character has enough mana
    local manaCost = spellData.manaCost or 10
    
    -- Apply mana cost reductions from spell level and skills
    if character.discoveredSpells[spellId].level > 1 and spellData.manaCostReduction then
        local reduction = (character.discoveredSpells[spellId].level - 1) * spellData.manaCostReduction
        manaCost = math.max(spellData.minimumManaCost or 1, manaCost - reduction)
    end
    
    if character.stats.mana < manaCost then
        return { success = false, message = "Not enough mana" }
    end
    
    -- Just return success if this is a preview check
    if options.previewOnly then
        return { success = true, message = "Can cast spell" }
    end
    
    -- Consume mana
    character.stats.mana = character.stats.mana - manaCost
    
    -- Execute spell effect
    local result
    if type(spellData.cast) == "function" then
        result = spellData.cast(character, targets, character.discoveredSpells[spellId].level, options)
    else
        -- Process based on spell type
        if spellData.type == "damage" then
            result = Magic.executeDamageSpell(character, spellData, targets, character.discoveredSpells[spellId].level, options)
        elseif spellData.type == "healing" then
            result = Magic.executeHealingSpell(character, spellData, targets, character.discoveredSpells[spellId].level, options)
        elseif spellData.type == "buff" then
            result = Magic.executeBuffSpell(character, spellData, targets, character.discoveredSpells[spellId].level, options)
        elseif spellData.type == "debuff" then
            result = Magic.executeDebuffSpell(character, spellData, targets, character.discoveredSpells[spellId].level, options)
        elseif spellData.type == "utility" then
            result = Magic.executeUtilitySpell(character, spellData, targets, character.discoveredSpells[spellId].level, options)
        elseif spellData.type == "summon" then
            result = Magic.executeSummonSpell(character, spellData, targets, character.discoveredSpells[spellId].level, options)
        else
            result = { success = true, message = "Cast " .. spellData.name }
        end
    end
    
    -- Increase spell experience
    if not options.noExperience then
        Magic.gainSpellExperience(character, spellId, spellData.experienceGain or 1)
    end
    
    -- Update character statistics
    character.statistics.spellsCast = (character.statistics.spellsCast or 0) + 1
    
    return result or { success = true, message = "Cast " .. spellData.name }
end

-- Execute a damage spell
function Magic.executeDamageSpell(character, spellData, targets, spellLevel, options)
    local result = {
        success = true,
        message = "Cast " .. spellData.name,
        targets = {},
        totalDamage = 0
    }
    
    -- Calculate base damage
    local damageInfo = Magic.calculateSpellDamage(character, spellData.id or "unknown", targets)
    local baseDamage = damageInfo.final
    
    -- Process each target
    for _, target in ipairs(targets or {}) do
        local targetResult = {
            target = target,
            damage = 0,
            effects = {}
        }
        
        -- Calculate final damage for this target
        local damage = baseDamage
        
        -- Apply random variance
        if spellData.damageVariance then
            local variance = spellData.damageVariance
            damage = damage * (1 + (math.random() * variance * 2 - variance))
        end
        
        -- Apply critical hit
        local criticalHit = false
        local critChance = (spellData.criticalChance or 5) + 
                           ((character.attributes.intelligence or 0) * 0.2) +
                           ((spellLevel - 1) * (spellData.critChancePerLevel or 0.5))
        
        if math.random(100) <= critChance then
            criticalHit = true
            local critMultiplier = spellData.criticalMultiplier or 1.5
            damage = damage * critMultiplier
            
            table.insert(targetResult.effects, "critical")
            character.statistics.criticalHits = (character.statistics.criticalHits or 0) + 1
        end
        
        -- Apply target resistances/weaknesses
        local damageMultiplier = 1.0
        
        -- Element-based resistance/weakness
        if spellData.element and target.resistances and target.resistances[spellData.element] then
            damageMultiplier = damageMultiplier * (1 - target.resistances[spellData.element])
        elseif spellData.element and target.weaknesses and target.weaknesses[spellData.element] then
            damageMultiplier = damageMultiplier * (1 + target.weaknesses[spellData.element])
        end
        
        -- Apply damage multiplier
        damage = damage * damageMultiplier
        
        -- Round damage to integer
        damage = math.floor(damage)
        
        -- Apply damage to target
        if target.takeDamage then
            local damageType = spellData.element or "magical"
            local actualDamage = target.takeDamage(damage, damageType, character)
            
            targetResult.damage = actualDamage
            result.totalDamage = result.totalDamage + actualDamage
            
            -- Update statistics
            character.statistics.damageDealt = (character.statistics.damageDealt or 0) + actualDamage
        else
            -- Target doesn't have takeDamage method
            targetResult.damage = damage
            result.totalDamage = result.totalDamage + damage
        end
        
        -- Apply secondary effects
        if spellData.effects then
            for effectName, effectChance in pairs(spellData.effects) do
                -- Scale effect chance with spell level
                local scaledChance = effectChance
                if spellLevel > 1 and spellData.effectChancePerLevel then
                    scaledChance = scaledChance + ((spellLevel - 1) * (spellData.effectChancePerLevel or 1))
                end
                
                -- Roll for effect
                if math.random(100) <= scaledChance then
                    -- Determine effect duration
                    local duration = spellData.effectDuration or 3
                    
                    -- Scale duration with spell level
                    if spellLevel > 1 and spellData.effectDurationPerLevel then
                        duration = duration + ((spellLevel - 1) * spellData.effectDurationPerLevel)
                    end
                    
                    -- Apply the effect
                    if target.addStatusEffect then
                        target.addStatusEffect(effectName, duration, 1.0)
                        table.insert(targetResult.effects, effectName)
                    end
                end
            end
        end
        
        table.insert(result.targets, targetResult)
    end
    
    -- Create result message
    if #targets == 1 then
        local targetResult = result.targets[1]
        result.message = "Cast " .. spellData.name .. " on " .. (targets[1].name or "target") ..
                        " for " .. targetResult.damage .. " damage"
        
        if #targetResult.effects > 0 then
            result.message = result.message .. " (" .. table.concat(targetResult.effects, ", ") .. ")"
        end
    else
        result.message = "Cast " .. spellData.name .. " on " .. #targets .. " targets for " ..
                        result.totalDamage .. " total damage"
    end
    
    return result
end

-- Execute a healing spell
function Magic.executeHealingSpell(character, spellData, targets, spellLevel, options)
    local result = {
        success = true,
        message = "Cast " .. spellData.name,
        targets = {},
        totalHealing = 0
    }
    
    -- Base healing calculation
    local baseHealing = spellData.baseHealing or 10
    
    -- Scale by spell level
    if spellLevel > 1 and spellData.healingPerLevel then
        baseHealing = baseHealing + ((spellLevel - 1) * spellData.healingPerLevel)
    end
    
    -- Scale by magic attack and wisdom
    local magicMultiplier = 1.0 + (character.stats.mattack / 50)
    local wisdomMultiplier = 1.0 + ((character.attributes.wisdom or 10) / 100)
    
    -- Apply element affinity
    local elementMultiplier = 1.0
    if spellData.element and character.magicAffinities[spellData.element] then
        elementMultiplier = 1.0 + (character.magicAffinities[spellData.element] / 100)
    end
    
    -- Calculate final base healing
    local finalHealing = baseHealing * magicMultiplier * wisdomMultiplier * elementMultiplier
    
    -- Process each target
    for _, target in ipairs(targets or {}) do
        local targetResult = {
            target = target,
            healing = 0,
            effects = {}
        }
        
        -- Calculate healing for this target
        local healing = finalHealing
        
        -- Apply random variance
        if spellData.healingVariance then
            local variance = spellData.healingVariance
            healing = healing * (1 + (math.random() * variance * 2 - variance))
        end
        
        -- Check for critical healing
        local criticalHeal = false
        local critChance = (spellData.criticalChance or 5) + 
                           ((character.attributes.wisdom or 0) * 0.2) +
                           ((spellLevel - 1) * (spellData.critChancePerLevel or 0.5))
        
        if math.random(100) <= critChance then
            criticalHeal = true
            local critMultiplier = spellData.criticalMultiplier or 1.5
            healing = healing * critMultiplier
            
            table.insert(targetResult.effects, "critical")
        end
        
        -- Round healing to integer
        healing = math.floor(healing)
        
        -- Apply healing to target
        if target.heal then
            local actualHealing = target.heal(healing, character)
            
            targetResult.healing = actualHealing
            result.totalHealing = result.totalHealing + actualHealing
        else
            -- Target doesn't have heal method
            targetResult.healing = healing
            result.totalHealing = result.totalHealing + healing
        end
        
        -- Apply secondary effects
        if spellData.effects then
            for effectName, effectChance in pairs(spellData.effects) do
                -- Scale effect chance with spell level
                local scaledChance = effectChance
                if spellLevel > 1 and spellData.effectChancePerLevel then
                    scaledChance = scaledChance + ((spellLevel - 1) * (spellData.effectChancePerLevel or 1))
                end
                
                -- Roll for effect
                if math.random(100) <= scaledChance then
                    -- Determine effect duration
                    local duration = spellData.effectDuration or 3
                    
                    -- Scale duration with spell level
                    if spellLevel > 1 and spellData.effectDurationPerLevel then
                        duration = duration + ((spellLevel - 1) * spellData.effectDurationPerLevel)
                    end
                    
                    -- Apply the effect
                    if target.addStatusEffect then
                        target.addStatusEffect(effectName, duration, 1.0)
                        table.insert(targetResult.effects, effectName)
                    end
                end
            end
        end
        
        table.insert(result.targets, targetResult)
    end
    
    -- Create result message
    if #targets == 1 then
        local targetResult = result.targets[1]
        result.message = "Cast " .. spellData.name .. " on " .. (targets[1].name or "target") ..
                        " for " .. targetResult.healing .. " healing"
        
        if #targetResult.effects > 0 then
            result.message = result.message .. " (" .. table.concat(targetResult.effects, ", ") .. ")"
        end
    else
        result.message = "Cast " .. spellData.name .. " on " .. #targets .. " targets for " ..
                        result.totalHealing .. " total healing"
    end
    
    return result
end

-- Execute a buff spell
function Magic.executeBuffSpell(character, spellData, targets, spellLevel, options)
    local result = {
        success = true,
        message = "Cast " .. spellData.name,
        targets = {},
        effects = {}
    }
    
    -- Determine base duration
    local baseDuration = spellData.baseDuration or 30
    
    -- Scale duration by spell level
    if spellLevel > 1 and spellData.durationPerLevel then
        baseDuration = baseDuration + ((spellLevel - 1) * spellData.durationPerLevel)
    end
    
    -- Process each target
    for _, target in ipairs(targets or {}) do
        local targetResult = {
            target = target,
            effects = {}
        }
        
        -- Apply each buff effect
        if spellData.buffs then
            for buffName, buffData in pairs(spellData.buffs) do
                -- Determine buff strength
                local strength = buffData.strength or 1.0
                
                -- Scale strength with spell level
                if spellLevel > 1 and buffData.strengthPerLevel then
                    strength = strength + ((spellLevel - 1) * buffData.strengthPerLevel)
                end
                
                -- Determine duration (can be overridden per buff)
                local duration = buffData.duration or baseDuration
                
                -- Apply the buff
                if target.addStatusEffect then
                    target.addStatusEffect(buffName, duration, strength)
                    table.insert(targetResult.effects, buffName)
                    
                    -- Track applied effects
                    if not result.effects[buffName] then
                        result.effects[buffName] = 0
                    end
                    result.effects[buffName] = result.effects[buffName] + 1
                end
            end
        end
        
        table.insert(result.targets, targetResult)
    end
    
    -- Create result message
    if #targets == 1 then
        local targetResult = result.targets[1]
        result.message = "Cast " .. spellData.name .. " on " .. (targets[1].name or "target")
        
        if #targetResult.effects > 0 then
            result.message = result.message .. " (" .. table.concat(targetResult.effects, ", ") .. ")"
        end
    else
        result.message = "Cast " .. spellData.name .. " on " .. #targets .. " targets"
        
        -- Add effect summary
        local effectList = {}
        for effect,
		count in pairs(result.effects) do
            table.insert(effectList, effect .. " x" .. count)
        end
        
        if #effectList > 0 then
            result.message = result.message .. " (" .. table.concat(effectList, ", ") .. ")"
        end
    end
    
    return result
end

-- Execute a debuff spell
function Magic.executeDebuffSpell(character, spellData, targets, spellLevel, options)
    local result = {
        success = true,
        message = "Cast " .. spellData.name,
        targets = {},
        effects = {}
    }
    
    -- Determine base duration
    local baseDuration = spellData.baseDuration or 15
    
    -- Scale duration by spell level
    if spellLevel > 1 and spellData.durationPerLevel then
        baseDuration = baseDuration + ((spellLevel - 1) * spellData.durationPerLevel)
    end
    
    -- Process each target
    for _, target in ipairs(targets or {}) do
        local targetResult = {
            target = target,
            effects = {},
            resisted = {}
        }
        
        -- Apply each debuff effect
        if spellData.debuffs then
            for debuffName, debuffData in pairs(spellData.debuffs) do
                -- Calculate resist chance
                local resistChance = 0
                
                if target.stats then
                    if debuffData.type == "physical" then
                        resistChance = target.stats.pdefense / 2
                    elseif debuffData.type == "magical" then
                        resistChance = target.stats.mdefense / 2
                    end
                end
                
                -- Check if target resists
                if math.random(100) <= resistChance then
                    table.insert(targetResult.resisted, debuffName)
                else
                    -- Determine debuff strength
                    local strength = debuffData.strength or 1.0
                    
                    -- Scale strength with spell level
                    if spellLevel > 1 and debuffData.strengthPerLevel then
                        strength = strength + ((spellLevel - 1) * debuffData.strengthPerLevel)
                    end
                    
                    -- Determine duration (can be overridden per debuff)
                    local duration = debuffData.duration or baseDuration
                    
                    -- Apply the debuff
                    if target.addStatusEffect then
                        target.addStatusEffect(debuffName, duration, strength)
                        table.insert(targetResult.effects, debuffName)
                        
                        -- Track applied effects
                        if not result.effects[debuffName] then
                            result.effects[debuffName] = 0
                        end
                        result.effects[debuffName] = result.effects[debuffName] + 1
                    end
                end
            end
        end
        
        table.insert(result.targets, targetResult)
    end
    
    -- Create result message
    if #targets == 1 then
        local targetResult = result.targets[1]
        result.message = "Cast " .. spellData.name .. " on " .. (targets[1].name or "target")
        
        -- Add applied effects
        if #targetResult.effects > 0 then
            result.message = result.message .. " (" .. table.concat(targetResult.effects, ", ") .. ")"
        end
        
        -- Add resisted effects
        if #targetResult.resisted > 0 then
            result.message = result.message .. " [Resisted: " .. table.concat(targetResult.resisted, ", ") .. "]"
        end
    else
        result.message = "Cast " .. spellData.name .. " on " .. #targets .. " targets"
        
        -- Add effect summary
        local effectList = {}
        for effect, count in pairs(result.effects) do
            table.insert(effectList, effect .. " x" .. count)
        end
        
        if #effectList > 0 then
            result.message = result.message .. " (" .. table.concat(effectList, ", ") .. ")"
        end
    end
    
    return result
end

-- Execute a utility spell
function Magic.executeUtilitySpell(character, spellData, targets, spellLevel, options)
    local result = {
        success = true,
        message = "Cast " .. spellData.name,
        effects = {}
    }
    
    -- Handle different utility types
    if spellData.utilityType == "teleport" then
        if not targets or not targets[1] or not targets[1].x or not targets[1].y then
            result.success = false
            result.message = "Invalid teleport target"
        else
            -- Update character position
            if character.position then
                local oldX, oldY = character.position.x, character.position.y
                character.position.x = targets[1].x
                character.position.y = targets[1].y
                
                result.message = "Teleported from (" .. oldX .. ", " .. oldY .. ") to (" .. 
                                targets[1].x .. ", " .. targets[1].y .. ")"
                                
                -- Add teleport effect
                table.insert(result.effects, "teleport")
            else
                result.success = false
                result.message = "Character has no position to teleport from"
            end
        end
    elseif spellData.utilityType == "reveal" then
        -- Reveal area spell (for fog of war or hidden objects)
        local radius = spellData.baseRadius or 10
        
        -- Scale radius with level
        if spellLevel > 1 and spellData.radiusPerLevel then
            radius = radius + ((spellLevel - 1) * spellData.radiusPerLevel)
        end
        
        -- Apply reveal effect (game-specific implementation)
        result.message = "Revealed area with radius " .. radius
        result.radius = radius
        table.insert(result.effects, "reveal")
    elseif spellData.utilityType == "transform" then
        -- Transform target
        if targets and targets[1] and targets[1].transform then
            local transformType = spellData.transformType or "default"
            local duration = spellData.baseDuration or 30
            
            -- Scale duration with level
            if spellLevel > 1 and spellData.durationPerLevel then
                duration = duration + ((spellLevel - 1) * spellData.durationPerLevel)
            end
            
            targets[1].transform(transformType, duration)
            result.message = "Transformed " .. (targets[1].name or "target") .. " into " .. transformType
            table.insert(result.effects, "transform")
        else
            result.success = false
            result.message = "Invalid transform target"
        end
    elseif spellData.utilityType == "identify" then
        -- Identify items
        local identified = 0
        
        if character.inventory then
            for itemId, _ in pairs(character.inventory.items) do
                if not character.isItemIdentified(itemId) then
                    -- Limit by level
                    local maxItems = spellData.baseItems or 1
                    if spellLevel > 1 and spellData.itemsPerLevel then
                        maxItems = maxItems + ((spellLevel - 1) * spellData.itemsPerLevel)
                    end
                    
                    if identified < maxItems then
                        if character.identifyItem then
                            character.identifyItem(itemId)
                            identified = identified + 1
                        end
                    else
                        break
                    end
                end
            end
        end
        
        if identified > 0 then
            result.message = "Identified " .. identified .. " items"
            result.identified = identified
            table.insert(result.effects, "identify")
        else
            result.message = "No items to identify"
        end
    elseif spellData.utilityType == "detection" then
        -- Detect traps, secrets, enemies, etc.
        local detectionType = spellData.detectionType or "traps"
        local radius = spellData.baseRadius or 10
        
        -- Scale radius with level
        if spellLevel > 1 and spellData.radiusPerLevel then
            radius = radius + ((spellLevel - 1) * spellData.radiusPerLevel)
        end
        
        result.message = "Detected " .. detectionType .. " within " .. radius .. " radius"
        result.detectionType = detectionType
        result.radius = radius
        table.insert(result.effects, "detection")
    else
        -- Generic utility effect
        result.message = "Cast " .. spellData.name .. " utility spell"
    end
    
    return result
end

-- Execute a summon spell
function Magic.executeSummonSpell(character, spellData, targets, spellLevel, options)
    local result = {
        success = true,
        message = "Cast " .. spellData.name,
        summons = {}
    }
    
    -- Determine entity type to summon
    local entityType = spellData.entityType or "fire_elemental"
    
    -- Determine stats based on spell level
    local level = math.max(1, math.floor(spellLevel * (spellData.levelMultiplier or 0.5)))
    
    -- Determine duration
    local duration = spellData.baseDuration or 60
    if spellLevel > 1 and spellData.durationPerLevel then
        duration = duration + ((spellLevel - 1) * spellData.durationPerLevel)
    end
    
    -- Determine count
    local count = spellData.baseCount or 1
    if spellLevel > 1 and spellData.countPerLevel then
        local additionalCount = math.floor((spellLevel - 1) * spellData.countPerLevel)
        count = count + additionalCount
    end
    
    -- Cap number of summons based on intelligence
    local maxSummons = math.floor(5 + (character.attributes.intelligence or 10) / 2)
    count = math.min(count, maxSummons)
    
    -- Check current summons
    local currentSummons = 0
    if character.summons then
        for _, _ in pairs(character.summons) do
            currentSummons = currentSummons + 1
        end
    end
    
    -- Limit by maximum summons
    local maxAllowed = spellData.maxSummons or 5
    local summonsToCreate = math.min(count, maxAllowed - currentSummons)
    
    if summonsToCreate <= 0 then
        result.success = false
        result.message = "Cannot summon more entities (maximum reached)"
        return result
    end
    
    -- Create the summons
    local summonedEntities = {}
    
    for i = 1, summonsToCreate do
        -- Generate a unique ID for this summon
        local summonId = "summon_" .. character.id .. "_" .. (os.time() + i)
        
        -- Create summon properties
        local summonProperties = {
            owner = character.id,
            level = level,
            duration = duration,
            expireTime = os.time() + duration,
            loyalty = spellData.loyalty or 100,
            spellId = spellData.id or "unknown"
        }
        
        -- Copy additional properties from spell
        if spellData.summonProperties then
            for key, value in pairs(spellData.summonProperties) do
                summonProperties[key] = value
            end
        end
        
        -- Create summon entity in world (implementation depends on game engine)
        -- This is a placeholder - the actual summon creation would depend on your entity system
        local entity = {
            id = summonId,
            type = entityType,
            owner = character.id,
            properties = summonProperties
        }
        
        -- Store reference to summon
        if not character.summons then
            character.summons = {}
        end
        character.summons[summonId] = entity
        
        -- Add to result
        table.insert(summonedEntities, entity)
    end
    
    -- Update result
    result.summons = summonedEntities
    if #summonedEntities == 1 then
        result.message = "Summoned " .. entityType
    else
        result.message = "Summoned " .. #summonedEntities .. " " .. entityType .. "s"
    end
    
    return result
end

-- Gain experience for a spell
function Magic.gainSpellExperience(character, spellId, amount)
    if not character.discoveredSpells[spellId] then
        return false
    end
    
    -- Get current spell data
    local spell = character.discoveredSpells[spellId]
    
    -- Add experience
    spell.experience = (spell.experience or 0) + amount
    
    -- Check for level up
    local experienceToLevel = Magic.getExperienceToNextLevel(spell.level)
    
    if spell.experience >= experienceToLevel then
        -- Level up the spell
        spell.level = spell.level + 1
        spell.experience = spell.experience - experienceToLevel
        
        -- Log level up
        print(character.name .. "'s " .. spellId .. " spell improved to level " .. spell.level)
        
        -- Get spell data
        local spellData = Magic.loadSpell(spellId)
        if spellData and spellData.element then
            -- Improve affinity with spell element
            character.adjustElementAffinity(spellData.element, 1)
        end
        
        -- Check for secondary level ups (recursive)
        return Magic.gainSpellExperience(character, spellId, 0)
    end
    
    return true
end

-- Calculate experience required for next spell level
function Magic.getExperienceToNextLevel(currentLevel)
    -- Experience curve - each level requires more experience
    return math.floor(100 * math.pow(currentLevel, 1.5))
end

-- Check if a damage type is elemental damage
function Magic.isElementalDamage(damageType)
    return Magic.elements[damageType] ~= nil
end

-- Calculate resistance to a damage type
function Magic.calculateResistance(character, damageType)
    if not character or not damageType then return 0 end
    
    -- Base resistance from character resistances
    local resistance = 0
    if character.resistances and character.resistances[damageType] then
        resistance = character.resistances[damageType]
    end
    
    -- Add resistance from equipment
    if character.equipment then
        for slot, itemId in pairs(character.equipment) do
            if itemId then
                local item = character.getEquippedItemData(slot)
                if item and item.resistances and item.resistances[damageType] then
                    resistance = resistance + item.resistances[damageType]
                end
            end
        end
    end
    
    -- Add resistance from status effects
    if character.statusEffects then
        for effectName, effect in pairs(character.statusEffects) do
            if effect.resistances and effect.resistances[damageType] then
                resistance = resistance + effect.resistances[damageType]
            end
        end
    end
    
    -- Cap resistance at 80%
    return math.min(resistance, 0.8)
end

-- Get all spells a character can potentially discover
function Magic.getDiscoverableSpells(character)
    local discoverableSpells = {}
    
    for spellId, spellData in pairs(Magic.spells) do
        -- Skip already discovered spells
        if not character.discoveredSpells[spellId] then
            -- Check if spell is discoverable
            local canDiscover, _ = Magic.checkSpellRequirements(character, spellId)
            
            if canDiscover then
                table.insert(discoverableSpells, spellId)
            end
        end
    end
    
    return discoverableSpells
end

-- Get recommended spells for a character
function Magic.getRecommendedSpells(character, count)
    count = count or 5
    local recommendations = {}
    
    -- Get discoverable spells
    local discoverable = Magic.getDiscoverableSpells(character)
    
    -- Create scoring table
    local scoredSpells = {}
    
    for _, spellId in ipairs(discoverable) do
        local spellData = Magic.loadSpell(spellId)
        
        -- Calculate recommendation score based on various factors
        local score = 0
        
        -- Factor 1: Element affinity
        if spellData.element and character.magicAffinities[spellData.element] then
            score = score + (character.magicAffinities[spellData.element] / 20)
        end
        
        -- Factor 2: Character class compatibility
        local classBonus = 0
        for _, class in ipairs(character.classes or {}) do
            local classData = require("classes")[class.name]
            if classData and classData.magic_affinity and 
               classData.magic_affinity[spellData.element] then
                classBonus = classBonus + (classData.magic_affinity[spellData.element] / 10)
            end
        end
        score = score + classBonus
        
        -- Factor 3: Tier appropriateness
        local tierScore = 5 - math.abs((spellData.tier or 1) - (character.level / 10))
        score = score + tierScore
        
        -- Factor 4: Spell type diversity
        local typeCount = 0
        local spellType = spellData.type or "damage"
        
        for discoveredId, _ in pairs(character.discoveredSpells) do
            local discoveredSpell = Magic.loadSpell(discoveredId)
            if discoveredSpell and discoveredSpell.type == spellType then
                typeCount = typeCount + 1
            end
        end
        
        -- Bonus for types the character has few of
        score = score + (5 / (typeCount + 1))
        
        -- Add to scoring table
        table.insert(scoredSpells, {
            id = spellId,
            score = score
        })
    end
    
    -- Sort by score (descending)
    table.sort(scoredSpells, function(a, b) return a.score > b.score end)
    
    -- Take top recommendations
    for i = 1, math.min(count, #scoredSpells) do
        table.insert(recommendations, scoredSpells[i].id)
    end
    
    return recommendations
end

-- Check if a spell is currently castable by a character
function Magic.canCastSpell(character, spellId)
    -- Check if character knows the spell
    if not character.discoveredSpells[spellId] then
        return false, "Spell not known"
    end
    
    -- Load spell data
    local spellData = Magic.loadSpell(spellId)
    if not spellData then
        return false, "Spell not found"
    end
    
    -- Check mana cost
    local manaCost = spellData.manaCost or 10
    
    -- Apply reductions
    if character.discoveredSpells[spellId].level > 1 and spellData.manaCostReduction then
        local reduction = (character.discoveredSpells[spellId].level - 1) * spellData.manaCostReduction
        manaCost = math.max(spellData.minimumManaCost or 1, manaCost - reduction)
    end
    
    if character.stats.mana < manaCost then
        return false, "Not enough mana"
    end
    
    -- Check for silence or other magic-blocking effects
    if character.statusEffects and (character.statusEffects.silence or character.statusEffects.magicBlock) then
        return false, "Cannot cast while silenced"
    end
    
    return true, nil
end

-- Get element color
function Magic.getElementColor(element)
    if Magic.elements[element] then
        return Magic.elements[element].color
    end
    return {1.0, 1.0, 1.0} -- Default white
end

-- Get spell count
function Magic.getSpellCount()
    local count = 0
    for _ in pairs(Magic.spells) do
        count = count + 1
    end
    return count
end

-- Get all elements
function Magic.getAllElements()
    local elements = {}
    for element, _ in pairs(Magic.elements) do
        table.insert(elements, element)
    end
    return elements
end

-- Get visible elements (excludes hidden ones)
function Magic.getVisibleElements()
    local elements = {}
    for element, data in pairs(Magic.elements) do
        if not data.hidden then
            table.insert(elements, element)
        end
    end
    return elements
end

-- Get spell info (for UI display)
function Magic.getSpellInfo(spellId)
    local spellData = Magic.loadSpell(spellId)
    if not spellData then return nil end
    
    return {
        id = spellId,
        name = spellData.name,
        description = spellData.description,
        element = spellData.element,
        type = spellData.type,
        tier = spellData.tier or 1,
        manaCost = spellData.manaCost or 10,
        levelRequirement = spellData.levelRequirement,
        icon = spellData.icon
    }
end

-- Generate random spells for treasure or rewards
function Magic.getRandomSpells(count, options)
    count = count or 1
    options = options or {}
    
    -- Filter options
    local minTier = options.minTier or 1
    local maxTier = options.maxTier or 5
    local elements = options.elements -- Specific elements to include, nil for all
    local types = options.types -- Specific spell types, nil for all
    
    -- Collect matching spells
    local matchingSpells = {}
    
    for spellId, spellData in pairs(Magic.spells) do
        local tier = spellData.tier or 1
        
        -- Check tier range
        if tier >= minTier and tier <= maxTier then
            -- Check element filter
            local elementMatch = not elements
            if elements then
                for _, element in ipairs(elements) do
                    if spellData.element == element then
                        elementMatch = true
                        break
                    end
                end
            end
            
            -- Check type filter
            local typeMatch = not types
            if types then
                for _, spellType in ipairs(types) do
                    if spellData.type == spellType then
                        typeMatch = true
                        break
                    end
                end
            end
            
            -- Add to matching spells if all filters match
            if elementMatch and typeMatch then
                table.insert(matchingSpells, spellId)
            end
        end
    end
    
    -- Randomize and select
    local result = {}
    
    -- If we don't have enough matching spells, return what we have
    if #matchingSpells <= count then
        return matchingSpells
    end
    
    -- Select random spells
    for i = 1, count do
        local randIndex = math.random(1, #matchingSpells)
        table.insert(result, matchingSpells[randIndex])
        table.remove(matchingSpells, randIndex)
    end
    
    return result
end

return Magic