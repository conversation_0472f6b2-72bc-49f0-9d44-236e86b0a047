-- classes.lua
-- Manages class definitions, skill selection, and progression

local Classes = {
    baseClasses = {},
    prestigeClasses = {},
    loadedClasses = {}
}

-- Load all class files dynamically
local function loadClassFiles()
    local lfs = love.filesystem
    local files = lfs.getDirectoryItems("classes")

    for _, file in ipairs(files) do
        if file:match("%.lua$") then
            local className = file:gsub("%.lua$", "")
            local success, classModule = pcall(require, "classes." .. className)
            if success and type(classModule) == "table" and classModule.name then
                Classes.loadedClasses[className] = classModule
                if classModule.prestige then
                    Classes.prestigeClasses[className] = classModule
                else
                    Classes.baseClasses[className] = classModule
                end
                print("Loaded class: " .. className)
            else
                print("Failed to load class: " .. className)
            end
        end
    end
end

-- Load all classes at startup
loadClassFiles()

-- Check if a class exists
function Classes.exists(className)
    return Classes.loadedClasses[className] ~= nil
end

-- Get skill tree for a class
function Classes.getSkillTree(className)
    local classData = Classes.loadedClasses[className]
    return classData and classData.skill_tree or nil
end

-- Get all available skills a player can select from (not forced)
function Classes.getAvailableSkills(className, level, unlockedSkills)
    local classData = Classes.loadedClasses[className]
    if not classData then return {} end

    local availableSkills = {}

    -- Include already unlocked skills
    for skillId, _ in pairs(unlockedSkills or {}) do
        availableSkills[skillId] = true
    end

    -- Add starting skills (guaranteed)
    for _, skillId in ipairs(classData.starting_skills or {}) do
        availableSkills[skillId] = true
    end

    -- Unlock skills based on level (but do NOT auto-learn them)
    if classData.skill_tree then
        for tier, skills in pairs(classData.skill_tree) do
            local requiredLevel = tier == "tier1" and 5 or tier == "tier2" and 15 or tier == "tier3" and 30
            if level >= requiredLevel then
                for _, skillId in ipairs(skills) do
                    availableSkills[skillId] = true
                end
            end
        end
    end

    return availableSkills
end

-- Let a player choose a skill at level-up (instead of auto-assigning)
function Classes.selectSkill(character, skillId)
    if not character or not character.class then return false, "Character has no class" end

    -- Get available skills
    local availableSkills = Classes.getAvailableSkills(character.class, character.level, character.unlockedSkills)

    if not availableSkills[skillId] then
        return false, "Skill not unlocked yet"
    end

    -- Ensure the skill hasn't already been learned
    if character.skills[skillId] then
        return false, "Skill already learned"
    end

    -- Assign the skill
    character.skills[skillId] = { level = 1 }
    return true, "Skill successfully learned"
end

-- Unlock a skill via achievements, quests, or other non-level-up events
function Classes.unlockSkillForCharacter(character, skillId)
    if not character then return false, "Invalid character" end

    -- Mark the skill as unlocked, but NOT learned yet
    character.unlockedSkills = character.unlockedSkills or {}
    character.unlockedSkills[skillId] = true

    return true, "Skill unlocked"
end

return Classes
