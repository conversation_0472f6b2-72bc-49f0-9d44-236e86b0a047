-- entities/undead_bear.lua
-- Undead bear variant with enhanced system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local UndeadBear = {
    id = "undead_bear",
    name = "Undead Bear",
    type = "undead_bear",
    shape = {
        {0, -10}, {7, -7}, {10, 0}, {7, 7},
        {0, 10}, {-7, 7}, {-10, 0}, {-7, -7}
    },
    size = 12,  -- Larger than living bears

    -- Entity categories
    categories = {"undead", "predator", "cursed", "massive"},
    threatCategories = {"player", "living", "holy"},
    
    -- Base stats (undead bears are tanks)
    maxHealth = 150,  -- More health than living bears
    health = 150,
    maxStamina = 50,  -- Lower stamina
    stamina = 50,
    speed = 1.5,      -- Slower than living bears
    attack = 30,      -- Higher attack
    defense = 18,     -- Higher defense
    undeadResistance = 20,
    holyVulnerability = 2.5,
    
    -- Behaviors
    behaviors = {"hunt_living", "territorial_undead", "rampage"},
    behaviorConfigs = {
        hunt_living = {
            moveSpeed = 2.0,
            huntRadius = 25,
            chaseRadius = 30,
            relentless = true
        },
        territorial_undead = {
            territoryRadius = 40,
            aggressionRadius = 20,
            no_retreat = true
        },
        rampage = {
            triggerHealth = 0.3,  -- Rampage when below 30% health
            damageBonus = 2.0,
            speedBonus = 1.5,
            duration = 15
        }
    },
    
    -- Enhanced variant system for undead bears
    variantChances = {
        normal = 0.60,          -- 60% zombie bear
        shiny = 0.30,           -- 30% bone bear (shiny)
        rare = 0.08,            -- 8% plague bear (rare)
        legendary = 0.02        -- 2% death lord bear (legendary)
    },
    
    variants = {
        normal = {
            name = "Zombie Bear",
            description = "A massive reanimated bear with rotting flesh",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.5, 0.6, 0.4, 1.0},  -- Rotting green-brown
                decay_effects = true,
                maggot_infested = true
            }
        },
        
        shiny = {
            name = "Bone Bear",
            description = "A colossal skeletal bear wreathed in dark energy",
            statModifiers = {
                maxHealth = 1.4,    -- 210 health
                speed = 1.3,        -- 1.95 speed
                attack = 1.3,       -- 39 attack
                defense = 1.2,      -- 21.6 defense
                bone_spikes = 1.5
            },
            appearanceModifiers = {
                scale = 1.2,
                glow = true,
                colorTint = {1.0, 1.0, 1.2, 1.0},  -- Bone white with dark glow
                skeletal_form = true,
                bone_spikes = true,
                dark_energy_swirl = true
            },
            soundModifiers = {
                pitch = 0.7,
                volume = 1.4,
                bone_grinding = true
            }
        },
        
        rare = {
            name = "Plague Bear",
            description = "A diseased undead bear that spreads corruption",
            statModifiers = {
                maxHealth = 1.8,    -- 270 health
                speed = 1.1,        -- 1.65 speed
                attack = 1.6,       -- 48 attack
                defense = 1.5,      -- 27 defense
                plague_aura = 2.0,
                disease_immunity = 5.0
            },
            appearanceModifiers = {
                scale = 1.3,
                colorTint = {0.6, 0.4, 0.6, 1.0},  -- Sickly purple-brown
                plague_boils = true,
                toxic_drool = true,
                disease_cloud = true
            },
            soundModifiers = {
                pitch = 0.6,
                volume = 1.5,
                reverb = true,
                diseased_wheeze = true
            }
        },
        
        legendary = {
            name = "Death Lord Bear",
            description = "An ancient undead bear lord commanding legions of the dead",
            statModifiers = {
                maxHealth = 2.5,    -- 375 health
                speed = 1.2,        -- 1.8 speed
                attack = 2.0,       -- 60 attack
                defense = 2.0,      -- 36 defense
                necromantic_mastery = 5.0,
                undead_command = 10.0
            },
            appearanceModifiers = {
                scale = 1.6,
                glow = true,
                colorTint = {0.8, 0.3, 0.8, 1.0},  -- Dark purple necromantic energy
                death_crown = true,
                necromantic_aura = "legendary",
                floating_skulls = true,
                reality_tear = true
            },
            soundModifiers = {
                pitch = 0.4,
                volume = 2.0,
                reverb = true,
                echo = true,
                death_lord_roar = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "cursed_meat", chance = 0.8, quantity = {3, 6}},
        {id = "undead_hide", chance = 0.7, quantity = {2, 4}},
        {id = "necrotic_claw", chance = 0.6, quantity = {2, 4}},
        {id = "dark_essence", chance = 0.8, quantity = {2, 3}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "massive_bear_skull", chance = 0.9, quantity = {1, 1}},
            {id = "bone_spike_armor", chance = 0.8, quantity = {1, 2}},
            {id = "skeletal_bear_ribcage", chance = 0.7, quantity = {1, 1}},
            {id = "dark_energy_crystal", chance = 0.6, quantity = {1, 2}}
        },
        rare = {
            {id = "plague_bear_heart", chance = 0.9, quantity = {1, 1}},
            {id = "disease_essence", chance = 0.8, quantity = {1, 3}},
            {id = "corruption_crystal", chance = 0.7, quantity = {1, 1}},
            {id = "plague_immunity_serum", chance = 0.5, quantity = {1, 1}},
            {id = "toxic_bear_bile", chance = 0.6, quantity = {1, 2}}
        },
        legendary = {
            {id = "death_lord_crown", chance = 0.95, quantity = {1, 1}},
            {id = "necromantic_bear_soul", chance = 0.9, quantity = {1, 1}},
            {id = "undead_command_scepter", chance = 0.8, quantity = {1, 1}},
            {id = "death_mastery_tome", chance = 0.7, quantity = {1, 1}},
            {id = "reality_tear_fragment", chance = 0.6, quantity = {1, 1}},
            {id = "legion_summoning_scroll", chance = 0.5, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "undead_bear",
        scale = 1.3,
        animations = {
            "idle", "shamble", "rampage", "roar_undead", "plague_breath", "necromancy"
        },
        variants = {
            "zombie_bear", "bone_bear", "plague_bear", "death_lord_bear"
        }
    },
    
    -- Sound effects with undead characteristics
    sounds = {
        roar_undead = {
            file = "undead_bear_roar",
            synth = {
                instrument = "organ",
                notes = {"C1", "E1", "G1"},
                durations = {0.8, 0.6, 1.2},
                volume = 0.9,
                terrifying = true
            }
        },
        plague_breath = {
            file = "plague_breath",
            synth = {
                instrument = "synthesizer",
                notes = {"D2", "F2", "Ab2"},
                durations = {0.6, 0.6, 0.8},
                volume = 0.7,
                toxic = true
            }
        },
        bone_grind = {
            file = "bone_grind",
            synth = {
                instrument = "percussion",
                notes = {"C2", "D2", "C2"},
                durations = {0.3, 0.3, 0.4},
                volume = 0.6,
                grinding = true
            }
        },
        necromantic_chant = {
            file = "necromantic_chant",
            synth = {
                instrument = "choir",
                notes = {"F2", "Ab2", "C3", "Eb3"},
                durations = {0.8, 0.8, 0.8, 1.2},
                volume = 0.8,
                ominous = true
            }
        }
    },
    
    -- Special undead abilities
    abilities = {
        undead_resilience = {
            type = "passive",
            description = "Immunity to poison, disease, and fear",
            effect = "status_immunity"
        },
        necrotic_claws = {
            type = "passive",
            description = "Claws inflict necrotic damage over time",
            effect = "damage_over_time"
        },
        plague_aura = {
            type = "passive",
            description = "Nearby enemies take disease damage",
            effect = "aura_damage"
        },
        raise_undead = {
            type = "active",
            description = "Raise nearby corpses as undead minions",
            effect = "summon_undead",
            cooldown = 30
        },
        death_roar = {
            type = "active",
            description = "Roar that instills terror and weakness",
            effect = "fear_debuff",
            cooldown = 20
        }
    }
}

-- Initialize the undead bear entity using enhanced template
function UndeadBear.init(entity, world)
    -- Copy all fields from UndeadBear template to entity instance
    for k, v in pairs(UndeadBear) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return UndeadBear
