-- items/weapons/shortbow.lua

local Weapon = {
    id = "shortbow",
    name = "Shortbow",
    description = "A compact bow that's easier to handle than a longbow but with less range.",
    type = "bow",
    category = "weapon",
    stackable = false,

    stats = {
        attack_power = 10.0,
        attack_speed = 1.0,
        range = 12.0,
        durability = 90.0,
        repair_cost = 25,
        weight = 2.0,
    },

    damage_type = "physical",
    material = "wood",

    sprite = "res://Sprite/items/weapons/shortbow.png",
    size = { width = 64, height = 64 },

    effects = {
        critical_chance = 0.06,
        requires_ammo = "arrows"
    },

    meta = {
        rarity = "common"
    },

    take_damage = function(self, amount)
        self.stats.durability = math.max(0, self.stats.durability - amount)
        if self.stats.durability == 0 then
            print(self.name .. " is broken! Penalties applied.")
            -- Potential penalties could be applied here
        end
    end,

    repair = function(self)
        local repair_amount = 90 - self.stats.durability
        self.stats.durability = 90
        print(self.name .. " repaired by " .. repair_amount .. " points.")
    end,
    
    onUse = function(self, user, target)
        -- Check if user has arrows
        if user and user.character and user.character.inventory then
            local inventory = user.character.inventory
            local hasArrows = false
            
            -- Check for arrows in inventory
            if inventory.items and inventory.items["arrows"] and inventory.items["arrows"].quantity > 0 then
                hasArrows = true
                -- Consume an arrow
                inventory.items["arrows"].quantity = inventory.items["arrows"].quantity - 1
                
                -- If we ran out of arrows, remove the entry
                if inventory.items["arrows"].quantity <= 0 then
                    inventory.items["arrows"] = nil
                end
                
                print(user.name .. " fired an arrow from the " .. self.name)
                return true
            else
                print("You need arrows to use this bow!")
                return false
            end
        end
        return false
    end
}

return Weapon
