local OverdriveAbility = {}

OverdriveAbility.name = "Titan Slash"
OverdriveAbility.effect = "high_damage"
OverdriveAbility.multiplier = 3.0  -- Deals 3x normal attack
OverdriveAbility.cost = 50         -- Uses 50% of Overdrive bar
OverdriveAbility.description = "A devastating sword strike that cleaves through enemies."

function OverdriveAbility.activate(character)
    if character.resources.overdrive < OverdriveAbility.cost then
        print(character.name .. " does not have enough Overdrive to use " .. OverdriveAbility.name .. "!")
        return false
    end

    character.resources.overdrive = character.resources.overdrive - OverdriveAbility.cost
    print(character.name .. " used Overdrive: " .. OverdriveAbility.name .. " - " .. OverdriveAbility.description)

    -- Apply ability effects (damage, status effects, etc.)
    -- Example: Increase character's attack for one hit
    character.stats.pattack = character.stats.pattack * OverdriveAbility.multiplier

    return true
end

return OverdriveAbility
