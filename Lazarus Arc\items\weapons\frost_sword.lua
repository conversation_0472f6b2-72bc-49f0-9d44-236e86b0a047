local FrostSword = {
    id = "frost_sword",
    name = "Frost Sword",
    type = "weapon",
    category = "sword",
    
    -- Properties
    properties = {
        damage = 25,
        attackSpeed = 1.2,
        range = 1.5,
        durability = 200,
        maxDurability = 200,
        frostChance = 0.3,
        frostDuration = 3,
        frostDamage = 5,
        frostRadius = 1.0,
        iceShardChance = 0.2,
        iceShardDamage = 8,
        iceShardCount = 3,
        iceShardSpread = 0.5,
        chillEffect = 0.3,
        chillDuration = 2,
        freezeChance = 0.1,
        freezeDuration = 1.5,
        iceBreakChance = 0.15,
        iceBreakRadius = 1.5,
        iceBreakDamage = 12,
        frostResistance = 0.5,
        temperature = -0.5
    },
    
    -- Appearance
    appearance = {
        sprite = "frost_sword",
        scale = 1.0,
        animations = {
            "idle",
            "swing",
            "frost",
            "break"
        },
        variants = {
            "crystal",
            "ice",
            "frost",
            "glacial"
        },
        blendMode = "add",
        tint = {0.7, 0.9, 1.0},
        alpha = 0.9
    },
    
    -- Sound effects
    sounds = {
        swing = "frost_sword_swing",
        hit = "frost_sword_hit",
        frost = "frost_sword_frost",
        break = "frost_sword_break"
    },
    
    -- Effects
    effects = {
        frost = {
            type = "status",
            duration = 3,
            effects = {
                slow = 0.3,
                damage = 5,
                radius = 1.0
            }
        },
        chill = {
            type = "status",
            duration = 2,
            effects = {
                slow = 0.2,
                temperature = -0.3
            }
        },
        freeze = {
            type = "status",
            duration = 1.5,
            effects = {
                immobilize = true,
                damage = 8
            }
        }
    }
}

-- Initialize the weapon
function FrostSword.init(weapon, world)
    -- Copy all fields from FrostSword template to weapon instance
    for k, v in pairs(FrostSword) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        frostCharge = 0,
        iceShards = 0
    }

    return weapon
end

-- Update the weapon
function FrostSword.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Update frost charge
        if world.temperature and world.temperature < 0 then
            weapon.properties.weaponState.frostCharge = math.min(1.0,
                weapon.properties.weaponState.frostCharge + dt * 0.2)
        else
            weapon.properties.weaponState.frostCharge = math.max(0,
                weapon.properties.weaponState.frostCharge - dt * 0.1)
        end
        
        -- Update ice shards
        if weapon.properties.weaponState.frostCharge > 0.8 then
            weapon.properties.weaponState.iceShards = math.min(5,
                weapon.properties.weaponState.iceShards + dt * 0.5)
        end
    end
end

-- Handle attack
function FrostSword.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Apply base damage
    if target.takeDamage then
        target.takeDamage(weapon.properties.damage)
    end
    
    -- Check for frost effect
    if math.random() < weapon.properties.frostChance then
        -- Apply frost effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.frost)
        end
        
        -- Create frost effect
        if world.createEffect then
            world.createEffect({
                type = "frost",
                position = target.position,
                radius = weapon.properties.frostRadius
            })
        end
        
        -- Play frost sound
        if world.playSound then
            world.playSound(weapon.sounds.frost)
        end
    end
    
    -- Check for ice shards
    if weapon.properties.weaponState.iceShards > 0 and math.random() < weapon.properties.iceShardChance then
        -- Create ice shards
        for i = 1, weapon.properties.iceShardCount do
            local angle = (i - 1) * (math.pi * 2 / weapon.properties.iceShardCount)
            local direction = {
                x = math.cos(angle) * weapon.properties.iceShardSpread,
                y = math.sin(angle) * weapon.properties.iceShardSpread
            }
            
            if world.createProjectile then
                world.createProjectile({
                    type = "ice_shard",
                    position = target.position,
                    direction = direction,
                    damage = weapon.properties.iceShardDamage
                })
            end
        end
        
        -- Reduce ice shards
        weapon.properties.weaponState.iceShards = math.max(0,
            weapon.properties.weaponState.iceShards - 1)
    end
    
    -- Check for freeze
    if math.random() < weapon.properties.freezeChance then
        -- Apply freeze effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.freeze)
        end
    end
    
    -- Check for ice break
    if math.random() < weapon.properties.iceBreakChance then
        -- Create ice break effect
        if world.createEffect then
            world.createEffect({
                type = "ice_break",
                position = target.position,
                radius = weapon.properties.iceBreakRadius,
                damage = weapon.properties.iceBreakDamage
            })
        end
        
        -- Play break sound
        if world.playSound then
            world.playSound(weapon.sounds.break)
        end
    end
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function FrostSword.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough frost charge
    if weapon.properties.weaponState.frostCharge < 0.5 then
        return false
    end
    
    -- Create frost nova effect
    if world.createEffect then
        world.createEffect({
            type = "frost_nova",
            position = attacker.position,
            radius = weapon.properties.frostRadius * 2,
            damage = weapon.properties.damage * 1.5
        })
    end
    
    -- Apply chill effect to nearby entities
    if world.entities then
        for _, entity in ipairs(world.entities) do
            if entity.position then
                local distance = math.sqrt(
                    (entity.position.x - attacker.position.x)^2 + 
                    (entity.position.y - attacker.position.y)^2
                )
                
                if distance <= weapon.properties.frostRadius * 2 then
                    if entity.applyEffect then
                        entity.applyEffect(weapon.effects.chill)
                    end
                end
            end
        end
    end
    
    -- Reset frost charge
    weapon.properties.weaponState.frostCharge = 0
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 5)
    
    return true
end

return FrostSword 