-- items/magic/magical_focus.lua

local MagicItem = {
    id = "magical_focus",
    name = "Magical Focus",
    description = "A crystal that helps channel magical energies more effectively.",
    type = "focus",
    category = "magic",
    stackable = false,

    stats = {
        magic_power = 5.0,
        mana_cost_reduction = 0.1, -- 10% reduction in mana costs
        weight = 0.5,
    },

    sprite = "res://Sprite/items/magic/magical_focus.png",
    size = { width = 32, height = 32 },

    effects = {
        spell_accuracy = 0.1,  -- 10% increased spell accuracy
        critical_magic = 0.05  -- 5% increased chance of critical magic hits
    },

    meta = {
        rarity = "uncommon"
    },

    onUse = function(self, user)
        if user and user.stats and user.stats.mana then
            -- Restore a small amount of mana
            local manaRestored = math.min(10, user.stats.maxMana - user.stats.mana)
            user.stats.mana = user.stats.mana + manaRestored
            
            print(user.name .. " channeled energy through the magical focus, restoring " .. manaRestored .. " mana.")
            return true
        end
        return false
    end,
    
    -- When equipped, provides passive bonuses
    onEquip = function(self, user)
        if user and user.stats then
            -- Apply mana cost reduction
            if user.stats.mana_cost_modifier then
                user.stats.mana_cost_modifier = user.stats.mana_cost_modifier * (1 - self.stats.mana_cost_reduction)
            else
                user.stats.mana_cost_modifier = 1 - self.stats.mana_cost_reduction
            end
            
            -- Apply spell accuracy bonus
            if user.stats.spell_accuracy then
                user.stats.spell_accuracy = user.stats.spell_accuracy + self.effects.spell_accuracy
            else
                user.stats.spell_accuracy = self.effects.spell_accuracy
            end
            
            -- Apply critical magic chance
            if user.stats.critical_magic_chance then
                user.stats.critical_magic_chance = user.stats.critical_magic_chance + self.effects.critical_magic
            else
                user.stats.critical_magic_chance = self.effects.critical_magic
            end
            
            print(user.name .. " equipped the magical focus, gaining magical bonuses.")
        end
    end,
    
    -- When unequipped, removes passive bonuses
    onUnequip = function(self, user)
        if user and user.stats then
            -- Remove mana cost reduction
            if user.stats.mana_cost_modifier then
                user.stats.mana_cost_modifier = user.stats.mana_cost_modifier / (1 - self.stats.mana_cost_reduction)
            end
            
            -- Remove spell accuracy bonus
            if user.stats.spell_accuracy then
                user.stats.spell_accuracy = user.stats.spell_accuracy - self.effects.spell_accuracy
            end
            
            -- Remove critical magic chance
            if user.stats.critical_magic_chance then
                user.stats.critical_magic_chance = user.stats.critical_magic_chance - self.effects.critical_magic
            end
            
            print(user.name .. " unequipped the magical focus, losing magical bonuses.")
        end
    end
}

return MagicItem
