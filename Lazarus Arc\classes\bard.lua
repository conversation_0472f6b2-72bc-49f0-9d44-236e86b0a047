-- classes/bard.lua
-- Bard class definition for Lazarus Arc

local Bard = {
    name = "Bard",
    description = "A mystical performer whose music and storytelling enhance allies, weaken foes, and influence the battlefield.",
    icon = "bard_icon.png",

    -- 🎭 **Playstyle & Trade-offs**
    pros = {
        "Versatile spellcasting through music",
        "Strong support abilities for allies",
        "Can charm, stun, or debuff enemies"
    },
    cons = {
        "Weaker direct attacks compared to Fighters or Mages",
        "Requires strategic positioning to avoid getting caught",
        "Song effects take time to build up"
    },

    -- 🏋 **Base Stats & Growth**
    base_stats = { 
        health = 90, 
        maxHealth = 90,
        stamina = 60, 
        maxStamina = 60,
        mana = 80, 
        maxMana = 80,
        pattack = 8, 
        pdefense = 5, 
        mattack = 10, 
        mdefense = 7, 
        speed = 7, 
        luck = 10 
    },

    stat_growth = {
        health = 7,
        stamina = 5,
        mana = 8,
        pattack = 0.6,
        pdefense = 0.5,
        mattack = 1.0,
        mdefense = 0.8,
        speed = 0.7,
        luck = 1.0
    },

    -- ⚔️ **Weapon Proficiency**
    proficiencies = { 
        dagger = 1.5,  
        sword = 1.0,  
        bow = 1.2,  
        crossbow = 1.1,  
        staff = 1.3,  
        magic = 1.8,  -- Uses magical music-based casting
        gun = 0.7  
    },

    -- 🌿 **Elemental Affinities**
    element_affinities = {
        air = 10,   -- Sound-based attacks travel further
        water = 5,  -- Fluid and adaptive
        light = 5,  -- Can inspire hope and clarity
        earth = -5  -- Struggles in highly structured or rigid combat styles
    },

    -- 💥 **Overdrive Abilities**
    overdrive = { 
        "symphony_of_destruction", -- A powerful AOE attack that deals sonic damage
        "encore", -- Instantly refreshes all active buffs for allies
        "soul_resonance", -- Channels powerful energy, boosting all stats temporarily
    },

    -- 🎯 **Starting Skills**
    starting_skills = {
        "melody_of_courage",
        "discordant_strike",
        "hymn_of_mending"
    },

    -- 📈 **Skill Progression**
    skill_tree = {
        tier1 = {
            "song_of_swiftness",
            "charming_tune",
            "harmony_shield"
        },
        tier2 = {
            "discordant_melody",
            "battle_ballad", 
            "soothing_sonata",
            "note_of_silence"
        },
        tier3 = {
            "crescendo",
            "operatic_wrath",
            "requiem_of_souls"
        }
    },

    -- 🎒 **Inventory & Carrying Capacity**
    inventory_limit = 90, -- Lighter load due to instruments

    -- ✨ **Special Abilities (Passive Perks)**
    special_abilities = {
        "Stage Presence", -- Increased effectiveness of buffs and debuffs
        "Inspire Greatness", -- Small chance to give allies bonus actions
        "Harmonic Resonance", -- Songs build in power if continuously played
        "Counterpoint", -- If silenced, can still fight with physical attacks
        "Bardic Luck", -- Increased success rate on charm and persuasion attempts
    },

    -- 🎭 **Prestige Paths**
    prestige_paths = {
        virtuoso = {
            description = "A master of melody and magic, capable of bending reality with song.",
            bonuses = {
                "More powerful sound-based magic",
                "Increased mana regeneration",
                "Can link songs together for extended effects"
            },
            weaknesses = {
                "Extremely mana-dependent",
                "Weak against silence effects"
            }
        },

        skald = {
            description = "A battle-hardened Bard who wields a sword as well as a song.",
            bonuses = {
                "Can fight effectively while singing",
                "Improved melee damage",
                "Resistant to fear effects"
            },
            weaknesses = {
                "Songs are slightly weaker than pure spellcasting Bards",
                "Requires strong stamina management"
            }
        },

        trickster = {
            description = "A deceptive performer who uses illusions and misdirection to manipulate the battlefield.",
            bonuses = {
                "Can create temporary duplicates of themselves",
                "Higher evasion when playing music",
                "Can confuse enemies with auditory illusions"
            },
            weaknesses = {
                "Lower damage overall",
                "Illusions can be dispelled by true sight or dispel magic"
            }
        }
    },

    -- ⚔️ **Hybrid & Multiclass Options**
    hybrid_options = {
        battle_bard = {
            description = "A Bard who mixes musical magic with heavy armor and melee weapons.",
            pros = {
                "More durable than normal Bards",
                "Can wear medium armor without penalties"
            },
            cons = {
                "Loses some mobility and song effectiveness",
                "Heavier weapons reduce spellcasting speed"
            }
        },

        shadow_maestro = {
            description = "A Bard who blends music and shadow magic for deceptive, stealth-based combat.",
            pros = {
                "Can phase between shadows while performing",
                "Songs can carry dark energy to weaken enemies"
            },
            cons = {
                "Less effective against enemies with high magic resistance",
                "Requires constant movement to avoid being overwhelmed"
            }
        }
    }
}

return Bard
