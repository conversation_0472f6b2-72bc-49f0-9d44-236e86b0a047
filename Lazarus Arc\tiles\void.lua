local Void = {
    id = "void",
    name = "Void",
    type = "tile",
    
    -- Categories
    categories = {"terrain", "void", "dangerous", "special"},
    
    -- Properties
    properties = {
        walkable = false,
        swimable = false,
        climbable = false,
        flyable = false,
        diggable = false,
        buildable = false,
        flammable = false,
        damage = 100,
        damageInterval = 0.1,
        pullForce = 0.5,
        pullRadius = 2,
        lightAbsorption = 0.9,
        particleRate = 0.2,
        particleSpeed = 0.3,
        particleSize = 0.1,
        distortionStrength = 0.8,
        distortionRadius = 3,
        teleportChance = 0.01,
        teleportRadius = 5
    },
    
    -- Appearance
    appearance = {
        sprite = "void",
        scale = 1.0,
        animations = {
            "idle",
            "distort",
            "pull",
            "teleport"
        },
        variants = {
            "dark",
            "black",
            "abyss",
            "portal"
        },
        blendMode = "multiply",
        tint = {0.0, 0.0, 0.0},
        alpha = 0.95
    },
    
    -- Sound effects
    sounds = {
        pull = "void_pull",
        teleport = "void_teleport",
        distort = "void_distort"
    },
    
    -- Effects
    effects = {
        void = {
            type = "environment",
            duration = 0,
            effects = {
                lightAbsorption = 0.9,
                distortion = true,
                pullForce = 0.5
            }
        },
        teleport = {
            type = "special",
            duration = 0,
            effects = {
                teleportChance = 0.01,
                teleportRadius = 5
            }
        }
    },
    
    -- Spawn rules
    spawnRules = {
        structures = {
            "void_portal",
            "void_crystal",
            "void_formation"
        }
    }
}

-- Initialize the tile
function Void.init(tile, world)
    -- Copy all fields from Void template to tile instance
    for k, v in pairs(Void) do
        if type(v) ~= "function" and tile[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                tile[k] = {}
                for subk, subv in pairs(v) do
                    tile[k][subk] = subv
                end
            else
                tile[k] = v
            end
        end
    end

    -- Set random variant
    if tile.appearance and tile.appearance.variants then
        local variants = tile.appearance.variants
        tile.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize void state
    tile.properties.voidState = {
        lastDamageTime = 0,
        particleTimer = 0,
        distortionTimer = 0,
        pullStrength = 0
    }

    return tile
end

-- Update the tile
function Void.update(tile, world, dt)
    -- Update void state
    if tile.properties.voidState then
        -- Update particle effects
        tile.properties.voidState.particleTimer = tile.properties.voidState.particleTimer + dt
        if tile.properties.voidState.particleTimer >= 1 / tile.properties.particleRate then
            tile.properties.voidState.particleTimer = 0
            -- Create void particle effect
            if world.createEffect then
                world.createEffect({
                    type = "void_particle",
                    position = tile.position,
                    speed = tile.properties.particleSpeed,
                    size = tile.properties.particleSize
                })
            end
        end
        
        -- Update distortion effect
        tile.properties.voidState.distortionTimer = tile.properties.voidState.distortionTimer + dt
        if tile.properties.voidState.distortionTimer >= 1 then
            tile.properties.voidState.distortionTimer = 0
            -- Create distortion effect
            if world.createEffect then
                world.createEffect({
                    type = "void_distortion",
                    position = tile.position,
                    radius = tile.properties.distortionRadius,
                    strength = tile.properties.distortionStrength
                })
            end
        end
        
        -- Apply void effects to nearby entities
        if world.entities then
            for _, entity in ipairs(world.entities) do
                if entity.position then
                    local distance = math.sqrt(
                        (entity.position.x - tile.position.x)^2 + 
                        (entity.position.y - tile.position.y)^2
                    )
                    
                    if distance <= tile.properties.pullRadius then
                        -- Calculate pull force based on distance
                        local pullStrength = (1 - distance / tile.properties.pullRadius) * tile.properties.pullForce
                        
                        -- Apply pull force
                        if entity.move then
                            local angle = math.atan2(
                                tile.position.y - entity.position.y,
                                tile.position.x - entity.position.x
                            )
                            entity.move({
                                x = math.cos(angle) * pullStrength,
                                y = math.sin(angle) * pullStrength
                            })
                        end
                        
                        -- Check for teleport
                        if distance <= 0.5 and math.random() < tile.properties.teleportChance then
                            -- Find random position within teleport radius
                            local angle = math.random() * math.pi * 2
                            local radius = math.random() * tile.properties.teleportRadius
                            local newX = tile.position.x + math.cos(angle) * radius
                            local newY = tile.position.y + math.sin(angle) * radius
                            
                            -- Teleport entity
                            if entity.setPosition then
                                entity.setPosition({x = newX, y = newY})
                            end
                            
                            -- Play teleport sound
                            if world.playSound then
                                world.playSound(tile.sounds.teleport)
                            end
                        end
                        
                        -- Apply damage
                        if world.time - tile.properties.voidState.lastDamageTime >= tile.properties.damageInterval then
                            if entity.takeDamage then
                                entity.takeDamage(tile.properties.damage)
                            end
                            tile.properties.voidState.lastDamageTime = world.time
                        end
                    end
                end
            end
        end
    end
end

-- Handle interaction
function Void.interact(tile, world, entity, action)
    if action == "touch" then
        -- Apply void effect
        if entity.applyEffect then
            entity.applyEffect(tile.effects.void)
        end
        
        -- Apply damage
        if entity.takeDamage then
            entity.takeDamage(tile.properties.damage)
        end
        
        -- Play void sound
        if world.playSound then
            world.playSound(tile.sounds.pull)
        end
    end
end

return Void 