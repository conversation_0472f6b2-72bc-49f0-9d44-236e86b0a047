-- behaviors/healing.lua
-- Healing/Medical behavior for NPCs involved in medical aid or healing services

local HealingBehavior = {}  -- create an empty table

HealingBehavior.id = "healing"
HealingBehavior.name = "Healing/Medical Behavior"

-- Configuration parameters
HealingBehavior.defaultConfig = {
    healingRadius = 8,               -- Radius to detect entities needing healing
    healAmount = {10, 20},           -- Amount of health restored per healing action
    healInterval = {5, 10},          -- Interval between healing actions (seconds)
    moveSpeed = 1.2,                 -- Movement speed while seeking patients
    gatherResources = true,          -- Whether to gather medical supplies
    resourceTypes = {"herbs", "medicine"}, -- Types of resources used for healing
    restockInterval = {300, 600},    -- Time interval between restocking supplies
    maxCarryCapacity = 10,           -- Maximum supplies entity can carry
    retreatIfThreatened = true,      -- Retreat if threats detected
    threatDetectionRadius = 8        -- Radius to detect threats
}

-- Initialize the behavior
function HealingBehavior.init(entity, customConfig)
    entity.medicalState = entity.medicalState or {
        currentPatient = nil,
        supplies = 0,
        config = {}
    }

    local config = entity.medicalState.config
    for k, v in pairs(HealingBehavior.defaultConfig) do
        config[k] = v
    end

    if customConfig then
        for k, v in pairs(customConfig) do
            config[k] = v
        end
    end

    -- Now that config is merged, set healTimer using the merged healInterval
    entity.medicalState.healTimer = math.random(config.healInterval[1], config.healInterval[2])
end

-- Update medical behavior
function HealingBehavior.update(entity, world, dt)
    local state = entity.medicalState
    local config = state.config

    -- Detect threats and potentially retreat
    if config.retreatIfThreatened and HealingBehavior.detectThreat(entity, world) then
        HealingBehavior.retreat(entity, dt)
        return
    end

    -- Find entities needing healing
    local patient = HealingBehavior.findPatient(entity, world)
    if patient then
        HealingBehavior.moveToAndHeal(entity, patient, dt)
    elseif config.gatherResources then
        HealingBehavior.gatherSupplies(entity, world, dt)
    end
end

-- Find patient needing healing
function HealingBehavior.findPatient(entity, world)
    -- Implementation details...
end

-- Move to patient and heal
function HealingBehavior.moveToAndHeal(entity, patient, dt)
    -- Implementation details...
end

-- Retreat from threats
function HealingBehavior.retreat(entity, dt)
    -- Implementation details...
end

-- Detect threats (placeholder)
function HealingBehavior.detectThreat(entity, world)
    -- Implementation details...
end

-- Gather supplies (placeholder)
function HealingBehavior.gatherSupplies(entity, world, dt)
    -- Implementation details...
end

return HealingBehavior
