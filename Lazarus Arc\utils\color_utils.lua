-- color_utils.lua
-- Utility functions for color handling in Lazarus Arc

local ColorUtils = {
    enabled = true  -- Whether full colors are enabled (can be toggled in debug menu)
}

-- Predefine tile color palettes (fast lookup)
local TILE_COLORS = {
    grass = {0.3,0.7,0.3}, water = {0.2,0.5,0.8}, deep_water = {0.1,0.3,0.7},
    sand = {0.9,0.8,0.5}, desert_sand = {0.8,0.7,0.4}, road = {0.6,0.6,0.5},
    dirt = {0.6,0.4,0.2}, mud = {0.4,0.3,0.2}, snow = {0.9,0.9,0.95},
    ice = {0.8,0.9,1.0}, lava = {0.9,0.3,0.1}, stone = {0.5,0.5,0.55},
    mountain = {0.6,0.5,0.5}, forest = {0.2,0.5,0.2}, hub = {0.7,0.7,0.9},
    hub_floor = {0.7,0.7,0.9}, hub_grass = {0.4,0.8,0.4}, hub_path = {0.8,0.8,0.7},
    sanctuary_stone = {0.9,0.8,1.0}, ruins = {0.7,0.7,0.6}, crystal_formation = {0.7,0.9,1.0},
    ancient_technology = {0.6,0.8,0.9}, shallow_water = {0.3,0.6,0.9},
    forest_floor = {0.3,0.6,0.3}, dirt_path = {0.7,0.6,0.4}, plains = {0.5,0.8,0.3}, void = {0.1,0.1,0.2},
}

-- Helper to convert a color to grayscale
local function toGrayColor(c)
    local v = (c[1] + c[2] + c[3]) / 3
    return {v, v, v}
end

-- Get the color for a specific tile type
function ColorUtils.getTileColor(tileType)
    -- Lookup base color or use default gray
    local c = TILE_COLORS[tileType] or {0.5,0.5,0.5}
    -- Return color or grayscale based on enabled flag
    if ColorUtils.enabled then return c else return toGrayColor(c) end
end

-- Determine behavioral category for color coding
function ColorUtils.getBehavioralCategory(entityType)
    -- PASSIVE MOBS (Grey) - peaceful animals that don't attack
    local passiveMobs = {
        "rabbit", "deer", "sheep", "goat", "horse", "untamed_horse",
        "bird", "pigeon", "songbird", "duck", "goose", "swan", "crane", "flamingo", "pelican",
        "butterfly", "fly", "mouse", "field_mouse", "squirrel", "frog", "turtle", "crab",
        -- Fish are generally passive
        "bass", "trout", "carp", "catfish", "bluegill", "perch", "pike", "walleye", "sunfish",
        "minnow", "goldfish", "guppy", "angelfish", "betta", "crappie", "muskellunge", "northern_pike",
        "plecostomus", "tuna", "swordfish", "ray", "dolphin"
    }

    -- AGGRESSIVE MOBS (Red) - hostile creatures that attack
    local aggressiveMobs = {
        "wolf", "fox", "bear", "crow", "hawk", "eagle", "vulture", "great_horned_owl",
        "snowy_owl", "owl", "raccoon", "shark", "whale"
    }

    -- BOSS/ELITE MOBS (Purple) - special powerful creatures
    local bossMobs = {
        "ancient_treant", "forest_golem", "mystic_deer", "mimic"
    }

    -- RESOURCE MOBS (Green) - creatures that provide valuable resources
    local resourceMobs = {
        "sheep", "goat", "horse" -- These can be both passive and resource
    }

    -- Check categories in priority order
    for _, mob in ipairs(bossMobs) do
        if mob == entityType then return "boss" end
    end

    for _, mob in ipairs(aggressiveMobs) do
        if mob == entityType then return "aggressive" end
    end

    for _, mob in ipairs(resourceMobs) do
        if mob == entityType then return "resource" end
    end

    for _, mob in ipairs(passiveMobs) do
        if mob == entityType then return "passive" end
    end

    return nil -- No category found, use specific entity colors
end

-- Get the color for a specific entity type
function ColorUtils.getEntityColor(entityType)
    local color = {1, 1, 1} -- Default white

    if not entityType then
        return color
    end

    if ColorUtils.enabled then
        -- First check for behavioral category colors
        local category = ColorUtils.getBehavioralCategory(entityType)
        if category == "passive" then
            color = {0.7, 0.7, 0.7} -- Grey for passive mobs
        elseif category == "aggressive" then
            color = {0.8, 0.2, 0.2} -- Red for aggressive mobs
        elseif category == "resource" then
            color = {0.2, 0.8, 0.2} -- Green for resource mobs
        elseif category == "boss" then
            color = {0.8, 0.2, 0.8} -- Purple for boss/elite mobs
        -- Fall back to specific entity colors for special cases
        elseif entityType == "player" then
            color = {1, 0, 0}
        elseif entityType == "npc" then
            color = {0, 0.7, 0}
        elseif entityType == "enemy" then
            color = {0.8, 0.2, 0.2}
        elseif entityType == "item" then
            color = {0.9, 0.9, 0.2}
        elseif entityType == "trader" then
            color = {0.3, 0.6, 0.9}
        elseif entityType == "quest_giver" then
            color = {0.9, 0.8, 0.2}
        elseif entityType == "elite" then
            color = {0.9, 0.3, 0.9}
        end
    else
        -- When disabled, use grayscale
        local category = ColorUtils.getBehavioralCategory(entityType)
        if category then
            -- Use different shades of gray for different categories
            if category == "passive" then
                color = {0.6, 0.6, 0.6}
            elseif category == "aggressive" then
                color = {0.4, 0.4, 0.4}
            elseif category == "resource" then
                color = {0.5, 0.5, 0.5}
            elseif category == "boss" then
                color = {0.3, 0.3, 0.3}
            end
        else
            -- Fall back to specific grayscale colors
            if entityType == "player" then
                color = {0.7, 0.7, 0.7}
            elseif entityType == "npc" then
                color = {0.6, 0.6, 0.6}
            elseif entityType == "enemy" then
                color = {0.4, 0.4, 0.4}
            elseif entityType == "item" then
                color = {0.8, 0.8, 0.8}
            elseif entityType == "trader" then
                color = {0.6, 0.6, 0.6}
            elseif entityType == "quest_giver" then
                color = {0.7, 0.7, 0.7}
            elseif entityType == "elite" then
                color = {0.5, 0.5, 0.5}
            end
        end
    end

    return color
end

-- Toggle color mode (enable/disable colors)
function ColorUtils.toggle()
    ColorUtils.enabled = not ColorUtils.enabled
    return ColorUtils.enabled
end

-- Isometric mode flag
ColorUtils.isometricDebug = true

-- Convert world coordinates to isometric coordinates with height support
function ColorUtils.toIsometric(x, y, height)
    local tileWidth = 32
    local tileHeight = 16
    local isoX = (x - y) * (tileWidth / 2)
    local isoY = (x + y) * (tileHeight / 4)

    -- Apply height offset if provided (moves the tile upward)
    if height and height > 0 then
        -- Each unit of height moves the tile up by half the tile height
        isoY = isoY - (height * tileHeight / 2)
    end

    return isoX, isoY
end

-- Draw a tile with proper isometric or top-down rendering with variable height support
function ColorUtils.drawTile(x, y, width, height, color, tileData)
    if ColorUtils.isometricDebug then
        -- Get tile height from tileData or default to 0
        local tileHeight = 0
        if tileData and tileData.height then
            tileHeight = tileData.height
        end

        -- Extruded cube top and side faces
        local halfW = width / 2
        local topH = height / 2

        -- Calculate the vertical offset based on tile height
        local heightOffset = tileHeight * height / 2

        -- Top face - raised according to tile height
        love.graphics.setColor(color)
        love.graphics.polygon("fill",
            x, y - topH - heightOffset,       -- top
            x + halfW, y - heightOffset,      -- right
            x, y + topH - heightOffset,       -- bottom
            x - halfW, y - heightOffset       -- left
        )

        -- Side faces depth - increased based on tile height
        local depth = topH + heightOffset

        -- Left face (darker)
        love.graphics.setColor(color[1]*0.7, color[2]*0.7, color[3]*0.7)
        love.graphics.polygon("fill",
            x - halfW, y - heightOffset,                  -- top-left
            x, y + topH - heightOffset,                   -- top-bottom
            x, y + topH,                                  -- ground-bottom
            x - halfW, y                                  -- ground-left
        )

        -- Right face (slightly brighter)
        love.graphics.setColor(color[1]*0.85, color[2]*0.85, color[3]*0.85)
        love.graphics.polygon("fill",
            x + halfW, y - heightOffset,                  -- top-right
            x, y + topH - heightOffset,                   -- top-bottom
            x, y + topH,                                  -- ground-bottom
            x + halfW, y                                  -- ground-right
        )

        -- Reset color
        love.graphics.setColor(1,1,1)
    else
        love.graphics.setColor(color)
        love.graphics.rectangle("fill", x, y, width, height)
    end
end

return ColorUtils