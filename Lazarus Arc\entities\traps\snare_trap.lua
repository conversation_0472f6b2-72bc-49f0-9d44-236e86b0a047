local SnareTrap = {
    id = "snare_trap",
    name = "Snare Trap",
    type = "trap",
    
    -- Entity categories
    categories = {"trap", "dungeon", "physical"},
    
    -- Target categories
    targetCategories = {"player", "monster"},
    
    -- Stats
    maxHealth = 20,
    health = 20,
    duration = 12,
    cooldown = 30,
    
    -- Trap properties
    properties = {
        radius = 1.0,
        setupTime = 2,
        isArmed = false,
        isTriggered = false,
        destroyOnActivate = false,
        immobilizeDuration = 5,
        slowAmount = 0.7
    },
    
    -- Appearance
    appearance = {
        sprite = "snare_trap",
        scale = 1.0,
        animations = {
            "idle", "trigger", "snare"
        },
        variants = {
            "rope", "chain", "vine", "web"
        }
    },
    
    -- Sound effects
    sounds = {
        trigger = "snare_trap_trigger",
        snap = "snare_trap_snap",
        release = "snare_trap_release"
    },
    
    -- Effects
    effects = {
        snare = {
            type = "status",
            duration = 5,
            effects = {
                immobilize = true,
                slow = 0.7,
                damage = 1,
                interval = 2
            }
        },
        slow = {
            type = "status",
            duration = 7,
            effects = {
                slow = 0.7
            }
        }
    }
}

-- Initialize the trap
function SnareTrap.init(entity, world)
    -- Copy all fields from SnareTrap template to entity instance
    for k, v in pairs(SnareTrap) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position
    entity.position = entity.position or {x = 0, y = 0}

    -- Set random variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize trap state
    entity.properties.isArmed = false
    entity.properties.isTriggered = false
    entity.properties.triggerTime = 0
    entity.properties.snaredTargets = {}

    return entity
end

-- Update the trap
function SnareTrap.update(entity, world, dt)
    -- Check if trap is armed and not triggered
    if entity.properties.isArmed and not entity.properties.isTriggered then
        -- Check for targets in radius
        if world and world.entities then
            for _, target in ipairs(world.entities) do
                if target.position and target.categories then
                    -- Check if target is in trap categories
                    local isTarget = false
                    for _, category in ipairs(entity.targetCategories) do
                        if table.contains(target.categories, category) then
                            isTarget = true
                            break
                        end
                    end
                    
                    if isTarget then
                        local distance = math.sqrt(
                            (target.position.x - entity.position.x)^2 + 
                            (target.position.y - entity.position.y)^2
                        )
                        
                        if distance <= entity.properties.radius then
                            -- Trigger trap
                            entity.properties.isTriggered = true
                            entity.properties.triggerTime = world.time
                            
                            -- Add target to snared list
                            table.insert(entity.properties.snaredTargets, target)
                            
                            -- Apply snare effect to target
                            if target.applyEffect then
                                target.applyEffect(entity.effects.snare)
                            end
                            
                            -- Play trigger sound
                            if world.playSound then
                                world.playSound(entity.sounds.trigger)
                            end
                            
                            break
                        end
                    end
                end
            end
        end
    end
    
    -- Handle snared targets
    if entity.properties.isTriggered then
        for _, target in ipairs(entity.properties.snaredTargets) do
            if target.position then
                -- Keep target in place
                target.position.x = entity.position.x
                target.position.y = entity.position.y
                
                -- Apply slow effect after immobilization wears off
                if world.time - entity.properties.triggerTime >= entity.properties.immobilizeDuration then
                    if target.applyEffect then
                        target.applyEffect(entity.effects.slow)
                    end
                end
            end
        end
    end
    
    -- Check if trap should be destroyed
    if entity.properties.isTriggered and world.time - entity.properties.triggerTime >= entity.duration then
        -- Release snared targets
        for _, target in ipairs(entity.properties.snaredTargets) do
            if target.applyEffect then
                target.applyEffect(entity.effects.slow)
            end
        end
        
        if world.removeEntity then
            world.removeEntity(entity)
        end
    end
    
    -- Check if trap can be rearmed
    if not entity.properties.isArmed and world.time - entity.properties.triggerTime >= entity.cooldown then
        entity.properties.isArmed = true
        entity.properties.isTriggered = false
        entity.properties.snaredTargets = {}
    end
end

-- Arm the trap
function SnareTrap.arm(entity, world)
    if not entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = true
        entity.properties.triggerTime = world.time
        entity.properties.snaredTargets = {}
        return true
    end
    return false
end

-- Disarm the trap
function SnareTrap.disarm(entity, world)
    if entity.properties.isArmed and not entity.properties.isTriggered then
        entity.properties.isArmed = false
        entity.properties.triggerTime = world.time
        entity.properties.snaredTargets = {}
        return true
    end
    return false
end

return SnareTrap 