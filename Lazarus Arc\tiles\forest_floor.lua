-- tiles/forest_floor.lua
local ForestFloorTile = {
    id = "forest_floor",
    name = "Forest Floor",
    passable = true,
    variants = 5, -- Different forest floor textures (leafy, mossy, etc.)

    -- Entities that can spawn on this tile
    spawns = {"mushroom", "sapling", "fern", "fox", "squirrel", "hedgehog", "forest_beetle"},

    -- Movement properties
    movementSpeed = 0.9, -- Slightly slower movement through underbrush

    -- Interaction function
    interact = function(tile, entity)
        -- Chance to find forest items
        if math.random() < 0.25 then
            local items = {
                {name = "mushroom", chance = 0.4},
                {name = "forest_herb", chance = 0.3},
                {name = "acorn", chance = 0.2},
                {name = "truffle", chance = 0.05},
                {name = "magic_mushroom", chance = 0.05}
            }

            for _, item in ipairs(items) do
                if math.random() < item.chance then
                    return {
                        success = true,
                        message = "You found a " .. item.name .. " on the forest floor.",
                        effects = {
                            {type = "add_item", item = item.name, quantity = 1}
                        }
                    }
                end
            end
        end

        return {
            success = false,
            message = "The forest floor is covered with leaves, twigs, and patches of moss."
        }
    end,

    -- Weather effects
    weatherEffects = {
        rain = function(tile)
            tile.moisture = (tile.moisture or 0.5) + 0.2
            if tile.moisture > 0.8 and math.random() < 0.2 then
                return "spawn", "mushroom"
            end
            return nil
        end,

        sun = function(tile)
            tile.moisture = (tile.moisture or 0.5) - 0.1
            if tile.moisture < 0 then
                tile.moisture = 0
            end
            return nil
        end,

        wind = function(tile)
            if math.random() < 0.3 then
                tile.leafCover = (tile.leafCover or 0.5) + 0.1
                if tile.leafCover > 1 then
                    tile.leafCover = 1
                    tile.variant = 1 -- Heavy leaf cover variant
                end
            end
            return nil
        end,

        snow = function(tile)
            tile.isSnowCovered = true
            tile.variant = 5 -- Snow-covered variant
            return nil
        end
    },

    -- Season effects
    seasonEffects = {
        spring = function(tile)
            tile.leafCover = 0.3
            tile.moisture = 0.7
            tile.variant = 3 -- Mossy variant
            if math.random() < 0.15 then
                return "spawn", "forest_flower"
            end
            return nil
        end,

        summer = function(tile)
            tile.leafCover = 0.4
            tile.moisture = 0.5
            tile.variant = 2 -- Standard forest floor
            return nil
        end,

        fall = function(tile)
            tile.leafCover = 0.9
            tile.moisture = 0.6
            tile.variant = 1 -- Heavy leaf cover variant
            return nil
        end,

        winter = function(tile)
            tile.leafCover = 0.7
            tile.moisture = 0.3
            if math.random() < 0.8 then
                tile.isSnowCovered = true
                tile.variant = 5 -- Snow-covered variant
            else
                tile.variant = 4 -- Bare variant
            end
            return nil
        end
    },

    -- Visual properties
    visual = {
        base_color = {0.5, 0.4, 0.3}, -- Brown
        variants = {
            {name = "leafy", overlay = "fallen_leaves"},
            {name = "standard", overlay = nil},
            {name = "mossy", overlay = "moss_patches"},
            {name = "bare", overlay = "bare_dirt_patches"},
            {name = "snow_covered", overlay = "snow"}
        },
        shadow = 0.7,
        ambient_occlusion = true,
        foliageDensity = 0.5
    },

    -- Audio properties
    footstepSound = "forest_floor_step",
    ambientSound = "forest_floor_rustle",

    -- Forest properties
    foliageDensity = {0.3, 0.7},
    undergrowthDensity = 0.4,

    -- Tree proximity
    treeProximity = 0.8,
    preferredTrees = {"oak", "pine", "maple", "birch"},

    -- Connection to other tiles
    connects_to = {
        "grass",
        "dirt",
        "dirt_path",
        "shallow_water"
    },

    -- Effect on entities that walk on this tile
    onWalk = function(tile, entity)
        if entity.applyStatus then
            entity.applyStatus("quiet_movement", 5)
        end
        return {
            sound = "leaves_rustle",
            volume = 0.4
        }
    end
}

function ForestFloorTile.init(world)
    print("Forest floor tile module initialized")
    if world.systems and world.systems.ecosystem then
        world.systems.ecosystem:registerWoodlandTerrain("forest_floor")
    end
end

return ForestFloorTile
