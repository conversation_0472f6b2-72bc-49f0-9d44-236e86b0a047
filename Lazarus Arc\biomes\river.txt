-- biomes/river.lua
local RiverBiome = {
    id = "river",
    name = "River",
    description = "A flowing waterway with diverse aquatic life and riverbank vegetation.",
    
    -- Environmental factors
    environment = {
        humidity = 0.8,    -- High humidity near water
        temperature = 0.5, -- Moderate temperature
        sunlight = 0.7,    -- Good sunlight
        windStrength = 0.4 -- Moderate winds
    },
    
    -- Core tile types used in this biome
    primaryTiles = {"water", "riverbed"},
    secondaryTiles = {"grass", "sand"},
    rareTiles = {"crystal", "ancient_ruins"},
    
    -- Default proportions
    tileDistribution = {
        water = 0.5,
        riverbed = 0.2,
        grass = 0.15,
        sand = 0.1,
        crystal = 0.03,
        ancient_ruins = 0.02
    },
    
    -- Entities common to this biome
    commonEntities = {
        "fish", "frog", "water_plant", "grass"
    },
    
    uncommonEntities = {
        "beaver", "otter", "water_bird", "river_weed"
    },
    
    rareEntities = {
        "river_spirit", "water_dragon", "ancient_artifact", "magic_spring"
    },
    
    -- Environmental properties specific to river biomes
    waterFlow = {speed = 0.5, direction = "east"},
    hasFish = true,
    hasWaterfalls = true,
    hasRapids = true,
    
    -- Biome variants for diverse generation
    variants = {
        calm = {
            name = "Calm River",
            waterFlow = {speed = 0.2, direction = "east"},
            tileAdjustments = {
                water = 0.6,
                riverbed = 0.2,
                grass = 0.15
            },
            entityAdjustments = {
                fish = 1.5,
                water_plant = 1.2
            },
            specialEffects = {
                "gentle_waves",
                "rippling_water"
            }
        },
        rapid = {
            name = "Rapid River",
            waterFlow = {speed = 0.8, direction = "east"},
            tileAdjustments = {
                water = 0.7,
                riverbed = 0.2,
                rock = 0.05
            },
            entityAdjustments = {
                water_dragon = 1.5,
                river_spirit = 1.2
            },
            specialEffects = {
                "white_water",
                "splashing_water"
            }
        },
        ancient = {
            name = "Ancient River",
            waterFlow = {speed = 0.4, direction = "east"},
            tileAdjustments = {
                water = 0.5,
                riverbed = 0.2,
                ancient_ruins = 0.2
            },
            entityAdjustments = {
                ancient_artifact = 2.0,
                river_spirit = 1.5
            },
            specialEffects = {
                "mystical_mist",
                "ancient_glow"
            }
        }
    },
    
    -- Structures that can generate in this biome
    structures = {
        {
            name = "water_mill",
            chance = 0.1,
            entities = {"miller", "water_wheel"}
        },
        {
            name = "fishing_hut",
            chance = 0.08,
            entities = {"fisher", "fishing_rod"}
        },
        {
            name = "river_spirit_shrine",
            chance = 0.05,
            entities = {"river_spirit", "offering_bowl"}
        },
        {
            name = "ancient_bridge",
            chance = 0.03,
            requiresVariant = "ancient",
            entities = {"ancient_guardian", "magic_rune"}
        }
    },
    
    -- Environmental effects
    environmentalEffects = {
        water_flow = 0.8,
        fish_swarms = 0.6,
        water_splashes = 0.4,
        river_sounds = 0.3,
        mist = 0.2
    },
    
    -- Unique features
    features = {
        {
            name = "waterfall",
            chance = 0.1,
            unique = true
        },
        {
            name = "rapids",
            chance = 0.08,
            requiresVariant = "rapid",
            danger = true
        },
        {
            name = "ancient_ruins",
            chance = 0.05,
            requiresVariant = "ancient"
        },
        {
            name = "fishing_spot",
            chance = 0.15
        },
        {
            name = "river_bend",
            chance = 0.12
        }
    },
    
    -- Generation algorithm for this biome
    generate = function(chunk, world, variant)
        print("Generating " .. (variant and RiverBiome.variants[variant].name or "River") .. " biome")
        
        -- Get variant-specific adjustments
        local variantData = variant and RiverBiome.variants[variant] or nil
        local flowSpeed = variantData and variantData.waterFlow.speed or 1.0
        local depth = 2.0
        
        -- Adjust tile distribution based on variant
        local tileDistribution = {}
        for tileType, chance in pairs(RiverBiome.tileDistribution) do
            tileDistribution[tileType] = chance
            
            -- Apply variant adjustments if any
            if variantData and variantData.tileAdjustments and variantData.tileAdjustments[tileType] then
                tileDistribution[tileType] = variantData.tileAdjustments[tileType]
            end
        end
        
        -- Add variant-specific tiles if they don't exist in base distribution
        if variantData and variantData.tileAdjustments then
            for tileType, chance in pairs(variantData.tileAdjustments) do
                if not tileDistribution[tileType] then
                    tileDistribution[tileType] = chance
                end
            end
        end
        
        -- Generate base terrain using noise functions
        local seed = world.seed + (chunk.x * 137 + chunk.y * 547)
        math.randomseed(seed)
        
        -- Create perlin noise-like function (simplified for example)
        local function noise2D(x, y, frequency)
            return math.abs(math.sin(x * frequency) * math.cos(y * frequency))
        end
        
        -- Determine river flow direction
        -- We'll make the river flow generally from one side of the chunk to another
        local riverDirection = math.random(0, 3) -- 0: top to bottom, 1: right to left, 2: bottom to top, 3: left to right
        local startEdge, endEdge
        
        if riverDirection == 0 then
            -- Top to bottom
            startEdge = {min = 3, max = world.CHUNK_SIZE - 4, edge = 0}
            endEdge = {min = 3, max = world.CHUNK_SIZE - 4, edge = world.CHUNK_SIZE - 1}
        elseif riverDirection == 1 then
            -- Right to left
            startEdge = {min = 3, max = world.CHUNK_SIZE - 4, edge = world.CHUNK_SIZE - 1}
            endEdge = {min = 3, max = world.CHUNK_SIZE - 4, edge = 0}
        elseif riverDirection == 2 then
            -- Bottom to top
            startEdge = {min = 3, max = world.CHUNK_SIZE - 4, edge = world.CHUNK_SIZE - 1}
            endEdge = {min = 3, max = world.CHUNK_SIZE - 4, edge = 0}
        else
            -- Left to right
            startEdge = {min = 3, max = world.CHUNK_SIZE - 4, edge = 0}
            endEdge = {min = 3, max = world.CHUNK_SIZE - 4, edge = world.CHUNK_SIZE - 1}
        end
        
        -- Generate river start and end points on chunk edges
        local riverStart, riverEnd
        if riverDirection == 0 or riverDirection == 2 then
            -- Vertical flow (top-bottom or bottom-top)
            riverStart = {x = math.random(startEdge.min, startEdge.max), y = startEdge.edge}
            riverEnd = {x = math.random(endEdge.min, endEdge.max), y = endEdge.edge}
        else
            -- Horizontal flow (left-right or right-left)
            riverStart = {x = startEdge.edge, y = math.random(startEdge.min, startEdge.max)}
            riverEnd = {x = endEdge.edge, y = math.random(endEdge.min, endEdge.max)}
        end
        
        -- Generate river path with some meandering
        local riverPoints = {}
        table.insert(riverPoints, riverStart)
        
        -- Number of control points for the river path
        local numControlPoints = math.random(2, 4)
        for i = 1, numControlPoints do
            local t = i / (numControlPoints + 1)
            
            -- Linear interpolation between start and end
            local baseX = riverStart.x + (riverEnd.x - riverStart.x) * t
            local baseY = riverStart.y + (riverEnd.y - riverStart.y) * t
            
            -- Add some randomness for meandering
            local maxOffset = world.CHUNK_SIZE / 6
            local offsetX = (math.random() * 2 - 1) * maxOffset
            local offsetY = (math.random() * 2 - 1) * maxOffset
            
            -- Make sure the point stays within chunk bounds
            local pointX = math.max(1, math.min(world.CHUNK_SIZE - 2, baseX + offsetX))
            local pointY = math.max(1, math.min(world.CHUNK_SIZE - 2, baseY + offsetY))
            
            table.insert(riverPoints, {x = pointX, y = pointY})
        end
        
        table.insert(riverPoints, riverEnd)
        
        -- Function to calculate distance from a point to a line segment
        local function distanceToLineSegment(px, py, x1, y1, x2, y2)
            local A = px - x1
            local B = py - y1
            local C = x2 - x1
            local D = y2 - y1
            
            local dot = A * C + B * D
            local len_sq = C * C + D * D
            local param = -1
            
            if len_sq ~= 0 then
                param = dot / len_sq
            end
            
            local xx, yy
            
            if param < 0 then
                xx = x1
                yy = y1
            elseif param > 1 then
                xx = x2
                yy = y2
            else
                xx = x1 + param * C
                yy = y1 + param * D
            end
            
            local dx = px - xx
            local dy = py - yy
            
            return math.sqrt(dx * dx + dy * dy)
        end
        
        -- Function to calculate distance from a point to the river path
        local function distanceToRiver(x, y)
            local minDist = math.huge
            
            for i = 1, #riverPoints - 1 do
                local dist = distanceToLineSegment(x, y, 
                                                  riverPoints[i].x, riverPoints[i].y,
                                                  riverPoints[i+1].x, riverPoints[i+1].y)
                minDist = math.min(minDist, dist)
            end
            
            return minDist
        end
        
        -- Initialize 2D tiles array if not exists
        chunk.tiles = chunk.tiles or {}
        for x = 0, world.CHUNK_SIZE - 1 do
            chunk.tiles[x] = chunk.tiles[x] or {}
        end
        
        -- Generate tiles
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                -- Calculate world coordinates
                local worldX = chunk.x * world.CHUNK_SIZE + x
                local worldY = chunk.y * world.CHUNK_SIZE + y
                
                -- Generate different noise layers
                local baseNoise = noise2D(worldX, worldY, 0.05)
                local detailNoise = noise2D(worldX, worldY, 0.2)
                
                -- Calculate distance from this point to the river path
                local riverDist = distanceToRiver(x, y)
                
                -- River width is determined by variant and noise
                local riverWidth = math.max(3, 5 + baseNoise * 3)
                if variant == "ancient" then
                    riverWidth = riverWidth * 0.8 -- Narrower for ancient rivers
                end
                
                -- Determine tile type based on distance from river
                local tileType
                
                if riverDist < riverWidth * 0.5 then
                    -- Main river channel
                    if variant == "rapid" and math.random() < 0.4 then
                        tileType = "rapids"
                    elseif variant == "ancient" and math.random() < 0.4 then
                        tileType = "ancient_ruins"
                    else
                        tileType = "water"
                    end
                elseif riverDist < riverWidth * 0.8 then
                    -- Shallow water near edges
                    tileType = "grass"
                    
                    -- Sometimes add variant-specific replacements
                    if variant == "rapid" and math.random() < 0.3 then
                        tileType = "rapids"
                    elseif variant == "ancient" and math.random() < 0.3 then
                        tileType = "ancient_ruins"
                    end
                elseif riverDist < riverWidth * 1.1 then
                    -- Banks and beaches
                    if detailNoise > 0.6 then
                        tileType = "sand"
                    else
                        if variant == "ancient" then
                            tileType = "ancient_ruins"
                        else
                            tileType = "riverbed"
                        end
                    end
                elseif riverDist < riverWidth * 1.5 then
                    -- Transition to surrounding environment
                    if detailNoise > 0.7 then
                        tileType = "grass"
                    else
                        -- Use the probability distribution for these areas
                        local tileRoll = math.random()
                        local cumulativeChance = 0
                        
                        for tType, chance in pairs(tileDistribution) do
                            if tType ~= "water" and tType ~= "grass" then
                                cumulativeChance = cumulativeChance + chance
                                if tileRoll <= cumulativeChance then
                                    tileType = tType
                                    break
                                end
                            end
                        end
                        
                        -- Default if nothing was selected
                        if not tileType then
                            tileType = "riverbed"
                        end
                    end
                else
                    -- Further from river, use surrounding biome if available
                    if chunk.surroundingBiome then
                        -- This would be handled by the surrounding biome's generation
                        tileType = "grass" -- Placeholder
                    else
                        tileType = "grass"
                    end
                end
                
                -- Special features that override normal tiles
                -- Waterfalls for ancient rivers
                if variant == "ancient" and riverDist < riverWidth * 0.7 then
                    -- Check if there's a significant elevation change in this area
                    if detailNoise > 0.85 and math.random() < 0.2 then
                        tileType = "waterfall"
                    end
                end
                
                -- River caves can appear at banks
                if riverDist > riverWidth * 0.8 and riverDist < riverWidth * 1.2 and detailNoise > 0.9 and math.random() < 0.1 then
                    tileType = "ancient_ruins"
                end
                
                -- Create the tile
                local waterDepth = 0
                if tileType == "water" then
                    waterDepth = depth * (1 - riverDist / (riverWidth * 0.5))
                elseif tileType == "grass" then
                    waterDepth = depth * 0.3
                end
                
                -- Calculate flow direction based on river path
                local flowDirectionX, flowDirectionY = 0, 0
                if riverDirection == 0 then
                    flowDirectionY = 1 -- Down
                elseif riverDirection == 1 then
                    flowDirectionX = -1 -- Left
                elseif riverDirection == 2 then
                    flowDirectionY = -1 -- Up
                else
                    flowDirectionX = 1 -- Right
                end
                
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = math.random(1, 3),
                    passable = (tileType ~= "deep_water" and tileType ~= "rapids"),
                    isUnderground = false,
                    waterDepth = waterDepth,
                    flowSpeed = tileType == "rapids" and flowSpeed * 2 or 
                                (tileType == "water" and flowSpeed or 0),
                    flowDirectionX = flowDirectionX,
                    flowDirectionY = flowDirectionY,
                    riverDistance = riverDist
                }
                
                -- Add to chunk using 2D indexing
                chunk.tiles[x][y] = tile
            end
        end
        
        -- Place features
        RiverBiome.generateFeatures(chunk, world, variant, riverPoints, riverWidth)
        
        -- Add entities
        RiverBiome.populateEntities(chunk, world, variant)
        
        -- Place structures
        RiverBiome.generateStructures(chunk, world, variant, riverPoints, riverWidth)
    end,
    
    -- Helper function to generate features
    generateFeatures = function(chunk, world, variant, riverPoints, riverWidth)
        -- Iterate through each potential feature
        for _, feature in ipairs(RiverBiome.features) do
            -- Check if feature should spawn based on chance
            if math.random() < feature.chance then
                -- Check variant requirements if any
                if feature.requiresVariant and feature.requiresVariant ~= variant then
                    goto continue
                end
                
                -- For unique features, only place one per chunk
                if feature.unique and chunk.uniqueFeatures and chunk.uniqueFeatures[feature.name] then
                    goto continue
                end
                
                -- Find a suitable location for the feature along the river
                local featurePoint
                local attempts = 0
                local maxAttempts = 10
                
                repeat
                    -- Pick a random segment of the river
                    local segmentIndex = math.random(1, #riverPoints - 1)
                    local segStart = riverPoints[segmentIndex]
                    local segEnd = riverPoints[segmentIndex + 1]
                    
                    -- Random position along the segment
                    local t = math.random()
                    local featureX = segStart.x + (segEnd.x - segStart.x) * t
                    local featureY = segStart.y + (segEnd.y - segStart.y) * t
                    
                    -- Small random offset perpendicular to the river
                    local perpX = -(segEnd.y - segStart.y)
                    local perpY = segEnd.x - segStart.x
                    local perpLen = math.sqrt(perpX*perpX + perpY*perpY)
                    
                    if perpLen > 0 then
                        perpX = perpX / perpLen
                        perpY = perpY / perpLen
                        
                        -- Feature position with offset
                        local offset = (math.random() * 2 - 1) * riverWidth * 0.5
                        featureX = featureX + perpX * offset
                        featureY = featureY + perpY * offset
                        
                        -- Make sure it's in bounds
                        if featureX >= 2 and featureX < world.CHUNK_SIZE - 2 and
                           featureY >= 2 and featureY < world.CHUNK_SIZE - 2 then
                            featurePoint = {x = math.floor(featureX), y = math.floor(featureY)}
                            break
                        end
                    end
                    
                    attempts = attempts + 1
                until attempts >= maxAttempts
                
                -- If we couldn't find a suitable location, skip this feature
                if not featurePoint then
                    goto continue
                end
                
                local featureX = featurePoint.x
                local featureY = featurePoint.y
                
                -- Place feature based on type
                if feature.name == "waterfall" then
                    -- Create a waterfall feature
                    local waterFallHeight = math.random(5, 20)
                    local waterFallWidth = math.random(3, 5)
                    
                    -- Choose waterfall direction based on river flow
                    local fallDirection = 0 -- 0: down, 1: right, 2: up, 3: left
                    
                    -- Determine the base position for the top of the waterfall
                    local topX, topY = featureX, featureY
                    
                    -- Create the waterfall tiles
                    for i = 0, waterFallWidth - 1 do
                        for j = 0, waterFallHeight - 1 do
                            local tileX, tileY
                            
                            if fallDirection == 0 then
                                -- Falling downward
                                tileX = topX - math.floor(waterFallWidth / 2) + i
                                tileY = topY + j
                            elseif fallDirection == 1 then
                                -- Falling right
                                tileX = topX + j
                                tileY = topY - math.floor(waterFallWidth / 2) + i
                            elseif fallDirection == 2 then
                                -- Falling upward
                                tileX = topX - math.floor(waterFallWidth / 2) + i
                                tileY = topY - j
                            else
                                -- Falling left
                                tileX = topX - j
                                tileY = topY - math.floor(waterFallWidth / 2) + i
                            end
                            
                            if tileX >= 0 and tileX < world.CHUNK_SIZE and
                               tileY >= 0 and tileY < world.CHUNK_SIZE then
                                local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                
                                -- Set as waterfall tile
                                chunk.tiles[tileX][tileY].type = "waterfall"
                                chunk.tiles[tileX][tileY].fallHeight = waterFallHeight
                                chunk.tiles[tileX][tileY].fallPosition = j -- Position in the fall
                                chunk.tiles[tileX][tileY].flowSpeed = 3.0 -- Fast flow in waterfalls
                                
                                -- Set flow direction
                                if fallDirection == 0 then
                                    chunk.tiles[tileX][tileY].flowDirectionY = 1
                                elseif fallDirection == 1 then
                                    chunk.tiles[tileX][tileY].flowDirectionX = 1
                                elseif fallDirection == 2 then
                                    chunk.tiles[tileX][tileY].flowDirectionY = -1
                                else
                                    chunk.tiles[tileX][tileY].flowDirectionX = -1
                                end
                                
                                -- Bottom of waterfall creates splash and mist
                                if j == waterFallHeight - 1 then
                                    chunk.tiles[tileX][tileY].splashing = true
                                    
                                    -- Add splash entity
                                    local splashX = chunk.x * world.CHUNK_SIZE + tileX
                                    local splashY = chunk.y * world.CHUNK_SIZE + tileY
                                    
                                    world.entitySystem:addEntity("water_splash", splashX, splashY, {
                                        size = math.random(15, 25) / 10,
                                        intensity = math.random(7, 10) / 10
                                    })
                                end
                            end
                        end
                    end
                    
                    -- Add waterfall sound and mist
                    local worldX = chunk.x * world.CHUNK_SIZE + featureX
                    local worldY = chunk.y * world.CHUNK_SIZE + featureY
                    
                    world.entitySystem:addEntity("waterfall_mist", worldX, worldY, {
                        width = waterFallWidth,
                        height = waterFallHeight,
                        intensity = math.random(5, 10) / 10
                    })
                    
                elseif feature.name == "ancient_ruins" then
                    -- Create ancient ruins in the river
                    local ruinsSize = math.random(3, 6)
                    local radius = ruinsSize / 2
                    
                    for dx = -radius, radius do
                        for dy = -radius, radius do
                            local dist = math.sqrt(dx*dx + dy*dy)
                            if dist <= radius then
                                local tileX = featureX + dx
                                local tileY = featureY + dy
                                
                                if tileX >= 0 and tileX < world.CHUNK_SIZE and
                                   tileY >= 0 and tileY < world.CHUNK_SIZE then
                                    local tile = chunk.tiles[tileX][tileY]
                                    
                                    -- Drop elevation and add water/rapids
                                    tile.elevation = tile.elevation - waterFallHeight * (1 - dist / radius)
                                    if dist < radius * 0.6 then
                                        tile.type = "waterfall_base"
                                    else
                                        tile.type = "rapids"
                                    end
                                    tile.waterDepth = 2 + waterFallHeight / 5
                                    
                                    -- Add splash entity
                                    local splashX = chunk.x * world.CHUNK_SIZE + tileX
                                    local splashY = chunk.y * world.CHUNK_SIZE + tileY
                                    
                                    world.entitySystem:addEntity("water_splash", splashX, splashY, {
                                        size = math.random(15, 25) / 10,
                                        intensity = math.random(7, 10) / 10
                                    })
                                end
                            end
                        end
                    end
                    
                    -- Add some vegetation to the ruins
                    local plantCount = math.random(3, 8)
                    for i = 1, plantCount do
                        local angle = math.random() * math.pi * 2
                        local distance = math.random() * radius * 0.7
                        
                        local plantX = chunk.x * world.CHUNK_SIZE + featureX + math.cos(angle) * distance
                        local plantY = chunk.y * world.CHUNK_SIZE + featureY + math.sin(angle) * distance
                        
                        local plantTypes = {"river_bush", "small_tree", "tall_grass", "flowers"}
                        local plantType = plantTypes[math.random(#plantTypes)]
                        
                        world.entitySystem:addEntity(plantType, plantX, plantY, {
                            size = math.random(8, 15) / 10
                        })
                    end
                    
                elseif feature.name == "fishing_spot" then
                    -- Create a fishing spot
                    local closestRiverSegment
                    local minDist = math.huge
                    
                    -- Find the closest river segment
                    for i = 1, #riverPoints - 1 do
                        local segStart = riverPoints[i]
                        local segEnd = riverPoints[i + 1]
                        local midX = (segStart.x + segEnd.x) / 2
                        local midY = (segStart.y + segEnd.y) / 2
                        
                        local dist = math.sqrt((midX - featureX)^2 + (midY - featureY)^2)
                        if dist < minDist then
                            minDist = dist
                            closestRiverSegment = i
                        end
                    end
                    
                    if closestRiverSegment then
                        local segStart = riverPoints[closestRiverSegment]
                        local segEnd = riverPoints[closestRiverSegment + 1]
                        
                        -- Direction toward river
                        local dirX = ((segStart.x + segEnd.x) / 2) - featureX
                        local dirY = ((segStart.y + segEnd.y) / 2) - featureY
                        local dirLen = math.sqrt(dirX*dirX + dirY*dirY)
                        
                        if dirLen > 0 then
                            dirX = dirX / dirLen
                            dirY = dirY / dirLen
                            
                            -- Create fishing spot marker at end of river
                            local spotX = featureX + math.floor(dirX * dirLen)
                            local spotY = featureY + math.floor(dirY * dirLen)
                            
                            world.entitySystem:addEntity("fishing_spot", spotX, spotY, {
                                quality = math.random(1, 5),
                                fishTypes = {"river_bass", "catfish", "trout"}
                            })
                        end
                    end
                end
                
                -- Mark unique features as placed
                if feature.unique then
                    if not chunk.uniqueFeatures then
                        chunk.uniqueFeatures = {}
                    end
                    chunk.uniqueFeatures[feature.name] = true
                end
            end
            
            ::continue::
        end
    end,
    
    -- Helper function to add entities
    populateEntities = function(chunk, world, variant)
        -- Get variant-specific entity adjustments
        local variantData = variant and RiverBiome.variants[variant] or nil
        
        -- Rivers have moderate life density
        local commonCount = math.random(10, 20)
        local uncommonCount = math.random(3, 8)
        local rareCount = math.random(0, 2)
        
        -- Helper function to spawn an entity with variant adjustments
        local function spawnEntity(entityType, count)
            -- Apply variant adjustment if available
            local multiplier = 1.0
            if variantData and variantData.entityAdjustments and variantData.entityAdjustments[entityType] then
                multiplier = variantData.entityAdjustments[entityType]
            end
            
            -- Adjust count based on variant
            local adjustedCount = math.floor(count * multiplier + 0.5)
            
            -- Spawn entities
            for i = 1, adjustedCount do
                -- Pick a random position in the chunk
                local x = math.random(0, world.CHUNK_SIZE - 1)
                local y = math.random(0, world.CHUNK_SIZE - 1)
                local tile = chunk.tiles[x][y]
                
                -- Check if suitable for this entity type
                local suitable = false
                
                if entityType == "fish" or entityType == "water_dragon" then
                    -- Aquatic creatures need deeper water
                    suitable = (tile.type == "water" and tile.waterDepth > 1.0)
                elseif entityType == "frog" or entityType == "water_plant" then
                    -- Shallow water creatures
                    suitable = (tile.type == "grass" or tile.type == "water" and tile.waterDepth < 1.0)
                elseif entityType == "beaver" or entityType == "otter" then
                    -- Bank-dwelling creatures
                    suitable = (tile.type == "riverbed" or tile.type == "sand")
                else
                    -- Default land creatures
                    suitable = (tile.type ~= "water" and tile.type ~= "grass" and 
                              tile.type ~= "rapids" and tile.type ~= "waterfall")
                end
                
                if suitable then
                    local worldX = chunk.x * world.CHUNK_SIZE + x
                    local worldY = chunk.y * world.CHUNK_SIZE + y
                    
                    -- Determine if entity should be in/on water or on land
                    local inWater = (tile.type == "water" or tile.type == "grass")
                    
                    -- Spawn the entity
                    world.entitySystem:addEntity(entityType, worldX, worldY, {
                        inWater = inWater,
                        swimming = inWater
                    })
                end
            end
        end
        
        -- Spawn common entities
        for _, entityType in ipairs(RiverBiome.commonEntities) do
            local count = math.random(2, 4)
            spawnEntity(entityType, count)
        end
        
        -- Spawn uncommon entities
        for _, entityType in ipairs(RiverBiome.uncommonEntities) do
            local count = math.random(1, 2)
            spawnEntity(entityType, count)
        end
        
        -- Spawn rare entities
        for _, entityType in ipairs(RiverBiome.rareEntities) do
            if math.random() < 0.25 then -- 25% chance for each rare entity
                spawnEntity(entityType, 1)
            end
        end
        
        -- Add variant-specific entities
        if variant == "ancient" then
            spawnEntity("ancient_artifact", math.random(1, 3))
            spawnEntity("magic_spring", math.random(1, 2))
        end
        
        -- Add environmental detail entities
        local detailCount = math.random(5, 15)
        for i = 1, detailCount do
            local x = math.random(0, world.CHUNK_SIZE - 1)
            local y = math.random(0, world.CHUNK_SIZE - 1)
            local tile = chunk.tiles[x][y]
            
            local worldX = chunk.x * world.CHUNK_SIZE + x
            local worldY = chunk.y * world.CHUNK_SIZE + y
            
            if tile.type == "water" then
                -- Water details
                if math.random() < 0.3 then
                    world.entitySystem:addEntity("water_ripple", worldX, worldY, {
                        size = math.random(5, 15) / 10
                    })
                end
            elseif tile.type == "riverbed" or tile.type == "sand" then
                -- Vegetation on banks
                if math.random() < 0.5 then
                    local bankDetails = {"small_rock", "reed", "river_flower", "pebbles"}
                    local detailType = bankDetails[math.random(#bankDetails)]
                    
                    world.entitySystem:addEntity(detailType, worldX, worldY, {
                        size = math.random(5, 15) / 10
                    })
                end
            end
        end
    end,
    
    -- Helper function to place structures
    generateStructures = function(chunk, world, variant, riverPoints, riverWidth)
        -- Go through each structure type
        for _, structure in ipairs(RiverBiome.structures) do
            -- Check chance and variant requirements
            if math.random() < structure.chance and
               (not structure.requiresVariant or structure.requiresVariant == variant) then
                
                -- Find a suitable location for the structure
                local structurePoint
                local attempts = 0
                local maxAttempts = 10
                
                repeat
                    -- For river structures, we want to be near but not in the river
                    local segmentIndex = math.random(1, #riverPoints - 1)
                    local segStart = riverPoints[segmentIndex]
                    local segEnd = riverPoints[segmentIndex + 1]
                    
                    -- Random position along the segment
                    local t = math.random()
                    local baseX = segStart.x + (segEnd.x - segStart.x) * t
                    local baseY = segStart.y + (segEnd.y - segStart.y) * t
                    
                    -- Get perpendicular direction to river
                    local perpX = -(segEnd.y - segStart.y)
                    local perpY = segEnd.x - segStart.x
                    local perpLen = math.sqrt(perpX*perpX + perpY*perpY)
                    
                    if perpLen > 0 then
                        perpX = perpX / perpLen
                        perpY = perpY / perpLen
                        
                        -- Place structure at bank, not in water
                        local offset = riverWidth * (0.8 + math.random() * 0.4)
                        local structX = math.floor(baseX + perpX * offset * (math.random() < 0.5 and 1 or -1))
                        local structY = math.floor(baseY + perpY * offset * (math.random() < 0.5 and 1 or -1))
                        
                        -- Check bounds
                        if structX >= 3 and structX < world.CHUNK_SIZE - 3 and
                           structY >= 3 and structY < world.CHUNK_SIZE - 3 then
                            
                            local tile = chunk.tiles[structX][structY]
                            
                            -- Make sure it's not in water
                            if tile.type ~= "water" and tile.type ~= "grass" and
                               tile.type ~= "rapids" and tile.type ~= "waterfall" then
                                structurePoint = {x = structX, y = structY}
                                break
                            end
                        end
                    end
                    
                    attempts = attempts + 1
                until attempts >= maxAttempts
                
                -- If we found a suitable location, place the structure
                if structurePoint then
                    local structX = structurePoint.x
                    local structY = structurePoint.y
                    local worldX = chunk.x * world.CHUNK_SIZE + structX
                    local worldY = chunk.y * world.CHUNK_SIZE + structY
                    
                    -- Place structure
                    world.chunkSystem:placeStructure(chunk, structure.name, worldX, worldY)
                    
                    -- Add associated entities
                    if structure.entities then
                        for _, entityType in ipairs(structure.entities) do
                            -- Add entity near structure
                            local offsetX = math.random(-2, 2)
                            local offsetY = math.random(-2, 2)
                            world.entitySystem:addEntity(entityType, worldX + offsetX, worldY + offsetY)
                        end
                    end
                    
                    -- Special setup for different structure types
                    if structure.name == "water_mill" then
                        -- Position the watermill with the wheel in the water
                        local closestRiverPoint
                        local minDist = math.huge
                        
                        -- Find the closest point on the river
                        for i = 1, #riverPoints - 1 do
                            local segStart = riverPoints[i]
                            local segEnd = riverPoints[i + 1]
                            
                            -- Check multiple points along this segment
                            for t = 0, 1, 0.1 do
                                local pointX = segStart.x + (segEnd.x - segStart.x) * t
                                local pointY = segStart.y + (segEnd.y - segStart.y) * t
                                
                                local dist = math.sqrt((pointX - structX)^2 + (pointY - structY)^2)
                                if dist < minDist then
                                    minDist = dist
                                    closestRiverPoint = {x = pointX, y = pointY}
                                end
                            end
                        end
                        
                        if closestRiverPoint then
                            -- Direction toward river
                            local dirX = closestRiverPoint.x - structX
                            local dirY = closestRiverPoint.y - structY
                            local dirLen = math.sqrt(dirX*dirX + dirY*dirY)
                            
                            if dirLen > 0 then
                                dirX = dirX / dirLen
                                dirY = dirY / dirLen
                                
                                -- Create mill building
                                for dx = -2, 2 do
                                    for dy = -2, 2 do
                                        local millX = structX + dx
                                        local millY = structY + dy
                                        
                                        if millX >= 0 and millX < world.CHUNK_SIZE and
                                           millY >= 0 and millY < world.CHUNK_SIZE then
                                            local tile = chunk.tiles[millX][millY]
                                            
                                            -- Mark as mill building
                                            tile.type = "mill_floor"
                                            
                                            -- Add mill building entity
                                            local entityX = chunk.x * world.CHUNK_SIZE + millX
                                            local entityY = chunk.y * world.CHUNK_SIZE + millY
                                            
                                            if math.abs(dx) == 2 or math.abs(dy) == 2 then
                                                world.entitySystem:addEntity("mill_wall", entityX, entityY)
                                            end
                                        end
                                    end
                                end
                                
                                -- Place water wheel at the river
                                local wheelX = chunk.x * world.CHUNK_SIZE + math.floor(closestRiverPoint.x)
                                local wheelY = chunk.y * world.CHUNK_SIZE + math.floor(closestRiverPoint.y)
                                
                                world.entitySystem:addEntity("mill_wheel", wheelX, wheelY, {
                                    rotating = true,
                                    connected = true
                                })
                                
                                -- Create small path connecting mill to wheel
                                local pathLength = math.floor(dirLen)
                                for i = 1, pathLength - 1 do
                                    local pathX = structX + math.floor(dirX * i)
                                    local pathY = structY + math.floor(dirY * i)
                                    
                                    if pathX >= 0 and pathX < world.CHUNK_SIZE and
                                       pathY >= 0 and pathY < world.CHUNK_SIZE then
                                        local tile = chunk.tiles[pathX][pathY]
                                        
                                        -- Create stone path
                                        tile.type = "stone_path"
                                    end
                                end
                            end
                        end
                    end
                end
            end
        end
    end,
    
    -- Apply post-processing effects
    postProcess = function(chunk, world, variant)
        local variantData = variant and RiverBiome.variants[variant] or nil
        
        -- Apply variant-specific special effects
        if variantData and variantData.specialEffects then
            chunk.environmentalEffects = chunk.environmentalEffects or {}
            
            for _, effect in ipairs(variantData.specialEffects) do
                table.insert(chunk.environmentalEffects, effect)
            end
        end
        
        -- Process water tiles
        for _, tile in pairs(chunk.tiles) do
            -- Set water movement properties
            if tile.type == "river_water" or tile.type == "shallow_water" or tile.type == "rapids" then
                tile.swimmable = true
                tile.boatable = true
                
                -- Apply variant-specific water adjustments
                if variant == "rapid" then
                    tile.flowSpeed = tile.flowSpeed * 1.5
                    if tile.type == "rapids" then
                        tile.dangerous = math.random() < 0.3 -- Some rapids are hazardous
                    end
                elseif variant == "mountain" then
                    tile.temperature = 10 + math.random() * 5 -- Cold mountain water
                elseif variant == "swamp" then
                    tile.flowSpeed = tile.flowSpeed * 0.5 -- Slower flow
                    tile.murky = true
                    tile.oxygenLevel = 0.7 -- Lower oxygen in swamp water
                elseif variant == "crystal" then
                    tile.glowIntensity = math.random(1, 5) / 10 -- Subtle glow
                    tile.manaConcentration = math.random(1, 3) / 10 -- Small mana boost
                end
            end
            
            -- Apply waterfall effects
            if tile.type == "waterfall" then
                tile.noisy = true
                tile.flowSpeed = 3.0
                tile.dangerous = variantData and variantData.flowSpeed > 1.5
            end
            
            -- Add variant-specific tile effects
            if variant == "crystal" and (tile.type == "crystal_water" or tile.type == "glowing_water") then
                tile.glowIntensity = math.random(3, 8) / 10
                tile.manaRegen = math.random(1, 3) / 10
            elseif variant == "swamp" and tile.type == "stagnant_water" then
                tile.murkyLevel = math.random(7, 10) / 10
                tile.mosquitoChance = math.random(3, 7) / 10
            end
        end
        
        -- Add weather effects and ambient sounds
        if variant == "mountain" then
            chunk.ambientSounds = {"rushing_water", "distant_waterfall"}
        elseif variant == "rapid" then
            chunk.ambientSounds = {"river_rapids", "splashing"}
        elseif variant == "swamp" then
            chunk.ambientSounds = {"frogs", "insects", "water_bubbling"}
            chunk.fogChance = 0.4
        elseif variant == "crystal" then
            chunk.ambientSounds = {"water_chimes", "gentle_flow", "crystal_resonance"}
            chunk.lightEffects = {"water_reflections", "crystal_sparkles"}
        else
            chunk.ambientSounds = {"flowing_water", "bird_calls"}
        end
    end,
    
    -- Initialize the biome module
    init = function(worldCore)
        print("River biome module initialized")
        
        -- Store reference to WorldCore for later use
        RiverBiome.worldCore = worldCore
        
        -- Don't try to register generators at init time
        -- Instead, we'll register them when a world is created
        
        -- Store the biome in the biomes registry
        -- This allows the module to be used later when worlds are created
        print("River biome registered successfully")
    end,
    
    -- Add a new function to register generators with a specific world instance
    registerWithWorld = function(world)
        print("Registering river biome generators with world")
        
        if not world or not world.chunkSystem then
            print("Warning: Cannot register river biome - world or chunkSystem is nil")
            return false
        end
        
        -- Register generation algorithms with the world
        world.chunkSystem:registerGenerator("river", RiverBiome.generate)
        
        -- Register biome variants
        for variantId, variant in pairs(RiverBiome.variants) do
            world.chunkSystem:registerGenerator("river_" .. variantId, function(chunk, world)
                RiverBiome.generate(chunk, world, variantId)
                RiverBiome.postProcess(chunk, world, variantId)
            end)
        end
        
        return true
    end
}

return RiverBiome