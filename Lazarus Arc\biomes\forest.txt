-- biomes/forest.lua
local ForestBiome = {
    id = "forest",
    name = "Forest",
    
    -- Core tile types used in this biome
    primaryTiles = {"forest_floor", "grass"},
    secondaryTiles = {"water", "mountain", "ruins"},
    rareTiles = {"crystal_formation", "ancient_technology"},
    
    -- Default proportions (adjustable through variants)
    tileDistribution = {
        forest_floor = 0.7,
        grass = 0.2,
        water = 0.05,
        mountain = 0.03,
        ruins = 0.01,
        crystal_formation = 0.005,
        ancient_technology = 0.005
    },
    
    -- Entities common to this biome
    commonEntities = {
        "deer", "rabbit", "fox", "squirrel", "owl", "wolf"
    },
    
    uncommonEntities = {
        "bear", "forest_spirit", "bandit", "forest_golem"
    },
    
    rareEntities = {
        "ancient_treant", "forest_guardian", "mystic_deer"
    },
    
    -- Biome variants for diverse generation
    variants = {
        dense = {
            name = "Dense Forest",
            treeMultiplier = 2.0,
            lightLevel = 0.7, -- Darker due to dense canopy
            tileAdjustments = {
                forest_floor = 0.8,
                grass = 0.1
            },
            entityAdjustments = {
                -- More predators in dense forest
                wolf = 1.5,
                bear = 1.3
            }
        },
        old_growth = {
            name = "Ancient Forest",
            treeMultiplier = 1.5,
            treeSize = 1.5, -- Bigger trees
            tileAdjustments = {
                forest_floor = 0.75,
                ruins = 0.05 -- More ruins in ancient forest
            },
            entityAdjustments = {
                ancient_treant = 2.0,
                forest_spirit = 1.5
            }
        },
        sparse = {
            name = "Sparse Woodland",
            treeMultiplier = 0.6,
            lightLevel = 1.2, -- Brighter
            tileAdjustments = {
                forest_floor = 0.4,
                grass = 0.5
            },
            entityAdjustments = {
                deer = 1.5,
                rabbit = 1.3
            }
        },
        enchanted = {
            name = "Enchanted Grove",
            treeMultiplier = 1.2,
            magicalLevel = 2.0, -- Higher magic presence
            tileAdjustments = {
                crystal_formation = 0.05,
                forest_floor = 0.65
            },
            entityAdjustments = {
                forest_spirit = 2.0,
                mystic_deer = 1.8
            },
            specialEffects = {
                "glowing_particles",
                "whispering_sounds",
                "magical_weather"
            }
        },
        corrupted = {
            name = "Corrupted Forest",
            treeMultiplier = 0.8,
            corruptionLevel = 0.6, -- How corrupted the biome is
            tileAdjustments = {
                forest_floor = 0.6,
                void = 0.1
            },
            entityAdjustments = {
                wolf = 0.5, -- Natural animals reduced
                deer = 0.5,
                forest_guardian = 0.0, -- No guardians
                corrupted_wolf = 1.5, -- Added corrupted variants
                void_touched_deer = 1.0
            },
            specialEffects = {
                "corruption_spread",
                "eerie_sounds",
                "dark_mist"
            }
        }
    },
    
    -- Structures that can generate in this biome
    structures = {
        {
            name = "forest_cabin",
            chance = 0.15, -- Chance per chunk
            clearArea = true, -- Clear the area around the structure
            entities = {"hunter", "woodsman"}
        },
        {
            name = "druid_circle",
            chance = 0.1,
            requiresVariant = "enchanted", -- Only in enchanted forest
            entities = {"druid", "forest_guardian"}
        },
        {
            name = "bandit_camp",
            chance = 0.08,
            entities = {"bandit", "bandit_leader"}
        },
        {
            name = "ancient_shrine",
            chance = 0.05,
            requiresVariant = "old_growth",
            entities = {"forest_priest", "guardian_statue"}
        }
    },
    
    -- Weather patterns common in this biome
    weather = {
        rain = 0.3, -- 30% chance of rain
        fog = 0.2,
        thunder = 0.05,
        snow = 0.01 -- Rare in forest
    },
    
    -- Unique features generation
    features = {
        {
            name = "ancient_tree",
            chance = 0.2,
            unique = true -- Only one per chunk
        },
        {
            name = "mushroom_circle",
            chance = 0.15
        },
        {
            name = "forest_lake",
            chance = 0.1,
            size = {min = 10, max = 30}
        }
    },
    
    -- Generation algorithm for this biome
    generate = function(chunk, world, variant)
        print("Generating " .. (variant and ForestBiome.variants[variant].name or "Forest") .. " biome")
        
        -- Get variant-specific adjustments
        local variantData = variant and ForestBiome.variants[variant] or nil
        local treeMultiplier = variantData and variantData.treeMultiplier or 1.0
        
        -- Adjust tile distribution based on variant
        local tileDistribution = {}
        for tileType, chance in pairs(ForestBiome.tileDistribution) do
            tileDistribution[tileType] = chance
            
            -- Apply variant adjustments if any
            if variantData and variantData.tileAdjustments and variantData.tileAdjustments[tileType] then
                tileDistribution[tileType] = variantData.tileAdjustments[tileType]
            end
        end
        
        -- Generate base terrain using noise functions
        local seed = world.seed + (chunk.x * 263 + chunk.y * 637)
        math.randomseed(seed)
        
        -- Create perlin noise-like function (simplified for example)
        local function noise2D(x, y, frequency)
            return math.abs(math.sin(x * frequency) * math.cos(y * frequency))
        end
        
        -- Initialize 2D tiles array if not exists
        chunk.tiles = chunk.tiles or {}
        for x = 0, world.CHUNK_SIZE - 1 do
            chunk.tiles[x] = chunk.tiles[x] or {}
        end
        
        -- Generate tiles
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                -- Calculate world coordinates
                local worldX = chunk.x * world.CHUNK_SIZE + x
                local worldY = chunk.y * world.CHUNK_SIZE + y
                
                -- Generate different noise layers
                local baseNoise = noise2D(worldX, worldY, 0.1)
                local detailNoise = noise2D(worldX, worldY, 0.3)
                local featureNoise = noise2D(worldX, worldY, 0.05)
                
                -- Determine tile type based on noise and distribution
                local tileType = "forest_floor" -- Default
                local tileRoll = math.random()
                local cumulativeChance = 0
                
                for tType, chance in pairs(tileDistribution) do
                    cumulativeChance = cumulativeChance + chance
                    if tileRoll <= cumulativeChance then
                        tileType = tType
                        break
                    end
                end
                
                -- Special case: water forms in low areas
                if baseNoise < 0.2 and detailNoise < 0.3 then
                    tileType = "water"
                end
                
                -- Special case: mountains in high areas
                if baseNoise > 0.8 and detailNoise > 0.7 then
                    tileType = "mountain"
                end
                
                -- Variant-specific modifications
                if variant == "enchanted" and math.random() < 0.1 * baseNoise then
                    -- More crystal formations in enchanted forests
                    if tileType == "forest_floor" and math.random() < 0.2 then
                        tileType = "crystal_formation"
                    end
                elseif variant == "corrupted" and baseNoise > 0.6 then
                    -- Void spots in corrupted forest
                    if math.random() < baseNoise * variantData.corruptionLevel then
                        tileType = "void"
                    end
                end
                
                -- Create the tile
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = math.random(1, 3),
                    passable = true,
                    isUnderground = false
                }
                
                -- Add to chunk using 2D indexing
                chunk.tiles[x][y] = tile
            end
        end
        
        -- Place features
        ForestBiome.generateFeatures(chunk, world, variant)
        
        -- Add entities
        ForestBiome.populateEntities(chunk, world, variant)
        
        -- Place structures
        ForestBiome.generateStructures(chunk, world, variant)
    end,
    
    -- Helper function to generate features
    generateFeatures = function(chunk, world, variant)
        -- Iterate through each potential feature
        for _, feature in ipairs(ForestBiome.features) do
            -- Check if feature should spawn based on chance
            if math.random() < feature.chance then
                -- Check variant requirements if any
                if feature.requiresVariant and feature.requiresVariant ~= variant then
                    goto continue
                end
                
                -- For unique features, only place one per chunk
                if feature.unique and chunk.uniqueFeatures and chunk.uniqueFeatures[feature.name] then
                    goto continue
                end
                
                -- Determine position for feature
                local featureX = math.random(2, world.CHUNK_SIZE - 3)
                local featureY = math.random(2, world.CHUNK_SIZE - 3)
                
                -- Place feature based on type
                if feature.name == "ancient_tree" then
                    -- Create an ancient tree
                    local worldX = chunk.x * world.CHUNK_SIZE + featureX
                    local worldY = chunk.y * world.CHUNK_SIZE + featureY
                    
                    -- Change tiles around position to create tree area
                    for dx = -1, 1 do
                        for dy = -1, 1 do
                            local tileX = featureX + dx
                            local tileY = featureY + dy
                            
                            -- Only modify if within chunk bounds
                            if tileX >= 0 and tileX < world.CHUNK_SIZE and
                               tileY >= 0 and tileY < world.CHUNK_SIZE then
                                local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                local tile = chunk.tiles[tileX][tileY]
                                
                                -- Make ground around tree special
                                tile.type = "forest_floor"
                                tile.variant = 2 -- Special ancient tree floor variant
                                
                                -- Center tile is the tree
                                if dx == 0 and dy == 0 then
                                    world.entitySystem:addEntity("ancient_tree", worldX, worldY, {
                                        size = variant == "old_growth" and 2.0 or 1.5,
                                        age = math.random(500, 1000),
                                        magical = variant == "enchanted"
                                    })
                                end
                            end
                        end
                    end
                    
                elseif feature.name == "mushroom_circle" then
                    -- Create a circle of mushrooms
                    local radius = math.random(3, 5)
                    local worldX = chunk.x * world.CHUNK_SIZE + featureX
                    local worldY = chunk.y * world.CHUNK_SIZE + featureY
                    
                    -- Place mushrooms in a circle
                    for angle = 0, 355, 45 do
                        local radians = angle * math.pi / 180
                        local mushX = math.floor(worldX + math.cos(radians) * radius)
                        local mushY = math.floor(worldY + math.sin(radians) * radius)
                        
                        world.entitySystem:addEntity("mushroom", mushX, mushY, {
                            variant = variant == "enchanted" and "glowing" or "normal",
                            size = math.random() * 0.5 + 0.75
                        })
                    end
                    
                elseif feature.name == "forest_lake" then
                    -- Create a small lake
                    local size = math.random(feature.size.min, feature.size.max)
                    local radius = size / 2
                    
                    -- Convert tiles to water in a rough circle
                    for dx = -radius, radius do
                        for dy = -radius, radius do
                            -- Calculate distance from center
                            local dist = math.sqrt(dx*dx + dy*dy)
                            if dist <= radius then
                                local tileX = featureX + dx
                                local tileY = featureY + dy
                                
                                -- Check if tile is within chunk
                                if tileX >= 0 and tileX < world.CHUNK_SIZE and
                                   tileY >= 0 and tileY < world.CHUNK_SIZE then
                                    local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                    chunk.tiles[tileX][tileY].type = "water"
                                    chunk.tiles[tileX][tileY].variant = math.random(1, 3)
                                    
                                    -- Sometimes add water lilies
                                    if math.random() < 0.1 then
                                        local worldX = chunk.x * world.CHUNK_SIZE + tileX
                                        local worldY = chunk.y * world.CHUNK_SIZE + tileY
                                        world.entitySystem:addEntity("water_lily", worldX, worldY)
                                    end
                                end
                            end
                        end
                    end
                end
                
                -- Mark unique features as placed
                if feature.unique then
                    if not chunk.uniqueFeatures then
                        chunk.uniqueFeatures = {}
                    end
                    chunk.uniqueFeatures[feature.name] = true
                end
            end
            
            ::continue::
        end
    end,
    
    -- Helper function to add entities
    populateEntities = function(chunk, world, variant)
        -- Get variant-specific entity adjustments
        local variantData = variant and ForestBiome.variants[variant] or nil
        
        -- Determine entity counts
        local commonCount = math.random(8, 15)
        local uncommonCount = math.random(3, 6)
        local rareCount = math.random(0, 2)
        
        -- Helper function to spawn an entity with variant adjustments
        local function spawnEntity(entityType, count)
            -- Apply variant adjustment if available
            local multiplier = 1.0
            if variantData and variantData.entityAdjustments and variantData.entityAdjustments[entityType] then
                multiplier = variantData.entityAdjustments[entityType]
            end
            
            -- Adjust count based on variant
            local adjustedCount = math.floor(count * multiplier + 0.5)
            
            -- Spawn entities
            for i = 1, adjustedCount do
                -- Pick a random position in the chunk
                local x = math.random(0, world.CHUNK_SIZE - 1)
                local y = math.random(0, world.CHUNK_SIZE - 1)
                local tileIndex = x + y * world.CHUNK_SIZE
                local tile = chunk.tiles[x][y]
                
                -- Check if tile is passable (not water or mountain)
                if tile.type ~= "water" and tile.type ~= "mountain" and tile.type ~= "void" then
                    local worldX = chunk.x * world.CHUNK_SIZE + x
                    local worldY = chunk.y * world.CHUNK_SIZE + y
                    
                    -- Spawn the entity
                    world.entitySystem:addEntity(entityType, worldX, worldY)
                end
            end
        end
        
        -- Spawn common entities
        for _, entityType in ipairs(ForestBiome.commonEntities) do
            local count = math.random(1, 3)
            spawnEntity(entityType, count)
        end
        
        -- Spawn uncommon entities
        for _, entityType in ipairs(ForestBiome.uncommonEntities) do
            local count = math.random(0, 1)
            spawnEntity(entityType, count)
        end
        
        -- Spawn rare entities
        for _, entityType in ipairs(ForestBiome.rareEntities) do
            if math.random() < 0.2 then -- 20% chance for each rare entity
                spawnEntity(entityType, 1)
            end
        end
        
        -- Add variant-specific entities
        if variant == "corrupted" then
            spawnEntity("corrupted_wolf", math.random(2, 4))
            spawnEntity("void_touched_deer", math.random(1, 3))
        elseif variant == "enchanted" then
            spawnEntity("forest_spirit", math.random(1, 3))
            spawnEntity("mystic_deer", math.random(1, 2))
        end
    end,
    
    -- Helper function to place structures
    generateStructures = function(chunk, world, variant)
        -- Go through each structure type
        for _, structure in ipairs(ForestBiome.structures) do
            -- Check chance and variant requirements
            if math.random() < structure.chance and
               (not structure.requiresVariant or structure.requiresVariant == variant) then
                
                -- Find suitable location for structure
                local structX = math.random(3, world.CHUNK_SIZE - 4)
                local structY = math.random(3, world.CHUNK_SIZE - 4)
                local worldX = chunk.x * world.CHUNK_SIZE + structX
                local worldY = chunk.y * world.CHUNK_SIZE + structY
                
                -- Place structure
                world.chunkSystem:placeStructure(chunk, structure.name, worldX, worldY)
                
                -- Add associated entities
                if structure.entities then
                    for _, entityType in ipairs(structure.entities) do
                        -- Add entity near structure
                        local offsetX = math.random(-2, 2)
                        local offsetY = math.random(-2, 2)
                        world.entitySystem:addEntity(entityType, worldX + offsetX, worldY + offsetY)
                    end
                end
            end
        end
    end,
    
    -- Post-processing for variant-specific effects
    postProcess = function(chunk, world, variant)
        if variant == "enchanted" then
            -- Add magical effects to tiles
            for _, tile in pairs(chunk.tiles) do
                if math.random() < 0.1 then
                    tile.glowing = true
                    tile.magicAura = "nature"
                end
            end
        elseif variant == "corrupted" then
            -- Add corruption effects
            for _, tile in pairs(chunk.tiles) do
                if math.random() < ForestBiome.variants.corrupted.corruptionLevel then
                    tile.corrupted = true
                    tile.corruptionLevel = math.random()
                end
            end
        end
    end
}

-- Initialize the biome module
function ForestBiome.init(worldCore)
    print("Forest biome module initialized")
    
    -- Store reference to WorldCore for later use
    ForestBiome.worldCore = worldCore
    
    -- Don't try to register generators at init time
    -- Instead, we'll register them when a world is created
    
    -- Store the biome in the biomes registry
    -- This allows the module to be used later when worlds are created
    print("Forest biome registered successfully")
end

-- Add a new function to register generators with a specific world instance
function ForestBiome.registerWithWorld(world)
    print("Registering forest biome generators with world")
    
    if not world or not world.chunkSystem then
        print("Warning: Cannot register forest biome - world or chunkSystem is nil")
        return false
    end
    
    -- Register generation algorithms with the world
    world.chunkSystem:registerGenerator("forest", ForestBiome.generate)
    
    -- Register biome variants
    for variantId, variant in pairs(ForestBiome.variants) do
        world.chunkSystem:registerGenerator("forest_" .. variantId, function(chunk, world)
            ForestBiome.generate(chunk, world, variantId)
            ForestBiome.postProcess(chunk, world, variantId)
        end)
    end
    
    return true
end

return ForestBiome
