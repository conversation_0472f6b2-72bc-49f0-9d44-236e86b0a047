-- tiles/ancient_technology.lua
local AncientTechnologyTile = {
    id = "ancient_technology",
    name = "Ancient Technology",
    passable = true,
    variants = 7, -- Different tech artifacts and devices
    
    -- Entities that can spawn near ancient tech
    spawns = {"repair_drone", "security_construct", "tech_scavenger", "ancient_terminal"}, --
    
    -- Tech-specific properties
    techType = nil, -- Set during init
    powerLevel = 0, -- Set during init (0-5)
    functioning = false, -- Is it actually working --
    security = 0, -- Security level (0-10)
    dataContent = nil, -- What info it might contain
    
    -- Interaction function
    interact = function(tile, entity) --
        -- Check power level first
        if tile.powerLevel <= 0 then --
            return { --
                success = false, --
                message = "This ancient technology appears to be completely dead." --
            } --
        end --
        
        -- Check for tech scanner
        if entity.hasItem and entity.hasItem("tech_scanner") then --
            -- Can analyze with scanner
            local techInfo = { --
                power = "power generation", --
                defense = "defensive systems", --
                communication = "communication array", --
                portal = "teleportation technology", --
                manufacturing = "automated manufacturing", --
                medical = "medical technology", --
                unknown = "unknown purpose" --
            } --
            
            local techPurpose = techInfo[tile.techType] or "unknown purpose" --
            
            return { --
                success = true, --
                message = "Your scanner identifies this as " .. techPurpose .. " technology. Power level: " .. tile.powerLevel .. "/5", --
                effects = { --
                    {type = "gain_knowledge", category = "technology", tech = tile.techType} --
                } --
            } --
        end --
        
        -- Attempt to activate technology
        if math.random() < (0.2 + (tile.powerLevel * 0.1)) then --
            -- Technology activates!
            if tile.techType == "power" then --
                return { --
                    success = true, --
                    message = "The ancient power source surges to life, restoring your energy!", --
                    effects = { --
                        {type = "restore_energy", amount = tile.powerLevel * 5} --
                    } --
                } --
            elseif tile.techType == "defense" then --
                -- Defense system activates - could be good or bad --
                if tile.security > 5 or math.random() < 0.4 then --
                    return { --
                        success = false, --
                        message = "The defense system identifies you as a threat!", --
                        effects = { --
                            {type = "spawn", entity = "defense_drone", hostile = true} --
                        } --
                    } --
                else --
                    return { --
                        success = true, --
                        message = "The defense system activates to protect you!", --
                        effects = { --
                            {type = "spawn", entity = "defense_drone", friendly = true} --
                        } --
                    } --
                end --
            elseif tile.techType == "portal" then --
                if tile.powerLevel >= 3 then --
                    return { --
                        success = true, --
                        message = "The ancient portal device hums to life!", --
                        effects = { --
                            {type = "create_portal", temporary = true, duration = tile.powerLevel * 30} --
                        } --
                    } --
                else --
                    return { --
                        success = false, --
                        message = "The portal device activates but doesn't have enough power to maintain a connection." --
                    } --
                end --
            elseif tile.techType == "communication" then --
                return { --
                    success = true, --
                    message = "The communication device activates, relaying ancient recordings!", --
                    effects = { --
                        {type = "play_recording", recording = "ancient_message_" .. math.random(5)} --
                    } --
                } --
            elseif tile.techType == "medical" then --
                return { --
                    success = true, --
                    message = "The medical device scans your body and applies targeted healing energy!", --
                    effects = { --
                        {type = "heal", amount = tile.powerLevel * 10}, --
                        {type = "cure_status", status = "poison"}, --
                        {type = "cure_status", status = "bleeding"} --
                    } --
                } --
            elseif tile.techType == "manufacturing" then --
                if tile.powerLevel >= 2 then --
                    local items = { --
                        {name = "tech_component", chance = 0.4}, --
                        {name = "power_cell", chance = 0.3}, --
                        {name = "synthetic_material", chance = 0.2}, --
                        {name = "advanced_tool", chance = 0.1} --
                    } --
                    
                    for _, item in ipairs(items) do --
                        if math.random() < item.chance then --
                            return { --
                                success = true, --
                                message = "The manufacturing device whirs to life and produces a " .. item.name .. "!", --
                                effects = { --
                                    {type = "add_item", item = item.name, quantity = 1} --
                                } --
                            } --
                        end --
                    end --
                else --
                    return { --
                        success = false, --
                        message = "The manufacturing device tries to activate but doesn't have enough power." --
                    } --
                end --
            else --
                -- Unknown tech has random effects
                local effects = { --
                    {message = "The device emits a strange energy field that heals you!", effect = {type = "restore_health", amount = 20}}, --
                    {message = "The technology sends information directly to your mind!", effect = {type = "gain_knowledge", amount = 1}}, --
                    {message = "The device malfunctions, releasing harmful radiation!", effect = {type = "damage", amount = 10, element = "tech"}}, --
                    {message = "The ancient machine whirs and produces a strange artifact!", effect = {type = "add_item", item = "strange_artifact"}} --
                } --
                
                local chosenEffect = effects[math.random(#effects)] --
                return { --
                    success = true, --
                    message = chosenEffect.message, --
                    effects = {chosenEffect.effect} --
                } --
            end --
        else --
            -- Failed to activate
            tile.powerLevel = tile.powerLevel - 1 --
            if tile.powerLevel < 0 then tile.powerLevel = 0 end --
            
            return { --
                success = false, --
                message = "The ancient technology sputters but fails to activate fully. It seems to have lost some power." --
            } --
        end --
    end, -- Comma removed after function end if present
    
    -- Weather effects
    weatherEffects = { --
        lightning = function(tile) --
            -- Lightning can recharge technology
            if math.random() < 0.4 then --
                local oldPower = tile.powerLevel --
                tile.powerLevel = math.min(5, tile.powerLevel + 1 + math.random(0, 1)) --
                
                if oldPower == 0 and tile.powerLevel > 0 then --
                    return "activate", "power_restored" --
                else --
                    return "power_increase" --
                end --
            end --
            return nil --
        end, --
        
        rain = function(tile) --
            -- Rain can damage unprotected tech
            if tile.weatherProof then --
                return nil --
            end --
            
            if math.random() < 0.2 then --
                tile.malfunctioning = true --
                return "malfunction" --
            end --
            return nil --
        end --
    }, --
    
    -- Time effects
    timeEffects = { --
        night = function(tile) --
            -- Ancient tech sometimes recharges at night or activates --
            if tile.powerLevel < 5 and math.random() < 0.2 then --
                tile.powerLevel = tile.powerLevel + 1 --
                return "power_up" --
            end --
            
            -- Tech might glow at night --
            if tile.powerLevel > 0 then --
                tile.glowing = true --
            end --
            
            return nil --
        end, --
        
        day = function(tile) --
            -- Reset glow during day
            tile.glowing = false --
            
            -- Solar tech might recharge during day
            if tile.techType == "power" and tile.powerLevel < 5 and math.random() < 0.3 then --
                tile.powerLevel = tile.powerLevel + 1 --
                return "solar_recharge" --
            end --
            
            return nil --
        end --
    }, --
    
    -- Visual properties
    visual = { --
        base_color = {0.4, 0.4, 0.4}, -- Metallic grey --
        variants = { --
            {name = "console", type = "interactive"}, --
            {name = "power_node", type = "power"}, --
            {name = "portal_device", type = "portal"}, --
            {name = "defense_turret", type = "defense"}, --
            {name = "medical_station", type = "medical"}, --
            {name = "communication_array", type = "communication"}, --
            {name = "fabricator", type = "manufacturing"} --
        }, --
        power_indicators = { --
            powered = {color = {0.2, 0.8, 0.3}, pulse = true}, --
            low_power = {color = {0.8, 0.6, 0.2}, flicker = true}, --
            unpowered = {color = {0.5, 0.0, 0.0}, active = false} --
        }, --
        tech_lights = { --
            holo_displays = {chance = 0.3, power_required = 2}, --
            status_lights = {chance = 0.7, power_required = 1}, --
        } --
    } --
} --

-- Initialize the tile
function AncientTechnologyTile.init(tile, world) --
    -- Set random tech type if not set
    if not tile.techType then --
        local techTypes = {"power", "defense", "communication", "portal", "manufacturing", "medical", "unknown"} --
        tile.techType = techTypes[math.random(#techTypes)] --
    end --
    
    -- Set initial power level
    tile.powerLevel = math.random(0, 3) --
    
    -- Set security level based on tech type
    if tile.techType == "defense" then --
        tile.security = math.random(3, 8) --
    else --
        tile.security = math.random(0, 5) --
    end --
    
    -- Set random variant
    if tile.visual and tile.visual.variants then --
        local variants = tile.visual.variants --
        local matchingVariants = {} --
        for _, variant in ipairs(variants) do --
            if variant.type == tile.techType then --
                table.insert(matchingVariants, variant) --
            end --
        end --
        if #matchingVariants > 0 then --
            tile.currentVariant = matchingVariants[math.random(#matchingVariants)] --
        end --
    end --
    
    return tile --
end --

return AncientTechnologyTile 