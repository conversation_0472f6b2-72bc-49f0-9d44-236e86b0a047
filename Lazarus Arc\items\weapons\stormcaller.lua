local Stormcaller = {
    id = "stormcaller",
    name = "Stormcaller",
    type = "weapon",
    category = "staff",
    
    -- Properties
    properties = {
        damage = 40,
        attackSpeed = 1.2,
        range = 5.0,
        durability = 220,
        maxDurability = 220,
        lightningSpeed = 14,
        lightningSpread = 0.15,
        lightningChance = 0.35,
        lightningDamage = 30,
        lightningRadius = 2.0,
        chainLightningChance = 0.25,
        chainLightningDamage = 20,
        chainLightningJumps = 3,
        thunderStrikeChance = 0.2,
        thunderStrikeDamage = 45,
        thunderStrikeRadius = 3.5,
        thunderStrikeForce = 2.5,
        windGustChance = 0.15,
        windGustDamage = 15,
        windGustRadius = 2.5,
        windGustForce = 1.8,
        stormCharge = 0,
        maxStormCharge = 100,
        chargeRate = 3,
        dischargeRate = 10,
        lightningResistance = 0.8,
        windSpeed = 1.2,
        stormRadius = 4.0
    },
    
    -- Appearance
    appearance = {
        sprite = "stormcaller",
        scale = 1.0,
        animations = {
            "idle",
            "cast",
            "strike",
            "charge"
        },
        variants = {
            "blue",
            "purple",
            "azure",
            "storm"
        },
        blendMode = "add",
        tint = {0.4, 0.6, 1.0},
        alpha = 0.9
    },
    
    -- Sound effects
    sounds = {
        cast = "stormcaller_cast",
        strike = "stormcaller_strike",
        thunder = "stormcaller_thunder",
        wind = "stormcaller_wind"
    },
    
    -- Effects
    effects = {
        lightning = {
            type = "status",
            duration = 0.5,
            effects = {
                damage = 30,
                stun = true,
                radius = 2.0
            }
        },
        thunder = {
            type = "status",
            duration = 0.5,
            effects = {
                damage = 45,
                knockback = 2.5,
                radius = 3.5
            }
        },
        windGust = {
            type = "status",
            duration = 0.5,
            effects = {
                damage = 15,
                push = 1.8,
                radius = 2.5
            }
        }
    }
}

-- Initialize the weapon
function Stormcaller.init(weapon, world)
    -- Copy all fields from Stormcaller template to weapon instance
    for k, v in pairs(Stormcaller) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        stormCharge = 0,
        lastChargeTime = 0,
        lastStrikeTime = 0
    }

    return weapon
end

-- Update the weapon
function Stormcaller.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Update storm charge
        if world.weather and world.weather == "storm" then
            local chargeFactor = (world.windSpeed * 0.5 + 0.5) * dt
            weapon.properties.weaponState.stormCharge = math.min(weapon.properties.maxStormCharge,
                weapon.properties.weaponState.stormCharge + chargeFactor * weapon.properties.chargeRate)
        else
            weapon.properties.weaponState.stormCharge = math.max(0,
                weapon.properties.weaponState.stormCharge - weapon.properties.dischargeRate * dt)
        end
        
        -- Create storm effect
        if world.createEffect then
            world.createEffect({
                type = "storm",
                position = weapon.position,
                radius = weapon.properties.stormRadius,
                duration = dt
            })
        end
    end
end

-- Handle attack
function Stormcaller.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Calculate lightning direction
    local angle = math.atan2(
        target.position.y - attacker.position.y,
        target.position.x - attacker.position.x
    )
    
    -- Add spread to lightning direction
    local spread = (math.random() - 0.5) * weapon.properties.lightningSpread
    angle = angle + spread
    
    -- Create lightning projectile
    if world.createProjectile then
        world.createProjectile({
            type = "lightning",
            position = attacker.position,
            direction = {
                x = math.cos(angle) * weapon.properties.lightningSpeed,
                y = math.sin(angle) * weapon.properties.lightningSpeed
            },
            damage = weapon.properties.damage,
            range = weapon.properties.range
        })
    end
    
    -- Check for lightning effect
    if math.random() < weapon.properties.lightningChance then
        -- Create lightning effect
        if world.createEffect then
            world.createEffect({
                type = "lightning",
                position = target.position,
                radius = weapon.properties.lightningRadius,
                duration = 0.5
            })
        end
        
        -- Apply lightning effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.lightning)
        end
        
        -- Play strike sound
        if world.playSound then
            world.playSound(weapon.sounds.strike)
        end
        
        -- Check for chain lightning
        if math.random() < weapon.properties.chainLightningChance then
            -- Find nearby entities for chain lightning
            if world.entities then
                local hitEntities = {}
                for _, entity in ipairs(world.entities) do
                    if entity.position and entity ~= target then
                        local distance = math.sqrt(
                            (entity.position.x - target.position.x)^2 + 
                            (entity.position.y - target.position.y)^2
                        )
                        
                        if distance <= weapon.properties.lightningRadius * 2 then
                            table.insert(hitEntities, entity)
                        end
                    end
                end
                
                -- Apply chain lightning to random entities
                for i = 1, math.min(weapon.properties.chainLightningJumps, #hitEntities) do
                    local randomIndex = math.random(#hitEntities)
                    local entity = hitEntities[randomIndex]
                    table.remove(hitEntities, randomIndex)
                    
                    if entity.applyEffect then
                        entity.applyEffect(weapon.effects.lightning)
                    end
                end
            end
        end
    end
    
    -- Check for thunder strike
    if math.random() < weapon.properties.thunderStrikeChance then
        -- Create thunder strike effect
        if world.createEffect then
            world.createEffect({
                type = "thunder",
                position = target.position,
                radius = weapon.properties.thunderStrikeRadius,
                duration = 0.5
            })
        end
        
        -- Apply thunder strike effect to nearby entities
        if world.entities then
            for _, entity in ipairs(world.entities) do
                if entity.position then
                    local distance = math.sqrt(
                        (entity.position.x - target.position.x)^2 + 
                        (entity.position.y - target.position.y)^2
                    )
                    
                    if distance <= weapon.properties.thunderStrikeRadius then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.thunder)
                        end
                        
                        -- Apply knockback
                        if entity.move then
                            local force = (1 - distance / weapon.properties.thunderStrikeRadius) * 
                                weapon.properties.thunderStrikeForce
                            local angle = math.atan2(
                                entity.position.y - target.position.y,
                                entity.position.x - target.position.x
                            )
                            entity.move({
                                x = math.cos(angle) * force,
                                y = math.sin(angle) * force
                            })
                        end
                    end
                end
            end
        end
        
        -- Play thunder sound
        if world.playSound then
            world.playSound(weapon.sounds.thunder)
        end
    end
    
    -- Check for wind gust
    if math.random() < weapon.properties.windGustChance then
        -- Create wind gust effect
        if world.createEffect then
            world.createEffect({
                type = "wind_gust",
                position = target.position,
                radius = weapon.properties.windGustRadius,
                duration = 0.5
            })
        end
        
        -- Apply wind gust effect to nearby entities
        if world.entities then
            for _, entity in ipairs(world.entities) do
                if entity.position then
                    local distance = math.sqrt(
                        (entity.position.x - target.position.x)^2 + 
                        (entity.position.y - target.position.y)^2
                    )
                    
                    if distance <= weapon.properties.windGustRadius then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.windGust)
                        end
                        
                        -- Apply wind force
                        if entity.move then
                            local force = (1 - distance / weapon.properties.windGustRadius) * 
                                weapon.properties.windGustForce
                            local angle = math.atan2(
                                entity.position.y - target.position.y,
                                entity.position.x - target.position.x
                            )
                            entity.move({
                                x = math.cos(angle) * force,
                                y = math.sin(angle) * force
                            })
                        end
                    end
                end
            end
        end
        
        -- Play wind sound
        if world.playSound then
            world.playSound(weapon.sounds.wind)
        end
    end
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function Stormcaller.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough storm charge
    if weapon.properties.weaponState.stormCharge < 50 then
        return false
    end
    
    -- Create storm effect
    if world.createEffect then
        world.createEffect({
            type = "storm",
            position = attacker.position,
            radius = weapon.properties.stormRadius * 2,
            duration = 8,
            damage = weapon.properties.damage * 1.5
        })
    end
    
    -- Apply storm effects to nearby entities
    if world.entities then
        for _, entity in ipairs(world.entities) do
            if entity.position then
                local distance = math.sqrt(
                    (entity.position.x - attacker.position.x)^2 + 
                    (entity.position.y - attacker.position.y)^2
                )
                
                if distance <= weapon.properties.stormRadius * 2 then
                    -- Apply random storm effect
                    local effect = math.random()
                    if effect < 0.4 then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.lightning)
                        end
                    elseif effect < 0.7 then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.thunder)
                        end
                    else
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.windGust)
                        end
                    end
                end
            end
        end
    end
    
    -- Reset storm charge
    weapon.properties.weaponState.stormCharge = 0
    weapon.properties.weaponState.lastChargeTime = world.time
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 5)
    
    return true
end

return Stormcaller 