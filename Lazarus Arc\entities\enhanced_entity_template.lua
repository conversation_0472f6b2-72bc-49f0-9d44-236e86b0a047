-- entities/enhanced_entity_template.lua
-- Enhanced entity template with shiny variants, rare drops, and rarity system
-- Use this as a base for creating new entities with advanced features

local EnhancedEntityTemplate = {
    id = "enhanced_entity_template",
    name = "Enhanced Entity Template",
    type = "template",
    
    -- Basic entity properties
    shape = {
        {0, -1}, {0.7, -0.7}, {1, 0}, {0.7, 0.7},
        {0, 1}, {-0.7, 0.7}, {-1, 0}, {-0.7, -0.7}
    },
    size = 6,
    description = "A template entity with enhanced variant and rarity systems",
    
    -- Entity categories
    categories = {"template"},
    threatCategories = {},
    
    -- Base stats (these will be modified by variants)
    maxHealth = 100,
    health = 100,
    maxStamina = 50,
    stamina = 50,
    speed = 1.0,
    attack = 10,
    defense = 5,
    
    -- Behaviors
    behaviors = {"idle", "wander"},
    behaviorConfigs = {
        idle = {
            duration = {2, 5}
        },
        wander = {
            moveSpeed = 1.0,
            changeDirectionChance = 0.05,
            wanderRadius = 5
        }
    },
    
    -- === ENHANCED VARIANT SYSTEM ===
    
    -- Probability of spawning as different variants
    variantChances = {
        normal = 0.85,      -- 85% chance for normal
        shiny = 0.10,       -- 10% chance for shiny
        rare = 0.04,        -- 4% chance for rare
        legendary = 0.01    -- 1% chance for legendary
    },
    
    -- Variant definitions with modifiers
    variants = {
        normal = {
            name = "Normal",
            description = "A standard entity",
            statModifiers = {
                -- No modifiers for normal variant
            },
            appearanceModifiers = {
                -- No appearance changes for normal
            },
            soundModifiers = {
                -- No sound changes for normal
            }
        },
        
        shiny = {
            name = "Shiny",
            description = "A rare, gleaming variant with enhanced abilities",
            statModifiers = {
                maxHealth = 1.2,    -- 20% more health
                attack = 1.15,      -- 15% more attack
                speed = 1.1,        -- 10% more speed
                defense = 1.1       -- 10% more defense
            },
            appearanceModifiers = {
                scale = 1.1,        -- Slightly larger
                glow = true,        -- Add glow effect
                colorTint = {1.2, 1.2, 0.8, 1.0}  -- Golden tint
            },
            soundModifiers = {
                pitch = 1.2,        -- Higher pitched sounds
                volume = 1.1        -- Slightly louder
            }
        },
        
        rare = {
            name = "Rare",
            description = "An uncommon variant with unique properties",
            statModifiers = {
                maxHealth = 1.5,    -- 50% more health
                attack = 1.3,       -- 30% more attack
                speed = 1.2,        -- 20% more speed
                defense = 1.25      -- 25% more defense
            },
            appearanceModifiers = {
                scale = 1.2,        -- Larger
                colorTint = {0.8, 1.2, 1.2, 1.0}  -- Blue-cyan tint
                -- Add particle effects, special animations, etc.
            },
            soundModifiers = {
                pitch = 0.9,        -- Lower pitched sounds
                volume = 1.2,       -- Louder
                reverb = true       -- Add reverb effect
            }
        },
        
        legendary = {
            name = "Legendary",
            description = "An extremely rare and powerful variant",
            statModifiers = {
                maxHealth = 2.0,    -- 100% more health
                attack = 1.5,       -- 50% more attack
                speed = 1.3,        -- 30% more speed
                defense = 1.5       -- 50% more defense
            },
            appearanceModifiers = {
                scale = 1.5,        -- Much larger
                glow = true,
                colorTint = {1.5, 0.8, 1.5, 1.0},  -- Purple-magenta tint
                aura = "legendary"  -- Special aura effect
            },
            soundModifiers = {
                pitch = 0.8,        -- Much lower pitched
                volume = 1.5,       -- Much louder
                reverb = true,
                echo = true         -- Add echo effect
            }
        }
    },
    
    -- === ENHANCED DROP SYSTEM ===
    
    -- Base drops (always available regardless of variant)
    baseDrops = {
        {id = "basic_material", chance = 0.8, quantity = {1, 2}},
        {id = "common_essence", chance = 0.6, quantity = {1, 1}}
    },
    
    -- Variant-specific drops (only drop from specific variants)
    variantDrops = {
        shiny = {
            {id = "shiny_essence", chance = 0.7, quantity = {1, 1}},
            {id = "gleaming_shard", chance = 0.4, quantity = {1, 1}}
        },
        rare = {
            {id = "rare_crystal", chance = 0.6, quantity = {1, 1}},
            {id = "uncommon_essence", chance = 0.8, quantity = {1, 2}},
            {id = "rare_material", chance = 0.5, quantity = {1, 1}}
        },
        legendary = {
            {id = "legendary_core", chance = 0.9, quantity = {1, 1}},
            {id = "mythic_essence", chance = 0.7, quantity = {1, 2}},
            {id = "legendary_artifact", chance = 0.3, quantity = {1, 1}}
        }
    },
    
    -- Appearance system
    appearance = {
        sprite = "template_entity",
        scale = 1.0,
        animations = {"idle", "move", "attack", "death"},
        variants = {
            "normal", "shiny", "rare", "legendary"
        }
    },
    
    -- Sound effects
    sounds = {
        idle = {
            file = "template_idle",
            synth = {
                instrument = "kalimba",
                notes = {"C4"},
                duration = 0.5,
                volume = 0.3
            }
        },
        hurt = {
            file = "template_hurt",
            synth = {
                instrument = "kalimba",
                notes = {"D#4"},
                duration = 0.25,
                volume = 0.4
            }
        },
        death = {
            file = "template_death",
            synth = {
                instrument = "bass_guitar",
                notes = {"F3"},
                duration = 1.0,
                volume = 0.4
            }
        }
    }
}

-- === ENHANCED INITIALIZATION FUNCTION ===

function EnhancedEntityTemplate.init(entity, world)
    -- Copy all fields from template to entity instance
    for k, v in pairs(EnhancedEntityTemplate) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- === DETERMINE VARIANT ===
    entity.currentVariant = EnhancedEntityTemplate.determineVariant(entity)
    
    -- Apply variant modifiers
    EnhancedEntityTemplate.applyVariantModifiers(entity)
    
    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    return entity
end

-- Determine which variant this entity should be
function EnhancedEntityTemplate.determineVariant(entity)
    local roll = math.random()
    local cumulative = 0
    
    for variant, chance in pairs(entity.variantChances) do
        cumulative = cumulative + chance
        if roll <= cumulative then
            return variant
        end
    end
    
    return "normal" -- Fallback
end

-- Apply variant modifiers to entity stats and appearance
function EnhancedEntityTemplate.applyVariantModifiers(entity)
    local variant = entity.variants[entity.currentVariant]
    if not variant then return end
    
    -- Apply stat modifiers
    if variant.statModifiers then
        for stat, modifier in pairs(variant.statModifiers) do
            if entity[stat] then
                entity[stat] = entity[stat] * modifier
            end
        end
    end
    
    -- Apply appearance modifiers
    if variant.appearanceModifiers then
        entity.appearance = entity.appearance or {}
        for key, value in pairs(variant.appearanceModifiers) do
            entity.appearance[key] = value
        end
    end
    
    -- Store variant info for easy access
    entity.variantInfo = variant
end

-- Get all possible drops for this entity based on its variant
function EnhancedEntityTemplate.getAllDrops(entity)
    local allDrops = {}
    
    -- Add base drops
    if entity.baseDrops then
        for _, drop in ipairs(entity.baseDrops) do
            table.insert(allDrops, drop)
        end
    end
    
    -- Add variant-specific drops
    if entity.variantDrops and entity.variantDrops[entity.currentVariant] then
        for _, drop in ipairs(entity.variantDrops[entity.currentVariant]) do
            table.insert(allDrops, drop)
        end
    end
    
    return allDrops
end

return EnhancedEntityTemplate
