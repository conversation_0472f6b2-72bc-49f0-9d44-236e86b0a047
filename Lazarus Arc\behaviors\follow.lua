-- follow.lua
-- Follow behavior for <PERSON> Arc

-- Declare FollowBehavior at the top level so it can be referenced from within functions
local FollowBehavior = {}

-- Define the behavior properties
FollowBehavior = {
    id = "follow",
    name = "Follow Behavior",
    
    -- Default configuration
    defaultConfig = {
        followDistance = 3,
        maxSpeed = 2,
        target = nil,
        loseSightDistance = 10,
        stoppingDistance = 1
    },

    -- Initialize behavior
    init = function(entity, customConfig)
        -- Create follow state for this entity
        entity.followState = {
            target = nil,
            config = {}
        }
        
        -- Set default configuration
        local config = entity.followState.config
        for k, v in pairs(FollowBehavior.defaultConfig) do
            config[k] = v
        end
        
        -- Apply custom configuration if provided
        if customConfig then
            for k, v in pairs(customConfig) do
                config[k] = v
            end
        end
    end,

    description = "Makes an entity follow a target",
    defaultOptions = {
        target = nil,
        speed = 100,
        minDistance = 50,
        maxDistance = 200,
        pathfinding = true,
        updateInterval = 0.5
    }
}

function FollowBehavior.new(options)
    options = options or {}
    local behavior = {
        id = FollowBehavior.id,
        name = FollowBehavior.name,
        description = FollowBehavior.description,
        options = {},
        lastUpdate = 0,
        currentPath = nil,
        pathIndex = 1
    }
    
    -- Apply default options
    for k, v in pairs(FollowBehavior.defaultOptions) do
        behavior.options[k] = options[k] ~= nil and options[k] or v
    end
    
    return behavior
end

function FollowBehavior:update(dt, world)
    if not self.entity or not self.options.target then return end
    
    local target = self.options.target
    if not target.position then return end
    
    -- Calculate distance to target
    local dx = target.position.x - self.entity.position.x
    local dy = target.position.y - self.entity.position.y
    local distance = math.sqrt(dx * dx + dy * dy)
    
    -- Check if we need to update path
    if self.options.pathfinding and (not self.currentPath or self.lastUpdate + self.options.updateInterval < love.timer.getTime()) then
        self:updatePath(world)
        self.lastUpdate = love.timer.getTime()
    end
    
    -- Move towards target
    if distance > self.options.minDistance and distance < self.options.maxDistance then
        if self.options.pathfinding and self.currentPath then
            -- Follow path
            self:followPath(dt)
        else
            -- Direct movement
            local angle = math.atan2(dy, dx)
            local speed = self.options.speed * dt
            self.entity.position.x = self.entity.position.x + math.cos(angle) * speed
            self.entity.position.y = self.entity.position.y + math.sin(angle) * speed
        end
    end
end

function FollowBehavior:updatePath(world)
    if not self.pathfinder then return end
    
    local start = {x = math.floor(self.entity.position.x), y = math.floor(self.entity.position.y)}
    local goal = {x = math.floor(self.options.target.position.x), y = math.floor(self.options.target.position.y)}
    
    self.currentPath = self.pathfinder:findPath(start, goal, world)
    self.pathIndex = 1
end

function FollowBehavior:followPath(dt)
    if not self.currentPath or #self.currentPath == 0 then return end
    
    local currentPoint = self.currentPath[self.pathIndex]
    if not currentPoint then return end
    
    local dx = currentPoint.x - self.entity.position.x
    local dy = currentPoint.y - self.entity.position.y
    local distance = math.sqrt(dx * dx + dy * dy)
    
    if distance < 5 then
        -- Move to next point in path
        self.pathIndex = self.pathIndex + 1
        if self.pathIndex > #self.currentPath then
            self.currentPath = nil
            return
        end
        currentPoint = self.currentPath[self.pathIndex]
        dx = currentPoint.x - self.entity.position.x
        dy = currentPoint.y - self.entity.position.y
        distance = math.sqrt(dx * dx + dy * dy)
    end
    
    -- Move towards current path point
    local angle = math.atan2(dy, dx)
    local speed = self.options.speed * dt
    self.entity.position.x = self.entity.position.x + math.cos(angle) * speed
    self.entity.position.y = self.entity.position.y + math.sin(angle) * speed
end

function FollowBehavior:destroy()
    self.entity = nil
    self.options.target = nil
    self.currentPath = nil
end

return FollowBehavior 