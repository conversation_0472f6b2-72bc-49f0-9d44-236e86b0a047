-- behaviors/farming.lua
-- Farming behavior for NPCs involved in agricultural activities

local FarmingBehavior = {}  -- create an empty table

FarmingBehavior.id = "farming"
FarmingBehavior.name = "Farming Behavior"

FarmingBehavior.defaultConfig = {
    farmRadius = 12,               -- Radius defining the farming area
    cropTypes = {"wheat", "corn", "vegetables"}, -- Types of crops planted
    plantingInterval = {120, 240}, -- Time interval between planting cycles
    wateringInterval = {60, 120},  -- Time interval between watering crops
    harvestingInterval = {300, 600},-- Interval between harvesting crops
    gatherSeeds = true,            -- Whether entity gathers seeds
    seedGatherRadius = 15,         -- Radius for finding seeds
    maxCarryCapacity = 20,         -- Max seeds or crops carried at a time
    moveSpeed = 1.0,               -- Movement speed around the farm
    returnHomeAfterTasks = true,   -- Return home after farming tasks
    homePosition = nil             -- Position to return after tasks
}

function FarmingBehavior.init(entity, customConfig)
    entity.farmingState = entity.farmingState or {
        currentTask = nil,
        taskTimer = 0,
        carrying = 0,
        config = {}
    }

    local config = entity.farmingState.config
    -- Merge default configuration values into the entity's state
    for k, v in pairs(FarmingBehavior.defaultConfig) do
        config[k] = v
    end

    if customConfig then
        for k, v in pairs(customConfig) do
            config[k] = v
        end
    end
end

function FarmingBehavior.update(entity, world, dt)
    local state = entity.farmingState
    local config = state.config

    -- Determine next task if none is assigned
    if not state.currentTask then
        state.currentTask = FarmingBehavior.determineNextTask(entity)
        state.taskTimer = FarmingBehavior.getTaskDuration(config, state.currentTask)
    else
        state.taskTimer = state.taskTimer - dt

        if state.taskTimer <= 0 then
            FarmingBehavior.completeTask(entity, world)
            state.currentTask = nil
        else
            FarmingBehavior.performTask(entity, world, dt)
        end
    end

    -- Return home if enabled and no current task remains
    if config.returnHomeAfterTasks and not state.currentTask then
        FarmingBehavior.returnHome(entity, dt)
    end
end

function FarmingBehavior.determineNextTask(entity)
    -- Implementation details...
end

function FarmingBehavior.getTaskDuration(config, task)
    -- Implementation details...
end

function FarmingBehavior.completeTask(entity, world)
    -- Implementation details...
end

function FarmingBehavior.performTask(entity, world, dt)
    -- Implementation details...
end

function FarmingBehavior.returnHome(entity, dt)
    -- Implementation details...
end

return FarmingBehavior
