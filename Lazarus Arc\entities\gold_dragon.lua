-- entities/gold_dragon.lua
-- Gold dragon with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local GoldDragon = {
    id = "gold_dragon",
    name = "Gold Dragon",
    type = "gold_dragon",
    shape = {
        {0, -22}, {16, -16}, {28, 0}, {16, 16},
        {0, 22}, {-16, 16}, {-28, 0}, {-16, -16}
    },
    size = 28,  -- Larger than other dragons

    -- Entity categories
    categories = {"dragon", "flying", "holy", "noble", "ancient"},
    threatCategories = {"evil", "undead", "demon", "corruption"},
    
    -- Base stats (noble holy dragon)
    maxHealth = 900,
    health = 900,
    maxStamina = 400,
    stamina = 400,
    maxMana = 600,
    mana = 600,
    speed = 3.8,
    attack = 65,
    defense = 50,
    magicAttack = 70,
    magicDefense = 60,
    holyResistance = 100,
    evilVulnerability = 0.5,  -- Resistant to evil
    flight_altitude = 60,
    divine_power = 35,
    nobility = 30,
    
    -- Behaviors
    behaviors = {"noble_flight", "holy_breath", "divine_magic", "righteous_fury"},
    behaviorConfigs = {
        noble_flight = {
            majesticSoaring = true,
            divineGrace = 2.0,
            celestialMovement = true,
            inspiringPresence = 2.5
        },
        holy_breath = {
            breathRange = 40,
            holyDamage = 3.0,
            undeadBane = 4.0,
            purification = true,
            healingLight = 2.0
        },
        divine_magic = {
            healingMagic = 3.0,
            blessingSpells = true,
            holyWard = 2.5,
            divineIntervention = 2.0
        },
        righteous_fury = {
            evilDetection = 3.0,
            righteousWrath = 2.5,
            divineJustice = true,
            protectorInstinct = 3.0
        }
    },
    
    -- Enhanced variant system for gold dragons
    variantChances = {
        normal = 0.45,          -- 45% young gold dragon
        shiny = 0.40,           -- 40% adult gold dragon (shiny)
        rare = 0.13,            -- 13% ancient gold dragon (rare)
        legendary = 0.02        -- 2% celestial emperor (legendary)
    },
    
    variants = {
        normal = {
            name = "Young Gold Dragon",
            description = "A noble young dragon with a strong sense of justice",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {1.3, 1.1, 0.6, 1.0}  -- Bright gold scales
            }
        },
        
        shiny = {
            name = "Adult Gold Dragon",
            description = "A mature noble dragon with mastery over divine magic",
            statModifiers = {
                maxHealth = 1.4,    -- 1260 health
                magicAttack = 1.5,  -- 105 magic attack
                defense = 1.3,      -- 65 defense
                divine_power = 1.8, -- Enhanced divine power
                holy_mastery = 2.0,
                noble_authority = 2.5
            },
            appearanceModifiers = {
                scale = 1.3,
                glow = true,
                colorTint = {1.5, 1.3, 0.7, 1.0},  -- Radiant gold with divine aura
                divine_aura = true,
                holy_light = true,
                majestic_presence = true
            },
            soundModifiers = {
                pitch = 0.9,
                volume = 1.3,
                divine = true
            }
        },
        
        rare = {
            name = "Ancient Gold Dragon",
            description = "An ancient wyrm that serves as a guardian of cosmic order",
            statModifiers = {
                maxHealth = 2.2,    -- 1980 health
                magicAttack = 2.0,  -- 140 magic attack
                defense = 1.8,      -- 90 defense
                divine_power = 2.5, -- Divine master
                holy_mastery = 3.5,
                cosmic_order = 4.0,
                ancient_wisdom = 5.0
            },
            appearanceModifiers = {
                scale = 1.6,
                colorTint = {1.4, 1.2, 0.8, 1.0},  -- Ancient gold with cosmic power
                cosmic_integration = true,
                order_manifestation = true,
                wisdom_aura = true,
                ancient_divinity = true
            },
            soundModifiers = {
                pitch = 0.7,
                volume = 1.5,
                reverb = true,
                cosmic_harmony = true
            }
        },
        
        legendary = {
            name = "Celestial Emperor",
            description = "A legendary dragon emperor that rules over all good dragons",
            statModifiers = {
                maxHealth = 3.0,    -- 2700 health
                magicAttack = 2.8,  -- 196 magic attack
                defense = 2.2,      -- 110 defense
                divine_power = 3.5, -- Ultimate divine power
                imperial_authority = 10.0,
                celestial_dominion = 5.0,
                cosmic_rulership = 3.0
            },
            appearanceModifiers = {
                scale = 2.0,
                glow = true,
                colorTint = {1.8, 1.5, 1.0, 1.0},  -- Celestial gold with imperial radiance
                imperial_crown = true,
                celestial_aura = "legendary",
                cosmic_authority = true,
                divine_emperor_presence = true
            },
            soundModifiers = {
                pitch = 0.5,
                volume = 1.8,
                reverb = true,
                echo = true,
                imperial_resonance = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "gold_dragon_scale", chance = 1.0, quantity = {6, 12}},
        {id = "dragon_blood", chance = 0.9, quantity = {3, 5}},
        {id = "holy_fang", chance = 0.8, quantity = {1, 2}},
        {id = "divine_essence", chance = 1.0, quantity = {4, 8}},
        {id = "noble_heart", chance = 0.8, quantity = {1, 1}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "noble_dragon_scale", chance = 1.0, quantity = {8, 15}},
            {id = "divine_mastery_orb", chance = 0.9, quantity = {1, 1}},
            {id = "holy_authority_crystal", chance = 0.8, quantity = {1, 1}},
            {id = "noble_essence", chance = 0.8, quantity = {2, 3}}
        },
        rare = {
            {id = "ancient_gold_scale", chance = 1.0, quantity = {10, 18}},
            {id = "cosmic_order_crystal", chance = 0.9, quantity = {1, 2}},
            {id = "ancient_wisdom_core", chance = 0.8, quantity = {1, 1}},
            {id = "divine_authority_essence", chance = 0.8, quantity = {2, 4}},
            {id = "cosmic_harmony_orb", chance = 0.7, quantity = {1, 1}}
        },
        legendary = {
            {id = "celestial_emperor_scale", chance = 1.0, quantity = {12, 25}},
            {id = "imperial_dragon_crown", chance = 0.95, quantity = {1, 1}},
            {id = "celestial_dominion_orb", chance = 0.9, quantity = {1, 1}},
            {id = "cosmic_rulership_essence", chance = 0.8, quantity = {1, 2}},
            {id = "divine_emperor_codex", chance = 0.7, quantity = {1, 1}},
            {id = "celestial_authority_scepter", chance = 0.6, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "gold_dragon",
        scale = 1.9,
        animations = {
            "majestic_soar", "holy_breath", "divine_roar", "blessing_ritual", "imperial_presence"
        },
        variants = {
            "young_gold", "adult_gold", "ancient_gold", "celestial_emperor"
        }
    },
    
    -- Sound effects with divine characteristics
    sounds = {
        divine_roar = {
            file = "gold_dragon_roar",
            synth = {
                instrument = "organ",
                notes = {"C1", "G1", "C2", "G2", "C3"},
                durations = {1.5, 1.2, 1.4, 1.3, 2.0},
                volume = 1.3,
                majestic = true
            }
        },
        holy_breath = {
            file = "holy_breath",
            synth = {
                instrument = "choir",
                notes = {"F3", "A3", "C4", "F4", "A4"},
                durations = {0.8, 0.7, 0.9, 0.8, 1.2},
                volume = 1.0,
                divine = true
            }
        },
        celestial_harmony = {
            file = "celestial_harmony",
            synth = {
                instrument = "harp",
                notes = {"C4", "E4", "G4", "C5", "E5", "G5"},
                durations = {0.6, 0.5, 0.6, 0.5, 0.7, 1.0},
                volume = 0.8,
                heavenly = true
            }
        },
        imperial_decree = {
            file = "imperial_decree",
            synth = {
                instrument = "brass",
                notes = {"G2", "C3", "E3", "G3", "C4"},
                durations = {1.0, 0.8, 1.0, 0.8, 1.5},
                volume = 1.1,
                authoritative = true
            }
        }
    },
    
    -- Special gold dragon abilities
    abilities = {
        holy_breath = {
            type = "active",
            description = "Divine breath that heals allies and destroys undead",
            effect = "holy_breath_attack",
            cooldown = 15
        },
        divine_blessing = {
            type = "active",
            description = "Blesses allies with divine protection and power",
            effect = "divine_blessing",
            manaCost = 80,
            cooldown = 20
        },
        righteous_fury = {
            type = "active",
            description = "Enters divine wrath mode against evil enemies",
            effect = "righteous_mode",
            duration = 20,
            cooldown = 40
        },
        holy_immunity = {
            type = "passive",
            description = "Immune to evil magic and resistant to corruption",
            effect = "holy_immunity"
        },
        divine_healing = {
            type = "active",
            description = "Heals self and nearby allies with divine light",
            effect = "area_divine_heal",
            manaCost = 60,
            cooldown = 18
        },
        celestial_authority = {
            type = "passive",
            description = "Commands respect from all good creatures",
            effect = "divine_authority"
        }
    }
}

-- Initialize the gold dragon entity using enhanced template
function GoldDragon.init(entity, world)
    -- Copy all fields from GoldDragon template to entity instance
    for k, v in pairs(GoldDragon) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return GoldDragon
