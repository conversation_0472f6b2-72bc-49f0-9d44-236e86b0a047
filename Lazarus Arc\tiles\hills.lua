local Hills = {
    id = "hills",
    name = "Hills",
    type = "tile",
    
    -- Categories
    categories = {"terrain", "hills", "elevation", "biome"},
    
    -- Properties
    properties = {
        walkable = true,
        swimable = false,
        climbable = true,
        flyable = true,
        diggable = true,
        buildable = true,
        flammable = false,
        elevation = 1.5,
        steepness = 0.6,
        erosion = 0.1,
        erosionRate = 0.05,
        grassDensity = 0.7,
        grassHeight = 0.5,
        grassColor = {0.4, 0.6, 0.2},
        rockChance = 0.3,
        rockTypes = {"stone", "granite", "limestone"},
        soilFertility = 0.6,
        soilMoisture = 0.5,
        windExposure = 0.8,
        windSpeed = 0.3,
        viewDistance = 3,
        slopeAngle = 0,
        slopeDirection = {x = 0, y = 0}
    },
    
    -- Appearance
    appearance = {
        sprite = "hills",
        scale = 1.0,
        animations = {
            "idle",
            "wind",
            "erosion",
            "grass"
        },
        variants = {
            "gentle",
            "steep",
            "rocky",
            "grassy"
        },
        blendMode = "normal",
        tint = {0.7, 0.7, 0.7},
        alpha = 1.0
    },
    
    -- Sound effects
    sounds = {
        wind = "hills_wind",
        dig = "hills_dig",
        grass = "hills_grass",
        rock = "hills_rock"
    },
    
    -- Resources
    resources = {
        grass = {
            type = "plant",
            amount = 2,
            regrowthRate = 0.1,
            regrowthTime = 300,
            harvestable = true,
            harvestAmount = 1,
            harvestTool = "hand"
        },
        stone = {
            type = "mineral",
            amount = 3,
            harvestable = true,
            harvestAmount = 1,
            harvestTool = "pickaxe"
        }
    },
    
    -- Effects
    effects = {
        wind = {
            type = "environment",
            duration = 0,
            effects = {
                windSpeed = 0.3,
                windDirection = {x = 0, y = 1}
            }
        },
        elevation = {
            type = "terrain",
            duration = 0,
            effects = {
                height = 1.5,
                slope = 0.6
            }
        }
    },
    
    -- Spawn rules
    spawnRules = {
        plants = {
            "hill_grass",
            "hill_flower",
            "hill_bush",
            "hill_tree"
        },
        structures = {
            "rock_formation",
            "hill_fort",
            "windmill",
            "watchtower"
        }
    }
}

-- Initialize the tile
function Hills.init(tile, world)
    -- Initialize properties first
    tile.properties = tile.properties or {}
    
    -- Calculate slope angle first
    local slopeAngle = math.random() * math.pi * 2
    
    -- Initialize hill state
    tile.properties.hillState = {
        grassAmount = math.random() * (Hills.properties.grassDensity or 0.7),
        erosionAmount = 0,
        hasRock = math.random() < (Hills.properties.rockChance or 0.3),
        rockType = Hills.properties.rockTypes[math.random(#Hills.properties.rockTypes)],
        slopeAngle = slopeAngle,
        slopeDirection = {
            x = math.cos(slopeAngle),
            y = math.sin(slopeAngle)
        }
    }

    -- Copy all fields from Hills template to tile instance
    for k, v in pairs(Hills) do
        if type(v) ~= "function" and tile[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                tile[k] = {}
                for subk, subv in pairs(v) do
                    tile[k][subk] = subv
                end
            else
                tile[k] = v
            end
        end
    end

    -- Set random variant
    if tile.appearance and tile.appearance.variants then
        local variants = tile.appearance.variants
        tile.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize resources
    if tile.resources then
        for resourceType, resource in pairs(tile.resources) do
            resource.currentAmount = resource.amount
            resource.lastHarvest = 0
            resource.growthStage = "mature"
        end
    end

    return tile
end

-- Update the tile
function Hills.update(tile, world, dt)
    -- Update hill state
    if tile.properties.hillState then
        -- Update grass growth
        if world.temperature and world.moisture then
            local growthFactor = (world.temperature * 0.5 + world.moisture * 0.5) * dt
            tile.properties.hillState.grassAmount = math.min(tile.properties.grassDensity,
                tile.properties.hillState.grassAmount + growthFactor)
        end
        
        -- Update erosion
        if world.wind and world.wind.strength > 0.5 then
            tile.properties.hillState.erosionAmount = math.min(1.0,
                tile.properties.hillState.erosionAmount + tile.properties.erosionRate * dt)
            
            -- Create erosion effect
            if world.createEffect then
                world.createEffect({
                    type = "erosion",
                    position = tile.position,
                    strength = tile.properties.hillState.erosionAmount
                })
            end
        end
        
        -- Update wind effects
        if world.wind then
            local windStrength = world.wind.strength * tile.properties.windExposure
            -- Create wind effect
            if world.createEffect then
                world.createEffect({
                    type = "wind",
                    position = tile.position,
                    direction = world.wind.direction,
                    strength = windStrength
                })
            end
        end
    end
    
    -- Update resources
    if tile.resources then
        for resourceType, resource in pairs(tile.resources) do
            if resource.currentAmount < resource.amount then
                -- Regrow resources
                local regrowthAmount = resource.regrowthRate * dt
                resource.currentAmount = math.min(resource.amount, resource.currentAmount + regrowthAmount)
                
                -- Update growth stage
                if resource.currentAmount >= resource.amount then
                    resource.growthStage = "mature"
                elseif resource.currentAmount >= resource.amount * 0.5 then
                    resource.growthStage = "growing"
                else
                    resource.growthStage = "young"
                end
            end
        end
    end
end

-- Handle interaction
function Hills.interact(tile, world, entity, action)
    if action == "walk" then
        -- Apply slope movement
        if entity.move then
            local slopeEffect = {
                x = tile.properties.hillState.slopeDirection.x * tile.properties.steepness * 0.1,
                y = tile.properties.hillState.slopeDirection.y * tile.properties.steepness * 0.1
            }
            entity.move(slopeEffect)
        end
        
        -- Create grass effect
        if tile.properties.hillState.grassAmount > 0 then
            if world.createEffect then
                world.createEffect({
                    type = "grass",
                    position = tile.position,
                    density = tile.properties.hillState.grassAmount
                })
            end
            
            -- Play grass sound
            if world.playSound then
                world.playSound(tile.sounds.grass)
            end
        end
    elseif action == "dig" then
        -- Handle digging
        if tile.properties.diggable then
            -- Check for rock
            if tile.properties.hillState.hasRock then
                -- Create stone item
                if world.createItem then
                    world.createItem({
                        type = tile.properties.hillState.rockType,
                        amount = 1,
                        position = tile.position
                    })
                end
                
                -- Play rock sound
                if world.playSound then
                    world.playSound(tile.sounds.rock)
                end
            else
                -- Create dirt item
                if world.createItem then
                    world.createItem({
                        type = "dirt",
                        amount = 1,
                        position = tile.position
                    })
                end
                
                -- Play dig sound
                if world.playSound then
                    world.playSound(tile.sounds.dig)
                end
            end
            
            -- Convert to dirt tile
            if world.setTile then
                world.setTile(tile.position, "dirt")
            end
        end
    elseif action == "harvest" then
        -- Handle resource harvesting
        if tile.resources then
            for resourceType, resource in pairs(tile.resources) do
                if resource.harvestable and resource.currentAmount > 0 then
                    -- Create harvested item
                    if world.createItem then
                        world.createItem({
                            type = resourceType,
                            amount = resource.harvestAmount,
                            position = tile.position
                        })
                    end
                    
                    -- Update resource amount
                    resource.currentAmount = math.max(0, resource.currentAmount - resource.harvestAmount)
                    resource.lastHarvest = world.time
                    resource.growthStage = "young"
                end
            end
        end
    end
end

return Hills 