-- biomes/plains.lua
local PlainsBiome = {
    id = "plains",
    name = "Plains",
    
    -- Core tile types used in this biome
    primaryTiles = {"grass", "tall_grass"},
    secondaryTiles = {"flowers", "water", "dirt"},
    rareTiles = {"ruins", "ancient_technology"},
    
    -- Default proportions (adjustable through variants)
    tileDistribution = {
        grass = 0.5,
        tall_grass = 0.35,
        flowers = 0.08,
        water = 0.05,
        dirt = 0.01,
        ruins = 0.005,
        ancient_technology = 0.005
    },
    
    -- Entities common to this biome
    commonEntities = {
        "rabbit", "field_mouse", "butterfly", "grasshopper", "bison"
    },
    
    uncommonEntities = {
        "wolf", "hawk", "wanderer", "wild_horse"
    },
    
    rareEntities = {
        "plains_guardian", "earth_elemental", "chimera"
    },
    
    -- Biome variants for diverse generation
    variants = {
        lush = {
            name = "Lush Grasslands",
            grassHeight = 1.5,
            fertility = 2.0,
            tileAdjustments = {
                grass = 0.4,
                tall_grass = 0.5,
                flowers = 0.09
            },
            entityAdjustments = {
                bison = 1.5,
                rabbit = 1.3
            }
        },
        flower = {
            name = "Flower Fields",
            grassHeight = 1.0,
            fertility = 1.7,
            tileAdjustments = {
                grass = 0.3,
                tall_grass = 0.3,
                flowers = 0.35
            },
            entityAdjustments = {
                butterfly = 2.0,
                bee = 1.5
            },
            specialEffects = {
                "pollen_particles",
                "floral_scent"
            }
        },
        savanna = {
            name = "Savanna",
            grassHeight = 0.7,
            fertility = 0.8,
            heatLevel = 1.3,
            tileAdjustments = {
                grass = 0.35,
                tall_grass = 0.3,
                dirt = 0.3
            },
            entityAdjustments = {
                bison = 0.5,
                elephant = 1.0,
                lion = 0.7
            }
        },
        steppes = {
            name = "Windswept Steppes",
            grassHeight = 0.5,
            fertility = 0.6,
            windLevel = 1.8,
            tileAdjustments = {
                grass = 0.6,
                tall_grass = 0.2,
                dirt = 0.15
            },
            entityAdjustments = {
                hawk = 1.5,
                wild_horse = 2.0
            },
            specialEffects = {
                "constant_wind",
                "dust_devils"
            }
        },
        ancient_fields = {
            name = "Ancient Fields",
            grassHeight = 0.9,
            fertility = 1.1,
            tileAdjustments = {
                grass = 0.4,
                tall_grass = 0.3,
                ruins = 0.15,
                ancient_technology = 0.08
            },
            entityAdjustments = {
                plains_guardian = 1.8,
                wanderer = 0.5
            },
            specialEffects = {
                "floating_lights",
                "mysterious_whispers"
            }
        }
    },
    
    -- Structures that can generate in this biome
    structures = {
        {
            name = "village",
            chance = 0.08,
            clearArea = true,
            entities = {"villager", "farmer", "guard"}
        },
        {
            name = "nomad_camp",
            chance = 0.1,
            requiresVariant = "steppes",
            entities = {"nomad", "horse_tamer"}
        },
        {
            name = "standing_stones",
            chance = 0.05,
            entities = {"druid", "nature_spirit"}
        },
        {
            name = "wild_herd",
            chance = 0.2,
            entities = {"wild_horse", "bison"}
        },
        {
            name = "ancient_monument",
            chance = 0.06,
            requiresVariant = "ancient_fields",
            entities = {"guardian_statue", "field_keeper"}
        }
    },
    
    -- Weather patterns common in this biome
    weather = {
        clear = 0.5,
        rain = 0.25,
        wind = 0.15,
        thunder = 0.1
    },
    
    -- Unique features generation
    features = {
        {
            name = "massive_tree",
            chance = 0.1,
            unique = true
        },
        {
            name = "wildflower_patch",
            chance = 0.25,
            requiresVariant = "flower"
        },
        {
            name = "natural_spring",
            chance = 0.08
        },
        {
            name = "stone_circle",
            chance = 0.07,
            requiresVariant = "ancient_fields"
        },
        {
            name = "bison_trail",
            chance = 0.15
        }
    },
    
    -- Generation algorithm for this biome
    generate = function(chunk, world, variant)
        print("Generating " .. (variant and PlainsBiome.variants[variant].name or "Plains") .. " biome")
        
        -- Get variant-specific adjustments
        local variantData = variant and PlainsBiome.variants[variant] or nil
        local grassHeight = variantData and variantData.grassHeight or 1.0
        
        -- Adjust tile distribution based on variant
        local tileDistribution = {}
        for tileType, chance in pairs(PlainsBiome.tileDistribution) do
            tileDistribution[tileType] = chance
            
            -- Apply variant adjustments if any
            if variantData and variantData.tileAdjustments and variantData.tileAdjustments[tileType] then
                tileDistribution[tileType] = variantData.tileAdjustments[tileType]
            end
        end
        
        -- Generate base terrain using noise functions
        local seed = world.seed + (chunk.x * 157 + chunk.y * 941)
        math.randomseed(seed)
        
        -- Create perlin noise-like function (simplified for example)
        local function noise2D(x, y, frequency)
            return math.abs(math.sin(x * frequency) * math.cos(y * frequency))
        end
        
        -- Initialize 2D tiles array if not exists
        chunk.tiles = chunk.tiles or {}
        for x = 0, world.CHUNK_SIZE - 1 do
            chunk.tiles[x] = chunk.tiles[x] or {}
        end
        
        -- Generate tiles
        for x = 0, world.CHUNK_SIZE - 1 do
            for y = 0, world.CHUNK_SIZE - 1 do
                -- Calculate world coordinates
                local worldX = chunk.x * world.CHUNK_SIZE + x
                local worldY = chunk.y * world.CHUNK_SIZE + y
                
                -- Generate different noise layers
                local baseNoise = noise2D(worldX, worldY, 0.05)  -- Larger scale for plains
                local detailNoise = noise2D(worldX, worldY, 0.2)
                local featureNoise = noise2D(worldX, worldY, 0.01)
                
                -- Determine tile type based on noise and distribution
                local tileType = "grass" -- Default
                local tileRoll = math.random()
                local cumulativeChance = 0
                
                for tType, chance in pairs(tileDistribution) do
                    cumulativeChance = cumulativeChance + chance
                    if tileRoll <= cumulativeChance then
                        tileType = tType
                        break
                    end
                end
                
                -- Special case: water forms in low areas (less common in plains)
                if baseNoise < 0.15 and detailNoise < 0.2 then
                    tileType = "water"
                end
                
                -- Variant-specific modifications
                if variant == "flower" and detailNoise > 0.6 then
                    -- More flowers in flower fields
                    if tileType == "grass" or tileType == "tall_grass" then
                        local flowerRoll = math.random()
                        if flowerRoll < 0.4 then
                            tileType = "flowers"
                        end
                    end
                elseif variant == "savanna" and baseNoise > 0.7 then
                    -- More dirt patches in savanna
                    if math.random() < 0.3 then
                        tileType = "dirt"
                    end
                elseif variant == "ancient_fields" and featureNoise > 0.7 then
                    -- Ruins and ancient tech scattered in ancient fields
                    if math.random() < 0.2 then
                        tileType = math.random() < 0.7 and "ruins" or "ancient_technology"
                    end
                end
                
                -- Create the tile
                local tile = {
                    type = tileType,
                    x = x, y = y,
                    variant = math.random(1, 3),
                    passable = true,
                    isUnderground = false
                }
                
                -- Add to chunk using 2D indexing
                chunk.tiles[x][y] = tile
            end
        end
        
        -- Place features
        PlainsBiome.generateFeatures(chunk, world, variant)
        
        -- Add entities
        PlainsBiome.populateEntities(chunk, world, variant)
        
        -- Place structures
        PlainsBiome.generateStructures(chunk, world, variant)
    end,
    
    -- Helper function to generate features
    generateFeatures = function(chunk, world, variant)
        -- Iterate through each potential feature
        for _, feature in ipairs(PlainsBiome.features) do
            -- Check if feature should spawn based on chance
            if math.random() < feature.chance then
                -- Check variant requirements if any
                if feature.requiresVariant and feature.requiresVariant ~= variant then
                    goto continue
                end
                
                -- For unique features, only place one per chunk
                if feature.unique and chunk.uniqueFeatures and chunk.uniqueFeatures[feature.name] then
                    goto continue
                end
                
                -- Determine position for feature
                local featureX = math.random(2, world.CHUNK_SIZE - 3)
                local featureY = math.random(2, world.CHUNK_SIZE - 3)
                
                -- Place feature based on type
                if feature.name == "massive_tree" then
                    -- Create a lone massive tree (rare in plains)
                    local worldX = chunk.x * world.CHUNK_SIZE + featureX
                    local worldY = chunk.y * world.CHUNK_SIZE + featureY
                    
                    -- Change tiles around position to create tree area
                    for dx = -1, 1 do
                        for dy = -1, 1 do
                            local tileX = featureX + dx
                            local tileY = featureY + dy
                            
                            -- Only modify if within chunk bounds
                            if tileX >= 0 and tileX < world.CHUNK_SIZE and
                               tileY >= 0 and tileY < world.CHUNK_SIZE then
                                local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                local tile = chunk.tiles[tileX][tileY]
                                
                                -- Make ground around tree special
                                tile.type = "grass"
                                tile.variant = 3 -- Special variant under massive tree
                                
                                -- Center tile is the tree
                                if dx == 0 and dy == 0 then
                                    world.entitySystem:addEntity("massive_tree", worldX, worldY, {
                                        size = 2.0,
                                        age = math.random(800, 1500),
                                        landmark = true
                                    })
                                end
                            end
                        end
                    end
                    
                elseif feature.name == "wildflower_patch" then
                    -- Create a dense patch of wildflowers
                    local radius = math.random(4, 7)
                    local worldX = chunk.x * world.CHUNK_SIZE + featureX
                    local worldY = chunk.y * world.CHUNK_SIZE + featureY
                    
                    -- Place flowers in a patch
                    for dx = -radius, radius do
                        for dy = -radius, radius do
                            -- Calculate distance from center
                            local dist = math.sqrt(dx*dx + dy*dy)
                            
                            if dist <= radius then
                                local chance = 1.0 - (dist / radius)
                                
                                if math.random() < chance then
                                    local tileX = featureX + dx
                                    local tileY = featureY + dy
                                    
                                    -- Check if tile is within chunk
                                    if tileX >= 0 and tileX < world.CHUNK_SIZE and
                                       tileY >= 0 and tileY < world.CHUNK_SIZE then
                                        local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                        chunk.tiles[tileX][tileY].type = "flowers"
                                        chunk.tiles[tileX][tileY].variant = math.random(1, 5) -- Different flower types
                                        
                                        -- Add butterflies and bees
                                        if math.random() < 0.1 then
                                            local worldTileX = chunk.x * world.CHUNK_SIZE + tileX
                                            local worldTileY = chunk.y * world.CHUNK_SIZE + tileY
                                            local entity = math.random() < 0.7 and "butterfly" or "bee"
                                            world.entitySystem:addEntity(entity, worldTileX, worldTileY)
                                        end
                                    end
                                end
                            end
                        end
                    end
                    
                elseif feature.name == "natural_spring" then
                    -- Create a small water spring
                    local radius = math.random(2, 4)
                    
                    for dx = -radius, radius do
                        for dy = -radius, radius do
                            local dist = math.sqrt(dx*dx + dy*dy)
                            if dist <= radius then
                                local tileX = featureX + dx
                                local tileY = featureY + dy
                                
                                if tileX >= 0 and tileX < world.CHUNK_SIZE and
                                   tileY >= 0 and tileY < world.CHUNK_SIZE then
                                    local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                    
                                    if dist <= radius * 0.7 then
                                        -- Center is water
                                        chunk.tiles[tileX][tileY].type = "water"
                                        chunk.tiles[tileX][tileY].variant = 1 -- Clear spring water
                                    else
                                        -- Edges are lush grass
                                        chunk.tiles[tileX][tileY].type = "grass"
                                        chunk.tiles[tileX][tileY].variant = 2 -- Lush green variant
                                    end
                                    
                                    -- Sometimes add special plants around water
                                    if dist > radius * 0.7 and dist <= radius and math.random() < 0.3 then
                                        local worldX = chunk.x * world.CHUNK_SIZE + tileX
                                        local worldY = chunk.y * world.CHUNK_SIZE + tileY
                                        world.entitySystem:addEntity("water_plant", worldX, worldY)
                                    end
                                end
                            end
                        end
                    end
                    
                elseif feature.name == "stone_circle" then
                    -- Create ancient stone circle
                    local radius = math.random(5, 8)
                    local worldX = chunk.x * world.CHUNK_SIZE + featureX
                    local worldY = chunk.y * world.CHUNK_SIZE + featureY
                    
                    -- Place stones in a circle
                    for angle = 0, 330, 30 do
                        local radians = angle * math.pi / 180
                        local stoneX = math.floor(worldX + math.cos(radians) * radius)
                        local stoneY = math.floor(worldY + math.sin(radians) * radius)
                        
                        world.entitySystem:addEntity("standing_stone", stoneX, stoneY, {
                            height = math.random(10, 20) / 10, -- 1.0 to 2.0
                            ancient = true,
                            runic = math.random() < 0.3
                        })
                    end
                    
                    -- Create a special central stone or altar
                    world.entitySystem:addEntity("ritual_stone", worldX, worldY, {
                        height = 1.5,
                        runic = true,
                        glowing = variant == "ancient_fields"
                    })
                    
                elseif feature.name == "bison_trail" then
                    -- Create a trail made by bison herds
                    local trailLength = math.random(world.CHUNK_SIZE / 2, world.CHUNK_SIZE * 1.5)
                    local startX = math.random(0, world.CHUNK_SIZE - 1)
                    local startY = math.random(0, world.CHUNK_SIZE - 1)
                    local angle = math.random() * math.pi * 2 -- Random direction
                    
                    local dx = math.cos(angle)
                    local dy = math.sin(angle)
                    
                    -- Create the trail
                    for i = 0, trailLength do
                        local x = math.floor(startX + dx * i)
                        local y = math.floor(startY + dy * i)
                        
                        -- Check if coordinates are within the chunk
                        if x >= 0 and x < world.CHUNK_SIZE and
                           y >= 0 and y < world.CHUNK_SIZE then
                            local tileIndex = x + y * world.CHUNK_SIZE
                            local tile = chunk.tiles[x][y]
                            
                            -- Trail is trampled grass or dirt
                            if tile.type == "grass" or tile.type == "tall_grass" or
                               tile.type == "flowers" then
                                tile.type = math.random() < 0.7 and "grass" or "dirt"
                                tile.variant = 1 -- Trampled variant
                                tile.trampled = true
                            end
                        end
                    end
                    
                    -- Add bison along the trail
                    if math.random() < 0.4 then
                        local bisonCount = math.random(3, 8)
                        for i = 1, bisonCount do
                            local offset = math.random(0, trailLength)
                            local bisonX = math.floor(chunk.x * world.CHUNK_SIZE + startX + dx * offset)
                            local bisonY = math.floor(chunk.y * world.CHUNK_SIZE + startY + dy * offset)
                            
                            world.entitySystem:addEntity("bison", bisonX, bisonY, {
                                inHerd = true,
                                herdSize = bisonCount
                            })
                        end
                    end
                end
                
                -- Mark unique features as placed
                if feature.unique then
                    if not chunk.uniqueFeatures then
                        chunk.uniqueFeatures = {}
                    end
                    chunk.uniqueFeatures[feature.name] = true
                end
            end
            
            ::continue::
        end
    end,
    
    -- Helper function to add entities
    populateEntities = function(chunk, world, variant)
        -- Get variant-specific entity adjustments
        local variantData = variant and PlainsBiome.variants[variant] or nil
        
        -- Determine entity counts (plains have more open space, so more entities)
        local commonCount = math.random(10, 18)
        local uncommonCount = math.random(4, 8)
        local rareCount = math.random(0, 2)
        
        -- Helper function to spawn an entity with variant adjustments
        local function spawnEntity(entityType, count)
            -- Apply variant adjustment if available
            local multiplier = 1.0
            if variantData and variantData.entityAdjustments and variantData.entityAdjustments[entityType] then
                multiplier = variantData.entityAdjustments[entityType]
            end
            
            -- Adjust count based on variant
            local adjustedCount = math.floor(count * multiplier + 0.5)
            
            -- Spawn entities
            for i = 1, adjustedCount do
                -- Pick a random position in the chunk
                local x = math.random(0, world.CHUNK_SIZE - 1)
                local y = math.random(0, world.CHUNK_SIZE - 1)
                local tileIndex = x + y * world.CHUNK_SIZE
                local tile = chunk.tiles[x][y]
                
                -- Check if tile is passable (not water)
                if tile.type ~= "water" then
                    local worldX = chunk.x * world.CHUNK_SIZE + x
                    local worldY = chunk.y * world.CHUNK_SIZE + y
                    
                    -- Spawn the entity
                    world.entitySystem:addEntity(entityType, worldX, worldY)
                end
            end
        end
        
        -- Spawn common entities
        for _, entityType in ipairs(PlainsBiome.commonEntities) do
            local count = math.random(1, 4)
            spawnEntity(entityType, count)
        end
        
        -- Spawn uncommon entities
        for _, entityType in ipairs(PlainsBiome.uncommonEntities) do
            local count = math.random(0, 2)
            spawnEntity(entityType, count)
        end
        
        -- Spawn rare entities
        for _, entityType in ipairs(PlainsBiome.rareEntities) do
            if math.random() < 0.25 then -- 25% chance for each rare entity
                spawnEntity(entityType, 1)
            end
        end
        
        -- Add variant-specific entities
        if variant == "savanna" then
            spawnEntity("elephant", math.random(1, 3))
            spawnEntity("lion", math.random(1, 2))
        elseif variant == "steppes" then
            spawnEntity("wild_horse", math.random(2, 5))
        elseif variant == "ancient_fields" then
            spawnEntity("plains_guardian", math.random(1, 2))
            spawnEntity("ancient_artifact", math.random(2, 4))
        end
    end,
    
    -- Helper function to place structures
    generateStructures = function(chunk, world, variant)
        -- Go through each structure type
        for _, structure in ipairs(PlainsBiome.structures) do
            -- Check chance and variant requirements
            if math.random() < structure.chance and
               (not structure.requiresVariant or structure.requiresVariant == variant) then
                
                -- Find suitable location for structure
                local structX = math.random(3, world.CHUNK_SIZE - 4)
                local structY = math.random(3, world.CHUNK_SIZE - 4)
                local worldX = chunk.x * world.CHUNK_SIZE + structX
                local worldY = chunk.y * world.CHUNK_SIZE + structY
                
                -- Place structure
                world.chunkSystem:placeStructure(chunk, structure.name, worldX, worldY)
                
                -- Add associated entities
                if structure.entities then
                    for _, entityType in ipairs(structure.entities) do
                        -- Add entity near structure
                        local offsetX = math.random(-2, 2)
                        local offsetY = math.random(-2, 2)
                        world.entitySystem:addEntity(entityType, worldX + offsetX, worldY + offsetY)
                    end
                end
                
                -- Clear area if needed
                if structure.clearArea then
                    for dx = -3, 3 do
                        for dy = -3, 3 do
                            local tileX = structX + dx
                            local tileY = structY + dy
                            
                            if tileX >= 0 and tileX < world.CHUNK_SIZE and
                               tileY >= 0 and tileY < world.CHUNK_SIZE then
                                local tileIndex = tileX + tileY * world.CHUNK_SIZE
                                local tile = chunk.tiles[tileX][tileY]
                                
                                -- Clear to simple grass around structures
                                if tile.type ~= "water" then
                                    tile.type = "grass"
                                    tile.variant = 1
                                end
                            end
                        end
                    end
                end
            end
        end
    end,
    
    -- Post-processing for variant-specific effects
    postProcess = function(chunk, world, variant)
        if variant == "flower" then
            -- Add flower effects
            for _, tile in pairs(chunk.tiles) do
                if tile.type == "flowers" and math.random() < 0.2 then
                    tile.pollenLevel = math.random(1, 3)
                end
            end
        elseif variant == "ancient_fields" then
            -- Add mysterious effects to ruins and tech
            for _, tile in pairs(chunk.tiles) do
                if (tile.type == "ruins" or tile.type == "ancient_technology") and math.random() < 0.4 then
                    tile.glowing = true
                    tile.energyLevel = math.random(1, 5)
                end
            end
        elseif variant == "steppes" then
            -- Add wind effects
            for _, tile in pairs(chunk.tiles) do
                if tile.type == "grass" or tile.type == "tall_grass" then
                    tile.windSwayDirection = math.random(1, 8) -- 8 directions
                    tile.windSwayIntensity = math.random(5, 10) / 10 -- 0.5 to 1.0
                end
            end
        end
    end
}

--- Initialize the biome module
function PlainsBiome.init(worldCore)
    print("Plains biome module initialized")
    
    -- Store reference to WorldCore for later use
    PlainsBiome.worldCore = worldCore
    
    -- Don't try to register generators at init time
    -- Instead, we'll register them when a world is created
    
    -- Store the biome in the biomes registry
    -- This allows the module to be used later when worlds are created
    print("Plains biome registered successfully")
end

-- Add a new function to register generators with a specific world instance
function PlainsBiome.registerWithWorld(world)
    print("Registering plains biome generators with world")
    
    if not world or not world.chunkSystem then
        print("Warning: Cannot register plains biome - world or chunkSystem is nil")
        return false
    end
    
    -- Register generation algorithms with the world
    world.chunkSystem:registerGenerator("plains", PlainsBiome.generate)
    
    -- Register biome variants
    for variantId, variant in pairs(PlainsBiome.variants) do
        world.chunkSystem:registerGenerator("plains_" .. variantId, function(chunk, world)
            PlainsBiome.generate(chunk, world, variantId)
            PlainsBiome.postProcess(chunk, world, variantId)
        end)
    end
    
    return true
end

return PlainsBiome
