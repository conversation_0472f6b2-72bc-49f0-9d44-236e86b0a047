biome
savanna [done]

lava
cloud
void

metal_floor [done]

forest_edge [done]
hills [done]
farmstead [done]

mountain_path [done]
cliff [done]
bridge [done]
bridge start

thin_ice
thin_ice_patch
frozen mud

"frozen_river_bank", -- If forest borders a frozen river
"clearing_snowy" -- Open area within the snowy forest


"cave_entrance", -- Path might lead to a cave
"mountain_peak", -- Might lead to the summit
"lookout_point", -- Scenic spots
"shrine_mountain", -- Religious sites
"mountain_stream_crossing" -- Where path crosses water

sea_cave
"underwater_trench", -- Potential future tile
"underwater_volcano", -- Potential future tile
        
        



done:
ancient_technology [done]
"cave_floor", [done]
 "cliff_ocean" [done]
crystal_formation" [done]
"deep_water", [done]
"dirt_path", [done]
dunes [done]
"grass", [done]
ice [done]
plains [done]
forest_floor [done]
mountain [done]
"mountain_base", [done]
"mud", [done]
oasis, 
river bank [done]
"road", [done]
rocky_ground, 
ruins [done]
sanctuary_stone [done]
"sand", [done]
"shallow_water" [done]
snow [done]
"stone", [done]
"stone_path" [done]
swamp [done]
water [done]

remaining:
1. Special ice tiles:
   - thin_ice
   - thin_ice_patch
   - frozen_mud

2. Snow-related tiles:
   - frozen_river_bank
   - clearing_snowy

3. Mountain features:
   - cave_entrance
   - mountain_peak
   - lookout_point
   - shrine_mountain
   - mountain_stream_crossing

4. Underwater features:
   - sea_cave
   - underwater_trench
   - underwater_volcano

5. Bridge variations:
   - bridge_start