local ForestSpirits = {
    id = "forest_spirits",
    name = "Forest Spirits",
    location = {x = 0, y = 0},
    currentStage = 0,
    stages = {
        -- Stage 1: Disturbance
        {
            name = "Disturbance",
            duration = 60,
            objectives = {
                "Defend against the initial forest assault",
            },
            enemies = {
                {type = "dryad", count = 5},
                {type = "wolf", count = 5}, -- Or "corrupted_wolf"
                {type = "ent", count = 2},
            },
        },
        -- Stage 2: Elemental Fury
        {
            name = "Elemental Fury",
            duration = 90,
            objectives = {
                "Appease the enraged elementals",
            },
            enemies = {
                {type = "dryad", count = 5},
                {type = "fire_elemental", count = 3}, -- New enemy
                {type = "water_elemental", count = 3}, -- New enemy
                {type = "ent", count = 3},
            },
        },
        -- Stage 3: The Ancient Guardian
        {
            name = "The Ancient Guardian",
            duration = 120,
            objectives = {
                "Defeat the Ancient Guardian",
            },
            enemies = {
                {type = "corrupted_dryad", count = 5}, -- New enemy
                {type = "earth_elemental", count = 3}, -- New enemy
                {type = "ancient_guardian", count = 1}, -- New enemy
            },
        },
    },
    timer = 0,
    isActive = false,
    -- Raid-specific data
    corruptionLevel = 0,
}

function ForestSpirits.init(config)
    for k, v in pairs(config or {}) do
        ForestSpirits[k] = v
    end
    ForestSpirits.timer = 0
    ForestSpirits.currentStage = 0
    ForestSpirits.isActive = true
    ForestSpirits.corruptionLevel = 0
end

function ForestSpirits.update(world, dt)
    if not ForestSpirits.isActive then
        return
    end

    ForestSpirits.timer = ForestSpirits.timer + dt
    local currentStage = ForestSpirits.stages[ForestSpirits.currentStage + 1]

    if currentStage then
        ForestSpirits.spawnEnemies(world, currentStage.enemies)

        -- Stage progression logic
        if ForestSpirits.timer >= currentStage.duration then
            ForestSpirits.currentStage = ForestSpirits.currentStage + 1
            ForestSpirits.timer = 0
            print("Stage " .. ForestSpirits.currentStage .. " complete!")
        end

        -- Stage-specific logic
        if ForestSpirits.currentStage == 2 then
            ForestSpirits.updateElementals()
        end
        if ForestSpirits.currentStage == 3 then
            ForestSpirits.updateAncientGuardian()
        end

        ForestSpirits.updateCorruption(dt)
    else
        ForestSpirits.isActive = false
        print("Forest Spirits raid complete!")
    end
end

function ForestSpirits.spawnEnemies(world, enemies)
    for _, enemyGroup in ipairs(enemies) do
        for i = 1, enemyGroup.count do
            local entity = world.entityFactory:create(enemyGroup.type)
            if entity then
                entity.position = {
                    x = ForestSpirits.location.x + math.random(-5, 5),
                    y = ForestSpirits.location.y + math.random(-5, 5),
                }
                world.entitySystem:addEntity(entity)
            end
        end
    end
end

function ForestSpirits.updateElementals()
    -- Logic for elemental abilities or behaviors
    print("The elementals rage with power!")
end

function ForestSpirits.updateAncientGuardian()
    -- Logic for the Ancient Guardian's abilities (e.g., powerful attacks, summoning)
    print("The Ancient Guardian awakens!")
end

function ForestSpirits.updateCorruption(dt)
    -- Example corruption mechanic
    ForestSpirits.corruptionLevel = math.min(100, ForestSpirits.corruptionLevel + dt * 1.5) -- Slower corruption than Undead

    -- Add effects based on corruption level
    if ForestSpirits.corruptionLevel > 75 then
        print("The forest twists and turns with dark energy.")
        -- Example: Buff enemies, add environmental hazards
    end
end

return ForestSpirits