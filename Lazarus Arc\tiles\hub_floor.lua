-- tiles/hub_floor.lua
local HubFloorTile = {
    id = "hub_floor",
    name = "Sanctuary Floor",
    passable = true,
    variants = 4, -- Different floor patterns and appearances
    
    -- Entities that can spawn on the floor
    spawns = {"luminous_dust", "memory_fragment", "echo_crystal", "small_artifact"},
    
    -- Hub-specific properties
    sanctuaryLevel = 1, -- Affected by hub level
    resonance = 0, -- How strongly it reacts to player's memories
    energyFlow = 0.5, -- Energy flow through the sanctuary (0-1)
    
    -- Movement properties
    movementSpeed = 1.2, -- Slightly faster movement on smooth sanctuary floors
    
    -- Interaction function
    interact = function(tile, entity)
        -- Check for memory fragments
        if math.random() < 0.1 + (tile.sanctuaryLevel * 0.05) then
            return {
                success = true,
                message = "You find a small fragment of memory embedded in the floor.",
                effects = {
                    {type = "add_item", item = "memory_fragment", quantity = 1}
                }
            }
        end
        
        -- Check for sanctuary resonance
        if tile.resonance > 0.5 and math.random() < 0.15 then
            return {
                success = true,
                message = "The floor glows softly in response to your presence. You feel a sense of belonging.",
                effects = {
                    {type = "restore_stamina", amount = 5 * tile.sanctuaryLevel}
                }
            }
        end
        
        -- Standard examination
        local floorDescriptions = {
            "Smooth stone tiles form the floor of your sanctuary.",
            "The sanctuary floor seems to pulse gently with inner light.",
            "The stone floor feels warm and welcoming beneath your feet.",
            "Ancient patterns are etched into the sanctuary floor."
        }
        
        local description = floorDescriptions[tile.variant] or floorDescriptions[1]
        
        return {
            success = false,
            message = description
        }
    end,
    
    -- Time effects
    timeEffects = {
        night = function(tile)
            -- Floor glows more at night
            tile.glowIntensity = (tile.glowIntensity or 0.3) * 2
            return nil
        end,
        
        dawn = function(tile)
            -- Reset glow to normal in day
            tile.glowIntensity = tile.baseGlowIntensity or 0.3
            return nil
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {0.8, 0.8, 0.9}, -- Light stone color
        variants = {
            {name = "smooth", pattern = nil},
            {name = "patterned", pattern = "geometric"},
            {name = "ancient", pattern = "runes"},
            {name = "crystalline", pattern = "crystal_veins"}
        },
        glowing = true,
        glow_color = {0.9, 0.9, 1.0},
        glow_intensity = 0.3,
        animation = {
            type = "pulse",
            intensity = 0.1,
            speed = 0.5
        }
    },
    
    -- Audio properties
    footstepSound = "sanctuary_step",
    ambientSound = "sanctuary_hum",
    
    -- Sanctuary properties
    sanctuary = {
        safeZone = true,
        healingRate = 0.1, -- Base healing per minute
        staminaRegen = 0.2, -- Base stamina regen boost
        memoryResonance = 0.3 -- Base memory resonance
    },
    
    -- Hub level effects
    applyHubLevel = function(tile, level)
        tile.sanctuaryLevel = level
        tile.resonance = 0.2 * level
        tile.energyFlow = 0.3 + (level * 0.1)
        
        -- Visual upgrades with levels
        if level >= 3 then
            tile.visual.glow_intensity = 0.3 + (0.1 * level)
            tile.baseGlowIntensity = tile.visual.glow_intensity
        end
        
        -- Sanctuary benefits improve with level
        tile.sanctuary.healingRate = 0.1 * level
        tile.sanctuary.staminaRegen = 0.2 * level
        tile.sanctuary.memoryResonance = 0.3 * level
        
        -- Special effects at higher levels
        if level >= 4 then
            tile.sanctuary.manaRegen = 0.2 * (level - 3)
        end
    end,
    
    -- Connections to other tiles
    connects_to = {
        "hub_floor",
        "hub_path",
        "hub_grass",
        "sanctuary_stone"
    },
    
    -- Effect on entities that walk on this tile
    onWalk = function(tile, entity)
        -- Apply sanctuary effects
        if entity.applyStatus then
            entity.applyStatus("sanctuary_protection", 5)
            
            -- Healing effect
            if entity.stats and entity.stats.health and entity.stats.health < entity.stats.maxHealth then
                entity.stats.health = math.min(entity.stats.health + tile.sanctuary.healingRate, entity.stats.maxHealth)
            end
            
            -- Stamina regeneration
            if entity.stats and entity.stats.stamina and entity.stats.stamina < entity.stats.maxStamina then
                entity.stats.stamina = math.min(entity.stats.stamina + tile.sanctuary.staminaRegen, entity.stats.maxStamina)
            end
            
            -- Mana regeneration if available
            if tile.sanctuary.manaRegen and entity.stats and entity.stats.mana and entity.stats.mana < entity.stats.maxMana then
                entity.stats.mana = math.min(entity.stats.mana + tile.sanctuary.manaRegen, entity.stats.maxMana)
            end
        end
        
        -- Floor lighting effect
        return {
            visual = "floor_glow",
            intensity = tile.glowIntensity or 0.3,
            duration = 1
        }
    end
}

function HubFloorTile.init(world)
    print("Hub floor tile module initialized")
    
    -- Register with sanctuary system
    if world.systems and world.systems.sanctuary then
        world.systems.sanctuary:registerSanctuaryElement("hub_floor", "floor")
    end
end

return HubFloorTile