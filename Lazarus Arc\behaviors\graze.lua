-- behaviors/graze.lua
-- Grazing behavior for herbivore entities

local GrazeBehavior = {}  -- create an empty table

GrazeBehavior.id = "graze"
GrazeBehavior.name = "Graze Behavior"

GrazeBehavior.defaultConfig = {
    moveSpeed = 0.7,         -- Slow movement speed
    grazeTime = {5, 15},     -- Min/max seconds to spend grazing in one spot
    moveTime = {3, 8},       -- Min/max seconds to spend moving between spots
    grazeRadius = 10,        -- How far to roam while grazing
    homeX = nil,             -- Center point of grazing area (nil = current pos)
    homeY = nil,
    foodTypes = {            -- Tile types that can be grazed
        "grass", "tall_grass", "flowers", "bush", "crops"
    },
    foodValue = {            -- How much food value each type provides
        grass = 5,
        tall_grass = 10,
        flowers = 3,
        bush = 15,
        crops = 20
    },
    foodConsumptionRate = 1.0, -- Multiplier for food consumption
    hungerDecayRate = 0.05,  -- How fast hunger increases per second
    fleeWhenThreatened = true, -- Whether to flee when predators are nearby
    regrowthTime = 300,      -- Seconds for grazed tiles to regrow
    groupBehavior = true     -- Whether to follow herd/group
}

function GrazeBehavior.init(entity, customConfig)
    -- Ensure the entity has a position; if not, set a default position
    local pos = entity.position or { x = 0, y = 0 }
    entity.position = pos

    entity.grazeState = entity.grazeState or {
        isGrazing = false,
        grazingTile = nil,
        grazingTimer = 0,
        movingTimer = 0,
        targetPosition = {x = 0, y = 0},
        hunger = 0,          -- 0 = full, 100 = starving
        homePosition = {x = pos.x, y = pos.y},
        lastGrazedTiles = {}, -- Remember tiles we've already grazed
        groupEntities = {},  -- Nearby entities of same type
        config = {}
    }
    
    local config = entity.grazeState.config
    for k, v in pairs(GrazeBehavior.defaultConfig) do
        config[k] = v
    end
    
    if customConfig then
        for k, v in pairs(customConfig) do
            config[k] = v
        end
    end
    
    -- Set home position if provided in config
    if config.homeX and config.homeY then
        entity.grazeState.homePosition.x = config.homeX
        entity.grazeState.homePosition.y = config.homeY
    end
    
    -- Set initial target position
    GrazeBehavior.setNewGrazeTarget(entity)
end

function GrazeBehavior.update(entity, world, dt)
    local state = entity.grazeState
    if not state then
        GrazeBehavior.init(entity)
        state = entity.grazeState
    end
    
    local config = state.config
    
    -- Update hunger
    state.hunger = math.min(100, state.hunger + config.hungerDecayRate * dt)
    
    -- Check for threats if configured to flee
    if config.fleeWhenThreatened and math.random() < 0.05 * dt then
        local threat = GrazeBehavior.checkForThreats(entity, world)
        if threat then
            if entity.fleeState then
                entity.fleeState.currentThreat = threat.id
                entity.fleeState.threatPosition.x = threat.position.x
                entity.fleeState.threatPosition.y = threat.position.y
                entity.fleeState.isPanicking = true
                entity.fleeState.calmDownTimer = 5
                state.isGrazing = false
                state.grazingTimer = 0
                return
            end
        end
    end
    
    -- Update timers and behavior state
    if state.isGrazing then
        state.grazingTimer = state.grazingTimer - dt
        
        if state.grazingTimer <= 0 or state.hunger < 10 then
            state.isGrazing = false
            state.grazingTile = nil
            state.movingTimer = math.random(config.moveTime[1], config.moveTime[2])
            GrazeBehavior.setNewGrazeTarget(entity)
        else
            GrazeBehavior.performGraze(entity, world, dt)
        end
    else
        state.movingTimer = state.movingTimer - dt
        
        local dx = state.targetPosition.x - entity.position.x
        local dy = state.targetPosition.y - entity.position.y
        local distSq = dx*dx + dy*dy
        
        if distSq < 1 or state.movingTimer <= 0 then
            state.isGrazing = true
            state.grazingTimer = math.random(config.grazeTime[1], config.grazeTime[2])
            state.grazingTile = GrazeBehavior.findGrazableTile(entity, world)
            entity.velocity.x = 0
            entity.velocity.y = 0
            if entity.triggerAnimation then
                entity.triggerAnimation("graze")
            end
        else
            local dist = math.sqrt(distSq)
            entity.velocity.x = (dx / dist) * config.moveSpeed
            entity.velocity.y = (dy / dist) * config.moveSpeed
            if entity.triggerAnimation then
                entity.triggerAnimation("walk")
            end
            if config.groupBehavior and math.random() < 0.05 * dt then
                GrazeBehavior.updateGroup(entity, world)
            end
        end
    end
end

function GrazeBehavior.setNewGrazeTarget(entity)
    local state = entity.grazeState
    local config = state.config
    if config.groupBehavior and #state.groupEntities > 0 then
        local avgX, avgY = 0, 0
        local count = 0
        for _, groupEntity in ipairs(state.groupEntities) do
            if groupEntity and groupEntity.position then
                avgX = avgX + groupEntity.position.x
                avgY = avgY + groupEntity.position.y
                count = count + 1
            end
        end
        if count > 0 then
            avgX = avgX / count
            avgY = avgY / count
            local angle = math.random() * 2 * math.pi
            local distance = math.random() * 5 + 2
            state.targetPosition.x = avgX + math.cos(angle) * distance
            state.targetPosition.y = avgY + math.sin(angle) * distance
            return
        end
    end
    local angle = math.random() * 2 * math.pi
    local distance = math.random() * config.grazeRadius
    state.targetPosition.x = state.homePosition.x + math.cos(angle) * distance
    state.targetPosition.y = state.homePosition.y + math.sin(angle) * distance
end

function GrazeBehavior.findGrazableTile(entity, world)
    local state = entity.grazeState
    local config = state.config
    local pos = entity.position
    local currentTile = world.chunkSystem:getTileAt(pos.x, pos.y)
    if currentTile and GrazeBehavior.isTileGrazable(currentTile, config) then
        local tileKey = currentTile.x .. "," .. currentTile.y
        if not state.lastGrazedTiles[tileKey] then
            return currentTile
        end
    end
    for y = -1, 1 do
        for x = -1, 1 do
            if x == 0 and y == 0 then goto continue end
            local checkX = pos.x + x
            local checkY = pos.y + y
            local tile = world.chunkSystem:getTileAt(checkX, checkY)
            if tile and GrazeBehavior.isTileGrazable(tile, config) then
                local tileKey = tile.x .. "," .. tile.y
                if not state.lastGrazedTiles[tileKey] then
                    return tile
                end
            end
            ::continue::
        end
    end
    return nil
end

function GrazeBehavior.isTileGrazable(tile, config)
    for _, foodType in ipairs(config.foodTypes) do
        if tile.type == foodType then
            if tile.growthStage then
                return tile.growthStage >= 0.5
            end
            if tile.height then
                return tile.height > 0.3
            end
            return true
        end
    end
    return false
end

function GrazeBehavior.performGraze(entity, world, dt)
    local state = entity.grazeState
    local config = state.config
    if not state.grazingTile then
        state.grazingTile = GrazeBehavior.findGrazableTile(entity, world)
        if not state.grazingTile then
            state.isGrazing = false
            state.movingTimer = math.random(config.moveTime[1], config.moveTime[2])
            GrazeBehavior.setNewGrazeTarget(entity)
            return
        end
    end
    
    local foodValue = 0
    if config.foodValue[state.grazingTile.type] then
        foodValue = config.foodValue[state.grazingTile.type] * config.foodConsumptionRate * dt
    else
        foodValue = 5 * config.foodConsumptionRate * dt
    end
    
    state.hunger = math.max(0, state.hunger - foodValue)
    
    if state.grazingTile.growthStage then
        state.grazingTile.growthStage = math.max(0, state.grazingTile.growthStage - 0.1 * dt)
    end
    if state.grazingTile.height then
        state.grazingTile.height = math.max(0, state.grazingTile.height - 0.1 * dt)
    end
    if state.grazingTile.variant and state.grazingTile.variant > 1 then
        state.grazingTile.variant = 1
    end
    
    local tileKey = state.grazingTile.x .. "," .. state.grazingTile.y
    state.lastGrazedTiles[tileKey] = {
        time = world.time,
        regrowAt = world.time + config.regrowthTime
    }
    
    for k, v in pairs(state.lastGrazedTiles) do
        if v.regrowAt <= world.time then
            state.lastGrazedTiles[k] = nil
        end
    end
    
    if entity.triggerAnimation and math.random() < 0.1 then
        entity.triggerAnimation("graze_bite")
    end
    
    if entity.stats then
        if entity.stats.health and entity.stats.maxHealth then
            entity.stats.health = math.min(entity.stats.maxHealth, 
                entity.stats.health + foodValue * 0.2)
        end
        if entity.stats.weight then
            entity.stats.weight = entity.stats.weight + foodValue * 0.05
        end
    end
end

function GrazeBehavior.checkForThreats(entity, world)
    local threatRadius = 12
    local nearbyEntities = world.entitySystem:findNearbyEntities(
        entity.position.x, entity.position.y, threatRadius
    )
    local predators = {}
    for _, potentialThreat in ipairs(nearbyEntities) do
        if potentialThreat.id == entity.id then goto continue end
        if potentialThreat.huntState then
            table.insert(predators, potentialThreat)
        end
        if potentialThreat.type == "wolf" or 
           potentialThreat.type == "lion" or
           potentialThreat.type == "bear" or
           potentialThreat.type == "predator" then
            table.insert(predators, potentialThreat)
        end
        ::continue::
    end
    if #predators > 0 then
        table.sort(predators, function(a, b)
            local dxA = a.position.x - entity.position.x
            local dyA = a.position.y - entity.position.y
            local distA = dxA*dxA + dyA*dyA
            
            local dxB = b.position.x - entity.position.x
            local dyB = b.position.y - entity.position.y
            local distB = dxB*dxB + dyB*dyB
            
            return distA < distB
        end)
        return predators[1]
    end
    return nil
end

function GrazeBehavior.updateGroup(entity, world)
    local state = entity.grazeState
    local groupRadius = 15
    local nearbyEntities = world.entitySystem:findNearbyEntities(
        entity.position.x, entity.position.y, groupRadius
    )
    state.groupEntities = {}
    for _, otherEntity in ipairs(nearbyEntities) do
        if otherEntity.id == entity.id then goto continue end
        if otherEntity.type == entity.type then
            table.insert(state.groupEntities, otherEntity)
        end
        ::continue::
    end
    if #state.groupEntities > 0 and math.random() < 0.2 then
        local threat = GrazeBehavior.checkForThreats(entity, world)
        if threat then
            GrazeBehavior.alertGroup(entity, threat, world)
        end
    end
end

function GrazeBehavior.alertGroup(entity, threat, world)
    local state = entity.grazeState
    for _, groupEntity in ipairs(state.groupEntities) do
        if groupEntity.fleeState then
            groupEntity.fleeState.currentThreat = threat.id
            groupEntity.fleeState.threatPosition.x = threat.position.x
            groupEntity.fleeState.threatPosition.y = threat.position.y
            groupEntity.fleeState.isPanicking = true
            groupEntity.fleeState.calmDownTimer = 5
            if groupEntity.grazeState then
                groupEntity.grazeState.isGrazing = false
                groupEntity.grazeState.grazingTimer = 0
            end
        end
    end
end

function GrazeBehavior.setHomePosition(entity, x, y)
    local state = entity.grazeState
    state.homePosition.x = x
    state.homePosition.y = y
    state.lastGrazedTiles = {}
end

return GrazeBehavior
