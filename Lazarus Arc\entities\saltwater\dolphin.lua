local Dolphin = {
    id = "dolphin",
    name = "Bottlenose Dolphin",
    type = "fish",
    
    -- Entity categories
    categories = {"animal", "fish", "saltwater", "predator", "social", "mammal"},
    
    -- Threat and food categories
    threatCategories = {"player", "shark", "whale"},
    foodCategories = {"fish", "squid", "crustacean", "small_prey"},
    
    -- Stats
    maxHealth = 50,
    health = 50,
    maxStamina = 85,
    stamina = 85,
    speed = 2.5,
    
    -- Water properties
    water = {
        swimSpeed = 2.5,
        maxDepth = 12,
        preferredDepth = 3,
        oxygenLevel = 1.0,
        temperature = "warm",
        currentSpeed = 1.2
    },
    
    -- Behaviors
    behaviors = {"hunt", "patrol", "social", "play"},
    
    -- Behavior configurations
    behaviorConfigs = {
        hunt = {
            moveSpeed = 3.0,
            searchRadius = 20,
            attackRange = 2.0,
            damage = 25,
            preferredPrey = {"fish", "squid", "crustacean"},
            successChance = 0.8,
            echolocationBonus = 0.15
        },
        patrol = {
            moveSpeed = 2.0,
            patrolRadius = 25,
            preferredDepth = 3,
            restInterval = {15, 20}
        },
        social = {
            moveSpeed = 2.2,
            followDistance = 2.5,
            separationDistance = 2.0,
            alignmentStrength = 0.7,
            cohesionStrength = 0.8,
            maxGroupSize = 12
        },
        play = {
            moveSpeed = 2.8,
            duration = {10, 20},
            successChance = 0.9,
            preferredDepth = 2
        }
    },
    
    -- Special abilities
    abilities = {
        echolocate = {
            range = 30,
            duration = 3,
            cooldown = 5,
            effect = "detect",
            staminaCost = 8
        },
        leap = {
            speed = 3.5,
            duration = 0.6,
            cooldown = 2,
            staminaCost = 10
        },
        sonar = {
            range = 25,
            duration = 2,
            cooldown = 3,
            effect = "stun",
            staminaCost = 5
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "dolphin",
        scale = 1.2,
        animations = {
            "idle", "swim", "leap", "social", "play"
        },
        variants = {
            "gray", "spotted", "striped", "dark"
        }
    },
    
    -- Sound effects
    sounds = {
        splash = "fish_splash",
        swim = "dolphin_swim",
        echolocate = "dolphin_echolocate",
        leap = "dolphin_leap"
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.7, quantity = {1, 2}},
        {id = "blubber", chance = 0.6, quantity = {1, 2}},
        {id = "fin", chance = 0.3, quantity = {1, 1}},
        {id = "bone", chance = 0.4, quantity = {1, 1}}
    }
}

-- Initialize the entity
function Dolphin.init(entity, world)
    -- Copy all fields from Dolphin template to entity instance
    for k, v in pairs(Dolphin) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random dolphin variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Dolphin.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update water properties
    if entity.water then
        -- Adjust hunting success based on time of day
        if world and world.timeOfDay == "dawn" or world.timeOfDay == "dusk" then
            entity.behaviorConfigs.hunt.successChance = 0.85
        else
            entity.behaviorConfigs.hunt.successChance = 0.8
        end

        -- Adjust preferred depth based on time of day
        if world and world.timeOfDay == "night" then
            entity.water.preferredDepth = 2
        else
            entity.water.preferredDepth = 3
        end

        -- Apply echolocation bonus when near surface
        if entity.position.y < 4 then
            entity.behaviorConfigs.hunt.successChance = 
                entity.behaviorConfigs.hunt.successChance + 
                entity.behaviorConfigs.hunt.echolocationBonus
        end

        -- Check for nearby dolphins for social behavior
        if world and world.entities then
            local nearbyDolphins = 0
            for _, other in ipairs(world.entities) do
                if other.type == "fish" and other.id == "dolphin" then
                    local distance = math.sqrt(
                        (other.position.x - entity.position.x)^2 + 
                        (other.position.y - entity.position.y)^2
                    )
                    if distance < 6 then
                        nearbyDolphins = nearbyDolphins + 1
                    end
                end
            end
            if nearbyDolphins > 0 then
                entity.behaviorConfigs.play.successChance = 0.9
            end
        end
    end
end

return Dolphin 