-- entity_manager.lua
-- Entity lifecycle manager for Lazarus Arc
-- Handles entity registration, updates, and queries

local UUID = require("utils.uuid")
local EntityManager = {}
EntityManager.__index = EntityManager

-- Create a new EntityManager instance
function EntityManager.new()
    local self = setmetatable({}, EntityManager)
    self.entities = {}   -- Stores entities by their UUID
    return self
end

-- Register a new entity.
-- If the entity does not have a UUID, one is automatically assigned.
-- Returns the entity's UUID.
function EntityManager:register(entity)
    if not entity then
        error("Cannot register a nil entity")
    end
    if not entity.uuid then
        entity.uuid = UUID.generate()
    end
    self.entities[entity.uuid] = entity
    return entity.uuid
end

-- Unregister (remove) an entity from the manager.
-- Returns true if the entity was successfully removed.
-- Can accept either an entity object or an entity ID/UUID.
function EntityManager:unregister(entity)
    -- Handle case where entity is an ID number
    if type(entity) == "number" then
        -- Try to find the entity with this ID
        for uuid, e in pairs(self.entities) do
            if e.id == entity then
                self.entities[uuid] = nil
                return true
            end
        end
        return false
    end

    -- Handle case where entity is a UUID string
    if type(entity) == "string" and self.entities[entity] then
        self.entities[entity] = nil
        return true
    end

    -- Handle case where entity is an object with uuid
    if entity and entity.uuid and self.entities[entity.uuid] then
        self.entities[entity.uuid] = nil
        return true
    end

    return false
end

-- Update all registered entities.
-- If an entity has an update(dt) method, it will be called.
function EntityManager:update(dt)
    for _, entity in pairs(self.entities) do
        if entity.update and type(entity.update) == "function" then
            entity:update(dt)
        end
    end
end

-- Query entities using a custom filter function.
-- The filter function should return true for entities that match the criteria.
function EntityManager:query(filterFn)
    local result = {}
    for _, entity in pairs(self.entities) do
        if filterFn(entity) then
            table.insert(result, entity)
        end
    end
    return result
end

-- Convenience method: get entities by type
function EntityManager:getEntitiesByType(typeName)
    return self:query(function(entity)
        return entity.type == typeName
    end)
end

-- Convenience method: get entities by category
-- Assumes each entity has a 'categories' table.
function EntityManager:getEntitiesByCategory(category)
    return self:query(function(entity)
        if entity.categories then
            for _, cat in ipairs(entity.categories) do
                if cat == category then
                    return true
                end
            end
        end
        return false
    end)
end

-- Get an entity by its UUID
function EntityManager:getEntityByUUID(uuid)
    return self.entities[uuid]
end

-- Get all entities
function EntityManager:getAll()
    local allEntities = {}
    for uuid, entity in pairs(self.entities) do
        table.insert(allEntities, entity)
    end
    return allEntities
end

-- Add a new entity by loading its definition and creating an instance
-- This is the main function used by the debug menu and other systems to spawn entities
function EntityManager:addEntity(entityType, x, y, properties)
    if not entityType then
        error("Cannot add entity without type")
    end

    -- Try to load the entity definition
    local success, entityDef = pcall(require, "entities." .. entityType)
    if not success then
        print("Warning: Could not load entity definition for: " .. entityType)
        print("Error: " .. tostring(entityDef))
        return nil
    end

    -- Create basic entity instance
    local entity = {
        uuid = UUID.generate(),
        type = entityType,
        position = {x = x or 0, y = y or 0},
        properties = properties or {}
    }

    -- Initialize the entity using its definition's init function
    if entityDef and entityDef.init and type(entityDef.init) == "function" then
        -- Pass the entity and a world reference (if available)
        local world = nil
        -- Try to get world reference from Engine if available
        if _G.Engine and _G.Engine.currentWorld then
            world = _G.Engine.currentWorld
        end

        entity = entityDef.init(entity, world) or entity
    elseif entityDef then
        -- If no init function, copy properties directly from definition
        for k, v in pairs(entityDef) do
            if type(v) ~= "function" and entity[k] == nil then
                if type(v) == "table" then
                    -- Deep copy for tables
                    entity[k] = {}
                    for subk, subv in pairs(v) do
                        entity[k][subk] = subv
                    end
                else
                    entity[k] = v
                end
            end
        end
    end

    -- Register the entity
    self:register(entity)

    print("Spawned entity: " .. entityType .. " at (" .. (x or 0) .. ", " .. (y or 0) .. ")")
    return entity
end

return EntityManager
