-- world_core.lua (Updated for lunajson)
-- Core world system for Lazarus Arc
local json = require("lunajson")
local ChunkSystem = nil
-- Load ChunkSystem safely
local chunkSystemSuccess, chunkSystemModule = pcall(require, "chunk_system")
if chunkSystemSuccess then
    ChunkSystem = chunkSystemModule
else
    print("Warning: Could not load chunk_system module: " .. tostring(chunkSystemModule))
    -- Create placeholder ChunkSystem
    ChunkSystem = {
        new = function(chunkSize)
            return {
                chunks = {},
                chunkSize = chunkSize or 16,
                init = function(self, world)
                    print("Placeholder ChunkSystem initialized with chunk size: " .. self.chunkSize)
                    return self
                end,
                setBiomeAt = function(self, x, y, biomeName)
                    print("Placeholder: Setting biome at " .. x .. "," .. y .. " to " .. biomeName)
                end,
                getTileAt = function(self, x, y)
                    return { type = "placeholder" }
                end,
                getChunkAt = function(self, x, y)
                    local chunkId = x .. "," .. y
                    if not self.chunks[chunkId] then
                        self.chunks[chunkId] = {
                            x = x,
                            y = y,
                            tiles = {}
                        }
                    end
                    return self.chunks[chunkId]
                end,
                update = function(self, dt) end,
                saveAllChunks = function(self)
                    print("Placeholder: Saving all chunks")
                end,
                loadAllChunks = function(self)
                    print("Placeholder: Loading all chunks")
                end
            }
        end
    }
end

local UUID = require("utils.uuid")

local WorldCore = {
    initialized = false,
    modules = {},
    activeWorlds = {},
    defaultChunkSize = 16
}

-- Initialize the world core system
function WorldCore.init()
    WorldCore.modules = { tiles = {}, biomes = {}, entities = {}, behaviors = {}, structures = {}, weather = {} }
    WorldCore.loadModules()
    WorldCore.initialized = true
    return WorldCore
end

-- Load modules
function WorldCore.loadModules()
    local categories = {"tiles", "biomes", "entities", "behaviors", "structures", "weather"}
    for _, category in ipairs(categories) do
        WorldCore.loadModuleCategory(category)
    end
end

function WorldCore.loadModuleCategory(category)
    local files = love.filesystem.getDirectoryItems(category)
    if not files then return 0 end
    local count = 0
    for _, file in ipairs(files) do
        if file:match("%.lua$") then
            local moduleName = file:gsub("%.lua$", "")
            if WorldCore.loadModule(category, moduleName) then
                count = count + 1
            end
        end
    end
    return count
end

-- Load a module file
function WorldCore.loadModule(moduleType, moduleName)
    local success, module = pcall(require, moduleType .. "." .. moduleName)

    if success then
        -- Register the module
        WorldCore.modules[moduleType] = WorldCore.modules[moduleType] or {}
        WorldCore.modules[moduleType][moduleName] = module
        print("Loaded " .. moduleType .. " module: " .. moduleName)

        -- Call init function if it exists
        if module.init then
            module.init(WorldCore)
        end

        return module
    else
        print("Failed to load module: " .. moduleType .. "." .. moduleName)
        print(module) -- Print error message
        return nil
    end
end

-- Load all modules in a directory
function WorldCore.loadAllModules(moduleType, directory)
    local files = love.filesystem.getDirectoryItems(directory)
    if not files then return 0 end

    local count = 0
    for _, file in ipairs(files) do
        if file:match("%.lua$") then
            local moduleName = file:gsub("%.lua$", "")
            if WorldCore.loadModule(moduleType, moduleName) then
                count = count + 1
            end
        end
    end
    return count
end

-- Create a new world instance
function WorldCore.createWorld(seedOrOptions, optionalOptions)
    local options = {}

    -- Handle different calling conventions
    if optionalOptions then
        options = optionalOptions
        options.seed = seedOrOptions
    else
        if type(seedOrOptions) == "table" then
            options = seedOrOptions
        elseif type(seedOrOptions) == "number" then
            options = { seed = seedOrOptions }
        end
    end

    options = options or {}

    -- Try to load entity_manager module safely
    local entitySystem = nil
    if options.entitySystem then
        entitySystem = options.entitySystem
    else
        local entityManagerSuccess, entityManager = pcall(require, "utils.entity_manager")
        if entityManagerSuccess and entityManager and type(entityManager.new) == "function" then
            entitySystem = entityManager.new()
        else
            print("Warning: Could not load entity_manager module or it doesn't have a new() function")
            -- Create a simple placeholder entity system if placeholders are enabled
            if Settings and Settings.debug and Settings.debug.modules and Settings.debug.modules.placeholder_entities then
                print("Creating placeholder entity system (placeholder_entities=true)")
                entitySystem = {
                    entities = {},
                    createPlayer = function(self, playerData)
                        local player = {
                            id = playerData.id or UUID.generate(),
                            type = "player",
                            name = playerData.name or "Player",
                            position = playerData.position or {x = 0, y = 0}
                        }
                        self.entities[player.id] = player
                        return player
                    end,
                    register = function(self, entity)
                        if not entity.id then entity.id = UUID.generate() end
                        self.entities[entity.id] = entity
                        return entity
                    end,
                    update = function(self, dt)
                        -- Empty placeholder update function
                    end
                }
            else
                print("Placeholder entity system disabled (placeholder_entities=false)")
                entitySystem = nil
            end
        end
    end

    -- Create ChunkSystem safely
    local chunkSystem = nil
    if options.chunkSystem then
        chunkSystem = options.chunkSystem
    elseif ChunkSystem and type(ChunkSystem.new) == "function" then
        chunkSystem = ChunkSystem.new({ chunkSize = options.chunkSize or WorldCore.defaultChunkSize })
    else
        print("Warning: ChunkSystem.new not available")
        -- Create a placeholder chunk system if placeholders are enabled
        if Settings and Settings.debug and Settings.debug.modules and Settings.debug.modules.placeholder_chunks then
            print("Creating placeholder chunk system (placeholder_chunks=true)")
            chunkSystem = {
                chunks = {},
                chunkSize = options.chunkSize or WorldCore.defaultChunkSize,
                init = function(self, world)
                    print("Placeholder ChunkSystem initialized with chunk size: " .. self.chunkSize)
                    return self
                end,
                setBiomeAt = function(self, x, y, biomeName)
                    print("Placeholder: Setting biome at " .. x .. "," .. y .. " to " .. biomeName)
                end,
                getTileAt = function(self, x, y)
                    return { type = "placeholder" }
                end,
                getChunkAt = function(self, x, y)
                    local chunkId = x .. "," .. y
                    if not self.chunks[chunkId] then
                        self.chunks[chunkId] = {
                            x = x,
                            y = y,
                            tiles = {}
                        }
                    end
                    return self.chunks[chunkId]
                end,
                update = function(self, dt) end,
                saveAllChunks = function(self)
                    print("Placeholder: Saving all chunks")
                end,
                loadAllChunks = function(self)
                    print("Placeholder: Loading all chunks")
                end
            }
        else
            print("Placeholder chunk system disabled (placeholder_chunks=false)")
            chunkSystem = nil
        end
    end

    local world = {
        id = UUID.generate(),
        name = options.name or "New World",
        seed = options.seed or math.random(1, 1000000),
        time = 0,
        timeOfDay = 0,
        dayLength = options.dayLength or 1200,
        chunkSystem = chunkSystem,
        entitySystem = entitySystem,
        weatherSystem = nil,
        spawnPoint = options.spawnPoint or { x = 0, y = 0 },
        chunkSize = options.chunkSize or WorldCore.defaultChunkSize,
        initialized = false,
        isHubWorld = options.isHubWorld or false,
        hubOwner = options.hubOwner,
        hubSeed = options.hubSeed,
        --[[
            Player-World Connection System
            -----------------------------

            The world needs to keep track of the player entity for several reasons:
            1. To update the player's position in the world
            2. To handle collisions between the player and other entities
            3. To ensure the camera follows the player correctly
            4. To provide a central reference point for other systems

            For character movement to work properly:
            - The player entity must be registered with the world's entity system
            - The player must be set in the world using setPlayer
            - Other systems must be able to retrieve the player using getPlayer

            This creates a "single source of truth" for the player entity,
            preventing issues with duplicate player references.
        ]]
        player = nil,  -- Reference to the player entity

        -- Set the player for this world
        -- This is a critical function for the character movement system
        setPlayer = function(self, player)
            -- Store the player reference
            self.player = player

            -- Use debug console for clean logging
            local DebugConsole = require("utils.debug_console")
            DebugConsole.logPlayerRegistration(true, player.id or "Unknown")

            -- Ensure the player is registered with the entity system
            if self.entitySystem and player.id then
                local success = self.entitySystem:register(player)
                DebugConsole.logPlayerRegistration(success, player.id)
            end

            return true
        end,

        -- Get the player for this world
        -- Used by other systems to access the player entity
        getPlayer = function(self)
            return self.player
        end
    }

    -- Set up weather system with fallback
    if options.weatherSystem then
        world.weatherSystem = options.weatherSystem
    elseif type(Engine) == "table" and Engine.systems and Engine.systems.weatherSystem then
        world.weatherSystem = Engine.systems.weatherSystem
    else
        print("Warning: Weather system not available")
        -- Create a placeholder weather system if placeholders are enabled
        if Settings and Settings.debug and Settings.debug.modules and Settings.debug.modules.weather then
            print("Creating placeholder weather system (weather=true)")
            world.weatherSystem = {
                init = function(world, options)
                    print("Placeholder weather system initialized")
                    return true
                end,
                update = function(dt) end,
                setWeather = function(weatherType, duration)
                    print("Placeholder: Setting weather to " .. tostring(weatherType) .. " for " .. tostring(duration) .. " seconds")
                end,
                getCurrentWeather = function()
                    return "placeholder"
                end
            }
        else
            print("Placeholder weather system disabled (weather=false)")
            world.weatherSystem = nil
        end
    end

    -- Initialize weather system safely
    if world.weatherSystem and type(world.weatherSystem.init) == "function" then
        world.weatherSystem.init(world, {
            updateInterval = options.weatherUpdateInterval or 1,
            transitionDuration = options.weatherTransitionDuration or 10,
            debugMode = options.weatherDebugMode or false,
            season = options.season or "summer"
        })
    end

    -- Initialize chunk system
    if world.chunkSystem and type(world.chunkSystem.init) == "function" then
        world.chunkSystem:init(world)
    end

    -- If this is a hub world, generate it with the player's specific seed
    if world.isHubWorld and world.hubOwner and world.hubSeed then
        print("⭐ INITIALIZING HUB WORLD ⭐")
        print("  Owner: " .. world.hubOwner)
        print("  Seed: " .. world.hubSeed)

        -- Ensure the hub biome is loaded (manual load if needed)
        local hubBiomeLoaded = false
        if ChunkSystem and ChunkSystem.biomeModules and ChunkSystem.biomeModules["hub"] then
            hubBiomeLoaded = true
        elseif world.chunkSystem and world.chunkSystem.biomeModules and world.chunkSystem.biomeModules["hub"] then
            hubBiomeLoaded = true
        else
            print("  Attempting to load hub biome manually...")
            local success, hubModule = pcall(require, "biomes.hub")
            if success and hubModule then
                print("  Hub biome loaded successfully")
                -- If needed, add directly to chunkSystem
                if world.chunkSystem then
                    world.chunkSystem.biomeModules = world.chunkSystem.biomeModules or {}
                    world.chunkSystem.biomeModules["hub"] = hubModule
                    hubBiomeLoaded = true
                end
            else
                print("  WARNING: Failed to load hub biome: " .. tostring(hubModule))
            end
        end

        -- Set initial biome to hub
        if world.chunkSystem and type(world.chunkSystem.setBiomeAt) == "function" then
            print("  Setting biome at (0,0) to 'hub'")
            world.chunkSystem:setBiomeAt(0, 0, "hub")

            -- Create initial hub chunk with player's specific seed
            local hubChunk = world.chunkSystem:getChunkAt(0, 0)
            if hubChunk then
                hubChunk.biome = "hub"
                hubChunk.isHub = true
                hubChunk.hubOwner = world.hubOwner
                hubChunk.hubSeed = world.hubSeed
                print("  Created hub chunk successfully")

                -- Use the player's hub seed for generation
                local oldSeed = math.randomseed
                math.randomseed(world.hubSeed)
                print("  Using player's specific hub seed: " .. world.hubSeed)

                -- Initialize hub chunk with basic tiles - Fixed array initialization
                hubChunk.tiles = hubChunk.tiles or {}
                local tileTypes = {"hub_floor", "hub_grass", "hub_path"}

                -- Fill the center with hub_floor (make a clear floor area)
                local chunkSize = world.chunkSize or 8
                for x = 0, chunkSize - 1 do
                    if not hubChunk.tiles[x] then
                        hubChunk.tiles[x] = {}
                    end

                    for y = 0, chunkSize - 1 do
                        local distFromCenter = math.sqrt((x - chunkSize/2)^2 + (y - chunkSize/2)^2)
                        local tileType = "hub_floor"

                        -- Create a central area of hub_floor with a ring of paths around it
                        if distFromCenter > chunkSize/3 and distFromCenter < chunkSize/2.5 then
                            tileType = "hub_path"
                        elseif distFromCenter >= chunkSize/2.5 then
                            tileType = "hub_grass"
                        end

                        -- Calculate height based on distance from center
                        local centerX = chunkSize / 2
                        local centerY = chunkSize / 2
                        local distFromCenter = math.sqrt((x - centerX)^2 + (y - centerY)^2)

                        -- Hub has a raised center platform that gradually slopes down
                        local height = 0
                        if distFromCenter < chunkSize / 4 then
                            -- Center platform is raised
                            height = 1.0
                        elseif distFromCenter < chunkSize / 3 then
                            -- Gradual slope down from center
                            height = 1.0 - ((distFromCenter - chunkSize / 4) / (chunkSize / 12))
                        end

                        local tile = {
                            type = tileType,
                            x = x,
                            y = y,
                            biome = "hub",
                            variant = math.random(1, 3),
                            isSafe = true,
                            noHostileMobs = true,
                            height = height
                        }

                        hubChunk.tiles[x][y] = tile
                    end
                end

                -- Generate surrounding chunks with hub grass and paths
                for dx = -1, 1 do
                    for dy = -1, 1 do
                        if dx ~= 0 or dy ~= 0 then -- Skip the center chunk we already made
                            local surroundChunk = world.chunkSystem:getChunkAt(dx, dy)
                            if surroundChunk then
                                -- Mark as hub biome
                                surroundChunk.biome = "hub"

                                -- Fill with mostly grass, some paths connecting to center
                                for x = 0, chunkSize - 1 do
                                    if not surroundChunk.tiles[x] then
                                        surroundChunk.tiles[x] = {}
                                    end

                                    for y = 0, chunkSize - 1 do
                                        local tileType = "hub_grass"

                                        -- Create paths connecting to the central chunk
                                        if (dx == 0 and x % 8 == 0) or (dy == 0 and y % 8 == 0) then
                                            if ((dx == -1 and x > chunkSize / 2) or
                                                (dx == 1 and x < chunkSize / 2) or
                                                (dy == -1 and y > chunkSize / 2) or
                                                (dy == 1 and y < chunkSize / 2)) then
                                                tileType = "hub_path"
                                            end
                                        end

                                        -- Calculate height for surrounding chunks
                                        -- Outer areas are generally lower than the center
                                        local height = 0

                                        -- Create some terrain variation in the surrounding chunks
                                        local worldX = dx * chunkSize + x
                                        local worldY = dy * chunkSize + y
                                        local noise = love.math.noise(worldX * 0.1, worldY * 0.1)

                                        -- Add some small hills in the surrounding area
                                        if noise > 0.7 then
                                            height = (noise - 0.7) * 2.0
                                        end

                                        local tile = {
                                            type = tileType,
                                            x = x,
                                            y = y,
                                            biome = "hub",
                                            variant = math.random(1, 3),
                                            isSafe = true,
                                            noHostileMobs = true,
                                            height = height
                                        }

                                        surroundChunk.tiles[x][y] = tile
                                    end
                                end
                            end
                        end
                    end
                end

                -- Restore original random seed
                math.randomseed = oldSeed
                print("  Hub world initialization COMPLETE!")
            else
                print("  ERROR: Failed to create hub chunk!")
            end
        else
            print("  ERROR: ChunkSystem not available or setBiomeAt function missing!")

            -- Create a placeholder hub chunk if placeholders are enabled
            if Settings and Settings.debug and Settings.debug.modules and Settings.debug.modules.placeholder_biomes then
                print("  Creating placeholder hub biome (placeholder_biomes=true)")

                -- Create a basic table to act as a hub chunk
                local hubChunk = {
                    x = 0,
                    y = 0,
                    biome = "hub",
                    isHub = true,
                    hubOwner = world.hubOwner,
                    hubSeed = world.hubSeed,
                    tiles = {}
                }

                -- Add basic tiles
                for x = 0, (world.chunkSize or 32) - 1 do
                    for y = 0, (world.chunkSize or 32) - 1 do
                        local tileIndex = x + y * (world.chunkSize or 32)
                        hubChunk.tiles[tileIndex] = {
                            type = "hub_floor",
                            x = x,
                            y = y,
                            variant = 1,
                            isSafe = true,
                            noHostileMobs = true
                        }
                    end
                end

                -- Add the chunk to the world manually
                if not world.chunks then world.chunks = {} end
                world.chunks["0,0"] = hubChunk

                print("  Created placeholder hub chunk")
            else
                print("  Placeholder hub biome disabled (placeholder_biomes=false)")
            end
        end

        print("Hub biome initialized for " .. world.hubOwner)
    end

    -- Add to active worlds
    WorldCore.activeWorlds[world.id] = world

    world.initialized = true
    return world
end

-- Create a player hub world
function WorldCore.createPlayerHub(worldUUID, playerUUID, hubSeed)
    print("🌟 CREATING PLAYER HUB WORLD 🌟")
    print("  Parent world: " .. tostring(worldUUID))
    print("  Player UUID: " .. tostring(playerUUID))

    local parentWorld = WorldCore.activeWorlds[worldUUID]
    if not parentWorld then
        print("  ERROR: Cannot create hub - parent world not found: " .. tostring(worldUUID))
        return nil
    end

    -- Get or create player's hub seed
    local player = parentWorld.entitySystem:getEntity(playerUUID)
    if not player then
        print("  ERROR: Player not found: " .. tostring(playerUUID))
        return nil
    end

    print("  Player found: " .. tostring(player.name))

    -- If player already has a hub, return it
    if player.hubWorldId then
        local existingHub = WorldCore.activeWorlds[player.hubWorldId]
        if existingHub then
            print("  Player already has a hub world (ID: " .. player.hubWorldId .. "), returning it")
            return existingHub
        end
        print("  Hub world ID found but world not active, creating new hub")
    end

    -- Generate a unique seed for this player's hub if not provided
    local playerHubSeed = hubSeed or (playerUUID:gsub("[^%d]", "") .. os.time())
    print("  Generated hub seed: " .. tostring(playerHubSeed))

    -- Create a new hub world for this player
    print("  Creating new hub world...")
    local hubWorld = WorldCore.createWorld({
        name = player.name .. "'s Sanctuary",
        chunkSize = parentWorld.chunkSize,
        dayLength = parentWorld.dayLength,
        isHubWorld = true,
        hubOwner = playerUUID,
        hubSeed = playerHubSeed
    })

    if not hubWorld then
        print("  ERROR: Failed to create hub world!")
        return nil
    end

    print("  Hub world created with ID: " .. hubWorld.id)

    -- Store the hub world ID in the player's data
    player.hubWorldId = hubWorld.id
    player.hubSeed = playerHubSeed
    print("  Updated player data with hub information")

    -- Initialize the hub with the player's specific seed
    print("  Loading hub biome...")
    local HubBiome = require("biomes/hub")
    if HubBiome and HubBiome.generateHub then
        local hubChunk = hubWorld.chunkSystem:getChunkAt(0, 0)
        if hubChunk then
            print("  Generating hub using HubBiome.generateHub...")
            HubBiome.generateHub(hubChunk, hubWorld, player)
            print("  Hub generation complete!")
        else
            print("  ERROR: Could not get hub chunk at (0,0)")
        end
    else
        print("  WARNING: HubBiome module or generateHub function not found")
    end

    -- Save the player's hub data
    if parentWorld.savePlayerData then
        parentWorld:savePlayerData(playerUUID)
        print("  Saved player's hub data")
    else
        print("  WARNING: savePlayerData function not available")
    end

    print("  Hub world creation COMPLETE for player: " .. player.name)
    return hubWorld
end

-- Add a player to a world
function WorldCore.addPlayer(world, playerData)
    if not world or not playerData then
        print("Error: Invalid world or player data")
        return false
    end

    -- Create player entity in the world
    local playerEntity = world.entitySystem:createPlayer(playerData)

    -- Set up player-specific systems
    world.players = world.players or {}
    world.players[playerData.uuid] = playerEntity

    return playerEntity
end

-- Save a world to disk
function WorldCore.saveWorld(worldUUID)
    local world = WorldCore.activeWorlds[worldUUID]
    if not world then
        print("Error: World not found: " .. worldUUID)
        return false
    end

    -- Save world data
    local worldData = {
        uuid = world.uuid,
        seed = world.seed,
        name = world.name,
        created = world.created,
        lastPlayed = world.lastPlayed,
        chunkSize = world.chunkSize,
        worldType = world.worldType,
        width = world.width,
        height = world.height,
        spawnPoint = world.spawnPoint,
        timeOfDay = world.timeOfDay,
        dayLength = world.dayLength,
        dayCount = world.dayCount,
        biomeMap = world.biomeMap,
        structures = world.structures,
        worldFlags = world.worldFlags
    }

    -- Save to file
    local filename = "worlds/" .. worldUUID .. ".json"
    love.filesystem.write(filename, json.encode(worldData))

    -- Save chunks
    world.chunkSystem:saveAllChunks()

    return true
end

-- Load a world from disk
function WorldCore.loadWorld(worldUUID)
    local filename = "worlds/" .. worldUUID .. ".json"
    local worldData = love.filesystem.read(filename)
    if not worldData then
        print("Error: World file not found: " .. filename)
        return nil
    end

    -- Parse world data
    worldData = json.decode(worldData)

    -- Create world instance
    local world = WorldCore.createWorld(worldData.seed, {
        uuid = worldData.uuid,
        name = worldData.name,
        chunkSize = worldData.chunkSize,
        worldType = worldData.worldType,
        width = worldData.width,
        height = worldData.height,
        spawnPoint = worldData.spawnPoint,
        dayLength = worldData.dayLength
    })

    -- Restore world state
    world.created = worldData.created
    world.lastPlayed = worldData.lastPlayed
    world.timeOfDay = worldData.timeOfDay
    world.dayCount = worldData.dayCount
    world.biomeMap = worldData.biomeMap
    world.structures = worldData.structures
    world.worldFlags = worldData.worldFlags

    -- Load chunks
    world.chunkSystem:loadAllChunks()

    return world
end

function WorldCore.updateWorld(world, dt)
    if not world then return end

    -- Handle timeOfDay calculation safely
    if world.timeOfDay ~= nil and world.dayLength and world.dayLength > 0 then
        world.timeOfDay = (world.timeOfDay + dt / world.dayLength) % 1
    else
        -- Initialize timeOfDay if it's nil
        world.timeOfDay = world.timeOfDay or 0
    end

    -- Safely update systems
    if world.chunkSystem and type(world.chunkSystem.update) == "function" then
        world.chunkSystem:update(dt)
    elseif world.chunkSystem and world.chunkSystem.update then
        world.chunkSystem.update(dt)
    end

    if world.entitySystem and type(world.entitySystem.update) == "function" then
        world.entitySystem:update(dt)
    end

    if world.weatherSystem and type(world.weatherSystem.update) == "function" then
        world.weatherSystem:update(dt)
    elseif world.weatherSystem and world.weatherSystem.update then
        world.weatherSystem.update(dt)
    end
end

function WorldCore.generateInitialChunks(world)
    if not world or not world.chunkSystem then return end

    -- Check for spawn point, create one if missing
    if not world.spawnPoint then
        world.spawnPoint = { x = 0, y = 0 }
        print("Warning: World missing spawnPoint, defaulting to (0,0)")
    end

    -- Check for chunk size, default to 16 if missing
    local chunkSize = world.chunkSize or WorldCore.defaultChunkSize or 16

    local spawnChunkX = math.floor(world.spawnPoint.x / chunkSize)
    local spawnChunkY = math.floor(world.spawnPoint.y / chunkSize)
    local radius = 2

    -- Generate chunks around spawn point
    for x = -radius, radius do
        for y = -radius, radius do
            -- Check if getChunkAt is a function
            if type(world.chunkSystem.getChunkAt) == "function" then
                world.chunkSystem:getChunkAt(spawnChunkX + x, spawnChunkY + y)
            else
                -- Fallback if getChunkAt is not a method
                print("Warning: world.chunkSystem.getChunkAt is not a function")
                if world.chunkSystem.getChunkAt then
                    world.chunkSystem.getChunkAt(spawnChunkX + x, spawnChunkY + y)
                end
            end
        end
    end
end

-- Generate a random world beyond the hub
function WorldCore.generateRandomWorld(seedOrOptions, optionalOptions)
    local options = {}

    -- Handle different calling conventions
    if optionalOptions then
        options = optionalOptions
        options.seed = seedOrOptions
    else
        if type(seedOrOptions) == "table" then
            options = seedOrOptions
        elseif type(seedOrOptions) == "number" then
            options = { seed = seedOrOptions }
        end
    end

    options = options or {}

    -- Create a new world with random seed if not provided
    local world = WorldCore.createWorld(options.seed or math.random(1, 1000000), {
        name = options.name or "Random World",
        chunkSize = options.chunkSize or WorldCore.defaultChunkSize,
        dayLength = options.dayLength or 1200,
        isHubWorld = false -- Mark as non-hub world
    })

    -- Set up random biome generation
    if world.chunkSystem then
        -- Define possible biomes (excluding hub)
        local possibleBiomes = {
            "forest", "plains", "desert", "ocean", "mountain",
            "swamp", "river", "kingdom_region", "settled_lands",
            "sky_islands", "crystal_cavern"
        }

        -- Generate random biomes for chunks beyond the hub
        local function generateRandomBiome()
            return possibleBiomes[math.random(#possibleBiomes)]
        end

        -- Set up chunk generation callback
        world.chunkSystem.onChunkGenerate = function(chunk, x, y)
            -- Skip hub chunk
            if x == 0 and y == 0 then return end

            -- Generate random biome for this chunk
            local biome = generateRandomBiome()
            world.chunkSystem:setBiomeAt(x, y, biome)

            -- Initialize chunk with basic tiles based on biome
            for tileX = 0, world.chunkSize - 1 do
                for tileY = 0, world.chunkSize - 1 do
                    local tileIndex = tileX + tileY * world.chunkSize
                    chunk.tiles[tileIndex] = {
                        type = "grass", -- Default type, will be overridden by biome generation
                        x = tileX,
                        y = tileY,
                        variant = math.random(1, 3)
                    }
                end
            end
        end
    end

    return world
end

return WorldCore
