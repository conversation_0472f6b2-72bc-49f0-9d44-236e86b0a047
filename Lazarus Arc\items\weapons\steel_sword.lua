local SteelSword = {
    id = "steel_sword",
    name = "Steel Sword",
    type = "weapon",
    category = "sword",
    
    -- Properties
    properties = {
        damage = 25,
        attackSpeed = 1.2,
        range = 1.5,
        durability = 200,
        maxDurability = 200,
        criticalChance = 0.15,
        criticalMultiplier = 1.5,
        armorPenetration = 0.3,
        weight = 1.0,
        sharpness = 1.0
    },
    
    -- Appearance
    appearance = {
        sprite = "steel_sword",
        scale = 1.0,
        animations = {
            "idle",
            "slash",
            "thrust",
            "block"
        },
        variants = {
            "steel",
            "iron",
            "bronze",
            "copper"
        },
        blendMode = "normal",
        tint = {0.8, 0.8, 0.8},
        alpha = 1.0
    },
    
    -- Sound effects
    sounds = {
        slash = "steel_sword_slash",
        thrust = "steel_sword_thrust",
        block = "steel_sword_block",
        critical = "steel_sword_critical"
    },
    
    -- Effects
    effects = {
        critical = {
            type = "status",
            duration = 0.5,
            effects = {
                damage = 1.5
            }
        }
    }
}

-- Initialize the weapon
function SteelSword.init(weapon, world)
    -- Copy all fields from SteelSword template to weapon instance
    for k, v in pairs(SteelSword) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        comboCount = 0,
        lastComboTime = 0
    }

    return weapon
end

-- Update the weapon
function SteelSword.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Decay combo count
        if world.time - weapon.properties.weaponState.lastComboTime > 1.5 then
            weapon.properties.weaponState.comboCount = 0
        end
    end
end

-- Handle attack
function SteelSword.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Calculate slash direction
    local angle = math.atan2(
        target.position.y - attacker.position.y,
        target.position.x - attacker.position.x
    )
    
    -- Create slash effect
    if world.createEffect then
        world.createEffect({
            type = "slash",
            position = attacker.position,
            angle = angle,
            range = weapon.properties.range,
            duration = 0.5
        })
    end
    
    -- Check for critical hit
    if math.random() < weapon.properties.criticalChance then
        -- Create critical effect
        if world.createEffect then
            world.createEffect({
                type = "critical",
                position = target.position,
                duration = 0.5
            })
        end
        
        -- Apply critical effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.critical)
        end
        
        -- Play critical sound
        if world.playSound then
            world.playSound(weapon.sounds.critical)
        end
    end
    
    -- Update combo
    if world.time - weapon.properties.weaponState.lastComboTime <= 1.5 then
        weapon.properties.weaponState.comboCount = math.min(3,
            weapon.properties.weaponState.comboCount + 1)
    else
        weapon.properties.weaponState.comboCount = 1
    end
    weapon.properties.weaponState.lastComboTime = world.time
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function SteelSword.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough combo
    if weapon.properties.weaponState.comboCount < 3 then
        return false
    end
    
    -- Create powerful slash effect
    if world.createEffect then
        world.createEffect({
            type = "powerful_slash",
            position = attacker.position,
            angle = math.atan2(
                target.position.y - attacker.position.y,
                target.position.x - attacker.position.x
            ),
            range = weapon.properties.range * 1.5,
            duration = 0.5
        })
    end
    
    -- Apply critical effect
    if target.applyEffect then
        target.applyEffect(weapon.effects.critical)
    end
    
    -- Reset combo
    weapon.properties.weaponState.comboCount = 0
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 3)
    
    return true
end

return SteelSword 