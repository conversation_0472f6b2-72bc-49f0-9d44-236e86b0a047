-- friends.lua
-- Friend system for Lazarus Arc

local Friends = {}

-- Create a new friends list instance
function Friends.new()
    local self = {
        friends = {},           -- List of friends
        pendingRequests = {},   -- Pending friend requests
        sentRequests = {},      -- Requests sent by this player
        muted = {},             -- Muted players
        blocked = {},           -- Blocked players
        maxFriends = 100,       -- Maximum number of friends allowed
        lastSyncTime = 0        -- Last time synced with database
    }
    
    -- Add a friend directly (used for loading from save or accepting a request)
    function self.addFriend(friendId, friendName, options)
        options = options or {}
        
        -- Check if already a friend
        if self.friends[friendId] then
            print("Already friends with " .. friendName)
            return false
        end
        
        -- Check if at max friends limit
        if self.countFriends() >= self.maxFriends then
            print("Friend list is full")
            return false
        end
        
        -- Check if player is blocked
        if self.isBlocked(friendId) then
            print("Cannot add blocked player as friend")
            return false
        end
        
        -- Create friend entry
        self.friends[friendId] = {
            id = friendId,
            name = friendName,
            since = options.since or os.time(),
            lastSeen = options.lastSeen or os.time(),
            favorite = options.favorite or false,
            notes = options.notes or "",
            permissions = options.permissions or {
                canVisitHub = true,
                canSendMessages = true,
                canTradeItems = true,
                canSeeStatus = true
            }
        }
        
        -- Remove from pending requests if it was there
        if self.pendingRequests[friendId] then
            self.pendingRequests[friendId] = nil
        end
        
        -- Remove from sent requests if it was there
        if self.sentRequests[friendId] then
            self.sentRequests[friendId] = nil
        end
        
        print("Added " .. friendName .. " to friends list")
        return true
    end
    
    -- Remove a friend
    function self.removeFriend(friendId)
        if not self.friends[friendId] then
            print("Player is not on your friends list")
            return false
        end
        
        local friendName = self.friends[friendId].name
        self.friends[friendId] = nil
        
        print("Removed " .. friendName .. " from friends list")
        return true
    end
    
    -- Send a friend request
    function self.sendFriendRequest(targetId, targetName, message)
        -- Check if already a friend
        if self.friends[targetId] then
            print("Already friends with " .. targetName)
            return false
        end
        
        -- Check if at max friends limit
        if self.countFriends() >= self.maxFriends then
            print("Friend list is full")
            return false
        end
        
        -- Check if player is blocked
        if self.isBlocked(targetId) then
            print("Cannot send request to blocked player")
            return false
        end
        
        -- Check if request already sent
        if self.sentRequests[targetId] then
            print("Friend request already sent to " .. targetName)
            return false
        end
        
        -- Check if player has sent us a request (auto-accept)
        if self.pendingRequests[targetId] then
            print("Accepting existing request from " .. targetName)
            return self.acceptFriendRequest(targetId)
        end
        
        -- Create the request entry
        self.sentRequests[targetId] = {
            id = targetId,
            name = targetName,
            message = message or "I'd like to add you as a friend!",
            sent = os.time()
        }
        
        print("Sent friend request to " .. targetName)
        return true
    end
    
    -- Cancel a sent friend request
    function self.cancelFriendRequest(targetId)
        if not self.sentRequests[targetId] then
            print("No pending request to cancel")
            return false
        end
        
        local targetName = self.sentRequests[targetId].name
        self.sentRequests[targetId] = nil
        
        print("Cancelled friend request to " .. targetName)
        return true
    end
    
    -- Receive a friend request
    function self.receiveFriendRequest(senderId, senderName, message)
        -- Check if already a friend
        if self.friends[senderId] then
            print("Already friends with " .. senderName)
            return false
        end
        
        -- Check if player is blocked
        if self.isBlocked(senderId) then
            print("Request blocked (player is on block list)")
            return false
        end
        
        -- Check if we've already sent a request (auto-accept)
        if self.sentRequests[senderId] then
            print("Auto-accepting request from " .. senderName .. " (mutual request)")
            -- This would be handled by the receiver's acceptFriendRequest
            return true
        end
        
        -- Create the request entry
        self.pendingRequests[senderId] = {
            id = senderId,
            name = senderName,
            message = message or "I'd like to add you as a friend!",
            received = os.time()
        }
        
        print("Received friend request from " .. senderName)
        return true
    end
    
    -- Accept a friend request
    function self.acceptFriendRequest(senderId)
        if not self.pendingRequests[senderId] then
            print("No pending request from this player")
            return false
        end
        
        local senderName = self.pendingRequests[senderId].name
        
        -- Add as friend
        local success = self.addFriend(senderId, senderName)
        
        -- Remove from pending requests
        self.pendingRequests[senderId] = nil
        
        print("Accepted friend request from " .. senderName)
        return success
    end
    
    -- Decline a friend request
    function self.declineFriendRequest(senderId)
        if not self.pendingRequests[senderId] then
            print("No pending request from this player")
            return false
        end
        
        local senderName = self.pendingRequests[senderId].name
        self.pendingRequests[senderId] = nil
        
        print("Declined friend request from " .. senderName)
        return true
    end
    
    -- Get all friend requests
    function self.getFriendRequests()
        local requests = {}
        
        for id, requestData in pairs(self.pendingRequests) do
            table.insert(requests, requestData)
        end
        
        return requests
    end
    
    -- Get all sent requests
    function self.getSentRequests()
        local requests = {}
        
        for id, requestData in pairs(self.sentRequests) do
            table.insert(requests, requestData)
        end
        
        return requests
    end
    
    -- Set permission for a friend
    function self.setFriendPermission(friendId, permissionName, value)
        if not self.friends[friendId] then
            print("Player is not on your friends list")
            return false
        end
        
        -- Valid permission check
        local validPermissions = {
            canVisitHub = true,
            canSendMessages = true,
            canTradeItems = true,
            canSeeStatus = true
        }
        
        if not validPermissions[permissionName] then
            print("Invalid permission: " .. permissionName)
            return false
        end
        
        -- Set the permission
        self.friends[friendId].permissions[permissionName] = value
        
        local friendName = self.friends[friendId].name
        if value then
            print("Granted " .. permissionName .. " permission to " .. friendName)
        else
            print("Revoked " .. permissionName .. " permission from " .. friendName)
        end
        
        return true
    end
    
    -- Check if a friend has a specific permission
    function self.hasFriendPermission(friendId, permissionName)
        if not self.friends[friendId] then
            return false
        end
        
        return self.friends[friendId].permissions[permissionName] == true
    end
    
    -- Mute a player
    function self.mutePlayer(playerId, playerName, duration)
        -- Duration in minutes, 0 means indefinite
        duration = duration or 0
        
        local expiresAt = duration > 0 and (os.time() + (duration * 60)) or 0
        
        self.muted[playerId] = {
            id = playerId,
            name = playerName,
            since = os.time(),
            expiresAt = expiresAt
        }
        
        print("Muted " .. playerName .. " for " .. (duration > 0 and (duration .. " minutes") or "indefinitely"))
        return true
    end
    
    -- Unmute a player
    function self.unmutePlayer(playerId)
        if not self.muted[playerId] then
            print("Player is not muted")
            return false
        end
        
        local playerName = self.muted[playerId].name
        self.muted[playerId] = nil
        
        print("Unmuted " .. playerName)
        return true
    end
    
    -- Check if a player is muted
    function self.isMuted(playerId)
        if not self.muted[playerId] then
            return false
        end
        
        -- Check if mute has expired
        if self.muted[playerId].expiresAt > 0 and os.time() > self.muted[playerId].expiresAt then
            -- Auto-unmute
            self.muted[playerId] = nil
            return false
        end
        
        return true
    end
    
    -- Block a player
    function self.blockPlayer(playerId, playerName, reason)
        -- Remove from friends if present
        if self.friends[playerId] then
            self.removeFriend(playerId)
        end
        
        -- Remove from pending requests if present
        if self.pendingRequests[playerId] then
            self.pendingRequests[playerId] = nil
        end
        
        -- Remove from sent requests if present
        if self.sentRequests[playerId] then
            self.sentRequests[playerId] = nil
        end
        
        -- Add to blocked list
        self.blocked[playerId] = {
            id = playerId,
            name = playerName,
            reason = reason or "",
            since = os.time()
        }
        
        print("Blocked " .. playerName)
        return true
    end
    
    -- Unblock a player
    function self.unblockPlayer(playerId)
        if not self.blocked[playerId] then
            print("Player is not blocked")
            return false
        end
        
        local playerName = self.blocked[playerId].name
        self.blocked[playerId] = nil
        
        print("Unblocked " .. playerName)
        return true
    end
    
    -- Check if a player is blocked
    function self.isBlocked(playerId)
        return self.blocked[playerId] ~= nil
    end
    
    -- Get all friends
    function self.getAllFriends()
        local friendsList = {}
        
        for id, friendData in pairs(self.friends) do
            table.insert(friendsList, friendData)
        end
        
        -- Sort by favorite status then name
        table.sort(friendsList, function(a, b)
            if a.favorite ~= b.favorite then
                return a.favorite
            end
            return a.name < b.name
        end)
        
        return friendsList
    end
    
    -- Get online friends (based on lastSeen threshold)
    function self.getOnlineFriends(onlineThreshold)
        onlineThreshold = onlineThreshold or (15 * 60) -- 15 minutes by default
        
        local onlineFriends = {}
        local now = os.time()
        
        for id, friendData in pairs(self.friends) do
            if (now - friendData.lastSeen) <= onlineThreshold then
                table.insert(onlineFriends, friendData)
            end
        end
        
        return onlineFriends
    end
    
    -- Get muted players
    function self.getMutedPlayers()
        local mutedPlayers = {}
        
        for id, muteData in pairs(self.muted) do
            -- Check if mute has expired
            if muteData.expiresAt > 0 and os.time() > muteData.expiresAt then
                -- Auto-unmute
                self.muted[id] = nil
            else
                table.insert(mutedPlayers, muteData)
            end
        end
        
        return mutedPlayers
    end
    
    -- Get blocked players
    function self.getBlockedPlayers()
        local blockedPlayers = {}
        
        for id, blockData in pairs(self.blocked) do
            table.insert(blockedPlayers, blockData)
        end
        
        return blockedPlayers
    end
    
    -- Set favorite status for a friend
    function self.setFavorite(friendId, favorite)
        if not self.friends[friendId] then
            print("Player is not on your friends list")
            return false
        end
        
        self.friends[friendId].favorite = favorite
        
        local friendName = self.friends[friendId].name
        if favorite then
            print("Marked " .. friendName .. " as favorite")
        else
            print("Removed " .. friendName .. " from favorites")
        end
        
        return true
    end
    
    -- Set note for a friend
    function self.setFriendNote(friendId, note)
        if not self.friends[friendId] then
            print("Player is not on your friends list")
            return false
        end
        
        self.friends[friendId].notes = note
        
        local friendName = self.friends[friendId].name
        print("Updated note for " .. friendName)
        
        return true
    end
    
    -- Update friend's last seen time
    function self.updateLastSeen(friendId, timestamp)
        if not self.friends[friendId] then
            return false
        end
        
        self.friends[friendId].lastSeen = timestamp or os.time()
        return true
    end
    
    -- Count total number of friends
    function self.countFriends()
        local count = 0
        for _ in pairs(self.friends) do
            count = count + 1
        end
        return count
    end
    
    -- Check if player is a friend
    function self.isFriend(playerId)
        return self.friends[playerId] ~= nil
    end
    
    -- Check pending friend request
    function self.hasPendingRequest(playerId)
        return self.pendingRequests[playerId] ~= nil
    end
    
    -- Check sent friend request
    function self.hasSentRequest(playerId)
        return self.sentRequests[playerId] ~= nil
    end
    
    -- Serialize friends list for saving
    function self.serialize()
        return {
            friends = self.friends,
            pendingRequests = self.pendingRequests,
            sentRequests = self.sentRequests,
            muted = self.muted,
            blocked = self.blocked,
            lastSyncTime = os.time()
        }
    end
    
    -- Load friends data from database
    function self.syncWithDatabase(playerData)
        -- This would normally call the DatabaseManager to get the latest data
        -- For now, we just set the sync time
        self.lastSyncTime = os.time()
        
        -- Placeholder for actual database sync
        print("Friends list synced with database")
        return true
    end
    
    return self
end

-- Load friends list from serialized data
function Friends.load(data)
    if not data then
        print("Invalid friends data")
        return Friends.new()
    end
    
    local friends = Friends.new()
    
    -- Load saved data
    friends.friends = data.friends or {}
    friends.pendingRequests = data.pendingRequests or {}
    friends.sentRequests = data.sentRequests or {}
    friends.muted = data.muted or {}
    friends.blocked = data.blocked or {}
    friends.lastSyncTime = data.lastSyncTime or 0
    
    -- Clean up expired mutes
    for id, muteData in pairs(friends.muted) do
        if muteData.expiresAt > 0 and os.time() > muteData.expiresAt then
            friends.muted[id] = nil
        end
    end
    
    return friends
end

-- Check if permissions are compatible for an action
function Friends.checkMutualPermission(playerA, playerB, permissionName)
    -- This function checks if both players have given each other the specified permission
    
    -- Check if players are friends
    if not playerA.friends.isFriend(playerB.id) or not playerB.friends.isFriend(playerA.id) then
        return false
    end
    
    -- Check permission in both directions
    return playerA.friends.hasFriendPermission(playerB.id, permissionName) and
           playerB.friends.hasFriendPermission(playerA.id, permissionName)
end

-- Create a new friend notification
function Friends.createNotification(type, data)
    return {
        type = type, -- e.g., "request", "accepted", "removed", etc.
        data = data,
        time = os.time(),
        read = false
    }
end

-- Connect with another player's hub directly if permissions allow
function Friends.connectToFriendHub(character, friendId)
    if not character.friends.isFriend(friendId) then
        print("Player is not on your friends list")
        return false
    end
    
    if not character.friends.hasFriendPermission(friendId, "canVisitHub") then
        print("You don't have permission to visit this player's hub")
        return false
    end
    
    -- This would connect to the teleport system
    print("Connecting to friend's hub...")
    -- Call teleport system functions here
    
    return true
end

-- Send an item to a friend if permissions allow
function Friends.sendItemToFriend(character, friendId, itemId, quantity)
    if not character.friends.isFriend(friendId) then
        print("Player is not on your friends list")
        return false
    end
    
    if not character.friends.hasFriendPermission(friendId, "canTradeItems") then
        print("You don't have permission to trade with this player")
        return false
    end
    
    -- Check if player has the item
    if not character.inventory.hasItem(itemId, quantity) then
        print("You don't have enough of this item")
        return false
    end
    
    -- This would send the item through the trading system
    print("Sending item to friend...")
    -- Call trading system functions here
    
    return true
end

-- Global check for player relationship status
function Friends.getRelationshipStatus(playerA, playerB)
    -- Check blocked status first
    if playerA.friends.isBlocked(playerB.id) then
        return "blocked"
    elseif playerB.friends.isBlocked(playerA.id) then
        return "blocking"
    end
    
    -- Check friend status
    if playerA.friends.isFriend(playerB.id) then
        return "friend"
    end
    
    -- Check pending requests
    if playerA.friends.hasPendingRequest(playerB.id) then
        return "pending_incoming"
    elseif playerA.friends.hasSentRequest(playerB.id) then
        return "pending_outgoing"
    end
    
    -- No relationship
    return "none"
end

return Friends