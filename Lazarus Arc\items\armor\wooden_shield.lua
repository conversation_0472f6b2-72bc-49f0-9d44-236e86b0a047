-- items/armor/wooden_shield.lua

local Shield = {
    id = "wooden_shield",
    name = "Wooden Shield",
    description = "A basic wooden shield that provides minimal protection.",
    type = "shield",
    category = "shield",
    stackable = false,

    stats = {
        defense = 5.0,
        block_chance = 0.15,
        durability = 60.0,
        repair_cost = 15,
        weight = 4.0,
    },

    material = "wood",

    sprite = "res://Sprite/items/armor/wooden_shield.png",
    size = { width = 64, height = 64 },

    effects = {
        projectile_resistance = 0.05
    },

    meta = {
        rarity = "common"
    },

    take_damage = function(self, amount)
        self.stats.durability = math.max(0, self.stats.durability - amount)
        if self.stats.durability == 0 then
            print(self.name .. " is broken! Penalties applied.")
            -- Potential penalties could be applied here
        end
    end,

    repair = function(self)
        local repair_amount = 60 - self.stats.durability
        self.stats.durability = 60
        print(self.name .. " repaired by " .. repair_amount .. " points.")
    end
}

return Shield
