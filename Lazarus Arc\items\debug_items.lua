-- items/debug_items.lua
-- Debug placeholder items with color coding for different game elements

local DebugItems = {
    -- Entity Placeholders (Yellow)
    entity_placeholder = {
        id = "debug_entity",
        name = "Debug Entity",
        category = "debug",
        description = "Placeholder for missing entity sprite",
        value = 0,
        weight = 0,
        stackable = false,
        properties = {
            material = "debug",
            quality = 1.0,
            durability = 100,
            debug_color = {1, 1, 0, 1}, -- Yellow
            debug_shape = "circle",
            debug_size = 32
        },
        onUse = function(user, target)
            return true
        end
    },
    
    -- Character Placeholders (White)
    character_placeholder = {
        id = "debug_character",
        name = "Debug Character",
        category = "debug",
        description = "Placeholder for missing character sprite",
        value = 0,
        weight = 0,
        stackable = false,
        properties = {
            material = "debug",
            quality = 1.0,
            durability = 100,
            debug_color = {1, 1, 1, 1}, -- White
            debug_shape = "humanoid",
            debug_size = 48
        },
        onUse = function(user, target)
            return true
        end
    },
    
    -- Tile Placeholders (Green)
    tile_placeholder = {
        id = "debug_tile",
        name = "Debug Tile",
        category = "debug",
        description = "Placeholder for missing tile sprite",
        value = 0,
        weight = 0,
        stackable = false,
        properties = {
            material = "debug",
            quality = 1.0,
            durability = 100,
            debug_color = {0, 1, 0, 1}, -- Green
            debug_shape = "square",
            debug_size = 32
        },
        onUse = function(user, target)
            return true
        end
    },
    
    -- Water Placeholders (Blue)
    water_placeholder = {
        id = "debug_water",
        name = "Debug Water",
        category = "debug",
        description = "Placeholder for missing water sprite",
        value = 0,
        weight = 0,
        stackable = false,
        properties = {
            material = "debug",
            quality = 1.0,
            durability = 100,
            debug_color = {0, 0, 1, 0.5}, -- Semi-transparent Blue
            debug_shape = "square",
            debug_size = 32
        },
        onUse = function(user, target)
            return true
        end
    },
    
    -- Structure Placeholders (Red)
    structure_placeholder = {
        id = "debug_structure",
        name = "Debug Structure",
        category = "debug",
        description = "Placeholder for missing structure sprite",
        value = 0,
        weight = 0,
        stackable = false,
        properties = {
            material = "debug",
            quality = 1.0,
            durability = 100,
            debug_color = {1, 0, 0, 1}, -- Red
            debug_shape = "rectangle",
            debug_size = {64, 64}
        },
        onUse = function(user, target)
            return true
        end
    },
    
    -- Item Placeholders (Purple)
    item_placeholder = {
        id = "debug_item",
        name = "Debug Item",
        category = "debug",
        description = "Placeholder for missing item sprite",
        value = 0,
        weight = 0,
        stackable = true,
        maxStack = 99,
        properties = {
            material = "debug",
            quality = 1.0,
            durability = 100,
            debug_color = {1, 0, 1, 1}, -- Purple
            debug_shape = "diamond",
            debug_size = 24
        },
        onUse = function(user, target)
            return true
        end
    },
    
    -- Effect Placeholders (Cyan)
    effect_placeholder = {
        id = "debug_effect",
        name = "Debug Effect",
        category = "debug",
        description = "Placeholder for missing effect sprite",
        value = 0,
        weight = 0,
        stackable = false,
        properties = {
            material = "debug",
            quality = 1.0,
            durability = 100,
            debug_color = {0, 1, 1, 0.7}, -- Semi-transparent Cyan
            debug_shape = "star",
            debug_size = 32
        },
        onUse = function(user, target)
            return true
        end
    },
    
    -- UI Placeholders (Orange)
    ui_placeholder = {
        id = "debug_ui",
        name = "Debug UI",
        category = "debug",
        description = "Placeholder for missing UI element",
        value = 0,
        weight = 0,
        stackable = false,
        properties = {
            material = "debug",
            quality = 1.0,
            durability = 100,
            debug_color = {1, 0.5, 0, 1}, -- Orange
            debug_shape = "rectangle",
            debug_size = {32, 32}
        },
        onUse = function(user, target)
            return true
        end
    }
}

return DebugItems 