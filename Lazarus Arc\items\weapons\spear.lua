-- items/weapons/spear.lua

local Weapon = {
    id = "spear",
    name = "Spear",
    description = "A long weapon with a sharp point, providing good reach in combat.",
    type = "spear",
    category = "weapon",
    stackable = false,

    stats = {
        attack_power = 9.0,
        attack_speed = 0.9,
        range = 2.0, -- Extended melee range
        durability = 90.0,
        repair_cost = 25,
        weight = 4.0,
    },

    damage_type = "physical",
    material = "wood_and_iron",

    sprite = "res://Sprite/items/weapons/spear.png",
    size = { width = 64, height = 64 },

    effects = {
        critical_chance = 0.05,
        pierce_armor = 0.1
    },

    meta = {
        rarity = "common"
    },

    take_damage = function(self, amount)
        self.stats.durability = math.max(0, self.stats.durability - amount)
        if self.stats.durability == 0 then
            print(self.name .. " is broken! Penalties applied.")
            -- Potential penalties could be applied here
        end
    end,

    repair = function(self)
        local repair_amount = 90 - self.stats.durability
        self.stats.durability = 90
        print(self.name .. " repaired by " .. repair_amount .. " points.")
    end,
    
    onUse = function(self, user, target)
        -- Special attack: Thrust
        if user and user.stats and user.stats.stamina and user.stats.stamina >= 10 then
            -- Consume stamina
            user.stats.stamina = user.stats.stamina - 10
            
            print(user.name .. " performs a powerful thrust with the spear!")
            -- In a real implementation, this would apply a special attack effect
            return true
        else
            print("Not enough stamina for a special attack.")
            return false
        end
    end
}

return Weapon
