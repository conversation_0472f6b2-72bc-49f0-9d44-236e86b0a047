local Tuna = {
    id = "tuna",
    name = "Bluefin Tuna",
    type = "fish",
    
    -- Entity categories
    categories = {"animal", "fish", "saltwater", "predator", "schooling"},
    
    -- Threat and food categories
    threatCategories = {"player", "predator", "shark"},
    foodCategories = {"fish", "squid", "crustacean", "small_prey"},
    
    -- Stats
    maxHealth = 60,
    health = 60,
    maxStamina = 90,
    stamina = 90,
    speed = 3.0,
    
    -- Water properties
    water = {
        swimSpeed = 3.0,
        maxDepth = 15,
        preferredDepth = 8,
        oxygenLevel = 1.0,
        temperature = "warm",
        currentSpeed = 1.5
    },
    
    -- Behaviors
    behaviors = {"hunt", "patrol", "school", "migrate"},
    
    -- Behavior configurations
    behaviorConfigs = {
        hunt = {
            moveSpeed = 3.5,
            searchRadius = 25,
            attackRange = 2.5,
            damage = 35,
            preferredPrey = {"fish", "squid", "crustacean"},
            successChance = 0.8,
            schoolBonus = 0.2
        },
        patrol = {
            moveSpeed = 2.5,
            patrolRadius = 30,
            preferredDepth = 8,
            restInterval = {20, 30}
        },
        school = {
            moveSpeed = 2.8,
            followDistance = 3,
            separationDistance = 2,
            alignmentStrength = 0.6,
            cohesionStrength = 0.7,
            maxGroupSize = 20
        },
        migrate = {
            moveSpeed = 2.0,
            searchRadius = 40,
            preferredDepth = 10,
            season = "autumn",
            successChance = 0.9
        }
    },
    
    -- Special abilities
    abilities = {
        burst = {
            speed = 4.0,
            duration = 0.8,
            cooldown = 3,
            staminaCost = 15
        },
        tailWhip = {
            range = 4,
            damage = 25,
            duration = 0.4,
            cooldown = 2,
            effect = "stun"
        },
        schoolBoost = {
            speed = 1.3,
            duration = 5,
            cooldown = 6,
            effect = "group"
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "tuna",
        scale = 1.3,
        animations = {
            "idle", "swim", "burst", "school", "migrate"
        },
        variants = {
            "blue", "silver", "striped", "dark"
        }
    },
    
    -- Sound effects
    sounds = {
        splash = "fish_splash",
        swim = "fish_swim",
        burst = "fish_burst"
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.8, quantity = {2, 3}},
        {id = "scale", chance = 0.7, quantity = {2, 4}},
        {id = "fin", chance = 0.4, quantity = {1, 2}},
        {id = "bone", chance = 0.5, quantity = {1, 2}}
    }
}

-- Initialize the entity
function Tuna.init(entity, world)
    -- Copy all fields from Tuna template to entity instance
    for k, v in pairs(Tuna) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random tuna variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Tuna.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update water properties
    if entity.water then
        -- Adjust hunting success based on time of day
        if world and world.timeOfDay == "dawn" or world.timeOfDay == "dusk" then
            entity.behaviorConfigs.hunt.successChance = 0.9
        else
            entity.behaviorConfigs.hunt.successChance = 0.8
        end

        -- Adjust preferred depth based on time of day
        if world and world.timeOfDay == "night" then
            entity.water.preferredDepth = 5
        else
            entity.water.preferredDepth = 8
        end

        -- Apply school bonus to hunting
        if world and world.entities then
            local nearbySchool = 0
            for _, other in ipairs(world.entities) do
                if other.type == "fish" and other.id == "tuna" then
                    local distance = math.sqrt(
                        (other.position.x - entity.position.x)^2 + 
                        (other.position.y - entity.position.y)^2
                    )
                    if distance < 8 then
                        nearbySchool = nearbySchool + 1
                    end
                end
            end
            if nearbySchool > 0 then
                entity.behaviorConfigs.hunt.successChance = 
                    entity.behaviorConfigs.hunt.successChance + 
                    entity.behaviorConfigs.hunt.schoolBonus
            end
        end

        -- Check migration conditions
        if world and world.season == "autumn" then
            entity.behaviorConfigs.migrate.successChance = 0.9
        else
            entity.behaviorConfigs.migrate.successChance = 0.0
        end
    end
end

return Tuna 