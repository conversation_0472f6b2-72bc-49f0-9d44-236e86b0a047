-- behaviors/schooling.lua
-- Schooling behavior for fish or other entities that move in groups

local SchoolingBehavior = {}  -- create an empty table

SchoolingBehavior.id = "schooling"
SchoolingBehavior.name = "Schooling Behavior"

-- Configuration parameters
SchoolingBehavior.defaultConfig = {
    neighborRadius = 5,       -- How far to look for neighbors
    separationDistance = 1,   -- Desired space between individuals
    cohesionWeight = 0.2,     -- How strongly to steer towards group center
    alignmentWeight = 0.3,    -- How strongly to align with neighbors' direction
    separationWeight = 0.5,   -- How strongly to avoid crowding
    maxSpeed = 2.0,           -- Maximum movement speed
    viewAngle = 120,          -- Field of view for neighbors (degrees)
    avoidObstacles = true,    -- Whether to avoid impassable tiles
    obstacleRadius = 1        -- How far to look ahead for obstacles
}

function SchoolingBehavior.init(entity, customConfig)
    entity.schoolingState = entity.schoolingState or {
        neighbors = {},
        avgPosition = {x = 0, y = 0},
        avgDirection = {x = 0, y = 0},
        config = {}
    }
    
    local config = entity.schoolingState.config
    for k, v in pairs(SchoolingBehavior.defaultConfig) do
        config[k] = v
    end
    if customConfig then
        for k, v in pairs(customConfig) do
            config[k] = v
        end
    end
end

function SchoolingBehavior.update(entity, world, dt)
    local state = entity.schoolingState
    if not state then
        SchoolingBehavior.init(entity)
        state = entity.schoolingState
    end
    
    local config = state.config

    -- Find neighbors within radius and view angle
    state.neighbors = SchoolingBehavior.findNeighbors(entity, world)
    
    -- Calculate average position and direction of neighbors
    SchoolingBehavior.calculateGroupProperties(entity, world)
    
    -- Apply schooling forces
    local cohesionForce = SchoolingBehavior.calculateCohesionForce(entity)
    local alignmentForce = SchoolingBehavior.calculateAlignmentForce(entity)
    local separationForce = SchoolingBehavior.calculateSeparationForce(entity)
    
    local totalForce = {x = 0, y = 0}
    totalForce.x = cohesionForce.x * config.cohesionWeight +
                   alignmentForce.x * config.alignmentWeight +
                   separationForce.x * config.separationWeight
    totalForce.y = cohesionForce.y * config.cohesionWeight +
                   alignmentForce.y * config.alignmentWeight +
                   separationForce.y * config.separationWeight
    
    -- Apply force to velocity
    entity.velocity.x = entity.velocity.x + totalForce.x * dt
    entity.velocity.y = entity.velocity.y + totalForce.y * dt
    
    -- Limit speed
    local speed = math.sqrt(entity.velocity.x^2 + entity.velocity.y^2)
    if speed > config.maxSpeed then
        entity.velocity.x = entity.velocity.x / speed * config.maxSpeed
        entity.velocity.y = entity.velocity.y / speed * config.maxSpeed
    end
    
    -- Avoid obstacles if needed
    if config.avoidObstacles then
        SchoolingBehavior.avoidObstacles(entity, world)
    end
end

function SchoolingBehavior.findNeighbors(entity, world)
    local state = entity.schoolingState
    local config = state.config
    local neighbors = {}
    
    local nearbyEntities = world.entitySystem:findNearbyEntities(
        entity.position.x, entity.position.y, config.neighborRadius
    )
    
    for _, otherEntity in ipairs(nearbyEntities) do
        if otherEntity.id == entity.id then goto continue end
        
        local dx = otherEntity.position.x - entity.position.x
        local dy = otherEntity.position.y - entity.position.y
        local angle = math.deg(math.atan2(dy, dx))
        local entityAngle = math.deg(math.atan2(entity.velocity.y, entity.velocity.x))
        
        if math.abs(angle - entityAngle) <= config.viewAngle / 2 then
            table.insert(neighbors, otherEntity)
        end
        
        ::continue::
    end
    
    return neighbors
end

function SchoolingBehavior.calculateGroupProperties(entity, world)
    local state = entity.schoolingState
    local config = state.config
    local avgPosition = {x = 0, y = 0}
    local avgDirection = {x = 0, y = 0}
    local neighborCount = 0
    
    for _, neighbor in ipairs(state.neighbors) do
        avgPosition.x = avgPosition.x + neighbor.position.x
        avgPosition.y = avgPosition.y + neighbor.position.y
        avgDirection.x = avgDirection.x + neighbor.velocity.x
        avgDirection.y = avgDirection.y + neighbor.velocity.y
        neighborCount = neighborCount + 1
    end
    
    if neighborCount > 0 then
        avgPosition.x = avgPosition.x / neighborCount
        avgPosition.y = avgPosition.y / neighborCount
        avgDirection.x = avgDirection.x / neighborCount
        avgDirection.y = avgDirection.y / neighborCount
    end
    
    state.avgPosition = avgPosition
    state.avgDirection = avgDirection
end

function SchoolingBehavior.calculateCohesionForce(entity)
    local state = entity.schoolingState
    local force = {x = 0, y = 0}
    if #state.neighbors > 0 then
        force.x = state.avgPosition.x - entity.position.x
        force.y = state.avgPosition.y - entity.position.y
    end
    return force
end

function SchoolingBehavior.calculateAlignmentForce(entity)
    local state = entity.schoolingState
    local force = {x = 0, y = 0}
    if #state.neighbors > 0 then
        force.x = state.avgDirection.x - entity.velocity.x
        force.y = state.avgDirection.y - entity.velocity.y
    end
    return force
end

function SchoolingBehavior.calculateSeparationForce(entity)
    local state = entity.schoolingState
    local config = state.config
    local force = {x = 0, y = 0}
    
    for _, neighbor in ipairs(state.neighbors) do
        local dx = entity.position.x - neighbor.position.x
        local dy = entity.position.y - neighbor.position.y
        local distSq = dx * dx + dy * dy
        if distSq < config.separationDistance * config.separationDistance then
            local dist = math.sqrt(distSq)
            force.x = force.x + dx / dist
            force.y = force.y + dy / dist
        end
    end
    
    return force
end

function SchoolingBehavior.avoidObstacles(entity, world)
    local state = entity.schoolingState
    local config = state.config
    local pos = entity.position
    local chunkSystem = world.chunkSystem
    
    local tileAheadX = pos.x + entity.velocity.x * config.obstacleRadius
    local tileAheadY = pos.y + entity.velocity.y * config.obstacleRadius
    local tileAhead = chunkSystem:getTileAt(tileAheadX, tileAheadY)
    
    if tileAhead then
        local tileModule = world.modules.tiles[tileAhead.type]
        if tileModule and not tileModule.passable then
            local newDirection = {
                x = -entity.velocity.x + math.random() * 0.2 - 0.1,
                y = -entity.velocity.y + math.random() * 0.2 - 0.1
            }
            local dirLength = math.sqrt(newDirection.x^2 + newDirection.y^2)
            if dirLength > 0 then
                entity.velocity.x = newDirection.x / dirLength * config.maxSpeed
                entity.velocity.y = newDirection.y / dirLength * config.maxSpeed
            end
        end
    end
end

return SchoolingBehavior
