Overdrive = {
    -- ⚔️ <PERSON> (Melee Powerhouse)
    warrior = {
        titan_slash = {
            name = "Titan Slash",
            effect = "high_damage",
            multiplier = 3.0, -- Deals 3x normal attack
            cost = 50, -- Uses 50% of Overdrive bar
            description = "A devastating sword strike that cleaves through enemies."
        },
        unstoppable_charge = {
            name = "Unstoppable Charge",
            effect = "stun",
            duration = 5, -- Stuns for 5 seconds
            cost = 40,
            description = "A powerful charge that knocks down all enemies in your path."
        },
        earthquake_stomp = {
            name = "Earthquake Stomp",
            effect = "aoe_damage",
            radius = 5, -- Affects all enemies in 5m range
            cost = 60,
            description = "Slams the ground, sending shockwaves that damage all nearby enemies."
        }
    },

    -- 🧙 Mage (Arcane Devastation)
    mage = {
        arcane_storm = {
            name = "Arcane Storm",
            effect = "magic_aoe",
            multiplier = 2.5,
            cost = 70,
            description = "Summons a chaotic storm of arcane energy, striking all enemies."
        },
        mana_burst = {
            name = "Man<PERSON> Burst",
            effect = "restore_mana",
            amount = 100, -- Restores full mana
            cost = 40,
            description = "Unleashes raw mana, fully restoring energy and empowering spells."
        },
        time_warp = {
            name = "Time Warp",
            effect = "slow_time",
            duration = 10,
            cost = 80,
            description = "Manipulates time, slowing enemies and speeding up allies for 10 seconds."
        }
    },

    -- 🏹 Archer (Precision Strikes)
    archer = {
        storm_of_arrows = {
            name = "Storm of Arrows",
            effect = "multi_shot",
            shots = 10,
            cost = 50,
            description = "Unleashes a flurry of 10 arrows, striking multiple enemies."
        },
        piercing_rain = {
            name = "Piercing Rain",
            effect = "armor_piercing",
            multiplier = 2.0,
            cost = 60,
            description = "Fires a deadly shot that ignores enemy armor and deals massive damage."
        },
        silent_execution = {
            name = "Silent Execution",
            effect = "instant_kill",
            chance = 10, -- 10% chance to instantly kill
            cost = 80,
            description = "A perfectly aimed shot that has a small chance to instantly eliminate the target."
        }
    },

    -- 🛡️ Paladin (Holy Defender)
    paladin = {
        divine_barrier = {
            name = "Divine Barrier",
            effect = "damage_reduction",
            reduction = 50, -- Reduces damage by 50%
            duration = 15,
            cost = 50,
            description = "Summons a holy shield that halves incoming damage for 15 seconds."
        },
        holy_wrath = {
            name = "Holy Wrath",
            effect = "holy_damage",
            multiplier = 3.0,
            cost = 60,
            description = "Unleashes divine energy, dealing immense damage to undead and dark creatures."
        },
        heavens_blessing = {
            name = "Heaven’s Blessing",
            effect = "group_heal",
            amount = 75, -- Heals all allies for 75 HP
            cost = 70,
            description = "Calls upon divine power to heal all nearby allies."
        }
    },

    -- 🗡️ Rogue (Speed & Lethality)
    rogue = {
        phantom_step = {
            name = "Phantom Step",
            effect = "invisibility",
            duration = 8,
            cost = 50,
            description = "Becomes invisible for 8 seconds, avoiding enemy detection."
        },
        shadow_ambush = {
            name = "Shadow Ambush",
            effect = "backstab",
            multiplier = 4.0, -- 4x damage from behind
            cost = 60,
            description = "A deadly strike from the shadows, dealing massive backstab damage."
        },
        deadly_chain = {
            name = "Deadly Chain",
            effect = "multi_attack",
            hits = 3, -- Hits up to 3 enemies
            cost = 70,
            description = "Unleashes a flurry of rapid attacks, striking multiple enemies in quick succession."
        }
    },

    -- 💀 Berserker (Pure Chaos)
    berserker = {
        blood_frenzy = {
            name = "Blood Frenzy",
            effect = "lifesteal",
            duration = 10,
            percentage = 50, -- Converts 50% of damage dealt into HP
            cost = 50,
            description = "Gains 50% lifesteal for 10 seconds, draining enemies' health."
        },
        rage_howl = {
            name = "Rage Howl",
            effect = "fear",
            duration = 6,
            cost = 60,
            description = "A terrifying roar that causes all enemies to flee in fear for 6 seconds."
        },
        adrenaline_overload = {
            name = "Adrenaline Overload",
            effect = "invincibility",
            duration = 5,
            cost = 80,
            description = "Enters a state of pure adrenaline, becoming invincible for 5 seconds."
        }
    }
}

-- 🔥 Function to Activate Overdrive Ability
function activate_overdrive(character, ability_name)
    local class_overdrives = Overdrive[character.class]
    
    if not class_overdrives then
        print("No Overdrive abilities found for class: " .. character.class)
        return false
    end

    local ability = class_overdrives[ability_name]
    if not ability then
        print("Invalid Overdrive ability: " .. ability_name)
        return false
    end

    if character.resources.overdrive < ability.cost then
        print(character.name .. " does not have enough Overdrive to use " .. ability.name .. "!")
        return false
    end

    character.resources.overdrive = character.resources.overdrive - ability.cost
    print(character.name .. " used Overdrive: " .. ability.name .. " - " .. ability.description)

    return true
end

return Overdrive
