-- tiles/ice.lua
local IceTile = {
    id = "ice",
    name = "Ice",
    passable = true, -- Passable, but potentially dangerous
    variants = 6, -- e.g., Smooth Lake Ice, Rough River Ice, Cracked Ice, Snow-Dusted Ice, Thin Ice, Glacier Blue Ice

    -- Entities that might spawn on or near ice
    -- Note: Conditional spawning (like 'seal_pup' if coastal) should be handled by the spawning system
    spawns = {"ice_elemental_minor", "arctic_fox", "seal_pup", "frozen_fish_visible", "ice_crystal_formation"}, -- Cold-related spawns

    -- Movement properties
    movementSpeed = 1.0, -- Base speed is normal, but slipperiness is the main factor
    slipperiness = 0.8, -- High chance to slip (0-1)

    -- Ice-specific properties
    thickness = 1.0, -- Represents ice thickness (0-2+), determines break chance
    brittle = true, -- Can be broken with sufficient force/weight
    meltingPoint = 0, -- Temperature (Celsius) at which it starts melting
    transparency = 0.6, -- How clear the ice is (0-1)

    -- Interaction function
    interact = function(tile, entity)
        -- Check ice thickness/safety
        if entity.skills and entity.skills.survival > 2 then
             local safetyMessage = "The ice seems thick and stable here."
             if tile.thickness < 0.5 then safetyMessage = "Warning: The ice looks dangerously thin here!" end
             return { success = false, message = safetyMessage, effects={{type="assess_ice_thickness", value=tile.thickness}} }
        end
        
        -- Attempt to break ice / fish through
        if entity.hasItem and entity.hasItem("ice_pick") or entity.hasItem("heavy_axe") then
            if tile.thickness < 1.5 then -- Can't break super thick ice easily
                 return {
                     success = true,
                     message = "You could attempt to break through the ice here, perhaps for fishing.",
                     effects = {{type="suggest_action", action="break_ice"}}
                 }
             else
                  return { success = false, message = "The ice here is incredibly thick, resisting your attempts to break it." }
             end
        end

        -- Look for things frozen inside
        if tile.transparency > 0.5 and math.random() < 0.15 then
            local frozenItems = {
                {id="frozen_fish", chance=0.6},
                {id="ancient_mammoth_tusk_fragment", chance=0.1}, -- Rare find
                {id="preserved_leaf", chance=0.3}
            }
             for _, item in ipairs(frozenItems) do
                 if math.random() < item.chance then
                     -- Finding it doesn't mean obtaining it, might need breaking ice
                     return {
                         success = true,
                         message = "Peering into the ice, you spot something frozen within: a " .. item.id .. "!",
                         effects = {{type="discover_frozen_item", item=item.id, location={tile.x, tile.y}}}
                     }
                 end
             end
        end

        -- General examination
        return {
            success = false,
            message = "A slick, frozen surface stretches out. It looks very slippery."
        }
    end,

    -- Weather effects
    weatherEffects = {
        heat = function(tile) -- Melting
            -- Assuming world context is available if needed, e.g., via tile.worldRef
            local currentTemp = (tile.worldRef and tile.worldRef.temperature) or 0
            if currentTemp > tile.meltingPoint then
                 tile.thickness = (tile.thickness or 1.0) - 0.2
                 tile.slipperiness = math.min(1.0, (tile.slipperiness or 0.8) + 0.1) -- Melting ice is even more slippery initially
                 if tile.thickness <= 0 then
                      -- Transforms back to water/shallow water depending on context
                      return "transform", tile.underlyingTile or "shallow_water" 
                 elseif tile.thickness < 0.5 then
                      -- Becomes thin ice variant visually/mechanically?
                      return "visual_effect", "melting_ice_thin"
                 end
                 return "visual_effect", "melting_ice_surface"
            end
            return nil
        end,

        sun = function(tile) -- Slower melting than direct heat
            -- Assuming world context is available if needed, e.g., via tile.worldRef
            local currentTemp = (tile.worldRef and tile.worldRef.temperature) or 0
            if currentTemp > tile.meltingPoint then
                 tile.thickness = (tile.thickness or 1.0) - 0.1
                 tile.slipperiness = math.min(0.95, (tile.slipperiness or 0.8) + 0.05)
                 if tile.thickness <= 0 then
                      return "transform", tile.underlyingTile or "shallow_water"
                 elseif tile.thickness < 0.5 then
                      return "visual_effect", "melting_ice_thin"
                 end
                 return "visual_effect", "melting_ice_surface"
            end
             -- Remove snow cover if present
             if tile.isSnowCovered then
                 tile.isSnowCovered = false
                 tile.slipperiness = 0.8 -- Reset base slipperiness
                 return "remove_visual_effect", "snow_covered_ice"
             end
            return nil
        end,

        freeze = function(tile) -- Refreezing or thickening
             -- Assuming world context is available if needed, e.g., via tile.worldRef
             local currentTemp = (tile.worldRef and tile.worldRef.temperature) or 0
             if currentTemp < tile.meltingPoint - 5 then -- Needs to be colder to thicken significantly
                 tile.thickness = (tile.thickness or 1.0) + 0.15
                 tile.slipperiness = 0.7 -- Hard frozen ice might be slightly less slippery?
             end
             return nil
        end,

        snow = function(tile) -- Snow cover
             tile.isSnowCovered = true
             tile.slipperiness = 0.4 -- Snow reduces slipperiness but hides ice condition
             return "visual_effect", "snow_covered_ice"
        end
    },

     -- Time effects
    timeEffects = {
        night = function(tile)
            -- Refreezing is more likely
            -- Assuming world context is available if needed, e.g., via tile.worldRef
            local currentTemp = (tile.worldRef and tile.worldRef.temperature) or 0
            if currentTemp < tile.meltingPoint then
                 tile.thickness = (tile.thickness or 1.0) + 0.05
            end
             tile.ambientSound = "ice_cracking_subtle"
            return nil
        end,
        
        day = function(tile)
             -- Melting more likely if temp > meltingPoint
             tile.ambientSound = "wind_over_ice"
            return nil
        end
    },
    
    -- Visual properties
    visual = {
        base_color = {0.8, 0.9, 1.0, 0.8}, -- Light blue/white, semi-transparent
        variants = {
            {name = "Smooth Lake Ice", transparency=0.8, texture="ice_smooth"},
            {name = "Rough River Ice", texture="ice_rough", transparency=0.5},
            {name = "Cracked Ice", overlay_objects={{name="ice_crack_decal", chance=0.7}}, stability_mod=-0.2},
            {name = "Snow-Dusted Ice", overlay_objects={{name="snow_patch_light", chance=0.8}}, condition_property="isSnowCovered", slipperiness_mod=0.5}, -- Changed condition=tile.isSnowCovered
            {name = "Thin Ice", thickness_mod=0.3, transparency=0.9, color_shift={-0.1, -0.1, 0}}, -- Visually distinct thin ice
            {name = "Glacier Blue Ice", transparency=0.4, color_shift={-0.2, 0, 0.2}, thickness_mod=2.0} -- Very thick, old ice
        },
        reflection = 0.6, -- Reflective surface
        refraction = 0.4,
        transparency = 0.6, -- Base transparency
        special_effects = {
             frozen_bubbles = {type="internal_decal", texture="bubble_frozen", base_chance=0.3, chance_modifier="transparency"}, -- Changed chance=0.3 * tile.transparency
             light_refraction = {type="caustic_effect", intensity=0.4, when="sunny"},
             frost_patterns = {type="overlay_texture", texture="frost_fern", chance=0.2, when="very_cold"}
         },
         weather_overlays = {
             surface_melt = {shininess=0.8, effect="wet_glisten", when="melting"},
             snow_cover = {color={0.95, 0.95, 1.0}, coverage=0.7, when="isSnowCovered"}
         }
    },
    
    -- Audio properties
    ambientSound = "wind_over_ice",
    footstepSound = "ice_scrape_crunch", -- Distinct footsteps
     specialSounds = {
        {name = "ice_crack_sharp", trigger = "heavy_impact_nearby", chance = 0.4},
        {name = "ice_groan_deep", trigger = "temperature_change", chance = 0.1},
        {name = "slip_skitter", trigger = "on_walk_slip", chance = 1.0} -- Sound specifically for slipping
    },

    -- Connections to other tiles
    connects_to = {
        "ice", -- Itself
        "water", -- Edge of melting ice
        "shallow_water", -- Edge of melting ice
        "snow", -- Land covered in snow meeting ice
        "dirt", -- Shoreline
        "sand", -- Shoreline
        "rocky_ground" -- Shoreline
    },

    -- Pathfinding properties
     pathfinding = {
        travel_cost = 1.0, -- Base cost is normal
        hazard_chance_property = "slipperiness", hazard_chance_multiplier = 0.3, -- Changed hazard_chance = 0.3 * tile.slipperiness
        break_chance_property = "thickness", break_chance_base = 0.1, break_chance_divisor = 0.1, -- Changed break_chance = 0.1 * (1 / math.max(0.1, tile.thickness))
        weight_limit_property = "thickness", weight_limit_multiplier = 100 -- Changed weight_limit = tile.thickness * 100
    },

    -- Effects on entities that walk on this tile
    onWalk = function(tile, entity)
        local sound = tile.footstepSound or "ice_scrape_crunch"
        local effects = {}
        
        -- Check for breaking ice
        local thickness = tile.thickness or 1.0
        local breakTestValue = math.random()
        local breakThreshold = 0.1 / math.max(0.1, thickness) -- Higher chance for thinner ice
        if entity.weight and entity.weight > (thickness * 100) then breakThreshold = breakThreshold * 2 end -- Heavier entities increase chance
        
        if breakTestValue < breakThreshold then
            -- Ice breaks! Transform tile, apply effects to entity
            -- Tile transformation might need to happen at world level
            return {
                 sound = "ice_shatter_loud",
                 volume = 1.2,
                 message = "The ice cracks and shatters beneath you!",
                 effects = {
                     {type="apply_status", status="falling_through_ice", duration=5},
                     {type="apply_status", status="submerged", duration=10},
                     {type="apply_status", status="freezing", duration=60},
                      -- {type="world_event", event="transform_tile", target=tile, new_type="shallow_water"} -- Signal world to change tile
                 }
            }
        end

        -- Check for slipping
        local slipperiness = tile.slipperiness or 0.8
        if math.random() < (slipperiness * 0.8) then -- High chance based on slipperiness
            sound = "slip_skitter"
             table.insert(effects, {type="apply_status", status="slipping", duration=2, magnitude=slipperiness}) -- Slip effect, maybe forced movement/loss of turn?
             return {
                 sound = sound,
                 volume = 0.9,
                 message = "You slip on the slick ice!",
                 effects = effects
             }
        end

        return {
            sound = sound,
            volume = 0.8,
            effects = effects -- No effects if not breaking or slipping
        }
    end,
    
    -- Effect when discovering ice
    onDiscover = function(tile, entity)
        if entity.journal and entity.journal.addDiscovery then
             entity.journal.addDiscovery({
                type = "terrain",
                name = "Frozen Surface",
                location = {x = tile.x, y = tile.y},
                notes = "A body of water frozen solid. Appears slippery and potentially dangerous."
            })
        end
        return {
            message = "You find the water here is frozen solid.",
            effects = {
                {type = "reveal_map", radius = 2}
            }
        }
    end
}

function IceTile.init(world)
    print("Ice tile module initialized")
    -- Register with weather/environment systems
     if world and world.systems and world.systems.weather then
        world.systems.weather:registerFreezableSurface("ice", {meltPoint=0})
    end
     if world and world.systems and world.systems.pathfinding then
        world.systems.pathfinding:registerHazardousTerrain("ice", {slip_chance=0.3, break_chance=0.05}) -- Base chances
    end
end

return IceTile
