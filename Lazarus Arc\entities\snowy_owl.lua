local SnowyOwl = {
    id = "snowy_owl",
    name = "Snowy Owl",
    type = "bird",
    
    -- Entity categories
    categories = {"animal", "bird", "flying", "predator", "arctic"},
    
    -- Threat and food categories
    threatCategories = {"player", "predator"},
    foodCategories = {"small_prey", "medium_prey", "bird", "rodent"},
    
    -- Stats
    maxHealth = 45,
    health = 45,
    maxStamina = 70,
    stamina = 70,
    speed = 2.2,
    
    -- Flight properties
    flight = {
        maxHeight = 16,
        minHeight = 1,
        ascentSpeed = 1.0,
        descentSpeed = 2.0,
        hoverHeight = 4,
        currentHeight = 4,
        wingFlapRate = 0.08,
        soarChance = 0.5,
        silentFlight = true
    },
    
    -- Arctic properties
    arctic = {
        coldResistance = 1.0,
        snowCamouflage = true,
        nightVision = true
    },
    
    -- Behaviors
    behaviors = {"hunt", "patrol", "roost", "territorial"},
    
    -- Behavior configurations
    behaviorConfigs = {
        hunt = {
            moveSpeed = 2.5,
            searchRadius = 40,
            diveSpeed = 4.0,
            attackRange = 2,
            damage = 25,
            preferredPrey = {"rabbit", "lemming", "bird", "squirrel"},
            nightVision = true,
            silentHunt = true,
            snowCamouflage = true
        },
        patrol = {
            moveSpeed = 2.2,
            patrolRadius = 50,
            soarChance = 0.5,
            restInterval = {15, 25},
            preferredTime = "night"
        },
        roost = {
            startTime = "dawn",
            endTime = "dusk",
            roostHeight = {3, 6},
            healthRegen = 0.05,
            staminaRegen = 0.1
        },
        territorial = {
            territoryRadius = 45,
            chaseRadius = 30,
            warningRadius = 20,
            aggressionLevel = 0.8,
            preferredTime = "night"
        }
    },
    
    -- Special abilities
    abilities = {
        dive = {
            speed = 4.0,
            duration = 1.5,
            cooldown = 5,
            staminaCost = 20,
            silent = true
        },
        talonGrip = {
            damage = 20,
            duration = 2,
            cooldown = 3,
            staminaCost = 15
        },
        hoot = {
            range = 25,
            duration = 1.2,
            cooldown = 4,
            effect = "fear",
            nightBoost = true
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "snowy_owl",
        scale = 1.1,
        animations = {
            "idle", "fly", "dive", "attack", "hoot", "roost"
        },
        variants = {
            "white", "spotted", "gray", "dark"
        }
    },
    
    -- Sound effects
    sounds = {
        hoot = "owl_deep_hoot",
        screech = "owl_screech",
        wingFlap = "owl_wing_flap",
        call = "owl_call"
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.7, quantity = {2, 3}},
        {id = "feather", chance = 0.8, quantity = {2, 4}},
        {id = "talon", chance = 0.4, quantity = {1, 2}},
        {id = "beak", chance = 0.3, quantity = {1, 1}}
    }
}

-- Initialize the entity
function SnowyOwl.init(entity, world)
    -- Copy all fields from SnowyOwl template to entity instance
    for k, v in pairs(SnowyOwl) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random owl variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function SnowyOwl.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update flight properties
    if entity.flight then
        -- Update wing flapping based on height and activity
        if entity.flight.currentHeight > entity.flight.minHeight then
            if entity.flight.soarChance > math.random() then
                entity.flight.wingFlapRate = 0.04
            else
                entity.flight.wingFlapRate = 0.08
            end
        else
            entity.flight.wingFlapRate = 0.15
        end

        -- Adjust silent flight based on time of day and weather
        if world then
            if world.timeOfDay == "night" or world.weather == "snow" then
                entity.flight.silentFlight = true
            else
                entity.flight.silentFlight = false
            end
        end
    end

    -- Update arctic properties
    if entity.arctic then
        -- Adjust camouflage based on weather
        if world and world.weather == "snow" then
            entity.arctic.snowCamouflage = true
        else
            entity.arctic.snowCamouflage = false
        end
    end
end

return SnowyOwl 