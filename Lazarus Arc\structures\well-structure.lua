-- structures/well.lua
-- Simple well structure definition

local Well = {
    id = "well",
    name = "Well",
    category = "utility",
    
    -- Physical properties
    width = 3,  -- Tiles wide
    height = 3, -- Tiles tall
    solid = true,
    providesShelter = false,
    
    -- Structure properties
    hasInterior = false,
    waterLevel = 0.8, -- 0.0 = dry, 1.0 = full
    waterQuality = 0.9, -- 0.0 = contaminated, 1.0 = pure
    
    -- Spawn properties
    spawnChance = {
        village = 0.7,
        plains = 0.1,
        forest = 0.05,
        desert = 0.15
    },
    
    -- Spawn requirements
    requirements = {
        minDistanceFromOtherStructures = 10,
        validTiles = {"grass", "dirt", "sand"},
        invalidTiles = {"water", "lava", "void", "stone"},
        needsFlatGround = true,
        preferredNearSettlement = true
    },
    
    -- Variant types for this structure
    variants = {
        "stone", -- Stone well
        "wooden", -- Wooden well
        "ornate", -- Decorated well
        "ancient" -- Very old well
    },
    
    -- Tile layout
    tileLayout = {
        "www",
        "wcw",
        "www"
    },
    
    -- Tile legend
    tileLegend = {
        w = "well_wall",
        c = "well_center"
    },
    
    -- Tags for search/filtering
    tags = {"water", "utility", "settlement"},
    
    -- Interactions available with this structure
    interactions = {
        {
            name = "draw_water",
            position = {x = 1, y = 1}, -- Center of well
            prompt = "Draw Water",
            action = function(structure, entity, world)
                -- Check if well has water
                if structure.waterLevel <= 0 then
                    return {
                        success = false,
                        message = "The well is dry. There's no water to draw."
                    }
                end
                
                -- Check if player has a container
                local hasContainer = false
                if entity.inventory and (
                   entity.inventory.hasItem("water_flask") or
                   entity.inventory.hasItem("bucket") or
                   entity.inventory.hasItem("empty_bottle")) then
                    hasContainer = true
                end
                
                if not hasContainer then
                    return {
                        success = false,
                        message = "You don't have anything to carry water in."
                    }
                end
                
                -- Determine water quality effect
                local qualityEffect = ""
                if structure.waterQuality < 0.3 then
                    qualityEffect = "The water looks and smells foul. Drinking it would be dangerous."
                elseif structure.waterQuality < 0.7 then
                    qualityEffect = "The water is somewhat cloudy, but seems drinkable."
                else
                    qualityEffect = "The water is clear and fresh."
                end
                
                -- Determine which container was used
                local containerUsed = ""
                if entity.inventory.hasItem("water_flask") then
                    containerUsed = "water_flask"
                elseif entity.inventory.hasItem("bucket") then
                    containerUsed = "bucket"
                else
                    containerUsed = "empty_bottle"
                end
                
                -- Fill the container
                local filledItem = ""
                if containerUsed == "water_flask" then
                    filledItem = "filled_water_flask"
                elseif containerUsed == "bucket" then
                    filledItem = "water_bucket"
                else
                    filledItem = "water_bottle"
                end
                
                -- Remove empty container, add filled one
                entity.inventory.removeItem(containerUsed, 1)
                entity.inventory.addItem(filledItem, 1)
                
                -- Slightly reduce well water level
                structure.waterLevel = math.max(0, structure.waterLevel - 0.01)
                
                return {
                    success = true,
                    message = "You draw water from the well. " .. qualityEffect,
                    effects = {}
                }
            end
        },
        {
            name = "inspect",
            prompt = "Examine Well",
            action = function(structure, entity, world)
                local age = ""
                if structure.variant == "ancient" then
                    age = "ancient"
                elseif structure.variant == "ornate" then
                    age = "ornately decorated"
                else
                    age = structure.variant
                end
                
                local waterDesc = ""
                if structure.waterLevel <= 0 then
                    waterDesc = "It appears to be dry."
                elseif structure.waterLevel < 0.3 then
                    waterDesc = "The water level is very low."
                elseif structure.waterLevel < 0.7 then
                    waterDesc = "It contains a moderate amount of water."
                else
                    waterDesc = "It's filled with water."
                end
                
                return {
                    success = true,
                    message = "An " .. age .. " well built to access groundwater. " .. waterDesc
                }
            end
        },
        {
            name = "drop_coin",
            position = {x = 1, y = 1}, -- Center of well
            prompt = "Drop Coin for Luck",
            condition = function(structure, entity, world)
                return entity.inventory and entity.inventory.hasItem("coin")
            end,
            action = function(structure, entity, world)
                -- Remove a coin
                entity.inventory.removeItem("coin", 1)
                
                -- Add to well's coin count
                structure.coinCount = (structure.coinCount or 0) + 1
                
                -- Determine effect based on well properties
                local effectRoll = math.random()
                
                -- Special effects if well is magical
                if structure.isMagical then
                    if effectRoll < 0.3 then
                        -- Minor blessing
                        return {
                            success = true,
                            message = "You drop a coin into the well and make a wish. You feel a subtle warmth flow through you.",
                            effects = {
                                {type = "apply_status", status = "lucky", duration = 600}
                            }
                        }
                    elseif effectRoll < 0.5 then
                        -- Restore some health
                        return {
                            success = true,
                            message = "You drop a coin into the well and make a wish. You feel refreshed and restored.",
                            effects = {
                                {type = "heal", amount = 10}
                            }
                        }
                    else
                        -- Just a regular coin drop
                        return {
                            success = true,
                            message = "You drop a coin into the well and make a wish. It splashes into the water below."
                        }
                    end
                else
                    -- Normal well - just superstition
                    if structure.coinCount > 10 and effectRoll < 0.1 then
                        -- Many coins sometimes create a minor magical effect
                        return {
                            success = true,
                            message = "You drop a coin into the well and make a wish. Surprisingly, you feel slightly luckier.",
                            effects = {
                                {type = "apply_status", status = "lucky", duration = 300}
                            }
                        }
                    else
                        return {
                            success = true,
                            message = "You drop a coin into the well and make a wish. It splashes into the water below."
                        }
                    end
                end
            end
        },
        {
            name = "peer_down",
            position = {x = 1, y = 1}, -- Center of well
            prompt = "Peer Down Well",
            action = function(structure, entity, world)
                local message = "You peer down into the darkness of the well."
                
                if structure.waterLevel <= 0 then
                    message = message .. " It appears completely dry."
                else
                    message = message .. " You can see water far below."
                    
                    -- Chance to see something interesting
                    local perceptionCheck = math.random()
                    local perceptionBonus = 0
                    
                    if entity.attributes and entity.attributes.perception then
                        perceptionBonus = entity.attributes.perception / 20 -- 0.05 per point
                    end
                    
                    if perceptionCheck < 0.15 + perceptionBonus then
                        -- See something
                        if structure.coinCount and structure.coinCount > 5 then
                            message = message .. " Coins glint at the bottom, catching what little light reaches down there."
                        elseif structure.isMagical then
                            message = message .. " The water seems to shimmer in an unusual way."
                        elseif structure.hasSecretItem then
                            message = message .. " You notice something unusual at the bottom, but can't quite make it out."
                        end
                    end
                end
                
                return {
                    success = true,
                    message = message
                }
            end
        }
    },
    
    -- Initialize the structure with specific properties
    init = function(structure, world, x, y)
        -- Set structure position
        structure.x = x
        structure.y = y
        
        -- Choose random variant if none specified
        structure.variant = structure.variant or Well.variants[math.random(#Well.variants)]
        
        -- Adjust properties based on variant
        if structure.variant == "ancient" then
            structure.waterQuality = 0.3 + math.random() * 0.4 -- Lower quality
            structure.waterLevel = 0.2 + math.random() * 0.6 -- Variable level
            structure.isMagical = math.random() < 0.4 -- 40% chance to be magical
            
            -- Update the tile legend to reflect age
            structure.tileLegend.w = "ancient_well_wall"
            structure.tileLegend.c = "ancient_well_center"
        elseif structure.variant == "wooden" then
            structure.waterQuality = 0.6 + math.random() * 0.3 -- Decent quality
            structure.waterLevel = 0.5 + math.random() * 0.5 -- Good level
            structure.isMagical = math.random() < 0.1 -- 10% chance to be magical
            
            structure.tileLegend.w = "wooden_well_wall"
            structure.tileLegend.c = "wooden_well_center"
        elseif structure.variant == "stone" then
            structure.waterQuality = 0.7 + math.random() * 0.3 -- Good quality
            structure.waterLevel = 0.7 + math.random() * 0.3 -- High level
            structure.isMagical = math.random() < 0.2 -- 20% chance to be magical
            
            structure.tileLegend.w = "stone_well_wall"
            structure.tileLegend.c = "stone_well_center"
        elseif structure.variant == "ornate" then
            structure.waterQuality = 0.8 + math.random() * 0.2 -- Very good quality
            structure.waterLevel = 0.8 + math.random() * 0.2 -- High level
            structure.isMagical = math.random() < 0.5 -- 50% chance to be magical
            
            structure.tileLegend.w = "ornate_well_wall"
            structure.tileLegend.c = "ornate_well_center"
        end
        
        -- Small chance of a secret item
        structure.hasSecretItem = math.random() < 0.1
        if structure.hasSecretItem then
            -- Determine what kind of item
            local itemRoll = math.random()
            if itemRoll < 0.3 then
                structure.secretItem = "lost_jewelry"
            elseif itemRoll < 0.6 then
                structure.secretItem = "ancient_coin"
            elseif itemRoll < 0.9 then
                structure.secretItem = "magical_trinket"
            else
                structure.secretItem = "sealed_bottle"
            end
        end
        
        -- Initialize coin count
        structure.coinCount = 0
        
        return structure
    end,
    
    -- Update function (for time-based events like water replenishing)
    update = function(structure, world, dt)
        -- Slowly replenish water if there's rainfall
        if world.weather and world.weather.isRaining and structure.waterLevel < 1.0 then
            structure.waterLevel = math.min(1.0, structure.waterLevel + 0.001 * dt)
        end
    end,
    
    -- Draw the structure (for debugging/visualization)
    draw = function(structure, renderer)
        -- This would be implemented in the game's rendering system
        print("Drawing " .. structure.name .. " at " .. structure.x .. "," .. structure.y)
    end
}

return Well