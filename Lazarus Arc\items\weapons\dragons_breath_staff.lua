local DragonsBreathStaff = {
    id = "dragons_breath_staff",
    name = "Dragon's Breath Staff",
    type = "weapon",
    category = "staff",
    
    -- Properties
    properties = {
        damage = 45,
        attackSpeed = 1.0,
        range = 6.0,
        durability = 250,
        maxDurability = 250,
        fireballSpeed = 12,
        fireballSize = 1.5,
        fireballSpread = 0.2,
        dragonFireChance = 0.3,
        dragonFireDamage = 25,
        dragonFireDuration = 5,
        dragonFireRadius = 2.5,
        dragonFireSpeed = 0.8,
        dragonRoarChance = 0.2,
        dragonRoarDamage = 35,
        dragonRoarRadius = 4.0,
        dragonRoarDuration = 3,
        dragonRoarForce = 3.0,
        dragonScaleChance = 0.15,
        dragonScaleDamage = 20,
        dragonScaleCount = 3,
        dragonScaleSpread = 0.3,
        dragonCharge = 0,
        maxDragonCharge = 100,
        chargeRate = 4,
        dischargeRate = 8,
        dragonResistance = 0.8,
        temperature = 2.0,
        heatRadius = 3.0
    },
    
    -- Appearance
    appearance = {
        sprite = "dragons_breath_staff",
        scale = 1.0,
        animations = {
            "idle",
            "cast",
            "roar",
            "charge"
        },
        variants = {
            "red",
            "gold",
            "crimson",
            "ancient"
        },
        blendMode = "add",
        tint = {1.0, 0.4, 0.0},
        alpha = 0.9
    },
    
    -- Sound effects
    sounds = {
        cast = "dragons_breath_cast",
        roar = "dragons_breath_roar",
        fire = "dragons_breath_fire",
        charge = "dragons_breath_charge"
    },
    
    -- Effects
    effects = {
        dragonFire = {
            type = "status",
            duration = 5,
            effects = {
                damage = 25,
                burn = true,
                radius = 2.5
            }
        },
        dragonRoar = {
            type = "status",
            duration = 3,
            effects = {
                damage = 35,
                knockback = 3.0,
                radius = 4.0
            }
        },
        dragonScale = {
            type = "status",
            duration = 0.5,
            effects = {
                damage = 20,
                count = 3
            }
        }
    }
}

-- Initialize the weapon
function DragonsBreathStaff.init(weapon, world)
    -- Copy all fields from DragonsBreathStaff template to weapon instance
    for k, v in pairs(DragonsBreathStaff) do
        if type(v) ~= "function" and weapon[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                weapon[k] = {}
                for subk, subv in pairs(v) do
                    weapon[k][subk] = subv
                end
            else
                weapon[k] = v
            end
        end
    end

    -- Set random variant
    if weapon.appearance and weapon.appearance.variants then
        local variants = weapon.appearance.variants
        weapon.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize weapon state
    weapon.properties.weaponState = {
        durability = weapon.properties.maxDurability,
        lastAttackTime = 0,
        dragonCharge = 0,
        lastChargeTime = 0,
        lastRoarTime = 0
    }

    return weapon
end

-- Update the weapon
function DragonsBreathStaff.update(weapon, world, dt)
    -- Update weapon state
    if weapon.properties.weaponState then
        -- Update dragon charge
        if world.temperature and world.temperature > 0.7 then
            local chargeFactor = (world.temperature * 0.7 + 0.3) * dt
            weapon.properties.weaponState.dragonCharge = math.min(weapon.properties.maxDragonCharge,
                weapon.properties.weaponState.dragonCharge + chargeFactor * weapon.properties.chargeRate)
        else
            weapon.properties.weaponState.dragonCharge = math.max(0,
                weapon.properties.weaponState.dragonCharge - weapon.properties.dischargeRate * dt)
        end
        
        -- Create heat effect
        if world.createEffect then
            world.createEffect({
                type = "heat",
                position = weapon.position,
                radius = weapon.properties.heatRadius,
                duration = dt
            })
        end
    end
end

-- Handle attack
function DragonsBreathStaff.attack(weapon, world, attacker, target)
    -- Check cooldown
    if world.time - weapon.properties.weaponState.lastAttackTime < 1 / weapon.properties.attackSpeed then
        return false
    end
    
    -- Calculate fireball direction
    local angle = math.atan2(
        target.position.y - attacker.position.y,
        target.position.x - attacker.position.x
    )
    
    -- Add spread to fireball direction
    local spread = (math.random() - 0.5) * weapon.properties.fireballSpread
    angle = angle + spread
    
    -- Create fireball projectile
    if world.createProjectile then
        world.createProjectile({
            type = "dragon_fireball",
            position = attacker.position,
            direction = {
                x = math.cos(angle) * weapon.properties.fireballSpeed,
                y = math.sin(angle) * weapon.properties.fireballSpeed
            },
            damage = weapon.properties.damage,
            range = weapon.properties.range,
            size = weapon.properties.fireballSize
        })
    end
    
    -- Check for dragon fire effect
    if math.random() < weapon.properties.dragonFireChance then
        -- Create dragon fire effect
        if world.createEffect then
            world.createEffect({
                type = "dragon_fire",
                position = target.position,
                radius = weapon.properties.dragonFireRadius,
                duration = weapon.properties.dragonFireDuration
            })
        end
        
        -- Apply dragon fire effect
        if target.applyEffect then
            target.applyEffect(weapon.effects.dragonFire)
        end
        
        -- Play fire sound
        if world.playSound then
            world.playSound(weapon.sounds.fire)
        end
    end
    
    -- Check for dragon roar effect
    if math.random() < weapon.properties.dragonRoarChance then
        -- Create dragon roar effect
        if world.createEffect then
            world.createEffect({
                type = "dragon_roar",
                position = target.position,
                radius = weapon.properties.dragonRoarRadius,
                duration = weapon.properties.dragonRoarDuration
            })
        end
        
        -- Apply dragon roar effect to nearby entities
        if world.entities then
            for _, entity in ipairs(world.entities) do
                if entity.position then
                    local distance = math.sqrt(
                        (entity.position.x - target.position.x)^2 + 
                        (entity.position.y - target.position.y)^2
                    )
                    
                    if distance <= weapon.properties.dragonRoarRadius then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.dragonRoar)
                        end
                        
                        -- Apply knockback
                        if entity.move then
                            local force = (1 - distance / weapon.properties.dragonRoarRadius) * 
                                weapon.properties.dragonRoarForce
                            local angle = math.atan2(
                                entity.position.y - target.position.y,
                                entity.position.x - target.position.x
                            )
                            entity.move({
                                x = math.cos(angle) * force,
                                y = math.sin(angle) * force
                            })
                        end
                    end
                end
            end
        end
        
        -- Play roar sound
        if world.playSound then
            world.playSound(weapon.sounds.roar)
        end
    end
    
    -- Check for dragon scale effect
    if math.random() < weapon.properties.dragonScaleChance then
        -- Create dragon scale projectiles
        for i = 1, weapon.properties.dragonScaleCount do
            local scaleAngle = angle + (i - 1) * (weapon.properties.dragonScaleSpread / (weapon.properties.dragonScaleCount - 1))
            
            if world.createProjectile then
                world.createProjectile({
                    type = "dragon_scale",
                    position = target.position,
                    direction = {
                        x = math.cos(scaleAngle) * weapon.properties.fireballSpeed * 0.8,
                        y = math.sin(scaleAngle) * weapon.properties.fireballSpeed * 0.8
                    },
                    damage = weapon.properties.dragonScaleDamage,
                    range = weapon.properties.range * 0.7
                })
            end
        end
    end
    
    -- Update last attack time
    weapon.properties.weaponState.lastAttackTime = world.time
    
    -- Reduce durability
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 1)
    
    return true
end

-- Handle special attack
function DragonsBreathStaff.specialAttack(weapon, world, attacker, target)
    -- Check if weapon has enough dragon charge
    if weapon.properties.weaponState.dragonCharge < 50 then
        return false
    end
    
    -- Create dragon breath effect
    if world.createEffect then
        world.createEffect({
            type = "dragon_breath",
            position = attacker.position,
            radius = weapon.properties.dragonFireRadius * 3,
            duration = 6,
            damage = weapon.properties.damage * 2
        })
    end
    
    -- Apply dragon effects to nearby entities
    if world.entities then
        for _, entity in ipairs(world.entities) do
            if entity.position then
                local distance = math.sqrt(
                    (entity.position.x - attacker.position.x)^2 + 
                    (entity.position.y - attacker.position.y)^2
                )
                
                if distance <= weapon.properties.dragonFireRadius * 3 then
                    -- Apply random dragon effect
                    local effect = math.random()
                    if effect < 0.4 then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.dragonFire)
                        end
                    elseif effect < 0.7 then
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.dragonRoar)
                        end
                    else
                        if entity.applyEffect then
                            entity.applyEffect(weapon.effects.dragonScale)
                        end
                    end
                end
            end
        end
    end
    
    -- Reset dragon charge
    weapon.properties.weaponState.dragonCharge = 0
    weapon.properties.weaponState.lastChargeTime = world.time
    
    -- Reduce durability more than normal attack
    weapon.properties.weaponState.durability = math.max(0,
        weapon.properties.weaponState.durability - 5)
    
    return true
end

return DragonsBreathStaff 