local Carp = {
    id = "carp",
    name = "Common Carp",
    type = "fish",

    -- Entity categories
    categories = { "animal", "fish", "freshwater", "bottom_feeder", "social" },

    -- Threat categories
    threatCategories = { "player", "predator" },
    foodCategories = { "plant", "insect", "carrion", "small_prey" },

    -- Stats
    maxHealth = 35,
    health = 35,
    maxStamina = 55,
    stamina = 55,
    speed = 1.5,

    -- Water properties
    water = {
        swimSpeed = 1.5,
        maxDepth = 8,
        preferredDepth = 5,
        oxygenLevel = 1.0,
        temperature = "warm",
        bottomFeeder = true
    },

    -- Behaviors
    behaviors = { "forage", "patrol", "school", "bottom_feed" },

    -- Behavior configurations
    behaviorConfigs = {
        forage = {
            moveSpeed = 1.2,
            searchRadius = 12,
            preferredFood = { "plant", "insect", "carrion" },
            successChance = 0.7,
            bottomBonus = 0.15
        },
        patrol = {
            moveSpeed = 1.3,
            patrolRadius = 15,
            preferredDepth = 5,
            restInterval = { 8, 12 }
        },
        school = {
            moveSpeed = 1.4,
            followDistance = 2,
            separationDistance = 1.5,
            alignmentStrength = 0.4,
            cohesionStrength = 0.5,
            maxGroupSize = 10
        },
        bottom_feed = {
            moveSpeed = 0.8,
            searchRadius = 8,
            preferredFood = { "plant", "carrion" },
            successChance = 0.8,
            preferredTerrain = "mud"
        }
    },

    -- Special abilities
    abilities = {
        mudStir = {
            range = 4,
            duration = 2,
            cooldown = 3,
            effect = "reveal",
            staminaCost = 5
        },
        tailSwipe = {
            range = 2,
            duration = 0.3,
            cooldown = 2,
            effect = "stun"
        },
        schoolBoost = {
            speed = 1.3,
            duration = 4,
            cooldown = 6,
            effect = "group"
        }
    },

    -- Appearance
    appearance = {
        sprite = "carp",
        scale = 1.1,
        animations = {
            "idle", "swim", "bottom_feed", "school"
        },
        variants = {
            "brown", "gold", "mirror", "leather"
        }
    },

    -- Sound effects
    sounds = {
        splash = "fish_splash",
        swim = "fish_swim",
        mudStir = "fish_mud_stir"
    },

    -- Loot drops
    drops = {
        { id = "meat", chance = 0.6, quantity = { 1, 2 } },
        { id = "scale", chance = 0.5, quantity = { 1, 3 } },
        { id = "fin", chance = 0.3, quantity = { 1, 1 } },
        { id = "bone", chance = 0.4, quantity = { 1, 1 } }
    }
}

-- Initialize the entity
function Carp.init(entity, world)
    -- Copy all fields from Carp template to entity instance
    for k, v in pairs(Carp) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or { x = 0, y = 0 }
    entity.velocity = entity.velocity or { x = 0, y = 0 }

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random carp variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Carp.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update water properties
    if entity.water then
        -- Adjust foraging success based on time of day
        if world and world.timeOfDay == "dawn" or world.timeOfDay == "dusk" then
            entity.behaviorConfigs.forage.successChance = 0.8
        else
            entity.behaviorConfigs.forage.successChance = 0.7
        end

        -- Adjust preferred depth based on time of day
        if world and world.timeOfDay == "night" then
            entity.water.preferredDepth = 3
        else
            entity.water.preferredDepth = 5
        end

        -- Apply bottom feeding bonus
        if entity.water.bottomFeeder then
            entity.behaviorConfigs.forage.successChance = 
                entity.behaviorConfigs.forage.successChance + 
                entity.behaviorConfigs.forage.bottomBonus
        end
    end
end

return Carp