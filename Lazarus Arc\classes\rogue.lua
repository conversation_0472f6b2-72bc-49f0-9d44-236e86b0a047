-- classes/rogue.lua
-- Rogue class definition for Lazarus Arc

local Rogue = {
    name = "<PERSON>",
    description = "A nimble expert in stealth and precision strikes, dealing massive damage from the shadows.",
    icon = "rogue_icon.png",

    -- 🎭 **Playstyle & Trade-offs**
    pros = {
        "Extremely high burst damage from stealth",
        "Evasive and difficult to hit",
        "Utility-focused with traps, poisons, and locks"
    },
    cons = {
        "Very fragile—low defense and health",
        "Weak when caught in direct combat",
        "Limited effectiveness against enemies with detection abilities"
    },

    -- 🏋 **Base Stats & Growth**
    base_stats = { 
        health = 90, 
        maxHealth = 90,
        stamina = 80, 
        maxStamina = 80,
        mana = 40, 
        maxMana = 40,
        pattack = 12, 
        pdefense = 4, 
        mattack = 6, 
        mdefense = 5, 
        speed = 9, 
        luck = 7 
    },

    stat_growth = {
        health = 8,
        stamina = 8,
        mana = 3,
        pattack = 1.2,
        pdefense = 0.3,
        mattack = 0.4,
        mdefense = 0.4,
        speed = 0.9,
        luck = 0.7
    },

    -- ⚔️ **Weapon Proficiency**
    proficiencies = { 
        sword = 1.8, 
        dagger = 2.0,
        greatsword = 0.7, 
        bow = 1.5, 
        gun = 1.0, 
        magic = 0.6 
    },

    -- 🌿 **Elemental Affinities**
    element_affinities = {
        dark = 10,  -- Increased stealth efficiency
        air = 5     -- Quick reflexes and evasiveness
    },

    -- 💥 **Overdrive Abilities**
    overdrive = { 
        "phantom_step", 
        "shadow_ambush", 
        "deadly_chain" 
    },

    -- 🎯 **Starting Skills**
    starting_skills = {
        "backstab",
        "evade",
        "pickpocket"
    },

    -- 📈 **Skill Progression**
    skill_tree = {
        tier1 = {
            "stealth",
            "poison_blade",
            "lockpicking"
        },
        tier2 = {
            "assassinate",
            "smoke_bomb", 
            "shadow_step",
            "disarm_trap"
        },
        tier3 = {
            "death_mark",
            "vanish",
            "perfect_strike"
        }
    },

    -- 🎭 **Prestige Paths**
    prestige_paths = {
        assassin = {
            description = "A master of elimination, specializing in precise and deadly attacks.",
            bonuses = {
                "Massively increased critical hit damage",
                "Faster stealth movement",
                "Can silence enemies"
            },
            weaknesses = {
                "No real defense",
                "Limited crowd control abilities"
            }
        },

        shadowblade = {
            description = "A rogue who blends darkness magic with their strikes.",
            bonuses = {
                "Can teleport short distances in shadow",
                "Dark-based attacks deal additional damage",
                "Can regenerate health in darkness"
            },
            weaknesses = {
                "Magic-based abilities consume mana",
                "Weaker against holy and light-based enemies"
            }
        },

        thief = {
            description = "A master of deception and misdirection.",
            bonuses = {
                "Higher loot quality from stolen items",
                "Can disable traps and pick advanced locks",
                "Increased movement speed while sneaking"
            },
            weaknesses = {
                "Less raw combat power",
                "Heavily dependent on preparation"
            }
        }
    }
}

return Rogue
