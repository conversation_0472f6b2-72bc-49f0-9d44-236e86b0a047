local ForestGolem = {
    id = "forest_golem",
    name = "Forest Golem",
    type = "golem",

    -- Entity categories
    categories = {"monster", "golem", "nature", "large"},

    -- Threat and food categories
    threatCategories = {"player", "monster"},
    foodCategories = {"none"},

    -- Stats
    maxHealth = 120,
    health = 120,
    maxStamina = 80,
    stamina = 80,
    speed = 1.5,

    -- Nature properties
    nature = {
        growthRate = 0.1,
        maxGrowth = 1.5,
        currentGrowth = 1.0,
        healingRate = 0.5,
        vineLength = 5
    },

    -- Behaviors
    behaviors = {"patrol", "defend", "grow", "summon"},

    -- Behavior configurations
    behaviorConfigs = {
        patrol = {
            moveSpeed = 1.2,
            patrolRadius = 30,
            restTime = {5, 10},
            growthCheck = 0.1
        },
        defend = {
            moveSpeed = 2.0,
            detectionRadius = 20,
            attackRange = 4,
            damage = 25,
            vineWhipRange = 8
        },
        grow = {
            growthInterval = 60,
            growthAmount = 0.1,
            maxSize = 1.5,
            vineSpawnChance = 0.3
        },
        summon = {
            summonRadius = 15,
            summonInterval = 30,
            summonTypes = {"vine", "sapling"},
            maxMinions = 3
        }
    },

    -- Special abilities
    abilities = {
        vineWhip = {
            damage = 20,
            range = 8,
            cooldown = 5,
            staminaCost = 15
        },
        rootGrasp = {
            damage = 10,
            duration = 3,
            cooldown = 8,
            staminaCost = 20
        },
        natureHeal = {
            healAmount = 30,
            radius = 5,
            cooldown = 15,
            staminaCost = 25
        }
    },

    -- Appearance
    appearance = {
        sprite = "forest_golem",
        scale = 1.5,
        animations = {
            "idle", "walk", "attack", "grow", "summon"
        },
        variants = {
            "oak", "pine", "maple", "ancient"
        }
    },

    -- Sound effects
    sounds = {
        groan = "golem_groan",
        creak = "golem_creak",
        grow = "golem_grow"
    },

    -- Loot drops
    drops = {
        {id = "wood", chance = 0.8, quantity = {3, 5}},
        {id = "vine", chance = 0.6, quantity = {2, 4}},
        {id = "seed", chance = 0.4, quantity = {1, 2}},
        {id = "heart_of_nature", chance = 0.1, quantity = {1, 1}}
    }
}

-- Initialize the entity
function ForestGolem.init(entity, world)
    -- Copy all fields from ForestGolem template to entity instance
    for k, v in pairs(ForestGolem) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random golem variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function ForestGolem.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update growth
    if entity.nature and entity.nature.currentGrowth < entity.nature.maxGrowth then
        entity.nature.currentGrowth = math.min(
            entity.nature.maxGrowth,
            entity.nature.currentGrowth + entity.nature.growthRate * dt
        )
        -- Update scale based on growth
        if entity.appearance then
            entity.appearance.scale = 1.5 * entity.nature.currentGrowth
        end
    end

    -- Natural healing
    if entity.health < entity.maxHealth then
        entity.health = math.min(
            entity.maxHealth,
            entity.health + entity.nature.healingRate * dt
        )
    end
end

return ForestGolem
