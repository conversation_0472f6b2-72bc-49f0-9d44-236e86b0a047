-- biomes/kingdom_ruin.lua
-- Store the world core reference at module level
local worldCoreRef = nil

-- Forward declarations of functions
local generateFeatures
local populateEntities
local generateStructures
local postProcess
local generate

-- Biome definition
local KingdomRuin = {
    id = "kingdom_ruin",
    name = "Kingdom Ruin",
    description = "The vast, crumbling remains of a significant structure or settlement from a past kingdom, such as a castle, fortress, or city district.",

    -- Environmental factors (Often reflects surrounding area, but with dust, stillness, potential magic residue)
    environment = {
        humidity = 0.5,
        temperature = 0.5, -- Can vary, might be cooler due to stone mass/shade
        sunlight = 0.6,    -- Often partially blocked by remaining structures
        windStrength = 0.2, -- Often sheltered within walls
        airQuality = 0.7,  -- Dusty
        magicResidue = 0.3 -- Lingering magic potential (0-1)
    },

    -- Core tile types used in this biome
    -- NOTE: Uses 'ruins' heavily. May need 'unstable_floor', 'pitfall'. Uses 'grass', 'dirt', 'stone'.
    primaryTiles = {"ruins"}, -- Dominated by ruin tiles
    secondaryTiles = {"stone", "dirt", "grass"}, -- Foundations, overgrown areas
    rareTiles = {"unstable_floor", "pitfall", "ancient_technology", "treasure_vault_tile"}, -- Hazards, tech, special rooms

    -- Default proportions (Adjustable through variants)
    tileDistribution = {
        ruins = 0.7,
        stone = 0.1, -- Exposed foundations/paving
        dirt = 0.08, -- Areas where stone has crumbled to earth
        grass = 0.05, -- Overgrown sections
        unstable_floor = 0.03,
        pitfall = 0.02,
        ancient_technology = 0.01,
        treasure_vault_tile = 0.01
    },

    -- Entities common to this biome (Ghosts, scavengers, guardians)
    commonEntities = {
        "rubble_crawler", "giant_rat", "ghost_minor", "shade" -- Shade from ruins tile [cite: 44]
    },
    uncommonEntities = {
        "memory_fragment", "forgotten_guardian", "skeleton_warrior", "scavenger_humanoid" -- From ruins tile [cite: 44] + others
    },
    rareEntities = {
        "royal_ghost", "knight_specter", "ruined_golem", "artifact_hunter", "treasure_mimic"
    },

    -- Biome variants for diverse generation
    variants = {
        ruined_castle = {
            name = "Ruined Castle",
            structureType = "Castle", -- Hint for generation
            tileAdjustments = { ruins=0.75, stone=0.15, pitfall=0.05 }, -- More stone, defensive pitfalls
            entityAdjustments = { knight_specter=1.5, royal_ghost=1.0, skeleton_warrior=1.2 },
            specialFeatures = {"keep_remains", "collapsed_battlements", "dungeon_entrance"}
        },
        abandoned_city_district = {
            name = "Abandoned City District",
            structureType = "City",
            tileAdjustments = { ruins=0.6, stone=0.05, dirt=0.15, grass=0.1, unstable_floor=0.05 }, -- More overgrown/decayed
            entityAdjustments = { ghost_minor=1.5, giant_rat=1.3, scavenger_humanoid=1.1 },
            specialFeatures = {"market_square_rubble", "crumbling_houses", "overgrown_plaza"}
        },
        fallen_fortress = {
            name = "Fallen Fortress",
            structureType = "Fortress",
            tileAdjustments = { ruins=0.7, stone=0.2, unstable_floor=0.05 }, -- Very solid construction remnants
            entityAdjustments = { forgotten_guardian=1.5, ruined_golem=1.2, skeleton_warrior=1.0 },
            specialFeatures = {"broken_gatehouse", "barracks_foundation", "armory_debris"}
        },
        cursed_temple_ruins = {
             name = "Cursed Temple Ruins",
             structureType = "Temple",
             environment = { magicResidue = 0.7, airQuality = 0.4 },
             tileAdjustments = { ruins=0.6, stone=0.1, cursed_altar_tile=0.1, unstable_floor=0.1 }, -- Need cursed altar tile
             entityAdjustments = { cursed_spirit=1.5, shade=1.3, dark_priest_ghost=1.0 },
             specialEffects = {"negative_energy_aura", "unsettling_whispers", "lingering_curse"}
        }
    },

    -- Structures (Represent key areas within the larger ruin complex)
    structures = {
        -- These would be placed by the biome generator as points of interest within the ruin layout
        { name = "Main Keep/Building Foundation", chance = 0.5, requiresVariant = "ruined_castle|fallen_fortress" },
        { name = "Collapsed Tower", chance = 0.4 },
        { name = "Throne Room/Main Hall Debris", chance = 0.3 },
        { name = "Hidden Crypt/Vault Entrance", chance = 0.15 },
        { name = "Courtyard Remnants", chance = 0.6 },
        { name = "Library Rubble", chance = 0.2 }
    },

    -- Weather (Standard regional weather, interacts with ruins)
    weather = { -- Inherits from surrounding region, but maybe more sheltered/prone to fog?
        transitions = {
             clear = { clear = 60, cloudy = 30, fog = 5, rain_light = 5 },
             cloudy = { clear = 40, cloudy = 40, fog = 10, rain_light = 10 },
             fog = { clear = 30, cloudy = 30, fog = 40 }, -- Fog lingers in ruins
             rain_light = { cloudy = 60, rain_light = 30, clear = 10 },
             rain_heavy = { rain_light = 70, rain_heavy = 30 }
        },
        default = "clear"
    },
    environmentalEffects = { -- Specific non-weather effects
         dust_particles = 0.5,
         echo = 0.4, -- Echoes within remaining structures
         structural_instability = 0.3 -- General risk modifier
    },

    -- Unique features generation
    features = {
        { name = "lingering_magical_ward", chance = 0.1 * 0.3 }, -- Default magic residue of 0.3
        { name = "hidden_treasure_cache", chance = 0.08 },
        { name = "ghostly_reenactment_event", chance = 0.05, isEvent=true },
        { name = "major_collapse_hazard", chance = 0.1, danger=true }, -- Large area of unstable tiles
        { name = "secret_passage", chance = 0.12 } -- Feature revealing hidden path
    },

    -- Function references (will be set after definitions)
    generateFeatures = nil,
    populateEntities = nil,
    generateStructures = nil,
    postProcess = nil,
    generate = nil,
    
    -- Initialize the biome
    init = function(worldCore)
        print("Kingdom Ruin biome module initialized")
        worldCoreRef = worldCore
        print("Kingdom Ruin biome registered successfully for later use")
    end,

    -- Getter for worldCore
    getWorldCore = function()
        return worldCoreRef
    end
}

-- Function implementations
generateFeatures = function(chunk, world, variant)
    print("Generating Kingdom Ruin features...")
    -- Logic for placing specific named areas, hidden passages, wards, treasure, collapse hazards, secrets etc.
    -- This needs sophisticated logic to place features meaningfully within the ruin layout.
     for _, feature in ipairs(KingdomRuin.features) do
         if math.random() < feature.chance then
              if not feature.requiresVariant or feature.requiresVariant == variant then
                  if not feature.unique or not (chunk.uniqueFeatures and chunk.uniqueFeatures[feature.name]) then
                     local attempts = 0; local placed = false; local fx, fy
                     while attempts < 10 and not placed do
                         fx = math.random(1, world.CHUNK_SIZE - 2)
                         fy = math.random(1, world.CHUNK_SIZE - 2)
                         local tile = chunk.tiles[fx][fy]
                         -- Place features on ruin/stone tiles usually
                         if tile and (tile.type=="ruins" or tile.type=="stone") then placed = true end
                         attempts = attempts + 1
                     end
                     if placed then
                         local fwx = chunk.x * world.CHUNK_SIZE + fx
                         local fwy = chunk.y * world.CHUNK_SIZE + fy
                         print("Placing feature: " .. feature.name .. " at " .. fwx .. "," .. fwy)
                         -- Add specific generation logic per feature
                         if feature.name == "secret_passage" then
                             -- Modify tiles or place trigger entity
                         elseif feature.name == "hidden_treasure_cache" then
                             -- Place treasure entity
                         end
                         if feature.unique then chunk.uniqueFeatures = chunk.uniqueFeatures or {}; chunk.uniqueFeatures[feature.name] = true end
                     end
                 end
             end
         end
     end
end

populateEntities = function(chunk, world, variant)
    print("Populating Kingdom Ruin entities...")
    -- Spawn ghosts, guardians, scavengers etc.
    local variantData = variant and KingdomRuin.variants[variant] or nil
    local function spawnEntity(entityType, count)
        local multiplier = 1.0
        if variantData and variantData.entityAdjustments and variantData.entityAdjustments[entityType] then multiplier = variantData.entityAdjustments[entityType] end
        local adjustedCount = math.floor(count * multiplier + 0.5)
        for i = 1, adjustedCount do
             local attempts = 0; local placed = false
             while attempts < 5 and not placed do
                  local x = math.random(0, world.CHUNK_SIZE - 1)
                  local y = math.random(0, world.CHUNK_SIZE - 1)
                  local tile = chunk.tiles[x][y]
                  if tile and tile.passable then -- Check passability
                       world.entitySystem:addEntity(entityType, chunk.x * world.CHUNK_SIZE + x, chunk.y * world.CHUNK_SIZE + y)
                       placed = true
                  end
                  attempts = attempts + 1
             end
        end
    end
    -- Spawn based on lists
    for _, et in ipairs(KingdomRuin.commonEntities) do spawnEntity(et, math.random(2,6)) end
    for _, et in ipairs(KingdomRuin.uncommonEntities) do spawnEntity(et, math.random(1,4)) end
    for _, et in ipairs(KingdomRuin.rareEntities) do if math.random() < 0.15 then spawnEntity(et, 1) end end
    -- Add variant specifics
end

generateStructures = function(chunk, world, variant)
    print("Generating Kingdom Ruin structures (key areas)...")
     -- Logic to place named points of interest (Keep, Tower, etc.) within the generated ruin tiles.
     -- This likely involves placing markers or special entities rather than large separate structures.
      for _, structure in ipairs(KingdomRuin.structures) do
         if math.random() < structure.chance then
              if not structure.requiresVariant or structure.requiresVariant == variant then
                   local attempts = 0; local placed = false; local sx, sy
                   while attempts < 15 and not placed do
                       sx = math.random(3, world.CHUNK_SIZE - 4)
                       sy = math.random(3, world.CHUNK_SIZE - 4)
                       local tile = chunk.tiles[sx][sy]
                       -- Place within the ruins area
                       if tile and tile.type == "ruins" or tile.type == "stone" then placed = true end
                       attempts = attempts + 1
                   end
                   if placed then
                        local worldX = chunk.x * world.CHUNK_SIZE + sx
                        local worldY = chunk.y * world.CHUNK_SIZE + sy
                        print("Marking structure area: "..structure.name.." at "..worldX..","..worldY)
                        -- Place a Point of Interest marker entity
                        world.entitySystem:addEntity("ruin_poi_marker", worldX, worldY, { poi_name = structure.name, description="The remains of the "..structure.name.."." })
                        -- No separate entities needed usually, as they are spawned by populateEntities based on area
                   end
              end
         end
     end
end

postProcess = function(chunk, world, variant)
    print("Post-processing Kingdom Ruin chunk...")
    -- Apply ambient dust, echo, magic residue effects
    local variantData = variant and KingdomRuin.variants[variant] or nil
    chunk.environmentalEffects = chunk.environmentalEffects or {}
    table.insert(chunk.environmentalEffects, {type="ambient_dust", level=0.5})
    table.insert(chunk.environmentalEffects, {type="echo", level=0.4})
    local magicResidue = variantData and (variantData.environment and variantData.environment.magicResidue) or KingdomRuin.environment.magicResidue
    if magicResidue > 0.1 then table.insert(chunk.environmentalEffects, {type="magic_residue_aura", level=magicResidue}) end

    if variantData and variantData.specialEffects then
         for _, effect in ipairs(variantData.specialEffects) do table.insert(chunk.environmentalEffects, effect) end
     end
end

generate = function(chunk, world, variant)
    print("Generating " .. (variant and KingdomRuin.variants[variant].name or "Kingdom Ruin") .. " biome")
    local variantData = variant and KingdomRuin.variants[variant] or nil
    local magicResidue = variantData and (variantData.environment and variantData.environment.magicResidue or KingdomRuin.environment.magicResidue) or KingdomRuin.environment.magicResidue

    -- Adjust tile distribution
    local tileDistribution = {}
    for tileType, chance in pairs(KingdomRuin.tileDistribution) do
        tileDistribution[tileType] = chance
        if variantData and variantData.tileAdjustments and variantData.tileAdjustments[tileType] then
            tileDistribution[tileType] = variantData.tileAdjustments[tileType]
        end
    end

    -- Noise functions
    local seed = world.seed + (chunk.x * 709 + chunk.y * 433)
    math.randomseed(seed)
    local function pnoise2D(x, y, frequency, seed_offset) return love.math.noise(x * frequency, y * frequency, (world.seed + (seed_offset or 0)) * 0.01) end

    -- Initialize 2D tiles array if not exists
    chunk.tiles = chunk.tiles or {}
    for x = 0, world.CHUNK_SIZE - 1 do
        chunk.tiles[x] = chunk.tiles[x] or {}
    end

    -- Generate tiles (Primarily ruins, with variations)
    for x = 0, world.CHUNK_SIZE - 1 do
        for y = 0, world.CHUNK_SIZE - 1 do
            local worldX = chunk.x * world.CHUNK_SIZE + x
            local worldY = chunk.y * world.CHUNK_SIZE + y

            local ruinDensityNoise = pnoise2D(worldX, worldY, 0.1, 1) -- Controls density vs overgrown areas
            local structureNoise = pnoise2D(worldX, worldY, 0.05, 2) -- Controls where major structures might have been
            local hazardNoise = pnoise2D(worldX, worldY, 0.3, 3) -- Controls hazard placement

            -- Determine tile type
            local tileType = "ruins" -- Default is ruins
            if ruinDensityNoise < -0.4 then -- Less dense areas become overgrown
                tileType = math.random() < 0.6 and "dirt" or "grass"
            elseif structureNoise > 0.6 then -- Areas of former major structures are solid stone foundations
                 tileType = "stone"
            end

            -- Apply rarer tiles based on distribution and hazard noise
            local tileRoll = math.random()
            local cumulativeChance = 0
            for tType, chance in pairs(tileDistribution) do
                 cumulativeChance = cumulativeChance + chance
                 -- Allow override if not already a special type?
                 if tileType == "ruins" and tileRoll <= cumulativeChance then
                      if tType == "unstable_floor" or tType == "pitfall" or tType == "ancient_technology" or tType == "treasure_vault_tile" then
                           -- Place hazards based on noise
                           if hazardNoise > 0.5 and (tType == "unstable_floor" or tType == "pitfall") then
                                tileType = tType
                                break
                           -- Place special tiles more sparsely
                           elseif math.random() < 0.2 then
                                 tileType = tType
                                 break
                           end
                      end
                 end
            end

            -- Create the tile
            local tile = {
                type = tileType,
                x = x, y = y,
                variant = math.random(1, 6), -- Use ruin variants [cite: 44]
                magicResidue = magicResidue + (structureNoise * 0.1), -- Higher magic near former structures?
                stability = 0.5 - math.abs(hazardNoise) * 0.5, -- Stability based on hazard noise
                passable = (tileType ~= "pitfall"), -- Pits are impassable
                isUnderground = false -- Assume surface ruins unless specified
            }

            chunk.tiles[x][y] = tile
        end
    end

    -- Call helper functions
    KingdomRuin.generateFeatures(chunk, world, variant)
    KingdomRuin.populateEntities(chunk, world, variant)
    KingdomRuin.generateStructures(chunk, world, variant) -- Places key named areas
    KingdomRuin.postProcess(chunk, world, variant)
end

-- Assign function references
KingdomRuin.generateFeatures = generateFeatures
KingdomRuin.populateEntities = populateEntities
KingdomRuin.generateStructures = generateStructures
KingdomRuin.postProcess = postProcess
KingdomRuin.generate = generate

return KingdomRuin