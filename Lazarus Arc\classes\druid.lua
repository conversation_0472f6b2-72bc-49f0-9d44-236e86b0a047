-- classes/druid.lua
-- Druid class definition for Lazarus Arc

local Druid = {
    name = "Druid",
    description = "A mystical guardian of nature, using elemental magic, shapeshifting, and summoning to defend the wilds.",
    icon = "druid_icon.png",

    -- 🎭 **Playstyle & Trade-offs**
    pros = {
        "Can shapeshift into animals for different playstyles",
        "Strong elemental magic tied to nature",
        "Can heal, buff, and summon nature spirits"
    },
    cons = {
        "Shapeshifting locks out spellcasting while transformed",
        "Elemental magic is less direct than Wizard spells",
        "Requires mana or stamina to maintain transformations"
    },

    -- 🏋 **Base Stats & Growth**
    base_stats = { 
        health = 100, 
        maxHealth = 100,
        stamina = 70, 
        maxStamina = 70,
        mana = 90, 
        maxMana = 90,
        pattack = 8, 
        pdefense = 7, 
        mattack = 12, 
        mdefense = 9, 
        speed = 6, 
        luck = 6 
    },

    stat_growth = {
        health = 9,
        stamina = 6,
        mana = 8,
        pattack = 1.0,
        pdefense = 0.7,
        mattack = 1.4,
        mdefense = 1.0,
        speed = 0.5,
        luck = 0.5
    },

    -- ⚔️ **Weapon Proficiency**
    proficiencies = { 
        staff = 2.2,  
        wand = 2.0,  
        dagger = 1.2,  
        sword = 1.0,  
        greatsword = 0.7,  
        bow = 1.5,  
        gun = 0.6,  
        magic = 2.3  -- Strong elemental magic affinity
    },

    -- 🌿 **Elemental Affinities**
    element_affinities = {
        earth = 15,    -- Mastery over nature and stone magic
        water = 10,    -- Can manipulate healing waters and control tides
        air = 8,       -- Storm summoning, wind-based movement
        fire = -5,     -- Less effective at destructive, non-natural magic
    },

    -- 💥 **Overdrive Abilities**
    overdrive = { 
        "wrath_of_nature", -- Summons massive roots, vines, and storms to crush enemies
        "primal_transformation", -- Temporarily becomes a massive beast with immense power
        "elemental_fury", -- Unleashes all stored elemental energy in an explosive attack
    },

    -- 🎯 **Starting Skills**
    starting_skills = {
        "entangling_roots",
        "lesser_wild_shape",
        "nature's_blessing"
    },

    -- 📈 **Skill Progression**
    skill_tree = {
        tier1 = {
            "earth_spike",
            "healing_waters",
            "wind_sprint"
        },
        tier2 = {
            "stormcall",
            "feral_rage", 
            "guardian_spirit",
            "solar_flare"
        },
        tier3 = {
            "ancient_rebirth",
            "wild_fury",
            "cataclysmic_growth"
        }
    },

    -- 🎒 **Inventory & Carrying Capacity**
    inventory_limit = 100, -- Moderate carrying capacity

    -- ✨ **Special Abilities (Passive Perks)**
    special_abilities = {
        "Shapeshifter’s Instinct", -- Can shift between forms quickly
        "Nature's Resilience", -- Minor HP regeneration over time
        "Elemental Attunement", -- Reduced mana cost for elemental spells
        "Call of the Wild", -- Summons a random nature spirit in emergencies
        "Druidic Knowledge", -- Can identify plants, animals, and natural dangers
    },

    -- 🦁 **Shapeshifting Forms**
    shapeshifting_forms = {
        bear = {
            description = "A hulking beast form for increased defense and melee attacks.",
            bonuses = {
                "High physical defense and health",
                "Strong melee attacks",
                "Can taunt enemies to draw attacks"
            },
            weaknesses = {
                "Slower movement speed",
                "Unable to cast spells while transformed"
            }
        },
        
        wolf = {
            description = "A swift and agile form for fast-paced combat and tracking.",
            bonuses = {
                "Increased speed and attack rate",
                "Can track enemies over long distances",
                "Higher evasion and stealth"
            },
            weaknesses = {
                "Weaker defense",
                "Less effective against armored opponents"
            }
        },
        
        eagle = {
            description = "A form for high-speed aerial movement and scouting.",
            bonuses = {
                "Can fly over obstacles and avoid ground-based attacks",
                "Excellent vision for spotting enemies",
                "Increased ranged accuracy"
            },
            weaknesses = {
                "Extremely low defense",
                "Limited attack options while airborne"
            }
        }
    },

    -- 🎭 **Prestige Paths**
    prestige_paths = {
        archdruid = {
            description = "A Druid who has mastered nature magic, commanding the forces of the wild with near-limitless power.",
            bonuses = {
                "Increased healing and elemental magic",
                "Summoned creatures are stronger and last longer",
                "Can communicate with all natural beings"
            },
            weaknesses = {
                "More reliant on mana than other prestige paths",
                "Less effective in direct melee combat"
            }
        },

        feral_avatar = {
            description = "A warrior of the wild, using shapeshifting to overpower enemies in raw physical combat.",
            bonuses = {
                "Shapeshifting is faster and more powerful",
                "Stronger melee damage in beast form",
                "Can temporarily combine shapeshifting forms"
            },
            weaknesses = {
                "Limited spellcasting",
                "Needs stamina management to sustain transformations"
            }
        },

        stormcaller = {
            description = "A Druid who bends the weather itself to their will, controlling the battlefield with wind and lightning.",
            bonuses = {
                "Can summon storms for ongoing battlefield effects",
                "Increased damage for wind and lightning spells",
                "Can temporarily become one with the storm"
            },
            weaknesses = {
                "Less physical durability",
                "Relies on setting up spells over time rather than instant attacks"
            }
        }
    },

    -- ⚔️ **Hybrid & Multiclass Options**
    hybrid_options = {
        druid_warrior = {
            description = "A Druid who blends shapeshifting with martial combat.",
            pros = {
                "Can use weapons while shapeshifted",
                "Stronger defensive capabilities"
            },
            cons = {
                "Less spellcasting ability",
                "Requires balancing stamina and mana usage"
            }
        },

        nature_sorcerer = {
            description = "A Druid who taps into primal arcane forces to enhance their spells.",
            pros = {
                "Increased elemental spell potency",
                "Can amplify magic through their connection with nature"
            },
            cons = {
                "Weaker physical stats",
                "Mana-hungry playstyle"
            }
        }
    }
}

return Druid
