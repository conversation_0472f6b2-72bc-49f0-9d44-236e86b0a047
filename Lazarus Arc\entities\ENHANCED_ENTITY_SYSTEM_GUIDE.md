# Enhanced Entity System Guide

## Overview

The Enhanced Entity System adds **shiny variants**, **rare drops**, and **probability-based rarity** to your entities. This system allows for:

- **Variant System**: Normal, Shiny, Rare, and Legendary variants of entities
- **Enhanced Drops**: Rarity-based loot tables with variant-specific drops
- **Stat Modifiers**: Variants can have different stats, appearance, and sounds
- **Backward Compatibility**: Existing entities can be easily upgraded

## Quick Start

### 1. Using the Enhanced Template

```lua
local EnhancedTemplate = require("entities.enhanced_entity_template")

-- Your entity inherits from the enhanced template
local MyEntity = {
    -- ... your entity definition
}

function MyEntity.init(entity, world)
    -- Copy your entity data
    -- ... copy logic
    
    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end
```

### 2. Defining Variant Chances

```lua
variantChances = {
    normal = 0.85,      -- 85% chance for normal
    shiny = 0.10,       -- 10% chance for shiny
    rare = 0.04,        -- 4% chance for rare
    legendary = 0.01    -- 1% chance for legendary
}
```

### 3. Defining Variants

```lua
variants = {
    normal = {
        name = "Common Wolf",
        description = "A typical forest wolf",
        statModifiers = {},  -- No changes for normal
        appearanceModifiers = {}
    },
    
    shiny = {
        name = "Silver Wolf",
        description = "A rare wolf with silver fur",
        statModifiers = {
            maxHealth = 1.3,    -- 30% more health
            attack = 1.2,       -- 20% more attack
            speed = 1.1         -- 10% more speed
        },
        appearanceModifiers = {
            scale = 1.1,
            glow = true,
            colorTint = {1.2, 1.2, 1.4, 1.0}  -- Silver tint
        }
    }
}
```

### 4. Enhanced Drop System

```lua
-- Base drops (all variants can drop these)
baseDrops = {
    {id = "wolf_pelt", chance = 0.8, quantity = {1, 2}},
    {id = "wolf_fang", chance = 0.6, quantity = {1, 1}}
},

-- Variant-specific drops
variantDrops = {
    shiny = {
        {id = "silver_wolf_pelt", chance = 0.9, quantity = {1, 1}},
        {id = "moonlight_essence", chance = 0.5, quantity = {1, 1}}
    },
    legendary = {
        {id = "legendary_wolf_spirit", chance = 0.8, quantity = {1, 1}},
        {id = "alpha_wolf_crown", chance = 0.6, quantity = {1, 1}}
    }
}
```

## New Race Entities

The following new race entities have been created with the enhanced system:

### Fantasy Races
- **Elf** (`entities/elf.lua`) - Magical, agile, nature-focused
- **Dwarf** (`entities/dwarf.lua`) - Hardy, crafting-focused, defensive
- **Orc** (`entities/orc.lua`) - Aggressive, strong, tribal
- **Tiefling** (`entities/tiefling.lua`) - Infernal heritage, charismatic, magical
- **Halfling** (`entities/halfling.lua`) - Small, lucky, peaceful

### Sci-Fi Races
- **Robot** (`entities/robot.lua`) - Mechanical, energy-based, technological

Each race has unique:
- **Stat distributions** (elves are fast/magical, dwarves are tanky/strong)
- **Variant names** (High Elf, Forge Master, War Chief, etc.)
- **Special drops** (runic stones, infernal essence, chrome plating)
- **Unique abilities** (darkvision, self-repair, berserker rage)

## Upgrading Existing Entities

See `entities/enhanced_rabbit_example.lua` for a complete example of upgrading an existing entity.

### Step-by-Step Upgrade Process:

1. **Add Enhanced Template Import**
```lua
local EnhancedTemplate = require("entities.enhanced_entity_template")
```

2. **Add Variant System**
```lua
variantChances = {
    normal = 0.85,
    shiny = 0.12,
    rare = 0.025,
    legendary = 0.005
},

variants = {
    -- Define your variants here
}
```

3. **Split Drops into Base and Variant**
```lua
-- Replace old 'drops' with:
baseDrops = {
    -- Common drops here
},

variantDrops = {
    shiny = {
        -- Shiny-specific drops
    }
}
```

4. **Update Init Function**
```lua
function YourEntity.init(entity, world)
    -- Your existing copy logic
    -- ...
    
    -- Add this line at the end:
    return EnhancedTemplate.init(entity, world)
end
```

## Variant Properties

### Stat Modifiers
Multiply base stats by the modifier value:
```lua
statModifiers = {
    maxHealth = 1.5,    -- 50% more health
    attack = 1.2,       -- 20% more attack
    speed = 0.8         -- 20% less speed
}
```

### Appearance Modifiers
```lua
appearanceModifiers = {
    scale = 1.2,        -- 20% larger
    glow = true,        -- Add glow effect
    colorTint = {1.2, 1.0, 1.0, 1.0},  -- Red tint
    special_effects = "sparkles"
}
```

### Sound Modifiers
```lua
soundModifiers = {
    pitch = 1.3,        -- Higher pitch
    volume = 1.1,       -- Louder
    reverb = true,      -- Add reverb
    special_tone = "magical"
}
```

## Spawning Entities with Specific Variants

```lua
-- Normal spawning (random variant based on chances)
local entity = world.entitySystem:addEntity("elf", x, y)

-- Force a specific variant (for testing/special events)
local entity = world.entitySystem:addEntity("elf", x, y)
entity.currentVariant = "legendary"
EnhancedTemplate.applyVariantModifiers(entity)
```

## Getting Entity Drops

```lua
-- Get all possible drops for an entity
local allDrops = EnhancedTemplate.getAllDrops(entity)

-- Process drops on entity death
for _, drop in ipairs(allDrops) do
    if math.random() <= drop.chance then
        local quantity = math.random(drop.quantity[1], drop.quantity[2])
        -- Spawn the drop item
    end
end
```

## Tips and Best Practices

1. **Balanced Rarity**: Keep legendary variants very rare (1-3% chance)
2. **Meaningful Differences**: Each variant should feel significantly different
3. **Thematic Consistency**: Variant names and effects should fit your world
4. **Performance**: The system is designed to be lightweight and efficient
5. **Testing**: Use forced variants to test all possibilities

## Customization Examples

### Custom Variant Types
You can add your own variant types beyond the standard four:
```lua
variantChances = {
    normal = 0.70,
    shiny = 0.15,
    rare = 0.08,
    legendary = 0.02,
    cursed = 0.03,      -- Custom variant
    ancient = 0.02      -- Another custom variant
}
```

### Conditional Variants
```lua
-- In your entity's init function, you can modify chances based on conditions
if world.currentBiome == "haunted_forest" then
    entity.variantChances.cursed = 0.15  -- Higher cursed chance in haunted areas
end
```

### Special Events
```lua
-- During special events, increase shiny chances
if world.events.shinyEvent then
    entity.variantChances.shiny = 0.25  -- 25% shiny chance during event
end
```

This enhanced system provides a solid foundation for creating engaging, varied gameplay with meaningful progression and rare discoveries!
