-- entities/dwarf.lua
-- Dwarven entity with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local Dwarf = {
    id = "dwarf",
    name = "Dwarf",
    type = "dwarf",
    shape = {
        {0, -0.8}, {0.6, -0.5}, {0.8, 0}, {0.6, 0.5},
        {0, 0.8}, {-0.6, 0.5}, {-0.8, 0}, {-0.6, -0.5}
    },
    size = 5,  -- Smaller than elves
    description = "A sturdy dwarven craftsman with exceptional resilience and skill",

    -- Entity categories
    categories = {"humanoid", "dwarf", "crafter", "hardy"},
    threatCategories = {"player", "orc", "goblin", "troll"},
    
    -- Base stats (tanky and strong)
    maxHealth = 120,
    health = 120,
    maxStamina = 90,
    stamina = 90,
    maxMana = 40,
    mana = 40,
    speed = 1.8,  -- Slower than elves
    attack = 18,  -- Higher physical attack
    defense = 15, -- Much higher defense
    magicAttack = 6,
    magicDefense = 8,
    crafting = 20,  -- Special crafting stat
    
    -- Behaviors (dwarves are industrious)
    behaviors = {"wander", "mine", "craft", "defend"},
    behaviorConfigs = {
        wander = {
            moveSpeed = 1.5,
            changeDirectionChance = 0.04,
            wanderRadius = 6,
            preferUnderground = true
        },
        mine = {
            duration = {15, 30},
            efficiency = 1.5,
            frequency = 0.3,
            preferredMaterials = {"stone", "metal", "gems"}
        },
        craft = {
            duration = {20, 40},
            skillBonus = 2.0,
            frequency = 0.25,
            specialties = {"weapons", "armor", "tools"}
        },
        defend = {
            stance = "defensive",
            defenseBonus = 1.5,
            counterAttackChance = 0.3
        }
    },
    
    -- Enhanced variant system for dwarves
    variantChances = {
        normal = 0.70,          -- 70% mountain dwarf
        shiny = 0.18,           -- 18% forge master (shiny)
        rare = 0.10,            -- 10% runebeard (rare)
        legendary = 0.02        -- 2% dwarf king (legendary)
    },
    
    variants = {
        normal = {
            name = "Mountain Dwarf",
            description = "A hardy dwarf from the mountain clans",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {1.0, 0.9, 0.8, 1.0}  -- Earthy tint
            }
        },
        
        shiny = {
            name = "Forge Master",
            description = "A master craftsman with glowing forge-heated skin",
            statModifiers = {
                maxHealth = 1.4,
                attack = 1.3,
                defense = 1.4,
                crafting = 1.6,
                speed = 1.1
            },
            appearanceModifiers = {
                scale = 1.1,
                glow = true,
                colorTint = {1.3, 1.1, 0.8, 1.0},  -- Forge-glow orange
                forge_sparks = true
            },
            soundModifiers = {
                pitch = 0.9,
                volume = 1.2,
                metallic_ring = true
            }
        },
        
        rare = {
            name = "Runebeard",
            description = "An ancient dwarf with mystical runes in their beard",
            statModifiers = {
                maxHealth = 1.8,
                attack = 1.5,
                defense = 1.7,
                magicDefense = 1.8,
                crafting = 2.0,
                runic_power = 1.5
            },
            appearanceModifiers = {
                scale = 1.15,
                colorTint = {0.9, 0.9, 1.2, 1.0},  -- Blue runic glow
                runic_beard = true,
                mystical_aura = true
            },
            soundModifiers = {
                pitch = 0.8,
                volume = 1.3,
                reverb = true,
                runic_resonance = true
            }
        },
        
        legendary = {
            name = "Dwarf King",
            description = "A legendary dwarven monarch with unmatched power",
            statModifiers = {
                maxHealth = 2.5,
                attack = 2.0,
                defense = 2.2,
                magicDefense = 1.8,
                crafting = 3.0,
                leadership = 2.5
            },
            appearanceModifiers = {
                scale = 1.4,
                glow = true,
                colorTint = {1.4, 1.2, 0.6, 1.0},  -- Royal gold
                crown = true,
                royal_aura = "legendary",
                throne_presence = true
            },
            soundModifiers = {
                pitch = 0.7,
                volume = 1.8,
                reverb = true,
                echo = true,
                royal_authority = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "dwarven_metal", chance = 0.8, quantity = {1, 3}},
        {id = "stone_chunk", chance = 0.9, quantity = {2, 4}},
        {id = "gold_coin", chance = 0.7, quantity = {3, 8}},
        {id = "beard_hair", chance = 0.5, quantity = {1, 2}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "masterwork_ingot", chance = 0.8, quantity = {1, 2}},
            {id = "forge_ember", chance = 0.7, quantity = {1, 3}},
            {id = "crafted_tool", chance = 0.6, quantity = {1, 1}},
            {id = "dwarven_ale", chance = 0.4, quantity = {1, 2}}
        },
        rare = {
            {id = "runic_stone", chance = 0.8, quantity = {1, 2}},
            {id = "ancient_dwarven_scroll", chance = 0.6, quantity = {1, 1}},
            {id = "mystical_beard_braid", chance = 0.5, quantity = {1, 1}},
            {id = "enchanted_pickaxe", chance = 0.4, quantity = {1, 1}}
        },
        legendary = {
            {id = "royal_dwarven_crown", chance = 0.9, quantity = {1, 1}},
            {id = "kings_hammer", chance = 0.7, quantity = {1, 1}},
            {id = "throne_fragment", chance = 0.6, quantity = {1, 1}},
            {id = "legendary_dwarven_artifact", chance = 0.5, quantity = {1, 1}},
            {id = "mountain_heart_gem", chance = 0.3, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "dwarf",
        scale = 0.9,  -- Slightly smaller than default
        animations = {
            "idle", "walk", "mine", "craft", "drink_ale", "death"
        },
        variants = {
            "mountain_dwarf", "forge_master", "runebeard", "dwarf_king"
        }
    },
    
    -- Sound effects with dwarven characteristics
    sounds = {
        idle = {
            file = "dwarf_idle",
            synth = {
                instrument = "bass_guitar",
                notes = {"D3", "G3"},
                durations = {0.4, 0.6},
                volume = 0.3
            }
        },
        mine = {
            file = "dwarf_mine",
            synth = {
                instrument = "percussion",
                notes = {"C2"},
                duration = 0.2,
                volume = 0.5,
                metallic = true
            }
        },
        craft = {
            file = "dwarf_craft",
            synth = {
                instrument = "percussion",
                notes = {"D3", "D3", "D3"},
                durations = {0.15, 0.15, 0.15},
                volume = 0.4,
                hammer_rhythm = true
            }
        },
        hurt = {
            file = "dwarf_hurt",
            synth = {
                instrument = "bass_guitar",
                notes = {"F#2"},
                duration = 0.4,
                volume = 0.5,
                gruff = true
            }
        },
        death = {
            file = "dwarf_death",
            synth = {
                instrument = "bass_guitar",
                notes = {"D2", "A2", "F2", "D2"},
                durations = {0.6, 0.4, 0.4, 1.2},
                volume = 0.6,
                solemn = true
            }
        },
        battle_cry = {
            file = "dwarf_battle_cry",
            synth = {
                instrument = "brass",
                notes = {"G3", "D4"},
                durations = {0.3, 0.5},
                volume = 0.7,
                fierce = true
            }
        }
    },
    
    -- Special abilities
    abilities = {
        stone_skin = {
            type = "passive",
            description = "Natural resistance to physical damage",
            effect = "damage_reduction"
        },
        master_craftsman = {
            type = "active",
            description = "Can craft superior items",
            effect = "crafting_bonus"
        },
        dwarven_resilience = {
            type = "passive",
            description = "Resistance to poison and magic",
            effect = "status_resistance"
        },
        underground_navigation = {
            type = "passive",
            description = "Never gets lost underground",
            effect = "navigation_bonus"
        }
    }
}

-- Initialize the dwarf entity using enhanced template
function Dwarf.init(entity, world)
    -- Copy all fields from Dwarf template to entity instance
    for k, v in pairs(Dwarf) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return Dwarf
