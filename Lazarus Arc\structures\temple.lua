-- temple.lua
-- Temple structure for Lazarus Arc

local Temple = {
    id = "temple",
    name = "Ancient Temple",
    description = "A mysterious ancient temple with magical properties",
    width = 5,
    height = 5,
    sprite = "structures/temple",
    collision = true,
    interactable = true,
    storage = {
        slots = 20,
        items = {}
    },
    durability = 200,
    requirements = {
        stone = 50,
        gold = 20,
        magic_crystal = 5
    },
    properties = {
        magic_resistance = 50,
        healing_rate = 5,
        mana_regeneration = 10
    }
}

function Temple.new(options)
    options = options or {}
    local temple = {
        id = Temple.id,
        name = Temple.name,
        description = Temple.description,
        position = options.position or {x = 0, y = 0},
        rotation = options.rotation or 0,
        width = Temple.width,
        height = Temple.height,
        sprite = Temple.sprite,
        collision = Temple.collision,
        interactable = Temple.interactable,
        storage = {
            slots = Temple.storage.slots,
            items = {}
        },
        durability = Temple.durability,
        requirements = Temple.requirements,
        properties = Temple.properties,
        owner = options.owner,
        isBuilt = false,
        active = false
    }
    
    return temple
end

function Temple.init(structure, world)
    structure.world = world
    if not structure.isBuilt then
        Temple.build(structure)
    end
end

function Temple.build(structure)
    -- Check if requirements are met
    if not Temple.checkRequirements(structure) then
        return false
    end
    
    -- Consume resources
    Temple.consumeResources(structure)
    
    -- Mark as built
    structure.isBuilt = true
    structure.active = true
    return true
end

function Temple.checkRequirements(structure)
    if not structure.owner then return false end
    
    -- Check if player has required resources
    local inventory = structure.owner.inventory
    if not inventory then return false end
    
    for resource, amount in pairs(structure.requirements) do
        if not inventory.items[resource] or inventory.items[resource].quantity < amount then
            return false
        end
    end
    
    return true
end

function Temple.consumeResources(structure)
    if not structure.owner then return end
    
    local inventory = structure.owner.inventory
    if not inventory then return end
    
    for resource, amount in pairs(structure.requirements) do
        if inventory.items[resource] then
            inventory.items[resource].quantity = inventory.items[resource].quantity - amount
            if inventory.items[resource].quantity <= 0 then
                inventory.items[resource] = nil
            end
        end
    end
end

function Temple.interact(structure, player)
    if not structure.isBuilt or not structure.active then return end
    
    -- Apply temple effects to player
    if player then
        player.magic_resistance = (player.magic_resistance or 0) + structure.properties.magic_resistance
        player.healing_rate = (player.healing_rate or 0) + structure.properties.healing_rate
        player.mana_regeneration = (player.mana_regeneration or 0) + structure.properties.mana_regeneration
    end
    
    -- Open storage interface
    if player and player.openStorage then
        player:openStorage(structure.storage)
    end
end

function Temple.damage(structure, amount)
    if not structure.isBuilt then return end
    
    structure.durability = structure.durability - amount
    if structure.durability <= 0 then
        Temple.destroy(structure)
    end
end

function Temple.destroy(structure)
    structure.isBuilt = false
    structure.active = false
    structure.durability = Temple.durability
    
    -- Drop storage items
    if structure.world then
        for _, item in pairs(structure.storage.items) do
            structure.world:spawnItem(item, structure.position.x, structure.position.y)
        end
    end
    
    structure.storage.items = {}
end

function Temple.update(structure, dt)
    if not structure.active then return end
    
    -- Apply temple effects to nearby entities
    if structure.world then
        local radius = 10
        for _, entity in pairs(structure.world.entities) do
            if entity.position then
                local dx = entity.position.x - structure.position.x
                local dy = entity.position.y - structure.position.y
                local distance = math.sqrt(dx * dx + dy * dy)
                
                if distance <= radius then
                    entity.magic_resistance = (entity.magic_resistance or 0) + structure.properties.magic_resistance
                    entity.healing_rate = (entity.healing_rate or 0) + structure.properties.healing_rate
                    entity.mana_regeneration = (entity.mana_regeneration or 0) + structure.properties.mana_regeneration
                end
            end
        end
    end
end

function Temple.draw(structure)
    if not structure.sprite then return end
    
    love.graphics.setColor(1, 1, 1, 1)
    love.graphics.draw(structure.sprite, structure.position.x, structure.position.y, structure.rotation)
    
    -- Draw magical aura if active
    if structure.active then
        love.graphics.setColor(0.5, 0.5, 1, 0.3)
        love.graphics.circle("fill", structure.position.x, structure.position.y, 10)
    end
end

return Temple 