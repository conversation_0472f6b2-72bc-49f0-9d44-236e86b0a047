-- entities/spider.lua
-- Spider insect with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local Spider = {
    id = "spider",
    name = "Spider",
    type = "spider",
    shape = {
        {0, -0.8}, {0.6, -0.6}, {0.8, 0}, {0.6, 0.6},
        {0, 0.8}, {-0.6, 0.6}, {-0.8, 0}, {-0.6, -0.6}
    },
    size = 4,

    -- Entity categories
    categories = {"insect", "predator", "venomous", "web_weaver"},
    threatCategories = {"player", "fly", "small_creatures"},
    
    -- Base stats (agile predator)
    maxHealth = 25,
    health = 25,
    maxStamina = 40,
    stamina = 40,
    speed = 2.5,
    attack = 12,
    defense = 6,
    venom_potency = 10,
    web_strength = 8,
    climbing = 15,
    
    -- Behaviors
    behaviors = {"web_hunt", "ambush", "climb_walls", "venom_inject"},
    behaviorConfigs = {
        web_hunt = {
            webRadius = 12,
            trapEfficiency = 0.8,
            webRepair = true,
            preyDetection = 15
        },
        ambush = {
            hideChance = 0.7,
            ambushBonus = 2.0,
            patientWait = true,
            surpriseAttack = 1.5
        },
        climb_walls = {
            climbSpeed = 2.0,
            wallWalking = true,
            ceilingHanging = true,
            escapeRoute = true
        },
        venom_inject = {
            venomChance = 0.6,
            paralysisChance = 0.3,
            damageOverTime = 5,
            duration = 10
        }
    },
    
    -- Enhanced variant system for spiders
    variantChances = {
        normal = 0.70,          -- 70% garden spider
        shiny = 0.22,           -- 22% black widow (shiny)
        rare = 0.07,            -- 7% tarantula (rare)
        legendary = 0.01        -- 1% arachne (legendary)
    },
    
    variants = {
        normal = {
            name = "Garden Spider",
            description = "A common spider that weaves intricate webs",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.6, 0.5, 0.3, 1.0}  -- Brown garden spider
            }
        },
        
        shiny = {
            name = "Black Widow",
            description = "A deadly spider with potent venom and distinctive markings",
            statModifiers = {
                maxHealth = 1.3,    -- 32.5 health
                attack = 1.4,       -- 16.8 attack
                venom_potency = 2.5, -- 25 venom potency
                speed = 1.2,        -- 3.0 speed
                lethality = 2.0
            },
            appearanceModifiers = {
                scale = 1.1,
                glow = true,
                colorTint = {0.1, 0.1, 0.1, 1.0},  -- Jet black with red markings
                red_hourglass = true,
                venomous_fangs = true,
                deadly_aura = true
            },
            soundModifiers = {
                pitch = 1.1,
                volume = 1.2,
                sinister = true
            }
        },
        
        rare = {
            name = "Giant Tarantula",
            description = "A massive, hairy spider with incredible strength",
            statModifiers = {
                maxHealth = 2.0,    -- 50 health
                attack = 1.8,       -- 21.6 attack
                defense = 1.6,      -- 9.6 defense
                size_advantage = 2.0,
                crushing_bite = 1.8
            },
            appearanceModifiers = {
                scale = 1.6,
                colorTint = {0.4, 0.3, 0.2, 1.0},  -- Dark brown with hair
                massive_fangs = true,
                thick_hair = true,
                intimidating_size = true,
                ground_tremor = true
            },
            soundModifiers = {
                pitch = 0.7,
                volume = 1.4,
                reverb = true,
                heavy_steps = true
            }
        },
        
        legendary = {
            name = "Arachne",
            description = "A mythical spider-being with divine weaving powers",
            statModifiers = {
                maxHealth = 3.0,    -- 75 health
                attack = 2.2,       -- 26.4 attack
                defense = 1.8,      -- 10.8 defense
                venom_potency = 4.0, -- 40 venom potency
                divine_weaving = 5.0,
                metamorphosis = 3.0
            },
            appearanceModifiers = {
                scale = 1.4,
                glow = true,
                colorTint = {0.8, 0.6, 1.0, 1.0},  -- Mystical purple-silver
                divine_markings = true,
                golden_web = true,
                arachne_aura = "legendary",
                reality_weaving = true
            },
            soundModifiers = {
                pitch = 0.8,
                volume = 1.5,
                reverb = true,
                echo = true,
                divine_weaving = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "spider_silk", chance = 0.9, quantity = {2, 4}},
        {id = "venom_sac", chance = 0.6, quantity = {1, 1}},
        {id = "spider_leg", chance = 0.7, quantity = {2, 6}},
        {id = "chitin_fragment", chance = 0.5, quantity = {1, 2}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "black_widow_venom", chance = 0.9, quantity = {1, 2}},
            {id = "deadly_silk", chance = 0.8, quantity = {2, 3}},
            {id = "red_hourglass_marking", chance = 0.7, quantity = {1, 1}},
            {id = "lethality_essence", chance = 0.6, quantity = {1, 1}}
        },
        rare = {
            {id = "tarantula_fang", chance = 0.9, quantity = {1, 2}},
            {id = "thick_spider_hair", chance = 0.8, quantity = {3, 6}},
            {id = "crushing_mandible", chance = 0.7, quantity = {1, 1}},
            {id = "size_enhancement_gland", chance = 0.5, quantity = {1, 1}}
        },
        legendary = {
            {id = "arachne_divine_silk", chance = 0.95, quantity = {1, 3}},
            {id = "reality_weaving_essence", chance = 0.9, quantity = {1, 1}},
            {id = "divine_spider_fang", chance = 0.8, quantity = {1, 1}},
            {id = "metamorphosis_crystal", chance = 0.7, quantity = {1, 1}},
            {id = "golden_web_fragment", chance = 0.6, quantity = {1, 2}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "spider",
        scale = 0.7,
        animations = {
            "crawl", "web_weave", "ambush_strike", "climb", "venom_bite"
        },
        variants = {
            "garden_spider", "black_widow", "giant_tarantula", "arachne"
        }
    },
    
    -- Sound effects with spider characteristics
    sounds = {
        crawl = {
            file = "spider_crawl",
            synth = {
                instrument = "xylophone",
                notes = {"D4", "F4", "A4"},
                durations = {0.1, 0.1, 0.1},
                volume = 0.2,
                skittering = true
            }
        },
        web_weave = {
            file = "web_weave",
            synth = {
                instrument = "harp",
                notes = {"G4", "B4", "D5"},
                durations = {0.3, 0.2, 0.4},
                volume = 0.15,
                silky = true
            }
        },
        venom_hiss = {
            file = "venom_hiss",
            synth = {
                instrument = "synthesizer",
                notes = {"F#3"},
                duration = 0.4,
                volume = 0.3,
                venomous = true
            }
        },
        divine_weaving = {
            file = "divine_weaving",
            synth = {
                instrument = "celesta",
                notes = {"C4", "E4", "G4", "C5"},
                durations = {0.4, 0.3, 0.4, 0.6},
                volume = 0.4,
                mystical = true
            }
        }
    },
    
    -- Special spider abilities
    abilities = {
        web_trap = {
            type = "active",
            description = "Create web traps that immobilize enemies",
            effect = "immobilize_trap",
            cooldown = 12
        },
        venom_bite = {
            type = "active",
            description = "Inject venom that causes damage over time",
            effect = "poison_damage",
            cooldown = 8
        },
        wall_climbing = {
            type = "passive",
            description = "Can climb on any surface including ceilings",
            effect = "surface_climbing"
        },
        ambush_predator = {
            type = "passive",
            description = "Gains massive damage bonus when attacking from hiding",
            effect = "stealth_damage_bonus"
        },
        silk_production = {
            type = "passive",
            description = "Continuously produces valuable silk",
            effect = "resource_generation"
        }
    }
}

-- Initialize the spider entity using enhanced template
function Spider.init(entity, world)
    -- Copy all fields from Spider template to entity instance
    for k, v in pairs(Spider) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return Spider
