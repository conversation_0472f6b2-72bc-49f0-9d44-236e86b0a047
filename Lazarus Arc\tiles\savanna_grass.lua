local SavannaGrass = {
    id = "savanna_grass",
    name = "Savanna Grass",
    type = "tile",
    
    -- Categories
    categories = {"ground", "grass", "savanna", "biome"},
    
    -- Properties
    properties = {
        walkable = true,
        swimable = false,
        climbable = false,
        flyable = true,
        diggable = true,
        buildable = true,
        flammable = true,
        fertility = 0.6,
        moisture = 0.3,
        temperature = 0.8,
        grassDensity = 0.7,
        grassHeight = 0.8,
        grassColor = {0.8, 0.7, 0.2}, -- Golden yellow
        grassVariants = 4,
        grassTypes = {"tall", "medium", "short", "dry"},
        grassDistribution = {
            tall = 0.3,
            medium = 0.4,
            short = 0.2,
            dry = 0.1
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "savanna_grass",
        scale = 1.0,
        animations = {
            "idle",
            "wind",
            "dry"
        },
        variants = {
            "golden",
            "yellow",
            "brown",
            "dry"
        },
        blendMode = "multiply",
        tint = {0.9, 0.8, 0.3},
        alpha = 1.0
    },
    
    -- Sound effects
    sounds = {
        walk = "grass_walk",
        dig = "grass_dig",
        burn = "grass_burn"
    },
    
    -- Resources
    resources = {
        grass = {
            type = "plant",
            amount = 3,
            regrowthRate = 0.2,
            regrowthTime = 300,
            harvestable = true,
            harvestAmount = 1,
            harvestTool = "scythe"
        },
        seeds = {
            type = "plant",
            amount = 1,
            regrowthRate = 0.1,
            regrowthTime = 600,
            harvestable = true,
            harvestAmount = 1,
            harvestTool = "scythe"
        }
    },
    
    -- Effects
    effects = {
        wind = {
            type = "environment",
            duration = 0,
            effects = {
                grassWave = true,
                windStrength = 0.7
            }
        },
        dry = {
            type = "environment",
            duration = 0,
            effects = {
                grassColor = {0.7, 0.6, 0.1},
                grassHeight = 0.6
            }
        }
    },
    
    -- Spawn rules
    spawnRules = {
        plants = {
            "savanna_grass",
            "dry_bush",
            "acacia_sapling",
            "baobab_sapling",
            "savanna_flower"
        },
        animals = {
            "zebra",
            "giraffe",
            "lion",
            "elephant",
            "ostrich"
        },
        structures = {
            "termite_mound",
            "acacia_tree",
            "baobab_tree"
        }
    }
}

-- Initialize the tile
function SavannaGrass.init(tile, world)
    -- Initialize properties first
    tile.properties = tile.properties or {}
    
    -- Copy all fields from SavannaGrass template to tile instance
    for k, v in pairs(SavannaGrass) do
        if type(v) ~= "function" and tile[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                tile[k] = {}
                for subk, subv in pairs(v) do
                    tile[k][subk] = subv
                end
            else
                tile[k] = v
            end
        end
    end

    -- Set random variant
    if tile.appearance and tile.appearance.variants then
        local variants = tile.appearance.variants
        tile.appearance.currentVariant = variants[math.random(#variants)]
    end

    -- Initialize grass state with fallback values
    tile.properties.grassState = {
        type = (tile.properties.grassTypes and tile.properties.grassTypes[math.random(#tile.properties.grassTypes)]) or "medium",
        height = tile.properties.grassHeight or 0.8,
        color = tile.properties.grassColor or {0.8, 0.7, 0.2},
        density = tile.properties.grassDensity or 0.7
    }

    -- Initialize resources
    if tile.resources then
        for resourceType, resource in pairs(tile.resources) do
            resource.currentAmount = resource.amount
            resource.lastHarvest = 0
            resource.growthStage = "mature"
        end
    end

    return tile
end

-- Update the tile
function SavannaGrass.update(tile, world, dt)
    -- Update grass state based on environment
    if tile.properties.grassState then
        -- Check for wind effect
        if world.wind and world.wind.strength > 0.5 then
            tile.properties.grassState.windEffect = true
        else
            tile.properties.grassState.windEffect = false
        end
        
        -- Check for dry conditions
        if world.temperature and world.temperature > 0.8 and world.moisture and world.moisture < 0.3 then
            tile.properties.grassState.dry = true
        else
            tile.properties.grassState.dry = false
        end
    end
    
    -- Update resources
    if tile.resources then
        for resourceType, resource in pairs(tile.resources) do
            if resource.currentAmount < resource.amount then
                -- Regrow resources
                local regrowthAmount = resource.regrowthRate * dt
                resource.currentAmount = math.min(resource.amount, resource.currentAmount + regrowthAmount)
                
                -- Update growth stage
                if resource.currentAmount >= resource.amount then
                    resource.growthStage = "mature"
                elseif resource.currentAmount >= resource.amount * 0.5 then
                    resource.growthStage = "growing"
                else
                    resource.growthStage = "young"
                end
            end
        end
    end
end

-- Handle interaction
function SavannaGrass.interact(tile, world, entity, action)
    if action == "dig" then
        -- Handle digging
        if tile.properties.diggable then
            -- Create dirt item
            if world.createItem then
                world.createItem({
                    type = "dirt",
                    amount = 1,
                    position = tile.position
                })
            end
            
            -- Play dig sound
            if world.playSound then
                world.playSound(tile.sounds.dig)
            end
            
            -- Convert to dirt tile
            if world.setTile then
                world.setTile(tile.position, "dirt")
            end
        end
    elseif action == "harvest" then
        -- Handle resource harvesting
        if tile.resources then
            for resourceType, resource in pairs(tile.resources) do
                if resource.harvestable and resource.currentAmount > 0 then
                    -- Create harvested item
                    if world.createItem then
                        world.createItem({
                            type = resourceType,
                            amount = resource.harvestAmount,
                            position = tile.position
                        })
                    end
                    
                    -- Update resource amount
                    resource.currentAmount = math.max(0, resource.currentAmount - resource.harvestAmount)
                    resource.lastHarvest = world.time
                    resource.growthStage = "young"
                end
            end
        end
    end
end

return SavannaGrass 