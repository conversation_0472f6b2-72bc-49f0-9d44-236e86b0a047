-- entities/mole.lua
-- Underground mole with enhanced variant system
local EnhancedTemplate = require("entities.enhanced_entity_template")

local Mole = {
    id = "mole",
    name = "<PERSON><PERSON>",
    type = "mole",
    shape = {
        {0, -0.7}, {0.5, -0.5}, {0.7, 0}, {0.5, 0.5},
        {0, 0.7}, {-0.5, 0.5}, {-0.7, 0}, {-0.5, -0.5}
    },
    size = 4,

    -- Entity categories
    categories = {"underground", "mammal", "burrower", "small"},
    threatCategories = {"player", "predator", "surface_dweller"},
    
    -- Base stats (specialized for underground life)
    maxHealth = 30,
    health = 30,
    maxStamina = 50,
    stamina = 50,
    speed = 1.2,  -- Slow on surface
    underground_speed = 3.0,  -- Fast underground
    attack = 8,
    defense = 6,
    digging_power = 20,
    tunnel_sense = 15,
    vibration_detection = 25,
    
    -- Behaviors
    behaviors = {"tunnel_network", "underground_hunt", "surface_emerge", "burrow_escape"},
    behaviorConfigs = {
        tunnel_network = {
            tunnelSpeed = 2.5,
            networkSize = 50,
            tunnelMaintenance = true,
            hiddenPaths = true
        },
        underground_hunt = {
            preyDetection = 20,
            ambushFromBelow = true,
            rootHunting = true,
            wormHunting = true
        },
        surface_emerge = {
            emergeChance = 0.3,
            surfaceTime = {5, 15},
            quickRetreat = true,
            alertness = 2.0
        },
        burrow_escape = {
            escapeSpeed = 4.0,
            burrowTime = 2.0,
            emergencyTunnel = true
        }
    },
    
    -- Enhanced variant system for moles
    variantChances = {
        normal = 0.75,          -- 75% garden mole
        shiny = 0.18,           -- 18% star-nosed mole (shiny)
        rare = 0.06,            -- 6% giant mole (rare)
        legendary = 0.01        -- 1% tunnel king (legendary)
    },
    
    variants = {
        normal = {
            name = "Garden Mole",
            description = "A common mole that creates tunnel networks under gardens",
            statModifiers = {},
            appearanceModifiers = {
                colorTint = {0.4, 0.3, 0.2, 1.0}  -- Dark brown
            }
        },
        
        shiny = {
            name = "Star-nosed Mole",
            description = "A unique mole with a star-shaped sensory organ",
            statModifiers = {
                maxHealth = 1.4,    -- 42 health
                underground_speed = 1.5,  -- 4.5 underground speed
                vibration_detection = 2.0,  -- 50 vibration detection
                sensory_power = 3.0,
                aquatic_ability = 1.5
            },
            appearanceModifiers = {
                scale = 1.1,
                glow = true,
                colorTint = {0.3, 0.2, 0.1, 1.0},  -- Darker with pink star
                star_nose = true,
                enhanced_senses = true,
                sensory_aura = true
            },
            soundModifiers = {
                pitch = 1.2,
                volume = 1.1,
                sensitive = true
            }
        },
        
        rare = {
            name = "Giant Mole",
            description = "A massive mole that can create caverns with its digging",
            statModifiers = {
                maxHealth = 2.2,    -- 66 health
                attack = 2.0,       -- 16 attack
                defense = 1.8,      -- 10.8 defense
                digging_power = 3.0, -- 60 digging power
                size_advantage = 2.5,
                cavern_creation = 2.0
            },
            appearanceModifiers = {
                scale = 1.8,
                colorTint = {0.5, 0.4, 0.3, 1.0},  -- Larger, more imposing
                massive_claws = true,
                thick_hide = true,
                ground_tremor = true
            },
            soundModifiers = {
                pitch = 0.7,
                volume = 1.4,
                reverb = true,
                earth_shaking = true
            }
        },
        
        legendary = {
            name = "Tunnel King",
            description = "A legendary mole that rules vast underground kingdoms",
            statModifiers = {
                maxHealth = 3.5,    -- 105 health
                attack = 2.5,       -- 20 attack
                defense = 2.2,      -- 13.2 defense
                digging_power = 5.0, -- 100 digging power
                underground_mastery = 10.0,
                kingdom_control = 5.0
            },
            appearanceModifiers = {
                scale = 1.5,
                glow = true,
                colorTint = {0.8, 0.6, 0.4, 1.0},  -- Regal golden-brown
                crown_of_earth = true,
                royal_aura = "legendary",
                tunnel_mastery = true,
                underground_dominion = true
            },
            soundModifiers = {
                pitch = 0.6,
                volume = 1.6,
                reverb = true,
                echo = true,
                royal_authority = true
            }
        }
    },
    
    -- Base drops
    baseDrops = {
        {id = "mole_fur", chance = 0.8, quantity = {1, 2}},
        {id = "digging_claw", chance = 0.7, quantity = {2, 4}},
        {id = "tunnel_dirt", chance = 0.9, quantity = {2, 4}},
        {id = "underground_essence", chance = 0.4, quantity = {1, 1}}
    },
    
    -- Variant-specific drops
    variantDrops = {
        shiny = {
            {id = "star_nose_organ", chance = 0.9, quantity = {1, 1}},
            {id = "sensory_enhancement_gland", chance = 0.8, quantity = {1, 1}},
            {id = "vibration_crystal", chance = 0.7, quantity = {1, 1}},
            {id = "aquatic_adaptation_organ", chance = 0.6, quantity = {1, 1}}
        },
        rare = {
            {id = "giant_mole_claw", chance = 0.9, quantity = {1, 2}},
            {id = "cavern_creation_essence", chance = 0.8, quantity = {1, 1}},
            {id = "massive_digging_organ", chance = 0.7, quantity = {1, 1}},
            {id = "earth_shaking_core", chance = 0.6, quantity = {1, 1}}
        },
        legendary = {
            {id = "tunnel_king_crown", chance = 0.95, quantity = {1, 1}},
            {id = "underground_dominion_orb", chance = 0.9, quantity = {1, 1}},
            {id = "tunnel_mastery_crystal", chance = 0.8, quantity = {1, 1}},
            {id = "earth_kingdom_seal", chance = 0.7, quantity = {1, 1}},
            {id = "subterranean_authority_gem", chance = 0.6, quantity = {1, 1}}
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "mole",
        scale = 0.7,
        animations = {
            "dig", "tunnel", "emerge", "burrow", "sense_vibration"
        },
        variants = {
            "garden_mole", "star_nosed_mole", "giant_mole", "tunnel_king"
        }
    },
    
    -- Sound effects with underground characteristics
    sounds = {
        digging = {
            file = "mole_dig",
            synth = {
                instrument = "percussion",
                notes = {"D2", "F2", "D2"},
                durations = {0.2, 0.1, 0.2},
                volume = 0.3,
                earthy = true
            }
        },
        tunnel_movement = {
            file = "tunnel_movement",
            synth = {
                instrument = "bass_guitar",
                notes = {"C2"},
                duration = 0.4,
                volume = 0.2,
                underground = true
            }
        },
        emerge = {
            file = "mole_emerge",
            synth = {
                instrument = "xylophone",
                notes = {"G3", "C4"},
                durations = {0.2, 0.3},
                volume = 0.25,
                surprise = true
            }
        },
        vibration_sense = {
            file = "vibration_sense",
            synth = {
                instrument = "kalimba",
                notes = {"A4", "C5", "E5"},
                durations = {0.1, 0.1, 0.2},
                volume = 0.2,
                sensitive = true
            }
        }
    },
    
    -- Special underground abilities
    abilities = {
        tunnel_network = {
            type = "passive",
            description = "Creates and maintains extensive tunnel systems",
            effect = "terrain_modification"
        },
        underground_movement = {
            type = "passive",
            description = "Moves much faster underground than on surface",
            effect = "terrain_speed_bonus"
        },
        vibration_detection = {
            type = "passive",
            description = "Can sense movement through ground vibrations",
            effect = "enhanced_detection"
        },
        emergency_burrow = {
            type = "active",
            description = "Quickly burrow underground to escape danger",
            effect = "escape_ability",
            cooldown = 15
        },
        earth_sense = {
            type = "passive",
            description = "Knows location of underground resources and passages",
            effect = "resource_detection"
        }
    }
}

-- Initialize the mole entity using enhanced template
function Mole.init(entity, world)
    -- Copy all fields from Mole template to entity instance
    for k, v in pairs(Mole) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    if type(subv) == "table" then
                        entity[k][subk] = {}
                        for subsubk, subsubv in pairs(subv) do
                            entity[k][subk][subsubk] = subsubv
                        end
                    else
                        entity[k][subk] = subv
                    end
                end
            else
                entity[k] = v
            end
        end
    end

    -- Use enhanced template initialization
    return EnhancedTemplate.init(entity, world)
end

return Mole
